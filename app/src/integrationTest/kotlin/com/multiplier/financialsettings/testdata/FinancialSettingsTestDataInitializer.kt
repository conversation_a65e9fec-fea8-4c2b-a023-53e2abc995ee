package com.multiplier.financialsettings.testdata

import mu.KotlinLogging
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

/**
 * Initializes financial settings test data after the application context is ready
 */
@Component
class FinancialSettingsTestDataInitializer(
    private val testDataLoader: FinancialSettingsTestDataLoader
) {

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    @EventListener(ApplicationReadyEvent::class)
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun initializeTestData() {
        logger.info { "Initializing financial settings test data" }
        testDataLoader.loadTestData()
        logger.info { "Financial settings test data initialization complete" }
    }
}
