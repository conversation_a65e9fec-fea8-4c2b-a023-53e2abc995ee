package com.multiplier.financialsettings.testdata.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

/**
 * Test data DTO for BillingCurrencySetting JSON deserialization
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class BillingCurrencySettingTestData(
    val id: Long,
    val companyEntityContextId: Long,
    val transactionType: String,
    val billingCurrencyCode: String,
    val createdBy: Long,
    val updatedBy: Long,
    val description: String? = null
)
