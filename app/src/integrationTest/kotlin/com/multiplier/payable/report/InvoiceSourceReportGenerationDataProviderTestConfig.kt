package com.multiplier.payable.report


import com.multiplier.payable.engine.collector.gross.ContractDepartmentMapper
import com.multiplier.payable.engine.formatter.AggregatedDataFormatter
import com.multiplier.payable.engine.formatter.GroupingDataFormatter
import com.multiplier.payable.engine.formatter.TextDataFormatter
import com.multiplier.payable.engine.formatter.accessor.PropertyAccessor
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import

@Configuration
@Import(
    value = [
        ContractDepartmentMapper::class,
        AggregatedDataFormatter::class,
        TextDataFormatter::class,
        GroupingDataFormatter::class,
        PropertyAccessor::class,
    ]
)
class InvoiceSourceReportGenerationDataProviderTestConfig