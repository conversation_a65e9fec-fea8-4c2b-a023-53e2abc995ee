package com.multiplier.payable.engine.reconciler.order

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.PayableApp
import com.multiplier.payable.engine.payableitem.JpaPayableItemStore
import com.multiplier.payable.engine.payableitem.JpaPayableItemStoreRepository
import com.multiplier.payable.engine.payableitem.PayableItemMapper
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
import org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.FilterType
import org.springframework.context.annotation.Import
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.testcontainers.junit.jupiter.Testcontainers

@Configuration
@ImportAutoConfiguration(
    DataSourceAutoConfiguration::class,
    HibernateJpaAutoConfiguration::class,
    SqlInitializationAutoConfiguration::class
)
@EntityScan(
    basePackageClasses = [
        JpaPayableItemStore::class,
    ]
)
@EnableJpaRepositories(
    basePackageClasses = [PayableApp::class],
    includeFilters = [ComponentScan.Filter(
        type = FilterType.ASSIGNABLE_TYPE,
        value = [
            JpaPayableItemStoreRepository::class
        ]
    )]
)
@Import(
    value = [
        OrderFormAdvanceItemStoreProvider::class,
        ObjectMapper::class,
        PayableItemMapper::class,
    ]
)
@Testcontainers
class OrderFormAdvanceItemStoreProviderTestConfiguration {

}
