package com.multiplier.payable.engine.payableitem

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.gross.ContractGrossSalary
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import java.math.BigDecimal
import java.sql.Timestamp
import java.time.LocalDate

@ActiveProfiles("test")
@SpringBootTest(classes = [GrossSalaryPayableItemMapperTestConfiguration::class])
class GrossSalaryPayableItemMapperIT {
    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var grossSalaryPayableItemMapper: GrossSalaryPayableItemMapper

    @Test
    fun `should map correctly for null or nonnull department data`() {
        // GIVEN
        val contractWithDepartmentJsonString: String =
            objectMapper.writeValueAsString(
                ContractGrossSalary(
                    companyId = 1,
                    contractId = 2,
                    grossSalary = BigDecimal(1000.0),
                    currencyCode = com.multiplier.payable.engine.currency.CurrencyCode.USD,
                ),
            )
        val contractWithoutDepartmentJsonString: String =
            objectMapper.writeValueAsString(
                ContractGrossSalary(
                    companyId = 1,
                    contractId = 2,
                    grossSalary = BigDecimal(1000.0),
                    currencyCode = com.multiplier.payable.engine.currency.CurrencyCode.USD,
                ),
            )
        val jpaItemWithDepartment =
            JpaPayableItemStore(
                1,
                1000.0,
                CurrencyCode.USD,
                1,
                2,
                CountryCode.USA,
                4,
                2024,
                LineItemType.GROSS_SALARY,
                contractWithDepartmentJsonString,
                LocalDate.of(2024, 4, 1),
                LocalDate.of(2024, 4, 30),
                -1,
                -1,
                LocalDate.of(2024, 4, 1).atStartOfDay(),
                LocalDate.of(2024, 4, 1).atStartOfDay(),
                "1",
                Timestamp(System.currentTimeMillis()),
                null,
                null,
            )
        val jpaItemWithoutDepartment =
            JpaPayableItemStore(
                1,
                1000.0,
                CurrencyCode.USD,
                1,
                3,
                CountryCode.USA,
                4,
                2024,
                LineItemType.GROSS_SALARY,
                contractWithoutDepartmentJsonString,
                LocalDate.of(2024, 4, 1),
                LocalDate.of(2024, 4, 30),
                -1,
                -1,
                LocalDate.of(2024, 4, 1).atStartOfDay(),
                LocalDate.of(2024, 4, 1).atStartOfDay(),
                "1",
                Timestamp(System.currentTimeMillis()),
                null,
                null
            )
        val jpaPayableItems = listOf(jpaItemWithDepartment, jpaItemWithoutDepartment)

        // WHEN
        val mappedItems = grossSalaryPayableItemMapper.mapList(jpaPayableItems, InvoiceCycle.MONTHLY)

        // THEN
        assertThat(mappedItems).hasSize(2)
    }
}
