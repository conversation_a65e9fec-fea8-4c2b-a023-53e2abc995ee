package com.multiplier.core.payable.creditnote.facade

import com.multiplier.core.payable.config.RuntimeScalarWiring
import com.multiplier.core.payable.sync.jsonmapper.JsonObjectMapper
import com.multiplier.core.utility.TestFacilitator
import com.multiplier.payable.types.CreditNoteQueryInput
import com.multiplier.payable.types.CreditNotesResult
import com.multiplier.payable.types.PageResult
import com.netflix.graphql.dgs.DgsQueryExecutor
import com.netflix.graphql.dgs.autoconfig.DgsAutoConfiguration
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import java.util.Map

@SpringBootTest(
    classes = [
        DgsAutoConfiguration::class,
        RuntimeScalarWiring::class,
        CreditNoteDataFetcher::class,
        JsonObjectMapper::class
    ]
)
class CreditNoteDataFetcherIT {

    private val testFacilitator = TestFacilitator()

    @Autowired
    private lateinit var dgsQueryExecutor: DgsQueryExecutor

    @MockBean
    private lateinit var creditNoteWithPaginationService: CreditNoteWithPaginationService

    @Test
    fun givenQuery_whenGetCreditNotesWithPagination_thenReturnResult() {
        // GIVEN
        val relativeFilePath = "graphql/getCreditNotesWithPagination.graphql"
        val inputStream = this.javaClass.classLoader.getResourceAsStream(relativeFilePath)
        val query = testFacilitator.readFileAsString(inputStream)
        val input = CreditNoteQueryInput.newBuilder()
            .build()
        val expectedResult = CreditNotesResult.newBuilder()
            .data(listOf())
            .pageResult(
                PageResult.newBuilder()
                    .build()
            )
            .build()
        whenever(creditNoteWithPaginationService.get(input))
            .thenReturn(expectedResult)

        // WHEN
        val response = dgsQueryExecutor.executeAndExtractJsonPathAsObject(
            query,
            "data.creditNotesWithPagination",
            Map.of<String, Any>("input", testFacilitator.convertToMap(input)),
            CreditNotesResult::class.java
        )

        // THEN
        assertEquals(expectedResult, response)
    }
}