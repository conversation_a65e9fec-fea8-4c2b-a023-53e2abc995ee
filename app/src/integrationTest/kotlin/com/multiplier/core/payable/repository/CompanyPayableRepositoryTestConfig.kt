package com.multiplier.core.payable.repository

import com.multiplier.PayableApp
import com.multiplier.core.payable.creditnote.database.JpaCreditNote
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaInvoice
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
import org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.FilterType
import org.springframework.data.jpa.repository.config.EnableJpaRepositories

@Configuration
/**
 * Scan necessary autoconfiguration beans.
 */
@ImportAutoConfiguration(
    DataSourceAutoConfiguration::class,
    HibernateJpaAutoConfiguration::class,
    SqlInitializationAutoConfiguration::class,
)
/**
 * Scan necessary JPA entities.
 */
@EntityScan(
    basePackageClasses = [
        JpaCompanyPayable::class,
        JpaInvoice::class,
        JpaCreditNote::class,
    ]
)
/**
 * Scan necessary JPA repositories.
 */
@EnableJpaRepositories(
    basePackageClasses = [PayableApp::class],
    includeFilters = [ComponentScan.Filter(
        type = FilterType.ASSIGNABLE_TYPE,
        value = [
            JpaCompanyPayableRepository::class,
        ]
    )]
)
class CompanyPayableRepositoryTestConfig