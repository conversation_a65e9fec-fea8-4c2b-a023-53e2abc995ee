package com.multiplier.dispute.database.page

import com.multiplier.PayableApp
import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.core.config.AuditConfig
import com.multiplier.dispute.database.JpaDispute
import com.multiplier.dispute.database.JpaDisputeRepository
import com.multiplier.dispute.database.JpaDisputeToApiMapper
import com.multiplier.dispute.database.specification.BaseDisputeSpecificationBuilder
import com.multiplier.dispute.database.specification.DisputeSpecificationBuilderFactory
import com.multiplier.dispute.database.specification.InSpecificationBuilder
import com.multiplier.dispute.origin.database.JpaDisputeOrigin
import com.multiplier.dispute.origin.database.JpaDisputeOriginRepository
import com.multiplier.dispute.origin.database.JpaDisputeOriginToApiMapper
import com.multiplier.dispute.origin.database.ReadDisputeOriginService
import com.multiplier.payable.engine.payableitem.TestCurrentUser
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
import org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.FilterType
import org.springframework.context.annotation.Import
import org.springframework.data.jpa.repository.config.EnableJpaRepositories

@Configuration
/**
 * Scan necessary autoconfiguration beans.
 */
@ImportAutoConfiguration(
    DataSourceAutoConfiguration::class,
    HibernateJpaAutoConfiguration::class,
    SqlInitializationAutoConfiguration::class,
)
/**
 * Scan necessary JPA entities.
 */
@EntityScan(
    basePackageClasses = [
        JpaDisputeOrigin::class,
        JpaDispute::class
    ]
)
/**
 * Scan necessary JPA repositories.
 */
@EnableJpaRepositories(
    basePackageClasses = [PayableApp::class],
    includeFilters = [ComponentScan.Filter(
        type = FilterType.ASSIGNABLE_TYPE,
        value = [
            JpaDisputeOriginRepository::class,
            JpaDisputeRepository::class
        ]
    )]
)
@Import(
    value = [
        AuditConfig::class,

        DisputePageService::class,

        UserAwareDisputeOriginProvider::class,
        ReadDisputeOriginService::class,
        JpaDisputeOriginToApiMapper::class,

        JpaDisputeToApiMapper::class,

        DisputeSpecificationBuilderFactory::class,
        BaseDisputeSpecificationBuilder::class,
        InSpecificationBuilder::class,

        DisputePageRequestToDisputeSpecificationRequestMapper::class,

        DisputePageRequestBuilder::class,
        DisputeSortOrderBuilder::class,
    ]
)

class DisputePageTestConfiguration {

    @Bean
    fun currentUser(): CurrentUser {
        return TestCurrentUser()
    }
}