package com.multiplier.core.payable.invoice;

import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.adapters.api.LineItemDTO;
import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.invoice.api.InvoiceAdapter;
import com.multiplier.core.payable.invoice.api.NetsuiteInvoiceAdapter;
import com.multiplier.core.payable.repository.model.InvoiceReason;
import com.multiplier.payable.types.CurrencyCode;
import com.multiplier.payable.types.InvoiceStatus;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.time.LocalDate;
import java.util.List;

@Slf4j
public class InvoiceIntegrationTestUtils {

    private InvoiceIntegrationTestUtils() {} //not allowing object creation for this class. Only static methods

    public static String createInvoice(
            String invoiceNumber,
            String invoiceReference,
            String customerId,
            double invoiceAmount,
            InvoiceAdapter netsuiteInvoiceAdapter
    ) {
        val invoiceDto = InvoiceDTO.builder()
                .invoiceNo(
                        invoiceNumber
                )
                .date(LocalDate.now())
                .billingCurrencyCode(CurrencyCode.USD)
                .customerId(customerId)
                .lineItems(
                        List.of(
                                LineItemDTO.builder()
                                        .unitAmount(invoiceAmount)
                                        .amountInBaseCurrency(invoiceAmount)
                                        .startPayCycleDate(LocalDate.now())
                                        .endPayCycleDate(LocalDate.now())
                                        .taxType("GST_SG:OS-SG") // Get from the "netsuite-mapping-release.json"
                                        .baseCurrency("USD")
                                        .itemType(LineItemType.GROSS_SALARY)
                                        .build()
                        )
                )
                .reference(invoiceReference)
                .totalAmount(invoiceAmount)
                .date(LocalDate.now())
                .status(InvoiceStatus.AUTHORIZED)
                .reason(InvoiceReason.SECOND_INVOICE)
                .build();

        return netsuiteInvoiceAdapter.create(invoiceDto);
    }

    public static void deleteInvoice(String externalInvoiceId, InvoiceAdapter netsuiteInvoiceAdapter) {
        if (externalInvoiceId != null) {
            netsuiteInvoiceAdapter.delete(externalInvoiceId);
            log.info("deletedInvoiceId: {}", externalInvoiceId);
        }
    }
}
