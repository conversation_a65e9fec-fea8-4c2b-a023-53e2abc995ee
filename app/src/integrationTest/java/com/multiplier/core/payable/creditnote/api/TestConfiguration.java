package com.multiplier.core.payable.creditnote.api;

import com.multiplier.core.payable.adapters.netsuite.client.NetsuiteRestletClient;
import com.multiplier.core.payable.adapters.netsuite.models.savedsearch.SavedSearchResp;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;

import java.util.List;

@Configuration
class TestConfiguration {

    /**
     * Since I can't create NetsuiteRestletClient using @EnableFeignClients,
     * I just create an empty NetsuiteRestletClient to pass the current ITs first.
     * TODO Find a way to auto-wire NetsuiteRestletClient
     */
    @Bean
    public NetsuiteRestletClient netsuiteRestletClient() {

        return new NetsuiteRestletClient() {
            @Override
            public ResponseEntity<String> getPdfById(String id) {
                return null;
            }

            @Override
            public ResponseEntity<String> removeLineItem(String invoiceId, Integer lineItemLineNo) {
                return null;
            }

            @Override
            public ResponseEntity<Void> syncInvoicesForCustomerIds(final List<String> customerIds) {
                return null;
            }

            @Override
            public ResponseEntity<String> resyncFinancialTransactions(String commaSeperatedIds) {
                return null;
            }

            @Override
            public ResponseEntity<SavedSearchResp> downloadSavedSearchCSV(String savedSearchID, int pageStart, int pageEnd) {
                return null;
            }

            @Override
            public ResponseEntity<SavedSearchResp> downloadBankFeeBalanceSavedSearchCSV(String savedSearchID, int pageStart, int pageEnd, int month, int year) {
                return null;
            }
        };
    }

}
