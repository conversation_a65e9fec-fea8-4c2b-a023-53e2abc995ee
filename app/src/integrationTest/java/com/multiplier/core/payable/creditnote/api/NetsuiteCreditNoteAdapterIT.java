package com.multiplier.core.payable.creditnote.api;

import com.multiplier.core.config.featureflag.FeatureFlagService;
import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.adapters.netsuite.client.NetsuiteMappings;
import com.multiplier.core.payable.adapters.netsuite.mapper.NetsuiteWsInvoiceDTOMapperImpl;
import com.multiplier.core.payable.adapters.netsuite.mapper.NetsuiteWsInvoiceMapperImpl;
import com.multiplier.core.payable.adapters.netsuite.webservices.NetsuiteWebserviceClient;
import com.multiplier.core.payable.adapters.netsuite.webservices.NetsuiteWebserviceConfiguration;
import com.multiplier.core.payable.adapters.netsuite.webservices.NetsuiteWsProperties;
import com.multiplier.core.payable.invoice.InvoiceIntegrationTestUtils;
import com.multiplier.core.payable.invoice.api.InvoiceAdapter;
import com.multiplier.core.payable.invoice.api.InvoiceMapper;
import com.multiplier.core.payable.invoice.api.InvoiceStatusMapper;
import com.multiplier.core.payable.invoice.api.NetsuiteInvoiceAdapter;
import com.multiplier.core.payable.subsidiary.TempInvoiceSubsidiaryMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ActiveProfiles("test")
@SpringBootTest(
        classes = {
                // For invoice
                NetsuiteMappings.class,
                NetsuiteWsInvoiceMapperImpl.class,
                NetsuiteWebserviceClient.class,
                NetsuiteInvoiceAdapter.class,
                NetsuiteWsInvoiceDTOMapperImpl.class,
                InvoiceMapper.class,
                InvoiceStatusMapper.class,
                TempInvoiceSubsidiaryMapper.class,
                FeatureFlagService.class,

                // For credit note
                CreditMemoMapperImpl.class,
                CreditNoteResponseMapperImpl.class,
                ApiResponseParser.class,
                NetsuiteCreditNoteAdapter.class,
        }
)
@Import({
        NetsuiteWebserviceConfiguration.class,
        TestConfiguration.class
})
@EnableConfigurationProperties({NetsuiteWsProperties.class})
@Slf4j
class NetsuiteCreditNoteAdapterIT {

    private final static String currentDateFormat = "yyMMddHHmmssSSS";

    @Autowired
    private InvoiceAdapter invoiceAdapter;

    @Autowired
    private CreditNoteAdapter creditNoteAdapter;

    private final String customerIdOfACustomerThatMustNotBeDeleted = "21";

    private String invoiceId;

    private String creditNoteId;

    @AfterEach
    void tearDown() {
        deleteCreditNote(creditNoteId);
        deleteInvoice(invoiceId);
    }

    private void deleteCreditNote(String creditNoteId) {
        if (creditNoteId != null) {
            creditNoteAdapter.delete(creditNoteId);
            log.info("deletedCreditNoteId: {}", creditNoteId);
        }
    }

    private void deleteInvoice(String invoiceId) {
        InvoiceIntegrationTestUtils.deleteInvoice(invoiceId, invoiceAdapter);
    }

    @Test
    void givenCreateRequest_whenCreate_thenReturnCreatedCreditNote() {
        // GIVEN
        invoiceId = createInvoice();
        log.info("createdInvoiceId: {}", invoiceId);

        var createRequest = CreateCreditNoteApiRequest.builder()
                .externalInvoiceId(invoiceId)
                /*
                 * Don't add more fields
                 * because when creating credit notes on NS,
                 * fields are expected to inherit form the related invoice.
                 */
                .build();

        // WHEN
        var createdCreditNote = creditNoteAdapter.create(createRequest);
        creditNoteId = createdCreditNote.getExternalId();
        log.info("createdCreditNoteId: " + creditNoteId);

        // THEN
        assertNotNull(createdCreditNote.getExternalId());
    }

    private String createInvoice() {
        return InvoiceIntegrationTestUtils.createInvoice(
                "awesomeInvoiceNumber_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern(currentDateFormat)),
                "awesomeReference",
                customerIdOfACustomerThatMustNotBeDeleted,
                42d,
                invoiceAdapter
        );
    }

    @Test
    void givenUpdateRequest_whenUpdate_thenReturnUpdatedCreditNote() {
        // GIVEN
        // Create the credit note first.
        givenCreateRequest_whenCreate_thenReturnCreatedCreditNote();

        var updateRequest = UpdateCreditNoteApiRequest.builder()
                .externalId(creditNoteId)
                .reason(CreditNoteReason.DEPOSIT_REFUND)
                .reference("updatedAwesomeCreditNoteNumber")
                .externalInvoiceId(invoiceId)
                .build();

        // WHEN
        var updatedCreditNote = creditNoteAdapter.update(updateRequest);
        creditNoteId = updatedCreditNote.getExternalId();
        log.info("updatedCreditNoteId: " + creditNoteId);

        // THEN
        assertEquals(creditNoteId, updatedCreditNote.getExternalId());
    }

    @Test
    void givenCreateRequestWithoutExternalInvoiceIdAndMinimalFields_whenCreate_thenReturnCreatedCreditNote() {
        // GIVEN
        var createRequest = CreateCreditNoteApiRequest.builder()
                // externalInvoiceId omitted
                .customerId(customerIdOfACustomerThatMustNotBeDeleted)
                .items(List.of(
                        CreateCreditNoteItemApiRequest.builder()
                                .itemType(LineItemType.GROSS_SALARY)
                                .unitAmount(42d)
                                .taxCode("GST_SG:OS-SG")
                                /*
                                 * Don't pass more field
                                 * because this test creates a credit note with *mandatory* fields only.
                                 */
                                .build()
                ))
                /*
                 * Don't pass more field
                 * because this test creates a credit note with *mandatory* fields only.
                 */
                .build();

        // WHEN
        var createdCreditNote = creditNoteAdapter.create(createRequest);
        creditNoteId = createdCreditNote.getExternalId();
        log.info("createdCreditNoteId: " + creditNoteId);

        // THEN
        assertNotNull(createdCreditNote.getExternalId());
    }

    @Test
    void givenCreateRequestWithoutExternalInvoiceIdAndAllFields_whenCreate_thenReturnCreatedCreditNote() {
        // GIVEN
        var now = LocalDate.now();
        var createRequest = CreateCreditNoteApiRequest.builder()
                // externalInvoiceId omitted
                .customerId(customerIdOfACustomerThatMustNotBeDeleted)
                .reason(CreditNoteReason.INVOICE_VOIDED)
                .reference("awesomeReference")
                .date(LocalDate.now())
                .status("FULLY APPLIED")
                .items(List.of(
                        CreateCreditNoteItemApiRequest.builder()
                                .itemType(LineItemType.GROSS_SALARY)
                                .description("awesomeDescription")
                                .unitAmount(42d)
                                .taxCode("GST_SG:OS-SG") // Get from the "netsuite-mapping-release.json"
                                .countryName("United States")
                                .contractId(42L)
                                .memberName("John Doe")
                                .baseCurrency("USD")
                                .amountInBaseCurrency(42d)
                                .revRecStartDate(now)
                                .revRecEndDate(now)
                                .build()
                ))
                .build();

        // WHEN
        var createdCreditNote = creditNoteAdapter.create(createRequest);
        creditNoteId = createdCreditNote.getExternalId();
        log.info("createdCreditNoteId: " + creditNoteId);

        // THEN
        assertNotNull(createdCreditNote.getExternalId());
    }

    @Test
    void givenCreditNoteWithoutInvoice_whenUpdate_thenReturn() {
        // GIVEN
        givenCreateRequestWithoutExternalInvoiceIdAndAllFields_whenCreate_thenReturnCreatedCreditNote();
        var updateRequest = UpdateCreditNoteApiRequest.builder()
                // externalInvoiceId omitted
                .customerId(customerIdOfACustomerThatMustNotBeDeleted)
                .invoiceSourceReportLink("http://test.link")
                .externalId(creditNoteId)
                .build();

        // WHEN
        var updatedCreditNote = creditNoteAdapter.update(updateRequest);
        creditNoteId = updatedCreditNote.getExternalId();
        log.info("updatedCreditNoteId: " + creditNoteId);

        // THEN
        assertNotNull(updatedCreditNote.getExternalId());
    }

    // TODO Test REST API a.k.a NetsuiteRestletClient

}