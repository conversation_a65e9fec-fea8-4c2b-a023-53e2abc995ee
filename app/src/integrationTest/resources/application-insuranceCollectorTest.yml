spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driverClassName: org.h2.Driver
    username: sa
    password:
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
    show-sql: false
  h2:
    console:
      enabled: false
  sql:
    init:
      schema-locations: classpath:schema-h2-insuranceCollectorTest.sql
      continue-on-error: true
      mode: always

logging:
  level:
    root: ERROR
    org:
      springframework: ERROR
      hibernate: ERROR
