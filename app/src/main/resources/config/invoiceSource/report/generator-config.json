[{"report_name": "company-invoice-source-detailed-report", "report_category": "COMPANY_INVOICE_SOURCE_DETAILED_REPORT", "type": "XLSX_MULTI_SHEET", "column_names": ["Company ID", "Company Legal Name", "Contract ID", "Member Name", "Country", "Contract Start Date", "Contract End Date", "Gross Salary", "Bonus", "Commission", "Allowances", "Employer Contribution", "Expenses", "Client Deductions", "Total Payroll Cost (Local currency)", "Transaction Currency", "Fx", "Payroll Cost - GST amount", "Total Payroll Cost (Billing Currency)", "Severance Amount (Local Currency)", "Severance Amount (Billing Currency)", "Billing <PERSON><PERSON><PERSON>cy", "Amount Billed in 1st Invoice", "Management Fee(Base Currency)", "Management Fee Fx rate", "Management Fees(Billing Currency)", "GST Amount", "Total Management Fees", "M fee billed in 1st invoice (Billing currency)", "Processing Fee (Billing currency)", "Bank Fee Recovery", "Balance Amount To Billed", "Total Cost (Billing Currency)"], "columns": [{"country_code": "PRK", "columns": [{"name": "Company ID", "alias": "Company ID", "format": "", "type": "STRING", "group": "DEFAULT", "formula": ""}, {"name": "Company Legal Name", "alias": "Company Legal Name", "format": "", "type": "STRING", "group": "DEFAULT", "formula": ""}, {"name": "Contract ID", "alias": "Contract ID", "format": "", "type": "STRING", "group": "DEFAULT", "formula": ""}, {"name": "Member Name", "alias": "Member Name", "format": "", "type": "STRING", "group": "DEFAULT", "formula": ""}, {"name": "Country", "alias": "Country", "format": "", "type": "STRING", "group": "DEFAULT", "formula": ""}, {"name": "Contract Start Date", "alias": "Contract Start Date", "format": "", "type": "STRING", "group": "DEFAULT", "formula": ""}, {"name": "Contract End Date", "alias": "Contract End Date", "format": "", "type": "STRING", "group": "DEFAULT", "formula": ""}, {"name": "Gross Salary", "alias": "Gross Salary", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": "", "previousNames": ["Gross Salary"]}, {"name": "Bonus", "alias": "Bonus", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Commission", "alias": "Commission", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Allowances", "alias": "Allowances", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Employer Contribution", "alias": "Employer Contribution", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Expenses", "alias": "Expenses", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Client Deductions", "alias": "Client Deductions", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Total Payroll Cost (Local currency)", "alias": "Total Payroll Cost (Local currency)", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Transaction Currency", "alias": "Transaction Currency", "format": "", "type": "STRING", "group": "DEFAULT", "formula": ""}, {"name": "Fx", "alias": "Fx", "format": "0.0000", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Payroll Cost - GST amount", "alias": "Payroll Cost - GST amount", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Total Payroll Cost (Billing Currency)", "alias": "Total Payroll Cost (Billing Currency)", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Severance Amount (Local Currency)", "alias": "Severance Amount (Local Currency)", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Severance Amount (Billing Currency)", "alias": "Severance Amount (Billing Currency)", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Billing <PERSON><PERSON><PERSON>cy", "alias": "Billing <PERSON><PERSON><PERSON>cy", "format": "", "type": "STRING", "group": "DEFAULT", "formula": ""}, {"name": "Amount Billed in 1st Invoice", "alias": "Amount Billed in 1st Invoice", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Management Fee(Base Currency)", "alias": "Management Fee(Base Currency)", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Management Fee Fx rate", "alias": "Management Fee Fx rate", "format": "0.0000", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Management Fees(Billing Currency)", "alias": "Management Fees(Billing Currency)", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "GST Amount", "alias": "GST Amount", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Total Management Fees", "alias": "Total Management Fees", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "M fee billed in 1st invoice (Billing currency)", "alias": "M fee billed in 1st invoice (Billing currency)", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Processing Fee (Billing currency)", "alias": "Processing Fee (Billing currency)", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Annual Plan Fee (including GST)", "alias": "Annual Plan Fee (including GST)", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "VAT Billing Currency", "alias": "VAT Billing Currency", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "VAT - GST Amount", "alias": "VAT - GST Amount", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Total VAT Amount", "alias": "Total VAT Amount", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Bank Fee Recovery", "alias": "Bank Fee Recovery", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Balance Amount To Billed", "alias": "Balance Amount To Billed", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}, {"name": "Total Cost (Billing Currency)", "alias": "Total Cost (Billing Currency)", "format": "0.00", "type": "DOUBLE", "group": "DEFAULT", "formula": ""}], "freeze_cols": 0, "freeze_rows": 1, "has_formula_row": 0}]}, {"report_name": "company-invoice-source-compare-detailed-report", "report_category": "COMPANY_INVOICE_SOURCE_COMPARE_DETAILED_REPORT", "type": "XLSX_MULTI_SHEET", "column_names": ["Company ID", "Company Legal Name", "Contract ID", "Member Name", "Country", "Contract Start Date", "Contract End Date", "Gross Salary", "Bonus", "Commission", "Allowances", "Employer Contribution", "Expenses", "Client Deductions", "Total Payroll Cost (Local currency)", "Transaction Currency", "Fx", "Total Payroll Cost (Billing Currency)", "Billing <PERSON><PERSON><PERSON>cy", "Amount Billed in 1st Invoice", "Management Fee(Base Currency)", "Management Fee Fx rate", "Management Fees(Billing Currency)", "GST Amount", "Total Management Fees", "M fee billed in 1st invoice (Billing currency)", "Processing Fee (Billing currency)", "Annual Plan Fee (including GST)", "VAT Billing Currency", "VAT - GST Amount", "Total VAT Amount", "Bank Fee Recovery", "Balance Amount To Billed", "Total Cost (Billing Currency)"]}]