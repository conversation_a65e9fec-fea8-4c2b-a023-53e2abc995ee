{"COUNTRY_ID_MAP": {"configMap": {"Asia Pacific": "1", "HQ": "2", "Africa": "102", "Americas": "103", "EMEA": "104", "Europe": "105", "Global": "106", "South/Latin America": "107", "Middle East": "108", "North America": "109", "UKI": "110", "Singapore": "111", "Malaysia": "112", "Indonesia": "113", "Philippines": "114", "India": "115", "Hong Kong SAR China": "116", "Australia": "117", "Thailand": "118", "Sri Lanka": "119", "Vietnam": "120", "Japan": "121", "Pakistan": "122", "Bangladesh": "123", "China": "124", "Taiwan": "125", "New Zealand": "126", "Nepal": "127", "South Korea": "128", "Maldives": "129", "Uzbekistan": "130", "Cambodia": "131", "Myanmar": "132", "Azerbaijian": "133", "Azerbaijan": "133", "Singapore - MSPL": "134", "Kazakhstan": "135", "Macao": "136", "Netherlands": "137", "Russia": "138", "United Kingdom": "139", "Isle of Man": "140", "Turkey": "141", "Denmark": "142", "Gibraltar": "143", "Portugal": "144", "Norway": "145", "Germany": "146", "Belgium": "147", "France": "148", "Lithuania": "149", "Ukraine": "150", "Bulgaria": "151", "Sweden": "152", "Finland": "153", "Italy": "154", "Austria": "155", "Spain": "156", "Switzerland": "157", "Luxembourg": "158", "Malta": "159", "Ireland": "160", "Croatia": "161", "Greece": "162", "Hungary": "163", "Iceland": "164", "Romania": "165", "Serbia": "166", "Latvia": "167", "Estonia": "168", "Georgia": "169", "Czech Republic": "170", "Czechia": "170", "Poland": "171", "Albania": "172", "Slovakia": "173", "Liechtenstein": "174", "Canada": "175", "United States": "176", "Peru": "177", "Mexico": "178", "Colombia": "179", "Guatemala": "180", "Venezuela": "181", "Bolivia": "182", "Jamaica": "183", "Puerto Rico": "184", "Chile": "185", "Uruguay": "186", "Panama": "187", "Argentina": "188", "El Salvador": "189", "Brazil": "190", "Ecuador": "191", "UAE": "192", "United Arab Emirates": "192", "Egypt": "193", "Kuwait": "194", "Cyprus": "195", "Libya": "196", "Saudi Arabia": "197", "Ghana": "198", "South Africa": "199", "Morocco": "200", "Kenya": "201", "Nigeria": "202", "Senegal": "203", "Madagascar": "204", "Mauritius": "205", "Mozambique": "206", "Tanzania": "207", "APAC": "216", "St Vincent and the Grenadines": "311", "Laos": "312", "Kyrgyzstan": "313", "Bhutan": "314", "Israel": "316", "Republic of North Macedonia": "317", "Montenegro": "318", "Armenia": "319", "Belarus": "320", "Bosnia and Herzegovina": "321", "Costa Rica": "323", "Dominican Republic": "324", "Saint Lucia": "325", "Barbados": "326", "Honduras": "327", "Qatar": "328", "Uganda": "329", "Benin": "330", "Côte D'Ivoire": "331", "Burkina Faso": "333", "Congo": "334", "Rwanda": "335", "Congo, The Democratic Republic of the": "336", "Tunisia": "337", "Togo": "338", "Curacao": "339", "Belize": "340", "Haiti": "341", "Bahamas": "342", "Slovenia": "343", "Solvenia": "345", "Paraguay": "346", "Jordan": "347", "Yemen": "348", "Algeria": "399", "Malawi": "400", "North America : Saint Barthélemy": "401", "Kosovo": "402", "Ethiopia": "403", "Zambia": "404", "Niger": "406", "Zimbabwe": "407", "Liberia": "408", "Botswana": "409", "Trinidad and Tobago": "410", "Bahrain": "411", "Guyana": "412", "San Marino": "413", "Lebanon": "414", "Nicaragua": "415", "Guam": "416", "Cayman Islands": "417"}, "defaultValue": null}, "COUNTRY_CODE_MAP": {"configMap": {"Afghanistan": "AFG", "Aland Islands": "ALA", "Albania": "ALB", "Algeria": "DZA", "American Samoa": "ASM", "Andorra": "AND", "Angola": "AGO", "Anguilla": "AIA", "Antarctica": "ATA", "Antigua and Barbuda": "ATG", "Argentina": "ARG", "Armenia": "ARM", "Aruba": "ABW", "Australia": "AUS", "Austria": "AUT", "Azerbaijan": "AZE", "Bahamas": "BHS", "Bahrain": "BHR", "Bangladesh": "BGD", "Barbados": "BRB", "Belarus": "BLR", "Belgium": "BEL", "Belize": "BLZ", "Benin": "BEN", "Bermuda": "BMU", "Bhutan": "BTN", "Bolivia": "BOL", "Bonaire, Sint Eustatius and Saba": "BES", "Bosnia and Herzegovina": "BIH", "Botswana": "BWA", "Bouvet Island": "BVT", "Brazil": "BRA", "British Indian Ocean Territory": "IOT", "Brunei Darussalam": "BRN", "Bulgaria": "BGR", "Burkina Faso": "BFA", "Burundi": "BDI", "Cambodia": "KHM", "Cameroon": "CMR", "Canada": "CAN", "Cape Verde": "CPV", "Cayman Islands": "CYM", "Central African Republic": "CAF", "Chad": "TCD", "Chile": "CHL", "China": "CHN", "Christmas Island": "CXR", "Cocos (Keeling) Islands": "CCK", "Colombia": "COL", "Comoros": "COM", "Congo, Democratic Republic of the": "COD", "Congo, Republic of the": "COG", "Cook Islands": "COK", "Costa Rica": "CRI", "Cote d'Ivoire": "CIV", "Croatia": "HRV", "Cuba": "CUB", "Curacao": "CUW", "Cyprus": "CYP", "Czech Republic": "CZE", "Denmark": "DNK", "Djibouti": "DJI", "Dominica": "DMA", "Dominican Republic": "DOM", "East Timor": "TLS", "Ecuador": "ECU", "Egypt": "EGY", "El Salvador": "SLV", "Equatorial Guinea": "GNQ", "Eritrea": "ERI", "Estonia": "EST", "Ethiopia": "ETH", "Falkland Islands": "FLK", "Faroe Islands": "FRO", "Fiji": "FJI", "Finland": "FIN", "France": "FRA", "French Guiana": "GUF", "French Polynesia": "PYF", "French Southern Territories": "ATF", "Gabon": "GAB", "Gambia": "GMB", "Georgia": "GEO", "Germany": "DEU", "Ghana": "GHA", "Gibraltar": "GIB", "Greece": "GRC", "Greenland": "GRL", "Grenada": "GRD", "Guadeloupe": "GLP", "Guam": "GUM", "Guatemala": "GTM", "Guernsey": "GGY", "Guinea": "GIN", "Guinea-Bissau": "GNB", "Guyana": "GUY", "Haiti": "HTI", "Heard Island and McDonald Islands": "HMD", "Holy See (Vatican City State)": "VAT", "Honduras": "HND", "Hong Kong": "HKG", "Hungary": "HUN", "Iceland": "ISL", "India": "IND", "Indonesia": "IDN", "Iran (Islamic Republic of)": "IRN", "Iraq": "IRQ", "Ireland": "IRL", "Isle of Man": "IMN", "Israel": "ISR", "Italy": "ITA", "Jamaica": "JAM", "Japan": "JPN", "Jersey": "JEY", "Jordan": "JOR", "Kazakhstan": "KAZ", "Kenya": "KEN", "Kiribati": "KIR", "Korea, Democratic People's Republic of": "PRK", "Korea, Republic of": "KOR", "Kosovo": "BLM", "Kuwait": "KWT", "Kyrgyzstan": "KGZ", "Lao People's Democratic Republic": "LAO", "Latvia": "LVA", "Lebanon": "LBN", "Lesotho": "LSO", "Liberia": "LBR", "Libya": "LBY", "Liechtenstein": "LIE", "Lithuania": "LTU", "Luxembourg": "LUX", "Macau": "MAC", "Macedonia": "MKD", "Madagascar": "MDG", "Malawi": "MWI", "Malaysia": "MYS", "Maldives": "MDV", "Mali": "MLI", "Malta": "MLT", "Marshall Islands": "MHL", "Martinique": "MTQ", "Mauritania": "MRT", "Mauritius": "MUS", "Mayotte": "MYT", "Mexico": "MEX", "Micronesia (Federated States of)": "FSM", "Moldova (Republic of)": "MDA", "Monaco": "MCO", "Mongolia": "MNG", "Montenegro": "MNE", "Montserrat": "MSR", "Morocco": "MAR", "Mozambique": "MOZ", "Myanmar": "MMR", "Namibia": "NAM", "Nauru": "NRU", "Nepal": "NPL", "Netherlands": "NLD", "New Caledonia": "NCL", "New Zealand": "NZL", "Nicaragua": "NIC", "Niger": "NER", "Nigeria": "NGA", "Niue": "NIU", "Norfolk Island": "NFK", "Northern Mariana Islands": "MNP", "Norway": "NOR", "Oman": "OMN", "Pakistan": "PAK", "Palau": "PLW", "Panama": "PAN", "Papua New Guinea": "PNG", "Paraguay": "PRY", "Peru": "PER", "Philippines": "PHL", "Pitcairn": "PCN", "Poland": "POL", "Portugal": "PRT", "Puerto Rico": "PRI", "Qatar": "QAT", "Reunion": "REU", "Romania": "ROU", "Russian Federation": "RUS", "Rwanda": "RWA", "Saint Barthelemy": "BLM", "Saint Helena": "SHN", "Saint Kitts and Nevis": "KNA", "Saint Lucia": "LCA", "Saint Martin": "MAF", "Saint Vincent and the Grenadines": "VCT", "Samoa": "WSM", "San Marino": "SMR", "Sao Tome and Principe": "STP", "Saudi Arabia": "SAU", "Senegal": "SEN", "Serbia": "SRB", "Seychelles": "SYC", "Sierra Leone": "SLE", "Singapore": "SGP", "Sint Maarten": "SXM", "Slovak Republic": "SVK", "Slovenia": "SVN", "Solomon Islands": "SLB", "Somalia": "SOM", "South Africa": "ZAF", "South Georgia and the South Sandwich Islands": "SGS", "South Sudan": "SSD", "Spain": "ESP", "Sri Lanka": "LKA", "State of Palestine": "PSE", "Saint Pierre and Miquelon": "SPM", "Sudan": "SDN", "Suriname": "SUR", "Svalbard and Jan Mayen Islands": "SJM", "Swaziland": "SWZ", "Sweden": "SWE", "Switzerland": "CHE", "Syrian Arab Republic": "SYR", "Taiwan": "TWN", "Tajikistan": "TJK", "Tanzania": "TZA", "Thailand": "THA", "Togo": "TGO", "Tokelau": "TKL", "Tonga": "TON", "Trinidad and Tobago": "TTO", "Tunisia": "TUN", "Turkey": "TUR", "Turkmenistan": "TKM", "Turks and Caicos Islands": "TCA", "Tuvalu": "TUV", "Uganda": "UGA", "Ukraine": "UKR", "United Arab Emirates": "ARE", "United Kingdom": "GBR", "United States": "USA", "Uruguay": "URY", "US Minor Outlying Islands": "UMI", "Uzbekistan": "UZB", "Vanuatu": "VUT", "Venezuela": "VEN", "Vietnam": "VNM", "Virgin Islands (British)": "VGB", "Virgin Islands (U.S.)": "VIR", "Wallis and Futuna Islands": "WLF", "Western Sahara": "ESH", "Yemen": "YEM", "Zambia": "ZMB", "Zimbabwe": "ZWE", "Congo": "COG", "Congo, The Democratic Republic of the": "COD", "Côte D'Ivoire": "CIV", "Solvenia": "SVN", "Azerbaijian": "AZE", "Korea": "KOR", "Laos": "LAO", "Macao": "MAC", "Singapore - MSPL": "SGP", "Netherland": "NLD", "Republic of North Macedonia": "MKD", "Russia": "RUS", "Slovakia": "SVK", "Jordon": "JOR", "UAE": "ARE", "North America": "USA", "Saint Barthélemy": "BLM", "USA": "USA", "Alabama": "USA", "Alaska": "USA", "Arizona": "USA", "Arkansas": "USA", "California": "USA", "Colorado": "USA", "Connecticut": "USA", "Delaware": "USA", "Florida": "USA", "Hawaii": "USA", "Idaho": "USA", "Illinois": "USA", "Indiana": "USA", "Iowa": "USA", "Kansas": "USA", "Kentucky": "USA", "Louisiana": "USA", "Maine": "USA", "Maryland": "USA", "Massachusetts": "USA", "Michigan": "USA", "Minnesota": "USA", "Mississippi": "USA", "Missouri": "USA", "Montana": "USA", "Nebraska": "USA", "Nevada": "USA", "New Hampshire": "USA", "New Jersey": "USA", "New Mexico": "USA", "New York": "USA", "North Carolina": "USA", "North Dakota": "USA", "Ohio": "USA", "Oklahoma": "USA", "Oregon": "USA", "Pennsylvania": "USA", "Rhode Island": "USA", "South Carolina": "USA", "South Dakota": "USA", "Tennessee": "USA", "Texas": "USA", "Utah": "USA", "Vermont": "USA", "Virginia": "USA", "Washington": "USA", "West Virginia": "USA", "Wisconsin": "USA", "Wyoming": "USA", "South/Latin America": "SRR", "ST Vincent and the Grenadines": "VCT", "UKI": "GBR", "UK": "GBR"}, "defaultValue": null}, "CURRENCY_ID_MAP": {"configMap": {"USD": "1", "GBP": "2", "CAD": "3", "EUR": "4", "SGD": "5", "MYR": "6", "INR": "7", "PHP": "8", "THB": "9", "IDR": "10", "AUD": "11", "BDT": "12", "CNY": "13", "JPY": "14", "KRW": "15", "PEN": "16", "LKR": "17", "CHF": "18", "TWD": "19", "VND": "20", "COP": "21", "ALL": "22", "AZN": "23", "BBD": "24", "BOB": "25", "BRL": "26", "BGN": "27", "KHR": "28", "CLP": "29", "CRC": "30", "CZK": "31", "AED": "32", "DKK": "33", "UAH": "34", "EGP": "35", "TRY": "36", "GEL": "37", "TZS": "38", "GIP": "39", "GTQ": "40", "ILS": "41", "KES": "42", "ZAR": "43", "MGA": "44", "MUR": "45", "MXN": "46", "NZD": "47", "NGN": "48", "RSD": "49", "RWF": "50", "RON": "51", "PLN": "52", "HKD": "53", "BYN": "54", "HUF": "55", "PKR": "56", "RUB": "57", "KZT": "58", "MZN": "59", "SEK": "60", "NPR": "61", "HNL": "62", "LAK": "63", "MKD": "64", "AMD": "65", "ISK": "66", "UYU": "67", "UGX": "68", "ARS": "70", "DOP": "324", "XCD": "325", "XOF": "203", "IMP": "78", "PAB": "79"}, "defaultValue": null}, "ITEM_ID_MAP": {"configMap": {"EOR Gross Salary": "626", "EOR Expense Disbursement": "627", "EOR Salary Disbursement": "627", "Freelancer Payment": "649", "Insurance Premium": "629", "Laptop Fee - Others": "636", "Laptop Fee - Singapore": "637", "Management Fee - EOR": "638", "Management Fee - Freelancers": "650", "Management Fee - Others": "640", "Management Fee - PEO": "641", "Member Deposit": "648", "Other Services": "643", "Other Setup Fee": "644", "Payment Fee": "651", "PEO Salary Disbursement": "634", "Visa Fee": "652", "Platform Fee": "813", "Additional Payroll Expense": "1118", "Severance Deposit": "1123", "Additional Management Fee - EOR": "1128", "One Time Setup Fee": "827", "Pay Slip Minimum Commitment": "641", "Per Pay Slip Fee": "641", "Total Payments": "831", "Stat Filing": "831", "Year End Documentation": "830", "GP-Fund Request": "807", "vas incident laptop payment": "636", "vas incident laptop management fee": "640", "vas incident monitor payment": "636", "vas incident monitor management fee": "640", "vas incident accessories payment": "636", "vas incident accessories management fee": "640", "vas incident discount": "640", "vas incident pickup delivery amount": "643", "vas incident pickup delivery fee": "640", "vas incident storage amount": "643", "vas incident storage fee": "640", "vas incident contract customisation fee": "640", "vas incident legal consultation fee": "640", "vas incident others service amount": "643", "vas incident others service fee": "640", "vas incident others visa fee": "652", "Order Form Advance - EOR": "1141", "Order Form Advance - AOR": "1148", "Order Form Advance - Global Payroll": "1149", "Order Form Advance Adjustment - Global Payroll": "1152", "Annual Management Fee - EOR": "1150", "Annual Management Fee AOR - Contractor": "1151", "Annual Management Fee AOR - Freelancer": "1151", "Order Form Advance Adjustment - AOR": "1148", "Order Form Advance Adjustment - EOR": "1141", "bank fees EOR": "805", "Offcycle MF - EOR": "1135", "Standard Background Verification Checks Fee": "1153"}, "defaultValue": null}, "XERO_GL_CODE_ID_MAP": {"configMap": {"520-000": "2052", "523-000": "2025", "541-000": "2051", "530-000": "2338", "531-000": "2337", "532-000": "2334", "647-000": "2456", "140-10-101-1": "3199"}, "defaultValue": null}, "TAX_CODE_MAP": {"configMap": {"SGP:GROSS_SALARY": "GST_SG:SR-SG", "SGP:EOR_SALARY_DISBURSEMENT": "GST_SG:SR-SG", "SGP:EOR_EXPENSE_DISBURSEMENT": "GST_SG:SR-SG", "SGP:PEO_SALARY_DISBURSEMENT": "GST_SG:OS-SG", "SGP:MEMBER_DEPOSIT": "GST_SG:OS-SG", "SGP:FREELANCER_PAYMENT": "GST_SG:OS-SG", "SGP:MANAGEMENT_FEE_EOR": "GST_SG:SR-SG", "SGP:MANAGEMENT_FEE_EOR_PAYROLL": "GST_SG:SR-SG", "SGP:BILLED_MANAGEMENT_FEE": "GST_SG:SR-SG", "SGP:BILLED_GROSS_SALARY_SUPPLEMENTARY": "GST_SG:SR-SG", "SGP:BILLED_GROSS_SALARY": "GST_SG:SR-SG", "SGP:ANNUAL_MANAGEMENT_FEE_EOR": "GST_SG:SR-SG", "SGP:ANNUAL_MANAGEMENT_FEE_AOR_CONTRACTOR": "GST_SG:SR-SG", "SGP:ANNUAL_MANAGEMENT_FEE_AOR_FREELANCER": "GST_SG:SR-SG", "SGP:ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL": "GST_SG:SR-SG", "SGP:INSURANCE_PREMIUM": "GST_SG:SR-SG", "SGP:MANAGEMENT_FEE_FREELANCER": "GST_SG:SR-SG", "SGP:PAYMENT_FEE": "GST_SG:SR-SG", "SGP:PLATFORM_FEE": "GST_SG:SR-SG", "SGP:VAT_PAYROLL_COST": "GST_SG:SR-SG", "SGP:VAT_PAYROLL_EXPENSE": "GST_SG:SR-SG", "SGP:VAT_MANAGEMENT_FEE_EOR_PAYROLL": "GST_SG:SR-SG", "SGP:VAT_ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL": "GST_SG:SR-SG", "SGP:VAT_BILLED_MANAGEMENT_FEE": "GST_SG:SR-SG", "SGP:SEVERANCE_DEPOSIT_EOR_PAYROLL": "GST_SG:OS-SG", "SGP:FREELANCER_PAYOUT_FEE": "GST_SG:OS-SG", "SGP:VAS_INCIDENT_LAPTOP_PAYMENT": "GST_SG:SR-SG", "SGP:VAS_INCIDENT_LAPTOP_MANAGEMENT_FEE": "GST_SG:SR-SG", "SGP:VAS_INCIDENT_MONITOR_PAYMENT": "GST_SG:ZR-SG", "SGP:VAS_INCIDENT_MONITOR_MANAGEMENT_FEE": "GST_SG:ZR-SG", "SGP:VAS_INCIDENT_ACCESSORIES_PAYMENT": "GST_SG:ZR-SG", "SGP:VAS_INCIDENT_ACCESSORIES_MANAGEMENT_FEE": "GST_SG:ZR-SG", "SGP:VAS_INCIDENT_DISCOUNT": "GST_SG:ZR-SG", "SGP:VAS_INCIDENT_PICKUP_DELIVERY_AMOUNT": "GST_SG:ZR-SG", "SGP:VAS_INCIDENT_PICKUP_DELIVERY_FEE": "GST_SG:ZR-SG", "SGP:VAS_INCIDENT_STORAGE_AMOUNT": "GST_SG:ZR-SG", "SGP:VAS_INCIDENT_STORAGE_FEE": "GST_SG:ZR-SG", "SGP:VAS_INCIDENT_CONTRACT_CUSTOMISATION_FEE": "GST_SG:ZR-SG", "SGP:VAS_INCIDENT_LEGAL_CONSULTATION_FEE": "GST_SG:ZR-SG", "SGP:VAS_INCIDENT_OTHERS_SERVICE_AMOUNT": "GST_SG:ZR-SG", "SGP:VAS_INCIDENT_OTHERS_SERVICE_FEE": "GST_SG:ZR-SG", "SGP:VAS_INCIDENT_OTHERS_VISA_FEE": "GST_SG:ZR-SG", "SGP:SEVERANCE_DEPOSIT_EOR_PAYROLL_REFUND": "GST_SG:OS-SG", "SGP:OFFCYCLE_PAYROLL_COST": "GST_SG:SR-SG", "SGP:OFFCYCLE_PAYROLL_EXPENSE": "GST_SG:SR-SG", "SGP:OFFCYCLE_MANAGEMENT_FEE": "GST_SG:SR-SG", "SGP:BANK_FEE": "GST_SG:SR-SG", "FREELANCER_PAYMENT": "GST_SG:OS-SG", "MEMBER_DEPOSIT": "GST_SG:OS-SG", "GROSS_SALARY": "GST_SG:ZR-SG", "MANAGEMENT_FEE_EOR_PAYROLL": "GST_SG:ZR-SG", "BILLED_GROSS_SALARY": "GST_SG:ZR-SG", "BILLED_MANAGEMENT_FEE": "GST_SG:ZR-SG", "BILLED_GROSS_SALARY_SUPPLEMENTARY": "GST_SG:ZR-SG", "EOR_SALARY_DISBURSEMENT": "GST_SG:ZR-SG", "EOR_EXPENSE_DISBURSEMENT": "GST_SG:ZR-SG", "PEO_SALARY_DISBURSEMENT": "GST_SG:OS-SG", "VAT_PAYROLL_COST": "GST_SG:ZR-SG", "VAT_PAYROLL_EXPENSE": "GST_SG:ZR-SG", "VAT_MANAGEMENT_FEE_EOR_PAYROLL": "GST_SG:ZR-SG", "VAT_BILLED_MANAGEMENT_FEE": "GST_SG:ZR-SG", "VAT_ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL": "GST_SG:ZR-SG", "SEVERANCE_DEPOSIT_EOR_PAYROLL": "GST_SG:OS-SG", "SGP:ONE_TIME_SETUP_FEE": "GST_SG:SR-SG", "SGP:PAYSLIP_MINIMUM_COMMITMENT": "GST_SG:SR-SG", "SGP:PER_PAYSLIP_FEE": "GST_SG:SR-SG", "SGP:TOTAL_PAYMENTS": "GST_SG:SR-SG", "SGP:STAT_FILING": "GST_SG:SR-SG", "SGP:YEAR_END_DOCUMENTATION": "GST_SG:SR-SG", "FREELANCER_PAYOUT_FEE": "GST_SG:OS-SG", "VAS_INCIDENT_LAPTOP_PAYMENT": "GST_SG:ZR-SG", "VAS_INCIDENT_LAPTOP_MANAGEMENT_FEE": "GST_SG:ZR-SG", "VAS_INCIDENT_MONITOR_PAYMENT": "GST_SG:ZR-SG", "VAS_INCIDENT_MONITOR_MANAGEMENT_FEE": "GST_SG:ZR-SG", "VAS_INCIDENT_ACCESSORIES_PAYMENT": "GST_SG:ZR-SG", "VAS_INCIDENT_ACCESSORIES_MANAGEMENT_FEE": "GST_SG:ZR-SG", "VAS_INCIDENT_DISCOUNT": "GST_SG:ZR-SG", "VAS_INCIDENT_PICKUP_DELIVERY_AMOUNT": "GST_SG:ZR-SG", "VAS_INCIDENT_PICKUP_DELIVERY_FEE": "GST_SG:ZR-SG", "VAS_INCIDENT_STORAGE_AMOUNT": "GST_SG:ZR-SG", "VAS_INCIDENT_STORAGE_FEE": "GST_SG:ZR-SG", "VAS_INCIDENT_CONTRACT_CUSTOMISATION_FEE": "GST_SG:ZR-SG", "VAS_INCIDENT_LEGAL_CONSULTATION_FEE": "GST_SG:ZR-SG", "VAS_INCIDENT_OTHERS_SERVICE_AMOUNT": "GST_SG:ZR-SG", "VAS_INCIDENT_OTHERS_SERVICE_FEE": "GST_SG:ZR-SG", "VAS_INCIDENT_OTHERS_VISA_FEE": "GST_SG:ZR-SG", "SEVERANCE_DEPOSIT_EOR_PAYROLL_REFUND": "GST_SG:OS-SG", "SGP:ORDER_FORM_ADVANCE_EOR": "GST_SG:SR-SG", "SGP:ORDER_FORM_ADVANCE_AOR": "GST_SG:SR-SG", "SGP:ORDER_FORM_ADVANCE_GLOBAL_PAYROLL": "GST_SG:SR-SG", "SGP:ORDER_FORM_ADVANCE_ADJUSTMENT_GLOBAL_PAYROLL": "GST_SG:SR-SG", "SGP:ORDER_FORM_ADVANCE_ADJUSTMENT_AOR": "GST_SG:SR-SG", "SGP:STANDARD_BACKGROUND_VERIFICATION_CHECKS_FEE": "GST_SG:SR-SG", "STANDARD_BACKGROUND_VERIFICATION_CHECKS_FEE": "GST_SG:SR-SG", "BANK_FEE": "GST_SG:ZR-SG"}, "defaultValue": "GST_SG:ZR-SG"}, "TAX_CODE_ID_MAP": {"configMap": {"GST_SG:OS-SG": "11", "GST_SG:SR-SG": "6", "GST_SG:ZR-SG": "7", "UNDEF-MY": "25", "UNDEF-AE": "577", "SST_MY": "765", "GST - 5%": "73", "UNDEF-PT": "420", "UNDEF-IE": "299", "UNDEF-DE": "654", "UNDEF-GB": "584"}, "defaultValue": null}, "APPROVAL_STATUS_ID_MAP": {"configMap": {"PENDING": "1", "APPROVED": "2", "REJECTED": "3"}, "defaultValue": null}, "SUBSIDIARY_ID_MAP": {"configMap": {"Multiplier Technologies Pte Ltd": "1", "Multiplier Technologies India Pvt. Ltd.": "6", "Multiplier Technologies FZ-LLC": "67", "Multiplier Technologies US Inc.": "72", "UseMultiplier Technologies Sdn. Bhd.": "5", "Usemultiplier Technologies Portugal, Unipessoal LDA": "53", "Multiplier Technologies Ireland Limited": "37", "Multiplier Technologies Germany MTG GmbH": "73", "Multiplier Technologies UK LTD": "68"}, "defaultValue": "1"}, "CUSTOM_FORM_ID_MAP": {"configMap": {"invoiceForm": "144", "creditMemoForm": "142"}, "defaultValue": null}, "WS_COUNTRY_ISO_MAP": {"configMap": {"AFG": "_afghanistan", "ALA": "_alandIslands", "ALB": "_albania", "DZA": "_algeria", "ASM": "_americanSamoa", "AND": "_andorra", "AGO": "_angola", "AIA": "_anguilla", "ATA": "_antarctica", "ATG": "_antiguaAndBarbuda", "ARG": "_argentina", "ARM": "_armenia", "ABW": "_aruba", "AUS": "_australia", "AUT": "_austria", "AZE": "_azerbaijan", "BHS": "_bahamas", "BHR": "_bahrain", "BGD": "_bangladesh", "BRB": "_barbados", "BLR": "_belarus", "BEL": "_belgium", "BLZ": "_belize", "BEN": "_benin", "BMU": "_bermuda", "BTN": "_bhutan", "BOL": "_bolivia", "BES": "_bonaireSaintEustatiusAndSaba", "BIH": "_bosniaAndHerzegovina", "BWA": "_botswana", "BVT": "_bouvetIsland", "BRA": "_brazil", "IOT": "_britishIndianOceanTerritory", "BRN": "_bruneiDarussalam", "BGR": "_bulgaria", "BFA": "_burkinaFaso", "BDI": "_burundi", "KHM": "_cambodia", "CMR": "_cameroon", "CAN": "_canada", "CPV": "_cape<PERSON><PERSON>e", "CYM": "_caymanIslands", "CAF": "_centralAfricanRepublic", "TCD": "_chad", "CHL": "_chile", "CHN": "_china", "CXR": "_christmasIsland", "CCK": "_cocosKeelingIslands", "COL": "_colombia", "COM": "_comoros", "COG": "_congoDemocraticPeoplesRepublic", "COD": "_congoRepublicOf", "COK": "_cookIslands", "CRI": "_costaRica", "CIV": "_coteDIvoire", "HRV": "_croatiaHrvatska", "CUB": "_cuba", "CUW": "_curacao", "CYP": "_cyprus", "CZE": "_czechRepublic", "DNK": "_denmark", "DJI": "_djibouti", "DMA": "_dominica", "DOM": "_dominicanRepublic", "TLS": "_eastTimor", "ECU": "_ecuador", "EGY": "_egypt", "SLV": "_elSalvador", "GNQ": "_equatorialGuinea", "ERI": "_eritrea", "EST": "_estonia", "ETH": "_ethiopia", "FLK": "_falklandIslands", "FRO": "_faroeIslands", "FJI": "_fiji", "FIN": "_finland", "FRA": "_france", "GUF": "_french<PERSON><PERSON><PERSON>", "PYF": "_frenchPolynesia", "ATF": "_frenchSouthernTerritories", "GAB": "_gabon", "GMB": "_gambia", "GEO": "_georgia", "DEU": "_germany", "GHA": "_ghana", "GIB": "_gibraltar", "GRC": "_greece", "GRL": "_greenland", "GRD": "_grenada", "GLP": "_guadeloupe", "GUM": "_guam", "GTM": "_guatemala", "GGY": "_guernsey", "GIN": "_guinea", "GNB": "_guineaBissau", "GUY": "_guyana", "HTI": "_haiti", "HMD": "_heardAndMcDonaldIslands", "VAT": "_holySeeCityVaticanState", "HND": "_honduras", "HKG": "_hong<PERSON>ong", "HUN": "_hungary", "ISL": "_iceland", "IND": "_india", "IDN": "_indonesia", "IRN": "_iranIslamicRepublicOf", "IRQ": "_iraq", "IRL": "_ireland", "IMN": "_isleOfMan", "ISR": "_israel", "ITA": "_italy", "JAM": "_jamaica", "JPN": "_japan", "JEY": "_jersey", "JOR": "_jordan", "KAZ": "_kazakhstan", "KEN": "_kenya", "KIR": "_kiribati", "PRK": "_koreaDemocraticPeoplesRepublic", "KOR": "_koreaRepublicOf", "XKX": "_kosovo", "KWT": "_kuwait", "KGZ": "_kyrgyzstan", "LAO": "_laoPeoplesDemocraticRepublic", "LVA": "_latvia", "LBN": "_lebanon", "LSO": "_lesotho", "LBR": "_liberia", "LBY": "_libya", "LIE": "_liechtenstein", "LTU": "_lithuania", "LUX": "_luxembourg", "MAC": "_macau", "MKD": "_macedonia", "MDG": "_madagascar", "MWI": "_malawi", "MYS": "_malaysia", "MDV": "_maldives", "MLI": "_mali", "MLT": "_malta", "MHL": "_marshallIslands", "MTQ": "_martinique", "MRT": "_mauritania", "MUS": "_mauritius", "MYT": "_mayotte", "MEX": "_mexico", "FSM": "_micronesiaFederalStateOf", "MDA": "_moldovaRepublicOf", "MCO": "_monaco", "MNG": "_mongolia", "MNE": "_montenegro", "MSR": "_montserrat", "MAR": "_morocco", "MOZ": "_mozambique", "MMR": "_myanmar", "NAM": "_namibia", "NRU": "_nauru", "NPL": "_nepal", "NLD": "_netherlands", "NCL": "_newCaledonia", "NZL": "_newZealand", "NIC": "_nicaragua", "NER": "_niger", "NGA": "_nigeria", "NIU": "_niue", "NFK": "_norfolkIsland", "MNP": "_northernMarianaIslands", "NOR": "_norway", "OMN": "_oman", "PAK": "_pakistan", "PLW": "_palau", "PAN": "_panama", "PNG": "_papuaNewGuinea", "PRY": "_paraguay", "PER": "_peru", "PHL": "_philippines", "PCN": "_pitcairnIsland", "POL": "_poland", "PRT": "_portugal", "PRI": "_puertoRico", "QAT": "_qatar", "REU": "_reunionIsland", "ROU": "_romania", "RUS": "_russianFederation", "RWA": "_rwanda", "BLM": "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SHN": "_<PERSON><PERSON><PERSON><PERSON>", "KNA": "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LCA": "_<PERSON><PERSON><PERSON><PERSON>", "MAF": "_<PERSON><PERSON><PERSON><PERSON>", "VCT": "_saintVincentAndTheGrenadines", "WSM": "_samoa", "SMR": "_sanMarino", "STP": "_saoTomeAndPrincipe", "SAU": "_saudiArabia", "SEN": "_senegal", "SRB": "_serbia", "SYC": "_seychelles", "SLE": "_sierraLeone", "SGP": "_singapore", "SXM": "_sintMaarten", "SVK": "_slovakRepublic", "SVN": "_slovenia", "SLB": "_solomonIslands", "SOM": "_somalia", "ZAF": "_southAfrica", "SGS": "_southGeorgia", "SSD": "_southSudan", "ESP": "_spain", "LKA": "_sriLanka", "PSE": "_stateOfPalestine", "SPM": "_stPierreAndMiquelon", "SDN": "_sudan", "SUR": "_suriname", "SJM": "_svalbardAndJanMayenIslands", "SWZ": "_swaziland", "SWE": "_sweden", "CHE": "_switzerland", "SYR": "_syrianArabRepublic", "TWN": "_taiwan", "TJK": "_tajikistan", "TZA": "_tanzania", "THA": "_thailand", "TGO": "_togo", "TKL": "_tokelau", "TON": "_tonga", "TTO": "_trinidadAndTobago", "TUN": "_tunisia", "TUR": "_turkey", "TKM": "_turkmenistan", "TCA": "_turksAndCaicosIslands", "TUV": "_tuvalu", "UGA": "_uganda", "UKR": "_ukraine", "ARE": "_unitedArabEmirates", "GBR": "_unitedKingdom", "USA": "_unitedStates", "URY": "_uruguay", "UMI": "_uSMinorOutlyingIslands", "UZB": "_uzbekistan", "VUT": "_vanuatu", "VEN": "_venezuela", "VNM": "_vietnam", "VGB": "_virginIslandsBritish", "VIR": "_virginIslandsUSA", "WLF": "_wallisAndFutunaIslands", "ESH": "_westernSahara", "YEM": "_yemen", "ZMB": "_zambia", "ZWE": "_zimbabwe"}, "defaultValue": null}, "CUSTOM_EMAIL_FIELDS": {"configMap": {"0": "custentity_multi_email_1", "1": "custentity_multi_email_2", "2": "custentity_multi_email_3", "3": "custentity_multi_email_4", "4": "custentity_multi_email_5"}, "defaultValue": null}, "CUSTOMER_CATEGORY_ID_MAP": {"configMap": {"EOR": "1"}, "defaultValue": "1"}, "INVOICE_TYPE_MAP": {"configMap": {"GROSS": "1", "SALARY": "2", "GLOBAL_PAYROLL_FUNDING": "3"}, "defaultValue": null}, "ACCOUNT_ID_MAP": {"configMap": {"WAGES_PAYABLE": "2933", "EXPENSE_LIST": "2486"}, "defaultValue": null}, "DEPARTMENT_ID_MAP": {"configMap": {"DEFAULT": "538"}, "defaultValue": "538"}, "VENDOR_ID_MAP": {"configMap": {"INDIA_PAYROLL": "25471", "INDIA_PAYROLL_STATUTORY_TDS": "102403", "INDIA_PAYROLL_STATUTORY_PTAX": "102404", "INDIA_PAYROLL_STATUTORY_ESIC": "102405", "INDIA_PAYROLL_STATUTORY_PF": "102406", "INDIA_PAYROLL_STATUTORY_LWF": "102407", "CANADA_PAYROLL_NET_PAY": "24332", "CANADA_PAYROLL_RETIREMENT_SAVINGS_PLAN": "65640", "CANADA_REVENUE_AGENCY_CRA_STAT_PAY": "65501", "CANADA_REVENU_QUEBEC_STAT_PAY": "76141", "CANADA_WCB_WORKSAFE_BC_STAT_PAY": "73830", "CANADA_WSIB_WORKPLACE_SAFETY_AND_INSURANCE_BOARD_STAT_PAY": "84511", "CANADA_STATUTORY_PAY_EHT_BC": "106242", "CANADA_STATUTORY_PAY_EHT_ONTARIO": "88242", "BOLIVIA_CHRISTMAS_BONUS_ACCRUAL": "184503", "BOLIVIA_PAYROLL": "184500", "BOLIVIA_SEVERANCE_ACCRUAL": "184504", "BOLIVIA_STATUTORY_PAY_SOCIAL_SECURITY": "184502", "COLOMBIA_AFC": "112018", "COSTA_RICA_CHRISTMAS_BONUS_ACCRUAL": "65621", "COSTA_RICA_VACATIONS_ACCRUAL": "65620", "PUERTO_RICO_VACATIONS_ACCRUAL": "81992", "ISLE_OF_MAN_NET_PAY": "192435", "PHILIPPINES_PAYROLL": "25470", "PHILIPPINES_PAYROLL_TAX_PAYMENT": "64366", "PHILIPPINES_SSS_PAYMENT": "64363", "PHILIPPINES_SSS_LOAN_PAYMENT": "64365", "PHILIPPINES_PHIC_PAYMENT": "64364", "PHILIPPINES_HDMF_PAYMENT": "64470", "PHILIPPINES_HDMF_LOAN_PAYMENT": "64471", "UK_P11D_ADJUSTMENT": "181577", "UK_PAYROLL": "25595", "HMRC_UK": "69588", "NESTUK": "87740", "CHILD_MAINTENANCE_SERVICE_DEO_UK": "133119", "UNITED_STATES_PAYROLL": "24321", "UNITED_STATES_PAYROLL_STATUTORY_DEDUCTIONS": "65503", "UNITED_STATES_PAYROLL_401K": "117974", "ACT_REVENUE_OFFICE": "118882", "AUSTRALIA_PAYROLL": "24315", "AUSTRALIA_PAYROLL_PAYG": "63742", "AUSTRALIA_PAYROLL_VIC": "63753", "QUICKSUPER": "63743", "REVENUE_NSW": "63738", "REVENUE_NT": "117873", "REVENUE_QLD": "63739", "REVENUE_SA": "63740", "REVENUE_WA": "63741", "MEXICO_PAYROLL": "24330", "MEXICO_SIPARE_SOCIAL_SECURITY": "65393", "MEXICO_STAT_PAY_INCOME_TAX": "65394", "MEXICO_STAT_PAY_LOCAL_PAYROLL_TAX": "65392", "PFRON_STATE_FUND_FOR_THE_REHABILITATION_OF_DISABLED_PERSONS_POLAND": "139464", "POLAND_PAYROLL": "24450", "POLAND_STATUTORY_PAYPERSONAL_INCOME_TAX": "65285", "POLAND_STATUTORY_PAY_MONTHLY_ZUS_PAYMENT": "65283", "POLAND_STATUTORY_PAY_PPK_CONTRIBUTION": "65284", "SINGAPORE_PAYROLL_MT_SG": "24318", "SINGAPORE_PAYROLL_STATUTORY": "63748"}, "defaultValue": null}, "LOCATION_ID_MAP": {"configMap": {"IND": "1"}, "defaultValue": null}}