server:
  port: 8080
  max-http-request-header-size: 10MB

# Contract Severance History Scheduler Configuration
contract-severance-history:
  scheduler:
    # Cron expression for the scheduler (default: "0 0 3,15 1,6,11,16,20-31 * ?" - 3 AM and 3 PM UTC: every 5 days in first 20 days, daily from 20th onwards)
    cron-expression: ${CONTRACT_SEVERANCE_HISTORY_SCHEDULER_CRON:0 0 3,15 1,6,11,16,20-31 * ?}
    # Time zone for the scheduler (default: "UTC")
    time-zone: ${CONTRACT_SEVERANCE_HISTORY_SCHEDULER_TIMEZONE:UTC}
    # Batch size for processing records (default: 10)
    batch-size: ${CONTRACT_SEVERANCE_HISTORY_SCHEDULER_BATCH_SIZE:10}
    # Whether the scheduler is enabled (default: true)
    enabled: ${CONTRACT_SEVERANCE_HISTORY_SCHEDULER_ENABLED:true}

spring:
  application:
    name: payable-service
  main:
    banner-mode: "off"
  liquibase:
    changeLog: classpath:liquibase/master.xml
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    hikari:
      poolName: Hikari
      auto-commit: false
      max-lifetime: 1800000
      idle-timeout: 300000
      connection-timeout: 30000
      minimum-idle: 1
      maximum-pool-size: 5
      keepalive-time: 60000
  jpa:
    open-in-view: false
    properties:
      org.hibernate.envers.store_data_at_delete: true
      hibernate:
        id:
          db_structure_naming_strategy: legacy
        format_sql: true
        jdbc.time_zone: UTC
        id.new_generator_mappings: true
        connection.provider_disables_autocommit: true
        cache.use_second_level_cache: false
        cache.use_query_cache: false
        generate_statistics: false
        jdbc.batch_size: 25
        order_inserts: true
        order_updates: true
        query.fail_on_pagination_over_collection_fetch: true
        query.in_clause_parameter_padding: true
    hibernate:
      dialect: org.hibernate.dialect.PostgreSQLDialect
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 50MB
  security:
    oauth2:
      client:
        registration:
          netsuite:
            provider: "netsuite"
            client-id: ${SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_NETSUITE_CLIENTID}
            client-secret: ${SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_NETSUITE_CLIENTSECRET}
            authorization-grant-type: client_credentials
            scope:
              - rest_webservices
              - restlets
        provider:
          netsuite:
            token-uri: ${SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_NETSUITE_TOKENURI}
  profiles:
    active: local

  scheduler:
    contactUpdate:
      cronTime: "0 5/30 * * * ?"
      enabled: false # for CompanyUpdateScheduler
    customer-aging-report:
      cronTime: "0 0 18 * * ?"
    company-bank-fee-balance:
      cronTime: "0 0 * * * ?"  # Every hour at the top of the hour
      enabled: true

platform:
  frontend:
    baseurl: ${PLATFORM_FRONTEND_BASEURL}
  invoice-engine:
    kafka:
      topic: topic.internal.v1.invoice.engine.command
      producer:
        bootstrap-servers: ${PLATFORM_INVOICEENGINE_KAFKA_BOOTSTRAPSERVERS}
        key-serializer: org.apache.kafka.common.serialization.StringSerializer
        value-serializer: org.apache.kafka.common.serialization.StringSerializer
        max-block-ms: 3200
        retry-count: 3
        retry-backoff-ms: 1000
      consumer:
        bootstrap-servers: ${PLATFORM_INVOICEENGINE_KAFKA_BOOTSTRAPSERVERS}
        group-id: invoice-engine
        key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        max-poll-records: 5
        concurrency: 3
        auto-start: false
  dispute-service:
    system.notification.email: ${PLATFORM_DISPUTESERVICE_SYSTEM_NOTIFICATION_EMAIL}
    system.notification.billing.email: ${PLATFORM_DISPUTESERVICE_SYSTEM_NOTIFICATION_BILLING_EMAIL}
    kafka:
      bootstrap-servers: ${PLATFORM_DISPUTESERVICE_KAFKA_BOOTSTRAPSERVERS}
      group-id: dispute-service
      topic: topic.internal.v1.dispute.status.update
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: com.github.daniel.shuy.kafka.protobuf.serde.KafkaProtobufSerializer
      max-block-ms: 3200
      retry-count: 3
      retry-backoff-ms: 1000
  payable-service:
    kafka:
      bootstrap-servers: ${PLATFORM_PAYABLESERVICE_KAFKA_BOOTSTRAPSERVERS}
      group-id: payable-service
      max-block-ms: 3200
      retry-count: 3
      retry-backoff-ms: 1000
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: com.github.daniel.shuy.kafka.protobuf.serde.KafkaProtobufSerializer
      payable-update-topic: topic.internal.v1.payable
  payroll-service:
    kafka:
      bootstrap-servers: ${PLATFORM_PAYROLLSERVICE_KAFKA_BOOTSTRAPSERVERS}
      group-id: payable-payroll-service
      payroll-cycle-status-change:
        topic: topic.internal.v1.payrollcycle
  userservice:
    system.notification.support.email: <EMAIL>
    system.notification.email: <EMAIL>
    system.notification.hello.email: <EMAIL>
    system.notification.billing.email: <EMAIL>
    baseurl: ${PLATFORM_USERSERVICE_BASEURL}
    system.user.username: ${PLATFORM_USERSERVICE_SYSTEM_USER_USERNAME}
    system.user.password: ${PLATFORM_USERSERVICE_SYSTEM_USER_PASSWORD}
  docgen:
    baseurl: ${PLATFORM_DOCGEN_BASEURL}
    public-baseurl: ${PLATFORM_DOCGEN_PUBLICBASEURL}
  notification:
    slack:
      channel:
        deposit-paid: <EMAIL>
  company-service:
    kafka:
      bootstrap-servers: ${PLATFORM_COMPANYSERVICE_KAFKA_BOOTSTRAPSERVERS}
      group-id: company-service-payable
      topic: topic.internal.v1.company.contact
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: com.github.daniel.shuy.kafka.protobuf.serde.KafkaProtobufSerializer
      max-block-ms: 3200
      retry-count: 3
      retry-backoff-ms: 1000
      consumer-auto-startup: true
  legal-entity:
    kafka:
      auto-startup: true
      bootstrap-servers: ${PLATFORM_LEGALENTITY_KAFKA_BOOTSTRAPSERVERS}
      group-id: payable-service
      topic: topic.internal.v1.company.legal-entity
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: com.github.daniel.shuy.kafka.protobuf.serde.KafkaProtobufSerializer
      max-block-ms: 3200
      retry-count: 3
      retry-backoff-ms: 1000
  netsuite-transaction:
    kafka:
      topic:
        name: topic.internal.v1.netsuite-transaction
      producer:
        bootstrap-servers: ${PLATFORM_NETSUITETRANSACTION_KAFKA_BOOTSTRAPSERVERS}
        max-block-ms: 3200
        retry-count: 3
        retry-backoff-ms: 1000
      consumer:
        bootstrap-servers: ${PLATFORM_NETSUITETRANSACTION_KAFKA_BOOTSTRAPSERVERS}
        group-id: netsuite-transaction
        max-poll-records: 1
        concurrency: 1
        auto-startup: true
        max-poll-interval-ms: 900000
  billing-service:
    kafka:
      topic:
        name: topic.internal.v1.billing-registered
      consumer:
        bootstrap-servers: ${PLATFORM_BILLINGSERVICE_KAFKA_CONSUMER_BOOTSTRAPSERVERS}
        group-id: payable-billing
        max-poll-records: 1
        concurrency: 1
        auto-startup: true
        max-poll-interval-ms: 900000
  advance-collection-balance:
    kafka:
      topic:
        name: topic.internal.v1.netsuite-transaction
      consumer:
        bootstrap-servers: ${PLATFORM_NETSUITETRANSACTION_KAFKA_BOOTSTRAPSERVERS}
        group-id: advance-collection-balance
        max-poll-records: 5
        concurrency: 1
        auto-startup: true
        max-poll-interval-ms: 900000
grpc:
  client:
    core-service:
      address: ${GRPC_CLIENT_CORESERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    member-service:
      address: ${GRPC_CLIENT_MEMBERSERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    payroll-service:
      address: ${GRPC_CLIENT_PAYROLLSERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    country-service:
      address: ${GRPC_CLIENT_COUNTRYSERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    contract-service:
      address: ${GRPC_CLIENT_CONTRACTSERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
    company-service:
      address: ${GRPC_CLIENT_COMPANYSERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
    pigeon-service:
      address: ${GRPC_CLIENT_PIGEONSERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    pricing-service:
      address: ${GRPC_CLIENT_PRICINGSERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
    org-management-service:
      address: ${GRPC_CLIENT_ORGMANAGEMENTSERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
    deposit-service:
      address: ${GRPC_CLIENT_DEPOSITSERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
    authority-service:
      address: ${GRPC_CLIENT_AUTHORITY_SERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    billing-service:
      address: ${GRPC_CLIENT_BILLINGSERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
    severance-service:
      address: ${GRPC_CLIENT_SEVERANCE_SERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
    compensation-service:
      address: ${GRPC_CLIENT_COMPENSATION_SERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
    vas-incidental-service:
      address: ${GRPC_CLIENT_VAS_INCIDENTAL_SERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
    product-catalogue-service:
      address: ${GRPC_CLIENT_PRODUCT_CATALOGUE_SERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true

  server:
    max-inbound-metadata-size: 2097152
    port: 9091
    security:
      enabled: true
      certificate-chain: classpath:certificates/server.local.crt
      private-key: classpath:certificates/server.local.key

ops-platform:
  frontend:
    user-baseurl: ${OPSPLATFORM_FRONTEND_USERBASEURL}

pigeon:
  client:
    kafka:
      bootstrap-servers: ${PIGEON_CLIENT_KAFKA_BOOTSTRAPSERVERS}

feign:
  hystrix:
    enabled: false
  client:
    config:
      docgen-service:
        url: ${FEIGN_CLIENT_CONFIG_DOCGEN_SERVICE_URL}
        connectTimeout: 120000
        readTimeout: 120000
        loggerLevel: BASIC
        decode404: false
      netsuite-rest-api:
        url: ${FEIGN_CLIENT_CONFIG_NETSUITERESTAPI_URL}
        connectTimeout: 120000
        readTimeout: 120000
        loggerLevel: FULL
        decode404: false
      netsuite-restlet-api:
        url: ${FEIGN_CLIENT_CONFIG_NETSUITERESTLETAPI_URL}
        connectTimeout: 120000
        readTimeout: 120000
        loggerLevel: FULL
        decode404: false

reactive:
  feign:
    logger:
      enabled: true
    client:
      config:
        netsuite-restlet-async-api:
          url: ${FEIGN_CLIENT_CONFIG_NETSUITERESTLETAPI_URL}
          loggerLevel: BASIC
          options:
            readTimeoutMillis: 3600000

jwt:
  publicKey: ${JWT_PUBLIC_KEY}

  # Token is valid 24 hours
  token-validity-in-seconds: 86400
  token-validity-in-seconds-for-remember-me: 2592000

xero:
  client-id: ${XERO_CLIENTID}
  client-secret: ${XERO_CLIENTSECRET}
  token-server-url: ${XERO_TOKENSERVERURL}
  secret: ${XERO_SECRET}
  # if true: create the draft invoice in Xero otherwise: just save the invoice in the system database but not in Xero
  create-draft-invoices: true
  scheduler:
    enabled: false

netsuite:
  oauth:
    private-key: ${NETSUITE_OAUTH_PRIVATEKEY}
    realm-id: ${NETSUITE_OAUTH_REALMID}
    certificate-id: ${NETSUITE_OAUTH_CERTIFICATEID}
  webservice:
    wsUrl: ${NETSUITE_WEBSERVICE_WSURL}
    account: ${NETSUITE_WEBSERVICE_ACCOUNT}
    tbaConsumerKey: ${NETSUITE_WEBSERVICE_TBACONSUMERKEY}
    tbaConsumerSecret: ${NETSUITE_WEBSERVICE_TBACONSUMERSECRET}
    tbaTokenId: ${NETSUITE_WEBSERVICE_TBATOKENID}
    tbaTokenSecret: ${NETSUITE_WEBSERVICE_TBATOKENSECRET}
  webhook:
    preSharedKey: ${NETSUITE_WEBHOOK_PRESHAREDKEY}
  pdf:
    restlet:
      script-id: 773
  inv-line-delete:
    restlet:
      script-id: 860
  forward-sync:
    restlet:
      script-id: 881
  resync:
    restlet:
      script-id: ${NETSUITE_RESTLET_RESYNC_SCRIPT_ID}
  download-saved-search:
    restlet:
      script-id: ${NETSUITE_RESTLET_DOWNLOAD_SAVED_SEARCH_SCRIPT_ID}
  feign:
    retry:
      startingIntervalInMilliseconds: 100
      maximumIntervalInMilliseconds: 7000
      maxAttempts: 10

growthbook:
  base-url: ${GROWTHBOOK_BASEURL}
  env-key: ${GROWTHBOOK_ENVKEY}
  refresh-frequency-ms: 15000

config:
  report:
    generator-config:
      jsonfile: config/report/report-generator-config.json
    invoice-source-generator-config:
      jsonfile: config/invoiceSource/report/generator-config.json
    invoice-source-download-config:
      duration: P30D
      max-usage: 1000
      resource-group: INVOICE_SOURCE_REPORT_VIEW

logging:
  level:
    com.multiplier.core.payable.adapters.netsuite: DEBUG
    net.javacrumbs.shedlock: DEBUG

cloud:
  aws:
    s3:
      enabled: true
      bucket: multiplier-staging
    region:
      static: ap-southeast-1
    credentials:
      accessKey: CLOUD_AWS_CREDENTIALS_ACCESS_KEY
      secretKey: CLOUD_AWS_CREDENTIALS_SECRET

back-sync:
  scheduler-lock:
    name: payableBackSync
    lockAtMostFor: PT40M
    lockAtLeastFor: PT0S

token-refresher:
  scheduler-lock:
    name: payableTokenRefresher
    lockAtMostFor: PT2M
    lockAtLeastFor: PT1M

data-collector:
  scheduler-lock:
    name: dataCollector
    lockAtMostFor: PT2M
    lockAtLeastFor: PT1M

orchestrator:
  invoice:
    aor-annual-plan-invoice:
      lease-time: PT5M
      scheduler:
        cron-time: 0 0/15 * * * ?
mpl:
  redis:
    enabled: true
    connection:
      single:
        address: null # Must be null to use host/port configuration
        host: ${REDIS_HOST}
        port: 6379
      password: ${REDIS_PASSWORD}
      protocol: redis # Use 'rediss' for SSL connections
      database: 0 # Redis database index
      # Connection pool settings
      poolSize: 20 # Connection pool size (default: 20)
      connectionTimeout: 10000 # Connection timeout in ms (default: 10000)
      # Cache settings
      defaultTtl: 0 # Default TTL in seconds, 0 means no expiration (default: 0)