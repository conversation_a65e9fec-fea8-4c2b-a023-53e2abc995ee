spring:
  datasource:
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}

platform:
  frontend:
    baseurl: ${PLATFORM_FRONTEND_BASEURL}
  docgen:
    baseurl: ${PLATFORM_DOCGEN_BASEURL}
    public-baseurl: ${PLATFORM_DOCGEN_PUBLICBASEURL}
  notification:
    slack:
      channel:
        deposit-paid: <EMAIL>

grpc:
  client:
    core-service:
      address: ${GRPC_CLIENT_CORESERVICE_ADDRESS}
    member-service:
      address: ${GRPC_CLIENT_MEMBERSERVICE_ADDRESS}
    payroll-service:
      address: ${GRPC_CLIENT_PAYROLLSERVICE_ADDRESS}
    country-service:
      address: ${GRPC_CLIENT_COUNTRYSERVICE_ADDRESS}
    contract-service:
      address: ${GRPC_CLIENT_CONTRACTSERVICE_ADDRESS}
    company-service:
      address: ${GRPC_CLIENT_COMPANYSERVICE_ADDRESS}
    pigeon-service:
      address: ${GRPC_CLIENT_PIGEONSERVICE_ADDRESS}
    deposit-service:
      address: ${GRPC_CLIENT_DEPOSITSERVICE_ADDRESS}
    billing-service:
      address: ${GRPC_CLIENT_BILLINGSERVICE_ADDRESS}
  server:
    port: 9090

pigeon:
  client:
    kafka:
      bootstrap-servers: ${PIGEON_CLIENT_KAFKA_BOOTSTRAPSERVERS}

ops-platform:
  frontend:
    user-baseurl: ${OPSPLATFORM_FRONTEND_USERBASEURL}

feign:
  client:
    config:
      docgen-service:
        url: ${FEIGN_CLIENT_CONFIG_DOCGEN_SERVICE_URL}
