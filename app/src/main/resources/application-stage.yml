spring:
  datasource:
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
  scheduler:
    customer-aging-report:
      cronTime: "0 0 10 * * ?"

# Stage environment specific configuration for Contract Severance History Scheduler
contract-severance-history:
  scheduler:
    # Run every day at 2 AM UTC in staging for easier testing
    cron-expression: "0 0 2 * * ?"
    # Use a smaller batch size in staging
    batch-size: 5

platform:
  frontend:
    baseurl: ${PLATFORM_FRONTEND_BASEURL}
  docgen:
    baseurl: ${PLATFORM_DOCGEN_BASEURL}
    public-baseurl: ${PLATFORM_DOCGEN_PUBLICBASEURL}
  notification:
    slack:
      channel:
        deposit-paid: <EMAIL>

grpc:
  client:
    core-service:
      address: ${GRPC_CLIENT_CORESERVICE_ADDRESS}
    member-service:
      address: ${GRPC_CLIENT_MEMBERSERVICE_ADDRESS}
    payroll-service:
      address: ${GRPC_CLIENT_PAYROLLSERVICE_ADDRESS}
    country-service:
      address: ${GRPC_CLIENT_COUNTRYSERVICE_ADDRESS}
    contract-service:
      address: ${GRPC_CLIENT_CONTRACTSERVICE_ADDRESS}
    company-service:
      address: ${GRPC_CLIENT_COMPANYSERVICE_ADDRESS}
    pigeon-service:
      address: ${GRPC_CLIENT_PIGEONSERVICE_ADDRESS}
    org-management-service:
      address: ${GRPC_CLIENT_ORGMANAGEMENTSERVICE_ADDRESS}
    deposit-service:
      address: ${GRPC_CLIENT_DEPOSITSERVICE_ADDRESS}
    billing-service:
      address: ${GRPC_CLIENT_BILLINGSERVICE_ADDRESS}
  server:
    port: 9090

pigeon:
  client:
    kafka:
      bootstrap-servers: ${PIGEON_CLIENT_KAFKA_BOOTSTRAPSERVERS}

ops-platform:
  frontend:
    user-baseurl: ${OPSPLATFORM_FRONTEND_USERBASEURL}

feign:
  client:
    config:
      docgen-service:
        url: ${FEIGN_CLIENT_CONFIG_DOCGEN_SERVICE_URL}