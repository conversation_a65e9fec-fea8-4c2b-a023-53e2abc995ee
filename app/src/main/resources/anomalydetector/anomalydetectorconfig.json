{"REFERENCE_TEXT": {"config": {"first_invoice_text": "%s Gross Salary - EOR %s", "second_invoice_text": "%s Salary - EOR", "invoice_text_date_format": "M<PERSON>''yy"}, "result": {"type": "ERROR", "message": "Invoice Text is expected to be %s, but is %s"}, "condition": {"active": true, "supported_invoice_types": ["FIRST_INVOICE", "SECOND_INVOICE"]}}, "BILLING_CURRENCY": {"config": {}, "result": {"type": "ERROR", "message": "Invoice Billing currency is expected to be %s but is %s"}, "condition": {"active": true, "supported_invoice_types": ["FIRST_INVOICE", "SECOND_INVOICE"]}}, "FX_RATE": {"config": {"variance": 0.05}, "fxResult": {"type": "ERROR", "fxMessage": {"different_currency": "Variance between fx rate used is expected to be %s, but is %s", "same_currency": "Fx rate used is not 1 for %s to %s"}}, "condition": {"active": false, "supported_invoice_types": ["FIRST_INVOICE", "SECOND_INVOICE"]}}, "CREDIT_NOTE": {"config": {}, "result": {"type": "ERROR", "message": "Invoice amount is negative. Credit notes need manual intervention"}, "condition": {"active": false, "supported_invoice_types": ["FIRST_INVOICE", "SECOND_INVOICE"]}}, "HIGH_VALUE_INVOICE": {"config": {"default_max_net_amount": 100000, "currency_based_max_net_amounts": {}}, "result": {"type": "ERROR", "message": "Invoice amount is higher than 100000. Invoice need manual intervention"}, "condition": {"active": true, "supported_invoice_types": ["FIRST_INVOICE", "SECOND_INVOICE"]}}, "INV_HISTORY_CONTRACT_COUNT": {"config": {}, "result": {"type": "ERROR", "message": "contracts=%s are different amongst current invoice(s) and previous invoice(s)"}, "condition": {"active": true, "supported_invoice_types": ["FIRST_INVOICE", "SECOND_INVOICE"]}}, "INV_HISTORY_MANAGEMENT_FEE": {"config": {}, "result": {"type": "ERROR", "message": "Management fee for contract=%s is different. currentInvoice=%s, previousInvoice=%s"}, "condition": {"active": true, "supported_invoice_types": ["FIRST_INVOICE", "SECOND_INVOICE"]}}, "INV_HISTORY_GROSS_SALARY": {"config": {"is_base": true}, "result": {"type": "ERROR", "message": "Gross Salary for contract=%s is different. currentInvoice=%s, previousInvoice=%s"}, "condition": {"active": true, "supported_invoice_types": ["FIRST_INVOICE", "SECOND_INVOICE"]}}}