spring:
  datasource:
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}

  liquibase:
    # Disable by default to avoid accidentally running in-progress changelogs on RELEASE
    enabled: false

  scheduler:
    company-bank-fee-balance:
      enabled: false

platform:
  frontend:
    baseurl: ${PLATFORM_FRONTEND_BASEURL}
  docgen:
    baseurl: ${PLATFORM_DOCGEN_BASEURL}
    public-baseurl: ${PLATFORM_DOCGEN_PUBLICBASEURL}
  company-service:
    kafka:
      bootstrap-servers: localhost:9092
  netsuite-transaction:
    kafka:
      consumer:
        auto-startup: false

grpc:
  client:
    core-service:
      address: ${GRPC_CLIENT_CORESERVICE_ADDRESS}
    member-service:
      address: ${GRPC_CLIENT_MEMBERSERVICE_ADDRESS}
    payroll-service:
      address: ${GRPC_CLIENT_PAYROLLSERVICE_ADDRESS}
    country-service:
      address: ${GRPC_CLIENT_COUNTRYSERVICE_ADDRESS}
    contract-service:
      address: ${GRPC_CLIENT_CONTRACTSERVICE_ADDRESS}
    company-service:
      address: ${GRPC_CLIENT_COMPANYSERVICE_ADDRESS}
    pigeon-service:
      address: ${GRPC_CLIENT_PIGEONSERVICE_ADDRESS}
    pricing-service:
      address: ${GRPC_CLIENT_PRICINGSERVICE_ADDRESS}
    org-management-service:
      address: ${GRPC_CLIENT_ORGMANAGEMENTSERVICE_ADDRESS}
    deposit-service:
      address: ${GRPC_CLIENT_DEPOSITSERVICE_ADDRESS}
    billing-service:
      address: ${GRPC_CLIENT_BILLINGSERVICE_ADDRESS}
    authority-service:
      address: ${GRPC_CLIENT_AUTHORITY_SERVICE_ADDRESS}
    severance-service:
      address: ${GRPC_CLIENT_SEVERANCE_SERVICE_ADDRESS}
    compensation-service:
      address: ${GRPC_CLIENT_COMPENSATION_SERVICE_ADDRESS}

  server:
    port: 9090
    security:
      enabled: false
      certificate-chain: classpath:certificates/server.local.crt
      private-key: classpath:certificates/server.local.key

pigeon:
  client:
    kafka:
      bootstrap-servers: ${PIGEON_CLIENT_KAFKA_BOOTSTRAPSERVERS}

growthbook:
  env-key: ${GROWTHBOOK_ENVKEY}

cloud:
  aws:
    s3:
      bucket: multiplier-release

ops-platform:
  frontend:
    user-baseurl: ${OPSPLATFORM_FRONTEND_USERBASEURL}

netsuite:
  resync:
    restlet:
      script-id: 956

feign:
  hystrix:
    enabled: false
  client:
    config:
      docgen-service:
        url: ${FEIGN_CLIENT_CONFIG_DOCGEN_SERVICE_URL}
        connectTimeout: 120000
        readTimeout: 120000
        loggerLevel: BASIC
        decode404: false

reactive:
  feign:
    client:
      config:
        netsuite-restlet-async-api:
          loggerLevel: FULL

back-sync:
  scheduler-lock:
    name: payableBackSyncDebug
    lockAtMostFor: PT40M
    lockAtLeastFor: PT0S

token-refresher:
  scheduler-lock:
    name: payableTokenRefresherDebug
    lockAtMostFor: PT2M
    lockAtLeastFor: PT1M

data-collector:
  scheduler-lock:
    name: dataCollectorDebug
    lockAtMostFor: PT2M
    lockAtLeastFor: PT1M

# Contract Severance History Scheduler Configuration for Debug environment
contract-severance-history:
  scheduler:
    # Run every 30 minutes for easier debugging if needed
    cron-expression: "0 0/30 * * * ?"
    time-zone: "UTC"
    batch-size: 3
    # Disabled in debug environment
    enabled: false