<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="max" id="20231102233000-1">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already ran">
            <not>
                <columnExists schemaName="company" tableName="legal_entity_aud" columnName="capabilities"/>
            </not>
        </preConditions>
        <addColumn schemaName="company" tableName="legal_entity_aud">
            <column name="capabilities" type="text[]"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>