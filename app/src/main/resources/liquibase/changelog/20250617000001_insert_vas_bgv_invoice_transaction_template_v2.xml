<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="20250617000001-1" author="augment-agent">
        <preConditions onFail="MARK_RAN" onFailMessage="VAS_BACKGROUND_VERIFICATION_INVOICE template already exists.">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM payable.transaction_template_v2
                WHERE transaction_type = 'VAS_BACKGROUND_VERIFICATION_INVOICE'
                AND is_default = true;
            </sqlCheck>
        </preConditions>
        <sql>
            INSERT INTO payable.transaction_template_v2 (
                id, transaction_type, is_default, description, json, created_by, created_on, updated_by, updated_on
            ) VALUES (
                         gen_random_uuid(),
                         'VAS_BACKGROUND_VERIFICATION_INVOICE',
                         true,
                         'Default Template for VAS Background Verification Invoice',
                         '{"identifier":"vas-bgv-invoice-default","invoiceType":"VAS_BACKGROUND_VERIFICATION_INVOICE","lineItemTypes":["STANDARD_BACKGROUND_VERIFICATION_CHECKS_FEE"],"dataFormatters":[{"name":"com.multiplier.payable.engine.formatter.TextDataFormatter","field":"description","order":0,"filter":"lineItemType == ''STANDARD_BACKGROUND_VERIFICATION_CHECKS_FEE''","additionalData":{"text":"''Resource Background Verification Fee for '' + contractId + '': '' + baseCurrency + '' '' + getAmountRoundedUp2Decimals()"}},{"name":"com.multiplier.payable.engine.formatter.OrderByFieldsFormatter","field":"contractId,lineItemType","order":1}],"netSuiteTemplate":"345","invoiceEntityMode":"LOCAL","referenceTemplate":"getStartMonth() + ''Value Added Services Fee'' + getTransactionCount()","invoiceEntitySender":"MT SGP"}',
                         -1,
                         now(),
                         -1,
                         now()
                     );
        </sql>
    </changeSet>

</databaseChangeLog>
