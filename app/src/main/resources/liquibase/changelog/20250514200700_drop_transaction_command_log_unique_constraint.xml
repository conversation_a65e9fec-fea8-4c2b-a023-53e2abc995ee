<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="20250514200700-1" author="crobles-multiplier">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as constraint already dropped">
            <sqlCheck expectedResult="1">SELECT COUNT(*)
                                         FROM information_schema.table_constraints
                                         WHERE table_name = 'transaction_command_log'
                                           AND constraint_name = 'uniq_tid_type_status'
                                           AND constraint_type = 'UNIQUE';</sqlCheck>
        </preConditions>
        <comment>Drop unique constraint on transaction_command_log to allow duplicate transaction records</comment>
        <dropUniqueConstraint
                tableName="transaction_command_log"
                schemaName="payable"
                constraintName="uniq_tid_type_status"/>
    </changeSet>

    <changeSet id="20250514200700-2" author="crobles-multiplier">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as index already exists">
            <not>
                <indexExists schemaName="payable" tableName="transaction_command_log" indexName="idx_transaction_command_log_tid_type_status"/>
            </not>
        </preConditions>
        <comment>Recreate non-unique index to maintain query performance after constraint removal</comment>
        <createIndex indexName="idx_transaction_command_log_tid_type_status"
                     tableName="transaction_command_log"
                     schemaName="payable">
            <column name="transaction_id"/>
            <column name="transaction_type"/>
            <column name="transaction_status"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
