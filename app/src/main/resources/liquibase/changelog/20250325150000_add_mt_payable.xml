<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <!-- ChangeSet 1: Create multiplier_payable table -->
    <changeSet id="20250325150000-1" author="shubhamjainm">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="payable" tableName="multiplier_payable"/>
            </not>
        </preConditions>
        <sql>
            CREATE TABLE payable.multiplier_payable (
                                                        id               BIGSERIAL PRIMARY KEY,
                                                        status           VARCHAR(255) NOT NULL,
                                                        line_item_type   VARCHAR(255) NOT NULL,
                                                        items            JSONB,
                                                        transaction_id   VARCHAR(255) NOT NULL,
                                                        payroll_cycle_id BIGINT,
                                                        created_on       TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                        updated_on       TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                                                        created_by       BIGINT NOT NULL,
                                                        updated_by       BIGINT
            );
        </sql>
    </changeSet>

    <!-- ChangeSet 2: Create index on transaction_id -->
    <changeSet id="20250325150000-2" author="shubhamjainm">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists schemaName="payable" indexName="idx_multiplier_payable_transaction_id"/>
            </not>
        </preConditions>
        <sql>
            CREATE INDEX idx_multiplier_payable_transaction_id
                ON payable.multiplier_payable (transaction_id);
        </sql>
    </changeSet>

    <!-- ChangeSet 3: Create multiplier_payable_aud table -->
    <changeSet id="20250325150000-3" author="shubhamjainm">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="payable" tableName="multiplier_payable_aud"/>
            </not>
        </preConditions>
        <sql>
            CREATE TABLE payable.multiplier_payable_aud (
                                                            id               BIGINT NOT NULL,
                                                            rev              INTEGER NOT NULL,
                                                            revtype          SMALLINT,
                                                            status           VARCHAR(255) NOT NULL,
                                                            line_item_type   VARCHAR(255) NOT NULL,
                                                            items            JSONB,
                                                            transaction_id   VARCHAR(255) NOT NULL,
                                                            payroll_cycle_id BIGINT,
                                                            created_on       TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                            updated_on       TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                                                            created_by       BIGINT NOT NULL,
                                                            updated_by       BIGINT,
                                                            PRIMARY KEY (id, rev)
            );
        </sql>
    </changeSet>

    <!-- ChangeSet 4: Create index on transaction_id in audit table -->
    <changeSet id="20250325150000-4" author="shubhamjainm">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists schemaName="payable" indexName="idx_multiplier_payable_aud_transaction_id"/>
            </not>
        </preConditions>
        <sql>
            CREATE INDEX idx_multiplier_payable_aud_transaction_id
                ON payable.multiplier_payable_aud (transaction_id);
        </sql>
    </changeSet>

</databaseChangeLog>
