<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20241215230000-1" author="prakharAtMultiplier" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <comment>For any mapping exists for binding table where entity and company exists, we are making it primary</comment>
        <sql>
            UPDATE payable.company_binding target_cb
            SET
                is_primary = CASE
                                 WHEN c.primary_legal_entity_id = target_cb.entity_id THEN true
                                 ELSE false
                    END,
                updated_by = -1,
                updated_on = now()
            FROM company.company c
            WHERE target_cb.company_id = c.id
              AND target_cb.external_system = 'NETSUITE'
              AND c.primary_legal_entity_id IS NOT NULL
              AND EXISTS (
                SELECT 1 FROM payable.company_binding cb2
                WHERE cb2.company_id = c.id
                  AND cb2.entity_id = c.primary_legal_entity_id
                  AND cb2.external_system = 'NETSUITE'
            );
        </sql>
    </changeSet>

    <changeSet id="20241215230000-2" author="prakharAtMultiplier" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <comment>Updating entityId for those entries where entity could be null or not same as company primary entity</comment>
        <sql>
            UPDATE payable.company_binding target_cb
            SET
                entity_id = c.primary_legal_entity_id,
                updated_by = -1,
                updated_on = now()
            FROM company.company c
            WHERE target_cb.company_id = c.id
              AND target_cb.external_system = 'NETSUITE'
              AND target_cb.is_primary = true
              AND c.primary_legal_entity_id IS NOT NULL
              AND NOT EXISTS (
                SELECT 1 FROM payable.company_binding cb2
                WHERE cb2.company_id = c.id
                  AND cb2.entity_id = c.primary_legal_entity_id
                  AND cb2.external_system = 'NETSUITE'
            );
        </sql>
    </changeSet>

</databaseChangeLog>
