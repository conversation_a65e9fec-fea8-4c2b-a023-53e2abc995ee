<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="edward" id="20240222151300-1">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already dropped">
            <columnExists schemaName="payable" tableName="company_payable" columnName="version_id"/>
        </preConditions>
        <dropColumn schemaName="payable" tableName="company_payable" columnName="version_id">
        </dropColumn>
    </changeSet>

    <changeSet author="edward" id="20240222151300-2">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already dropped">
            <columnExists schemaName="payable" tableName="company_payable_aud" columnName="version_id"/>
        </preConditions>
        <dropColumn schemaName="payable" tableName="company_payable_aud" columnName="version_id">
        </dropColumn>
    </changeSet>
</databaseChangeLog>