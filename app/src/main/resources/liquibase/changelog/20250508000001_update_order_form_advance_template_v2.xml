<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.9.xsd">

    <changeSet id="20250508000001-1" author="chriscao">
        <validCheckSum>ANY</validCheckSum>
        <sql>
            UPDATE payable.transaction_template_v2
            SET json = '{
              "identifier": "default-order-form-advance-template",
              "invoiceType": "ORDER_FORM_ADVANCE",
              "lineItemTypes": [
                "ORDER_FORM_ADVANCE_EOR",
                "ORDER_FORM_ADVANCE_GLOBAL_PAYROLL",
                "ORDER_FORM_ADVANCE_AOR"
              ],
              "dataFormatters": [],
              "netSuiteTemplate": "345",
              "invoiceEntityMode": "LOCAL",
              "referenceTemplate": "''Order Form Advance''",
              "invoiceEntitySender": "MT SGP"
            }'
            WHERE transaction_type = 'ORDER_FORM_ADVANCE'
            AND is_default = true;
        </sql>
    </changeSet>

</databaseChangeLog>
