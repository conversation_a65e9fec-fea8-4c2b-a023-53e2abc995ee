<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="20250618080500-1" author="caokhanhmultiplier" dbms="postgresql">
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as invoiceLineReference column does not exist or invoice_line_reference already exists">
            <and>
                <tableExists tableName="advance_collection_balance" schemaName="payable"/>
                <columnExists tableName="advance_collection_balance" schemaName="payable" columnName="invoiceLineReference"/>
                <not>
                    <columnExists tableName="advance_collection_balance" schemaName="payable" columnName="invoice_line_reference"/>
                </not>
            </and>
        </preConditions>
        <comment>Rename invoiceLineReference column to invoice_line_reference for consistent snake_case naming in advance_collection_balance</comment>
        <renameColumn tableName="advance_collection_balance" schemaName="payable"
                      oldColumnName="invoiceLineReference"
                      newColumnName="invoice_line_reference"/>
    </changeSet>

    <changeSet id="20250618080500-2" author="caokhanhmultiplier" dbms="postgresql">
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as invoiceLineReference column does not exist or invoice_line_reference already exists">
            <and>
                <tableExists tableName="advance_collection_balance_aud" schemaName="payable"/>
                <columnExists tableName="advance_collection_balance_aud" schemaName="payable" columnName="invoiceLineReference"/>
                <not>
                    <columnExists tableName="advance_collection_balance_aud" schemaName="payable" columnName="invoice_line_reference"/>
                </not>
            </and>
        </preConditions>
        <comment>Rename invoiceLineReference column to invoice_line_reference for consistent snake_case naming in advance_collection_balance_aud</comment>
        <renameColumn tableName="advance_collection_balance_aud" schemaName="payable"
                      oldColumnName="invoiceLineReference"
                      newColumnName="invoice_line_reference"/>
    </changeSet>

</databaseChangeLog>