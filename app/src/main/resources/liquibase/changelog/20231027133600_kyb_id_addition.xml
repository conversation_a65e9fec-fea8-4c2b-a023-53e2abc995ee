<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="gayan-nettasinghe" id="20231027133600-1" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already ran">
            <not>
                <columnExists tableName="legal_entity" schemaName="company" columnName="kyb_id"/>
            </not>
        </preConditions>
        <addColumn tableName="legal_entity" schemaName="company">
            <column name="kyb_id" type="BIGINT"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>