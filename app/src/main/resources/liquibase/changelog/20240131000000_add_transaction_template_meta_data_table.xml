<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="add-transaction_template_meta_data" author="chamil">
        <validCheckSum>ANY</validCheckSum>
        <createTable tableName="transaction_template_meta_data" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="pk_transaction_template_meta_data"/>
            </column>
            <column name="transaction_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="company_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="template_identifier" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="config" type="TEXT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

</databaseChangeLog>