<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="himanshugoyal" id="20241003001001-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="company_country_value_added_tax" schemaName="payable"/>
            </not>
        </preConditions>
        <comment>Create company_country_value_added_tax table</comment>
        <createTable tableName="company_country_value_added_tax" schemaName="payable">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="company_id" type="BIGINT">
            </column>
            <column name="country_code" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="rate" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="rate_type" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint
            schemaName="payable"
            tableName="company_country_value_added_tax"
            columnNames="company_id, country_code"
            constraintName="uc_jpacompanycountryvalueaddedtax_companyid_countrycode"
        />
    </changeSet>


    <changeSet author="himanshugoyal" id="20241003001001-2">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="company_country_value_added_tax_aud" schemaName="payable"/>
            </not>
        </preConditions>
        <comment>Create company_value_added_tax_aud table</comment>
        <createTable tableName="company_country_value_added_tax_aud" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="company_country_vat_aud_pkey"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="company_country_vat_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="company_id" type="BIGINT">
            </column>
            <column name="country_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="rate" type="DECIMAL">
                <constraints nullable="false"/>
            </column>
            <column name="rate_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet author="himanshugoyal" id="20241003001001-3">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists
                    indexName="idx_jpacompanycountryvalueaddedtax"
                    schemaName="payable"
                    tableName="company_country_value_added_tax"
                />
            </not>
        </preConditions>
        <comment>Adding index to company_country_value_added_tax table</comment>
        <createIndex
            indexName="idx_jpacompanycountryvalueaddedtax"
            schemaName="payable"
            tableName="company_country_value_added_tax"
        >
            <column name="company_id"/>
            <column name="country_code"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>