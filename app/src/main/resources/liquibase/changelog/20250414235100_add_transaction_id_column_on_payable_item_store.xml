<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="20250414235100-1" author="crobles">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="payable" tableName="payable_item_store" columnName="transaction_id"/>
            </not>
        </preConditions>

        <comment>Add transaction_id on payable_item_store table</comment>
        <addColumn schemaName="payable" tableName="payable_item_store">
            <column name="transaction_id" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20250414235100-2" author="crobles">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists indexName="payable_item_store_transaction_id_idx" schemaName="payable"
                             tableName="payable_item_store"/>
            </not>
        </preConditions>

        <comment>Index transaction_id on payable_item_store table</comment>
        <createIndex indexName="payable_item_store_transaction_id_idx" schemaName="payable"
                     tableName="payable_item_store">
            <column name="transaction_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="20250414235100-3" author="crobles">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists schemaName="payable" tableName="payable_item_store_aud" columnName="transaction_id"/>
            </not>
        </preConditions>

        <comment>Add transaction_id on payable_item_store_aud table</comment>
        <addColumn schemaName="payable" tableName="payable_item_store_aud">
            <column name="transaction_id" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog> 