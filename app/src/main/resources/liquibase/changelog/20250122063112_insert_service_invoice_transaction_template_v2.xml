<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="20250122063112-1" author="shubhamjainm">
        <sql>
            INSERT INTO payable.transaction_template_v2 (
                id, transaction_type, is_default, description, json, created_by, created_on, updated_by, updated_on
            ) VALUES (
                         gen_random_uuid(),
                         'GP_SERVICE_INVOICE',
                         true,
                         'Default Template for Service Invoice',
                         '{"identifier":"gp-service-invoice-default","invoiceType":"GP_SERVICE_INVOICE","lineItemTypes":["ONE_TIME_SETUP_FEE","PAYSLIP_MINIMUM_COMMITMENT","PER_PAYSLIP_FEE","TOTAL_PAYMENTS","STAT_FILING","YEAR_END_DOCUMENTATION"],"dataFormatters":[],"netSuiteTemplate":"345","invoiceEntityMode":"LOCAL","referenceTemplate":"getStartMonth() + '' Payroll Service Fee''","invoiceEntitySender":"MT SGP"}',
                         -1,
                         now(),
                         -1,
                         now()
                     );
        </sql>
    </changeSet>

</databaseChangeLog>