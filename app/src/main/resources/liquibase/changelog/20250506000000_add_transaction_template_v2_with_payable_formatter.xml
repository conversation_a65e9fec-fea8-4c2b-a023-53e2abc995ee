<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="20250506000000-1" author="caokhanhmultiplier" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <comment>Add a new second invoice template with payable formatter for filtering Canadian items</comment>
        <sql>
            INSERT INTO payable.transaction_template_v2 (id, transaction_type, is_default, description, json, created_by,
                                                        created_on, updated_by, updated_on)
            VALUES (gen_random_uuid(), 'SECOND_INVOICE', false, 'Trinet Second Invoice Template',
                    '{
                        "identifier": "trinet-second-invoice-template",
                        "invoiceType": "SECOND_INVOICE",
                        "lineItemTypes": [
                            "CANADA_GROSS_WAGES",
                            "CANADA_EMPLOYER_BENEFITS",
                            "CANADA_EMPLOYMENT_INSURANCE",
                            "CANADA_PENSION_PLAN",
                            "CANADA_PROVINCIAL_HEALTH_TAX",
                            "CANADA_WORKERS_COMPENSATION",
                            "EOR_SALARY_DISBURSEMENT",
                            "EOR_EXPENSE_DISBURSEMENT",
                            "MANAGEMENT_FEE_EOR_PAYROLL",
                            "PLATFORM_FEE",
                            "BILLED_GROSS_SALARY_SUPPLEMENTARY",
                            "BILLED_GROSS_SALARY",
                            "BILLED_MANAGEMENT_FEE",
                            "VAT_PAYROLL_COST",
                            "VAT_PAYROLL_EXPENSE",
                            "VAT_MANAGEMENT_FEE_EOR_PAYROLL",
                            "VAT_BILLED_MANAGEMENT_FEE",
                            "SEVERANCE_DEPOSIT_EOR_PAYROLL"
                        ],
                        "dataFormatters": [
                            {
                                "name": "com.multiplier.payable.engine.formatter.UpdateItemTypesDataFormatter",
                                "order": 0,
                                "additionalData": {
                                    "targetLineItemType": "VAT_AGGREGATED"
                                },
                                "supportedItemTypes": [
                                    "VAT_PAYROLL_COST",
                                    "VAT_PAYROLL_EXPENSE",
                                    "VAT_MANAGEMENT_FEE_EOR_PAYROLL",
                                    "VAT_BILLED_MANAGEMENT_FEE",
                                    "VAT_AGGREGATED"
                                ]
                            },
                            {
                                "name": "com.multiplier.payable.engine.formatter.GroupingDataFormatter",
                                "field": "countryCode",
                                "order": 1,
                                "filter": "lineItemType == ''VAT_AGGREGATED''"
                            },
                            {
                                "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
                                "field": "itemCount",
                                "order": 2,
                                "filter": "lineItemType == ''VAT_AGGREGATED''",
                                "additionalData": {
                                    "text": "''1''"
                                }
                            },
                            {
                                "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
                                "field": "description",
                                "order": 3,
                                "filter": "lineItemType == ''VAT_AGGREGATED''",
                                "additionalData": {
                                    "text": "''Additional Payroll Charges '' + ''('' + getCountryCodeInUpperCase() + '')''"
                                }
                            },
                            {
                                "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
                                "field": "memberName",
                                "order": 4,
                                "filter": "lineItemType == ''VAT_AGGREGATED''",
                                "additionalData": {
                                    "text": "''''"
                                }
                            },
                            {
                                "name": "com.multiplier.payable.engine.formatter.OrderByFieldsFormatter",
                                "field": "countryCode",
                                "order": 5,
                                "filter": "lineItemType == ''VAT_AGGREGATED''"
                            },
                            {
                                "name": "com.multiplier.payable.engine.formatter.GroupingDataFormatter",
                                "field": "contractId",
                                "order": 6,
                                "filter": "lineItemType == ''SEVERANCE_DEPOSIT_EOR_PAYROLL''"
                            },
                            {
                                "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
                                "field": "description",
                                "order": 7,
                                "filter": "lineItemType == ''SEVERANCE_DEPOSIT_EOR_PAYROLL''",
                                "additionalData": {
                                    "text": "''Severance accrual: '' + baseCurrency + '' '' + amountInBaseCurrency"
                                }
                            },
                            {
                                "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
                                "field": "itemCount",
                                "order": 8,
                                "filter": "lineItemType == ''SEVERANCE_DEPOSIT_EOR_PAYROLL''",
                                "additionalData": {
                                    "text": "''1''"
                                }
                            }
                        ],
                        "netSuiteTemplate": "345",
                        "invoiceEntityMode": "LOCAL",
                        "payableFormatters": [
                            {
                                "name": "com.multiplier.payable.engine.formatter.payable.CompanyPayableItemFilterFormatter",
                                "order": 0,
                                "filter": "countryCode == ''CAN'' and (lineItemType == ''EOR_SALARY_DISBURSEMENT'' or lineItemType == ''EOR_EXPENSE_DISBURSEMENT'')"
                            }
                        ],
                        "referenceTemplate": "getStartMonth() + '' Salary - EOR''",
                        "invoiceEntitySender": "MT SGP",
                        "allowMultipleInvoiceSameMonth": true
                    }',
                    -1, now(), -1, now());
        </sql>
    </changeSet>

</databaseChangeLog>
