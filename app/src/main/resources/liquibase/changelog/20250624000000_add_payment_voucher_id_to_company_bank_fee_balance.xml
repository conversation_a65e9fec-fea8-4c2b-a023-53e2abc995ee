<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <!-- Truncate existing data to ensure clean state with new payment_voucher_id requirement -->
    <changeSet author="himanshugoyal" id="**************-1">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="company_bank_fee_balance" schemaName="payable"/>
        </preConditions>
        <comment>Truncate existing company_bank_fee_balance data to ensure clean state with new payment_voucher_id requirement</comment>
        <sql>
            TRUNCATE TABLE payable.company_bank_fee_balance CASCADE;
        </sql>
        <rollback>
            <comment>Cannot rollback truncate operation - data will be lost</comment>
        </rollback>
    </changeSet>

    <!-- Add payment_voucher_id column to company_bank_fee_balance table -->
    <changeSet author="himanshugoyal" id="**************-2">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="company_bank_fee_balance"
                             columnName="payment_voucher_id"
                             schemaName="payable"/>
            </not>
        </preConditions>
        <comment>Add payment_voucher_id column to company_bank_fee_balance table</comment>
        <addColumn tableName="company_bank_fee_balance" schemaName="payable">
            <column name="payment_voucher_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Add payment_voucher_id column to company_bank_fee_balance_aud table -->
    <changeSet author="himanshugoyal" id="**************-3">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="company_bank_fee_balance_aud"
                             columnName="payment_voucher_id"
                             schemaName="payable"/>
            </not>
        </preConditions>
        <comment>Add payment_voucher_id column to company_bank_fee_balance_aud table</comment>
        <addColumn tableName="company_bank_fee_balance_aud" schemaName="payable">
            <column name="payment_voucher_id" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
