<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <!---
        NOTE: These indices work only on new data starting from the given time.
        For example,

        Case #1:
        + Create invoice "AAA" with externalId = "42"
        + Create invoice "BBB" with externalId = "42"
        => Data constraint error

        Case #2:
        + Assuming we already have invoice "AAA" with externalId = "42" in the database
        + Create invoice "BBB" with externalId = "42"
        => No data constraint error
        + Create invoice "CCC" with externalId = "42"
        => Now, data constraint error
    -->

    <changeSet author="hieu.nguyen" id="20231130000000-1">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already dropped">
            <columnExists schemaName="payable" tableName="invoice" columnName="external_invoice_id"/>
        </preConditions>
        <sql>
            drop index payable.unique_invoice_external_invoice_id;
        </sql>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20231130000000-2">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already dropped">
            <columnExists schemaName="payable" tableName="invoice" columnName="external_invoice_id"/>
        </preConditions>
        <sql>
            CREATE UNIQUE INDEX unique_invoice_external_invoice_id
            ON payable.invoice(external_invoice_id)
            WHERE created_on > '2023-11-30 00:00:00.000';
        </sql>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20231130000000-3">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already dropped">
            <columnExists schemaName="payable" tableName="credit_note" columnName="external_id"/>
        </preConditions>
        <sql>
            drop index payable.unique_credit_note_external_id;
        </sql>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20231130000000-4">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already dropped">
            <columnExists schemaName="payable" tableName="credit_note" columnName="external_id"/>
        </preConditions>
        <sql>
            CREATE UNIQUE INDEX unique_credit_note_external_id
            ON payable.credit_note(external_id)
            WHERE created_on > '2023-11-30 00:00:00.000';
        </sql>
    </changeSet>

</databaseChangeLog>
