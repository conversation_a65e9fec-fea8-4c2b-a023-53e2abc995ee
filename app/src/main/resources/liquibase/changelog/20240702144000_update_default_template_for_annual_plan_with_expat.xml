<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="caokhanh" id="20240702144000-1">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped if not exist.">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM payable.transaction_template
                WHERE transaction_type = 'ANNUAL_PLAN_INVOICE' AND company_id is null;
            </sqlCheck>
        </preConditions>
        <sql>
            UPDATE payable.transaction_template
            SET config = '{
  "identifier": "annual-plan-default",
  "invoiceType": "ANNUAL_PLAN_INVOICE",
  "referenceTemplate": "''Annual Subscription Fee '' + getStartMonth() + '' - '' + getEndMonth() + getPaymentTermDescription()",
  "invoiceEntityMode": "LOCAL",
  "invoiceEntitySender": "MT SGP",
  "lineItemTypes": [
    "ANNUAL_MANAGEMENT_FEE_EOR"
  ],
  "netSuiteTemplate": "345",
  "dataFormatters": [
    {
      "name": "com.multiplier.payable.engine.formatter.GroupingDataFormatter",
      "field": "countryCode,amountInBaseCurrency,billableCost,periodStartDate,periodEndDate,annualSeatPaymentTerm.interval,annualSeatPaymentTerm.timeUnit,annualSeatPaymentTerm.periodNumber,annualSeatPaymentTerm.periodCount,countryWorkStatus",
      "order": 0
    },
    {
      "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
      "field": "description",
      "order": 1,
      "additionalData": {
        "text": "''Annual Management Fee for '' + itemCount + '' '' + getAnnualMemberSingularOrPlural() + '' '' + getCountryCodeAndCountryWorkStatusDescription() + getAnnualBillingItemDescription() + '': '' + baseCurrency + '' '' + amountInBaseCurrency"
      }
    },
    {
      "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
      "field": "memberName",
      "order": 2,
      "additionalData": {
        "text": "''''"
      }
    },
    {
      "name": "com.multiplier.payable.engine.formatter.OrderByFieldsFormatter",
      "field": "countryCode,annualSeatPaymentTerm.periodNumber",
      "order": 3
    }
  ]
}'
            WHERE transaction_type = 'ANNUAL_PLAN_INVOICE' AND company_id is null;
        </sql>
    </changeSet>
</databaseChangeLog>