<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="caokhanhmultiplier" id="20250603134800-1" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <sql>
            INSERT INTO payable.transaction_template_v2 (
                id, transaction_type, is_default, description, json, created_by, created_on, updated_by, updated_on
            ) VALUES (
                gen_random_uuid(),
                'ANNUAL_PLAN_AOR_INVOICE',
                false,
                'Template for Annual Plan AOR AP Invoice with Adjustments',
                '{
                  "identifier": "annual-plan-aor-ap-with-adjustments",
                  "invoiceType": "ANNUAL_PLAN_AOR_AP_INVOICE",
                  "referenceTemplate": "''Subscription Fee AP '' + getStartMonth() + '' - '' + getEndMonth() + getPaymentTermDescription()",
                  "invoiceEntityMode": "LOCAL",
                  "invoiceEntitySender": "MT SGP",
                  "lineItemTypes": [
                    "ANNUAL_MANAGEMENT_FEE_AOR_CONTRACTOR",
                    "ANNUAL_MANAGEMENT_FEE_AOR_FREELANCER",
                    "ORDER_FORM_ADVANCE_ADJUSTMENT_AOR_CONTRACTOR",
                    "ORDER_FORM_ADVANCE_ADJUSTMENT_AOR_FREELANCER"
                  ],
                  "netSuiteTemplate": "345",
                  "dataFormatters": [
                    {
                      "name": "com.multiplier.payable.engine.formatter.DefaultUpdateItemTypesDataFormatter",
                      "order": 0,
                      "additionalData": {
                        "targetLineItemType": "ORDER_FORM_ADVANCE_ADJUSTMENT_AOR"
                      },
                      "supportedItemTypes": [
                        "ORDER_FORM_ADVANCE_ADJUSTMENT_AOR_CONTRACTOR",
                        "ORDER_FORM_ADVANCE_ADJUSTMENT_AOR_FREELANCER"
                      ]
                    },
                    {
                      "name": "com.multiplier.payable.engine.formatter.GroupingDataFormatter",
                      "field": "countryCode,amountInBaseCurrency,billableCost,periodStartDate,periodEndDate,annualSeatPaymentTerm.interval,annualSeatPaymentTerm.timeUnit,annualSeatPaymentTerm.periodNumber,annualSeatPaymentTerm.periodCount,countryWorkStatus,lineItemType",
                      "order": 1
                    },
                    {
                      "name": "com.multiplier.payable.engine.formatter.GroupingDataFormatter",
                      "field": "baseCurrency",
                      "order": 2,
                      "filter": "lineItemType == ''ORDER_FORM_ADVANCE_ADJUSTMENT_AOR''"
                    },
                    {
                      "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
                      "field": "description",
                      "order": 3,
                      "filter": "lineItemType == ''ANNUAL_MANAGEMENT_FEE_AOR_CONTRACTOR'' or lineItemType == ''ANNUAL_MANAGEMENT_FEE_AOR_FREELANCER''",
                      "additionalData": {
                        "text": "''Subscription Management Fee for '' + itemCount + '' '' + getAORContractType() + '' ('' + countryCode + ''): '' + baseCurrency + '' '' + amountInBaseCurrency"
                      }
                    },
                    {
                      "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
                      "field": "description",
                      "order": 4,
                      "filter": "lineItemType == ''ORDER_FORM_ADVANCE_ADJUSTMENT_AOR''",
                      "additionalData": {
                        "text": "''Order Form Advance Adjustment AOR - '' + baseCurrency + '' '' + getAmountRoundedUp2Decimals()"
                      }
                    },
                    {
                      "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
                      "field": "itemCount",
                      "order": 5,
                      "filter": "lineItemType == ''ORDER_FORM_ADVANCE_ADJUSTMENT_AOR''",
                      "additionalData": {
                        "text": "''1''"
                      }
                    },
                    {
                      "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
                      "field": "memberName",
                      "order": 6,
                      "additionalData": {
                        "text": "''''"
                      }
                    },
                    {
                      "name": "com.multiplier.payable.engine.formatter.OrderByFieldsFormatter",
                      "field": "countryCode,annualSeatPaymentTerm.periodNumber,lineItemType",
                      "order": 7
                    }
                  ]
                }',
                -1,
                now(),
                -1,
                now()
            );
        </sql>
    </changeSet>

</databaseChangeLog>
