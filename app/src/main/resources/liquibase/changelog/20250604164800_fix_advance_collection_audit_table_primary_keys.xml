<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- Fix advance_collection_balance_aud primary key -->
    <changeSet id="20250604164800-1" author="caokhanhmultiplier" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as table doesn't exist or primary key already correct">
            <tableExists tableName="advance_collection_balance_aud" schemaName="payable"/>
        </preConditions>

        <comment>Fix audit table primary key to allow multiple audit records for same entity ID</comment>

        <!-- Drop the incorrect primary key constraint on id only -->
        <dropPrimaryKey tableName="advance_collection_balance_aud"
                        schemaName="payable"/>

        <!-- Add composite primary key (id, rev) following product-catalogue-service pattern -->
        <addPrimaryKey tableName="advance_collection_balance_aud"
                       schemaName="payable"
                       columnNames="id,rev"
                       constraintName="advance_collection_balance_aud_pkey"/>
    </changeSet>

    <!-- Fix advance_collection_entry_aud primary key -->
    <changeSet id="20250604164800-2" author="caokhanhmultiplier" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as table doesn't exist or primary key already correct">
            <tableExists tableName="advance_collection_entry_aud" schemaName="payable"/>
        </preConditions>

        <comment>Fix audit table primary key to allow multiple audit records for same entity ID</comment>

        <!-- Drop the incorrect primary key constraint on id only -->
        <dropPrimaryKey tableName="advance_collection_entry_aud"
                        schemaName="payable"/>

        <!-- Add composite primary key (id, rev) following product-catalogue-service pattern -->
        <addPrimaryKey tableName="advance_collection_entry_aud"
                       schemaName="payable"
                       columnNames="id,rev"
                       constraintName="advance_collection_entry_aud_pkey"/>
    </changeSet>

</databaseChangeLog>
