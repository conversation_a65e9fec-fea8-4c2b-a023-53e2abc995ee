<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="himanshu" id="20240522001001-1">
        <validCheckSum>ANY</validCheckSum>
        <sql>
            UPDATE payable.transaction_template set config = '{
            "identifier": "second-invoice-default",
            "invoiceType": "SECOND_INVOICE",
            "referenceTemplate": "getMonthInThreeLetters() + '''''''' + getYearInTwoDigits() + '' Gross Salary - EOR''",
            "invoiceEntityMode": "LOCAL",
            "invoiceEntitySender": "MT SGP",

            "lineItemTypes": [
            "EOR_SALARY_DISBURSEMENT",
            "EOR_EXPENSE_DISBURSEMENT",
            "MANAGEMENT_FEE_EOR",
            "PLATFORM_FEE"
            ],

            "netSuiteTemplate": "345",
            "dataFormatters": [
            {
            "name": "com.multiplier.payable.engine.formatter.OrderByFieldsFormatter",
            "field": "contractId,lineItemType",
            "order": 0
            }
            ]
            }' where id = (
            SELECT id
            FROM payable.transaction_template
            WHERE transaction_type = 'SECOND_INVOICE'
            AND company_id is null
            );
        </sql>
    </changeSet>
</databaseChangeLog>