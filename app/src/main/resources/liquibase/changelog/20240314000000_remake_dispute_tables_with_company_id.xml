<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="hieu.nguyen" id="20240314000000-1">
        <preConditions onFail="MARK_RAN">
            <tableExists schemaName="platform" tableName="dispute_aud"/>
        </preConditions>
        <dropTable schemaName="platform" tableName="dispute_aud"/>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20240314000000-2">
        <preConditions onFail="MARK_RAN">
            <tableExists schemaName="platform" tableName="dispute"/>
        </preConditions>
        <dropTable schemaName="platform" tableName="dispute"/>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20240314000000-3">
        <preConditions onFail="MARK_RAN">
            <tableExists schemaName="platform" tableName="dispute_origin_aud"/>
        </preConditions>
        <dropTable schemaName="platform" tableName="dispute_origin_aud"/>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20240314000000-4">
        <preConditions onFail="MARK_RAN">
            <tableExists schemaName="platform" tableName="dispute_origin"/>
        </preConditions>
        <dropTable schemaName="platform" tableName="dispute_origin"/>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20240314000000-5">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="1">
                SELECT COUNT(sequence_name)
                FROM information_schema.sequences
                WHERE sequence_schema='platform' AND sequence_name='dispute_seq';
            </sqlCheck>
        </preConditions>
        <dropSequence
            schemaName="platform"
            sequenceName="dispute_seq"/>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20240314000000-6">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT COUNT(sequence_name)
                FROM information_schema.sequences
                WHERE sequence_schema='platform' AND sequence_name='dispute_seq';
            </sqlCheck>
        </preConditions>
        <createSequence schemaName="platform"
                        sequenceName="dispute_seq"
                        cacheSize="1"
                        cycle="false"
                        dataType="BIGINT"
                        incrementBy="1"
                        maxValue="9223372036854775807"
                        minValue="1"
                        startValue="1"/>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20240314000000-7">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT COUNT(sequence_name)
                FROM information_schema.sequences
                WHERE sequence_schema='platform' AND sequence_name='dispute_origin_seq';
            </sqlCheck>
        </preConditions>
        <createSequence schemaName="platform"
                        sequenceName="dispute_origin_seq"
                        cacheSize="1"
                        cycle="false"
                        dataType="BIGINT"
                        incrementBy="1"
                        maxValue="9223372036854775807"
                        minValue="1"
                        startValue="1"/>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20240314000000-8">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT COUNT(schema_name)
                FROM information_schema.schemata
                WHERE schema_name = 'platform'
            </sqlCheck>
        </preConditions>
        <sql>
            CREATE SCHEMA platform
        </sql>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20240314000000-9">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="platform" tableName="dispute_origin"/>
            </not>
        </preConditions>
        <createTable schemaName="platform" tableName="dispute_origin">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="origin_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex schemaName="platform" tableName="dispute_origin" indexName="idx_dispute_origin_type">
            <column name="type"/>
        </createIndex>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20240314000000-10">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="platform" tableName="dispute_origin_aud"/>
            </not>
        </preConditions>
        <createTable schemaName="platform" tableName="dispute_origin_aud">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="dispute_origin_aud_pkey"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="dispute_origin_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="origin_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20240314000000-11">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="platform" tableName="dispute"/>
            </not>
        </preConditions>
        <createTable schemaName="platform" tableName="dispute">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="category" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="sub_category" type="VARCHAR(255)"/>
            <column name="description" type="TEXT"/>
            <column name="dispute_origin_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addForeignKeyConstraint
            baseTableSchemaName="platform"
            baseTableName="dispute"
            baseColumnNames="dispute_origin_id"
            constraintName="fk_dispute_dispute_origin_dispute_origin_id"
            referencedTableSchemaName="platform"
            referencedTableName="dispute_origin"
            referencedColumnNames="id"
            onDelete="CASCADE"
            onUpdate="CASCADE"/>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20240314000000-12">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="platform" tableName="dispute_aud"/>
            </not>
        </preConditions>
        <createTable schemaName="platform" tableName="dispute_aud">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="dispute_aud_pkey"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="dispute_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(255)"/>
            <column name="category" type="VARCHAR(255)"/>
            <column name="sub_category" type="VARCHAR(255)"/>
            <column name="description" type="TEXT"/>
            <column name="dispute_origin_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

</databaseChangeLog>