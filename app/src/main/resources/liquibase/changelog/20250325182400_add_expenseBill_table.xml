<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <!-- Create Sequence -->
    <changeSet id="20250325182400-1" author="prakharAtMultiplier">
        <createSequence dataType="bigint"
                        incrementBy="1"
                        schemaName="payable"
                        sequenceName="expense_bill_seq"
                        startValue="1"/>
    </changeSet>

    <!-- Create Table with Sequence & Index -->
    <changeSet id="20250325182400-2" author="prakharAtMultiplier">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="expense_bill" schemaName="payable"/>
            </not>
        </preConditions>

        <createTable tableName="expense_bill" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="expense_bill_pkey"/>
            </column>

            <!-- Required Fields -->
            <column name="transaction_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="transaction_date" type="DATE">
                <constraints nullable="false"/>
            </column>
            <column name="vendor_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="VARCHAR(3)">
                <constraints nullable="false"/>
            </column>
            <column name="items" type="JSONB">
                <constraints nullable="false"/>
            </column>
            <column name="vendor_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="record_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="account_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="department_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="location_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="reference" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="external_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="external_system" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="payroll_cycle_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="country_code" type="VARCHAR(3)">
                <constraints nullable="false"/>
            </column>
            <column name="multiplier_payable_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>

            <!-- Optional Fields -->
            <column name="due_date" type="DATE"/>
            <column name="last_synced_time" type="TIMESTAMP WITHOUT TIME ZONE"/>

            <!-- Audit Columns -->
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>

        <!-- Add Indexes -->
        <createIndex tableName="expense_bill" indexName="idx_expense_bill_tx_id" schemaName="payable">
            <column name="transaction_id"/>
        </createIndex>
        <createIndex tableName="expense_bill" indexName="idx_expense_bill_payroll_cycle" schemaName="payable">
            <column name="payroll_cycle_id"/>
        </createIndex>
    </changeSet>

    <!-- Create Audit Table -->
    <changeSet id="20250325182400-3" author="prakharAtMultiplier">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="expense_bill_aud" schemaName="payable"/>
            </not>
        </preConditions>

        <createTable tableName="expense_bill_aud" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="expense_bill_aud_pkey"/>
            </column>

            <column name="rev" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="expense_bill_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT"/>

            <!-- Required Fields -->
            <column name="transaction_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="transaction_date" type="DATE">
                <constraints nullable="false"/>
            </column>
            <column name="vendor_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="VARCHAR(3)">
                <constraints nullable="false"/>
            </column>
            <column name="items" type="JSONB">
                <constraints nullable="false"/>
            </column>
            <column name="vendor_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="record_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="account_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="department_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="location_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="reference" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="external_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="external_system" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="payroll_cycle_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="country_code" type="VARCHAR(3)">
                <constraints nullable="false"/>
            </column>
            <column name="multiplier_payable_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>

            <!-- Optional Fields -->
            <column name="due_date" type="DATE"/>
            <column name="last_synced_time" type="TIMESTAMP WITHOUT TIME ZONE"/>

            <!-- Audit Columns -->
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>

        <!-- Add Index for Audit Table -->
        <createIndex tableName="expense_bill_aud" indexName="idx_expense_bill_aud_tx_id" schemaName="payable">
            <column name="transaction_id"/>
        </createIndex>
    </changeSet>

    <!-- Set Default Value for ID -->
    <changeSet id="20250325182400-4" author="prakharAtMultiplier">
        <sql>
            ALTER TABLE payable.expense_bill
                ALTER COLUMN id SET DEFAULT nextval('payable.expense_bill_seq');
        </sql>
    </changeSet>

    <!-- Add Foreign Key Constraint -->
    <changeSet id="20250325182400-5" author="prakharAtMultiplier">
        <addForeignKeyConstraint baseTableName="expense_bill"
                                 baseColumnNames="multiplier_payable_id"
                                 baseTableSchemaName="payable"
                                 referencedTableName="multiplier_payable"
                                 referencedColumnNames="id"
                                 referencedTableSchemaName="payable"
                                 constraintName="fk_expense_bill_multiplier_payable"
                                 onDelete="CASCADE"
                                 onUpdate="CASCADE"/>
    </changeSet>

</databaseChangeLog>