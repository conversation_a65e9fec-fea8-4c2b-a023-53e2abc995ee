<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="edward" id="20240222150200-1">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already modified">
            <not>
                <columnExists schemaName="payable" tableName="company_payable" columnName="transaction_id"/>
            </not>
        </preConditions>
        <renameColumn
            schemaName="payable"
            columnDataType="VARCHAR(50)"
            newColumnName="transaction_id"
            oldColumnName="tran_id"
            tableName="company_payable"/>
    </changeSet>

    <changeSet author="edward" id="20240222150200-2">
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already indexed">
            <not>
                <indexExists schemaName="payable" tableName="company_payable" indexName="idx_cp_transaction_id"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_cp_transaction_id" schemaName="payable" tableName="company_payable">
            <column name="transaction_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="edward" id="20240222150200-3">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already modified">
            <not>
                <columnExists schemaName="payable" tableName="company_payable_aud" columnName="transaction_id"/>
            </not>
        </preConditions>
        <renameColumn
            schemaName="payable"
            columnDataType="VARCHAR(50)"
            newColumnName="transaction_id"
            oldColumnName="tran_id"
            tableName="company_payable_aud"/>
    </changeSet>
</databaseChangeLog>