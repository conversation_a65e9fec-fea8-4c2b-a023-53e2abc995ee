<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <!-- Create advance_collection_balance table -->
    <changeSet id="20250526224947-1" author="caokhanhmultiplier" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <tableExists tableName="advance_collection_balance" schemaName="payable"/>
            </not>
        </preConditions>

        <!-- Create sequence for advance_collection_balance -->
        <createSequence sequenceName="advance_collection_balance_seq"
                        schemaName="payable"
                        startValue="1"
                        incrementBy="1"/>

        <createTable tableName="advance_collection_balance" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="entity_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="target_product_line_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="target_product_dimensions" type="jsonb">
                <constraints nullable="false"/>
            </column>
            <column name="target_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="hash" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="balance" type="DOUBLE PRECISION">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="VARCHAR(3)">
                <constraints nullable="false"/>
            </column>
            <column name="invoice_no" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="updated_on" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="updated_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Create indexes for advance_collection_balance -->
        <createIndex indexName="idx_acb_entity_id" tableName="advance_collection_balance" schemaName="payable">
            <column name="entity_id"/>
        </createIndex>
        <createIndex indexName="idx_acb_company_id" tableName="advance_collection_balance" schemaName="payable">
            <column name="company_id"/>
        </createIndex>
        <createIndex indexName="idx_acb_line_code" tableName="advance_collection_balance" schemaName="payable">
            <column name="target_product_line_code"/>
        </createIndex>
        <createIndex indexName="idx_acb_invoice_no" tableName="advance_collection_balance" schemaName="payable">
            <column name="invoice_no"/>
        </createIndex>

        <!-- Add unique constraint on hash column -->
        <addUniqueConstraint tableName="advance_collection_balance"
                             schemaName="payable"
                             columnNames="hash"
                             constraintName="unique_acb_hash"/>
    </changeSet>

    <!-- Create advance_collection_entry table -->
    <changeSet id="20250526224947-2" author="caokhanhmultiplier" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <tableExists tableName="advance_collection_entry" schemaName="payable"/>
            </not>
        </preConditions>

        <!-- Create sequence for advance_collection_entry -->
        <createSequence sequenceName="advance_collection_entry_seq"
                        schemaName="payable"
                        startValue="1"
                        incrementBy="1"/>

        <createTable tableName="advance_collection_entry" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="balance_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="transaction_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="note" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="references" type="jsonb">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="DOUBLE PRECISION">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="updated_on" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="updated_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Create index for advance_collection_entry -->
        <createIndex indexName="idx_ace_balance_id" tableName="advance_collection_entry" schemaName="payable">
            <column name="balance_id"/>
        </createIndex>

        <createIndex indexName="idx_ace_transactionId" tableName="advance_collection_entry" schemaName="payable">
            <column name="transaction_id"/>
        </createIndex>

        <!-- Add foreign key constraint -->
        <addForeignKeyConstraint baseTableName="advance_collection_entry"
                                 baseTableSchemaName="payable"
                                 baseColumnNames="balance_id"
                                 constraintName="fk_ace_balance_id"
                                 referencedTableName="advance_collection_balance"
                                 referencedTableSchemaName="payable"
                                 referencedColumnNames="id"/>
    </changeSet>

    <!-- Create audit table for advance_collection_balance -->
    <changeSet id="20250526224947-3" author="caokhanhmultiplier" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <tableExists tableName="advance_collection_balance_aud" schemaName="payable"/>
            </not>
        </preConditions>

        <createTable tableName="advance_collection_balance_aud" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="company_id" type="BIGINT"/>
            <column name="entity_id" type="BIGINT"/>
            <column name="target_product_line_code" type="VARCHAR(255)"/>
            <column name="target_product_dimensions" type="jsonb"/>
            <column name="target_type" type="VARCHAR(50)"/>
            <column name="hash" type="VARCHAR(255)"/>
            <column name="balance" type="DOUBLE PRECISION"/>
            <column name="currency" type="VARCHAR(3)"/>
            <column name="invoice_no" type="VARCHAR(255)"/>
            <column name="created_on" type="TIMESTAMP"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>
    </changeSet>

    <!-- Create audit table for advance_collection_entry -->
    <changeSet id="20250526224947-4" author="caokhanhmultiplier" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <tableExists tableName="advance_collection_entry_aud" schemaName="payable"/>
            </not>
        </preConditions>

        <createTable tableName="advance_collection_entry_aud" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="balance_id" type="BIGINT"/>
            <column name="transaction_id" type="VARCHAR(255)"/>
            <column name="note" type="TEXT"/>
            <column name="references" type="jsonb"/>
            <column name="status" type="VARCHAR(50)"/>
            <column name="amount" type="DOUBLE PRECISION"/>
            <column name="created_on" type="TIMESTAMP"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>
    </changeSet>

</databaseChangeLog>
