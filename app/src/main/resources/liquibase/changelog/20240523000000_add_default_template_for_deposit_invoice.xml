<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="nguyen.pham" id="20240523000000">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exist.">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM payable.transaction_template
                WHERE transaction_type = 'DEPOSIT_INVOICE'
                AND company_id is null;
            </sqlCheck>
        </preConditions>
        <sql>
            INSERT INTO payable.transaction_template (transaction_type, company_id,
            template_identifier, config) VALUES('DEPOSIT_INVOICE', NULL, 'Default Template for Deposit Invoice',
            '{"identifier":"deposit-invoice-default","invoiceType":"DEPOSIT_INVOICE","referenceTemplate":"''Deposit - '' + getMemberName() + '' '' + getContractId()","invoiceEntityMode":"LOCAL","invoiceEntitySender":"MT SGP","lineItemTypes":["MEMBER_DEPOSIT"],"netSuiteTemplate":"345","dataFormatters":[]}');
        </sql>
    </changeSet>
</databaseChangeLog>