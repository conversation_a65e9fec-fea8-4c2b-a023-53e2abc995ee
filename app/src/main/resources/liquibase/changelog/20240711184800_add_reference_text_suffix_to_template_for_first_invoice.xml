<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="khanhcao" id="20240711184800-1">
        <validCheckSum>ANY</validCheckSum>
        <sql>
            UPDATE payable.transaction_template
            SET config = '{"identifier":"first-invoice-default","invoiceType":"FIRST_INVOICE","referenceTemplate":"getStartMonth() + '' Gross Salary - EOR'' + getTransactionCount()","invoiceEntityMode":"LOCAL","invoiceEntitySender":"MT SGP","lineItemTypes":["GROSS_SALARY","MANAGEMENT_FEE_EOR"],"netSuiteTemplate":"345","dataFormatters":[{"name":"com.multiplier.payable.engine.formatter.OrderByFieldsFormatter","field":"contractId,lineItemType","order":0}]}'
            WHERE transaction_type = 'FIRST_INVOICE'
              AND company_id IS NULL;
        </sql>
    </changeSet>
</databaseChangeLog>