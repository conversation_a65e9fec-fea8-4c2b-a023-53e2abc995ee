<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="nguyen-pham" id="20240302141500-1">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM pg_sequences WHERE sequencename = 'payable.transaction_template_meta_data_id_seq'
            </sqlCheck>
        </preConditions>
        <sql>
            DO '
            DECLARE
            start_value bigint;
            BEGIN
            SELECT COALESCE(MAX(id), 0) + 1 INTO start_value FROM payable.transaction_template_meta_data;

            EXECUTE ''CREATE SEQUENCE payable.transaction_template_meta_data_id_seq START WITH '' || start_value;
            END; ' LANGUAGE plpgsql;
        </sql>
    </changeSet>

    <changeSet id="20240302141500-2" author="nguyen-pham">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*) FROM information_schema.columns
                                WHERE table_name = 'payable.transaction_template_meta_data'
                                  AND column_name = 'id' AND column_default ILIKE 'nextval(%'
            </sqlCheck>
        </preConditions>
        <sql>
            ALTER TABLE payable.transaction_template_meta_data
                ALTER COLUMN id SET DEFAULT nextval('payable.transaction_template_meta_data_id_seq');
        </sql>
    </changeSet>


    <changeSet author="nguyen-pham" id="20240302141500-3">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exist.">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM payable.transaction_template_meta_data
                WHERE transaction_type = 'ANNUAL_PLAN_INVOICE' AND company_id is null;
            </sqlCheck>
        </preConditions>
        <sql>

            INSERT INTO payable.transaction_template_meta_data (transaction_type, company_id, template_identifier, config)
            VALUES ('ANNUAL_PLAN_INVOICE', NULL, 'Default Template for Annual Plan Invoice', '{
"identifier": "annual-plan-default",
"invoiceType": "ANNUAL_PLAN_INVOICE",
"referenceTemplate": "''Annual Subscription Fee '' + getStartMonth() + '' - '' + getEndMonth()",
"invoiceEntityMode": "LOCAL",
"invoiceEntitySender": "MT SGP",

"lineItemTypes": [
"ANNUAL_MANAGEMENT_FEE_EOR"
],

"netSuiteTemplate": "345",
"dataFormatters": [
{
"name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
"field": "description",
"order": 0,
"additionalData": {
"text": "''Yearly Management Fee: '' + baseCurrency + amountInBaseCurrency"
}
}
]
}');
        </sql>
    </changeSet>
</databaseChangeLog>