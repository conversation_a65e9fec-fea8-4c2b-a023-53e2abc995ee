<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="chamil" id="20240216111610-1">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exist.">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM payable.transaction_template_meta_data
                WHERE transaction_type = 'GP_FUNDING_INVOICE'
                  AND company_id is null;
            </sqlCheck>
        </preConditions>
        <sql>

            INSERT INTO payable.transaction_template_meta_data (id, transaction_type, company_id, template_identifier, config)
            VALUES (1, 'GP_FUNDING_INVOICE', NULL, 'Default Template for GP Funding Invoice', '{
"identifier": "global-payroll-funding-default",
"invoiceType": "GP_FUNDING_INVOICE",
"referenceTemplate": "getMonthInThreeLetters() + '''''''' + getYearInTwoDigits() + '' Payroll - '' + country",
"invoiceEntityMode": "LOCAL",
"invoiceEntitySender": "MT SGP",

"lineItemTypes": [
"PEO_SALARY_DISBURSEMENT"
],

"netSuiteTemplate": "345",
"dataFormatters": [
{
"name": "com.multiplier.payable.engine.formatter.GroupingDataFormatter",
"field": "countryCode",
"order": 0
},
{
"name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
"field": "description",
"order": 1,
"additionalData": {
"text": "getMonthInThreeLetters() + '''''''' + getYearInTwoDigits() + '' '' + cycle + '' Total Payroll Cost - '' + countryCode + '' ('' + itemCount + '' Members) - '' + baseCurrency + '' '' + amountInBaseCurrency"
}
}
]
}');
        </sql>
    </changeSet>
</databaseChangeLog>