<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="phong-tran" id="20250225000000-1">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <columnExists schemaName="payable" tableName="company_payable" columnName="skip_isr_generation"/>
            </not>
        </preConditions>
        <addColumn schemaName="payable" tableName="company_payable">
            <column name="skip_isr_generation" type="boolean" />
        </addColumn>
        <addNotNullConstraint schemaName="payable" tableName="company_payable" columnName="skip_isr_generation" defaultNullValue="false"/>
    </changeSet>

    <changeSet author="phong-tran" id="20250225000000-2">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <columnExists schemaName="payable" tableName="company_payable_aud" columnName="skip_isr_generation"/>
            </not>
        </preConditions>
        <addColumn schemaName="payable" tableName="company_payable_aud">
            <column name="skip_isr_generation" type="boolean" />
        </addColumn>
    </changeSet>
</databaseChangeLog>