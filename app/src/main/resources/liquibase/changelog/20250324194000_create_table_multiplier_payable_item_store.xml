<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <!-- Create Sequence -->
    <changeSet id="20250324194000-1" author="shivam.kumar">
        <createSequence dataType="bigint"
                        incrementBy="1"
                        schemaName="payable"
                        sequenceName="multiplier_payable_item_store_seq"
                        startValue="1"/>
    </changeSet>

    <!-- Create Table with Sequence & Index -->
    <changeSet id="20250324194000-2" author="shivam kumar">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="multiplier_payable_item_store" schemaName="payable"/>
            </not>
        </preConditions>

        <createTable tableName="multiplier_payable_item_store" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="multiplier_payable_item_store_pkey"/>
            </column>

            <column name="amount" type="NUMERIC(19,6)">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="VARCHAR(5)">
                <constraints nullable="false"/>
            </column>
            <column name="contract_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="country_code" type="VARCHAR(5)">
                <constraints nullable="false"/>
            </column>
            <column name="item_type" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="metadata" type="JSONB">
                <constraints nullable="false"/>
            </column>
            <column name="transaction_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>

            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>

        <!-- Add Index while creating the table -->
        <createIndex tableName="multiplier_payable_item_store" indexName="idx_multiplier_payable_item_store_tx_id" schemaName="payable">
            <column name="transaction_id"/>
        </createIndex>

    </changeSet>

    <!-- Create Audit Table with Index -->
    <changeSet id="20250324194000-3" author="shivam kumar">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="multiplier_payable_item_store_aud" schemaName="payable"/>
            </not>
        </preConditions>

        <createTable tableName="multiplier_payable_item_store_aud" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="multiplier_payable_item_store_aud_pkey"/>
            </column>

            <column name="rev" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="multiplier_payable_item_store_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT"/>

            <column name="amount" type="NUMERIC(19,6)">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="VARCHAR(5)">
                <constraints nullable="false"/>
            </column>
            <column name="contract_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="country_code" type="VARCHAR(5)">
                <constraints nullable="false"/>
            </column>
            <column name="item_type" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="metadata" type="JSONB">
                <constraints nullable="false"/>
            </column>
            <column name="transaction_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>

            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>

        <createIndex tableName="multiplier_payable_item_store_aud" indexName="idx_multiplier_payable_item_store_aud_tx_id" schemaName="payable">
            <column name="transaction_id"/>
        </createIndex>

    </changeSet>

    <changeSet id="20250324194000-4" author="shivam.kumar">
        <sql>
            ALTER TABLE payable.multiplier_payable_item_store
                ALTER COLUMN id SET DEFAULT nextval('payable.multiplier_payable_item_store_seq');
        </sql>
    </changeSet>

</databaseChangeLog>