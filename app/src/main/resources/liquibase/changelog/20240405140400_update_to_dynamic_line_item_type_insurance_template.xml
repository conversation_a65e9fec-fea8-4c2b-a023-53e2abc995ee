<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="khanhcao" id="20240405140400">
        <validCheckSum>ANY</validCheckSum>
        <sql>
            UPDATE payable.transaction_template set config = '{
            "identifier": "insurance-invoice-default",
            "invoiceType": "INSURANCE_INVOICE",
            "referenceTemplate": "''Annual Insurance Premium_''+getMonthInThreeLetters() + '''''''' + getYearInTwoDigits()",
            "invoiceEntityMode": "LOCAL",
            "invoiceEntitySender": "MT SGP",

            "lineItemTypes": [
            "INSURANCE_PREMIUM"
            ],

            "netSuiteTemplate": "345",
            "dataFormatters": [
            {
            "name": "com.multiplier.payable.engine.formatter.GroupingDataFormatter",
            "field": "contractId",
            "order": 0
            },
            {
            "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
            "field": "description",
            "order": 1,
            "additionalData": {
            "text": "employementType + ''_Annual Insurance Premium_New '' + insuranceType.packageName + '' Plan'' + '''''''' + baseCurrency + '' : '' + amountInBaseCurrency"
            }
            }
            ]
            }' where transaction_type = 'INSURANCE_INVOICE' and company_id is null;
        </sql>
    </changeSet>
</databaseChangeLog>