<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="nguyen-pham" id="20240806160000-1">
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already ran">
            <not>
                <columnExists schemaName="payable" tableName="invoice_source_report_aud" columnName="company_payable_id"/>
            </not>
        </preConditions>
        <addColumn schemaName="payable" tableName="invoice_source_report_aud">
            <column name="company_payable_id" type="BIGINT"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
