<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

<!--    No need VAT handling for now -->
    <changeSet id="20250514190418-1" author="chris-cao">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exist.">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM payable.transaction_template_v2
                WHERE transaction_type = 'ANNUAL_PLAN_AOR_INVOICE' AND is_default = true;
            </sqlCheck>
        </preConditions>
        <sql>
            INSERT INTO payable.transaction_template_v2 (
                id, transaction_type, is_default, description, json, created_by, created_on, updated_by, updated_on
            ) VALUES (
                         gen_random_uuid(),
                         'ANNUAL_PLAN_AOR_INVOICE',
                         true,
                         'Default Template for Annual Plan AOR Invoice',
                          '{
                              "identifier": "annual-plan-aor-default",
                              "invoiceType": "ANNUAL_PLAN_AOR_INVOICE",
                              "referenceTemplate": "''Annual Subscription Fee AOR '' + getStartMonth() + '' - '' + getEndMonth() + getPaymentTermDescription()",
                              "invoiceEntityMode": "LOCAL",
                              "invoiceEntitySender": "MT SGP",
                              "lineItemTypes": [
                                "ANNUAL_MANAGEMENT_FEE_AOR"
                              ],
                              "netSuiteTemplate": "345",
                              "dataFormatters": [
                                {
                                  "name": "com.multiplier.payable.engine.formatter.GroupingDataFormatter",
                                  "field": "countryCode,amountInBaseCurrency,billableCost,periodStartDate,periodEndDate,annualSeatPaymentTerm.interval,annualSeatPaymentTerm.timeUnit,annualSeatPaymentTerm.periodNumber,annualSeatPaymentTerm.periodCount,countryWorkStatus",
                                  "order": 0
                                },
                                {
                                  "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
                                  "field": "description",
                                  "order": 1,
                                  "additionalData": {
                                    "text": "''Annual Management Fee for '' + itemCount + '' '' + getAnnualMemberSingularOrPlural() + '' '' + getCountryCodeAndCountryWorkStatusDescription() + getAnnualBillingItemDescription() + '': '' + baseCurrency + '' '' + amountInBaseCurrency"
                                  }
                                },
                                {
                                  "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
                                  "field": "memberName",
                                  "order": 2,
                                  "additionalData": {
                                    "text": "''''"
                                  }
                                },
                                {
                                  "name": "com.multiplier.payable.engine.formatter.OrderByFieldsFormatter",
                                  "field": "countryCode,annualSeatPaymentTerm.periodNumber",
                                  "order": 3
                                }
                              ]
                        }',
                         -1,
                         now(),
                         -1,
                         now()
                     );
        </sql>
    </changeSet>
</databaseChangeLog>
