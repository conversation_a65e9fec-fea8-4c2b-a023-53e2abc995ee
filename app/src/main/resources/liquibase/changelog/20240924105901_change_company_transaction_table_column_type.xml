<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="chamil" id="20240924105901-1">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="company_transaction_template_aud" schemaName="payable"/>
            <columnExists tableName="company_transaction_template_aud" columnName="transaction_template_id" schemaName="payable"/>
            <not>
                <sqlCheck expectedResult="0">
                    SELECT COUNT(*) FROM information_schema.columns
                    WHERE table_name='company_transaction_template_aud'
                    AND column_name='transaction_template_id'
                    AND data_type='uuid'
                </sqlCheck>
            </not>
        </preConditions>
        <dropColumn tableName="company_transaction_template_aud" columnName="transaction_template_id" schemaName="payable"/>
        <addColumn tableName="company_transaction_template_aud" schemaName="payable">
            <column name="transaction_template_id" type="uuid">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>