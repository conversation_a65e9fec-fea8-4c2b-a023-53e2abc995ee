<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="pejak" id="20240309000000">
        <validCheckSum>ANY</validCheckSum>
        <sql>
            UPDATE payable.transaction_template_meta_data set config = '{
            "identifier": "insurance-invoice-default",
            "invoiceType": "INSURANCE_INVOICE",
            "referenceTemplate": "Annual Insurance Premium_''+getMonthInThreeLetters() + '''''''' + getYearInTwoDigits()",
            "invoiceEntityMode": "LOCAL",
            "invoiceEntitySender": "MT SGP",

            "lineItemTypes": [
            "INSURANCE_PREMIUM"
            ],

            "netSuiteTemplate": "345",
            "dataFormatters": [
            {
            "name": "com.multiplier.payable.engine.formatter.GroupingDataFormatter",
            "field": "contractId",
            "order": 0
            },
            {
            "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
            "field": "description",
            "order": 1,
            "additionalData": {
            "text": "''EOR_Annual Insurance premium_New health Insurance'' + '''''''' + baseCurrency + '' : '' + amountInBaseCurrency"
            }
            }
            ]
            }' where id = 2;
        </sql>
    </changeSet>
</databaseChangeLog>