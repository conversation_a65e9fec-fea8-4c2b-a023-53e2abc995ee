<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="nguyen.pham" id="20240301212200-1">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <columnExists schemaName="payable" tableName="payable_item_store" columnName="country_code"/>
            </not>
        </preConditions>
        <addColumn schemaName="payable" tableName="payable_item_store">
            <column name="country_code" type="VARCHAR(3)"/>
        </addColumn>
    </changeSet>

    <changeSet author="nguyen.pham" id="20240301212200-2">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <columnExists schemaName="payable" tableName="payable_item_store_aud" columnName="country_code"/>
            </not>
        </preConditions>
        <addColumn schemaName="payable" tableName="payable_item_store_aud">
            <column name="country_code" type="VARCHAR(3)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>