<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="20240902085100-1" author="chamil">
        <preConditions onFail="MARK_RAN">
            <indexExists schemaName="payable" tableName="transaction_template_v2" indexName="uc_transactiontemplateentity"/>
        </preConditions>

        <dropForeignKeyConstraint baseTableSchemaName="payable"
                   baseTableName="transaction_template_v2"
                   constraintName="uc_transactiontemplateentity"/>
    </changeSet>

    <changeSet id="20240902085100-2" author="chamil">
        <sql>
            CREATE UNIQUE INDEX uc_transactiontemplateentity ON payable.transaction_template_v2 (transaction_type) WHERE is_default = true;
        </sql>
    </changeSet>

    <changeSet author="chamil" id="20240902085100-3">

            <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already dropped">
                <sqlCheck expectedResult="0">
                    SELECT COUNT(*) FROM payable.transaction_template_v2;
                </sqlCheck>
            </preConditions>
        <sql>
            -- Step 1: Insert unique combinations of transaction_type and config into transaction_template_v2
            WITH unique_templates AS (SELECT distinct gen_random_uuid()                                     AS id,
                                                      transaction_type                                      AS type,
                                                      CASE WHEN company_id IS NULL THEN TRUE ELSE FALSE END AS is_default,
                                                      config,                                                              -- Keep config as TEXT
                                                      MIN(template_identifier)                              AS description -- Arbitrarily pick a description for unique config/type
                                      FROM payable.transaction_template
                                      GROUP BY transaction_type, config, company_id IS NULL)
            INSERT
            INTO payable.transaction_template_v2 (id, transaction_type , is_default, "json", description, created_by, updated_by, created_on, updated_on)
            select id,
                   type,
                   is_default,
                   "config"::json, description,
                   -1    as created_by,
                   -1    as updated_by,
                   now() as created_on,
                   now() as updated_on
            FROM unique_templates;


            -- Step 2: Populate the company_transaction_template table
            INSERT INTO payable.company_transaction_template (id, company_id, transaction_template_id, transaction_type,
                                                              created_by, updated_by, created_on, updated_on)
            select gen_random_uuid() as id,
                   pt.company_id,
                   ptv.id,
                   pt.transaction_type,
                   -1                as created_by,
                   -1                as updated_by,
                   now()             as created_on,
                   now()             as updated_on
            FROM payable.transaction_template pt
                     JOIN
                 payable.transaction_template_v2 ptv
                 ON
                     pt.transaction_type = ptv.transaction_type
                         AND pt.config::jsonb = ptv.json  -- Compare config as JSON
            WHERE
                pt.company_id IS NOT NULL;
        </sql>
    </changeSet>

</databaseChangeLog>
