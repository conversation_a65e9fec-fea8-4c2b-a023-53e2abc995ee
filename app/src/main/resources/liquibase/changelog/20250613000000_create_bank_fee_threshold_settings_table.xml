<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.9.xsd">

    <!-- Create bank_fee_threshold_settings table -->
    <changeSet author="himanshugoyal" id="**************-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="bank_fee_threshold_settings" schemaName="payable"/>
            </not>
        </preConditions>
        <comment>Create bank_fee_threshold_settings table</comment>
        <createTable tableName="bank_fee_threshold_settings" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="company_entity_context_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="bank_fee_threshold" type="JSONB">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addForeignKeyConstraint
            baseTableSchemaName="payable"
            baseTableName="bank_fee_threshold_settings"
            baseColumnNames="company_entity_context_id"
            referencedTableSchemaName="payable"
            referencedTableName="company_entity_context"
            referencedColumnNames="id"
            constraintName="fk_bank_fee_threshold_context"
        />

        <createIndex schemaName="payable"
                     tableName="bank_fee_threshold_settings"
                     indexName="idx_bank_fee_threshold_company_entity_context">
            <column name="company_entity_context_id"/>
        </createIndex>
    </changeSet>

    <!-- Create audit table for bank_fee_threshold_settings -->
    <changeSet author="himanshugoyal" id="**************-2">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="bank_fee_threshold_settings_aud" schemaName="payable"/>
            </not>
        </preConditions>
        <comment>Create bank_fee_threshold_settings_aud table</comment>
        <createTable tableName="bank_fee_threshold_settings_aud" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="company_entity_context_id" type="BIGINT"/>
            <column name="bank_fee_threshold" type="JSONB"/>
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>

    <!-- Create sequence for bank_fee_threshold_settings -->
    <changeSet id="**************-3" author="himanshugoyal" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <sequenceExists schemaName="payable" sequenceName="bank_fee_threshold_settings_seq"/>
            </not>
        </preConditions>
        <comment>Creation of payable.bank_fee_threshold_settings_seq</comment>
        <createSequence schemaName="payable"
                        sequenceName="bank_fee_threshold_settings_seq"
                        cacheSize="1"
                        cycle="false"
                        dataType="BIGINT"
                        incrementBy="1"
                        maxValue="9223372036854775807"
                        minValue="1"
                        startValue="1"
        />
    </changeSet>

    <!-- Associate sequence with table -->
    <changeSet id="**************-4" author="himanshugoyal" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped Associating bank_fee_threshold_settings_seq with id">
            <sequenceExists schemaName="payable" sequenceName="bank_fee_threshold_settings_seq"/>
            <tableExists schemaName="payable" tableName="bank_fee_threshold_settings" />
        </preConditions>
        <comment>Associate bank_fee_threshold_settings_seq with id</comment>
        <sql>
            ALTER TABLE payable.bank_fee_threshold_settings
            ALTER COLUMN id SET DEFAULT nextval('payable.bank_fee_threshold_settings_seq');
        </sql>
    </changeSet>


</databaseChangeLog>
