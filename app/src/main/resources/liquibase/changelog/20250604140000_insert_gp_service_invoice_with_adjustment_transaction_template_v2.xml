<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="2025060140000-1" author="phong-tran-multiplier">
        <validCheckSum>ANY</validCheckSum>
        <sql>
            INSERT INTO payable.transaction_template_v2 (
                id, transaction_type, is_default, description, json, created_by, created_on, updated_by, updated_on
            ) VALUES (
                         gen_random_uuid(),
                         'GP_SERVICE_INVOICE',
                         false,
                         'Template for Service Invoice with adjustment',
                        '{
                            "identifier": "gp-service-invoice-with-adjustment",
                            "invoiceType": "GP_SERVICE_INVOICE",
                            "lineItemTypes": [
                                "ONE_TIME_SETUP_FEE",
                                "ORDER_FORM_ADVANCE_ADJUSTMENT_GP_ONE_TIME_SETUP_FEE",
                                "PAYSLIP_MINIMUM_COMMITMENT",
                                "ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PAYSLIP_MINIMUM_COMMITMENT",
                                "PER_PAYSLIP_FEE",
                                "ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PER_PAYSLIP_FEE",
                                "TOTAL_PAYMENTS",
                                "ORDER_FORM_ADVANCE_ADJUSTMENT_GP_TOTAL_PAYMENTS",
                                "STAT_FILING",
                                "ORDER_FORM_ADVANCE_ADJUSTMENT_GP_STAT_FILING",
                                "YEAR_END_DOCUMENTATION",
                                "ORDER_FORM_ADVANCE_ADJUSTMENT_GP_YEAR_END_DOCUMENTATION"
                            ],
                            "dataFormatters": [
                                {
                                    "name": "com.multiplier.payable.engine.formatter.UpdateItemTypesDataFormatter",
                                    "order": 0,
                                    "additionalData": {
                                        "targetLineItemType": "ORDER_FORM_ADVANCE_ADJUSTMENT_GLOBAL_PAYROLL"
                                    },
                                    "supportedItemTypes": [
                                        "ORDER_FORM_ADVANCE_ADJUSTMENT_GP_ONE_TIME_SETUP_FEE",
                                        "ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PAYSLIP_MINIMUM_COMMITMENT",
                                        "ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PER_PAYSLIP_FEE",
                                        "ORDER_FORM_ADVANCE_ADJUSTMENT_GP_TOTAL_PAYMENTS",
                                        "ORDER_FORM_ADVANCE_ADJUSTMENT_GP_STAT_FILING",
                                        "ORDER_FORM_ADVANCE_ADJUSTMENT_GP_YEAR_END_DOCUMENTATION"
                                    ]
                                },
                                {
                                    "name": "com.multiplier.payable.engine.formatter.GroupingDataFormatter",
                                    "field": "baseCurrency,billingCurrency",
                                    "order": 1,
                                    "filter": "lineItemType == ''ORDER_FORM_ADVANCE_ADJUSTMENT_GLOBAL_PAYROLL''"
                                },
                                {
                                    "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
                                    "field": "description",
                                    "order": 2,
                                    "filter": "lineItemType == ''ORDER_FORM_ADVANCE_ADJUSTMENT_GLOBAL_PAYROLL''",
                                    "additionalData": {
                                        "text": "''Order Form Advance Adjustment Global Payroll - '' + baseCurrency + '' '' + getAmountRoundedUp2Decimals()"
                                    }
                                },
                                {
                                    "name": "com.multiplier.payable.engine.formatter.TextDataFormatter",
                                    "field": "itemCount",
                                    "order": 5,
                                    "filter": "lineItemType == ''ORDER_FORM_ADVANCE_ADJUSTMENT_GLOBAL_PAYROLL''",
                                    "additionalData": {
                                        "text": "''1''"
                                    }
                                }
                            ],
                            "netSuiteTemplate": "345",
                            "invoiceEntityMode": "LOCAL",
                            "referenceTemplate": "getStartMonth() + '' Payroll Service Fee''",
                            "invoiceEntitySender": "MT SGP"
                        }',
                         -1,
                         now(),
                         -1,
                         now()
                     );
        </sql>
    </changeSet>

</databaseChangeLog>