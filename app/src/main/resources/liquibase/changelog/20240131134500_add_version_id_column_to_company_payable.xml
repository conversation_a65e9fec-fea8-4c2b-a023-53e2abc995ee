<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="edward" id="20240131134500-1">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <columnExists schemaName="payable" tableName="company_payable" columnName="version_id"/>
            </not>
        </preConditions>
        <addColumn schemaName="payable" tableName="company_payable">
            <column name="version_id" type="VARCHAR(50)"/>
        </addColumn>
    </changeSet>

    <changeSet author="edward" id="20240131134500-2">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <columnExists schemaName="payable" tableName="company_payable_aud" columnName="version_id"/>
            </not>
        </preConditions>
        <addColumn schemaName="payable" tableName="company_payable_aud">
            <column name="version_id" type="VARCHAR(50)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>