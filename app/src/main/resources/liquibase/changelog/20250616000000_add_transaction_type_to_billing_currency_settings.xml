<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <!-- Add transaction_type column to billing_currency_settings table -->
    <changeSet author="madhupmamodia" id="20250616000000-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="billing_currency_settings" columnName="transaction_type" schemaName="payable"/>
            </not>
        </preConditions>
        <comment>Add transaction_type column to billing_currency_settings table</comment>
        <addColumn tableName="billing_currency_settings" schemaName="payable">
            <column name="transaction_type" type="TEXT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Set default value for existing records -->
    <changeSet author="madhupmamodia" id="20250616000000-2">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="billing_currency_settings" columnName="transaction_type" schemaName="payable"/>
        </preConditions>
        <comment>Set default transaction_type to GLOBAL for existing billing currency settings</comment>
        <update tableName="billing_currency_settings" schemaName="payable">
            <column name="transaction_type" value="GLOBAL"/>
            <where>transaction_type IS NULL</where>
        </update>
    </changeSet>

    <!-- Make transaction_type column not nullable -->
    <changeSet author="madhupmamodia" id="20250616000000-3">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="billing_currency_settings" columnName="transaction_type" schemaName="payable"/>
        </preConditions>
        <comment>Make transaction_type column not nullable</comment>
        <addNotNullConstraint tableName="billing_currency_settings" 
                              columnName="transaction_type" 
                              schemaName="payable"/>
    </changeSet>

    <!-- Add transaction_type column to billing_currency_settings_aud table -->
    <changeSet author="madhupmamodia" id="20250616000000-4">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="billing_currency_settings_aud" columnName="transaction_type" schemaName="payable"/>
            </not>
        </preConditions>
        <comment>Add transaction_type column to billing_currency_settings_aud table</comment>
        <addColumn tableName="billing_currency_settings_aud" schemaName="payable">
            <column name="transaction_type" type="TEXT"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
