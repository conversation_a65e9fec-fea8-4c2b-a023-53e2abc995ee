<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.9.xsd">

    <!-- Rename references column to entry_references in advance_collection_entry table -->
    <changeSet id="20250602200114-1" author="caokhanhmultiplier" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as references column already renamed">
            <and>
                <tableExists tableName="advance_collection_entry" schemaName="payable"/>
                <columnExists tableName="advance_collection_entry" schemaName="payable" columnName="references"/>
                <not>
                    <columnExists tableName="advance_collection_entry" schemaName="payable" columnName="entry_references"/>
                </not>
            </and>
        </preConditions>

        <renameColumn tableName="advance_collection_entry"
                      schemaName="payable"
                      oldColumnName="references"
                      newColumnName="entry_references"/>
    </changeSet>

    <!-- Rename status column to entry_status in advance_collection_entry table -->
    <changeSet id="20250602200114-2" author="caokhanhmultiplier" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as status column already renamed">
            <and>
                <tableExists tableName="advance_collection_entry" schemaName="payable"/>
                <columnExists tableName="advance_collection_entry" schemaName="payable" columnName="status"/>
                <not>
                    <columnExists tableName="advance_collection_entry" schemaName="payable" columnName="entry_status"/>
                </not>
            </and>
        </preConditions>

        <renameColumn tableName="advance_collection_entry"
                      schemaName="payable"
                      oldColumnName="status"
                      newColumnName="entry_status"/>
    </changeSet>

    <!-- Rename references column to entry_references in advance_collection_entry_aud table -->
    <changeSet id="20250602200114-3" author="caokhanhmultiplier" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as audit references column already renamed">
            <and>
                <tableExists tableName="advance_collection_entry_aud" schemaName="payable"/>
                <columnExists tableName="advance_collection_entry_aud" schemaName="payable" columnName="references"/>
                <not>
                    <columnExists tableName="advance_collection_entry_aud" schemaName="payable" columnName="entry_references"/>
                </not>
            </and>
        </preConditions>

        <renameColumn tableName="advance_collection_entry_aud"
                      schemaName="payable"
                      oldColumnName="references"
                      newColumnName="entry_references"/>
    </changeSet>

    <!-- Rename status column to entry_status in advance_collection_entry_aud table -->
    <changeSet id="20250602200114-4" author="caokhanhmultiplier" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as audit status column already renamed">
            <and>
                <tableExists tableName="advance_collection_entry_aud" schemaName="payable"/>
                <columnExists tableName="advance_collection_entry_aud" schemaName="payable" columnName="status"/>
                <not>
                    <columnExists tableName="advance_collection_entry_aud" schemaName="payable" columnName="entry_status"/>
                </not>
            </and>
        </preConditions>

        <renameColumn tableName="advance_collection_entry_aud"
                      schemaName="payable"
                      oldColumnName="status"
                      newColumnName="entry_status"/>
    </changeSet>

</databaseChangeLog>
