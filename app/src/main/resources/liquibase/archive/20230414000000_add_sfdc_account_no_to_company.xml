<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="HuyNguyen" id="**************-1" dbms="postgresql">
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <columnExists tableName="company" columnName="sfdc_account_no" schemaName="company"/>
            </not>
        </preConditions>
        <comment>Add sfdc_account_no column in company table</comment>

        <addColumn tableName="company" schemaName="company">
            <column name="sfdc_account_no" type="VARCHAR(100)"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
