<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="hieu.nguyen" id="20230912000000-1">
        <preConditions>
            <columnExists schemaName="payable" tableName="netsuite_event" columnName="event_type"/>
        </preConditions>
        <dropColumn schemaName="payable" tableName="netsuite_event" columnName="event_type"/>
    </changeSet>

    <changeSet author="hieu.nguyen" id="20230912000000-2">
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <columnExists schemaName="payable" tableName="netsuite_event" columnName="event_type"/>
            </not>
        </preConditions>
        <!-- Let's assume previous "event_type"s are UPDATE.
             That reason is we need to have "defaultValue" to create the NULLABLE constraint.
             On top of that, there's no way to set event_type for previous events.
        -->
        <addColumn schemaName="payable" tableName="netsuite_event">
            <column name="event_type"
                    type="VARCHAR(50)"
                    defaultValue="UPDATE">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
