<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="himanshu" id="20230815001001-1" dbms="postgresql">

        <preConditions>
            <not>
                <columnExists tableName="credit_note" columnName="company_payable_id" schemaName="payable"/>
            </not>
        </preConditions>

        <addColumn tableName="credit_note" schemaName="payable">
            <column name="company_payable_id" type="BIGINT" />
        </addColumn>

    </changeSet>

    <changeSet author="himanshugoyal" id="20230815001001-2" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="credit_note_aud" columnName="company_payable_id" schemaName="payable"/>
            </not>
        </preConditions>

        <addColumn tableName="credit_note_aud" schemaName="payable">
            <column name="company_payable_id" type="BIGINT" />
        </addColumn>
    </changeSet>

</databaseChangeLog>
