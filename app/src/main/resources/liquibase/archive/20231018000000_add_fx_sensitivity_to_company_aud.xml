<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="nrasulic" id="20231018000000-1" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already ran">
            <not>
                <columnExists tableName="company_aud" schemaName="company" columnName="fx_sensitivity"/>
            </not>
        </preConditions>
        <addColumn tableName="company_aud" schemaName="company">
            <column name="fx_sensitivity" type="VARCHAR(25)" defaultValue="LOW"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>