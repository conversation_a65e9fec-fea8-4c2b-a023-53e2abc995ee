<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="himanshugoyal (generated)" id="20230428200200-1">
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already dropped">
            <not>
                <columnExists schemaName="payable" tableName="company_payable_generation_history" columnName="company_payable_id"/>
            </not>
        </preConditions>
        <addColumn schemaName="payable" tableName="company_payable_generation_history">
            <column name="company_payable_id" type="bigint"/>
        </addColumn>
    </changeSet>


</databaseChangeLog>