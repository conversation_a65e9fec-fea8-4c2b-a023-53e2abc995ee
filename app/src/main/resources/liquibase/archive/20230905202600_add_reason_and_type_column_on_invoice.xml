<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="christian.robles" id="20230905202600-1" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="invoice" columnName="reason" schemaName="payable"/>
            </not>
        </preConditions>
        <addColumn tableName="invoice" schemaName="payable">
            <column name="reason" type="varchar(50)" />
        </addColumn>
    </changeSet>

    <changeSet author="christian.robles" id="20230905202600-2" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="invoice" columnName="type" schemaName="payable"/>
            </not>
        </preConditions>
        <addColumn tableName="invoice" schemaName="payable">
            <column name="type" type="varchar(50)" />
        </addColumn>
    </changeSet>

    <changeSet author="christian.robles" id="20230905202600-3" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="invoice_aud" columnName="reason" schemaName="payable"/>
            </not>
        </preConditions>
        <addColumn tableName="invoice_aud" schemaName="payable">
            <column name="reason" type="varchar(50)" />
        </addColumn>
    </changeSet>

    <changeSet author="christian.robles" id="20230905202600-4" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="invoice_aud" columnName="type" schemaName="payable"/>
            </not>
        </preConditions>
        <addColumn tableName="invoice_aud" schemaName="payable">
            <column name="type" type="varchar(50)" />
        </addColumn>
    </changeSet>

</databaseChangeLog>
