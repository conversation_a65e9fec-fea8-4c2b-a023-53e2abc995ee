<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">


    <changeSet author="gayan_nettasinghe" id="20230530093600-1">
        <validCheckSum>ANY</validCheckSum>
        <createTable tableName="anomaly_detector_report" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="anomaly_detector_report_pkey"/>
            </column>
            <column name="company_payable_id" type="BIGINT"/>
            <column name="derived_result" type="VARCHAR(50)"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>

        <createSequence dataType="BIGINT"
                        incrementBy="1"
                        schemaName="payable"
                        sequenceName="anomaly_detector_report_seq"
                        startValue="1"/>

        <createIndex tableName="anomaly_detector_report"  schemaName="payable" indexName="idx_company_payable_id">
            <column name="company_payable_id"/>
        </createIndex>

    </changeSet>

    <changeSet author="gayan_nettasinghe" id="20230530093600-2">
        <validCheckSum>ANY</validCheckSum>
        <createTable tableName="anomaly_detector_result" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="anomaly_detector_result_pkey"/>
            </column>
            <column name="report_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(50)"/>
            <column name="result" type="VARCHAR(50)"/>
            <column name="anomaly_result_message" type="JSONB"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>

        <createSequence dataType="BIGINT"
                        incrementBy="1"
                        sequenceName="anomaly_detector_result_seq"
                        schemaName="payable"
                        startValue="1"/>

        <createIndex tableName="anomaly_detector_result"  schemaName="payable" indexName="idx_anomaly_report_id">
            <column name="report_id"/>
        </createIndex>

    </changeSet>

    <changeSet author="gayan_nettasinghe" id="20230530093600-3">
        <validCheckSum>ANY</validCheckSum>

        <createTable tableName="anomaly_detector_report_aud" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="anomaly_detector_report_aud_pkey"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="anomaly_detector_report_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="company_payable_id" type="BIGINT"/>
            <column name="derived_result" type="VARCHAR(50)"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>

        <createTable tableName="anomaly_detector_result_aud" schemaName="payable">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="anomaly_detector_result_aud_pkey"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="anomaly_detector_result_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="report_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(50)"/>
            <column name="result" type="VARCHAR(50)"/>
            <column name="anomaly_result_message" type="JSONB"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>

    </changeSet>

    <changeSet author="gayan_nettasinghe" id="20230530093600-4">
        <validCheckSum>ANY</validCheckSum>
        <addForeignKeyConstraint baseTableName="anomaly_detector_result"
                                 baseColumnNames="report_id"
                                 baseTableSchemaName="payable"
                                 constraintName="fk_report_id"
                                 referencedTableName="anomaly_detector_report"
                                 referencedTableSchemaName="payable"
                                 referencedColumnNames="id"/>
    </changeSet>

    <changeSet author="gayan_nettasinghe" id="20230530093600-5">
        <preConditions onFail="MARK_RAN"><tableExists schemaName="company" tableName="anomaly_detector_report"/></preConditions>
        <dropTable cascadeConstraints="true"
                   schemaName="company"
                   tableName="anomaly_detector_report"/>
        <dropTable cascadeConstraints="true"
                   schemaName="company"
                   tableName="anomaly_detector_result"/>
        <dropTable cascadeConstraints="true"
                   schemaName="company"
                   tableName="anomaly_detector_report_aud"/>
        <dropTable cascadeConstraints="true"
                   schemaName="company"
                   tableName="anomaly_detector_result_aud"/>
    </changeSet>
</databaseChangeLog>