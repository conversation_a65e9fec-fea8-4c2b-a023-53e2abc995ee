<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="datnguyen" id="20231006130025-1">
        <sql>
            UPDATE payable.pricing
            SET employee_type_global_pricing = employee_type_global_pricing|| '[{"globalPrice": 400.0, "employeeType": "CONTRACTOR"}]'::jsonb
            WHERE employee_type_global_pricing is not null;
        </sql>
    </changeSet>
</databaseChangeLog>