package com.multiplier.core.exception;

import com.netflix.graphql.dgs.exceptions.DefaultDataFetcherExceptionHandler;
import graphql.execution.DataFetcherExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
class DataFetcherExceptionHandlerConfiguration {

    @Bean
    public DataFetcherExceptionHandler defaultDataFetcherExceptionHandler() {
        return new DefaultDataFetcherExceptionHandler();
    }

}