package com.multiplier.core.config;


import com.multiplier.common.transport.user.CurrentUser;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
@RequiredArgsConstructor
public class AuditConfig {

    private final CurrentUser currentUser;

    @Bean
    AuditorAware<Long> auditorProvider() {
        return new AuditorAwareImpl(currentUser);
    }
}
