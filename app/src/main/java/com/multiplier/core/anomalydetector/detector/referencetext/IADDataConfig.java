package com.multiplier.core.anomalydetector.detector.referencetext;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.multiplier.payable.types.CompanyPayableType;
import lombok.*;
import lombok.extern.jackson.Jacksonized;
import org.apache.logging.log4j.util.Strings;

@Builder(toBuilder = true)
@Getter
@Jacksonized
@EqualsAndHashCode
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
final class IADDataConfig {
    @JsonAlias("first_invoice_text")
    private final String firstInvoiceText;

    @JsonAlias("second_invoice_text")
    private final String secondInvoiceText;

    @JsonAlias("invoice_text_date_format")
    private final String dateFormat;

    public String getInvoiceText(CompanyPayableType type){
        if (type == null) {
            return Strings.EMPTY;
        }

        switch (type) {
            case FIRST_INVOICE: return firstInvoiceText;
            case SECOND_INVOICE: return secondInvoiceText;
            default:
                return Strings.EMPTY;
        }
    }
}
