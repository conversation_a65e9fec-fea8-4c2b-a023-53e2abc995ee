package com.multiplier.core.anomalydetector.detector.referencetext;

import com.multiplier.core.anomalydetector.detector.base.Condition;
import com.multiplier.core.anomalydetector.detector.base.Result;
import lombok.*;
import lombok.extern.jackson.Jacksonized;

@Builder(toBuilder = true)
@Getter
@Jacksonized
@EqualsAndHashCode
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public final class IADReferenceTextConfig {
    private final IADDataConfig config;
    private final Result result;
    private final Condition condition;
}

