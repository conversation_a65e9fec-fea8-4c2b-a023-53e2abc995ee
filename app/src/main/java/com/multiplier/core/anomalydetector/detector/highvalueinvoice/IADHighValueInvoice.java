package com.multiplier.core.anomalydetector.detector.highvalueinvoice;

import com.multiplier.core.anomalydetector.InvoiceAnomalyDetector;
import com.multiplier.core.anomalydetector.InvoiceAnomalyDetectorConfigParser;
import com.multiplier.core.anomalydetector.model.AnomalyResultType;
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest;
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType;
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyResult;
import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.repository.model.JpaCompanyPayable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class IADHighValueInvoice extends InvoiceAnomalyDetector {

    protected IADHighValueInvoice(final InvoiceAnomalyDetectorConfigParser parser) {
        super(parser);
    }

    @Override
    public InvoiceAnomalyResult detect(InvoiceAnomalyDetectorRequest request) {
        JpaCompanyPayable companyPayable = request.getPayable();
        InvoiceDTO invoiceDTO = request.getInvoiceDTO();
        if (companyPayable == null || invoiceDTO == null) {
            log.info("[AnomalyDetectionProcess] Company payable or invoiceDTO is null, hence cannot proceed");
            return returnSuccessOnNotRun(request, getIADInputNullMessage());
        }

        log.info("[AnomalyDetectionProcess] Running IADHighValueInvoice for companyPayableId={}", companyPayable.getId());
        IADHighValueInvoiceConfig config = getConfigForIAD(type(), IADHighValueInvoiceConfig.class);

        if (config == null || !config.getCondition().isActive()) {
            String msg = config != null ? "IADHighValueInvoice IAD is inactive" : "Cannot find config for High value invoice IAD";
            log.info("[AnomalyDetectionProcess] " + msg + " for companyPayableId={}", companyPayable.getId());
            return InvoiceAnomalyResult.builder()
                    .anomalyResultMessage(List.of(msg + " for companyPayableId=" + companyPayable.getId()))
                    .payable(companyPayable)
                    .result(AnomalyResultType.WARN)
                    .type(type())
                    .build();
        }

//TODO:        if (!config.getConditions().isInvoiceTypeSupported(companyPayable.getType())) {
//            log.info("[AnomalyDetectionProcess] InvoiceType={} is not supported in IAD High value invoice for companyPayableID={}. Only supported types={}", companyPayable.getType(), companyPayable.getId(), config.getConditions().getSupportedInvoiceTypes());
//            return InvoiceAnomalyResult.builder()
//                    .anomalyResultMessage(new ArrayList<>())
//                    .payable(companyPayable)
//                    .result(AnomalyResultType.SUCCESS)
//                    .type(type())
//                    .build();
//        }

        AnomalyResultType result = config.getConfig().isHighValueInvoice(companyPayable.getTotalAmount(), companyPayable.getCurrency()) ? config.getResult().getType() : AnomalyResultType.SUCCESS;
        List<String> message = (result == AnomalyResultType.SUCCESS) ?
            List.of("High value invoice validation passed - amount is within acceptable thresholds") :
            List.of(config.getResult().getMessage());

        return InvoiceAnomalyResult.builder()
                .anomalyResultMessage(message)
                .payable(companyPayable)
                .result(result)
                .type(type())
                .build();
    }

    @Override
    public InvoiceAnomalyDetectorType type() {
        return InvoiceAnomalyDetectorType.HIGH_VALUE_INVOICE;
    }
}
