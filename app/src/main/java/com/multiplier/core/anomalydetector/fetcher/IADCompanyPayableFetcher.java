package com.multiplier.core.anomalydetector.fetcher;

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository;
import com.multiplier.core.payable.repository.model.JpaCompanyPayable;
import com.multiplier.payable.types.PayableStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/*
Temporarily using this class
to fetch required data without
affecting performance of invoice generation.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IADCompanyPayableFetcher {

    private final JpaCompanyPayableRepository companyPayableRepository;

    private static final Set<PayableStatus> INACTIVE_PAYABLE_STATUSES = Set.of(PayableStatus.DELETED, PayableStatus.VOIDED);

    public List<JpaCompanyPayable> getInvoicesForReqTime(JpaCompanyPayable companyPayable, boolean isPreviousMonth) {
        Long companyID = companyPayable.getCompanyId();
        var effectiveDate = getEffectiveDate(companyPayable, isPreviousMonth);

        log.info("Fetching all {} invoices for companyID={}, year={} and month={}", isPreviousMonth ? "previous month" : "payableMonth", companyID, effectiveDate.getYear(), effectiveDate.getMonthValue());
        return companyPayableRepository.findByCompanyIdAndYearAndMonth(companyID, effectiveDate.getYear(), effectiveDate.getMonthValue())
                .stream()
                .filter(i -> !INACTIVE_PAYABLE_STATUSES.contains(i.getStatus()))
                .toList();
    }

    private LocalDate getEffectiveDate(JpaCompanyPayable companyPayable, boolean isPreviousMonth) {
        if (isPreviousMonth) {
            return LocalDate.of(companyPayable.getYear(), companyPayable.getMonth(), 1).minusMonths(1);
        }

        return LocalDate.of(companyPayable.getYear(), companyPayable.getMonth(), 1);
    }
}