package com.multiplier.core.anomalydetector.detector.highvalueinvoice;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.multiplier.core.anomalydetector.detector.base.Condition;
import com.multiplier.core.anomalydetector.detector.base.Result;
import com.multiplier.payable.types.CurrencyCode;
import lombok.*;
import lombok.extern.jackson.Jacksonized;

import java.util.HashMap;
import java.util.Map;

@Builder(toBuilder = true)
@Getter
@Jacksonized
@EqualsAndHashCode
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public final class IADHighValueInvoiceConfig {
    private final IADDataConfig config;
    private final Result result;
    private final Condition condition;
}

@Builder(toBuilder = true)
@Getter
@Jacksonized
@EqualsAndHashCode
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
final class IADDataConfig {

    @JsonAlias("default_max_net_amount")
    private final Long defaultMaxNetAmount;

    @JsonAlias("currency_based_max_net_amounts")
    @Builder.Default
    private final Map<CurrencyCode, Long> currencyBasedMaxNetAmounts = new HashMap<>();

    public boolean isHighValueInvoice(Double total, CurrencyCode currency) {
        return total >= currencyBasedMaxNetAmounts.getOrDefault(currency, defaultMaxNetAmount);
    }
}
