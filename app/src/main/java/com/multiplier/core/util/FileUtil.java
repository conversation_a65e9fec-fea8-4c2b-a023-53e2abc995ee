package com.multiplier.core.util;

import org.jetbrains.annotations.Nullable;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.FileAttribute;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.time.LocalDateTime;
import java.util.Set;

public class FileUtil {
    private interface SUFFIX {
        String CSV = ".csv";
        String XLSX = ".xlsx";
    }

    /**
     * @return a temp csv file without posix
     * @throws IOException
     */
    public static Path createCsvTempFile() throws IOException {
        return createTempFile(SUFFIX.CSV, false);
    }

    /**
     * @return a temp csv file with posix
     * @throws IOException
     */
    public static Path createCsvTempFileWithPosix() throws IOException {
        return createTempFile(SUFFIX.CSV, true);
    }

    /**
     * @return a temp xlsx file without posix
     * @throws IOException
     */
    public static Path createXlsxTempFile() throws IOException {
        return createTempFile(SUFFIX.XLSX, false);
    }

    /**
     * @return true if the platform/os supports posix
     */
    public static boolean isPosixSupported() {
        return FileSystems.getDefault().supportedFileAttributeViews().contains("posix");
    }

    private static Path createTempFile(String suffix, boolean isWithPosix) throws IOException {
        if (isWithPosix && FileUtil.isPosixSupported()) {
            FileAttribute<Set<PosixFilePermission>> attr = PosixFilePermissions.asFileAttribute(PosixFilePermissions.fromString("rw-------"));
            return Files.createTempFile(LocalDateTime.now().toString().replace(":", "-"), suffix, attr);
        }
        return Files.createTempFile(LocalDateTime.now().toString().replace(":", "-"), suffix);
    }

    @Nullable
    public static Long sizeOrNull(@Nullable MultipartFile file) {
        return file == null
                ? null
                : file.getSize();
    }
}
