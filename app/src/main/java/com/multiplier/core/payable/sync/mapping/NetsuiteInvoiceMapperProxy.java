package com.multiplier.core.payable.sync.mapping;

import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

@Component("netsuiteInvoiceMapperProxy")
public class NetsuiteInvoiceMapperProxy {

    private final LineItemMappingAdapter lineItemMappingAdapter;
    private InvoiceDtoFromNetsuiteInvoiceMapper delegate;

    @Autowired
    public NetsuiteInvoiceMapperProxy(LineItemMappingAdapter lineItemMappingAdapter) {
        this.lineItemMappingAdapter = lineItemMappingAdapter;
    }

    @PostConstruct
    public void init() {
        this.delegate = Mappers.getMapper(InvoiceDtoFromNetsuiteInvoiceMapper.class);
        // Inject the dependency into the delegate
        this.delegate.lineItemMappingAdapter = this.lineItemMappingAdapter;
    }

    public InvoiceDTO map(NetsuiteInvoice netsuiteInvoice, String externalId) {
        return delegate.map(netsuiteInvoice, externalId);
    }
}
