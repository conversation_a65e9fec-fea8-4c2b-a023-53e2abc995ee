package com.multiplier.core.payable.sync.processor.creditnote;

import com.multiplier.core.payable.companypayable.database.CompanyPayableDto;
import com.multiplier.core.payable.companypayable.database.CompanyPayableDtoMapper;
import com.multiplier.core.payable.creditnote.database.CreditNoteDto;
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository;
import com.multiplier.payable.kafka.schema.EventType;
import com.multiplier.payable.service.CompanyPayableBroadcaster;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

@Component
@RequiredArgsConstructor
@Slf4j
class CreditNoteCompanyPayableUpdater {

    private final JpaCompanyPayableRepository jpaCompanyPayableRepository;
    private final CreditNoteDtoToJpaCompanyPayableMergeMapper creditNoteDtoToJpaCompanyPayableMergeMapper;
    private final CompanyPayableDtoMapper companyPayableDtoMapper;
    private final CompanyPayableBroadcaster companyPayableBroadcaster;

    @Transactional
    public CompanyPayableDto update(CreditNoteDto externalCreditNote) {
        Objects.requireNonNull(externalCreditNote.getCompanyPayableId());
        log.info("Update company payable with ID {} for credit note with externalId {}",
                externalCreditNote.getCompanyPayableId(),
                externalCreditNote.getExternalId());

        var currentCompanyPayable = jpaCompanyPayableRepository.findById(externalCreditNote.getCompanyPayableId())
                .orElseThrow(() -> new IllegalArgumentException("Company payable not found"));

        var companyPayableToUpdate = creditNoteDtoToJpaCompanyPayableMergeMapper.merge(externalCreditNote, currentCompanyPayable);

        var updatedCompanyPayable = jpaCompanyPayableRepository.save(companyPayableToUpdate);
        companyPayableBroadcaster.publishPayableUpdateEvent(updatedCompanyPayable.getId(), EventType.STATUS_UPDATED);

        return companyPayableDtoMapper.map(updatedCompanyPayable);
    }
}
