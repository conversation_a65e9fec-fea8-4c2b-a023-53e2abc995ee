package com.multiplier.core.payable.report.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

@Getter
@AllArgsConstructor
public class DefaultIsrExcelWorkbook implements IsrExcelWorkbook {
    private final Workbook workbook;
    public static DefaultIsrExcelWorkbook of() {
        return new DefaultIsrExcelWorkbook(new XSSFWorkbook());
    }
    @Override
    public void addSheet(Sheet sourceSheet) throws Exception {
        IsrExcelWorksheet isrExcelWorksheet = DefaultIsrExcelWorksheet.of(workbook, sourceSheet.getSheetName());
        isrExcelWorksheet.copy(workbook, sourceSheet);
    }

    @Override
    public ByteArrayOutputStream toOutputStream() throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        workbook.write(byteArrayOutputStream);
        return byteArrayOutputStream;
    }

    @Override
    public void close() throws Exception {
        if (workbook != null) {
            workbook.close();
        }
    }
}
