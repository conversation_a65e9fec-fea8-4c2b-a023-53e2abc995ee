package com.multiplier.core.payable.graphql.dataloader;

import com.multiplier.core.util.ExecutorUtil;
import com.multiplier.core.payable.service.PayableService;
import com.multiplier.payable.types.CompanyPayable;
import com.netflix.graphql.dgs.DgsDataLoader;
import lombok.RequiredArgsConstructor;
import lombok.val;
import org.dataloader.MappedBatchLoader;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@DgsDataLoader(name = "companyPayableById", maxBatchSize = 2000)
public class CompanyPayableByIdDataLoader implements MappedBatchLoader<Long, CompanyPayable> {
    private final PayableService payableService;

    @Override
    public CompletionStage<Map<Long, CompanyPayable>> load(final Set<Long> payableIds) {
        val executor = ExecutorUtil.getNewSecurityContextExecutor();
        return CompletableFuture.supplyAsync(() -> payableService.getCompanyPayablesByIds(payableIds)
                .stream()
                .collect(Collectors.toMap(
                        CompanyPayable::getId,
                        Function.identity()
                )), executor);
    }

}