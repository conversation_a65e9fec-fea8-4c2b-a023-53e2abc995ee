package com.multiplier.core.payable.sync.webhook;

import com.multiplier.core.payable.adapters.api.ThirdPartyServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * A class that verifies Netsuite content using symmetric-key signature.
 */
@Component
@Slf4j
class NetsuiteContentVerifier {

    private final String preSharedKey;

    public NetsuiteContentVerifier(@Value("${netsuite.webhook.preSharedKey}") String preSharedKey) {
        this.preSharedKey = preSharedKey;
    }

    public String getVerifiedContent(String rawBody) {
        try {
            int index = rawBody.lastIndexOf("||=");
            String content;
            String signature;

            if (index != -1) {
                signature = rawBody.substring(index + 3);
                content = rawBody.substring(0, index);
            } else {
                log.error("The content and the signature separator '||=' is not found. ");
                return null;
            }

            Mac sha256Hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(preSharedKey.getBytes(), "HmacSHA256");
            sha256Hmac.init(secretKey);
            byte[] hmacBytes = sha256Hmac.doFinal(content.getBytes());
            String hash = Base64.getEncoder().encodeToString(hmacBytes);

            if (hash.equals(signature)) {
                return content;
            }
            log.error("The signature verification failed. '{}'.", signature);
            return null;
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new ThirdPartyServiceException("Failed to initialize sha256-HMAC algorithm.", e);
        }
    }

}
