package com.multiplier.core.payable.service;

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract;
import com.multiplier.core.payable.adapters.CountryServiceAdapter;
import com.multiplier.core.payable.adapters.DocgenServiceAdapter;
import com.multiplier.core.payable.company.Company;
import com.multiplier.core.payable.company.adapter.CompanyServiceAdapter;
import com.multiplier.core.payable.docgen.model.DocumentRecipient;
import com.multiplier.core.payable.docgen.model.DocumentResponse;
import com.multiplier.core.payable.docgen.model.DocumentShareRequest;
import com.multiplier.core.payable.docgen.model.DocumentShareResponse;
import com.multiplier.country.schema.Country;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.message.BasicNameValuePair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Optional;

import static java.text.MessageFormat.format;

@Service
@Slf4j
@RequiredArgsConstructor
public class ContractDocumentService {

    private static final String AMRIT_NAME = "amrit";
    private static final String MULTIPLIER_EMAIL = "@usemultiplier.com";
    private static final String NAME = "name";
    private static final String COUNTRY = "country";
    private static final String LOGO = "logo";
    private static final String EMBEDDED_DOC_LINK_TEMPLATE = "{0}/documents/session/{1}";

    private final CompanyServiceAdapter companyServiceAdapter;
    private final DocgenServiceAdapter docgenServiceAdapter;
    private final CountryServiceAdapter countryServiceAdapter;

    @Value("${platform.frontend.baseurl}")
    private String baseUrl;

    String fetchPandaDocLink(Long documentId, Contract contract) {
        if (documentId == null) {
            return Strings.EMPTY;
        }

        var document = docgenServiceAdapter.getDocument(documentId);

        Optional<DocumentRecipient> documentRecipientDTO = document.getRecipients() == null
                ? Optional.empty()
                : document.getRecipients().stream()
                .filter(this::isForAmrit)
                .findAny();

        String pandadocShareableLink = Optional.of(document)
                .map(DocumentResponse::getExternalURL)
                .map(URL::toString)
                .orElse(Strings.EMPTY);

        if (documentRecipientDTO.isPresent() && contract != null) {
            var company = companyServiceAdapter.getCompanyById(contract.getCompanyId());
            var queryParams = buildQueryParamsForEmbeddedDocLink(company, getCompanyLogoLink(company));
            pandadocShareableLink = getPandaDocShareableLink(documentId.toString(), documentRecipientDTO.get().getEmail(), queryParams, pandadocShareableLink);
        }

        return pandadocShareableLink;
    }

    private boolean isForAmrit(DocumentRecipient documentRecipientDTO) {
        return (documentRecipientDTO.getEmail() != null && documentRecipientDTO.getEmail().toLowerCase().contains(AMRIT_NAME)
                || documentRecipientDTO.getFirstName() != null && documentRecipientDTO.getFirstName().toLowerCase().contains(AMRIT_NAME))
                && documentRecipientDTO.getEmail() != null && documentRecipientDTO.getEmail().endsWith(MULTIPLIER_EMAIL);
    }

    private String getPandaDocShareableLink(String documentId, String email, final String queryParams, @NonNull final String viewPandaDocLink) {
        DocumentShareResponse documentShareResponse = null;
        try {
            documentShareResponse = docgenServiceAdapter.shareDocument(Long.valueOf(documentId), new DocumentShareRequest().email(email));
        } catch (Exception e) {
            log.error("Error while sharing document with id = {}", documentId, e);
        }
        return Optional.ofNullable(documentShareResponse)
                .map(DocumentShareResponse::shareId)
                .map(this::mapToEmbeddedDocLink)
                .map(link -> link + "?" + queryParams)
                .orElse(viewPandaDocLink);
    }

    private String mapToEmbeddedDocLink(String linkSessionId) {
        return Optional.ofNullable(linkSessionId)
                .map(m -> format(EMBEDDED_DOC_LINK_TEMPLATE, baseUrl, m))
                .orElse(null);
    }

    private String buildQueryParamsForEmbeddedDocLink(@NonNull final Company company, final String companyLogoLink) {
        var queryParams = new ArrayList<NameValuePair>(3);

        Optional.ofNullable(company.getDisplayName())
                .map(name -> new BasicNameValuePair(NAME, name))
                .ifPresent(queryParams::add);

        Optional.ofNullable(company.getPrimaryEntity())
                .map(entity -> entity.getAddress())
                .map(address -> address.getCountry())
                .map(countryCode -> Country.GrpcCountryCode.valueOf(countryCode.name()))
                .map(countryServiceAdapter::getCountryNameByCode)
                .map(countryName -> new BasicNameValuePair(COUNTRY, countryName))
                .ifPresent(queryParams::add);

        Optional.ofNullable(companyLogoLink)
                .filter(link -> !link.isBlank())
                .map(link -> new BasicNameValuePair(LOGO, link))
                .ifPresent(queryParams::add);
        return URLEncodedUtils.format(queryParams, Charset.defaultCharset());
    }

    private String getCompanyLogoLink(@NonNull Company company) {
        return Optional.ofNullable(company.getCompanyLogoId())
                .map(docgenServiceAdapter::getDocument)
                .map(DocumentResponse::getViewURL)
                .map(Object::toString)
                .orElse(StringUtils.EMPTY);
    }
}
