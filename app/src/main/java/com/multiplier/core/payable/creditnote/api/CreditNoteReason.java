package com.multiplier.core.payable.creditnote.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@RequiredArgsConstructor
public enum CreditNoteReason {

    @JsonProperty("invoiceVoided")
    INVOICE_VOIDED("invoiceVoided", "Invoice Voided", "1"),

    @JsonProperty("invoiceCorrection")
    INVOICE_CORRECTION("invoiceCorrection", "Invoice Correction", "2"),

    @JsonProperty("discounts")
    DISCOUNTS("discounts", "Discounts", "3"),

    @JsonProperty("depositRefunds")
    DEPOSIT_REFUND("depositRefunds", "Deposit Refunds", "4"),

    @JsonProperty("insuranceCancellation")
    INSURANCE_CANCELLATION("insuranceCancellation", "Insurance Cancellation", "5"),

    @JsonProperty("secondInvoice")
    SECOND_INVOICE("secondInvoice", "Second Invoice", "6"),

    @JsonProperty("writeOff")
    WRITE_OFF("writeOff", "Write Off", "7"),

    @JsonProperty("expensesRefunds")
    EXPENSES_REFUNDS("expensesRefunds", "Expenses Refunds", "8"),

    // the name on NS has a white space after the word
    @JsonProperty("freelancer")
    FREELANCER("freelancer", "Freelancer ", "9"),

    @JsonProperty("doublePayment")
    DOUBLE_PAYMENT("doublePayment", "Double Payment", "10");

    private final String keyword;
    private final String name;
    private final String externalId;
    private static final Map<String, CreditNoteReason> REASONS_BY_EXTERNAL_ID =
            Arrays.stream(CreditNoteReason.values())
                    .collect(Collectors.toMap(CreditNoteReason::getExternalId, Function.identity()));
    static final String NOT_FOUND_PATTERN = "Credit note reason for ID %s not found";

    public static CreditNoteReason getReasonForExternalId(String externalId) {
        if (!REASONS_BY_EXTERNAL_ID.containsKey(externalId)) {
            var errorMessage = NOT_FOUND_PATTERN.formatted(externalId);
            throw new IllegalArgumentException(errorMessage);
        }
        return REASONS_BY_EXTERNAL_ID.get(externalId);
    }
}
