package com.multiplier.core.payable.adapters.netsuite.client;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.client.ReactiveFeignException;
import reactivefeign.client.ReactiveHttpRequestInterceptor;
import reactivefeign.client.ReactiveHttpRequestInterceptors;
import reactivefeign.client.statushandler.ReactiveStatusHandler;
import reactivefeign.client.statushandler.ReactiveStatusHandlers;
import reactivefeign.retry.BasicReactiveRetryPolicy;
import reactivefeign.retry.ReactiveRetryPolicy;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @deprecated since cu-86cugqfk7
 * Feign Reactive doesn't support Spring Boot 3.2.
 * See https://github.com/PlaytikaOSS/feign-reactive/issues/651
 * TODO https://app.clickup.com/t/86cumbkvq
 *  or use Spring's WebClient (because it's more actively and well maintained)
 */
@Deprecated
/*
@FeignClient(value = "netsuite-restlet-async-api",
        url = "${reactive.feign.client.config.netsuite-restlet-async-api.url}",
        configuration = NetsuiteAsyncRestletClient.NetsuiteReactiveClientConfiguration.class
)
*/
public interface NetsuiteAsyncRestletClient {

    @RequiredArgsConstructor
    @Configuration
    class NetsuiteReactiveClientConfiguration {

        private final NetsuiteTokenProvider netsuiteTokenProvider;

        @Bean
        public ReactiveHttpRequestInterceptor netsuiteOAuth2Interceptor() {
            return ReactiveHttpRequestInterceptors.from((reactiveHttpRequest -> {
                reactiveHttpRequest.headers().put("Authorization", List.of("Bearer " + netsuiteTokenProvider.getAccessToken()));
                return reactiveHttpRequest;
            }));
        }

        @Bean
        public ReactiveStatusHandler netsuiteErrorDecoder() {
            return ReactiveStatusHandlers.throwOnStatus(
                    status -> status == HttpStatus.FORBIDDEN.value(),
                    (methodKey, response) -> {
                        netsuiteTokenProvider.refreshAccessToken();
                        return new ReactiveFeignException(new Exception("The auth token for NS failed"), response.request());
                    });
        }

        @Bean
        public ReactiveRetryPolicy reactiveRetryPolicy() {
            return BasicReactiveRetryPolicy.retryWithBackoff(1, 2000);
        }

    }

    @GetMapping("/app/site/hosting/restlet.nl?script=${netsuite.forward-sync.restlet.script-id}&deploy=1")
    Mono<Void> syncInvoicesForCustomerIds(@RequestParam("customerIds") List<String> customerIds);
}
