package com.multiplier.core.payable.pricing;

import com.multiplier.core.payable.repository.model.JpaEmployeePricing;
import com.multiplier.payable.types.ContractType;
import com.multiplier.payable.types.CountryCode;

public record EmployeePricingKey(
        CountryCode countryCode,
        ContractType contractType
) {
    public static EmployeePricingKey fromJpa(JpaEmployeePricing jpaEmployeePricing) {
        return new EmployeePricingKey(jpaEmployeePricing.getCountry(), jpaEmployeePricing.getEmployeeType());
    }
}
