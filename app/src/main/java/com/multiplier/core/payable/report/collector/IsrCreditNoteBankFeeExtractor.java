package com.multiplier.core.payable.report.collector;

import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.creditnote.database.CreditNoteDto;
import com.multiplier.core.payable.creditnote.database.CreditNoteItemDto;
import com.multiplier.core.payable.service.dataholder.SourceReportData;
import com.multiplier.payable.types.CreditNoteReason;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.Predicate;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class IsrCreditNoteBankFeeExtractor {

    private final Predicate<CreditNoteItemDto> isLineItemProcessable = lineItem -> 
            lineItem.getItemType() == LineItemType.BANK_FEE;

    private final CreditNoteBankFeeExtractor creditNoteBankFeeExtractor;

    public List<SourceReportData> extractingBankFee(Collection<CreditNoteDto> creditNoteDtos) {
        return creditNoteDtos.stream()
                .filter(creditNoteDto -> creditNoteDto.getReason() == CreditNoteReason.SECOND_INVOICE)
                .map(this::getBankFeeReportLine)
                .filter(Objects::nonNull)
                .toList();
    }

    private SourceReportData getBankFeeReportLine(CreditNoteDto creditNoteDto) {
        return ListUtils.emptyIfNull(creditNoteDto.getItems())
                .stream()
                .filter(isLineItemProcessable::evaluate)
                .findFirst()
                .map(creditNoteItemDto -> getSourceReportData(creditNoteDto, creditNoteItemDto))
                .orElse(null);
    }

    private SourceReportData getSourceReportData(CreditNoteDto creditNoteDto, CreditNoteItemDto creditNoteItemDto) {
        var bankFee = creditNoteBankFeeExtractor.getAmount(creditNoteItemDto);
        return SourceReportData.builder()
                .companyId(creditNoteDto.getCompanyId())
                .bankFee(Math.abs(bankFee))
                .currencyCode(creditNoteBankFeeExtractor.getBaseCurrencyCode(creditNoteItemDto))
                .payableId(creditNoteDto.getCompanyPayableId())
                .build();
    }
}
