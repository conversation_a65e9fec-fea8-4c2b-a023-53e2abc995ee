package com.multiplier.core.payable.invoice.database;

import com.multiplier.core.payable.adapters.api.LineItemDTO;
import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.companypayable.database.LineItemTypeMapper;
import com.multiplier.core.payable.companypayable.database.PayableItemDataDto;
import com.multiplier.core.payable.companypayable.database.PayableItemDto;
import com.multiplier.core.util.IgnoreUnmappedMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.util.List;

@Mapper(componentModel = "spring", config = IgnoreUnmappedMapperConfig.class)
public interface LineItemDtoFromPayableItemDtoMapper {

    @Mapping(target = "quantity", constant = "1L")
    @Mapping(target = "unitAmount", source = "billableCost")
    @Mapping(target = "memberName", source = "itemData", qualifiedByName = "mapMemberName")
    @Mapping(target = "itemType", source = "payableItemDto", qualifiedByName = "mapItemType")
    @Mapping(target = "contractId", source = "itemData", qualifiedByName = "mapContractId")
    @Mapping(target = "baseCurrency", source = "currencyCode")
    @Mapping(target = "amountInBaseCurrency", source = "totalCost")
    @Mapping(target = "startPayCycleDate", source = "itemData", qualifiedByName = "mapStartPayCycleDate")
    @Mapping(target = "endPayCycleDate", source = "itemData", qualifiedByName = "mapEndPayCycleDate")
    LineItemDTO map(PayableItemDto payableItemDto);

    @Named("mapMemberName")
    default String mapMemberName(List<PayableItemDataDto> itemData) {
        return itemData != null && !itemData.isEmpty() ? itemData.iterator().next().getMemberName() : null;
    }

    @Named("mapContractId")
    default Long mapContractId(List<PayableItemDataDto> itemDataSet) {
        return itemDataSet != null && !itemDataSet.isEmpty() ? itemDataSet.iterator().next().getContractId() : null;
    }

    @Named("mapStartPayCycleDate")
    default LocalDate mapStartPayCycleDate(List<PayableItemDataDto> itemDataSet) {
        return itemDataSet != null && !itemDataSet.isEmpty() ? itemDataSet.iterator().next().getStartPayCycleDate() : null;
    }

    @Named("mapEndPayCycleDate")
    default LocalDate mapEndPayCycleDate(List<PayableItemDataDto> itemDataSet) {
        return itemDataSet != null && !itemDataSet.isEmpty() ? itemDataSet.iterator().next().getEndPayCycleDate() : null;
    }

    @Named("mapItemType")
    default LineItemType mapItemType(PayableItemDto payableItem) {
        if (payableItem.isPayrollOrExpense()) {
            return LineItemType.EOR_SALARY_DISBURSEMENT;
        }
        var lineItemTypeMapper = Mappers.getMapper(LineItemTypeMapper.class);
        return lineItemTypeMapper.map(payableItem.getType());
    }

    List<LineItemDTO> map(List<PayableItemDto> payableItemDtos);
}
