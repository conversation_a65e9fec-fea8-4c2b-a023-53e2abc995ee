package com.multiplier.core.payable.report.collector;

import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.repository.model.ExternalSystem;
import com.multiplier.core.payable.repository.model.JpaCompanyPayable;
import com.multiplier.core.payable.repository.model.JpaInvoice;
import com.multiplier.core.payable.repository.model.JpaInvoiceLineItem;
import com.multiplier.core.payable.service.dataholder.SourceReportData;
import com.multiplier.payable.types.InvoiceType;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class IsrInvoiceBankFeeExtractor {

    private final Predicate<JpaInvoiceLineItem> isLineItemProcessable = lineItem -> 
            lineItem.getItemType() == LineItemType.BANK_FEE;

    private final InvoiceBankFeeExtractor invoiceBankFeeExtractor;

    public List<SourceReportData> extractingBankFee(
            List<Pair<JpaCompanyPayable, JpaInvoice>> previousPayablesInvoices
    ) {
        if (CollectionUtils.isEmpty(previousPayablesInvoices)) {
            return List.of();
        }

        var netsuiteSalaryInvoices = previousPayablesInvoices.stream()
                .filter(pair -> pair.getRight().getExternalSystem() == ExternalSystem.NETSUITE)
                .filter(pair -> {
                    var invoiceType = getInvoiceTypeFromReference(pair.getRight().getReference());
                    return invoiceType == InvoiceType.SALARY;
                })
                .toList();

        return netsuiteSalaryInvoices.stream()
                .map(this::getBankFeeReportLine)
                .filter(Objects::nonNull)
                .toList();
    }

    private SourceReportData getBankFeeReportLine(Pair<JpaCompanyPayable, JpaInvoice> payableInvoicePair) {
        var payable = payableInvoicePair.getLeft();
        var invoice = payableInvoicePair.getRight();

        return ListUtils.emptyIfNull(invoice.getLineItems())
                .stream()
                .filter(isLineItemProcessable::evaluate)
                .findFirst()
                .map(jpaInvoiceLineItem -> getSourceReportData(payable, jpaInvoiceLineItem))
                .orElse(null);
    }

    private SourceReportData getSourceReportData(JpaCompanyPayable payable, JpaInvoiceLineItem jpaInvoiceLineItem) {
        var bankFee = invoiceBankFeeExtractor.getAmount(jpaInvoiceLineItem);
        var currencyCode = invoiceBankFeeExtractor.getCurrencyCode(jpaInvoiceLineItem);
        return SourceReportData.builder()
                .companyId(payable.getCompanyId())
                .bankFee(bankFee)
                .payableId(payable.getId())
                .currencyCode(currencyCode)
                .build();
    }

    private InvoiceType getInvoiceTypeFromReference(String reference) {
        if (StringUtils.containsIgnoreCase(reference, "salary")
                && StringUtils.containsIgnoreCase(reference, "eor")) {
            if (StringUtils.containsIgnoreCase(reference, "gross")) {
                return InvoiceType.GROSS;
            }
            return InvoiceType.SALARY;
        }
        return null;
    }
}
