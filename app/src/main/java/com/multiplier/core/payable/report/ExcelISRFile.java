package com.multiplier.core.payable.report;

import com.multiplier.payable.types.DocumentReadable;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode
public class ExcelISRFile implements ISRFile {

    private final String fileName;
    private final String blob;

    public ExcelISRFile(String fileName, String blob) {
        this.fileName = fileName;
        this.blob = blob;
    }

    @Override
    public DocumentReadable toDocumentReadable() {
        return DocumentReadable.newBuilder()
                .blob(blob)
                .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                .id(System.currentTimeMillis())
                // frontend will append the extension again to the file name as name here is without extension
                .name(fileName.replace(".xlsx", ""))
                .extension("xlsx")
                .build();
    }
}
