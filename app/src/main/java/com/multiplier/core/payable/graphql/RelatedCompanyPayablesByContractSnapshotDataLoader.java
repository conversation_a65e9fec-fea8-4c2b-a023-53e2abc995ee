package com.multiplier.core.payable.graphql;

import com.multiplier.core.util.ExecutorUtil;
import com.multiplier.core.payable.service.PayableService;
import com.multiplier.payable.types.CompanyPayable;
import com.multiplier.payable.types.ContractSnapshot;
import com.netflix.graphql.dgs.DgsDataLoader;
import lombok.RequiredArgsConstructor;
import org.dataloader.MappedBatchLoader;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;

@RequiredArgsConstructor
@DgsDataLoader(name = "relatedCompanyPayablesByContractSnapshot", maxBatchSize = 100)
public class RelatedCompanyPayablesByContractSnapshotDataLoader implements MappedBatchLoader<ContractSnapshot, List<CompanyPayable>> {
    private final PayableService payableService;

    @Override
    public CompletionStage<Map<ContractSnapshot, List<CompanyPayable>>> load(Set<ContractSnapshot> contractSnapshots) {
        Executor executor = ExecutorUtil.getNewSecurityContextExecutor();
        return CompletableFuture.supplyAsync(() ->
                payableService.getContractSnapshotToRelatedCompanyPayablesMap(contractSnapshots), executor);
    }
}
