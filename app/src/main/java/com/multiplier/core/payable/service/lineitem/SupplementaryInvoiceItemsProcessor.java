package com.multiplier.core.payable.service.lineitem;

import com.multiplier.core.payable.repository.model.JpaInvoice;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
class SupplementaryInvoiceItemsProcessor implements LineItemProcessor<JpaInvoice> {
    private final ProcessedLineItemMapper processedLineItemMapper;
    private final LineItemChecker lineItemChecker;

    @Override
    public List<ProcessedLineItem> process(List<JpaInvoice> supplementaryInvoices,
                                           Map<Long, MetadataItem> metadataItemsByContractId) {
        log.info("Processing first supplementary invoices items.");
        return supplementaryInvoices.stream()
                .flatMap(supplementaryInvoice -> supplementaryInvoice.getLineItems().stream()
                        .map(invoiceItem -> {
                            var metadata = metadataItemsByContractId.get(invoiceItem.getContractId());
                            var invoiceNo = supplementaryInvoice.getInvoiceNo();
                            return processedLineItemMapper.mapFromInvoiceItem(invoiceItem, metadata, invoiceNo);
                        }))
                .filter(lineItemChecker::check)
                .map(lineItemChecker::update)
                .filter(processedLineItem -> processedLineItem.getItemCategory() == LineItemCategory.SUPPLEMENTS)
                .toList();
    }
}
