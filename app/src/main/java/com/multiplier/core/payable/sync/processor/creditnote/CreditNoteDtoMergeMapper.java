package com.multiplier.core.payable.sync.processor.creditnote;

import com.multiplier.core.payable.creditnote.database.CreditNoteDto;
import com.multiplier.core.payable.creditnote.database.CreditNoteItemDto;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
class CreditNoteDtoMergeMapper {

    public CreditNoteDto merge(CreditNoteDto externalCreditNote, CreditNoteDto currentCreditNoteDto) {
        var builder = currentCreditNoteDto.toBuilder();

        if (externalCreditNote.getExternalId() != null) {
            builder.externalId(externalCreditNote.getExternalId());
        }
        if (externalCreditNote.getStatus() != null) {
            builder.status(externalCreditNote.getStatus());
        }
        if (externalCreditNote.getCurrencyCode() != null) {
            builder.currencyCode(externalCreditNote.getCurrencyCode());
        }
        if (externalCreditNote.getAmountTotal() != null) {
            builder.amountTotal(externalCreditNote.getAmountTotal());
        }
        if (externalCreditNote.getAmountApplied() != null) {
            builder.amountApplied(externalCreditNote.getAmountApplied());
        }
        if (externalCreditNote.getAmountUnapplied() != null) {
            builder.amountUnapplied(externalCreditNote.getAmountUnapplied());
        }
        if (externalCreditNote.getReference() != null) {
            builder.reference(externalCreditNote.getReference());
        }
        if (externalCreditNote.getCompanyId() != null) {
            builder.companyId(externalCreditNote.getCompanyId());
        }
        if (externalCreditNote.getReason() != null) {
            builder.reason(externalCreditNote.getReason());
        }
        if (externalCreditNote.getCreditNoteNo() != null) {
            builder.creditNoteNo(externalCreditNote.getCreditNoteNo());
        }
        if (externalCreditNote.getMonth() != null) {
            builder.month(externalCreditNote.getMonth());
        }
        if (externalCreditNote.getYear() != null) {
            builder.year(externalCreditNote.getYear());
        }
        if (externalCreditNote.getCreatedFromInvoiceId() != null) {
            builder.createdFromInvoiceId(externalCreditNote.getCreatedFromInvoiceId());
        }
        if (externalCreditNote.getCreatedDate() != null) {
            builder.createdDate(externalCreditNote.getCreatedDate());
        }

        List<CreditNoteItemDto> externalItems = externalCreditNote.getItems();
        if (externalItems != null) {
            builder.items(new ArrayList<>(externalItems));
        }

        return builder.build();
    }
}