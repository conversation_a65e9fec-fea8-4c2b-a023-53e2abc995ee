package com.multiplier.core.payable.generator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.multiplier.core.payable.report.domain.ReportCategory;
import com.multiplier.payable.types.CountryCode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class InvoiceSourceReportGeneratorConfigStore {

    private final ObjectMapper mapper;
    private final ConcurrentHashMap<String, ConcurrentHashMap<String, Object>> values;

    public InvoiceSourceReportGeneratorConfigStore(@Value("${config.report.invoice-source-generator-config.jsonfile}") String fileName) throws IOException {

        mapper = new ObjectMapper();
        values = new ConcurrentHashMap<>();

        try {
            InputStream json = getClass().getClassLoader().getResourceAsStream(fileName);
            List<ConcurrentHashMap<String, Object>> values = mapper.readValue(json, new TypeReference<>() {
            });
            for (ConcurrentHashMap value : values) {
                this.values.put((String) value.get("report_category"), value);
            }
        } catch (JsonMappingException ex) {
            throw new InvalidConfigException("Wrong JSON format found in the template file " + fileName, ex);
        } catch (IllegalArgumentException ex) {
            throw new IllegalArgumentException("JSON format file not found file:" + fileName, ex);
        }
    }

    /**
     * Method to get country specific configurations
     *
     * @param reportCategory Categrory of report
     * @param countryCode    Country Code
     * @param countryDataKey Key to retrive configuration object
     */

    private Object getValueByCountryAndKey(ReportCategory reportCategory, CountryCode countryCode, String countryDataKey) {
        ConcurrentHashMap<String, Object> reportConfig = this.values.get(reportCategory.toString());
        if (reportConfig == null) {
            return Collections.emptyList();
        }
        List<Object> columnWrapper = (List<Object>) reportConfig.get("columns");
        return (Object) columnWrapper.stream().filter(countryColumn
                -> ((LinkedHashMap<?, ?>) countryColumn).get("country_code").equals(countryCode.toString())).map(columnList
                -> ((LinkedHashMap<?, ?>) columnList).get(countryDataKey)).findFirst().orElse(null);
    }

    /**
     * Method to get country specific configurations
     *
     * @param reportCategory Categrory of report
     * @param countryCode    Country Code
     */

    public List<Object> getColumnsByCountry(ReportCategory reportCategory, CountryCode countryCode) {
        List<Object> columns = (List<Object>) getValueByCountryAndKey(reportCategory, countryCode, "columns");
        return columns == null ? (List<Object>) getValueByCountryAndKey(reportCategory, CountryCode.PRK, "columns") : columns;
    }

}
