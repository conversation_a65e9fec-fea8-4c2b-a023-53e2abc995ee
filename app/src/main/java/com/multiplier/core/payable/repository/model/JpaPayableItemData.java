package com.multiplier.core.payable.repository.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.multiplier.payable.engine.contract.ContractType;
import com.multiplier.payable.engine.contract.CountryWorkStatus;
import com.multiplier.payable.engine.deposit.Deposit;
import com.multiplier.payable.engine.domain.aggregates.AnnualSeatPaymentTerm;
import com.multiplier.payable.engine.payableitem.ContractDepartment;
import com.multiplier.payable.types.CurrencyCode;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Embeddable
public class JpaPayableItemData implements Serializable {
     private Long memberPayId;
     private Long contractId;
     private String matchingAdvanceCollectionLineTaxCode;
     private ContractType contractType;
     private Double amountTotalCost;
     private CurrencyCode currencyCode;
     private String memberName;
     private LocalDate startPayCycleDate;
     private LocalDate endPayCycleDate;
     private String cycle;
     private String insuranceType;
     private ContractDepartment contractDepartment;
     private Deposit deposit;
     private AnnualSeatPaymentTerm annualSeatPaymentTerm;
     private CountryWorkStatus countryWorkStatus;
}
