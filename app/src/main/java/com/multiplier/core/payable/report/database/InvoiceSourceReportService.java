package com.multiplier.core.payable.report.database;

import com.multiplier.core.payable.report.ISRFileStore;
import com.multiplier.core.payable.report.domain.IsrKey;
import com.multiplier.payable.types.CompanyPayable;
import com.multiplier.payable.types.CompanyPayableReport;
import com.multiplier.payable.types.CompanyPayableReportType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class InvoiceSourceReportService {
    private final ISRFileStore isrFileStore;

    public List<CompanyPayable> updateCompanyPayablesWithAvailableReports(List<CompanyPayable> companyPayables) {
        var isrKeys = companyPayables.stream()
                .filter(this::isReportSupportedInCompanyPayable)
                .map(this::getIsrKey)
                .toList();

        var isrKeyToExistReport = isrFileStore.reportExistForKeysWithFallback(isrKeys);
        return companyPayables.stream()
                .map(companyPayable -> {
                    if (!isReportSupportedInCompanyPayable(companyPayable)) {
                        return companyPayable;
                    }
                    var isrKey = getIsrKey(companyPayable);
                    var reportExist = isrKeyToExistReport.getOrDefault(isrKey, false);
                    if (reportExist) {
                        companyPayable.setReport(getCompanyPayableReport(companyPayable));
                    }
                    return companyPayable;
                })
                .collect(Collectors.toList());
    }

    private boolean isReportSupportedInCompanyPayable(CompanyPayable companyPayable) {
        return companyPayable.getInvoice() != null && companyPayable.getInvoice().getType() != null &&
                companyPayable.getInvoice().getType().equals(com.multiplier.payable.types.InvoiceType.SALARY);
    }

    private IsrKey getIsrKey(CompanyPayable companyPayable) {
        return IsrKey.builder()
                .companyId(companyPayable.getCompany().getId())
                .month(companyPayable.getMonth())
                .year(companyPayable.getYear())
                .companyPayableId(companyPayable.getId())
                .build();
    }

    private CompanyPayableReport getCompanyPayableReport(CompanyPayable companyPayable) {
        return CompanyPayableReport.newBuilder()
                .companyId(companyPayable.getCompany().getId())
                .month(companyPayable.getMonth())
                .year(companyPayable.getYear())
                .type(CompanyPayableReportType.SALARY_INVOICE_SOURCE_REPORT)
                .build();
    }
}
