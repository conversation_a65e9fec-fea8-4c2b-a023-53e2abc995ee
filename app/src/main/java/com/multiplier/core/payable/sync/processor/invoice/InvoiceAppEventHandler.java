package com.multiplier.core.payable.sync.processor.invoice;

import com.multiplier.core.config.featureflag.FeatureFlagService;
import com.multiplier.core.payable.event.database.EventType;
import com.multiplier.core.payable.freelancerinvoice.MemberPayableExternalInvoiceService;
import com.multiplier.core.payable.repository.model.InvoiceType;
import com.multiplier.core.payable.service.InvoiceNotificationService;
import com.multiplier.core.payable.service.PayableService;
import com.multiplier.payable.types.InvoiceStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.Collections;
import java.util.Map;

import static com.multiplier.core.config.featureflag.FeatureFlag.SEND_SLACK_NOTIFICATION_FOR_PAID_DEPOSIT;

/**
 * A class that receives and processes {@link InvoiceAppEvent}.
 */
@Component
@Slf4j
@RequiredArgsConstructor
class InvoiceAppEventHandler {

    private final InvoiceNotificationService invoiceNotificationService;
    private final MemberPayableExternalInvoiceService memberPayableExternalInvoiceService;
    private final FeatureFlagService featureFlagService;

    @TransactionalEventListener(value = InvoiceAppEvent.class, phase = TransactionPhase.AFTER_COMMIT)
    public void handle(InvoiceAppEvent invoiceAppEvent) {
        var eventType = invoiceAppEvent.getEventType();

        if (eventType == EventType.CREATE) {
            handleCreateEventType(invoiceAppEvent);
            return;
        }

        if (eventType == EventType.UPDATE) {
            handleUpdateEventType(invoiceAppEvent);
        }
    }

    private void handleCreateEventType(InvoiceAppEvent invoiceAppEvent) {
        log.info("Handle event type CREATE");

        var isLegacyXeroInvoice = invoiceAppEvent.isLegacyXeroInvoice();
        if (!isLegacyXeroInvoice && isNotificationOverriddenByPlatform()) {
            var afterInvoice = invoiceAppEvent.getAfterInvoice();
            invoiceNotificationService.sendEorInvoiceRaisedEmailToAllCompanyAdmins(afterInvoice);
        }
    }

    private void handleUpdateEventType(InvoiceAppEvent invoiceAppEvent) {
        log.info("Handle event type UPDATE");

        var isLegacyXeroInvoice = invoiceAppEvent.isLegacyXeroInvoice();
        var beforeInvoice = invoiceAppEvent.getBeforeInvoice();
        var afterInvoice = invoiceAppEvent.getAfterInvoice();

        if (!isLegacyXeroInvoice && isNotificationOverriddenByPlatform()) {
            invoiceNotificationService.sendEorInvoiceRaisedEmailToAllCompanyAdmins(beforeInvoice, afterInvoice);
        }

        if (isInvoiceUpdatedFromAuthorizedToPaid(beforeInvoice.getStatus(), afterInvoice.getStatus())) {
            var companyPayableId = beforeInvoice.getCompanyPayableId();
            memberPayableExternalInvoiceService.onInvoiceUpdatedToPaid(afterInvoice, companyPayableId);
        }

        if (isDepositInvoicePaid(invoiceAppEvent) && isSendSlackNotificationOnPaidDepositInvoiceEnabled()) {
            invoiceNotificationService.sendDepositInvoicePaidSlackNotificationToMultiplier(afterInvoice);
        }
    }

    boolean isInvoiceUpdatedFromAuthorizedToPaid(InvoiceStatus beforeStatus, InvoiceStatus afterStatus) {
        log.info("Invoice status is being updated from {} to {}", beforeStatus, afterStatus);
        return beforeStatus == InvoiceStatus.AUTHORIZED
                && afterStatus == InvoiceStatus.PAID;
    }

    boolean isNotificationOverriddenByPlatform() {
        var feature = featureFlagService.feature("invoice-override-notification", Map.of());
        return Boolean.TRUE.equals(feature.on());
    }

    private boolean isDepositInvoicePaid(InvoiceAppEvent invoiceAppEvent) {
        var beforeInvoice = invoiceAppEvent.getBeforeInvoice();
        var afterInvoice = invoiceAppEvent.getAfterInvoice();
        log.info("Check if deposit invoice {} is paid. " +
                        "Before invoice type: {}. After invoice type: {}. " +
                        "Before invoice status: {}. After invoice status: {}.",
                afterInvoice.getInvoiceNo(),
                beforeInvoice.getType(), afterInvoice.getType(),
                beforeInvoice.getStatus(), afterInvoice.getStatus());
        return beforeInvoice.getStatus() == InvoiceStatus.AUTHORIZED
                && afterInvoice.getType() == InvoiceType.DEPOSIT
                && afterInvoice.getStatus() == InvoiceStatus.PAID;
    }

    private boolean isSendSlackNotificationOnPaidDepositInvoiceEnabled() {
        var feature = featureFlagService.feature(
                SEND_SLACK_NOTIFICATION_FOR_PAID_DEPOSIT.getFlagName(),
                Collections.emptyMap());
        return Boolean.TRUE.equals(feature.on());
    }
}
