package com.multiplier.core.payable.service.secondinvoice;

import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.adapters.api.InvoiceGenerationAdapter;
import com.multiplier.core.payable.adapters.api.LineItemDTO;
import com.multiplier.core.payable.creditnote.facade.CreateCreditNoteItemRequest;
import com.multiplier.core.payable.creditnote.facade.CreateCreditNoteRequest;
import com.multiplier.core.payable.repository.model.InvoiceReason;
import com.multiplier.core.util.IgnoreUnmappedMapperConfig;
import com.multiplier.payable.types.CreditNoteReason;
import com.multiplier.payable.types.CurrencyCode;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring", config = IgnoreUnmappedMapperConfig.class)
interface NegativeInvoiceMapper {

    @Mapping(target = "customerId", source = "customerId")
    @Mapping(target = "companyId", source = "companyId")
    @Mapping(target = "date", source = "invoiceDTO.date")
    @Mapping(target = "reference", source = "invoiceDTO.reference")
    @Mapping(target = "reason", source = "invoiceDTO.reason", qualifiedByName = "mapCreditNoteReason")
    @Mapping(target = "status", constant = "DRAFT")
    @Mapping(target = "currencyCode", source = "invoiceDTO.billingCurrencyCode")
    @Mapping(target = "month", source = "invoiceDTO", qualifiedByName = "mapMonth")
    @Mapping(target = "year", source = "invoiceDTO", qualifiedByName = "mapYear")
    @Mapping(target = "externalSystem", constant = "NETSUITE")
    @Mapping(target = "items", source = "invoiceDTO.lineItems")
    @Mapping(target = "subsidiaryName", source = "invoiceDTO.subsidiaryName", defaultValue = "Multiplier Technologies Pte Ltd")
    CreateCreditNoteRequest map(InvoiceGenerationAdapter.InvoiceResponseDataHolder dataHolder);

    @Named("mapMonth")
    default Integer mapMonth(InvoiceDTO invoiceDTO) {
        var referenceInvoiceDate = invoiceDTO.getReferenceInvoiceDate();
        if (referenceInvoiceDate == null) {
            return null;
        }
        return referenceInvoiceDate.getMonthValue();
    }

    @Named("mapYear")
    default Integer mapYear(InvoiceDTO invoiceDTO) {
        var referenceInvoiceDate = invoiceDTO.getReferenceInvoiceDate();
        if (referenceInvoiceDate == null) {
            return null;
        }
        return referenceInvoiceDate.getYear();
    }

    // Map taxType to taxCode because the LineItemDto stores NS taxCode in the field "taxType"
    @Mapping(target = "taxCode", source = "taxType")
    @Mapping(target = "unitAmount", source = "unitAmount", qualifiedByName = "mapUnitAmount")
    @Mapping(target = "baseCurrency", source = "baseCurrency", qualifiedByName = "extractCurrencyCode")
    @Mapping(target = "revRecStartDate", source = "startPayCycleDate")
    @Mapping(target = "revRecEndDate", source = "endPayCycleDate")
    CreateCreditNoteItemRequest map(LineItemDTO lineItemDTO);

    @Named("extractCurrencyCode")
    default CurrencyCode getCurrencyCode(String strCurrency) {
        if (strCurrency == null || strCurrency.isEmpty()) {
            return null;
        }
        return CurrencyCode.valueOf(strCurrency);
    }

    @Named("mapUnitAmount")
    default double mapUnitAmount(double unitAmount) {
        return -1 * unitAmount;
    }

    @Named("mapCreditNoteReason")
    default CreditNoteReason mapCreditNoteReason(InvoiceReason invoiceReason) {
        if (invoiceReason == null) {
            return null;
        }

        switch (invoiceReason) {
            case INVOICE_VOIDED:
                return CreditNoteReason.INVOICE_VOIDED;
            case DEPOSITS:
                return CreditNoteReason.DEPOSIT_REFUND;
            case INSURANCE:
                return CreditNoteReason.INSURANCE_CANCELLATION;
            case SECOND_INVOICE:
                return CreditNoteReason.SECOND_INVOICE;
            default:
                return CreditNoteReason.INVOICE_CORRECTION;
        }
    }
}
