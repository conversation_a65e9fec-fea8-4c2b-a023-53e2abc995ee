package com.multiplier.core.payable.repository.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum InvoiceReason {

    INVOICE_VOIDED("Invoice Voided", "1"),
    OTHERS("Others", "2"),
    CORRECTION_OF_INVOICE("Correction of Invoice", "3"),
    FIRST_INVOICE("First Invoice", "4"),
    SECOND_INVOICE("Second Invoice", "5"),
    DEPOSITS("Deposit Invoice", "6"),
    INSURANCE("Insurance Invoice", "7"),
    VISA("Visa", "8"),
    FIRST_INVOICE_SUPPLEMENTARY("Additional First Invoice", "11")
    ;

    private final String name;
    private final String externalId;
}
