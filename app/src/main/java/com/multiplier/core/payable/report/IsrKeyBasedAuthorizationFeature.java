package com.multiplier.core.payable.report;

import com.multiplier.core.config.featureflag.FeatureFlagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static java.util.Collections.emptyMap;

/**
 * A class that checks if the feature "ISR-key-based authorization" is enabled.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class IsrKeyBasedAuthorizationFeature {

    private final FeatureFlagService featureFlagService;
    private static final String FEATURE_KEY = "isr-key-based-authorization";

    public boolean isEnabled() {
        var result = featureFlagService.feature(FEATURE_KEY, emptyMap());
        var enabled = result.on();
        var enabledText = enabled ? "on" : "off";
        log.info("{} is {}", FEATURE_KEY, enabledText);
        return enabled;
    }
}
