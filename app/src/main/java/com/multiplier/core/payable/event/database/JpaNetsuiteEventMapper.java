package com.multiplier.core.payable.event.database;

import com.multiplier.core.util.IgnoreUnmappedMapperConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Collection;
import java.util.List;

@Mapper(componentModel = "spring", config = IgnoreUnmappedMapperConfig.class)
interface JpaNetsuiteEventMapper {

    List<JpaNetsuiteEvent> map(Collection<EventDto> eventDtos);

    @Mapping(target = "eventData", source = "eventDto", qualifiedByName = "mapEventData")
    JpaNetsuiteEvent map(EventDto eventDto);

    @Named("mapEventData")
    default Object mapEventData(EventDto eventDto) {
        return eventDto;
    }
}
