package com.multiplier.core.payable.graphql.dataloader;

import com.multiplier.core.util.ExecutorUtil;
import com.multiplier.core.payable.service.ContractDepositPayableService;
import com.multiplier.payable.types.CompanyPayable;
import com.netflix.graphql.dgs.DgsDataLoader;
import lombok.RequiredArgsConstructor;
import org.dataloader.MappedBatchLoader;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;

@RequiredArgsConstructor
@DgsDataLoader(name = "contractDepositPayables", maxBatchSize = 500)
public class DepositPayableByContractIdDataLoader implements MappedBatchLoader<Long, List<CompanyPayable>> {

    private final ContractDepositPayableService contractDepositPayableService;

    @Override
    public CompletionStage<Map<Long, List<CompanyPayable>>> load(final Set<Long> contractIds) {
        Executor executor = ExecutorUtil.getNewSecurityContextExecutor();
        return CompletableFuture.supplyAsync(() ->
                contractDepositPayableService.getCompanyPayablesForContractIds(contractIds), executor);
    }

}
