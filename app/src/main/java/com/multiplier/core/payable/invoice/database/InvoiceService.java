package com.multiplier.core.payable.invoice.database;

import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository;
import com.multiplier.core.payable.repository.model.InvoiceType;
import com.multiplier.payable.types.InvoiceStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class InvoiceService {

    private static final Set<InvoiceStatus> INACTIVE_INVOICE_STATUSES
            = Set.of(InvoiceStatus.VOIDED, InvoiceStatus.DELETED);

    private final JpaInvoiceMapper jpaInvoiceMapper;
    private final JpaInvoiceRepository jpaInvoiceRepository;
    private final InvoiceDtoMapper invoiceDtoMapper;
    private final JpaCompanyPayableRepository jpaCompanyPayableRepository;

    @Transactional
    public InvoiceDTO save(InvoiceDTO invoiceDto) {
        log.info("Save invoice with externalId {}", invoiceDto.getExternalId());

        var invoiceToSave = jpaInvoiceMapper.map(invoiceDto);
        InvoiceDTO savedInvoiceDTO = null;
        try {
            var savedInvoice = jpaInvoiceRepository.save(invoiceToSave);
            savedInvoiceDTO = invoiceDtoMapper.map(savedInvoice);
        } catch (DataIntegrityViolationException e) {
            log.warn("Skip saving invoice with externalId={}", invoiceDto.getExternalId(), e);
        }

        return savedInvoiceDTO;
    }

    @Transactional(readOnly = true)
    public InvoiceDTO get(String externalId) {
        log.info("Get invoice with externalId {}", externalId);

        return jpaInvoiceRepository.findByExternalId(externalId)
                .map(invoiceDtoMapper::map)
                .orElseThrow(() -> new InvoiceDatabaseException(String.format("Invoice %s not found", externalId)));
    }

    @Transactional(readOnly = true)
    public Optional<InvoiceDTO> find(String externalId) {
        log.info("Find invoice with externalId {}", externalId);

        return jpaInvoiceRepository.findByExternalId(externalId)
                .map(invoiceDtoMapper::map);
    }

    @Transactional
    public void linkToCompanyPayable(String externalId, Long companyPayableId) {
        log.info("Link invoice with externalId {} to companyPayable = {}", externalId, companyPayableId);

        var companyPayable = jpaCompanyPayableRepository.getById(companyPayableId);
        var currentInvoice = jpaInvoiceRepository
                .findByExternalId(externalId)
                .orElseThrow(() -> new InvoiceDatabaseException(String.format("Invoice %s not found" + externalId)));
        currentInvoice.setCompanyPayable(companyPayable);
        jpaInvoiceRepository.save(currentInvoice);
    }

    /**
     * @deprecated Used for Xero only.
     */
    @Deprecated
    @Transactional(readOnly = true)
    public InvoiceDTO getByInvoiceNo(String invoiceNo) {
        log.info("Get Xero invoice with invoiceNo {}", invoiceNo);

        return jpaInvoiceRepository.findByInvoiceNo(invoiceNo)
                .map(invoiceDtoMapper::map)
                .orElseThrow(() -> new InvoiceDatabaseException(String.format("Xero invoice %s not found", invoiceNo)));
    }

    @Transactional
    public int markAuthorized(List<InvoiceDTO> relatedInvoices) {
        log.info("Mark AUTHORIZED for for invoices with externalIds: {}", relatedInvoices);
        var affectedInvoiceExternalIds = relatedInvoices.stream()
                .filter(invoiceDTO -> invoiceDTO.getStatus() == InvoiceStatus.PAID || invoiceDTO.getStatus() == InvoiceStatus.VOIDED)
                .map(InvoiceDTO::getExternalId)
                .collect(Collectors.toList());
        if (affectedInvoiceExternalIds.isEmpty()) {
            return 0;
        }
        return jpaInvoiceRepository.updateStatusByExternalIds(affectedInvoiceExternalIds, InvoiceStatus.AUTHORIZED);
    }

    @Transactional
    public InvoiceDTO getByContractIdAndType(Long contractId, InvoiceType type) {
        log.info("Get invoice with contractId {} and type {}", contractId, type);

        return jpaInvoiceRepository.findJpaInvoicesByContractIdAndType(contractId, type)
                .stream()
                .map(invoiceDtoMapper::map)
                .findFirst()
                .orElse(null);
    }

    @Transactional(readOnly = true)
    public List<InvoiceDTO> getByCompanyPayableIds(Collection<Long> companyPayableIds) {
        log.info("Get invoices with companyPayableIds {}", companyPayableIds);

        var jpaInvoices = jpaInvoiceRepository.findByCompanyPayableIdIn(companyPayableIds);
        return jpaInvoices.stream()
                .map(invoiceDtoMapper::map)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<InvoiceDTO> getActiveInvoices(Collection<Long> companyPayableIds) {
        if (CollectionUtils.isEmpty(companyPayableIds)) {
            return new ArrayList<>();
        }
        log.info("Get invoices with companyPayableIds {}", companyPayableIds);

        var jpaInvoices = jpaInvoiceRepository.findByCompanyPayableIdIn(companyPayableIds);
        return jpaInvoices.stream()
                .map(invoiceDtoMapper::map)
                .filter(invoice -> !INACTIVE_INVOICE_STATUSES.contains(invoice.getStatus()))
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<InvoiceDTO> getAllByInvoiceNumbers(List<String> invoiceNumbers) {
        log.info("Get invoices with invoiceNumbers {}", invoiceNumbers);

        if (invoiceNumbers == null || invoiceNumbers.isEmpty()) {
            return List.of();
        }

        return jpaInvoiceRepository.findAllByInvoiceNoIn(new HashSet<>(invoiceNumbers)).stream()
                .map(invoiceDtoMapper::map)
                .toList();
    }
}
