package com.multiplier.core.payable.report.generator;

import com.multiplier.core.payable.report.generator.domain.ReportRow;
import com.multiplier.core.payable.report.generator.domain.ReportRowCellValue;
import com.multiplier.core.payable.service.dataholder.SourceReportData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class IsrBankFeeReportRowGenerator implements IsrReportRowGenerator {
    private final IsrColumnRearranger isrColumnRearranger;
    
    @Override
    public boolean isEligible(SourceReportData sourceReportData) {
        return sourceReportData.getContractData() == null && sourceReportData.getBankFee() != null;
    }

    @Override
    public ReportRow generate(SourceReportData sourceReportData) {
        var cellNameValues = Map.of(
                "Company ID", sourceReportData.getCompanyId(),
                "Company Legal Name", sourceReportData.getCompanyDisplayName(), // but it will just return display name
                "Bank Fee Recovery", sourceReportData.calculateBankFee(),
                "Balance Amount To Billed", sourceReportData.calculateBankFee(),
                "Total Cost (Billing Currency)", sourceReportData.calculateBankFee()
        );
        var reportRowCellValues = cellNameValues
                .entrySet()
                .stream()
                .map(entry -> ReportRowCellValue.builder()
                        .columnValue(entry.getValue())
                        .columnName(entry.getKey())
                        .build())
                .toList();

        var rearrangedReportRowCellValues = isrColumnRearranger.reArrangeReportRowCellValues(reportRowCellValues);
        return ReportRow.builder().reportRowCellValues(rearrangedReportRowCellValues).build();
    }
}
