package com.multiplier.core.payable.sync.processor.invoice;

import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.event.database.EventType;
import com.multiplier.core.payable.invoice.database.InvoiceService;
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository;
import com.multiplier.core.payable.repository.model.JpaCompanyPayable;
import com.multiplier.payable.types.PayableStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * A class that updates a single invoice based on invoice retrieved from external systems.
 */
@Component
@RequiredArgsConstructor
@Slf4j
class InvoiceUpdater implements Filterable<InvoiceDTO, InvoiceDTO> {

    private final JpaCompanyPayableRepository companyPayableRepository;
    private final UpsertContractDepositPayableService upsertContractDepositPayableService;
    private final ApplicationEventPublisher eventPublisher;
    private final InvoiceService invoiceService;
    private final InvoiceDtoMergeMapper invoiceDtoMergeMapper;
    private final LegacyXeroInvoiceProcessor legacyXeroInvoiceProcessor;
    private final InvoiceCompanyPayableUpdater invoiceCompanyPayableUpdater;

    @Transactional
    public InvoiceDTO update(InvoiceDTO externalInvoice) {
        log.info("Update invoice with externalId {}", externalInvoice.getExternalId());

        var currentInvoice = invoiceService.get(externalInvoice.getExternalId());
        var companyPayableId = currentInvoice.getCompanyPayableId();
        if (companyPayableId == null) {
            var message = String.format("The invoice with externalId %s doesn't have a company payable", currentInvoice.getExternalId());
            throw new IllegalArgumentException(message);
        }

        if (shouldBeFilteredOut(currentInvoice, externalInvoice)) {
            return null;
        }

        if (legacyXeroInvoiceProcessor.isLegacyXeroInvoice(externalInvoice)) {
            return legacyXeroInvoiceProcessor.process(externalInvoice);
        }

        var externalInvoiceWithCompanyPayableId = externalInvoice.toBuilder()
                .companyPayableId(companyPayableId)
                .build();
        var updatedCompanyPayable = invoiceCompanyPayableUpdater.update(externalInvoiceWithCompanyPayableId);

        var invoiceToUpdate = invoiceDtoMergeMapper.merge(externalInvoice, currentInvoice);
        var updatedInvoice = invoiceService.save(invoiceToUpdate);

        invoiceService.linkToCompanyPayable(externalInvoice.getExternalId(), updatedCompanyPayable.getId());

        upsertContractDepositPayableService.upsert(externalInvoice, updatedCompanyPayable.getId());

        var updatedInvoiceWithCompanyPayable = updatedInvoice.toBuilder()
                .companyPayableId(updatedCompanyPayable.getId())
                .companyId(updatedCompanyPayable.getCompanyId())
                .build();

        fireEvent(currentInvoice, updatedInvoiceWithCompanyPayable);

        return updatedInvoiceWithCompanyPayable;
    }

    /**
     * Delegate further processing to event handlers.
     * Advantages:
     * + Commit the transaction before doing further operations such as sending emails, notify external services
     * + Decouple "after operations" for testability and maintainability
     * + Ability to add multiple event handlers such as sendEmailHandler, notifyExternalServiceHandler
     */
    private void fireEvent(InvoiceDTO beforeInvoice, InvoiceDTO afterInvoice) {
        log.info("Publish event with beforeInvoiceId {}, beforeStatus {}, " +
                        "afterInvoiceId {}, afterStatus {}",
                beforeInvoice.getExternalId(), beforeInvoice.getStatus(),
                afterInvoice.getExternalId(), afterInvoice.getStatus());
        eventPublisher.publishEvent(
                InvoiceAppEvent.builder()
                        .source(this)
                        .eventType(EventType.UPDATE)
                        .beforeInvoice(beforeInvoice)
                        .afterInvoice(afterInvoice)
                        .build());
    }

    JpaCompanyPayable getCompanyPayableToUpdateUsingExternalInvoiceAsSource(
            InvoiceDTO externalInvoice, Long companyId, Long companyPayableId) {
        var currentCompanyPayable = companyPayableRepository.getById(companyPayableId);
        return currentCompanyPayable.toBuilder()
                .status(PayableStatus.valueOf(externalInvoice.getStatus().name()))
                .companyId(companyId)
                .date(externalInvoice.getDate().atStartOfDay())
                .totalAmount(externalInvoice.getTotalAmount())
                .currency(externalInvoice.getBillingCurrencyCode())
                .build();
    }

    @Override
    public boolean shouldBeFilteredOut(InvoiceDTO currentInvoice, InvoiceDTO externalInvoice) {
        log.info("Current synced time {}. External synced time {}.", currentInvoice.getSyncedTime(),
                currentInvoice.getSyncedTime());

        var filteredOut = currentInvoice.getSyncedTime() != null &&
                currentInvoice.getSyncedTime().isAfter(externalInvoice.getSyncedTime());

        log.info("The invoice with externalId {} {} filtered out", externalInvoice.getExternalId(),
                filteredOut ? "is" : "is not");
        return filteredOut;
    }
}
