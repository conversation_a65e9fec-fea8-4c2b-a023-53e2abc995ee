package com.multiplier.core.payable.sync.processor.invoice;

import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.event.database.EventType;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Objects;

/**
 * An {@link ApplicationEvent} event that happens when an invoice is persisted.
 */
@Getter
@EqualsAndHashCode(callSuper = false)
class InvoiceAppEvent extends ApplicationEvent {

    private final EventType eventType;

    /**
     * The invoice before persisting.
     * Null for inserting.
     */
    private final InvoiceDTO beforeInvoice;

    /**
     * The invoice after persisting.
     */
    private final InvoiceDTO afterInvoice;

    /**
     * Keep backward-compatibility.
     */
    private final boolean legacyXeroInvoice;

    @Builder
    public InvoiceAppEvent(Object source,
                           EventType eventType,
                           InvoiceDTO beforeInvoice,
                           InvoiceDTO afterInvoice,
                           boolean legacyXeroInvoice
    ) {
        super(source);
        if (eventType == null) {
            throw new IllegalArgumentException("eventType must not be null");
        }
        if (eventType == EventType.UPDATE) {
            if (beforeInvoice == null) {
                throw new IllegalArgumentException("beforeInvoice must not be null");
            }
            if (beforeInvoice.getCompanyPayableId() == null) {
                throw new IllegalArgumentException("beforeInvoice#companyPayableId must not be null");
            }
            if (afterInvoice == null) {
                throw new IllegalArgumentException("afterInvoice must not be null");
            }
            if (afterInvoice.getCompanyId() == null) {
                throw new IllegalArgumentException("afterInvoice#companyId must not be null");
            }
        } else if (eventType == EventType.CREATE) {
            if (afterInvoice == null) {
                throw new IllegalArgumentException("afterInvoice must not be null");
            }
        }
        this.beforeInvoice = beforeInvoice;
        this.afterInvoice = afterInvoice;
        this.legacyXeroInvoice = legacyXeroInvoice;
        this.eventType = eventType;
    }

}
