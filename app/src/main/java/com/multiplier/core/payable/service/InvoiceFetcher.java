package com.multiplier.core.payable.service;

import com.multiplier.core.payable.invoice.database.JpaInvoiceRepository;
import com.multiplier.core.payable.repository.model.InvoiceReason;
import com.multiplier.core.payable.repository.model.InvoiceType;
import com.multiplier.core.payable.repository.model.JpaInvoice;
import com.multiplier.payable.types.InvoiceDetail;
import com.multiplier.payable.types.InvoiceStatus;
import com.multiplier.payable.types.MonthYearInput;
import jakarta.persistence.EntityManager;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
@Slf4j
public class InvoiceFetcher {

    private final EntityManager entityManager;

    private final JpaInvoiceRepository jpaInvoiceRepository;

    /**
     * Regex match whole phrase in order:
     * Match any optional leading characters. E.g: "  Feb'23 ...", "Test Feb'23 ..."
     * Match month name. E.g: Feb, Mar
     * Match any characters. E.g: "...Feb'23...", "...Feb 23...", "...Feb`23..."
     * Match year number both short and long format. E.g: "...Feb'2023...", "...Feb'23..."
     * Match any characters after
     * Match "gross"
     * Match any characters after
     * Match "salary"
     * Match any characters after. E.g: "...Feb'23 Gross Salary - EOR", "...Feb'23 Gross Salary EOR"
     * Match any optional trailing characters. E.g "Feb'23 Gross Salary - EOR  ", "Feb'23 Gross Salary - EOR Test"
     * Characters are matched case-insensitively by Postgresql
     */
    private static final String FIRST_INVOICE_REFERENCE_REGEX = ".*%s.*(%s|%s).*gross.*salary.*eor.*";
    private static final String SECOND_INVOICE_REFERENCE_REGEX = ".*%s.*(%s|%s).*salary.*eor.*";

    public List<Pair<Long, Long>> getFirstInvoices(Long companyId, Integer month, Integer year) {
        List<Object[]> results = this.getCompanyPayableInvoiceIdPairFromDatabase(companyId, month, year);

        var invoiceAndPayableIdList = new LinkedList<Pair<Long, Long>>();

        for (Object[] objects : results) {
            var jpaCompanyPayableId = (Long) objects[1];
            var jpaInvoiceId = (Long) objects[2];
            invoiceAndPayableIdList.add(Pair.of(jpaCompanyPayableId, jpaInvoiceId));
        }

        return invoiceAndPayableIdList;
    }

    public Pair<Long, Long> getSecondInvoicePair(Long companyId, Integer month, Integer year) {
        List<Object[]> results = this.getSecondInvoiceCompanyPayableInvoiceIdPairFromDatabase(companyId, month, year);

        Map<Long, Pair<Long, Long>> companyIdPayableMap = new HashMap<>();

        for (Object[] objects : results) {
            var companyIdKey = (Long) objects[0];
            var jpaCompanyPayableId = (Long) objects[1];
            var jpaInvoiceId = (Long) objects[2];

            companyIdPayableMap.put(companyIdKey, Pair.of(jpaCompanyPayableId, jpaInvoiceId));
        }

        log.info("CompanyPayable pair for companyId " + companyIdPayableMap.get(companyId));
        return companyIdPayableMap.get(companyId);
    }

    public List<Pair<Long, Long>> getSecondInvoicePairForIsr(Long companyId, Integer month, Integer year) {
        List<Object[]> results = this.getSecondInvoiceCompanyPayableInvoiceIdPairFromDatabase(companyId, month, year);

        Map<Long, List<Pair<Long, Long>>> companyIdPayableMap = new HashMap<>();

        for (Object[] objects : results) {
            var companyIdKey = (Long) objects[0];
            var jpaCompanyPayableId = (Long) objects[1];
            var jpaInvoiceId = (Long) objects[2];
            var pair = Pair.of(jpaCompanyPayableId, jpaInvoiceId);

            companyIdPayableMap.compute(companyIdKey, (k, v) -> {
                if (v == null) {
                    v = new ArrayList<>();
                }
                v.add(pair);
                return v;
            });
        }

        log.info("CompanyPayable pair for companyId " + companyIdPayableMap.get(companyId));
        return companyIdPayableMap.getOrDefault(companyId, new ArrayList<>());
    }

    /**
     * Get first invoice status by contract ids
     *
     * @param contractIds
     * @param month       month filter (= month)
     * @param year        year filter (= year)
     * @return <ul>
     * <li>Empty map if empty input or no data</li>
     * <li>Map if contract id ->> invoice status</li>
     * </ul>
     */
    public Map<Long, InvoiceDetail> getInvoiceDetailBasedOnContracts(Set<Long> contractIds, Integer month, Integer year) {
        var query = entityManager.createNativeQuery(
                "SELECT contract_id, status, id, invoice_no, company_payable_id FROM (" +
                        "SELECT DISTINCT  " +
                        "CAST(JSONB_ARRAY_ELEMENTS(i.line_items)->>'contractId' AS BIGINT) AS contract_id, " +
                        "i.status,  " +
                        "i.id,  " +
                        "i.invoice_no,  " +
                        "i.company_payable_id,  " +
                        "DENSE_RANK() OVER (PARTITION BY JSONB_ARRAY_ELEMENTS(i.line_items)->>'contractId' ORDER BY i.created_on DESC) AS rank " +
                        "FROM payable.invoice i " +
                        "INNER JOIN payable.company_payable cp ON i.company_payable_id  = cp.id  " +
                        "WHERE i.type = 'GROSS' " +
                        "AND cp.month = :month AND cp.year = :year " +
                        ") AS ranked_contract_invoice " +
                        "WHERE " +
                        "rank  = 1 " +
                        "AND contract_id IS NOT NULL " +
                        "AND contract_id IN (:contractIds)"
        );

        query.setParameter("month", month);
        query.setParameter("year", year);
        query.setParameter("contractIds", contractIds);

        log.info("getFirstInvoiceStatusBasedOnContracts: {}", query);

        List<Object[]> results = query.getResultList();

        if (CollectionUtils.isEmpty(results)) {
            log.info("There's no return result for contractIds: {}, month: {}, year: {}", contractIds, month, year);
            return Collections.emptyMap();
        }

        return results.stream()
                .collect(Collectors.toMap(
                        k -> (Long) (k[0]),
                        v -> new InvoiceDetail((Long) (v[4]), (Long) (v[2]), (String) (v[3]), InvoiceStatus.valueOf(v[1].toString()))));
    }

    private List<Object[]> getCompanyPayableInvoiceIdPairFromDatabase(long companyId, int month, int year) {
        var query = entityManager.createNativeQuery(
                """
                        select cp.company_id, cp.id as payableId, i.id, i.reference as jpaInvoiceId
                        from payable.invoice i inner join payable.company_payable cp on i.company_payable_id = cp.id
                        inner join company.company c on c.id = cp.company_id
                        inner join company.legal_entity le on c.primary_legal_entity_id = le.id
                        left outer join company.address ad on le.address_id = ad.id
                        where i.reference ~* ? and i.status != 'VOIDED' and i.status != 'DELETED' and cp.company_id = ? 
                        and (i.reason != 'FIRST_INVOICE_SUPPLEMENTARY' or i.reason is null)
                        order by cp.created_on
                        """
        );

        query.setParameter(1, getRegexForFirstInvoiceReference(month, year));
        query.setParameter(2, companyId);

        return query.getResultList();
    }

    private List<Object[]> getSecondInvoiceCompanyPayableInvoiceIdPairFromDatabase(long companyId, int month, int year) {
        var query = entityManager.createNativeQuery(
                """
                        select cp.company_id, cp.id as payableId, i.id as jpaInvoiceId
                        from payable.invoice i inner join payable.company_payable cp on i.company_payable_id = cp.id
                        inner join company.company c on c.id = cp.company_id
                        inner join company.legal_entity le on c.primary_legal_entity_id = le.id
                        left outer join company.address ad on le.address_id = ad.id
                        where i.reference ~* ? and i.status != 'VOIDED' and i.status != 'DELETED' and cp.company_id = ?
                        and i.reference NOT ILIKE '%gross salary%'
                        order by cp.created_on
                        """
        );

        query.setParameter(1, getRegexForSecondInvoiceReference(month, year));
        query.setParameter(2, companyId);

        return query.getResultList();
    }

    private static String getMonthName(Integer month) {
        return Month.of(month).getDisplayName(TextStyle.SHORT_STANDALONE, Locale.getDefault());
    }

    private static String getYear(Integer year) {
        return String.valueOf(year % 2000);
    }

    public static String getRegexForFirstInvoiceReference(Integer month, Integer year) {
        return String.format(
                FIRST_INVOICE_REFERENCE_REGEX,
                getMonthName(month).toLowerCase(),
                year,
                getYear(year)
        );
    }

    public static String getRegexForSecondInvoiceReference(Integer month, Integer year) {
        return String.format(
                SECOND_INVOICE_REFERENCE_REGEX,
                getMonthName(month).toLowerCase(),
                year,
                getYear(year)
        );
    }

    public List<JpaInvoice> getSupplementaryInvoices(Long companyId, MonthYearInput monthYearInput) {
        return getSupplementaryInvoices(companyId, monthYearInput.getMonth(), monthYearInput.getYear());
    }

    public List<JpaInvoice> getSupplementaryInvoices(Long companyId, Integer month, Integer year) {
        var reference = InvoiceFetcher.generateSupplementaryInvoiceReference(month, year);
        return jpaInvoiceRepository.findActiveInvoicesByCompanyIdAndReference(companyId, reference)
                .stream()
                .filter(invoice -> invoice.getReason() == InvoiceReason.FIRST_INVOICE_SUPPLEMENTARY)
                .toList();
    }

    public List<JpaInvoice> getFirstInvoicesForCompany(Long companyId, int year, int month) {
        var reference = InvoiceFetcher.generateFirstInvoiceReference(month, year);
        return jpaInvoiceRepository.findActiveInvoicesByCompanyIdAndReference(companyId, reference);
    }

    public static String generateFirstInvoiceReference(Integer month, Integer year) {
        return String.format(
                "%s'%s Gross Salary - EOR",
                getMonthName(month),
                getYear(year)
        );
    }

    public static String generateSupplementaryInvoiceReference(Integer month, Integer year) {
        return String.format(
                "%s'%s - Variable Pay Funding",
                getMonthName(month),
                getYear(year)
        );
    }

    public List<Pair<Long, Long>> getGPServiceInvoices(Long companyId, LocalDateTime from, LocalDateTime to) {
        List<JpaInvoice> invoices = jpaInvoiceRepository.findActiveGpServiceInvoicesByCompanyAndDateRange(
                InvoiceType.GP_SERVICE_INVOICE,
                companyId,
                from.toLocalDate(),
                to.toLocalDate()
        );

        return invoices.stream()
                .map(invoice -> Pair.of(invoice.getCompanyPayable().getId(), invoice.getId()))
                .toList();
    }


}
