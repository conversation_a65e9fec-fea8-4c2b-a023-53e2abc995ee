package com.multiplier.core.payable.report;

import com.multiplier.core.payable.report.collector.PerformanceMonitor;
import com.multiplier.core.payable.report.generator.DefaultIsrReportRowGenerator;
import com.multiplier.core.payable.report.generator.IsrAnnualPlanFeeRowGenerator;
import com.multiplier.core.payable.report.generator.IsrBankFeeReportRowGenerator;
import com.multiplier.core.payable.report.generator.IsrProcessingFeeReportRowGenerator;
import com.multiplier.core.payable.report.generator.IsrVatRowGenerator;
import com.multiplier.core.payable.report.generator.domain.ReportSheet;
import com.multiplier.core.payable.service.dataholder.SourceReportData;
import com.multiplier.core.payable.service.dataholder.SourceReportRowType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
@Qualifier("v2")
public class CompanyInvoiceSourceReportV2 implements WorkbookBuilder {

    private static final List<String> COLUMN_NAMES_TO_EXCLUDE = List.of("Id");
    private static final String SHEET_NAME = "Invoice Report";

    private final DefaultIsrReportRowGenerator defaultIsrReportRowGenerator;

    private final IsrProcessingFeeReportRowGenerator isrProcessingFeeReportRowGenerator;
    private final IsrAnnualPlanFeeRowGenerator isrAnnualPlanFeeRowGenerator;
    private final IsrBankFeeReportRowGenerator isrBankFeeReportRowGenerator;
    private final IsrVatRowGenerator isrVatRowGenerator;

    private final SheetBuilder sheetBuilder;

    private final PerformanceMonitor performanceMonitor;

    @Override
    public Workbook createWorkbookAndMonitor(final List<SourceReportData> reportData, final Integer month, final Integer year) {
        if (reportData.isEmpty()) {
            log.warn("No data to generate report for month {} year {}", month, year);
            return null;
        }
        var input = " for company ID " + reportData.get(0).getCompanyId() + " month " + month + " year " + year;
        var sheets = performanceMonitor.monitor("convert member pay to report sheet V2" + input, () -> convertMemberPayToReportSheetExceptId(reportData));
        return performanceMonitor.monitor("build sheet V2" + input, () -> sheetBuilder.build(sheets));
    }

    private List<ReportSheet> convertMemberPayToReportSheetExceptId(List<SourceReportData> memPayData) {
        var sheets = convertMemberPayToReportData(memPayData);

        for (var sheet : sheets) {
            var rows = sheet.getReportRows();

            for (var row : rows) {
                var cellValues = row.getReportRowCellValues();
                cellValues.removeIf(cell -> COLUMN_NAMES_TO_EXCLUDE.contains(cell.getColumnName()));
            }

            sheet.setReportRows(rows);
        }

        return sheets;
    }

    private List<ReportSheet> convertMemberPayToReportData(List<SourceReportData> reportData) {
        var companyId = reportData.get(0).getCompanyId();
        var partitionedData = reportData.stream()
                .collect(Collectors.groupingBy(SourceReportData::getRowType));

        var defaultRows = partitionedData.getOrDefault(SourceReportRowType.DEFAULT, new ArrayList<>());
        var processingFeeRows = partitionedData.getOrDefault(SourceReportRowType.PROCESSING_FEE, new ArrayList<>());
        var annualPlanFeeRows = partitionedData.getOrDefault(SourceReportRowType.ANNUAL_PLAN_FEE, new ArrayList<>());
        var vatRows = partitionedData.getOrDefault(SourceReportRowType.VALUE_ADDED_TAX, new ArrayList<>());
        var bankFeeRows = partitionedData.getOrDefault(SourceReportRowType.BANK_FEE, new ArrayList<>());

        log.info("Found {} default rows, {} processing fee rows, {} annual plan fee rows, {} bank fee rows and {} vat rows for company ID {}",
                defaultRows.size(), processingFeeRows.size(), annualPlanFeeRows.size(), bankFeeRows.size(), vatRows.size(), companyId);

        var rows = Stream.of(
                        defaultIsrReportRowGenerator.generate(defaultRows),
                        isrProcessingFeeReportRowGenerator.generate(processingFeeRows),
                        isrAnnualPlanFeeRowGenerator.generate(annualPlanFeeRows),
                        isrVatRowGenerator.generate(vatRows),
                        isrBankFeeReportRowGenerator.generate(bankFeeRows)

                )
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .toList();
        return List.of(ReportSheet.builder()
                .sheetName(SHEET_NAME)
                .reportRows(rows)
                .build());
    }

}

