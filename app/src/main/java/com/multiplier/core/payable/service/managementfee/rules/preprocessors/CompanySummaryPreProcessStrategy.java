package com.multiplier.core.payable.service.managementfee.rules.preprocessors;

import com.multiplier.payable.types.CountryCode;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
@Order(300)
public class CompanySummaryPreProcessStrategy extends ResidentContractPreProcessStrategy {

    @Override
    public List<PreProcessedContract> processOnlyResident(List<PreProcessedContract> contracts) {

        List<PreProcessedContract> currentContracts = contracts.stream()
                .filter(c -> c.isForcedContract() || !c.isEndedBeforeTheInvoiceMonth())
                .collect(Collectors.toList());

        Map<CountryCode, Long> countryCountMap = currentContracts.stream()
                .collect(
                        Collectors.groupingBy(PreProcessedContract::getCountry, Collectors.counting())
                );

        long globalCount = currentContracts.size();

        CompanySummary companySummary = new CompanySummary(globalCount, countryCountMap);

        contracts.forEach(c -> c.setCompanySummary(companySummary));

        return currentContracts;
    }
}
