package com.multiplier.core.payable.repository;

import com.multiplier.core.payable.repository.model.JpaMultiplierPayableItemStore;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface JpaMultiplierPayableItemStoreRepository extends JpaRepository<JpaMultiplierPayableItemStore, Long> {

    List<JpaMultiplierPayableItemStore> getJpaMultiplierPayableItemStoresByTransactionId(String transactionId);
}