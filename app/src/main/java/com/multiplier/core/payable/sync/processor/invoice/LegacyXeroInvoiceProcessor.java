package com.multiplier.core.payable.sync.processor.invoice;

import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.event.database.EventType;
import com.multiplier.core.payable.invoice.database.InvoiceService;
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository;
import com.multiplier.core.payable.repository.model.JpaCompanyPayable;
import com.multiplier.payable.types.PayableStatus;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * A class that handles a single legacy Xero invoice based on invoice data retrieved from Xero.
 */
@Component
@RequiredArgsConstructor
@Slf4j
class LegacyXeroInvoiceProcessor {

    private final JpaCompanyPayableRepository companyPayableRepository;
    private final ApplicationEventPublisher eventPublisher;
    private final InvoiceService invoiceService;

    @Transactional
    public InvoiceDTO process(InvoiceDTO externalInvoice) {
        log.info("Process Xero invoice with invoiceNo {}", externalInvoice.getXeroInvoiceNo());

        var currentInvoice = invoiceService.getByInvoiceNo(externalInvoice.getXeroInvoiceNo());
        var companyPayableId = currentInvoice.getCompanyPayableId();
        if (companyPayableId == null) {
            var message = String.format("The Xero invoice {} doesn't have a company payable", currentInvoice.getInvoiceNo());
            throw new IllegalArgumentException(message);
        }

        var companyPayableToUpdate = getCompanyPayableToUpdate(externalInvoice, companyPayableId);
        var updatedCompanyPayable = companyPayableRepository.save(companyPayableToUpdate);

        var invoiceToUpdate = updateRequiredFields(currentInvoice, externalInvoice);
        var updatedInvoice = invoiceService.save(invoiceToUpdate);

        var updatedInvoiceWithCompanyPayable = updatedInvoice.toBuilder()
                .companyPayableId(updatedCompanyPayable.getId())
                .companyId(updatedCompanyPayable.getCompanyId())
                .build();

        fireEvent(currentInvoice, updatedInvoiceWithCompanyPayable);

        return updatedInvoiceWithCompanyPayable;
    }

    /**
     * Delegate further processing to event handlers.
     * Advantages:
     * + Commit the transaction before doing further operations such as sending emails, notify external services
     * + Decouple "after operations" for testability and maintainability
     * + Ability to add multiple event handlers such as sendEmailHandler, notifyExternalServiceHandler
     */
    private void fireEvent(InvoiceDTO beforeInvoice, InvoiceDTO afterInvoice) {
        log.info("Publish event with beforeInvoiceId {}, beforeStatus {}, " +
                        "afterInvoiceId {}, afterStatus {}",
                beforeInvoice.getExternalId(), beforeInvoice.getStatus(),
                afterInvoice.getExternalId(), afterInvoice.getStatus());
        eventPublisher.publishEvent(
                InvoiceAppEvent.builder()
                        .source(this)
                        .eventType(EventType.UPDATE)
                        .beforeInvoice(beforeInvoice)
                        .afterInvoice(afterInvoice)
                        .legacyXeroInvoice(true)
                        .build());
    }

    JpaCompanyPayable getCompanyPayableToUpdate(InvoiceDTO externalInvoice, Long companyPayableId) {
        var currentCompanyPayable = companyPayableRepository.getById(companyPayableId);
        return currentCompanyPayable.toBuilder()
                .status(Enum.valueOf(PayableStatus.class, externalInvoice.getStatus().name()))
                .build();
    }

    InvoiceDTO updateRequiredFields(@NonNull @NonFinal InvoiceDTO currentInvoiceDto, @NonNull InvoiceDTO externalInvoice) {
        return currentInvoiceDto.toBuilder()
                .amountDue(externalInvoice.getAmountDue())
                .amountPaid(externalInvoice.getAmountPaid())
                .status(externalInvoice.getStatus())
                .syncedTime(externalInvoice.getSyncedTime())
                .reason(externalInvoice.getReason())
                .type(externalInvoice.getType())
                .fullyPaidOnDate(externalInvoice.getFullyPaidOnDate())
                .build();

    }

    boolean isLegacyXeroInvoice(InvoiceDTO invoiceDTO) {
        return invoiceDTO.getXeroInvoiceNo() != null && !invoiceDTO.getXeroInvoiceNo().isEmpty();
    }

}
