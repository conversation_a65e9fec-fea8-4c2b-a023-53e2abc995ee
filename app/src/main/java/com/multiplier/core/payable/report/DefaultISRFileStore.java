package com.multiplier.core.payable.report;

import com.multiplier.core.payable.report.database.JpaInvoiceSourceReport;
import com.multiplier.core.payable.report.database.JpaInvoiceSourceReportRepository;
import com.multiplier.core.payable.report.domain.IsrKey;
import com.multiplier.core.payable.report.domain.RetrieveReportFileRequest;
import com.multiplier.core.payable.report.externalfilesystem.FileRetrieveRequest;
import com.multiplier.core.payable.report.externalfilesystem.FileStore;
import com.multiplier.core.payable.report.externalfilesystem.FileStoreRequest;
import com.multiplier.core.payable.service.dataholder.ISRFileMetaData;
import com.multiplier.common.exception.MplBusinessException;
import com.multiplier.common.exception.MplSystemException;
import com.multiplier.core.exception.PayableErrorCode;
import com.multiplier.core.payable.service.exception.ValidationException;
import com.multiplier.core.util.CurrentUserMetaProvider;
import com.multiplier.payable.types.MonthYear;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * This class wraps the FileStore and MetaData store into one complete ISR file storage.
 */
@Component
@AllArgsConstructor
@Slf4j
public class DefaultISRFileStore implements ISRFileStore {

    private final JpaInvoiceSourceReportRepository jpaISRRepository;
    private final FileStore fileStore;
    private final ISRFileMetaDataToJpaISRMapper mapper;
    private final FileStoreRequestFactory fileStoreRequestFactory;
    private final IsrKeyBasedAuthorizationFeature isrKeyBasedAuthorizationFeature;
    private final CurrentUserMetaProvider currentUserMetaProvider;

    @Override
    public void createMetaDataRecordFor(ISRFileMetaData iSRFileMetaData) {
        var jpaInvoiceSourceReport = mapper.map(iSRFileMetaData);
        jpaISRRepository.save(jpaInvoiceSourceReport);
    }

    @Override
    public void createMultipleMetaDataRecordFor(Collection<ISRFileMetaData> isrFileMetaDataList) {
        List<JpaInvoiceSourceReport> jpaInvoiceSourceReportList = isrFileMetaDataList.stream()
                .map(mapper::map)
                .collect(Collectors.toList());
        jpaISRRepository.saveAll(jpaInvoiceSourceReportList);
    }

    @Override
    public String store(IsrKey isrKey, String fileDataBlob) {
        try (InputStream inputStream = new ByteArrayInputStream(Base64.getDecoder().decode(fileDataBlob))) {
            var jpaInvoiceSourceReport = getJpaInvoiceSourceReportByKey(isrKey);
            FileStoreRequest request = fileStoreRequestFactory.createStoreRequest(jpaInvoiceSourceReport, inputStream);
            fileStore.uploadAndMonitor(request);
            return request.getFileNameWithoutExtension();
        } catch (IOException e) {
            throw new MplSystemException(PayableErrorCode.FILE_UPLOAD_FAILED,
                "Error while uploading ISR to S3 for " + isrKey.serialize());
        }
    }

    @Override
    public ISRFile retrieve(RetrieveReportFileRequest request) {
        log.info("Retrieving ISR file for {}", request.getKey().serialize());
        var isrKey = request.getKey();
        if (BooleanUtils.isFalse(request.getAuthorized())) {
            var keyAuthorizedJpaIsr = authorizeByIsrKey(request);
            return retrieveSingleFile(fileStoreRequestFactory.createRetrieveRequest(keyAuthorizedJpaIsr));
        }
        var report = getReportsByKeysWithFallback(List.of(isrKey)).get(isrKey);
        return retrieveSingleFile(fileStoreRequestFactory.createRetrieveRequest(report));
    }

    private JpaInvoiceSourceReport authorizeByIsrKey(RetrieveReportFileRequest request) {
        var jpaIsr = jpaISRRepository.findByHash(request.getAuthCode());
        var requestIsrKey = request.getKey();
        if (isrKeyBasedAuthorizationFeature.isEnabled()) {
            return jpaIsr.filter(isr -> isr.toISRKey().equals(requestIsrKey))
                .orElseThrow(() -> new ValidationException("Authentication Code is invalid for " + requestIsrKey.serialize()));
        }
        return jpaIsr.orElseThrow(() -> new ValidationException("Authentication Code is invalid for " + requestIsrKey.serialize()));
    }

    @Override
    public List<ISRFile> retrieveMultiple(RetrieveReportFileRequest request) {
        var isrKey = request.getKey();
        var serializedKey = isrKey.serialize();
        final var jpaInvoiceSourceReports = getAllByKey(isrKey);
        log.info("Retrieving ISR files for {}", serializedKey);
        if (CollectionUtils.isEmpty(jpaInvoiceSourceReports)) {
            throw new MplBusinessException(PayableErrorCode.INVOICE_SOURCE_REPORT_NOT_FOUND,
                "Invoice Report is not found for " + serializedKey);
        }
        return jpaInvoiceSourceReports.stream()
                .map(fileStoreRequestFactory::createRetrieveRequest)
                .distinct()
                .map(this::retrieveSingleFile)
                .toList();
    }

    @Override
    public Map<IsrKey, Boolean> reportExistForKeys(List<IsrKey> isrKeys) {
        var existingReportsByKeys = getReportsByKeys(isrKeys);
        return isrKeys.stream()
                .collect(Collectors.toMap(Function.identity(), existingReportsByKeys::containsKey));
    }

    @Override
    public Map<IsrKey, Boolean> reportExistForKeysWithFallback(final List<IsrKey> isrKeys) {
        var existingReportsByKeys = getReportsByKeysWithFallback(isrKeys);
        return isrKeys.stream()
                .collect(Collectors.toMap(Function.identity(), existingReportsByKeys::containsKey));
    }

    @Override
    public void store(String originalFilename, InputStream content) {
        jpaISRRepository.findFirstByFileNameOrderByIdDesc(originalFilename)
                .ifPresentOrElse(
                        jpaInvoiceSourceReport -> {
                            log.debug("Uploading the file to replace existing. fileName={} companyId={} month={} year={} payableId={}",
                                    originalFilename,
                                    jpaInvoiceSourceReport.getCompanyId(),
                                    jpaInvoiceSourceReport.getMonth(),
                                    jpaInvoiceSourceReport.getYear(),
                                    jpaInvoiceSourceReport.getCompanyPayableId()
                            );
                            FileStoreRequest request = fileStoreRequestFactory.createStoreRequest(jpaInvoiceSourceReport, content);
                            fileStore.uploadAndMonitor(request);
                            jpaInvoiceSourceReport.setManuallyUploadedBy(currentUserMetaProvider.getCurrentUserId());
                            jpaISRRepository.save(jpaInvoiceSourceReport);
                        },
                        () -> {
                            throw new MplSystemException(PayableErrorCode.FILE_METADATA_NOT_FOUND,
                                "No file name '" + originalFilename + "' found in the database");
                        }
                );
    }

    @Override
    public Collection<JpaInvoiceSourceReport> retrieveExistingMetaDataRecord(Collection<ISRFileMetaData> isrFileMetaDataCollection) {
        Map<Pair<Integer, Integer>, Collection<Long>> monthYearToCompanyIds = new HashMap<>();
        var existingIsrList = new ArrayList<JpaInvoiceSourceReport>();
        for (ISRFileMetaData isrFileMetaData : isrFileMetaDataCollection) {
            int month = isrFileMetaData.getMonth();
            int year = isrFileMetaData.getYear();
            Pair<Integer, Integer> monthYear = Pair.of(month, year);
            Collection<Long> companyIds = monthYearToCompanyIds.get(monthYear);
            if (companyIds == null) {
                companyIds = new ArrayList<>();
            }
            companyIds.add(isrFileMetaData.getCompanyId());
            monthYearToCompanyIds.put(monthYear, companyIds);
        }
        for (var entry : monthYearToCompanyIds.entrySet()) {
            Pair<Integer, Integer> monthYear = entry.getKey();
            int month = monthYear.getKey();
            int year = monthYear.getValue();
            Collection<Long> companyIds = entry.getValue();
            var jpaList = jpaISRRepository.findByMonthAndYearAndCompanyIdIn(month, year, companyIds);
            if (CollectionUtils.isNotEmpty(jpaList)) {
                existingIsrList.addAll(jpaList);
            }
        }
        return existingIsrList;
    }

    private List<JpaInvoiceSourceReport> getAllByKey(IsrKey isrKey) {
        return jpaISRRepository.findByCompanyIdAndMonthAndYear(isrKey.getCompanyId(), isrKey.getMonth(), isrKey.getYear());
    }

    private JpaInvoiceSourceReport getJpaInvoiceSourceReportByKey(IsrKey isrKey) {
        boolean allowFallback = isrKey.getCompanyPayableId() == null;
        var reportByKey = allowFallback ? getReportsByKeysWithFallback(List.of(isrKey)) : getReportsByKeys(List.of(isrKey));
        var result = reportByKey.get(isrKey);
        if (result == null) {
            throw new MplSystemException(PayableErrorCode.FILE_METADATA_NOT_FOUND,
                "Invoice Report is not found for " + isrKey.serialize());
        }
        return result;
    }

    private ISRFile retrieveSingleFile(FileRetrieveRequest fileRetrieveRequest) {
        var externalFileName = fileRetrieveRequest.getUid();
        log.info("Retrieving ISR file {} externally", externalFileName);
        try (var inputStream = fileStore.retrieve(fileRetrieveRequest)) {
            String data = Base64.getEncoder().encodeToString(inputStream.readAllBytes());
            return new ExcelISRFile(fileRetrieveRequest.getFileName(), data);
        } catch (IOException e) {
            log.error("Error while reading the ISR for {}", externalFileName, e);
            throw new MplSystemException(PayableErrorCode.FILE_PROCESSING_ERROR,
                "Error while reading the ISR file input stream for " + externalFileName);
        } catch (Exception e) {
            log.error("Error while retrieving the ISR for {}", externalFileName, e);
            throw new MplSystemException(PayableErrorCode.FILE_RETRIEVAL_FAILED,
                "Error while retrieving the ISR for " + externalFileName);
        }
    }

    private Map<IsrKey, JpaInvoiceSourceReport> getReportsByKeys(List<IsrKey> isrKeys) {
        var companyPayableIds = isrKeys.stream()
                .map(IsrKey::getCompanyPayableId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        var isrKeyToReport = new HashMap<IsrKey, JpaInvoiceSourceReport>();
        if (!companyPayableIds.isEmpty()) {
             jpaISRRepository.findByCompanyPayableIdIn(companyPayableIds)
                     .forEach(jpaInvoiceSourceReport -> isrKeyToReport.put(jpaInvoiceSourceReport.toISRKey(), jpaInvoiceSourceReport));
        }
        return isrKeyToReport;
    }

    private Map<IsrKey, JpaInvoiceSourceReport> getReportsByKeysWithFallback(List<IsrKey> isrKeys) {
        var isrKeyToReport = getReportsByKeys(isrKeys);
        var isrKeysWithoutReport = isrKeys.stream().filter(isrKey -> !isrKeyToReport.containsKey(isrKey)).toList();
        isrKeyToReport.putAll(fallback(isrKeysWithoutReport));
        return isrKeyToReport;
    }

    private Map<IsrKey, JpaInvoiceSourceReport> fallback(List<IsrKey> isrKeys) {
        var result = new HashMap<IsrKey, JpaInvoiceSourceReport>();
        var fallbackKeys = isrKeys.stream().map(isrKey -> isrKey.toBuilder().companyPayableId(null).build()).collect(Collectors.toSet());
        var monthYearToCompanyIds = fallbackKeys.stream()
                .collect(Collectors.groupingBy(isrKey -> MonthYear.newBuilder().month(isrKey.getMonth()).year(isrKey.getYear()).build(),
                        Collectors.mapping(IsrKey::getCompanyId, Collectors.toList())));

        var jpaInvoiceSourceReports = new HashMap<IsrKey, JpaInvoiceSourceReport>();
        for (var entry : monthYearToCompanyIds.entrySet()) {
            var monthYear = entry.getKey();
            jpaISRRepository.findByMonthAndYearAndCompanyIdInAndCompanyPayableIdIsNull(monthYear.getMonth(), monthYear.getYear(), entry.getValue())
                    .forEach(jpaInvoiceSourceReport -> jpaInvoiceSourceReports.put(jpaInvoiceSourceReport.toISRKey(), jpaInvoiceSourceReport));
        }

        isrKeys.forEach(isrKey -> {
            var fallbackKey = isrKey.toBuilder().companyPayableId(null).build();
            if (jpaInvoiceSourceReports.containsKey(fallbackKey)) {
                result.put(isrKey, jpaInvoiceSourceReports.get(fallbackKey));
            }
        });
        return result;
    }

}
