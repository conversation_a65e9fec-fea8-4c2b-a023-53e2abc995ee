package com.multiplier.core.payable.customer;

import com.multiplier.core.config.featureflag.FeatureFlagService;
import com.multiplier.core.payable.adapters.api.CustomerDto;
import com.multiplier.core.payable.adapters.api.ThirdPartyServiceException;
import com.multiplier.core.payable.adapters.netsuite.client.NetsuiteRestClient;
import com.multiplier.core.payable.adapters.netsuite.exception.NetsuiteAdapterException;
import com.multiplier.core.payable.adapters.netsuite.webservices.NetsuiteWebserviceClient;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class NetsuiteCustomerRestActionAdapter implements CustomerActionAdapter {
    private final NetsuiteWebserviceClient netsuiteWebserviceClient;
    private final NetsuiteRestClient netsuiteRestClient;
    private final NetsuiteCustomerMapper netsuiteCustomerMapper;
    private final NetsuiteCustomerSearchAdapter netsuiteCustomerSearchAdapter;
    private final FeatureFlagService featureFlagService;

    @Override
    public boolean enabled() {
        var featureResult = featureFlagService.feature("netsuite-should-use-web-service-for-customer", Map.of());
        return featureResult.off();
    }

    @Override
    public String createCustomer(CustomerDto customerDto) throws NetsuiteAdapterException {
        log.info("Creating customer in Netsuite for companyId {}", customerDto.getCompanyId());
        try {
            com.multiplier.core.payable.adapters.netsuite.models.Customer customer = netsuiteCustomerMapper.map(customerDto);
            ResponseEntity<Void> response = netsuiteRestClient.createCustomer(customer);

            var headerLocation = response.getHeaders().getLocation();
            if (headerLocation == null) {
                throw new NetsuiteAdapterException("Error occur parsing Netsuite API response. Location header cannot be found");
            }
            String[] headerLocationParts = response.getHeaders().getLocation().toString().split("/");
            return Optional.ofNullable(headerLocationParts[headerLocationParts.length - 1])
                    .orElseThrow(() -> new ThirdPartyServiceException("Netsuite didn't return an id"));

        } catch (NetsuiteAdapterException e) {
          throw e;
        } catch (FeignException fe) {
            throw new NetsuiteAdapterException(String.format("Error occur during createCustomer API call. {%s}", fe.getMessage()), fe);
        } catch (Exception e) {
            throw new NetsuiteAdapterException(String.format("An exception occurred while creating customer. {%s}", e.getMessage()), e);
        }
    }

    @Override
    public void deleteById(String customerInternalId) {
        netsuiteWebserviceClient.deleteCustomer(customerInternalId);
    }

    @Override
    public void updateCustomer(CustomerDto customerDto, @Nullable CustomerDto existingCustomer) throws NetsuiteAdapterException {
        log.info("Update customer in Netsuite for companyId {} externalId {}", customerDto.getCompanyId(), customerDto.getCompanyId());

        try {
            com.multiplier.core.payable.adapters.netsuite.models.Customer nsCustomer =
                    netsuiteCustomerMapper.map(customerDto, existingCustomer);
            val response = netsuiteRestClient.updateCustomer(nsCustomer.getId(), nsCustomer);
            if (response.getStatusCode().equals(HttpStatus.NO_CONTENT)) {
                log.info("Updated customer successfully in Netsuite. companyId: {} customerId: {}",
                        customerDto.getCompanyId(), customerDto.getCustomerId());
            } else {
                log.info("Failed to update customer in Netsuite. companyId: {} customerId: {} responseStatus={}",
                        customerDto.getCompanyId(), customerDto.getCustomerId(), response.getStatusCode());
            }
        } catch (FeignException fe) {
            throw new NetsuiteAdapterException(String.format("Error occur during updateCustomers API call. {%s}", fe.getMessage()), fe);
        } catch (Exception e) {
            throw new NetsuiteAdapterException(String.format("An exception occurred while updating customer. {%s}", e.getMessage()), e);
        }
    }
}
