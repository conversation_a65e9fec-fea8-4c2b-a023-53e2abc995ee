package com.multiplier.core.payable.creditnote.database;

import lombok.*;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

@Builder(toBuilder = true)
@Getter
@Jacksonized
@EqualsAndHashCode
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class PageDto<T> { // Can be moved to a common package for reusing

    private final List<T> content;
    private final int count;
    private final int pageNumber;
    private final int pageSize;
}
