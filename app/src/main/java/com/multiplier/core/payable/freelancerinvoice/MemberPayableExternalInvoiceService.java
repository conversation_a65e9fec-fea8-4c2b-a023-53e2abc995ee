package com.multiplier.core.payable.freelancerinvoice;

import com.multiplier.core.payable.adapters.PaymentBundleServiceAdapter;
import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.adapters.api.InvoiceGenerationFactory;
import com.multiplier.core.payable.freelancer.FreelancerInvoiceFactory;
import com.multiplier.core.payable.repository.model.ExternalSystem;
import com.multiplier.core.payable.service.CompanyBindingService;
import com.multiplier.core.payable.service.InvoiceGenerateService;
import com.multiplier.core.payable.service.InvoiceNotificationService;
import com.multiplier.core.payable.service.exception.InvoiceGenerationErrorCode;
import com.multiplier.core.payable.service.exception.InvoiceGenerationException;
import com.multiplier.core.payable.service.exception.ValidationException;
import com.multiplier.payable.grpc.schema.ExternalInvoiceCreationResponse;
import com.multiplier.payable.grpc.schema.FreelancerInvoiceLineItem;
import com.multiplier.payable.types.CurrencyCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.multiplier.core.payable.adapters.api.LineItemType.PAYMENT_FEE;

@RequiredArgsConstructor
@Component
@Slf4j
public class MemberPayableExternalInvoiceService {
    public static final String BANK_TRANSFER_FEE_DESCRIPTION = "Bank transfer fee";
    public static final String CREDIT_CARD_FEE_DESCRIPTION = "Credit card fee";
    public static final String DIRECT_DEBIT_FEE_DESCRIPTION = "Direct debit fee";

    private final InvoiceGenerateService invoiceGenerateService;
    private final InvoiceGenerationFactory invoiceGenerationFactory;
    private final CompanyBindingService companyBindingService;

    private final PaymentBundleServiceAdapter paymentBundleServiceAdapter;
    private final InvoiceNotificationService invoiceNotificationService;
    private final FreelancerInvoiceFactory freelancerInvoiceFactory;
    private final MemberPayableProcessingFeeMapperService memberPayableProcessingFeeMapperService;

    public ExternalInvoiceCreationResponse createExternalInvoiceForMemberPayables(long companyId, List<FreelancerInvoiceLineItem> invoiceLineItems) {
        log.info("Starting Creating external invoice for member payables for company id :{}", companyId);

        return freelancerInvoiceFactory.create(
                companyId,
                invoiceLineItems
        );
    }

    public void updatePaymentBundle(Long companyPayableId, String externalInvoiceId) {
        paymentBundleServiceAdapter.updatePaymentBundleOnXeroInvoiceUpdateEvent(companyPayableId, externalInvoiceId);
    }

    public void onInvoiceUpdatedToPaid(InvoiceDTO updatedInvoice, Long companyPayableId) {
        log.info("[ExternalSystemSync] Invoice is PAID, sending email to company billing/admin, external invoice id : {}, companyPayable id : {}", updatedInvoice.getExternalId(), companyPayableId);
        // Send email to company admin when an Invoice is PAID
        invoiceNotificationService.sendInvoiceIsPaidNotificationToCustomer(updatedInvoice);

        log.info("[ExternalSystemSync] Updating payment bundle triggered for external invoice update, external invoice id : {}, companyPayable id : {}",
                updatedInvoice.getExternalId(), companyPayableId);
        updatePaymentBundle(companyPayableId, updatedInvoice.getExternalId());
    }

    public void updateExternalInvoiceProcessingFee(String externalInvoiceId,
            String billingCurrency,
            FreelancerInvoiceLineItem newProcessingFeeLineItem) {
        if (StringUtils.isEmpty(externalInvoiceId) || StringUtils.isEmpty(billingCurrency) || newProcessingFeeLineItem == null) {
            log.error("Can not update external invoice from null inputs: externalInvoiceId={}, billingCurrency={}, newProcessingFeeLineItem={}", externalInvoiceId, billingCurrency, newProcessingFeeLineItem);
            return;
        }
        log.info("Updating processing fee for external invoice ID = {} to {} {}",
                externalInvoiceId,
                newProcessingFeeLineItem.getAmount(),
                billingCurrency);

        val invoiceAdapter = invoiceGenerationFactory.getAdapterForExternalSystem(ExternalSystem.NETSUITE);
        val invoices = invoiceAdapter.getInvoices(List.of(externalInvoiceId));
        if (invoices.size() != 1) {
            throw new InvoiceGenerationException(InvoiceGenerationErrorCode.MPE_INVOICE_NOT_FOUND);
        }
        val invoiceDTO = invoices.get(0);

        boolean successfulUpdate = this.updateInvoiceLineItemOnNetsuite(invoiceDTO, billingCurrency, newProcessingFeeLineItem);
        if (!successfulUpdate) {
            log.error("Fail to update invoice line item on Netsuite for invoice with invoiceNo {}, externalId {}",
                    invoiceDTO.getInvoiceNo(), invoiceDTO.getExternalId());
            throw new InvoiceGenerationException(InvoiceGenerationErrorCode.MPE_INTERNAL_SERVER_ERROR);
        }
        log.info("Finished update Invoice externalID {} successfully", externalInvoiceId);
    }

    private boolean updateInvoiceLineItemOnNetsuite(InvoiceDTO invoiceDTO,
            String billingCurrency,
            FreelancerInvoiceLineItem newProcessingFeeLineItem) {
        val oldProcessingFeeLineItem = invoiceDTO.getLineItems()
                .stream()
                .filter(item -> PAYMENT_FEE.equals(item.getItemType()) &&
                        Set.of(BANK_TRANSFER_FEE_DESCRIPTION, CREDIT_CARD_FEE_DESCRIPTION, DIRECT_DEBIT_FEE_DESCRIPTION)
                                .contains(item.getDescription()))
                .findFirst()
                .orElse(null);
        if (newProcessingFeeLineItem.getAmount() == 0.0 && oldProcessingFeeLineItem == null) {
            // Do nothing because there's no changes in the processing fee line item
            return true;
        }

        val companyBinding = companyBindingService.getBindingFromCustomerId(invoiceDTO.getCustomerId())
                .orElseThrow(() -> new InvoiceGenerationException(InvoiceGenerationErrorCode.MPE_COMPANY_BINDING_NOT_FOUND));
        val companyId = companyBinding.getCompanyId();
        val payableItem = memberPayableProcessingFeeMapperService.map(
                newProcessingFeeLineItem,
                billingCurrency,
                companyId
        );
        if (payableItem == null) {
            throw new ValidationException("Cannot get payable item for line item type: " + newProcessingFeeLineItem.getLineItemType());
        }
        val lineItemDTOToUpdate = Optional.ofNullable(oldProcessingFeeLineItem)
                .map(oldLine -> {
                    log.debug("Update Netsuite invoice id {} line number {} with new unitAmount {} and description {}",
                            invoiceDTO.getExternalId(), oldLine.getLine(), newProcessingFeeLineItem.getAmount(), payableItem.getDescription());
                    val newAmount = BigDecimal.valueOf(newProcessingFeeLineItem.getAmount()).setScale(2, RoundingMode.HALF_EVEN).doubleValue();
                    oldLine.setUnitAmount(newAmount);
                    oldLine.setDescription(payableItem.getDescription());
                    oldLine.setAmountInBaseCurrency(newAmount);
                    return oldLine;
                }).orElseGet(() -> {
                    log.debug("Add new line to Netsuite invoice id {}", invoiceDTO.getExternalId());
                    return invoiceGenerateService.getInvoiceLineItemDTOS(null,
                            CurrencyCode.valueOf(billingCurrency),
                            List.of(payableItem),
                            false
                    ).stream().findFirst().orElseThrow(() ->
                            new InvoiceGenerationException(InvoiceGenerationErrorCode.MPE_INTERNAL_SERVER_ERROR,
                                    companyId, null,
                                    "Cannot create new line item for type " + newProcessingFeeLineItem.getLineItemType()
                                            + " of invoice id " + invoiceDTO.getExternalId()));
                });
        return invoiceGenerateService.updateInvoiceLineDTO(invoiceDTO.getExternalId(), lineItemDTOToUpdate);
    }

}
