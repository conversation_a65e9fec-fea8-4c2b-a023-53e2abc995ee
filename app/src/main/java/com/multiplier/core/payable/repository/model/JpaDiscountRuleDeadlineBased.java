package com.multiplier.core.payable.repository.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.multiplier.payable.types.DiscountRuleDeadlineBasedInput;
import com.multiplier.payable.types.DiscountRuleDeadlineType;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Accessors(fluent = true, chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
public class JpaDiscountRuleDeadlineBased implements Serializable  {

    private LocalDate deadline;
    private DiscountRuleDeadlineType deadlineType;

    public static JpaDiscountRuleDeadlineBased mapper(DiscountRuleDeadlineBasedInput source) {
        return source == null
                ? null
                : new JpaDiscountRuleDeadlineBased(source.getDeadline(), source.getDeadlineType());
    }

}
