package com.multiplier.core.payable.creditnote.database;

import lombok.*;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

@Builder(toBuilder = true)
@Getter
@Jacksonized
@EqualsAndHashCode
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
public class CreditNoteQueryResponseDto {

    private final List<CreditNoteDto> creditNotes;
    private final int count;
    private final int pageNumber;
    private final int pageSize;
}
