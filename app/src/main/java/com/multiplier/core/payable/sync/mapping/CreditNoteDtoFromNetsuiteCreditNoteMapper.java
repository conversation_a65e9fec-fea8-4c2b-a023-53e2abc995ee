package com.multiplier.core.payable.sync.mapping;

import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.adapters.netsuite.client.NetsuiteMappings;
import com.multiplier.core.payable.creditnote.database.CreditNoteDto;
import com.multiplier.core.payable.creditnote.database.CreditNoteItemDto;

import com.multiplier.core.util.IgnoreUnmappedMapperConfig;
import com.multiplier.payable.types.CreditNoteReason;
import com.multiplier.payable.types.CreditNoteStatus;
import com.multiplier.payable.types.CurrencyCode;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper(config = IgnoreUnmappedMapperConfig.class)
abstract class CreditNoteDtoFromNetsuiteCreditNoteMapper {

    protected LineItemMappingAdapter lineItemMappingAdapter;

    static String ITEM_TYPE_ID_MUST_NOT_BE_NULL = "itemType must not be null";

    @Mapping(target = "status", source = "status", qualifiedByName = "mapStatus")
    @Mapping(target = "currencyCode", source = "currencyId", qualifiedByName = "mapCurrencyCode")
    @Mapping(target = "reason", source = "reason", qualifiedByName = "mapReason")
    @Mapping(target = "externalSystem", constant = "NETSUITE")
    @Mapping(target = "externalInvoiceIds", source = "netsuiteCreditNote", qualifiedByName = "mapExternalInvoiceIds")
    @Mapping(target = "createdFromExternalInvoiceId", source = "createdFromExternalInvoiceId", qualifiedByName = "mapCreatedFromExternalInvoiceId")
    @Mapping(target = "month", expression = "java(netsuiteCreditNote.getCreatedDate() == null ? null : netsuiteCreditNote.getCreatedDate().getMonthValue())")
    @Mapping(target = "year", expression = "java(netsuiteCreditNote.getCreatedDate() == null ? null : netsuiteCreditNote.getCreatedDate().getYear())")
    public abstract CreditNoteDto map(NetsuiteCreditNote netsuiteCreditNote, @Context List<String> externalInvoiceIds, @Context String externalId);

    @Named("mapStatus")
    public CreditNoteStatus mapStatus(NetsuiteCreditNoteStatus status) {
        if (status == null) {
            return null;
        }
        return CreditNoteStatus.valueOf(status.name());
    }

    @Named("mapCurrencyCode")
    public CurrencyCode mapCurrencyCode(String currencyId) {
        if (currencyId == null || currencyId.isEmpty()) {
            return null;
        }
        return NetsuiteMappings.fromCurrencyId(currencyId);
    }

    @Named("mapReason")
    public CreditNoteReason mapReason(String reasonId) {
        if (reasonId == null || reasonId.isEmpty()) {
            return null;
        }
        return CreditNoteReason.valueOf(
                String.valueOf(com.multiplier.core.payable.creditnote.api.CreditNoteReason.getReasonForExternalId(reasonId))
        );
    }

    @Mapping(target = "itemType", source = "item", qualifiedByName = "mapItemType")
    @Mapping(target = "countryName", source = "classValue", qualifiedByName = "mapCountryName")
    @Mapping(target = "baseCurrency", source = "baseCurrency", qualifiedByName = "mapBaseCurrency")
    @Mapping(target = "startPayCycleDate", source = "revRecStartDate")
    @Mapping(target = "endPayCycleDate", source = "revRecEndDate")
    @Mapping(target = "companyPayableLineItemIds", source = "companyPayableLineItemIds")
    public abstract CreditNoteItemDto mapItem(CreditNoteItem item, @Context String externalId);

    @Named("mapItemType")
    public LineItemType mapItemType(CreditNoteItem item, @Context String externalId) {
        String itemTypeId = item.getItemTypeId();
        if (itemTypeId == null) {
            throw new IllegalArgumentException(ITEM_TYPE_ID_MUST_NOT_BE_NULL);
        }
        return lineItemMappingAdapter.mapCreditNoteLineItemType(item, externalId);
    }

    @Named("mapCountryName")
    public String mapCountryName(String countryNameId) {
        if (countryNameId == null) {
            return null;
        }
        return NetsuiteMappings.fromCountryId(countryNameId);
    }

    @Named("mapBaseCurrency")
    public CurrencyCode mapBaseCurrency(String currencyCode) {
        if (currencyCode == null || currencyCode.isEmpty()) {
            return null;
        }
        return CurrencyCode.valueOf(currencyCode.toUpperCase());
    }

    @Named("mapExternalInvoiceIds")
    public List<String> mapExternalInvoiceIds(NetsuiteCreditNote netsuiteCreditNote, @Context List<String> externalInvoiceIds) {
        return externalInvoiceIds;
    }

    @Named("mapCreatedFromExternalInvoiceId")
    public String mapCreatedFromExternalInvoiceId(String createdFromExternalInvoiceId) {
        return StringUtils.isEmpty(createdFromExternalInvoiceId) ? null : createdFromExternalInvoiceId;
    }

    @Mapping(target = "status", source = "status", qualifiedByName = "mapStatus")
    @Mapping(target = "currencyCode", source = "currencyId", qualifiedByName = "mapCurrencyCode")
    @Mapping(target = "reason", source = "reason", qualifiedByName = "mapReason")
    @Mapping(target = "externalSystem", constant = "NETSUITE")
    @Mapping(target = "createdFromExternalInvoiceId", source = "createdFromExternalInvoiceId", qualifiedByName = "mapCreatedFromExternalInvoiceId")
    @Mapping(target = "month", expression = "java(netsuiteCreditNote.getCreatedDate() == null ? null : netsuiteCreditNote.getCreatedDate().getMonthValue())")
    @Mapping(target = "year", expression = "java(netsuiteCreditNote.getCreatedDate() == null ? null : netsuiteCreditNote.getCreatedDate().getYear())")
    public abstract CreditNoteDto mapRelatedCreditNote(NetsuiteCreditNote netsuiteCreditNote, @Context String externalId);
}
