package com.multiplier.core.payable.freelancerinvoice;

import com.multiplier.core.payable.company.adapter.CompanyServiceAdapter;
import com.multiplier.core.payable.repository.model.JpaPayableItem;
import com.multiplier.core.payable.service.exception.InvoiceGenerationErrorCode;
import com.multiplier.core.payable.service.exception.InvoiceGenerationException;
import com.multiplier.core.payable.service.exception.ValidationException;
import com.multiplier.core.payable.tax.TaxCodeService;
import com.multiplier.payable.grpc.schema.FreelancerInvoiceLineItem;
import com.multiplier.payable.grpc.schema.LineItemType;
import com.multiplier.payable.types.*;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.Optional;
import java.util.Set;

@RequiredArgsConstructor
@Component
@Slf4j
public class MemberPayableProcessingFeeMapperService {

    public static final String BANK_TRANSFER_FEE_DESCRIPTION = "Bank transfer fee";
    public static final String CREDIT_CARD_FEE_DESCRIPTION = "Credit card fee";
    public static final String DIRECT_DEBIT_FEE_DESCRIPTION = "Direct debit fee";
    public static final String PAYOUT_FEE_DESCRIPTION = "Crypto payout fee";

    private final Set<LineItemType> supportedProcessingFeeLineItemTypes = Set.of(
            LineItemType.PAYIN_BANK_TRANSFER_FEE,
            LineItemType.PAYIN_CREDIT_CARD_FEE,
            LineItemType.PAYIN_ACH_DIRECT_DEBIT_FEE,
            LineItemType.PAYOUT_FEE
    );

    private final CompanyServiceAdapter companyServiceAdapter;
    private final TaxCodeService taxCodeService;

    public JpaPayableItem map(FreelancerInvoiceLineItem newProcessingFeeLineItem, String billingCurrency, Long companyId) {
        CountryCode companyEntityLocation = Optional.ofNullable(companyServiceAdapter.getCompanyById(companyId).getPrimaryEntity())
                .map(LegalEntity::getAddress)
                .map(Address::getCountry)
                .map(countryCode -> CountryCode.valueOf(countryCode.name()))
                .orElseThrow(() -> {
                    log.error("Fail to get company legal country for companyId {}", companyId);
                    return new InvoiceGenerationException(InvoiceGenerationErrorCode.MPE_INTERNAL_SERVER_ERROR);
                });
        return getProcessingFeePayableItem(newProcessingFeeLineItem, CurrencyCode.valueOf(billingCurrency), companyEntityLocation)
                .orElseThrow(() -> new ValidationException("Cannot get payable item for line item type: " + newProcessingFeeLineItem.getLineItemType()));
    }

    private Optional<JpaPayableItem> getProcessingFeePayableItem(@NotNull FreelancerInvoiceLineItem freelancerInvoicePaymentFeeLineItem, @NotNull CurrencyCode billingCurrency, @Nullable CountryCode companyEntityLocation) {
        if (supportedProcessingFeeLineItemTypes.contains(freelancerInvoicePaymentFeeLineItem.getLineItemType())) {
            String taxType = getTaxTypeForPayableItemTypeInNetsuite(companyEntityLocation);
            log.info("Creating fee line item for {} with amount: {} and taxType: {}",
                    freelancerInvoicePaymentFeeLineItem.getLineItemType(), freelancerInvoicePaymentFeeLineItem.getAmount(), taxType);
            val description = switch (freelancerInvoicePaymentFeeLineItem.getLineItemType()) {
                case PAYIN_BANK_TRANSFER_FEE -> BANK_TRANSFER_FEE_DESCRIPTION;
                case PAYIN_CREDIT_CARD_FEE -> CREDIT_CARD_FEE_DESCRIPTION;
                case PAYIN_ACH_DIRECT_DEBIT_FEE -> DIRECT_DEBIT_FEE_DESCRIPTION;
                case PAYOUT_FEE -> PAYOUT_FEE_DESCRIPTION;
                default -> throw new IllegalStateException("Unexpected value: " + freelancerInvoicePaymentFeeLineItem.getLineItemType());
            };
            val jpaPayableItem = JpaPayableItem.builder()
                    .currencyCode(billingCurrency)
                    .type(PayableItemType.MEMBER_PROCESSING_FEE_FOR_FREELANCER)
                    .totalCost(freelancerInvoicePaymentFeeLineItem.getAmount())
                    .description(description)
                    .billableCost(freelancerInvoicePaymentFeeLineItem.getAmount())
                    .taxType(taxType)
                    .build();
            return Optional.of(jpaPayableItem);
        } else {
            log.error("Invalid Line item type :{} for freelancer invoice. hence returning null", freelancerInvoicePaymentFeeLineItem.getLineItemType());
            return Optional.empty();
        }
    }

    private @NotNull String getTaxTypeForPayableItemTypeInNetsuite(CountryCode companyEntityLocation) {
        var taxCodeIdFromNetsuite = taxCodeService.getTaxCode(companyEntityLocation, PayableItemType.MEMBER_PROCESSING_FEE_FOR_FREELANCER);
        log.info("netsuite taxCode id is {} for company countryCode {} and payableItemType {}",
                taxCodeIdFromNetsuite, companyEntityLocation, PayableItemType.MEMBER_PROCESSING_FEE_FOR_FREELANCER);
        return taxCodeIdFromNetsuite;
    }

}
