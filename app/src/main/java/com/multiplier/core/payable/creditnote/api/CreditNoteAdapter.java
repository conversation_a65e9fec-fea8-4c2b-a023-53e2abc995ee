package com.multiplier.core.payable.creditnote.api;

import java.util.List;

/**
 * An adapter that does CRUD operations for credit notes.
 */
public interface CreditNoteAdapter {

    CreditNoteApiResponse create(CreateCreditNoteApiRequest request);

    CreditNoteApiResponse get(String creditNoteId);

    CreditNoteApiResponse update(UpdateCreditNoteApiRequest request);

    void delete(String creditNoteId);

    List<CreditNoteApiResponse> list(List<String> creditNoteIds);

    /**
     * Gets the pdf as byte array from external system
     * @throws com.multiplier.core.payable.adapters.netsuite.exception.NetsuiteAdapterException
     * @param externalCreditNoteId the credit note id used by the external system as unique identifier.
     * @return byte[]
     */
    byte[] getPdfAsEncodedString(String externalCreditNoteId);
}
