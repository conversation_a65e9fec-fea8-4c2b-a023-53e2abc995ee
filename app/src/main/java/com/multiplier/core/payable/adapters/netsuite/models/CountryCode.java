package com.multiplier.core.payable.adapters.netsuite.models;

import java.util.Arrays;

public enum CountryCode {
    AFG("AF","AFG"),
    ALB("AL","ALB"),
    DZA("DZ","DZA"),
    ASM("AS","ASM"),
    AND("AD","AND"),
    AGO("AO","AGO"),
    AIA("AI","AIA"),
    ATA("AQ","ATA"),
    ATG("AG","ATG"),
    ARG("AR","ARG"),
    ARM("AM","ARM"),
    ABW("AW","ABW"),
    AUS("AU","AUS"),
    AUT("AT","AUT"),
    AZE("AZ","AZE"),
    BHS("BS","BHS"),
    BHR("BH","BHR"),
    BGD("BD","BGD"),
    BRB("BB","BRB"),
    BLR("BY","BLR"),
    BEL("BE","BEL"),
    BLZ("BZ","BLZ"),
    BEN("BJ","BEN"),
    BMU("BM","BMU"),
    BTN("BT","BTN"),
    BOL("BO","BOL"),
    BES("BQ","BES"),
    BIH("BA","BIH"),
    BWA("BW","BWA"),
    BVT("BV","BVT"),
    BRA("BR","BRA"),
    IOT("IO","IOT"),
    BRN("BN","BRN"),
    BGR("BG","BGR"),
    BFA("BF","BFA"),
    BDI("BI","BDI"),
    CPV("CV","CPV"),
    KHM("KH","KHM"),
    CMR("CM","CMR"),
    CAN("CA","CAN"),
    CYM("KY","CYM"),
    CAF("CF","CAF"),
    TCD("TD","TCD"),
    CHL("CL","CHL"),
    CHN("CN","CHN"),
    CXR("CX","CXR"),
    CCK("CC","CCK"),
    COL("CO","COL"),
    COM("KM","COM"),
    COD("CD","COD"),
    COG("CG","COG"),
    COK("CK","COK"),
    CRI("CR","CRI"),
    HRV("HR","HRV"),
    CUB("CU","CUB"),
    CUW("CW","CUW"),
    CYP("CY","CYP"),
    CZE("CZ","CZE"),
    CIV("CI","CIV"),
    DNK("DK","DNK"),
    DJI("DJ","DJI"),
    DMA("DM","DMA"),
    DOM("DO","DOM"),
    ECU("EC","ECU"),
    EGY("EG","EGY"),
    SLV("SV","SLV"),
    GNQ("GQ","GNQ"),
    ERI("ER","ERI"),
    EST("EE","EST"),
    SWZ("SZ","SWZ"),
    ETH("ET","ETH"),
    FLK("FK","FLK"),
    FRO("FO","FRO"),
    FJI("FJ","FJI"),
    FIN("FI","FIN"),
    FRA("FR","FRA"),
    GUF("GF","GUF"),
    PYF("PF","PYF"),
    ATF("TF","ATF"),
    GAB("GA","GAB"),
    GMB("GM","GMB"),
    GEO("GE","GEO"),
    DEU("DE","DEU"),
    GHA("GH","GHA"),
    GIB("GI","GIB"),
    GRC("GR","GRC"),
    GRL("GL","GRL"),
    GRD("GD","GRD"),
    GLP("GP","GLP"),
    GUM("GU","GUM"),
    GTM("GT","GTM"),
    GGY("GG","GGY"),
    GIN("GN","GIN"),
    GNB("GW","GNB"),
    GUY("GY","GUY"),
    HTI("HT","HTI"),
    HMD("HM","HMD"),
    VAT("VA","VAT"),
    HND("HN","HND"),
    HKG("HK","HKG"),
    HUN("HU","HUN"),
    ISL("IS","ISL"),
    IND("IN","IND"),
    IDN("ID","IDN"),
    IRN("IR","IRN"),
    IRQ("IQ","IRQ"),
    IRL("IE","IRL"),
    IMN("IM","IMN"),
    ISR("IL","ISR"),
    ITA("IT","ITA"),
    JAM("JM","JAM"),
    JPN("JP","JPN"),
    JEY("JE","JEY"),
    JOR("JO","JOR"),
    KAZ("KZ","KAZ"),
    KEN("KE","KEN"),
    KIR("KI","KIR"),
    PRK("KP","PRK"),
    KOR("KR","KOR"),
    KWT("KW","KWT"),
    KGZ("KG","KGZ"),
    LAO("LA","LAO"),
    LVA("LV","LVA"),
    LBN("LB","LBN"),
    LSO("LS","LSO"),
    LBR("LR","LBR"),
    LBY("LY","LBY"),
    LIE("LI","LIE"),
    LTU("LT","LTU"),
    LUX("LU","LUX"),
    MAC("MO","MAC"),
    MDG("MG","MDG"),
    MWI("MW","MWI"),
    MYS("MY","MYS"),
    MDV("MV","MDV"),
    MLI("ML","MLI"),
    MLT("MT","MLT"),
    MHL("MH","MHL"),
    MTQ("MQ","MTQ"),
    MRT("MR","MRT"),
    MUS("MU","MUS"),
    MYT("YT","MYT"),
    MEX("MX","MEX"),
    FSM("FM","FSM"),
    MDA("MD","MDA"),
    MCO("MC","MCO"),
    MNG("MN","MNG"),
    MNE("ME","MNE"),
    MSR("MS","MSR"),
    MAR("MA","MAR"),
    MOZ("MZ","MOZ"),
    MMR("MM","MMR"),
    NAM("NA","NAM"),
    NRU("NR","NRU"),
    NPL("NP","NPL"),
    NLD("NL","NLD"),
    NCL("NC","NCL"),
    NZL("NZ","NZL"),
    NIC("NI","NIC"),
    NER("NE","NER"),
    NGA("NG","NGA"),
    NIU("NU","NIU"),
    NFK("NF","NFK"),
    MNP("MP","MNP"),
    NOR("NO","NOR"),
    OMN("OM","OMN"),
    PAK("PK","PAK"),
    PLW("PW","PLW"),
    PSE("PS","PSE"),
    PAN("PA","PAN"),
    PNG("PG","PNG"),
    PRY("PY","PRY"),
    PER("PE","PER"),
    PHL("PH","PHL"),
    PCN("PN","PCN"),
    POL("PL","POL"),
    PRT("PT","PRT"),
    PRI("PR","PRI"),
    QAT("QA","QAT"),
    MKD("MK","MKD"),
    ROU("RO","ROU"),
    RUS("RU","RUS"),
    RWA("RW","RWA"),
    REU("RE","REU"),
    BLM("BL","BLM"),
    SHN("SH","SHN"),
    KNA("KN","KNA"),
    LCA("LC","LCA"),
    MAF("MF","MAF"),
    SPM("PM","SPM"),
    VCT("VC","VCT"),
    WSM("WS","WSM"),
    SMR("SM","SMR"),
    STP("ST","STP"),
    SAU("SA","SAU"),
    SEN("SN","SEN"),
    SRB("RS","SRB"),
    SYC("SC","SYC"),
    SLE("SL","SLE"),
    SGP("SG","SGP"),
    SXM("SX","SXM"),
    SVK("SK","SVK"),
    SVN("SI","SVN"),
    SLB("SB","SLB"),
    SOM("SO","SOM"),
    ZAF("ZA","ZAF"),
    SGS("GS","SGS"),
    SSD("SS","SSD"),
    ESP("ES","ESP"),
    LKA("LK","LKA"),
    SDN("SD","SDN"),
    SUR("SR","SUR"),
    SJM("SJ","SJM"),
    SWE("SE","SWE"),
    CHE("CH","CHE"),
    SYR("SY","SYR"),
    TWN("TW","TWN"),
    TJK("TJ","TJK"),
    TZA("TZ","TZA"),
    THA("TH","THA"),
    TLS("TL","TLS"),
    TGO("TG","TGO"),
    TKL("TK","TKL"),
    TON("TO","TON"),
    TTO("TT","TTO"),
    TUN("TN","TUN"),
    TUR("TR","TUR"),
    TKM("TM","TKM"),
    TCA("TC","TCA"),
    TUV("TV","TUV"),
    UGA("UG","UGA"),
    UKR("UA","UKR"),
    ARE("AE","ARE"),
    GBR("GB","GBR"),
    UMI("UM","UMI"),
    USA("US","USA"),
    URY("UY","URY"),
    UZB("UZ","UZB"),
    VUT("VU","VUT"),
    VEN("VE","VEN"),
    VNM("VN","VNM"),
    VGB("VG","VGB"),
    VIR("VI","VIR"),
    WLF("WF","WLF"),
    ESH("EH","ESH"),
    YEM("YE","YEM"),
    ZMB("ZM","ZMB"),
    ZWE("ZW","ZWE"),
    ALA("AX","ALA");

    private final String _alpha2Code;
    private final String _alpha3Code;

    CountryCode(String alpha2Code, String alpha3Code) {
        this._alpha2Code = alpha2Code;
        this._alpha3Code = alpha3Code;
    }

    public static String getAlpha2Code(String alpha3Code) {
        CountryCode cc = Arrays.stream(values())
                .filter(x -> x._alpha3Code.equalsIgnoreCase(alpha3Code))
                .findAny()
                .orElse(null);

        return cc != null ? cc._alpha2Code : null;
    }

    public static String getAlpha3Code(String alpha2Code) {
        CountryCode cc = Arrays.stream(values())
                .filter(x -> x._alpha2Code.equalsIgnoreCase(alpha2Code))
                .findAny()
                .orElse(null);

        return cc != null ? cc._alpha3Code : null;
    }

    public static CountryCode getCountryCodeWithoutPrefix(String countryCode) {
        if (countryCode == null || countryCode.isEmpty()) {
            return null;
        }
        return CountryCode.valueOf(countryCode.replaceFirst("^COUNTRY_CODE_", ""));
    }
}
