package com.multiplier.core.payable.customer;

import com.multiplier.core.util.IgnoreUnmappedMapperConfig;
import com.multiplier.core.payable.adapters.api.ContactPersonDto;
import com.multiplier.core.payable.adapters.api.CustomerDto;
import com.multiplier.core.payable.adapters.netsuite.client.NetsuiteMappings;
import com.multiplier.payable.types.CurrencyCode;
import com.netsuite.suitetalk.proxy.v2023_1.lists.relationships.Customer;
import com.netsuite.suitetalk.proxy.v2023_1.lists.relationships.CustomerAddressbook;
import com.netsuite.suitetalk.proxy.v2023_1.lists.relationships.CustomerAddressbookList;
import com.netsuite.suitetalk.proxy.v2023_1.platform.common.Address;
import com.netsuite.suitetalk.proxy.v2023_1.platform.common.types.Country;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.*;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.types.RecordType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static com.multiplier.core.payable.adapters.netsuite.NetsuiteConstants.CREDIT_MEMO_FORM;
import static com.multiplier.core.payable.adapters.netsuite.NetsuiteConstants.INVOICE_FORM;

@Mapper(componentModel = "spring", config = IgnoreUnmappedMapperConfig.class)
public abstract class NetsuiteWebserviceCustomerMapper {

    private NetsuiteCustomerLegalEntityMapper legalEntityMapper;

    @Autowired
    public void setLegalEntityMapper(NetsuiteCustomerLegalEntityMapper legalEntityMapper) {
        this.legalEntityMapper = legalEntityMapper;
    }

    public static final String CUSTOM_COL_MLT_FIRST_NAME = "custentity_mlt_first_name";
    public static final String CUSTOM_COL_MLT_LAST_NAME = "custentity_mlt_last_name";
    public static final String CUSTOM_COL_CREDIT_MEMO_FORM = "custentity_multi_credit_memo_form";
    public static final String CUSTOM_COL_INVOICE_FORM = "custentity_multiplier_invoice_form";

    @Mapping(target = "internalId", source = "customerId")
    @Mapping(target = "entityId", source = "customerDto", qualifiedByName = "mapEntity")
    @Mapping(target = "externalId", source = "customerDto", qualifiedByName = "mapEntity")
    @Mapping(target = "email", source = "primaryContactEmail")
    @Mapping(target = "phone", source = "primaryContactNumber")
    @Mapping(target = "currency", source = "billingCurrency", qualifiedByName = "mapNetsuiteCurrency")
    @Mapping(target = "addressbookList", source = "customerDto", qualifiedByName = "mapNetsuiteAddressBook")
    @Mapping(target = "parent", source = "customerDto", qualifiedByName = "mapParentCompanyId")
    abstract com.netsuite.suitetalk.proxy.v2023_1.lists.relationships.Customer map(CustomerDto customerDto, @Context CustomerDto existingCustomer);

    @Named("mapEntity")
    String mapEntity(CustomerDto customerDto) {
        return legalEntityMapper.mapEntity(customerDto);
    }

    // parent: should be added only if not a primary entity
    @Named("mapParentCompanyId")
    RecordRef mapParentCompanyId(CustomerDto customerDto) {
        if (Boolean.TRUE.equals(customerDto.getIsParentCompany())) {
            return null;
        }
        //TODO: the customer id should be mapped to the NS internal id
        return new RecordRef(null, customerDto.getCustomerId(), null, RecordType.customer);
    }

    @Named("mapNetsuiteCurrency")
    RecordRef mapNetsuiteCurrency(CurrencyCode currencyCode) {
        if (currencyCode == null) {
            return null;
        }
        var nsCurrencyInternalId = NetsuiteMappings.getCurrencyId(currencyCode.name());
        return new RecordRef(null, nsCurrencyInternalId, null, RecordType.currency);
    }

    @Named("mapNetsuiteAddressBook")
    CustomerAddressbookList mapNetsuiteAddressBook(CustomerDto customerDto) {
        var addresses = customerDto.getAddresses();

        if (addresses == null || addresses.isEmpty()) {
            return null;
        }

        var customerAddressbookList = new ArrayList<CustomerAddressbook>();
        addresses.forEach(address -> {
            var nsAddress = new Address(null, null,
                    Country.fromValue(NetsuiteMappings.fromCountryCode(address.getCountry())),
                    null,
                    customerDto.getCompanyName(), // addressee is the display name on the shipping / billing address
                    null,
                    address.getLine1(),
                    address.getLine2(),
                    null,
                    address.getCity(),
                    address.getState(),
                    address.getZipcode(),
                    null,
                    false, // override
                    /* explanation for override
                    Check this box to disable the free-form address text field. When this field is disabled,
                    text entered in the other address fields does not display in the Address text field.
                    Clear this box to allow text entered in the address component fields to appear in the free-form address text field.
                     */
                    null
            );


            boolean defaultBilling = true;
            boolean defaultShipping = true;
            boolean isResidential = false;
            var customerAddressBook = new CustomerAddressbook(defaultShipping, defaultBilling, isResidential,
                    null, // label
                    nsAddress,
                    null
            );
            customerAddressbookList.add(customerAddressBook);
        });
        var customerAddressbookArray = customerAddressbookList.toArray(CustomerAddressbook[]::new);
        return new CustomerAddressbookList(customerAddressbookArray, true);
    }

    @AfterMapping
    com.netsuite.suitetalk.proxy.v2023_1.lists.relationships.Customer postMap(@MappingTarget Customer customer, CustomerDto customerDto,
                                                                                      @Context CustomerDto existingCustomer) {
        customer.setIsInactive(false);
        customer.setIsPerson(false);
        customer.setSubsidiary(new RecordRef(null, NetsuiteMappings.getSubsidiaryId(null), null, RecordType.subsidiary));
        customer.setCategory(new RecordRef("EOR", NetsuiteMappings.getCustomerCategoryId("EOR"), null, RecordType.customerCategory));

        CustomFieldList customFieldList = new CustomFieldList();
        var customFieldRefList = new ArrayList<CustomFieldRef>();

        customFieldRefList.add(new StringCustomFieldRef(null, CUSTOM_COL_MLT_FIRST_NAME, customerDto.getPrimaryContactFirstName()));

        customFieldRefList.add(new StringCustomFieldRef(null, CUSTOM_COL_MLT_LAST_NAME, customerDto.getPrimaryContactLastName()));

        // skip updating form for existing customer on Netsuite
        if (existingCustomer == null) {
            customFieldRefList.add(new SelectCustomFieldRef(null, CUSTOM_COL_CREDIT_MEMO_FORM,
                    new ListOrRecordRef(null, NetsuiteMappings.getCustomFormId(CREDIT_MEMO_FORM), null, null)));

            customFieldRefList.add(new SelectCustomFieldRef(null, CUSTOM_COL_INVOICE_FORM,
                    new ListOrRecordRef(null, NetsuiteMappings.getCustomFormId(INVOICE_FORM), null, null)));
        }

        // handling custom email fields
        var customerContactEmails = getCustomerContactEmails(customerDto);
        var existingMultiplierEmails = getExistingMultiplierEmails(existingCustomer).stream()
                .filter(multiplierEmail -> !customerContactEmails.contains(multiplierEmail))
                .toList();
        var customEmailFieldRefs = createCustomEmailFields(customerContactEmails, existingMultiplierEmails);
        if (!customEmailFieldRefs.isEmpty()) {
            customFieldRefList.addAll(customEmailFieldRefs);
        }

        var customFieldRefs = customFieldRefList.toArray(CustomFieldRef[]::new);
        customFieldList.setCustomField(customFieldRefs);
        customer.setCustomFieldList(customFieldList);

        return customer;
    }

    private List<String> getCustomerContactEmails(CustomerDto customerDto) {
        if (customerDto.getContactPersons() == null) {
            return List.of();
        }
        return customerDto.getContactPersons().stream()
                .map(ContactPersonDto::getEmailAddress)
                .filter(StringUtils::isNotEmpty)
                .toList();
    }

    private List<String> getExistingMultiplierEmails(CustomerDto existingCustomer) {
        if (existingCustomer == null || !CollectionUtils.isNotEmpty(existingCustomer.getContactPersons())) {
            return List.of();
        }
        return existingCustomer.getContactPersons().stream()
                .map(ContactPersonDto::getEmailAddress)
                .filter(StringUtils::isNotEmpty)
                .filter(email -> email.endsWith("@usemultiplier.com"))
                .toList();
    }

    private List<CustomFieldRef> createCustomEmailFields(Collection<String> customerContactEmails, Collection<String> existingMultiplierEmails) {
        var customFieldRefs = new ArrayList<CustomFieldRef>();

        var customEmailFields = NetsuiteMappings.customEmailFields();
        var emailsToAdd = new ArrayList<>(customerContactEmails);
        emailsToAdd.addAll(existingMultiplierEmails);

        int numberOfEmailsToAdd = Math.min(customEmailFields.size(), emailsToAdd.size());
        for (int index = 0; index < numberOfEmailsToAdd; index++ ) {
            customFieldRefs.add(new StringCustomFieldRef(null, customEmailFields.get(index), emailsToAdd.get(index)));
        }
        return customFieldRefs;
    }
}
