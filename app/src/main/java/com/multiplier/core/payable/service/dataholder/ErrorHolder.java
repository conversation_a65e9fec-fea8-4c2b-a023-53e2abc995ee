package com.multiplier.core.payable.service.dataholder;

import com.multiplier.core.payable.service.util.InvoiceError;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class ErrorHolder {
    private String entity;
    private String id;
    private String reason;

    public static ErrorHolder getKnownError(InvoiceError invoiceError) {
        return new ErrorHolder(invoiceError.getEntity(), invoiceError.getId(), invoiceError.getReason());
    }

    public static ErrorHolder getKnownError(InvoiceError invoiceError, String id) {
        return new ErrorHolder(invoiceError.getEntity(), id, invoiceError.getReason());
    }

    public String createMessage() {
        return this.entity + " " + this.id + " " + this.reason;
    }
}
