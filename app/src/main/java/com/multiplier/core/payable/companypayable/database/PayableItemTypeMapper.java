package com.multiplier.core.payable.companypayable.database;

import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.util.IgnoreUnmappedMapperConfig;
import com.multiplier.payable.types.PayableItemType;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ValueMapping;

@Mapper(componentModel = "spring", config = IgnoreUnmappedMapperConfig.class)
public interface PayableItemTypeMapper {

    @ValueMapping(source = "GROSS_SALARY", target = "MEMBER_PAYROLL_COST")
    @ValueMapping(source = "FREELANCER_PAYMENT", target = "MEMBER_PAYROLL_COST_FOR_FREELANCER")
    @ValueMapping(source = "MANAGEMENT_FEE_EOR", target = "MEMBER_MANAGEMENT_FEE")
    @ValueMapping(source = "MANAGEMENT_FEE_FREELANCER", target = "MEMBER_MANAGEMENT_FEE_FOR_FREELANCER")
    @ValueMapping(source = "PAYMENT_FEE", target = "MEMBER_PROCESSING_FEE_FOR_FREELANCER")
    @ValueMapping(source = "PLATFORM_FEE", target = "MEMBER_PROCESSING_FEE_FOR_EOR")
    @ValueMapping(source = "PEO_SALARY_DISBURSEMENT", target = "MEMBER_GLOBAL_PAYROLL_FUNDING_COST")
    @ValueMapping(source = "ANNUAL_MANAGEMENT_FEE_EOR", target = "ANNUAL_MEMBER_MANAGEMENT_FEE")
    @ValueMapping(source = "INSURANCE_PREMIUM", target = "MEMBER_INSURANCE_COST")
    @ValueMapping(source = "EOR_SALARY_DISBURSEMENT", target = "MEMBER_TOTAL_PAYROLL_LESS_EXPENSE_COST")
    @ValueMapping(source = "EOR_EXPENSE_DISBURSEMENT", target = "MEMBER_TOTAL_EXPENSE_COST")
    @ValueMapping(source = "BILLED_GROSS_SALARY_SUPPLEMENTARY", target = "BILLED_GROSS_SALARY_SUPPLEMENTARY")
    @ValueMapping(source = "SEVERANCE_DEPOSIT_EOR_PAYROLL", target = "SEVERANCE_DEPOSIT_EOR_PAYROLL")
    @ValueMapping(source = "ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL", target = "ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL")
    @ValueMapping(source = "GP_FUND_REQUEST", target = "MEMBER_GLOBAL_PAYROLL_FUNDING_COST")
    @ValueMapping(source = "FREELANCER_PAYOUT_FEE", target = "MEMBER_PAYOUT_FEE_FOR_FREELANCER")
    @ValueMapping(source = "VAS_INCIDENT_LAPTOP_PAYMENT", target = "VAS_INCIDENT_LAPTOP_PAYMENT")
    @ValueMapping(source = "VAS_INCIDENT_LAPTOP_MANAGEMENT_FEE", target = "VAS_INCIDENT_LAPTOP_MANAGEMENT_FEE")
    @ValueMapping(source = "VAS_INCIDENT_MONITOR_PAYMENT", target = "VAS_INCIDENT_MONITOR_PAYMENT")
    @ValueMapping(source = "VAS_INCIDENT_MONITOR_MANAGEMENT_FEE", target = "VAS_INCIDENT_MONITOR_MANAGEMENT_FEE")
    @ValueMapping(source = "VAS_INCIDENT_ACCESSORIES_PAYMENT", target = "VAS_INCIDENT_ACCESSORIES_PAYMENT")
    @ValueMapping(source = "VAS_INCIDENT_ACCESSORIES_MANAGEMENT_FEE", target = "VAS_INCIDENT_ACCESSORIES_MANAGEMENT_FEE")
    @ValueMapping(source = "VAS_INCIDENT_DISCOUNT", target = "VAS_INCIDENT_DISCOUNT")
    @ValueMapping(source = "VAS_INCIDENT_PICKUP_DELIVERY_AMOUNT", target = "VAS_INCIDENT_PICKUP_DELIVERY_AMOUNT")
    @ValueMapping(source = "VAS_INCIDENT_PICKUP_DELIVERY_FEE", target = "VAS_INCIDENT_PICKUP_DELIVERY_FEE")
    @ValueMapping(source = "VAS_INCIDENT_STORAGE_AMOUNT", target = "VAS_INCIDENT_STORAGE_AMOUNT")
    @ValueMapping(source = "VAS_INCIDENT_STORAGE_FEE", target = "VAS_INCIDENT_STORAGE_FEE")
    @ValueMapping(source = "VAS_INCIDENT_CONTRACT_CUSTOMISATION_FEE", target = "VAS_INCIDENT_CONTRACT_CUSTOMISATION_FEE")
    @ValueMapping(source = "VAS_INCIDENT_LEGAL_CONSULTATION_FEE", target = "VAS_INCIDENT_LEGAL_CONSULTATION_FEE")
    @ValueMapping(source = "VAS_INCIDENT_OTHERS_SERVICE_AMOUNT", target = "VAS_INCIDENT_OTHERS_SERVICE_AMOUNT")
    @ValueMapping(source = "VAS_INCIDENT_OTHERS_SERVICE_FEE", target = "VAS_INCIDENT_OTHERS_SERVICE_FEE")
    @ValueMapping(source = "VAS_INCIDENT_OTHERS_VISA_FEE", target = "VAS_INCIDENT_OTHERS_VISA_FEE")
    @ValueMapping(source = "CANADA_GROSS_WAGES", target = "MEMBER_TOTAL_PAYROLL_LESS_EXPENSE_COST")
    @ValueMapping(source = "CANADA_EMPLOYER_BENEFITS", target = "MEMBER_TOTAL_PAYROLL_LESS_EXPENSE_COST")
    @ValueMapping(source = "CANADA_EMPLOYMENT_INSURANCE", target = "MEMBER_TOTAL_PAYROLL_LESS_EXPENSE_COST")
    @ValueMapping(source = "CANADA_PENSION_PLAN", target = "MEMBER_TOTAL_PAYROLL_LESS_EXPENSE_COST")
    @ValueMapping(source = "CANADA_QUEBEC_PENSION_PLAN", target = "MEMBER_TOTAL_PAYROLL_LESS_EXPENSE_COST")
    @ValueMapping(source = "CANADA_PROVINCIAL_HEALTH_TAX", target = "MEMBER_TOTAL_PAYROLL_LESS_EXPENSE_COST")
    @ValueMapping(source = "CANADA_WORKERS_COMPENSATION", target = "MEMBER_TOTAL_PAYROLL_LESS_EXPENSE_COST")
    @ValueMapping(source = "PAYROLL_OFFCYCLE_COST", target = "PAYROLL_OFFCYCLE_COST")
    @ValueMapping(source = "PAYROLL_OFFCYCLE_EXPENSE", target = "PAYROLL_OFFCYCLE_EXPENSE")
    @ValueMapping(source = "PAYROLL_OFFCYCLE_MANAGEMENT_FEE", target = "PAYROLL_OFFCYCLE_MANAGEMENT_FEE")
    @ValueMapping(source = "CANADA_PARENTAL_PLAN_TAX", target = "MEMBER_TOTAL_PAYROLL_LESS_EXPENSE_COST")
    @ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.THROW_EXCEPTION)
    PayableItemType map(LineItemType type);
}
