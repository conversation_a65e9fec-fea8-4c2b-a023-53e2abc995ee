package com.multiplier.core.payable.sync.eventdataprocessor;

import com.multiplier.core.payable.creditnote.database.CreditNoteService;
import com.multiplier.core.payable.event.database.EventDto;
import com.multiplier.core.payable.event.database.RecordType;
import com.multiplier.core.payable.invoice.database.InvoiceService;
import com.multiplier.core.payable.invoicecreditnote.InvoiceCreditNoteService;
import com.multiplier.core.payable.repository.model.ExternalSystem;
import com.multiplier.core.payable.sync.processor.creditnote.CreditNoteProcessor;
import com.multiplier.core.payable.sync.processor.invoice.InvoiceProcessor;
import com.multiplier.core.payable.validator.EventDataOrderValidatorService;
import com.multiplier.payable.netsuite.transaction.appevent.publisher.creditnote.CreditNoteAppEventPublisher;
import com.multiplier.payable.types.CreditNoteStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
class CreditNoteEventDataProcessor implements EventDataProcessor {

    private final CreditNoteProcessor creditNoteProcessor;
    private final InvoiceProcessor invoiceProcessor;
    private final CreditNoteService creditNoteService;
    private final InvoiceService invoiceService;
    private final InvoiceCreditNoteService invoiceCreditNoteService;
    private final CreditNoteAppEventPublisher creditNoteAppEventPublisher;
    private final EventDataOrderValidatorService eventDataOrderValidatorService;

    @Override
    public RecordType getType() {
        return RecordType.CREDIT_NOTE;
    }

    @Transactional
    @Override
    public void process(EventDto eventDto, ExternalSystem externalSystem) {
        log.info("Process event data of credit note with externalId {}", eventDto.getExternalId());

        var creditNoteDto = eventDto.getCreditNote();

        try {
            var processedCreditNote = creditNoteProcessor.process(creditNoteDto);
            if (processedCreditNote == null) {
                return;
            }

            var relatedInvoices = eventDto.getRelatedInvoices();

            if (!CollectionUtils.isEmpty(relatedInvoices)) {
                var processedInvoices = relatedInvoices.stream()
                    .map(relatedInvoice -> invoiceProcessor.process(relatedInvoice, externalSystem))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

                processedInvoices.forEach(processedInvoice ->
                    invoiceCreditNoteService.insert(processedInvoice.getExternalId(),
                        processedCreditNote.getExternalId()));

                if (processedCreditNote.getStatus().equals(CreditNoteStatus.DELETED)) {
                    invoiceService.markAuthorized(processedInvoices);
                }
            }

            creditNoteAppEventPublisher.publish(processedCreditNote);

        } catch (Exception e) {
            var message = String.format("Fail to process event data for credit note with externalId %s",
                creditNoteDto.getExternalId());
            throw new IllegalArgumentException(message, e);
        }
    }

    @Override
    public boolean isEventDataPresent(EventDto eventDto, ExternalSystem externalSystem) {
        log.info("Check if event data of credit note with externalId {} is present", eventDto.getExternalId());

        try {
            var creditNote = creditNoteService.find(eventDto.getExternalId());

            var creditNoteExists = creditNote.isPresent();
            var existenceMessage = String.format("Credit note with externalId %s %s",
                eventDto.getExternalId(),
                creditNoteExists ? " exists" : "doesn't exist");
            log.info(existenceMessage);

            var hasProposedProcessingTime = eventDto.getProposedProcessingTime() != null;
            var proposedProcessingTimeMessage = String.format("Credit note with externalId %s %s",
                eventDto.getExternalId(),
                hasProposedProcessingTime ? "has proposedProcessingTime" : "doesn't have proposedProcessingTime");
            log.info(proposedProcessingTimeMessage);

            boolean shouldProcess = eventDataOrderValidatorService.validateEventOrder(eventDto, externalSystem, hasProposedProcessingTime);

            return (creditNoteExists || hasProposedProcessingTime) && shouldProcess;

        } catch (IncorrectResultSizeDataAccessException e) {
            var message = String.format("Credit note with externalId %s has more than one record", eventDto.getExternalId());
            throw new IllegalArgumentException(message, e);
        }
    }

}
