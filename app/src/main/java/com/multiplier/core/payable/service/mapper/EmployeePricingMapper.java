package com.multiplier.core.payable.service.mapper;

import com.multiplier.core.payable.pricing.EmployeePricingKey;
import com.multiplier.core.payable.repository.model.JpaEmployeePricing;
import com.multiplier.core.payable.repository.model.JpaPricing;
import com.multiplier.core.payable.repository.model.PriceType;
import com.multiplier.payable.types.*;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface EmployeePricingMapper {

    List<EmployeePricing> map(List<JpaEmployeePricing> employeePricings);

    default EmployeePricing map(JpaEmployeePricing employeePricing) {
        if (employeePricing == null) {
            return null;
        }

        if (employeePricing.getType() == PriceType.COUNTRY) {
            return CountryEmployeePricing.newBuilder()
                    .id(employeePricing.getId())
                    .employeeType(employeePricing.getEmployeeType())
                    .fixedRate(employeePricing.getFixedRate())
                    .country(employeePricing.getCountry())
                    .build();
        } else {
            //region not supported yet
            return null;
        }
    }

    default JpaEmployeePricing buildJpaEmployeePricing(
            JpaPricing jpaPricing,
            CompanyEmployeePricingInput employeePricingInput,
            Map<EmployeePricingKey, JpaEmployeePricing> existingEmpPricing
    ) {
        var inputKey = new EmployeePricingKey(employeePricingInput.getCountry(), employeePricingInput.getEmployeeType());
        return JpaEmployeePricing.builder()
                .id(existingEmpPricing.getOrDefault(inputKey, new JpaEmployeePricing()).getId())
                .pricingId(jpaPricing.getId())
                .employeeType(employeePricingInput.getEmployeeType())
                .type(employeePricingInput.getCountry() == null ? PriceType.REGION : PriceType.COUNTRY)
                .country(employeePricingInput.getCountry())
                //.region(pricingFactory.getRegionForCountry(country))
                .fixedRate(employeePricingInput.getFixedRate())
                .build();
    }

    default JpaEmployeePricing buildJpaEmployeePricing(
            Long jpaPricingId,
            CompanyEmployeePricingInput employeePricingInput
    ) {
        return JpaEmployeePricing.builder()
                .pricingId(jpaPricingId)
                .employeeType(employeePricingInput.getEmployeeType())
                .type(employeePricingInput.getCountry() == null ? PriceType.REGION : PriceType.COUNTRY)
                .country(employeePricingInput.getCountry())
                .fixedRate(employeePricingInput.getFixedRate())
                .build();
    }

    default List<CompanyEmployeePricingInput> mapToInput(List<EmployeePricing> employeePricings) {
        if (employeePricings == null) {
            return new ArrayList<>();
        }

        return employeePricings
                .stream()
                .filter(ep -> ep instanceof CountryEmployeePricing)
                .map(ep -> CompanyEmployeePricingInput.newBuilder()
                        .fixedRate(ep.getFixedRate())
                        .employeeType(ep.getEmployeeType())
                        .country(((CountryEmployeePricing) ep).getCountry())
                        .build())
                .collect(Collectors.toList());
    }

    default List<CompanyVisaPricingInput> mapToVisaInput(List<EmployeePricing> employeePricings) {
        if (employeePricings == null) {
            return new ArrayList<>();
        }

        return employeePricings
                .stream()
                .filter(ep -> ep instanceof VisaEmployeePricing)
                .map(ep -> CompanyVisaPricingInput.newBuilder()
                        .fixedRate(ep.getFixedRate())
                        .employeeType(ep.getEmployeeType())
                        .country(((VisaEmployeePricing) ep).getCountry())
                        .build())
                .collect(Collectors.toList());
    }
}
