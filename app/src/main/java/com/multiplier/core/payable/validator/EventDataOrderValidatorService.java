package com.multiplier.core.payable.validator;

import com.multiplier.core.payable.event.database.EventDto;
import com.multiplier.core.payable.repository.model.ExternalSystem;
import com.multiplier.core.payable.sync.eventstore.EventStoreFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class EventDataOrderValidatorService {

    private final EventStoreFactory eventStoreFactory;

    public boolean validateEventOrder(EventDto eventDto, ExternalSystem externalSystem, boolean hasProposedProcessingTime){

        boolean shouldProcess = true;
        if(!hasProposedProcessingTime){
            var eventStore = eventStoreFactory.getEventStore(externalSystem);

            var eventsOfExternalId = eventStore.retrieveWaitingEventsByExternalId(eventDto);
            var eventsOfExternalIdExcludingCurrentEvent = eventsOfExternalId.stream()
                    .filter(
                            event -> !event.getId().equals(eventDto.getId()) && event.getProposedProcessingTime() != null
                    )
                    .toList();
            shouldProcess = eventsOfExternalIdExcludingCurrentEvent.isEmpty();
        }
        return shouldProcess;
    }
}
