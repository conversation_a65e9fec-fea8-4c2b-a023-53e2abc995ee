package com.multiplier.core.payable.repository;

import com.multiplier.core.payable.repository.model.JpaPricing;
import com.multiplier.payable.types.CurrencyCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface JpaPricingRepository extends JpaRepository<JpaPricing, Long> {

    JpaPricing findByCompanyId(Long companyId);

    List<JpaPricing> findByCompanyIdIn(final List<Long> companyIds);

    @Query("SELECT p.billingCurrencyCode FROM JpaPricing p WHERE p.companyId = :companyId")
    CurrencyCode findBillingCurrencyCodeByCompanyId(@Param("companyId") Long companyId);
}
