package com.multiplier.core.payable.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.multiplier.core.util.CSVFactory;
import com.multiplier.core.util.FileUtil;
import com.multiplier.core.payable.model.EmployeePricingBackfillModel;
import com.multiplier.core.payable.model.PricingBackfilDiscountRuleCol;
import com.multiplier.core.payable.model.PricingBackfillPricingCol;
import com.multiplier.core.payable.repository.JpaEmployeePricingRepository;
import com.multiplier.core.payable.repository.JpaPricingRepository;
import com.multiplier.core.payable.repository.model.*;
import com.multiplier.core.payable.service.exception.ValidationException;
import com.multiplier.payable.types.ContractType;
import com.multiplier.payable.types.CountryCode;
import com.opencsv.CSVReader;
import com.opencsv.bean.CsvToBeanBuilder;
import com.opencsv.exceptions.CsvException;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
@Slf4j
public class PricingBackFiller {
    private static final String EMPLOYEE = "EMPLOYEE";
    private static final String FREELANCER = "FREELANCER";
    private static final String HR_MEMBER = "HR_MEMBER";

    private final JpaPricingRepository pricingRepository;

    private final JpaEmployeePricingRepository employeePricingRepository;

    @SneakyThrows
    @Transactional
    public void backfillCompanyPricing(MultipartFile pricingFile, MultipartFile discountTermsFile) {
        Path tempPricingFileCSV = FileUtil.createCsvTempFile();
        Path tempDiscountTermsFileCSV = FileUtil.createCsvTempFile();
        pricingFile.transferTo(tempPricingFileCSV);
        discountTermsFile.transferTo(tempDiscountTermsFileCSV);

        try (
                CSVReader pricingFileCSVReader = CSVFactory.createCSVReader(new FileReader(tempPricingFileCSV.toFile()));
                CSVReader discountTermsFileCSVReader = CSVFactory.createCSVReader(new FileReader(tempDiscountTermsFileCSV.toFile()))
        ) {
            ObjectMapper pricingObjectMapper = new ObjectMapper();
            Map<Long, List<JpaDiscountTerm>> discountTermsMap = processDiscountTerms(discountTermsFileCSVReader);
            List<String[]> readAll = pricingFileCSVReader.readAll();
            for (int i = 1, readAllSize = readAll.size(); i < readAllSize; i++) {
                String[] pricingRow = readAll.get(i);
                if (pricingRow == null || pricingRow.length < 7) {
                    throw new ValidationException("Pricing sheet columns in backfiller cannot be lesser than 7");
                }
                try {
                    long companyId = Long.parseLong(pricingRow[0]);
                    log.info("[PricingBackFiller] Reading pricing for companyId={}", companyId);
                    Map<String, Integer> epMap = pricingObjectMapper.readValue(pricingRow[9], HashMap.class);
                    Map<String, Integer> globalPrices = pricingObjectMapper.readValue(pricingRow[10], HashMap.class);
                    JpaPricing savedPricing = savePricing(discountTermsMap.get(companyId), pricingRow, companyId, globalPrices);
                    log.info("[PricingBackFiller] Saved pricing for companyId={}", companyId);
                    saveEmployeePricing(epMap, savedPricing);
                    log.info("[PricingBackFiller] Saved employee pricing for companyId={}", companyId);
                } catch (JsonProcessingException e) {
                    throw new ValidationException("Reading employee pricing failed", e);
                }
            }
        } finally {
            Files.delete(tempPricingFileCSV);
            Files.delete(tempDiscountTermsFileCSV);
        }

        log.info("[PricingBackFiller] Backfilling successful");
    }

    @SneakyThrows
    @Transactional
    public void backFillCompanyEmployeePricing(MultipartFile employeePricingFile) {
        Path tempPricingFileCSV = FileUtil.createCsvTempFile();
        employeePricingFile.transferTo(tempPricingFileCSV);
        try {
            CSVReader employeePricingFileCSVReader = CSVFactory.createCSVReader(new FileReader(tempPricingFileCSV.toFile()));

            List<EmployeePricingBackfillModel> employeePricingModels = new CsvToBeanBuilder(employeePricingFileCSVReader)
                    .withType(EmployeePricingBackfillModel.class)
                    .withIgnoreEmptyLine(true)
                    .withIgnoreLeadingWhiteSpace(true)
                    .build()
                    .parse();

            for (EmployeePricingBackfillModel epbm : employeePricingModels) {
                var jpaPricing = pricingRepository.findByCompanyId(epbm.getCompanyId());
                if (jpaPricing == null) {
                    log.warn("skipping update for company: {} because it doesn't have pricing.", epbm.getCompanyId());
                    continue;
                }
                switch (epbm.getScope()) {
                    case EMPLOYEE_GLOBAL_PRICING:
                        saveOrUpdateEmployeeGlobalPricing(epbm, jpaPricing);
                        break;
                    case VISA_GLOBAL_PRICING:
                        saveOrUpdateVisaGlobalPricing(epbm, jpaPricing);
                        break;
                    case EMPLOYEE_PRICING:
                        saveOrUpdateCompanyEmployeePricing(epbm, jpaPricing.getId());
                        break;
                }
            }
        } catch (Exception e) {
            log.error("Error while updating employee pricing: {}", e.getMessage());
            throw new ValidationException("Reading employee pricing failed", e);
        } finally {
            Files.delete(tempPricingFileCSV);
        }
    }

    private void saveOrUpdateEmployeeGlobalPricing(EmployeePricingBackfillModel epbm, JpaPricing jpaPricing) {
        if (jpaPricing.getEmployeeTypeGlobalPricing() != null) {
            var employeeTypeGlobalPricing = jpaPricing.getEmployeeTypeGlobalPricing().stream()
                    .filter(ep -> ep.employeeType() == epbm.getContractType())
                    .findFirst();

            if (employeeTypeGlobalPricing.isPresent()) {
                employeeTypeGlobalPricing.get().globalPrice(epbm.getFixedRate());
            } else {
                jpaPricing.getEmployeeTypeGlobalPricing().add(new EmployeeTypeGlobalPricing(epbm.getContractType(), epbm.getFixedRate()));
            }
        } else {
            jpaPricing.setEmployeeTypeGlobalPricing(List.of(new EmployeeTypeGlobalPricing(epbm.getContractType(), epbm.getFixedRate())));
        }

        pricingRepository.save(jpaPricing);
    }

    private void saveOrUpdateVisaGlobalPricing(EmployeePricingBackfillModel epbm, JpaPricing jpaPricing) {

        if (jpaPricing.getVisaTypeGlobalPricing() != null) {
            var visaTypeGlobalPricing = jpaPricing.getVisaTypeGlobalPricing().stream()
                    .filter(ep -> ep.employeeType() == epbm.getContractType())
                    .findFirst();

            if (visaTypeGlobalPricing.isPresent()) {
                visaTypeGlobalPricing.get().globalPrice(epbm.getFixedRate());
            } else {
                jpaPricing.getVisaTypeGlobalPricing().add(new VisaTypeGlobalPricing(epbm.getContractType(), epbm.getFixedRate()));
            }
        } else {
            jpaPricing.setVisaTypeGlobalPricing(List.of(new VisaTypeGlobalPricing(ContractType.EMPLOYEE, epbm.getFixedRate())));
        }

        pricingRepository.save(jpaPricing);
    }

    private void saveOrUpdateCompanyEmployeePricing(EmployeePricingBackfillModel epbm, Long pricingId) {
        List<JpaEmployeePricing> jpaEmployeePricings = employeePricingRepository.findByPricingId(pricingId);
        var filteredJpaEmployeePricing = jpaEmployeePricings.stream()
                .filter(jep -> jep.getType().equals(epbm.getPriceType()))
                .filter(jep -> jep.getEmployeeType().equals(epbm.getContractType()))
                .filter(jep -> jep.getCountry().equals(epbm.getCountryCode()))
                .collect(Collectors.toList());

        if (epbm.getPriceType() == PriceType.VISA) {
            epbm.setContractType(ContractType.EMPLOYEE);
            if (epbm.getValidUntil() == null)
                epbm.setValidUntil(LocalDate.of(9999, 1, 1));

            filteredJpaEmployeePricing = filteredJpaEmployeePricing.stream()
                    .filter(jep -> jep.getValidUntil().equals(epbm.getValidUntil()))
                    .collect(Collectors.toList());
        } else {
            epbm.setValidUntil(null);
        }

        if (filteredJpaEmployeePricing.stream().findFirst().isPresent()) {
            var jpaEmployeePricing = filteredJpaEmployeePricing.stream().findFirst().orElseThrow();
            if (!jpaEmployeePricing.getFixedRate().equals(epbm.getFixedRate())) {
                jpaEmployeePricing.setFixedRate(epbm.getFixedRate());
                jpaEmployeePricing.setUpdatedOn(LocalDateTime.now());
                employeePricingRepository.save(jpaEmployeePricing);
            }
        } else if (!filteredJpaEmployeePricing.stream().findFirst().isPresent()) {
            JpaEmployeePricing jpaEmployeePricing = JpaEmployeePricing.builder()
                    .type(epbm.getPriceType())
                    .employeeType(epbm.getContractType())
                    .fixedRate(epbm.getFixedRate())
                    .country(epbm.getCountryCode())
                    .validUntil(epbm.getValidUntil())
                    .pricingId(pricingId)
                    .createdOn(LocalDateTime.now())
                    .build();
            employeePricingRepository.save(jpaEmployeePricing);
        }
    }

    private void saveEmployeePricing(Map<String, Integer> epMap, JpaPricing savedPricing) {
        Map<CountryCode, JpaEmployeePricing> existingEP = employeePricingRepository.findByPricingId(savedPricing.getId())
                .stream()
                .filter(jpaEmployeePricing -> jpaEmployeePricing.getEmployeeType() == ContractType.EMPLOYEE)
                .collect(Collectors.toMap(JpaEmployeePricing::getCountry, Function.identity()));

        epMap.entrySet().stream().forEach(
                employeePricing -> {
                    CountryCode currentEpCountry = CountryCode.valueOf(employeePricing.getKey());
                    JpaEmployeePricing ep = JpaEmployeePricing.builder()
                            .pricingId(savedPricing.getId())
                            .id(existingEP.containsKey(currentEpCountry) ? existingEP.get(currentEpCountry).getId() : null)
                            .type(PriceType.COUNTRY)
                            .employeeType(ContractType.EMPLOYEE)
                            .fixedRate(employeePricing.getValue().doubleValue())
                            .country(CountryCode.valueOf(employeePricing.getKey()))
                            .build();
                    employeePricingRepository.save(ep);
                }
        );
    }

    @NotNull
    private JpaPricing savePricing(List<JpaDiscountTerm> discountTerms, String[] pricingRow, long companyId, Map<String, Integer> globalPricing) {

        JpaPricing jpaPricing = JpaPricing.builder()
                .companyId(companyId)
                .discountTerms(discountTerms)
                .employeeTypeGlobalPricing(createGlobalPricingObject(globalPricing))
                .build();

        Arrays.stream(PricingBackfillPricingCol.values())
                .forEach(col -> col.mapper(pricingRow, jpaPricing));

        JpaPricing pricing = pricingRepository.findByCompanyId(companyId);

        if (pricing != null) {
            jpaPricing.setId(pricing.getId());
        }

        return pricingRepository.save(jpaPricing);
    }

    private Map<Long, List<JpaDiscountTerm>> processDiscountTerms(CSVReader discountTermsFileCSVReader) throws IOException, CsvException {
        log.info("[PricingBackFiller] Reading discount terms");

        Map<Long, List<JpaDiscountTerm>> discountTermsMap = new HashMap<>();
        List<String[]> readAll = discountTermsFileCSVReader.readAll();

        for (int i = 1, readAllSize = readAll.size(); i < readAllSize; i++) {
            String[] discountTermRow = readAll.get(i);
            Long companyId = Long.valueOf(discountTermRow[0]);
            JpaDiscountTerm jpaDiscountTerm = new JpaDiscountTerm();
            Arrays.stream(PricingBackfilDiscountRuleCol.values())
                    .forEach(col -> col.mapper(discountTermRow, jpaDiscountTerm));

            discountTermsMap.compute(companyId, (k, v) -> {
                if (v == null) {
                    v = new ArrayList<>();
                }
                v.add(jpaDiscountTerm);
                return v;
            });
        }

        log.info("[PricingBackFiller] Reading discount terms Successful");
        return discountTermsMap;
    }

    private List<EmployeeTypeGlobalPricing> createGlobalPricingObject(Map<String, Integer> globalPrices) {
        assert globalPrices.size() == 3 && Set.of(EMPLOYEE, FREELANCER, HR_MEMBER).containsAll(globalPrices.keySet())
                : "There must be 3 values in globalPricing Column";

        List<EmployeeTypeGlobalPricing> defaultGlobalPricing = new ArrayList<>();

        globalPrices.entrySet().forEach(employeeType -> {
            switch (employeeType.getKey()) {
                case EMPLOYEE:
                    defaultGlobalPricing.add(new EmployeeTypeGlobalPricing().employeeType(ContractType.EMPLOYEE).globalPrice(employeeType.getValue().doubleValue()));
                    break;
                case FREELANCER:
                    defaultGlobalPricing.add(new EmployeeTypeGlobalPricing().employeeType(ContractType.FREELANCER).globalPrice(employeeType.getValue().doubleValue()));
                    break;
                case HR_MEMBER:
                    defaultGlobalPricing.add(new EmployeeTypeGlobalPricing().employeeType(ContractType.HR_MEMBER).globalPrice(employeeType.getValue().doubleValue()));
                    break;
                default:
                    throw new ValidationException("Not recognised type. Only EMPLOYEE, FREELANCER, HR_MEMBER are recognised");
            }
        });

        return defaultGlobalPricing;
    }
}
