package com.multiplier.core.payable.pricing;

import com.multiplier.core.payable.adapters.netsuite.models.CountryCode;
import com.multiplier.core.payable.contract.ContractType;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.jackson.Jacksonized;

@Builder(toBuilder = true)
@Getter
@Jacksonized
@EqualsAndHashCode
@RequiredArgsConstructor
public class CountryEmployeePricing implements EmployeePricing {
    private final long id;
    private final ContractType employeeType;
    private final Double fixedRate;
    private final CountryCode country;
}
