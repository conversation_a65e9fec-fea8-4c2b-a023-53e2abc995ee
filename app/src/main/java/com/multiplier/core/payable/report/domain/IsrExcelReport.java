package com.multiplier.core.payable.report.domain;

import com.multiplier.core.payable.report.ISRFileStore;
import com.multiplier.core.payable.report.collector.IsrDataCollector;
import com.multiplier.core.payable.report.dataholder.IsrActionInput;
import com.multiplier.core.payable.report.dataholder.IsrReportDataExtractorInput;
import com.multiplier.core.payable.report.dataholder.IsrReportDataExtractorInputMapper;
import com.multiplier.core.payable.report.exception.IsrGenerationException;
import com.multiplier.core.payable.service.dataholder.SourceReportData;
import com.multiplier.payable.types.CompanyPayableInvoiceSourceReportExportInput;
import com.multiplier.payable.types.CompanyPayableReportType;
import com.multiplier.payable.types.DocumentReadable;
import com.multiplier.payable.types.SalaryInvoiceSourceReportGenerationInput;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.time.Month;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IsrExcelReport implements IsrReport {

    private final IsrReportFactory isrReportFactory;

    private final ISRFileStore isrFileStore;

    private final IsrReportDataExtractorInputMapper mapper;

    private final IsrDataCollector isrDataCollector;

    public IsrExcelReport(final IsrReportFactory isrReportFactory,
            final ISRFileStore isrFileStore,
            final IsrReportDataExtractorInputMapper mapper,
            final @Qualifier("versionAware") IsrDataCollector isrDataCollector) {
        this.isrReportFactory = isrReportFactory;
        this.isrFileStore = isrFileStore;
        this.mapper = mapper;
        this.isrDataCollector = isrDataCollector;
    }

    @Override
    @SneakyThrows
    public DocumentReadable generate(CompanyPayableInvoiceSourceReportExportInput input) {
        var isrReportDataExtractorInput = mapper.map(input);
        isrReportDataExtractorInput.validate();
        int month = isrReportDataExtractorInput.getMonth();
        int year = isrReportDataExtractorInput.getYear();
        var invoiceType = input.getInvoiceType();
        Long inputCompanyId = isrReportDataExtractorInput.getCompanyId();
        if (inputCompanyId == null) {
            if (CollectionUtils.isEmpty(isrReportDataExtractorInput.getCompanyIds()) || isrReportDataExtractorInput.getCompanyIds().size() > 1) {
                throw new IsrGenerationException(null, isrReportDataExtractorInput.getMonth(), isrReportDataExtractorInput.getYear(), "Single Company ID is required");
            } else {
                inputCompanyId = isrReportDataExtractorInput.getCompanyIds().stream()
                        .findAny()
                        .orElseThrow(() -> new IsrGenerationException(null, isrReportDataExtractorInput.getMonth(), isrReportDataExtractorInput.getYear(), "Company ID is required"));
            }
        }
        var finalIsrReportDataExtractorInput = isrReportDataExtractorInput.toBuilder().companyId(inputCompanyId).build();
        var companyId = inputCompanyId;

        var isrActionInput = IsrActionInput.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .invoiceType(invoiceType)
                .build();
        var collectedData = isrDataCollector.collect(isrActionInput);
        return Optional.ofNullable(collectedData)
                .map(data -> {
                    var fileEncodedString = buildDocumentEncodedString(finalIsrReportDataExtractorInput, data)
                            .orElseThrow(() -> {
                                var errorMessage = "Failed to generate ISR report for " + isrActionInput.toString();
                                return new IsrGenerationException(companyId, month, year, errorMessage);
                            });
                    var fileName = "";
                    if (Boolean.TRUE.equals(finalIsrReportDataExtractorInput.getStoreFile())) {
                        fileName = isrFileStore.store(
                                IsrKey.builder()
                                        .companyId(companyId)
                                        .month(month)
                                        .year(year)
                                        .build(),
                                fileEncodedString
                        );
                    } else {
                        fileName = defaultFileName(isrReportDataExtractorInput);
                    }
                    if (fileName.isEmpty()) {
                        var errorMessage = "Failed to upload ISR report to S3 " + isrActionInput.toString();
                        throw new IsrGenerationException(companyId, month, year, errorMessage);
                    }
                    return convertToDocumentReadable(fileName, fileEncodedString);
                })
                .orElseThrow(() -> {
                            var errorMessage = "Failed to generate ISR report " + isrActionInput.toString();
                            return new IsrGenerationException(companyId, month, year, errorMessage);
                        }
                );
    }

    @SuppressWarnings("unused")
    @Override
    public Map<Long, Boolean> generateMultiple(SalaryInvoiceSourceReportGenerationInput input) {
        var companyIds = input.getCompanyIds();
        int month = input.getPayableMonth().getMonth();
        int year = input.getPayableMonth().getYear();
        var result = new HashMap<Long, Boolean>();

        for (var companyId : companyIds) {
            var isrActionInput = IsrActionInput.builder()
                    .companyId(companyId)
                    .month(month)
                    .year(year)
                    .invoiceType(2)
                    .build();
            log.info("Generating ISR report for {}", isrActionInput);
            var successful = generateSalaryIsrForSingleCompany(isrActionInput);
            result.put(companyId, successful);
        }
        return result;
    }

    @Override
    public boolean generateSalaryIsrForSingleCompany(IsrActionInput isrActionInput) {
        log.info("ISR action input {}", isrActionInput);
        var financialData = isrDataCollector.collect(isrActionInput).stream()
                .filter(data -> isrActionInput.getCompanyPayableId() == null || Objects.equals(isrActionInput.getCompanyPayableId(), data.getPayableId()))
                .toList();
        // fail if there are group with NULL payableId and multiple groups
        var payableIdGroupNumber = financialData.stream().map(SourceReportData::getPayableId).distinct().count();
        if (payableIdGroupNumber > 1 && financialData.stream().anyMatch(data -> data.getPayableId() == null)) {
            log.error("There are records with NULL payableId. Unable to split ISR for {}", isrActionInput);
            return false;
        }

        var financialDataSplitByPayable = financialData.stream()
                .collect(Collectors.groupingBy(SourceReportData::getPayableId, Collectors.toList()));

        var isrReportDataExtractorInput = IsrReportDataExtractorInput.builder()
                .companyId(isrActionInput.getCompanyId())
                .month(isrActionInput.getMonth())
                .year(isrActionInput.getYear())
                .type(CompanyPayableReportType.SALARY_INVOICE_SOURCE_REPORT)
                .storeFile(true)
                .build();

        boolean successful = true;
        for (var entry : financialDataSplitByPayable.entrySet()) {
            long payableId = entry.getKey();
            var financialDataForPayableId = entry.getValue();
            log.info("Generating and uploading ISR for {} payable ID {}", isrActionInput, payableId);
            var encodedResultOptional = buildDocumentEncodedString(isrReportDataExtractorInput, financialDataForPayableId);
            if (encodedResultOptional.isEmpty()) {
                successful = false;
                break;
            }
            uploadToS3(isrReportDataExtractorInput, payableId, encodedResultOptional.get());
        }
        return successful;
    }

    private Optional<String> buildDocumentEncodedString(
            IsrReportDataExtractorInput input,
            List<SourceReportData> financialData
    ) {
        long companyId = input.getCompanyId();
        int month = input.getMonth();
        int year = input.getYear();
        try (var outputStream = isrReportFactory.buildOutputStream(input, financialData)) {
            return Optional.of(Base64.getEncoder().encodeToString(outputStream.toByteArray()));
        } catch (Exception e) {
            log.error("Failed to generate ISR report for company ID {} month {} year {}", companyId, month, year, e);
            return Optional.empty();
        }
    }

    private boolean uploadToS3(IsrReportDataExtractorInput input, @Nullable Long payableId, String fileData) {
        log.info("Uploading ISR report to S3 for company ID {} month {} year {}", input.getCompanyId(), input.getMonth(), input.getYear());
        var isrKey = IsrKey.builder()
                .companyId(input.getCompanyId())
                .month(input.getMonth())
                .year(input.getYear())
                .companyPayableId(payableId)
                .build();
        var fileName = "";
        try {
            fileName = isrFileStore.store(isrKey, fileData);
        } catch (Exception e) {
            log.error("Failed to upload ISR report to S3 for company ID {} month {} year {}", input.getCompanyId(), input.getMonth(), input.getYear(), e);
        }
        return !fileName.isEmpty();
    }

    private DocumentReadable convertToDocumentReadable(String fileName, String fileData) {
        return DocumentReadable.newBuilder()
                .blob(fileData)
                .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                .id(System.currentTimeMillis())
                .name(fileName)
                .extension("xlsx")
                .build();
    }

    private String defaultFileName(IsrReportDataExtractorInput input) {
        return Month.of(input.getMonth()).getDisplayName(TextStyle.FULL, Locale.ENGLISH) +
                input.getYear() + "_INVOICE_SOURCE_REPORT";
    }

}
