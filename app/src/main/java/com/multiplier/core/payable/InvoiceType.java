package com.multiplier.core.payable;

// TODO How is this enum different with the com.multiplier.payable.types.InvoiceType?
// If it has different meaning, why is it named the same as com.multiplier.payable.types.InvoiceType?
@Deprecated
public enum InvoiceType {
    ANY_INVOICE,
    FIRST_INVOICE,
    SECOND_INVOICE,
    DEPOSIT_INVOICE,
    FREELANCER_INVOICE;

    public static InvoiceType getByInvoiceNumber(int invoiceNumber) {
        switch (invoiceNumber) {
            case 1 : return FIRST_INVOICE;
            case 2 : return SECOND_INVOICE;
            default: return ANY_INVOICE;
        }
    }
}
