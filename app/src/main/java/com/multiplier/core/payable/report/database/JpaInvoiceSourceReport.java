package com.multiplier.core.payable.report.database;

import com.multiplier.core.payable.report.domain.IsrKey;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.envers.Audited;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Audited
@EntityListeners(AuditingEntityListener.class)
@Entity
@Table(name = "invoice_source_report", schema = "payable", indexes = {
        @Index(name = "unique_isr_hash", columnList = "hash")
})
@SequenceGenerator(name = "invoice_source_report_seq_gen", schema = "payable", sequenceName = "invoice_source_report_seq", allocationSize = 1, initialValue = 1)
public class JpaInvoiceSourceReport {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "invoice_source_report_seq_gen")
    private Long id;

    @Column(updatable = false)
    @CreatedDate
    private LocalDateTime createdOn;

    @Column(updatable = false)
    @CreatedBy
    private Long createdBy;

    @LastModifiedDate
    private LocalDateTime updatedOn;

    @LastModifiedBy
    private Long updatedBy;

    @Column(nullable = false)
    private Long companyId;

    @Column(nullable = false)
    private Integer month;

    @Column(nullable = false)
    private Integer year;

    private Long companyPayableId;

    private String externalFileUrl;

    @Column(unique = true, nullable = false)
    private String fileName;

    @Column(unique = true, nullable = false)
    private String hash;

    @Column
    private Long manuallyUploadedBy;

    public IsrKey toISRKey() {
        return IsrKey.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .companyPayableId(companyPayableId)
                .build();
    }
}