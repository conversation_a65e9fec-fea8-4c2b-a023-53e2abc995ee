package com.multiplier.core.payable.report.dataholder;

import com.multiplier.payable.types.CompanyPayableInvoiceSourceReportExportInput;
import com.multiplier.payable.types.CompanyPayableReportType;
import lombok.RequiredArgsConstructor;
import lombok.val;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class IsrReportDataExtractorInputMapper {
    public IsrReportDataExtractorInput map(CompanyPayableInvoiceSourceReportExportInput input) {
        if ( input == null ) {
            return null;
        }

        IsrReportDataExtractorInput.IsrReportDataExtractorInputBuilder isrReportDataExtractorInput = IsrReportDataExtractorInput.builder();

        isrReportDataExtractorInput.companyIds( mapCompanyIds( input ) );
        isrReportDataExtractorInput.type( mapType( input ) );
        val inputPayableMonthYearCycle = input.getPayableMonth();
        isrReportDataExtractorInput.month( inputPayableMonthYearCycle.getMonth() );
        isrReportDataExtractorInput.year( inputPayableMonthYearCycle.getYear() );
        isrReportDataExtractorInput.cycle( inputPayableMonthYearCycle.getCycle() );
        isrReportDataExtractorInput.companyId( input.getCompanyId() );
        isrReportDataExtractorInput.storeFile( input.getStoreFile() );

        return isrReportDataExtractorInput.build();
    }

    private List<Long> mapCompanyIds(CompanyPayableInvoiceSourceReportExportInput input) {
        if (input.getCompanyIds() != null && !input.getCompanyIds().isEmpty()) {
            return input.getCompanyIds();
        }
        if (input.getCompanyId() != null) {
            return List.of(input.getCompanyId());
        }
        return List.of();
    }

    private CompanyPayableReportType mapType(CompanyPayableInvoiceSourceReportExportInput input) {
        if (input.getInvoiceType() == null) {
            return null;
        }
        return switch (input.getInvoiceType()) {
            case 1 -> CompanyPayableReportType.GROSS_INVOICE_SOURCE_REPORT;
            case 2 -> CompanyPayableReportType.SALARY_INVOICE_SOURCE_REPORT;
            default -> null;
        };
    }
}
