package com.multiplier.core.payable.graphql;

import com.multiplier.core.payable.service.PricingService;
import com.multiplier.payable.DgsConstants;
import com.multiplier.payable.types.Pricing;
import com.multiplier.payable.types.PricingInput;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import com.netflix.graphql.dgs.DgsMutation;
import com.netflix.graphql.dgs.InputArgument;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;

@DgsComponent
@AllArgsConstructor
@Slf4j
public class PricingDataMutator {

    private final PricingService pricingService;

    @PreAuthorize("(@me.allowed('update.operations.company.pricing') " +
            "|| @me.allowed('update.operations.payables'))")
    @DgsMutation(field = DgsConstants.MUTATION.UpdateCompanyPricing)
    public Pricing updateCompanyPricing(
            @InputArgument("companyId") Long companyId,
            @InputArgument("pricing") PricingInput pricingInput,
            @InputArgument("skipCreatingMsaAddendum") boolean skipCreatingMsaAddendum,
            DgsDataFetchingEnvironment dfe
    ) {
        return pricingService.updateCompanyPricingInternal(companyId, pricingInput, skipCreatingMsaAddendum);
    }
}
