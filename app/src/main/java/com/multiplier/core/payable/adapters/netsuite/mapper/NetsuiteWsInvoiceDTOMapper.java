package com.multiplier.core.payable.adapters.netsuite.mapper;

import com.multiplier.core.util.IgnoreUnmappedMapperConfig;
import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.adapters.api.LineItemDTO;
import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.adapters.netsuite.client.NetsuiteMappings;
import com.multiplier.payable.types.CurrencyCode;
import com.multiplier.payable.types.InvoiceStatus;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.*;
import com.netsuite.suitetalk.proxy.v2023_1.transactions.sales.Invoice;
import com.netsuite.suitetalk.proxy.v2023_1.transactions.sales.InvoiceItem;
import com.netsuite.suitetalk.proxy.v2023_1.transactions.sales.InvoiceItemList;
import lombok.val;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

@Mapper(componentModel = "spring", config = IgnoreUnmappedMapperConfig.class)
public interface NetsuiteWsInvoiceDTOMapper {
    @Mapping(source = "internalId", target = "externalId")
    @Mapping(source = "tranId", target = "invoiceNo")
    @Mapping(source = "tranDate", target = "date")
    @Mapping(source = "entity", target = "customerId", qualifiedByName = "mapFromEntity")
    @Mapping(source = "currency", target = "billingCurrencyCode", qualifiedByName = "mapFromCurrency")
    @Mapping(source = "itemList", target = "lineItems", qualifiedByName = "mapFromInvoiceItemList")
    @Mapping(source = "total", target = "totalAmount")
    @Mapping(source = "memo", target = "reference")
    @Mapping(source = "status", target = "status", qualifiedByName = "mapStatusToInvoiceStatus")
    InvoiceDTO map(Invoice invoice);

    List<InvoiceDTO> map(List<Invoice> invoices);

    @Mapping(source = "rate", target = "unitAmount")
    @Mapping(source = "invoiceItem", target = "itemType", qualifiedByName = "mapFromItem")
    @Mapping(source = "taxCode", target = "taxType", qualifiedByName = "mapFromTaxCode")
    @Mapping(source = "_class", target = "countryName", qualifiedByName = "mapFromClass")
    @Mapping(target = "taxCode", ignore = true)
    LineItemDTO map(InvoiceItem invoiceItem);


    @Named("mapFromInvoiceItemList")
    default List<LineItemDTO> mapInvoiceItemList(InvoiceItemList invoiceItemList) {
        if (invoiceItemList == null || invoiceItemList.getItem().length == 0) {
            return List.of();
        }
        List<LineItemDTO> lineItemDTOS = new LinkedList<>();
        for (InvoiceItem invoiceItem : invoiceItemList.getItem()) {
            var lineItemDTO = this.map(invoiceItem);
            if (lineItemDTO == null) {
                continue;
            }
            if (invoiceItem.getItem() != null) {
                lineItemDTO.setExternalId(invoiceItem.getItem().getInternalId());
            }
            CustomFieldList customFieldList = invoiceItem.getCustomFieldList();
            for (CustomFieldRef customFieldRef : customFieldList.getCustomField()) {
                switch (customFieldRef.getScriptId()) {
                    case "custcol_contract_id":
                        Long contractId = null;
                        if (customFieldRef instanceof  StringCustomFieldRef) {
                            contractId = Optional.ofNullable(((StringCustomFieldRef) customFieldRef).getValue())
                                    .map(Long::parseLong).orElse(null);
                        } else if (customFieldRef instanceof LongCustomFieldRef) {
                            contractId = ((LongCustomFieldRef) customFieldRef).getValue();
                        }
                        lineItemDTO.setContractId(contractId);
                        break;
                    case "custcol_mlt_base_currency":
                        val baseCurrency = ((StringCustomFieldRef) customFieldRef).getValue();
                        lineItemDTO.setBaseCurrency(baseCurrency);
                        break;
                    case "custcol_mlt_amount_base_currency":
                        Double amountInBaseCurrency = null;
                        if (customFieldRef instanceof StringCustomFieldRef) {
                            amountInBaseCurrency = Optional.ofNullable(((StringCustomFieldRef) customFieldRef).getValue())
                                    .map(Double::parseDouble).orElse(null);
                        } else if (customFieldRef instanceof DoubleCustomFieldRef) {
                            amountInBaseCurrency = ((DoubleCustomFieldRef) customFieldRef).getValue();
                        }
                        lineItemDTO.setAmountInBaseCurrency(amountInBaseCurrency);
                        break;
                    default:
                        break;
                }
            }
            lineItemDTOS.add(lineItemDTO);
        }

        return lineItemDTOS;
    }

    @Named("mapFromEntity")
    default String mapFromEntity(RecordRef recordRef) {
        return Optional.ofNullable(recordRef).map(RecordRef::getInternalId).orElse(null);
    }

    @Named("mapFromItem")
    default LineItemType mapFromItem(InvoiceItem invoiceItem) {
        return Optional.ofNullable(NetsuiteMappings.fromItemIdAndDescription(invoiceItem.getItem().getInternalId(), invoiceItem.getDescription()))
                .map(LineItemType::fromRefName)
                .orElse(null);
    }

    @Named("mapFromCurrency")
    default CurrencyCode mapFromCurrency(RecordRef recordRef) {
        return Optional.ofNullable(recordRef).map(r -> NetsuiteMappings.fromCurrencyId(r.getInternalId())).orElse(null);
    }

    @Named("mapFromTaxCode")
    default String mapFromTaxCode(RecordRef recordRef) {
        return recordRef != null ? NetsuiteMappings.fromTaxCodeId(recordRef.getInternalId()) : null;
    }

    @Named("mapFromClass")
    default String mapFromClass(RecordRef recordRef) {
        return recordRef != null ? NetsuiteMappings.fromCountryId(recordRef.getInternalId()) : null;
    }

    @Named("mapStatusToInvoiceStatus")
    default InvoiceStatus mapStatusToInvoiceStatus(String status) {
        if ( status == null || status.isEmpty()) {
            return null;
        }

        return switch (status) {
            case "Pending Approval", "DRAFT" -> InvoiceStatus.DRAFT;
            case "Open", "AUTHORIZED", "Approved" -> InvoiceStatus.AUTHORIZED;
            case "Paid", "PAID", "Paid in Full" -> InvoiceStatus.PAID;
            case "Rejected", "VOIDED" -> InvoiceStatus.VOIDED;
            case "SUBMITTED" -> InvoiceStatus.SUBMITTED;
            case "DELETED" -> InvoiceStatus.DELETED;
            case "PENDING" -> InvoiceStatus.PENDING;
            case "OVERDUE" -> InvoiceStatus.OVERDUE;
            default -> throw new IllegalArgumentException("Unexpected enum constant: " + status);
        };
    }
}
