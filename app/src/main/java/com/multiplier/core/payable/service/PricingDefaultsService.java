package com.multiplier.core.payable.service;

import com.multiplier.core.payable.service.mapper.EmployeePricingMapper;
import com.multiplier.payable.types.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class PricingDefaultsService {

    private final PricingFactory pricingFactory;

    private final EmployeePricingMapper employeePricingMapper;

    @Transactional
    public EmployeePricingDefault getEmployeePricingDefaultCountries() {
        return getEmployeePricingDefaultCountriesPrivate();
    }

    public EmployeePricingDefault getEmployeeVisaPricingDefaultCountries() {
        List<EmployeePricing> employeeVisaPricings = new ArrayList<>();
        pricingFactory.getCountryVisaPricing().forEach(
                (pair, price) -> {
                    EmployeePricing countryEmployeePricing = VisaEmployeePricing.newBuilder()
                            .id(pair.getFirst().ordinal())
                            .country(pair.getFirst())
                            .employeeType(pair.getSecond())
                            .fixedRate(price)
                            .build();
                    employeeVisaPricings.add(countryEmployeePricing);
                }
        );

        List<GlobalPricing> globalPricings = new ArrayList<>();

        return EmployeePricingDefault
                .newBuilder()
                .employeePricings(employeeVisaPricings)
                .globalPricing(globalPricings)
                .build();
    }

    @NotNull
    public PricingInput getDefaultPricingForCompany() {
        List<DiscountTermInput> defaultDiscountTerms = getDefaultDiscountTerms();
        List<CompanyGlobalPricingInput> defaultGlobalPricing = getDefaultGlobalPricingInput();
        List<CompanyVisaGlobalPricingInput> defaultVisaGlobalPricing = getDefaultVisaGlobalPricingInput();
        List<CompanyVisaPricingInput> defaultVisaPricing = employeePricingMapper.mapToVisaInput(getEmployeeVisaPricingDefaultCountries().getEmployeePricings());
        List<CompanyEmployeePricingInput> defaultEmployeePricing = employeePricingMapper.mapToInput(getEmployeePricingDefaultCountriesPrivate().getEmployeePricings());
        List<OffboardingGlobalPricingInput> defaultOffboardingGlobalPricing =
                OffboardingPricingService.getDefaultOffboardingGlobalPricingInput();

        return PricingInput.newBuilder()
                .discountTerms(defaultDiscountTerms)
                .globalPricing(defaultGlobalPricing)
                .visaGlobalPricing(defaultVisaGlobalPricing)
                .visaPricing(defaultVisaPricing)
                .employeePricing(defaultEmployeePricing)
                .offboardingGlobalPricing(defaultOffboardingGlobalPricing)
                .build();
    }

    @NotNull
    ArrayList<DiscountTermInput> getDefaultDiscountTerms() {
        return new ArrayList<>();
    }

    @NotNull
    List<CompanyGlobalPricingInput> getDefaultGlobalPricingInput() {
        return pricingFactory.getDefaultGlobalPricing()
                .stream()
                .map(val -> CompanyGlobalPricingInput.newBuilder()
                        .employeeType(val.employeeType())
                        .fixedRate(val.globalPrice())
                        .build())
                .collect(Collectors.toList());
    }

    @NotNull
    List<CompanyVisaGlobalPricingInput> getDefaultVisaGlobalPricingInput() {
        return pricingFactory.getDefaultVisaGlobalPricing()
                .stream()
                .map(val -> CompanyVisaGlobalPricingInput.newBuilder()
                        .employeeType(val.employeeType())
                        .fixedRate(val.globalPrice())
                        .build())
                .collect(Collectors.toList());
    }

    private EmployeePricingDefault getEmployeePricingDefaultCountriesPrivate() {
        var employeePricings = new ArrayList<EmployeePricing>();
        pricingFactory.getCountryPricing().forEach(
                (pair, price) -> {
                    CountryEmployeePricing countryEmployeePricing = CountryEmployeePricing.newBuilder()
                            .id(pair.getFirst().ordinal())
                            .country(pair.getFirst())
                            .employeeType(pair.getSecond())
                            .fixedRate(price)
                            .build();
                    employeePricings.add(countryEmployeePricing);
                }
        );
        var employeeVisaPricings = getEmployeeVisaPricingDefaultCountries().getEmployeePricings();

        var globalPricings = pricingFactory.getDefaultGlobalPricing()
                .stream()
                .map(val -> GlobalPricing.newBuilder()
                        .fixedRate(val.globalPrice())
                        .employeeType(val.employeeType())
                        .build())
                .collect(Collectors.toList());

        var visaGlobalPricings = pricingFactory.getDefaultVisaGlobalPricing()
                .stream()
                .map(val -> VisaGlobalPricing.newBuilder()
                        .fixedRate(val.globalPrice())
                        .employeeType(val.employeeType())
                        .build())
                .toList();

        return EmployeePricingDefault
                .newBuilder()
                .employeePricings(employeePricings)
                .visaPricing(employeeVisaPricings)
                .globalPricing(globalPricings)
                .visaGlobalPricing(visaGlobalPricings)
                .build();
    }
}
