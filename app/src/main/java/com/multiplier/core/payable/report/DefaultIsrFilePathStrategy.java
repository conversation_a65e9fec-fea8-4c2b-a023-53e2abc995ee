package com.multiplier.core.payable.report;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public class DefaultIsrFilePathStrategy implements IsrFilePathStrategy {
    private int invoiceMonth;
    private int invoiceYear;

    public String generateFilePath() {
        String formattedYear = String.format("%04d", invoiceYear);
        String formattedMonth = String.format("%02d", invoiceMonth);

        return "invoices/reports/isr/" + formattedYear + formattedMonth;
    }
}
