package com.multiplier.core.payable.report.dataholder;

import com.multiplier.payable.types.CompanyPayableReportType;
import com.multiplier.core.payable.report.exception.IsrGenerationException;
import lombok.*;
import lombok.extern.jackson.Jacksonized;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Builder(toBuilder = true)
@Getter
@Jacksonized
@EqualsAndHashCode
public class IsrReportDataExtractorInput {
    private final Long companyId;
    private List<Long> companyIds;
    private final Integer month;
    private final Integer year;
    private final Integer cycle;
    private final CompanyPayableReportType type;
    @Builder.Default
    private final Boolean storeFile = false;

    public void validate() throws IsrGenerationException {
        if (companyId == null && (companyIds == null || companyIds.isEmpty())) {
            throw new IsrGenerationException(null, month, year, "Company ID(s) is mandatory");
        }
        if (storeFile && getCompanyId() == null && companyIds.size() > 1) {
            throw new IsrGenerationException(null, month, year, "Input ONLY one company to store ISR file");
        }
        if (type == null) {
            throw new IsrGenerationException(companyId, month, year, "Type for ISR is mandatory");
        }
    }

    @Override
    public String toString() {
        return "IsrReportDataExtractorInput{" +
                "companyId=" + companyId +
                ", companyIds=" + (companyIds == null ? "" : companyIds.stream().map(Objects::toString).collect(Collectors.joining(", "))) +
                ", month=" + month +
                ", year=" + year +
                ", cycle=" + cycle +
                ", type=" + type +
                ", storeFile=" + storeFile +
                '}';
    }
}
