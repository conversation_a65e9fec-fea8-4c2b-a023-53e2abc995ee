package com.multiplier.core.payable.service.lineitem;

import com.multiplier.core.payable.repository.model.JpaInvoice;
import com.multiplier.core.payable.service.exception.InvoiceGenerationErrorCode;
import com.multiplier.core.payable.service.exception.InvoiceGenerationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
class FirstInvoiceItemsProcessor implements LineItemProcessor<JpaInvoice> {
    private final ProcessedLineItemMapper processedLineItemMapper;
    private final LineItemChecker lineItemChecker;

    @Override
    public List<ProcessedLineItem> process(List<JpaInvoice> firstInvoices, Map<Long, MetadataItem> metadataItemsByContractId) {
        log.info("Processing first invoice items.");
        var lineItemsByContractId = groupLineItemsByContractIdFromInvoices(firstInvoices, metadataItemsByContractId);
        var companyId = metadataItemsByContractId.values().stream()
                .map(MetadataItem::getCompanyId)
                .findFirst()
                .get();

        checkContractsOnMultipleInvoices(lineItemsByContractId, companyId);

        return lineItemsByContractId.values().stream()
                .flatMap(Collection::stream)
                .filter(lineItemChecker::check)
                .map(lineItemChecker::update)
                .filter(lineItem -> lineItem.getItemCategory() != LineItemCategory.SUPPLEMENTS)
                .toList();
    }

    private HashMap<Long, LinkedHashSet<ProcessedLineItem>> groupLineItemsByContractIdFromInvoices(List<JpaInvoice> firstInvoices,
                                                                                                   Map<Long, MetadataItem> metadataByContractId) {
        var aggregatedLineItemsByContractId = new HashMap<Long, LinkedHashSet<ProcessedLineItem>>();
        for (JpaInvoice currentFirstInvoice : firstInvoices) {
            var lineItemsByContractId = currentFirstInvoice.getLineItems().stream()
                    .map(invoiceLineItem -> {
                        var metadata = metadataByContractId.get(invoiceLineItem.getContractId());
                        var invoiceNo = currentFirstInvoice.getInvoiceNo();
                        return processedLineItemMapper.mapFromInvoiceItem(invoiceLineItem, metadata, invoiceNo);
                    })
                    .collect(Collectors.groupingBy(
                            ProcessedLineItem::getContractId,
                            Collectors.toCollection(LinkedHashSet::new)));

            lineItemsByContractId.forEach((contract, aggregatedInvoiceItem) ->
                    aggregatedLineItemsByContractId.computeIfAbsent(contract, k -> new LinkedHashSet<>())
                            .addAll(aggregatedInvoiceItem));
        }

        return aggregatedLineItemsByContractId;
    }

    private void checkContractsOnMultipleInvoices(HashMap<Long, LinkedHashSet<ProcessedLineItem>> lineItemsByContractId,
                                                  Long companyId) {
        lineItemsByContractId.forEach((contractId, aggregateInvoiceItems) -> {
            log.info("checking if contractId: {} is present on other invoice.", contractId);
            var uniqueInvoiceNo = aggregateInvoiceItems.stream()
                    .map(ProcessedLineItem::getInvoiceNo)
                    .distinct()
                    .toList();

            if (uniqueInvoiceNo.size() > 1) {

                var errorMessage = String.format("contractId: %s is present on the the ff. first invoices: %s", contractId, uniqueInvoiceNo);
                log.error(errorMessage);
                throw new InvoiceGenerationException(InvoiceGenerationErrorCode.MPE_DUPLICATE_CONTRACT_ON_MULTIPLE_INVOICES,
                        companyId, contractId, errorMessage);
            }
        });
    }
}
