package com.multiplier.core.payable.creditnote.database;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.payable.types.CurrencyCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class JpaCreditNoteLineItem {

    private LineItemType itemType;
    private String description;
    private Double quantity;
    private Double unitAmount;
    private Double grossAmount;
    private Double taxAmount;
    // TODO Change the type to Double
    private String taxRate;
    private String taxCode;
    private String countryName;

    // Custom fields
    private Long contractId;
    private String memberName;
    private CurrencyCode baseCurrency;
    private Double amountInBaseCurrency;
    private Double fxRate;
    private LocalDate startPayCycleDate;
    private LocalDate endPayCycleDate;
    private List<UUID> companyPayableLineItemIds;
}
