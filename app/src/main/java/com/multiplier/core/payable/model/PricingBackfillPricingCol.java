package com.multiplier.core.payable.model;

import com.multiplier.core.payable.repository.model.JpaPricing;
import com.multiplier.payable.types.CurrencyCode;

import java.util.function.BiFunction;

public enum PricingBackfillPricingCol {
    COMPANY_ID((v, db) -> db), // not computed here.
    SETUP_FEE((v, db) -> {
        db.setSetupFee(v.isEmpty() ? 0.00 : Double.parseDouble(v));
        return db;
    }),
    SERVICES((v, db) -> {
        db.setServices(v);
        return db;
    }),
    BILLING_CURRENCY((v, db) -> {
        db.setBillingCurrencyCode(CurrencyCode.valueOf(v));
        return db;
    }),
    PAYMENT_TERMS((v, db) -> {
        db.setPaymentTermInDays(v.isEmpty() ? 0 : Integer.parseInt(v));
        return db;
    }),
    SERVICE_FEE((v, db) -> {
        db.setServiceFee(0.00);
        return db;
    }),
    DEPOSIT_TERM((v, db) -> {
        db.setDepositTermInMonths(v.isEmpty() ? 0 : Integer.parseInt(v));
        return db;
    }),
    DEPOSIT_CAP((v, db) -> {
        db.setMaxDeposit(v.isEmpty() ? -1 : Double.parseDouble(v));
        return db;
    }),
    RESOURCE_CLAIM((v,db) -> {
        db.setResourceClaim(v);
        return db;
    });

    private BiFunction<String, JpaPricing, JpaPricing> mapperFunction;

    PricingBackfillPricingCol(BiFunction<String, JpaPricing, JpaPricing> mapperFunction) {
        this.mapperFunction = mapperFunction;
    }

    public JpaPricing mapper(String[] row, JpaPricing JpaPricing) {
        String cellValue = row[this.ordinal()];
        return this.mapperFunction.apply(cellValue, JpaPricing);
    }
}
