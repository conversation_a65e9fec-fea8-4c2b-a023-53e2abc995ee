package com.multiplier.core.payable.adapters.netsuite.models.savedsearch;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SavedSearchResp {
    @Nullable
    private String data;
    @Nullable
    private Boolean moreRecordsAvailable;
}
