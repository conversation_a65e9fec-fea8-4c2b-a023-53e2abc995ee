package com.multiplier.core.payable.service;

import com.google.common.annotations.VisibleForTesting;
import com.google.type.Date;
import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.contract.schema.compensation.CompensationOuterClass;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.core.anomalydetector.InvoiceAnomalyDetectorService;
import com.multiplier.core.anomalydetector.model.AnomalyResultType;
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest;
import com.multiplier.core.payable.adapters.*;
import com.multiplier.core.payable.adapters.api.InvoiceGenerationAdapter;
import com.multiplier.core.payable.adapters.api.InvoiceGenerationFactory;
import com.multiplier.core.payable.adapters.api.LineItemDTO;
import com.multiplier.core.payable.adapters.netsuite.exception.NetsuiteAdapterException;
import com.multiplier.core.payable.calculator.PayableCompensationCalculator;
import com.multiplier.core.payable.company.ManagementFee;
import com.multiplier.core.payable.company.adapter.CompanyServiceAdapter;
import com.multiplier.core.payable.companypayable.database.CompanyPayableDtoMapper;
import com.multiplier.core.payable.companypayable.database.JpaPayableItemMapper;
import com.multiplier.core.payable.companypayable.database.PayableItemDtoMapper;
import com.multiplier.core.payable.creditnote.database.CreditNoteDto;
import com.multiplier.core.payable.creditnote.facade.UpdateCreditNoteRequest;
import com.multiplier.core.payable.currencyexchange.CurrencyExchangeService;
import com.multiplier.core.payable.event.database.RecordType;
import com.multiplier.core.payable.freelancerinvoice.MemberPayableExternalInvoiceService;
import com.multiplier.core.payable.generation.processingfee.ProcessingFeePayableItemRequest;
import com.multiplier.core.payable.generation.processingfee.ProcessingFeeService;
import com.multiplier.core.payable.invoice.database.InvoiceService;
import com.multiplier.core.payable.invoice.database.JpaInvoiceRepository;
import com.multiplier.core.payable.invoice.database.LineItemDtoFromPayableItemDtoMapper;
import com.multiplier.core.payable.pricing.adapter.PricingServiceAdapter;
import com.multiplier.core.payable.report.composition.InvoiceSourceReportCompositionService;
import com.multiplier.core.payable.report.database.InvoiceSourceReportService;
import com.multiplier.core.payable.report.dataholder.IsrDataHolder;
import com.multiplier.core.payable.report.dataholder.IsrReportDataExtractorInput;
import com.multiplier.core.payable.report.dataholder.IsrReportDataExtractorInputMapper;
import com.multiplier.core.payable.report.exception.IsrGenerationException;
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository;
import com.multiplier.core.payable.repository.JpaPricingRepository;
import com.multiplier.core.payable.repository.model.InvoiceType;
import com.multiplier.core.payable.repository.model.*;
import com.multiplier.core.payable.service.dataholder.*;
import com.multiplier.core.payable.service.deposit.DepositPayableItemService;
import com.multiplier.core.payable.service.exception.CurrencyConversionException;
import com.multiplier.common.exception.MplBusinessException;
import com.multiplier.common.exception.MplSystemException;
import com.multiplier.core.exception.PayableErrorCode;
import com.multiplier.core.payable.service.exception.InvoiceGenerationErrorCode;
import com.multiplier.core.payable.service.exception.InvoiceGenerationException;
import com.multiplier.core.payable.service.firstinvoice.FirstInvoiceEligibleContractsProvider;
import com.multiplier.core.payable.service.firstinvoice.FirstInvoiceEligibleContractsProviderFactory;
import com.multiplier.core.payable.service.insurance.InsurancePayableItemService;
import com.multiplier.core.payable.service.lineitem.JpaPayableItemFromProcessedLineItemMapper;
import com.multiplier.core.payable.service.lineitem.LineItemDtoFromProcessedLineItemMapper;
import com.multiplier.core.payable.service.lineitem.SecondInvoiceLineItemService;
import com.multiplier.core.payable.service.managementfee.ManagementFeeModifier;
import com.multiplier.core.payable.service.managementfee.ManagementFeePayableItemService;
import com.multiplier.core.payable.service.managementfee.rules.PricingRuleEngine;
import com.multiplier.core.payable.service.mapper.CompanyPayableReportMapper;
import com.multiplier.core.payable.service.mapper.PayableMapper;
import com.multiplier.core.payable.service.mapper.PayableReportKeyMapper;
import com.multiplier.core.payable.service.query.builder.CompanyPayablePageRequestBuilder;
import com.multiplier.core.payable.service.secondinvoice.CreditNoteRequestBuilder;
import com.multiplier.core.payable.service.secondinvoice.NegativeInvoiceCreditNoteService;
import com.multiplier.core.payable.service.util.InvoiceError;
import com.multiplier.core.payable.tax.TaxCodeService;
import com.multiplier.core.payable.utils.SpecificationBuilder;
import com.multiplier.core.util.dto.payroll.CompanyMemberPayWrapper;
import com.multiplier.core.util.dto.payroll.CompanyPayrollWrapper;
import com.multiplier.country.schema.Country;
import com.multiplier.member.schema.Member;
import com.multiplier.payable.kafka.schema.EventType;
import com.multiplier.payable.service.CompanyPayableBroadcaster;
import com.multiplier.payable.types.*;
import com.multiplier.payroll.schema.Payroll;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.multiplier.core.anomalydetector.InvoiceAnomalyDetectorService.buildIADRequest;
import static com.multiplier.core.payable.service.InvoiceHelper.createJpaInvoicingErrorObject;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toMap;
import static org.springframework.beans.BeanUtils.copyProperties;

@Service
@RequiredArgsConstructor
@Slf4j
public class PayableService {

    public static final int FIRST_INVOICE_TYPE = 1;
    public static final String COMPANY = "Company";
    private final CompanyBindingService companyBindingService;
    private final InvoiceGenerateService invoiceGenerateService;
    private final DepositPayableItemService depositPayableItemService;
    private final InsurancePayableItemService insurancePayableItemService;
    private final ManagementFeePayableItemService managementFeePayableItemService;
    private final PerformanceReviewServiceAdapter performanceReviewServiceAdapter;
    private final CompanyServiceAdapter companyServiceAdapter;
    private final MemberServiceAdapter memberServiceAdapter;
    private final PayrollServiceAdapter payrollServiceAdapter;
    private final ContractServiceAdapter contractServiceAdapter;
    private final CompensationServiceAdapter compensationServiceAdapter;
    private final CountryServiceAdapter countryServiceAdapter;
    private final InvoiceFetcher invoiceFetcher;
    private final CompanyPayableGenerationHistoryService companyPayableGenerationHistoryService;
    private final JpaPricingRepository pricingRepository;
    private final JpaCompanyPayableRepository jpaCompanyPayableRepository;
    private final JpaInvoiceRepository jpaInvoiceRepository;
    private final InvoiceGenerationFactory invoiceGenerationFactory;
    private final CurrentUser currentUser;
    private final PricingRuleEngine pricingRuleEngine;
    private PayableMapper payableMapper;
    private SpecificationBuilder<JpaCompanyPayable, CompanyPayableFilters> specificationBuilder;
    private final CompanyPayablePageRequestBuilder companyPayablePageRequestBuilder;

    private final FirstInvoiceService firstInvoiceService;
    private final FirstInvoiceEligibleContractsProviderFactory firstInvoiceEligibleContractsProviderFactory;
    private final InvoiceAnomalyDetectorService iadService;
    private final MemberPayableExternalInvoiceService memberPayableExternalInvoiceService;
    private final NegativeInvoiceCreditNoteService negativeInvoiceCreditNoteService;
    private final CreditNoteFetcherForSourceReport creditNoteFetcherForSourceReport;
    private final InvoiceSourceReportCompositionService invoiceSourceReportCompositionService;
    private final InvoiceService invoiceService;
    private final InvoiceSourceReportService invoiceSourceReportService;
    private final CompanyPayableReportMapper companyPayableReportMapper;
    private final PayableReportKeyMapper payableReportKeyMapper;
    private final IsrReportDataExtractorInputMapper isrReportDataExtractorInputMapper;
    private final CompanyMapper payableCompanyMapper;
    private final TaxCodeService taxCodeService;
    private final CurrencyExchangeService currencyExchangeService;
    private final JpaPayableItemMapper jpaPayableItemMapper;
    private final ProcessingFeeService processingFeeService;
    private final PricingServiceAdapter pricingServiceAdapter;
    private final CompanyPayableDtoMapper companyPayableDtoMapper;
    private final SecondInvoiceLineItemService secondInvoiceLineItemService;
    private final LineItemDtoFromProcessedLineItemMapper lineItemDtoFromProcessedLineItemMapper;
    private final JpaPayableItemFromProcessedLineItemMapper jpaPayableItemFromProcessedLineItemMapper;
    private final PayableItemDtoMapper payableItemDtoMapper;
    private final LineItemDtoFromPayableItemDtoMapper lineItemDtoFromPayableItemDtoMapper;
    private final ManagementFeeModifier managementFeeModifier;
    private final FinancialTransactionStartDatePopulator financialTransactionStartDatePopulator;
    private final FinancialTransactionEndDatePopulator financialTransactionEndDatePopulator;
    private final ForcedManagementFeeService forcedManagementFeeService;
    private final CyclePopulator cyclePopulator;
    private PayableCompensationCalculator payableCompensationCalculator;
    private final CompanyPayableBroadcaster companyPayableBroadcaster;

    @Autowired
    public void setSpecificationBuilder(@Lazy SpecificationBuilder<JpaCompanyPayable, CompanyPayableFilters> specificationBuilder) {
        this.specificationBuilder = specificationBuilder;
    }

    @Autowired
    public void setPayableMapper(@Lazy PayableMapper payableMapper) {
        this.payableMapper = payableMapper;
    }

    @Autowired
    public void setPayableCompensationCalculator(@Lazy PayableCompensationCalculator payableCompensationCalculator) {
        this.payableCompensationCalculator = payableCompensationCalculator;
    }

    /**
     * Return the given company's payable list.
     *
     * @param companyId         company id.
     * @param payableMonthInput payable month.
     * @return the company payable list.
     */
    public List<CompanyPayable> getCompanyPayables(Long companyId, PayableMonthInput payableMonthInput, Long payableId) {
        CompanyPayableFilters filters = CompanyPayableFilters.newBuilder()
                .companyId(companyId)
                .monthYear(payableMonthInput)
                .build();
        if (payableId != null) {
            filters.setPayableIds(List.of(payableId));
        }
        return getCompanyPayables(filters);
    }

    public List<CompanyPayable> getCompanyPayables(CompanyPayableFilters filters) {
        if (currentUser.getContext() != null
                && !"operations".equalsIgnoreCase(currentUser.getContext().getExperience())
        ) {
            // Return only the AUTHORIZED or PAID company payables for company experience otherwise return all statuses.
            filters.setStatuses(List.of(PayableStatus.PAID, PayableStatus.AUTHORIZED));
        }
        Specification<JpaCompanyPayable> specification = specificationBuilder.build(filters);
        List<JpaCompanyPayable> companyPayableList = jpaCompanyPayableRepository.findAll(specification);
        return payableMapper.map(companyPayableList);
    }

    public Map<Long, List<CompanyPayable>> getCompanyPayablesForCompanyPayableFilterGroupByCompanyId(Set<Long> companyIds,
                                                                                                     CompanyPayableFilters companyPayableFilters) {
        log.info("Start #getCompanyPayablesForCompanyPayableFilterGroupByCompanyId");
        companyPayableFilters.setCompanyIds(new ArrayList<>(companyIds));
        var companyPayables = getCompanyPayables(companyPayableFilters);
        var updatedCompanyPayables = updateCompanyPayablesWithAvailableReports(companyPayables);

        log.info("End #getCompanyPayablesForCompanyPayableFilterGroupByCompanyId");
        return updatedCompanyPayables.stream()
                .collect(Collectors.groupingBy(companyPayable -> companyPayable.getCompany().getId()));
    }

    @Transactional
    public CompanyPayablesResult getCompanyPayablesWithPagination(CompanyPayableFilters inputFilters, final PageRequest pageRequest) {
        log.info("CompanyService.getCompanyPayablesWithPagination: inputFilters = {}, pageRequest = {}", inputFilters, pageRequest);

        Specification<JpaCompanyPayable> spec = specificationBuilder.build(inputFilters);

        Page<JpaCompanyPayable> jpaCompaniesPaged = jpaCompanyPayableRepository.findAll(
                spec,
                companyPayablePageRequestBuilder.build(pageRequest)
        );
        List<CompanyPayable> companyPayables = payableMapper.map(jpaCompaniesPaged.getContent());
        var updatedCompanyPayables = updateCompanyPayablesWithAvailableReports(companyPayables);

        return CompanyPayablesResult.newBuilder()
                .data(updatedCompanyPayables)
                .pageResult(buildPageResult(jpaCompaniesPaged))
                .build();
    }

    private PageResult buildPageResult(Page<JpaCompanyPayable> jpaCompaniesPaged) {
        return PageResult.newBuilder()
                .count((int) jpaCompaniesPaged.getTotalElements())
                .pageNumber(jpaCompaniesPaged.getNumber())
                .pageSize(jpaCompaniesPaged.getSize())
                .build();
    }

    public List<CompanyPayable> getCompanyPayablesByIds(Set<Long> companyPayableIds) {
        log.info("Fetching company payables for ids {}", companyPayableIds);
        return payableMapper.map(jpaCompanyPayableRepository.findAllById(companyPayableIds));
    }

    /**
     * Return the company payable line item list.
     *
     * @param companyPayable company payable that need to get the line items.
     * @return the list of payable items.
     */
    public List<PayableItem> getCompanyPayableItems(CompanyPayable companyPayable) {
        List<JpaPayableItem> jpaPayableItems = jpaCompanyPayableRepository
                .findByCompanyId(companyPayable.getId())
                .stream()
                .map(JpaCompanyPayable::getItems)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        return payableMapper.mapToPayableItems(jpaPayableItems);
    }

    /**
     * Return the company payable line item list.
     *
     * @param companyPayable company payable that need to get the line items.
     * @return the list of payable items.
     */
    public List<PayableItem> getCompanyPayableItemsWithMemberPayAndContract(CompanyPayable companyPayable) {
        return companyPayable.getItems()
                .stream()
                .map(item -> getCompanyPayableItemWithMemberPayAndContract(companyPayable, item))
                .collect(Collectors.toList());
    }

    /**
     * This is a method used to update invoice and payment bundle data on NS missing event
     */
    @Transactional
    public void updateInvoiceAndBundleOnExternalInvoicePaid(String externalInvoiceId) {
        log.info("Updating invoice id {} to paid process started", externalInvoiceId);
        val jpaInvoice = jpaInvoiceRepository.findByExternalId(externalInvoiceId).orElse(null);
        if (jpaInvoice == null) {
            log.error("[ManualInvoiceDataUpdate] No invoice found for external invoice external id : {}", externalInvoiceId);
            return;
        }
        if (jpaInvoice.getCompanyPayable() == null) {
            log.error("[ManualInvoiceDataUpdate] No company payable found for the invoice external id : {}", externalInvoiceId);
            return;
        }
        val jpaCompanyPayable = jpaInvoice.getCompanyPayable();

        updateJpaInvoiceToPaid(jpaInvoice);

        if (jpaCompanyPayable.getStatus() == PayableStatus.AUTHORIZED) {
            // this means we did not receive any payment event for this invoice,
            // therefore we need to update the bundle and related data as well
            var invoiceDto = invoiceService.get(jpaInvoice.getExternalId());
            memberPayableExternalInvoiceService.onInvoiceUpdatedToPaid(invoiceDto, jpaCompanyPayable.getId());
        }

        jpaCompanyPayable.setStatus(PayableStatus.PAID);
        jpaCompanyPayableRepository.save(jpaCompanyPayable);
        jpaInvoiceRepository.save(jpaInvoice);

        companyPayableBroadcaster.publishPayableUpdateEvent(jpaCompanyPayable.getId(), EventType.STATUS_UPDATED);
    }

    private void updateJpaInvoiceToPaid(JpaInvoice jpaInvoice) {
        jpaInvoice.setStatus(InvoiceStatus.PAID);
        jpaInvoice.setAmountPaid(jpaInvoice.getAmountDue());
        jpaInvoice.setAmountDue(BigDecimal.ZERO);
        jpaInvoice.setFullyPaidOnDate(LocalDateTime.now());
    }

    @Transactional
    public CompanyPayableWithErrors generateCompanyPayableForGrossSalaryForCompanies(CompanyPayableInput companyPayableInput) {
        List<ErrorHolder> errorHolders = new ArrayList<>();
        List<CompanyPayable> companyPayables = new ArrayList<>();

        List<JpaInvoicingError> jpaInvoicingErrors = new ArrayList<>();
        List<JpaCompanyPayable> jpaCompanyPayables = new ArrayList<>();

        // validate the input
        if (!validateCompanyPayableInput(companyPayableInput, errorHolders)) {
            log.error("Validation on the input for generation company payables failed. Input : " + companyPayableInput);
            return CompanyPayableWithErrors.newBuilder()
                    .companyPayables(null)
                    .errorResponses(convertErrorsToTaskResponse(errorHolders))
                    .build();
        }

        MonthYearInput monthYearInput = convertInvoiceGenerationDate(companyPayableInput);

        // call for each company and store the required information for creating the invoice.
        Map<JpaCompanyPayable, InvoiceGenerateService.CreateInvoiceParameterHolder> holder = new HashMap<>();

        for (Long companyId : companyPayableInput.getCompanyIds()) {
            // generate the line items and get the payable object
            var jpaCompanyPayableCreateInvoiceParameterHolderPair = generateCompanyPayableForGrossSalaryPerCompany(
                    companyId, monthYearInput, errorHolders, jpaInvoicingErrors, companyPayableInput);
            if (jpaCompanyPayableCreateInvoiceParameterHolderPair.getKey() == null
                    || jpaCompanyPayableCreateInvoiceParameterHolderPair.getValue() == null
            ) {
                log.error("CompanyPayable creation failed for company ID " + companyId);
                continue;
            }
            holder.put(
                    jpaCompanyPayableCreateInvoiceParameterHolderPair.getKey(),
                    jpaCompanyPayableCreateInvoiceParameterHolderPair.getValue());
        }

        var invoiceType = InvoiceType.fromInvoiceNumber(companyPayableInput.getInvoiceType());

        // Creates Invoices in NetSuite
        log.info("Creating invoices in External System");
        var identifierInvoiceDataHolderMap = invoiceGenerateService.createExternalInvoices(new ArrayList<>(holder.values()));

        for (Map.Entry<String, InvoiceGenerationAdapter.InvoiceResponseDataHolder> entry : identifierInvoiceDataHolderMap.entrySet()) {
            InvoiceGenerationAdapter.InvoiceResponseDataHolder dataHolder = entry.getValue();
            if (dataHolder.getErrorMessage() != null && !dataHolder.getErrorMessage().isEmpty()) {
                var customerId = dataHolder.getCustomerId();
                Optional<JpaCompanyBinding> jpaCompanyBinding = companyBindingService.getBindingFromCustomerId(customerId);

                if (jpaCompanyBinding.isEmpty()) {
                    log.error("Company binding not found after the invoice creation");
                    continue;
                }

                var error = createJpaInvoicingErrorObject(
                        jpaCompanyBinding.get().getCompanyId(),
                        null,
                        InvoiceGenerationErrorCode.MPE_NETSUITE_ERROR,
                        monthYearInput.getYear(),
                        monthYearInput.getMonth(),
                        dataHolder.getErrorMessage().toString()
                );

                jpaInvoicingErrors.add(error);
                errorHolders.add(new ErrorHolder(entry.getKey(), "", dataHolder.getErrorMessage().toString()));
            }
        }

        var iadRequests = new ArrayList<InvoiceAnomalyDetectorRequest>();
        List<ISRFileMetaData> ISRFileMetaDataList = new ArrayList<>();
        var isrDataHolderList = new ArrayList<IsrDataHolder>();

        log.info("Creating invoices in our system");
        for (Map.Entry<JpaCompanyPayable, InvoiceGenerateService.CreateInvoiceParameterHolder> dataHolder : holder.entrySet()) {
            InvoiceGenerateService.CreateInvoiceParameterHolder createInvoiceDataHolder = dataHolder.getValue();
            var jpaCompanyPayable = dataHolder.getKey();
            var updatedPayableInput = dataHolder.getValue().getCompanyPayableInput();
            var companyId = jpaCompanyPayable.getCompanyId();
            var identifier = getAdapter().getUniqueIdentifier(createInvoiceDataHolder.getCustomerId(),
                    invoiceGenerateService.generateReference(createInvoiceDataHolder.getCompanyPayableInput()));
            var invoiceResponseDataHolder = identifierInvoiceDataHolderMap.get(identifier);

            if (invoiceResponseDataHolder.getErrorMessage() != null && !invoiceResponseDataHolder.getErrorMessage().isEmpty()) {
                continue;
            }

            /*
             * Reassigning a local variable is not a good practice
             * because it makes this flow bigger and hard to unit-test.
             * TODO Refactor to make invoice and credit note to go in two different directions.
             */
            JpaCompanyPayable savedCompanyPayable;

            if (invoiceResponseDataHolder.isCreditNoteNeeded()) {
                //create credit notes
                var creditNoteRequest = CreditNoteRequestBuilder.builder()
                        .invoiceResponseDataHolder(invoiceResponseDataHolder)
                        .isSaveRequired(true)
                        .companyPayableDto(companyPayableDtoMapper.map(jpaCompanyPayable))
                        .build();

                var creditNoteResponse = negativeInvoiceCreditNoteService.create(creditNoteRequest);

                savedCompanyPayable = jpaCompanyPayableRepository.findById(creditNoteResponse.getCompanyPayableId()).get();

                var creditNoteDataHolder = IsrDataHolder.builder()
                        .companyId(companyId)
                        .externalId(creditNoteResponse.getExternalId())
                        .recordType(RecordType.CREDIT_NOTE)
                        .customerId(dataHolder.getValue().getCustomerId())
                        .build();
                isrDataHolderList.add(creditNoteDataHolder);
            } else {
                String externalInvoiceId = invoiceResponseDataHolder.getExternalInvoiceId();
                log.info("Create invoice with externalId {}", externalInvoiceId);
                var jpaInvoice = invoiceGenerateService.createGrossOrSalaryInvoiceOnCore(jpaCompanyPayable, updatedPayableInput,
                        invoiceResponseDataHolder.getExternalInvoiceId(), createInvoiceDataHolder.getItems());

                jpaCompanyPayable.setInvoice(jpaInvoice);
                jpaCompanyPayable.setTotalAmount(invoiceResponseDataHolder.getInvoiceTotalAmount());
                jpaCompanyPayable.setStatus(PayableStatus.DRAFT);

                savedCompanyPayable = jpaCompanyPayableRepository.saveAndFlush(jpaCompanyPayable);
                companyPayableBroadcaster.publishPayableUpdateEvent(savedCompanyPayable.getId(), EventType.STATUS_UPDATED);

                var invoiceDataHolder = IsrDataHolder.builder()
                        .companyId(companyId)
                        .externalId(externalInvoiceId)
                        .recordType(RecordType.INVOICE)
                        .customerId(dataHolder.getValue().getCustomerId())
                        .build();
                isrDataHolderList.add(invoiceDataHolder);
            }

            jpaCompanyPayables.add(savedCompanyPayable);
            companyPayables.add(payableMapper.map(savedCompanyPayable));

            iadRequests.add(buildIADRequest(savedCompanyPayable, invoiceResponseDataHolder.getInvoiceDTO()));

            // only generate ISR link and update Invoices for Second Invoice
            if (InvoiceType.SALARY.equals(invoiceType)) {
                ISRFileMetaDataList.add(ISRFileMetaData.builder()
                        .companyId(companyId).month(monthYearInput.getMonth()).year(monthYearInput.getYear())
                        .companyPayableId(savedCompanyPayable.getId())
                        .build());
            }
        }

        // generate and update Invoice on NS with ISR link
        if (!ISRFileMetaDataList.isEmpty()) {
            // fetch and update data with companies' display names here to only get companies with successful invoice
            Set<Long> companyIds = ISRFileMetaDataList.stream().map(ISRFileMetaData::getCompanyId).collect(Collectors.toSet());
            var companyDataMap = CollectionUtils.emptyIfNull(companyServiceAdapter.getCompanies(companyIds)).stream()
                    .collect(toMap(com.multiplier.core.payable.company.Company::getId, company -> CompanyDataForIsr.builder()
                            .companyId(company.getId()).displayName(company.getDisplayName()).build()));
            ISRFileMetaDataList = ISRFileMetaDataList.stream().map(existing -> {
                val companyId = existing.getCompanyId();
                CompanyDataForIsr companyDataForIsr = companyDataMap.get(companyId);
                if (companyDataForIsr == null) {
                    return null;
                }
                return ISRFileMetaData.builder().companyId(companyId).companyDisplayName(companyDataForIsr.getDisplayName())
                        .month(monthYearInput.getMonth()).year(monthYearInput.getYear())
                        .build();
            }).filter(Objects::nonNull).collect(Collectors.toList());
            this.generateAndUpdateInvoiceWithSourceReportLink(ISRFileMetaDataList, isrDataHolderList
                    , jpaInvoicingErrors, errorHolders);
        }

        //check if invoice can be auto submit when auto submit param is true. If it cannot be auto submitted, it will remain on draft.
        autoSubmit(holder, identifierInvoiceDataHolderMap, jpaInvoicingErrors, errorHolders, iadRequests);

        //store generation history
        saveJpaCompanyPayableGenerationHistory(
                jpaCompanyPayables,
                jpaInvoicingErrors,
                companyPayableInput,
                monthYearInput
        );

        return CompanyPayableWithErrors.newBuilder()
                .companyPayables(companyPayables)
                .errorResponses(convertErrorsToTaskResponse(errorHolders))
                .build();
    }

    @Transactional
    // todo rename this method since it is not named properly. What does it do?
    public Pair<JpaCompanyPayable, InvoiceGenerateService.CreateInvoiceParameterHolder> generateCompanyPayableForGrossSalaryPerCompany(
            Long companyId,
            MonthYearInput monthYearInput,
            List<ErrorHolder> errorHolders,
            List<JpaInvoicingError> jpaInvoicingErrors,
            CompanyPayableInput companyPayableInput) {

        if (!validateCompanyBindingAndPricing(companyId, monthYearInput, jpaInvoicingErrors, errorHolders)) {
            return Pair.of(null, null);
        }

        if (companyPayableInput.getInvoiceType() == 2) {
            return generateSecondInvoice(companyId, monthYearInput, errorHolders, jpaInvoicingErrors, companyPayableInput);
        }

        var previousFirstInvoices = firstInvoiceService.getFirstInvoices(companyId, monthYearInput).stream()
                .map(Pair::getRight)
                .toList();

        var eligibleContracts = getEligibleContracts(companyId, monthYearInput, companyPayableInput,
                previousFirstInvoices, jpaInvoicingErrors, errorHolders);

        var contractIdCompensationMap = compensationServiceAdapter.getCurrentCompensationByContractIds(getContractIds(eligibleContracts));
        var memberMap = memberServiceAdapter.getMembers(getMemberIds(eligibleContracts))
                .stream()
                .collect(toMap(Member::getId, Function.identity()));

        if (eligibleContracts.isEmpty()
                || eligibleContracts.size() != contractIdCompensationMap.size()
                || eligibleContracts.size() != memberMap.size()
        ) {
            log.warn("No eligible contracts found for first invoice generation on company: {}", companyId);
            var jpaInvoicingError = createJpaInvoicingErrorObject(
                    companyId,
                    null,
                    InvoiceGenerationErrorCode.MPE_ELIGIBLE_CONTRACTS_NOT_FOUND,
                    monthYearInput.getYear(),
                    monthYearInput.getMonth(),
                    null
            );

            jpaInvoicingErrors.add(jpaInvoicingError);
            errorHolders.add(ErrorHolder.getKnownError(InvoiceError.NON_ELIGIBLE_CONTRACTS, companyId.toString()));
            return Pair.of(null, null);
        }

        log.info("{} contract(s) are eligible for invoice generation on company: {}", eligibleContracts.size(), companyId);
        var jpaCompanyBinding = companyBindingService.getBinding(companyId).get();
        var jpaPricing = pricingRepository.findByCompanyId(companyId);

        val jpaCompanyPayable = JpaCompanyPayable.builder()
                .companyId(companyId)
                .status(PayableStatus.DRAFT)
                .date(companyPayableInput.getInvoiceDate())
                .currency(jpaPricing.getBillingCurrencyCode())
                .month(monthYearInput.getMonth())
                .year(monthYearInput.getYear())
                .type(CompanyPayableType.FIRST_INVOICE)
                .transactionId("generatedByOldEngine-" + System.currentTimeMillis())
                .build();

        //get management fee calculated by the pricing engine

        var managementFees = calculateManagementFeesForGrossInvoice(companyId, eligibleContracts,
                MonthYear.newBuilder()
                        .month(monthYearInput.getMonth())
                        .year(monthYearInput.getYear())
                        .build(),
                jpaInvoicingErrors,
                errorHolders);


        var modifiedManagementFees = managementFeeModifier.modify(managementFees,
                Map.of("companyId", companyId
                        , "year", monthYearInput.getYear(), "month", monthYearInput.getMonth()));
        var managementFeeJpaPayableItems = createManagementFeeItems(modifiedManagementFees, companyId);
        var jpaPayableItems = createJpaPayableItemForGrossSalarySortedByCountry(contractIdCompensationMap
                , eligibleContracts, memberMap, errorHolders, companyId, monthYearInput);

        LinkedHashSet<JpaPayableItem> items = new LinkedHashSet<>(jpaPayableItems);
        items.addAll(managementFeeJpaPayableItems);
        financialTransactionStartDatePopulator.populateStartDate(items, InvoiceType.GROSS, companyPayableInput);
        financialTransactionEndDatePopulator.populateEndDate(items, InvoiceType.GROSS, companyPayableInput);
        cyclePopulator.populate(items, InvoiceType.GROSS);
        jpaCompanyPayable.setItems(items);

        List<LineItemDTO> invoiceLineItemList = invoiceGenerateService.getLineItemsWithSeparatedManagementFeeForFirstInvoice(
                jpaPayableItems,
                managementFeeJpaPayableItems,
                jpaPricing.getBillingCurrencyCode(),
                eligibleContracts,
                memberMap,
                companyId);

        var invoiceReferenceFromInput = invoiceGenerateService.generateReference(companyPayableInput);

        InvoiceGenerateService.CreateInvoiceParameterHolder createInvoiceParameterHolder = InvoiceGenerateService.CreateInvoiceParameterHolder.builder()
                .companyId(companyId)
                .currencyCode(jpaPricing.getBillingCurrencyCode())
                .companyPayableInput(CompanyPayableInput.newBuilder()
                        .companyId(companyPayableInput.getCompanyId())
                        .invoiceDate(companyPayableInput.getInvoiceDate())
                        .invoiceDueDate(companyPayableInput.getInvoiceDueDate() != null
                                ? companyPayableInput.getInvoiceDueDate()
                                : jpaPricing.getPaymentTermInDays() != null
                                ? companyPayableInput.getInvoiceDate().plusDays(jpaPricing.getPaymentTermInDays())
                                : companyPayableInput.getInvoiceDate().plusDays(7L))
                        .invoiceReference(firstInvoiceService.createInvoiceReference(invoiceReferenceFromInput, previousFirstInvoices.size()))
                        .companyIds(companyPayableInput.getCompanyIds())
                        .invoiceType(companyPayableInput.getInvoiceType())
                        .invoiceMonth(companyPayableInput.getInvoiceMonth())
                        .autoSubmit(companyPayableInput.getAutoSubmit())
                        .build())
                .items(invoiceLineItemList)
                .customerId(jpaCompanyBinding.getExternalCustomerId())
                .build();

        return Pair.of(jpaCompanyPayable, createInvoiceParameterHolder);
    }

    @VisibleForTesting
    FirstInvoiceEligibleContractsProvider getFirstInvoiceEligibleContractsProvider(CompanyPayableInput companyPayableInput) {
        return firstInvoiceEligibleContractsProviderFactory.getFirstInvoiceEligibleContractsProvider(companyPayableInput);
    }

    public CompanyPayable getCompanyPayableById(Long companyPayableId) {
        if (companyPayableId == null) {
            log.info("Company payable can not find from null id");
            return null;
        }
        JpaCompanyPayable jpaCompanyPayable = jpaCompanyPayableRepository.findById(companyPayableId).orElseThrow(
                () -> new MplBusinessException(PayableErrorCode.COMPANY_PAYABLE_NOT_FOUND,
                    "The company payable not found for the company payable id = " + companyPayableId));
        return payableMapper.map(jpaCompanyPayable);
    }

    private List<JpaPayableItem> createManagementFeeItems(List<ManagementFee> managementFees, Long companyId) {
        List<JpaPayableItem> jpaPayableItems = new ArrayList<>();
        var countryCode = getCountryCode(companyId);
        var taxCode = taxCodeService.getTaxCode(countryCode, PayableItemType.MEMBER_MANAGEMENT_FEE);

        for (ManagementFee fee : managementFees) {
            var contract = contractServiceAdapter.findContractOfAnyStatusById(fee.getContract().getId());
            Member member = memberServiceAdapter.getMember(contract.getMemberId());
            var memberName = getMemberName(member);

            JpaPayableItem item = JpaPayableItem.builder()
                    .type(PayableItemType.MEMBER_MANAGEMENT_FEE)
                    .billableCost(fee.getDiscountedFee())
                    .totalCost(fee.getOriginalFee())
                    .countryCode(CountryCode.valueOf(contract.getCountry()))
                    .currencyCode(CurrencyCode.USD)
                    .description("Applied Discount: " + fee.getAppliedDiscount())
                    .itemData(Set.of(JpaPayableItemData.builder()
                            .contractId(fee.getContract().getId())
                            .memberName(memberName)
                            .build()))
                    .taxType(taxCode)
                    .countryName(countryServiceAdapter.getCountryNameByCode(Country.GrpcCountryCode.valueOf(contract.getCountry())))
                    .build();

            jpaPayableItems.add(item);
        }
        return jpaPayableItems;
    }

    private List<Long> getMemberIds(List<ContractOuterClass.Contract> contracts) {
        return contracts
                .stream()
                .map(ContractOuterClass.Contract::getMemberId)
                .collect(Collectors.toList());
    }

    private List<Long> getContractIds(List<ContractOuterClass.Contract> contracts) {
        return contracts
                .stream()
                .map(ContractOuterClass.Contract::getId)
                .collect(Collectors.toList());
    }

    private boolean validateCompanyPayableInput(CompanyPayableInput companyPayableInput, List<ErrorHolder> errors) {
        if (companyPayableInput == null) {
            errors.add(ErrorHolder.getKnownError(InvoiceError.NULL_COMPANY_PAYABLE_INPUT));
            return false;
        }

        if (companyPayableInput.getCompanyIds() == null && companyPayableInput.getCompanyIds().isEmpty()) {
            errors.add(ErrorHolder.getKnownError(InvoiceError.NULL_COMPANIES_COMPANY_PAYABLE_INPUT));
            return false;
        }

        if (companyPayableInput.getInvoiceDate() == null) {
            companyPayableInput.setInvoiceDate(LocalDateTime.now());
        }

        if (companyPayableInput.getInvoiceMonth() == null) {
            errors.add(ErrorHolder.getKnownError(InvoiceError.NULL_MONTH_YEAR_COMPANY_PAYABLE_INPUT));
            return false;
        }

        return true;
    }

    private MonthYearInput convertInvoiceGenerationDate(CompanyPayableInput companyPayableInput) {

        var monthAndYear = companyPayableInput.getInvoiceMonth(); //non nullable from the graph

        return MonthYearInput.newBuilder()
                .month(monthAndYear.getMonth())
                .year(monthAndYear.getYear())
                .build();
    }

    private List<TaskResponse> convertErrorsToTaskResponse(List<ErrorHolder> errorHolders) {
        if (errorHolders != null && !errorHolders.isEmpty()) {
            return errorHolders.stream()
                    .map(errorHolder -> TaskResponse.newBuilder()
                            .success(false)
                            .message(errorHolder.createMessage())
                            .build()
                    ).collect(Collectors.toList());
        }

        return List.of();
    }

    // Prepare items for generating a first invoice
    private List<JpaPayableItem> createJpaPayableItemForGrossSalarySortedByCountry(
            Map<Long, CompensationOuterClass.Compensation> contractIdCompensationMap,
            List<ContractOuterClass.Contract> contracts,
            Map<Long, Member> memberIdMemberMap,
            List<ErrorHolder> errorHolders,
            Long companyId,
            MonthYearInput monthYearInput) {

        List<JpaPayableItem> jpaPayableItems = new ArrayList<>();
        var contractIdsWithHourlyBilling = contracts.stream().filter(contract -> contractIdCompensationMap.containsKey(contract.getId()))
                .filter(contract -> {
                    var compensation = contractIdCompensationMap.get(contract.getId());
                    var basePay = compensation.getBasePay();
                    return CompensationOuterClass.RateFrequency.HOURLY.equals(basePay.getFrequency());
                }).map(ContractOuterClass.Contract::getId).collect(Collectors.toCollection(LinkedList::new));
        var previousMonthPayrollMap = new HashMap<Long, CompanyMemberPayWrapper>();
        var previousTwoMonthPayrollMap = new HashMap<Long, CompanyMemberPayWrapper>();
        if (CollectionUtils.isNotEmpty(contractIdsWithHourlyBilling)) {
            previousMonthPayrollMap.putAll(this.getPreviousPayrolls(companyId, monthYearInput,
                    contractIdsWithHourlyBilling, -1));
            var contractIdsWithPreviousMonthPayroll = previousMonthPayrollMap.keySet();
            var contractIdsWithoutPreviousMonthPayroll = contractIdsWithHourlyBilling.stream().filter(id ->
                    !contractIdsWithPreviousMonthPayroll.contains(id)).collect(Collectors.toList());
            previousTwoMonthPayrollMap.putAll(this.getPreviousPayrolls(companyId, monthYearInput,
                    contractIdsWithoutPreviousMonthPayroll, -2));
        }

        com.multiplier.payable.types.CountryCode countryCode = companyServiceAdapter.getCompanyById(companyId).getPrimaryEntity().getAddress().getCountry();
        CountryCode companyEntityLocation = CountryCode.valueOf(countryCode.name());

        contracts.forEach(
                contract -> {
                    try {
                        Long contractId = contract.getId();
                        CompensationOuterClass.Compensation compensation = contractIdCompensationMap.get(contract.getId());
                        Member member = memberIdMemberMap.get(contract.getMemberId());
                        var basePay = compensation.getBasePay();
                        var monthlyPayAndCurrencyCode = calculateBasePayMonthlyAmountAndCurrencyCodeForHourlyBilling(contractId,
                                contractIdsWithHourlyBilling,
                                basePay,
                                monthYearInput,
                                contract,
                                previousMonthPayrollMap,
                                previousTwoMonthPayrollMap);

                        var monthlyBasePayAmount = monthlyPayAndCurrencyCode.getLeft();
                        var currencyCode = monthlyPayAndCurrencyCode.getRight();
                        var taxCode = taxCodeService.getTaxCode(companyEntityLocation, PayableItemType.MEMBER_PAYROLL_COST);

                        jpaPayableItems.add(
                                JpaPayableItem.builder()
                                        .type(PayableItemType.MEMBER_PAYROLL_COST)
                                        .description(createDescriptionForGrossSalary(monthlyBasePayAmount, String.valueOf(currencyCode)))
                                        .billableCost(monthlyBasePayAmount)
                                        .totalCost(monthlyBasePayAmount)
                                        .currencyCode(currencyCode)
                                        .countryCode(CountryCode.valueOf(contract.getCountry()))
                                        .itemData(Set.of(JpaPayableItemData.builder()
                                                .contractId(contract.getId())
                                                .memberName(getMemberName(member))
                                                .build())
                                        )
                                        .taxType(taxCode)
                                        .build()
                        );
                    } catch (Exception e) {

                        log.info("Exception occured for company id " + companyId + " exception : " + e + " stack trace : " + Arrays.toString(e.getStackTrace()));

                        errorHolders.add(new ErrorHolder("Contract/Compensation", String.valueOf(contract.getId()), e.getMessage()));
                    }
                }
        );

        return jpaPayableItems.stream()
                .sorted(Comparator.comparing(JpaPayableItem::getCountryCode)
                        .thenComparingLong(o -> o.getItemData().iterator().next().getContractId()))
                .collect(Collectors.toList());
    }

    public Pair<JpaCompanyPayable, InvoiceGenerateService.CreateInvoiceParameterHolder> generateSecondInvoice(
            Long companyId,
            MonthYearInput monthYearInput,
            List<ErrorHolder> errorHolders,
            List<JpaInvoicingError> jpaInvoicingErrors,
            CompanyPayableInput companyPayableInput
    ) {
        var jpaCompanyBinding = companyBindingService.getBinding(companyId).get();
        var pricing = pricingServiceAdapter.getPricing(companyId);
        var firstInvoices = firstInvoiceService.getFirstInvoices(companyId, monthYearInput).stream()
                .map(Pair::getRight)
                .toList();
        log.info("Total number of 1st invoices to process for 2nd invoice companyId {} number {}", companyId, firstInvoices.size());

        var supplementaryInvoices = invoiceFetcher.getSupplementaryInvoices(companyId, monthYearInput);
        log.info("Total number of supplementary invoices to process for 2nd invoice companyId {} number {}", companyId, supplementaryInvoices.size());

        var payrolls = getEligiblePayrollData(companyId, monthYearInput, jpaInvoicingErrors, errorHolders);

        if (payrolls.isEmpty()) {
            return Pair.of(null, null);
        }

        var jpaCompanyPayable = JpaCompanyPayable.builder()
                .companyId(companyId)
                .status(PayableStatus.DRAFT)
                .date(companyPayableInput.getInvoiceDate())
                .currency(pricing.getBillingCurrencyCode())
                .month(monthYearInput.getMonth())
                .year(monthYearInput.getYear())
                .type(CompanyPayableType.SECOND_INVOICE)
                .transactionId("generatedByOldEngine-" + System.currentTimeMillis())
                .build();
        var jpaPayableItems = new HashSet<JpaPayableItem>();
        var lineItemDtos = new ArrayList<LineItemDTO>();

        try {
            var payrollContractIds = payrolls.stream()
                    .flatMap(payroll -> payroll.getMemberPays().stream())
                    .map(memberPay -> memberPay.getContract().getId())
                    .distinct()
                    .toList();

            var forcedPayrollManagementFees = forcedManagementFeeService.calculate(companyId,
                    payrollContractIds,
                    monthYearInput.getMonth(),
                    monthYearInput.getYear());

            var processedLineItems = secondInvoiceLineItemService.process(firstInvoices,
                    supplementaryInvoices, payrolls, forcedPayrollManagementFees);
            var processedPayableItems = processedLineItems.stream()
                    .map(jpaPayableItemFromProcessedLineItemMapper::map)
                    .collect(Collectors.toSet());
            var processedLineItemDTOs = processedLineItems.stream()
                    .map(lineItemDtoFromProcessedLineItemMapper::map)
                    .toList();

            jpaPayableItems.addAll(processedPayableItems);
            lineItemDtos.addAll(processedLineItemDTOs);

            if (processingFeeService.isProcessingFeeEnabled()) {
                // TODO Replace it with gRPC PricingAdapter in service extraction phase #3
                var billingCurrencyCode = pricing.getBillingCurrencyCode();
                var countryCode = getCountryCode(companyId);
                var request = ProcessingFeePayableItemRequest.builder()
                        .billingCurrencyCode(billingCurrencyCode)
                        .companyId(companyId)
                        .countryCode(countryCode)
                        .build();
                var payableItemDto = processingFeeService.getPayableItem(request);
                var jpaPayableItem = jpaPayableItemMapper.map(payableItemDto);
                jpaPayableItems.add(jpaPayableItem);
                var lineItemDto = lineItemDtoFromPayableItemDtoMapper.map(payableItemDto);
                lineItemDtos.add(lineItemDto);
            }

            financialTransactionStartDatePopulator.populateStartDate(
                    jpaPayableItems,
                    InvoiceType.fromInvoiceNumber(companyPayableInput.getInvoiceType()),
                    companyPayableInput
            );

            financialTransactionEndDatePopulator.populateEndDate(
                    jpaPayableItems,
                    InvoiceType.fromInvoiceNumber(companyPayableInput.getInvoiceType()),
                    companyPayableInput
            );

            cyclePopulator.populate(jpaPayableItems, InvoiceType.SALARY);
            jpaCompanyPayable.setItems(jpaPayableItems);

        } catch (CurrencyConversionException currencyConversionException) {
            log.info("CurrencyConversionException occured for companyId " + companyId);

            var error = createJpaInvoicingErrorObject(
                    companyId,
                    null,
                    InvoiceGenerationErrorCode.MPE_PAYROLL_DATA_NOT_AVAILABLE,
                    monthYearInput.getYear(),
                    monthYearInput.getMonth(),
                    currencyConversionException.getMessage()
            );

            jpaInvoicingErrors.add(error);

            errorHolders.add(new ErrorHolder(COMPANY, companyId.toString(), currencyConversionException.getMessage()));
            return Pair.of(null, null);
        } catch (InvoiceGenerationException e) {
            log.info("Exception while generating second invoice for companyId " + companyId + " stack trace: " + e);

            var error = createJpaInvoicingErrorObject(
                    e.getCompanyId(),
                    e.getContractId(),
                    e.getInvoiceGenerationErrorCode(),
                    monthYearInput.getYear(),
                    monthYearInput.getMonth(),
                    e.getDescription()
            );

            jpaInvoicingErrors.add(error);

            errorHolders.add(new ErrorHolder(COMPANY, companyId.toString(), e.getMessage()));
            return Pair.of(null, null);
        } catch (Exception e) {
            log.error("Unexpected exception occurred while generating second invoice for company id: {}; Error  ", companyId, e);

            var error = createJpaInvoicingErrorObject(
                    companyId,
                    null,
                    InvoiceGenerationErrorCode.MPE_INTERNAL_SERVER_ERROR,
                    monthYearInput.getYear(),
                    monthYearInput.getMonth(),
                    null
            );

            jpaInvoicingErrors.add(error);
            return Pair.of(null, null);
        }

        InvoiceGenerateService.CreateInvoiceParameterHolder createInvoiceParameterHolder = InvoiceGenerateService.CreateInvoiceParameterHolder.builder()
                .companyId(companyId)
                .currencyCode(pricing.getBillingCurrencyCode())
                .companyPayableInput(CompanyPayableInput.newBuilder()
                        .companyId(companyPayableInput.getCompanyId())
                        .invoiceDate(companyPayableInput.getInvoiceDate())
                        .invoiceDueDate(companyPayableInput.getInvoiceDueDate() != null
                                ? companyPayableInput.getInvoiceDueDate()
                                : pricing.getPaymentTermInDays() != null
                                ? companyPayableInput.getInvoiceDate().plusDays(pricing.getPaymentTermInDays())
                                : companyPayableInput.getInvoiceDate().plusDays(7L))
                        .invoiceReference(companyPayableInput.getInvoiceReference())
                        .companyIds(companyPayableInput.getCompanyIds())
                        .invoiceType(companyPayableInput.getInvoiceType())
                        .invoiceMonth(companyPayableInput.getInvoiceMonth())
                        .build())
                .items(lineItemDtos)
                .customerId(jpaCompanyBinding.getExternalCustomerId())
                .build();

        return Pair.of(jpaCompanyPayable, createInvoiceParameterHolder);
    }

    /**
     * TODO Extract this into CompanyService with its own contracts like InvoiceService and CreditNoteService.
     */
    CountryCode getCountryCode(Long companyId) {
        var company = companyServiceAdapter.getCompanyById(companyId);
        if (company.getPrimaryEntity() == null) {
            throw new IllegalArgumentException("Primary entity must not be null");
        }
        if (company.getPrimaryEntity().getAddress() == null) {
            throw new IllegalArgumentException("Address must not be null");
        }
        if (company.getPrimaryEntity().getAddress().getCountry() == null) {
            throw new IllegalArgumentException("Country must not be null");
        }
        return company.getPrimaryEntity().getAddress().getCountry();
    }

    public List<CompanyPayrollWrapper> getEligiblePayrollData(Long companyId, MonthYearInput monthYearInput,
                                                              List<JpaInvoicingError> jpaInvoicingErrors, List<ErrorHolder> errorHolders) {
        try {
            var payrollData = payrollServiceAdapter.getCompaniesPayroll(List.of(companyId), monthYearInput.getMonth(), monthYearInput.getYear());

            payrollData.forEach(pd -> {
                log.info("PayrollData MemberPay count: {}", pd.getMemberPays().size());
                var filteredMemberPays = pd.getMemberPays().stream()
                        .filter(mp -> mp.getContract().getType() == ContractOuterClass.ContractType.EMPLOYEE)
                        .filter(mp -> mp.getPayrollCycle() == null || mp.getPayrollCycle().getCycleType() != Payroll.PayrollCycleType.OFF_CYCLE)
                        .collect(Collectors.toList());

                log.info("PayrollData MemberPay after filter count: {}", filteredMemberPays.size());
                pd.getMemberPays().retainAll(filteredMemberPays);
            });

            return payrollData;
        } catch (Exception e) {
            val payrollErrorMessage = "Payroll data not available for company id " + companyId + " and month year " + monthYearInput;
            log.error(payrollErrorMessage, e);
            var error = createJpaInvoicingErrorObject(
                    companyId,
                    null,
                    InvoiceGenerationErrorCode.MPE_PAYROLL_DATA_NOT_AVAILABLE,
                    monthYearInput.getYear(),
                    monthYearInput.getMonth(),
                    payrollErrorMessage
            );
            jpaInvoicingErrors.add(error);
            errorHolders.add(new ErrorHolder(COMPANY, companyId.toString(), payrollErrorMessage));
            return List.of();
        }
    }

    @NotNull
    private static String getMemberName(Member member) {
        if (StringUtils.isNotBlank(member.getFullLegalName())) {
            return member.getFullLegalName();
        }

        return String.format("%s %s", member.getFirstName(), member.getLastName());
    }

    private Pair<JpaCompanyPayable, JpaInvoice> getSecondInvoice(Long companyId, MonthYearInput monthYearInput) {
        var companyPayableAndInvoiceIdPair = invoiceFetcher.getSecondInvoicePair(companyId, monthYearInput.getMonth(), monthYearInput.getYear());

        if (companyPayableAndInvoiceIdPair == null) {
            log.info("No second invoice found for companyID " + companyId);
            return Pair.of(null, null);
        }

        var jpaCompanyPayable = jpaCompanyPayableRepository.findById(companyPayableAndInvoiceIdPair.getKey())
                .orElseThrow(() -> new RuntimeException("CompanyPayable not found for companyId " + companyId + " for " + monthYearInput));

        var jpaInvoice = jpaInvoiceRepository.findById(companyPayableAndInvoiceIdPair.getRight())
                .orElseThrow(() -> new RuntimeException("JpaInvoice not found for companyId " + companyId + " for " + monthYearInput));

        return Pair.of(jpaCompanyPayable, jpaInvoice);
    }

    private LocalDate mapToLocalDate(final Date date) {
        return LocalDate.of(date.getYear(), date.getMonth(), date.getDay());
    }

    @NotNull
    protected Pair<Double, CurrencyCode> calculateBasePayMonthlyAmountAndCurrencyCodeForHourlyBilling(Long contractId,
                                                                                                      List<Long> contractIdsWithHourlyBilling,
                                                                                                      CompensationOuterClass.CompensationPayComponent basePay,
                                                                                                      MonthYearInput monthYearInput,
                                                                                                      ContractOuterClass.Contract contract,
                                                                                                      Map<Long, CompanyMemberPayWrapper> previousMonthPayrollMap,
                                                                                                      Map<Long, CompanyMemberPayWrapper> previousTwoMonthPayrollMap) {

        if (contractIdsWithHourlyBilling.contains(contractId)) {
            var monthlyBasePayAmountAndCurrencyCode = this.calculateBasePayMonthlyAmountAndCurrencyCodeForHourlyBillingFromPayroll(contractId,
                    previousMonthPayrollMap,
                    previousTwoMonthPayrollMap);

            if (monthlyBasePayAmountAndCurrencyCode != null) {
                return monthlyBasePayAmountAndCurrencyCode;
            }

        }

        CurrencyCode currencyCode = basePay.getCurrency().isEmpty() ? null : CurrencyCode.valueOf(basePay.getCurrency());
        return Pair.of(payableCompensationCalculator.calculateBasePayMonthlyAmount(basePay, contract, monthYearInput), currencyCode);
    }

    //checks for the previous month and the month before that if the contract was present in the payroll
    protected Pair<Double, CurrencyCode> calculateBasePayMonthlyAmountAndCurrencyCodeForHourlyBillingFromPayroll(Long contractId,
                                                                                                                 Map<Long, CompanyMemberPayWrapper> previousMonthPayroll,
                                                                                                                 Map<Long, CompanyMemberPayWrapper> previousTwoMonthPayroll) {
        if (previousMonthPayroll.containsKey(contractId)) {
            return Pair.of(previousMonthPayroll.get(contractId).getAmountTotalCost(), CurrencyCode.valueOf(previousMonthPayroll.get(contractId).getCurrency().name()));
        }
        if (previousTwoMonthPayroll.containsKey(contractId)) {
            return Pair.of(previousTwoMonthPayroll.get(contractId).getAmountTotalCost(), CurrencyCode.valueOf(previousMonthPayroll.get(contractId).getCurrency().name()));
        }
        return null;
    }

    private Map<Long, CompanyMemberPayWrapper> getPreviousPayrolls(Long companyId, MonthYearInput monthYearInput,
                                                                   List<Long> contractIds, int delta) {
        try {
            var contractIdToPayMap = new HashMap<Long, CompanyMemberPayWrapper>();
            // get for previous month
            var dateFromInput = LocalDate.of(monthYearInput.getYear(), monthYearInput.getMonth(), 1);
            var requiredDate = dateFromInput.plusMonths(delta);
            var newMonthYearInput = MonthYearInput.newBuilder().month(requiredDate.getMonthValue())
                    .year(requiredDate.getYear()).build();
            var payrolls = payrollServiceAdapter.getCompaniesPayroll(List.of(companyId), newMonthYearInput.getYear(), monthYearInput.getMonth());
            payrolls.stream().flatMap(payroll -> payroll.getMemberPays().stream())
                    .filter(memberPay -> contractIds.contains(memberPay.getContract().getId()))
                    .collect(Collectors.toCollection(LinkedList::new))
                    .forEach(data -> contractIdToPayMap.compute(data.getContract().getId(), (contractId, existingData) -> {
                        if (existingData == null || existingData.getId() < data.getId()) {
                            return data;
                        }
                        return existingData;
                    }));
            return contractIdToPayMap;
        } catch (Exception e) {
            log.info("Payroll data not available for company id " + companyId + " and month year " + monthYearInput);
            return Map.of();
        }
    }

    protected String createDescriptionForGrossSalary(Double grossSalary, String currency) {
        return String.format("Gross Salary : %s %.2f", currency, grossSalary);
    }

    public List<CompanyPayable> getCompanyPayablesGeneratedByDryRun(CompanyPayableFilters filters) {
        com.multiplier.core.payable.company.Company company = companyServiceAdapter.getCompanyById(filters.getCompanyId());
        JpaPricing jpaPricing = getCompanyPricing(filters.getCompanyId());
        var finalItemList = generateCompanyPayableItems(
                company,
                MonthYearInput.newBuilder()
                        .month(filters.getMonthYear().getMonth())
                        .year(filters.getMonthYear().getYear()).build());

        JpaCompanyPayable jpaCompanyPayable = JpaCompanyPayable.builder()
                .companyId(company.getId())
                .status(PayableStatus.DRAFT)
                .currency(jpaPricing.getBillingCurrencyCode())
                .month(filters.getMonthYear().getMonth())
                .year(filters.getMonthYear().getYear())
                .totalAmount(calculatePayableItemsTotalAmount(finalItemList))
                .build();

        CompanyPayable companyPayable = payableMapper.map(jpaCompanyPayable);
        List<PayableItem> payableItems = finalItemList
                .stream()
                .map(item -> {
                    item.setCurrencyCode(jpaPricing.getBillingCurrencyCode());
                    return getPayableItemBySourceReportType(item, filters.getQueryMode());
                })
                .flatMap(List::stream)
                .collect(Collectors.toList()); // convert each set to a stream
        companyPayable.setCompany(payableCompanyMapper.map(company));
        companyPayable.setItems(payableItems);
        return List.of(companyPayable);
    }

    // Calculate the total cost/amount of all the payable items.
    private Double calculatePayableItemsTotalAmount(Set<JpaPayableItem> jpaPayableItems) {
        return jpaPayableItems.stream().mapToDouble(JpaPayableItem::getTotalCost).sum();
    }

    // Return jpa payable item list.
    private Set<JpaPayableItem> getJpaPayableItemList(List<CompanyPayrollWrapper> companyPayrollList, List<ContractOuterClass.Contract> contractsWithoutPayrollData) {
        // Create a map to keep company member pays in country vise.
        // TODO: verify items are correct
        EnumMap<Country.GrpcCountryCode, List<CompanyMemberPayWrapper>> memberPayMap = groupMemberPayByCountry(companyPayrollList);
        EnumMap<Country.GrpcCountryCode, List<ContractOuterClass.Contract>> contractMap = groupContractsByCountry(contractsWithoutPayrollData);

        Set<JpaPayableItem> jpaPayableItems = new HashSet<>();
        memberPayMap.forEach(
                (countryCode, companyMemberPays) -> jpaPayableItems.add(getJpaPayableItem(countryCode, companyMemberPays))
        );
        contractMap.forEach(
                (countryCode, jpaContracts) -> jpaPayableItems.add(getJpaPayableItemForMembersWithoutPayrollData(countryCode, jpaContracts))
        );
        return jpaPayableItems;
    }

    // Return jpa payable item.
    private JpaPayableItem getJpaPayableItem(Country.GrpcCountryCode countryCode, List<CompanyMemberPayWrapper> companyMemberPays) {
        Set<JpaPayableItemData> payrollCosts = new HashSet<>();
        double totalCost = 0.0;
        double billableCost = 0.0;
        CurrencyCode currencyCode = null;
        List<String> memberNames = new ArrayList<>();
        for (CompanyMemberPayWrapper companyMemberPay : companyMemberPays) {
            currencyCode = CurrencyCode.valueOf(companyMemberPay.getCurrency().name());
            totalCost = totalCost + companyMemberPay.getAmountTotalCost();
            memberNames.add(invoiceGenerateService.getMemberNameByContractId(companyMemberPay.getContract().getId()));
            payrollCosts.add(JpaPayableItemData.builder()
                    .memberPayId(companyMemberPay.getId())
                    .contractId(companyMemberPay.getContract().getId())
                    .amountTotalCost(companyMemberPay.getAmountTotalCost())
                    .currencyCode(CurrencyCode.valueOf(companyMemberPay.getCurrency().name()))
                    .build()
            );
        }

        return JpaPayableItem.builder()
                .type(PayableItemType.MEMBER_PAYROLL_COST)
                .description(invoiceGenerateService.getLineItemDescription(countryCode, memberNames) + " - " + totalCost + " " + (Objects.isNull(currencyCode) ? "" : currencyCode.name()))
                .totalCost(totalCost)
                .billableCost(billableCost)
                .itemData(payrollCosts)
                .currencyCode(currencyCode)
                .countryCode(CountryCode.valueOf(countryCode.name()))
                .build();
    }

    // Return map containing the company member pays group by each country.
    private EnumMap<Country.GrpcCountryCode, List<CompanyMemberPayWrapper>> groupMemberPayByCountry(List<CompanyPayrollWrapper> companyPayrolls) {
        return companyPayrolls.stream()
                .sorted(Comparator.comparing(CompanyPayrollWrapper::getId))
                .map(CompanyPayrollWrapper::getMemberPays)
                .flatMap(Collection::stream)
                .sorted(Comparator.comparing(CompanyMemberPayWrapper::getId))
                .collect(Collectors.groupingBy(companyMemberPay -> Country.GrpcCountryCode.valueOf(companyMemberPay.getContract().getCountry()),
                        () -> new EnumMap<>(Country.GrpcCountryCode.class),
                        Collectors.mapping(Function.identity(), Collectors.toList())));
    }

    // Group the given contract list by country.
    private EnumMap<Country.GrpcCountryCode, List<ContractOuterClass.Contract>> groupContractsByCountry(List<ContractOuterClass.Contract> contractsWithoutPayrollData) {
        EnumMap<Country.GrpcCountryCode, List<ContractOuterClass.Contract>> map = new EnumMap<>(Country.GrpcCountryCode.class);
        contractsWithoutPayrollData
                .stream()
                .sorted(Comparator.comparing(ContractOuterClass.Contract::getId))
                .forEach(
                        contract -> {
                            // Country code.
                            var countryCode = Country.GrpcCountryCode.valueOf(contract.getCountry());

                            // If country is already in the list, add this member pay to the value list.
                            if (map.containsKey(countryCode)) {
                                List<ContractOuterClass.Contract> existingList = map.get(countryCode);
                                existingList.add(ContractOuterClass.Contract.newBuilder().setId(contract.getId()).setCurrency(contract.getCurrency()).build());
                                map.put(countryCode, existingList);

                            } else {
                                // Else, create new list and add to the map.
                                List<ContractOuterClass.Contract> newList = new ArrayList<>();
                                newList.add(ContractOuterClass.Contract.newBuilder().setId(contract.getId()).setCurrency(contract.getCurrency()).build());
                                map.put(countryCode, newList);
                            }
                        }
                );
        return map;
    }

    // Return jpa payable item.
    private JpaPayableItem getJpaPayableItemForMembersWithoutPayrollData(Country.GrpcCountryCode countryCode, List<ContractOuterClass.Contract> jpaContracts) {
        Set<JpaPayableItemData> payrollCosts = new HashSet<>();
        List<String> memberNames = new ArrayList<>();
        CurrencyCode currencyCode = null;
        for (ContractOuterClass.Contract jpaContract : jpaContracts) {
            memberNames.add(invoiceGenerateService.getMemberNameByContractId(jpaContract.getId()));
            currencyCode = CurrencyCode.valueOf(jpaContract.getCurrency().name());
            payrollCosts.add(JpaPayableItemData.builder()
                    .memberPayId(null)
                    .contractId(jpaContract.getId())
                    .amountTotalCost(0.00)
                    .currencyCode(currencyCode)
                    .build()
            );
        }

        return JpaPayableItem.builder()
                .type(PayableItemType.MEMBER_PAYROLL_COST)
                .description(invoiceGenerateService.getLineItemDescription(countryCode, memberNames) + " - < Payroll values not available >")
                .totalCost(0.00)
                .countryCode(CountryCode.valueOf(countryCode.name()))
                .currencyCode(currencyCode)
                .itemData(payrollCosts)
                .build();
    }

    private Set<JpaPayableItem> generateCompanyPayableItems(com.multiplier.core.payable.company.Company company, MonthYearInput payrollMonth) {
        // Get invoice date month's company payroll.
        List<CompanyPayrollWrapper> companyPayrollList = payrollServiceAdapter.getCompanyPayroll(company.getId(),
                payrollMonth.getMonth(), payrollMonth.getYear(), null, List.of(company.getId()));

        // Get jpa payable item list.
        Set<JpaPayableItem> jpaPayableItems = getJpaPayableItemList(companyPayrollList, List.of());

        // Add deposit payable item list.
        jpaPayableItems.addAll(depositPayableItemService.getJpaPayableItems(company.getId(), payrollMonth));

        // Add insurance payable item list.
        jpaPayableItems.addAll(insurancePayableItemService.getJpaPayableItems(company.getId(), payrollMonth));
        jpaPayableItems.addAll(managementFeePayableItemService.getJpaPayableItems(company.getId(), payrollMonth));

        return jpaPayableItems;
    }

    /**
     * Generate company payable items for given report type
     *
     * @param jpaPayableItem   payable item
     * @param payableQueryMode report type (DETAILED or SUMMERIZED)
     */
    private List<PayableItem> getPayableItemBySourceReportType(JpaPayableItem jpaPayableItem, PayableQueryMode payableQueryMode) {

        List<JpaPayableItem> jpaPayableItems;

        if (PayableQueryMode.DRYRUN_DETAILED.equals(payableQueryMode)) {
            jpaPayableItems = generatePayableItemsForDetailedReport(jpaPayableItem);
        } else {
            jpaPayableItems = List.of(setCurrencyCodeByItemData(jpaPayableItem));
        }
        return payableMapper.mapToPayableItems(jpaPayableItems);

    }

    /**
     * Generate company payable items for given report type
     *
     * @param jpaPayableItem payable item
     */
    List<JpaPayableItem> generatePayableItemsForDetailedReport(JpaPayableItem jpaPayableItem) {
        return jpaPayableItem.getItemData().stream().map(itemData -> {

            var convertedAmount = currencyExchangeService.exchange(
                    BigDecimal.valueOf(itemData.getAmountTotalCost()),
                    itemData.getCurrencyCode(),
                    jpaPayableItem.getCurrencyCode());

            Double billableAmount = convertedAmount == null ? 0 : convertedAmount.doubleValue();

            JpaPayableItem jpaPayableItemGenerated = clone(jpaPayableItem);
            jpaPayableItemGenerated.setDescription(invoiceGenerateService.getMemberNameByContractId(itemData.getContractId()));
            jpaPayableItemGenerated.setCurrencyCode(itemData.getCurrencyCode());
            jpaPayableItemGenerated.setTotalCost(itemData.getAmountTotalCost());
            jpaPayableItemGenerated.setBillableCost(billableAmount);
            jpaPayableItemGenerated.setItemData(Set.of(itemData));
            return jpaPayableItemGenerated;

        }).collect(Collectors.toList());
    }

    /**
     * Method to clone jpaPayableItem for detailed view
     *
     * @param jpaPayableItem payable item
     */
    private JpaPayableItem clone(JpaPayableItem jpaPayableItem) {
        JpaPayableItem jpaPayableItemGenerated = JpaPayableItem.builder().build();
        copyProperties(jpaPayableItem, jpaPayableItemGenerated);
        return jpaPayableItemGenerated;
    }

    /**
     * Method to set currency code to payableItem from payableItemData
     *
     * @param jpaPayableItem payable item
     */
    private JpaPayableItem setCurrencyCodeByItemData(JpaPayableItem jpaPayableItem) {

        Optional<JpaPayableItemData> payableItemData = jpaPayableItem.getItemData().stream().findAny();

        if (payableItemData.isPresent() && payableItemData.get().getCurrencyCode() != null) {
            jpaPayableItem.setCurrencyCode(payableItemData.get().getCurrencyCode());
        }
        return jpaPayableItem;
    }

    /**
     * Method to set Contract, Memberpay to PayableItems
     */
    private PayableItem getCompanyPayableItemWithMemberPayAndContract(CompanyPayable companyPayable, PayableItem payableItem) {

        if (payableItem instanceof MemberPayrollCost) {
            MemberPayrollCost memberPayrollCost = (MemberPayrollCost) payableItem;

            getMemberPayrollCostWithBillingAmount(memberPayrollCost, companyPayable.getCurrency());
            getMemberPayrollCostWithContract(memberPayrollCost);
        } else {
            GenericPayableCost genericPayableCost = (GenericPayableCost) payableItem;
            getGenericPayableCostWithBillingAmount(genericPayableCost, companyPayable.getCurrency());
            getGenericPayableCostWithContract(genericPayableCost);
        }
        return payableItem;
    }

    /**
     * Method to set Contract to MemberPayrollCost item
     */
    private MemberPayrollCost getMemberPayrollCostWithContract(MemberPayrollCost memberPayrollCost) { // TODO Return value never used
        Long contractId = memberPayrollCost.getContract() != null ? memberPayrollCost.getContract().getId() : null;
        if (contractId != null) {
            memberPayrollCost.setContract(contractServiceAdapter.getContractOfAnyStatus(contractId));
        }
        return memberPayrollCost;
    }

    /**
     * Method to set Contract to GenericPayableCost item
     */
    private GenericPayableCost getGenericPayableCostWithContract(GenericPayableCost genericPayableCost) { // TODO return value never used
        Long contractId = genericPayableCost.getContract() != null ? genericPayableCost.getContract().getId() : null;
        if (contractId != null) {
            genericPayableCost.setContract(contractServiceAdapter.getContractOfAnyStatus(contractId));
        }
        return genericPayableCost;
    }

    /**
     * Method to set billingAmount to MemberPayrollCost item
     */
    private MemberPayrollCost getMemberPayrollCostWithBillingAmount(MemberPayrollCost memberPayrollCost, CurrencyCode billingCurrency) { // TODO
        // Return
        // value never used
        if (memberPayrollCost.getBillingCost() == null || memberPayrollCost.getBillingCost().equals(0.0)) {
            Double billingCost = insurancePayableItemService.convertContractCurrencyToBillingCurrency(
                    memberPayrollCost.getTotalCost(),
                    memberPayrollCost.getCurrency(),
                    billingCurrency);

            memberPayrollCost.setBillingCost(billingCost);
        }
        return memberPayrollCost;
    }

    /**
     * Method to set billingAmount to GenericPayableCost item
     */
    private GenericPayableCost getGenericPayableCostWithBillingAmount(GenericPayableCost genericPayableCost, CurrencyCode billingCurrency) { //
        // TODO return value never used
        if (genericPayableCost.getBillingCost() == null || genericPayableCost.getBillingCost().equals(0.0)) {
            Double billingCost = insurancePayableItemService.convertContractCurrencyToBillingCurrency(
                    genericPayableCost.getTotalCost(),
                    genericPayableCost.getCurrency(),
                    billingCurrency);

            genericPayableCost.setBillingCost(billingCost);
        }
        return genericPayableCost;
    }

    private JpaPricing getCompanyPricing(Long companyId) {
        JpaPricing jpaPricing = pricingRepository.findByCompanyId(companyId);

        if (Objects.isNull(jpaPricing)) {
            throw new MplBusinessException(PayableErrorCode.COMPANY_PRICING_NOT_FOUND,
                "Company pricing information is not available. companyId = " + companyId);
        }

        if (Objects.isNull(jpaPricing.getBillingCurrencyCode())) {
            throw new MplBusinessException(PayableErrorCode.COMPANY_BILLING_CURRENCY_NOT_FOUND,
                "Company billing currency is not available. companyId = " + companyId);
        }
        return jpaPricing;
    }

    @Transactional
    public List<SourceReportData> getDataForInvoiceSourceReportGen(CompanyPayableInvoiceSourceReportExportInput input) {
        var extractorInput = isrReportDataExtractorInputMapper.map(input);
        return getDataForInvoiceSourceReportGen(extractorInput);
    }

    @Transactional
    public List<SourceReportData> getDataForInvoiceSourceReportGen(IsrReportDataExtractorInput input) {
        List<JpaInvoicingError> invoicingErrors = new ArrayList<>();
        var inputCompanyIds = input.getCompanyIds();
        var month = input.getMonth();
        var year = input.getYear();
        var type = input.getType();
        input.validate();

        Set<Long> companyIds = new HashSet<>(inputCompanyIds);
        String companyIdsString = companyIds.stream().map(Objects::toString)
                .collect(Collectors.joining(","));
        var companies = CollectionUtils.emptyIfNull(companyServiceAdapter.getCompanies(companyIds));
        if (companies.isEmpty()) {
            throw new IsrGenerationException(null, month, year,
                    "No company is available from the input ID list. companyIds=" + companyIdsString);
        }

        MonthYearInput payrollMonthYear = MonthYearInput.newBuilder()
                .year(year)
                .month(month)
                .build();

        List<Pair<JpaCompanyPayable, JpaInvoice>> firstInvoicePairs = fetchFirstInvoicesForCompanySourceReport(payrollMonthYear,
                companies);
        List<Pair<JpaCompanyPayable, JpaInvoice>> secondInvoicePairs = new ArrayList<>();
        if (type == CompanyPayableReportType.SALARY_INVOICE_SOURCE_REPORT) {
            secondInvoicePairs.addAll(fetchSecondInvoicesForCompanySourceReport(payrollMonthYear, companies));
        }
        List<Pair<JpaCompanyPayable, JpaInvoice>> fetchedInvoicePairs = new ArrayList<>();
        fetchedInvoicePairs.addAll(firstInvoicePairs);
        fetchedInvoicePairs.addAll(secondInvoicePairs);
        Map<Long, ContractInvoiceOrCreditNoteData> contractInvoiceData = extractInvoiceDataForSourceReport(fetchedInvoicePairs,
                invoicingErrors);

        Comparator<CompanyMemberPayWrapper> sortByContract = Comparator.comparing(mp -> ofNullable(mp.getContract())
                .map(ContractOuterClass.Contract::getId).orElse(Long.MAX_VALUE));
        Comparator<CompanyMemberPayWrapper> sortByPayCycle = Comparator.comparing(CompanyMemberPayWrapper::getCycle);
        List<SourceReportData> reportDatalist = new ArrayList<>();

        // first invoice no payroll data needed.
        if (type == CompanyPayableReportType.GROSS_INVOICE_SOURCE_REPORT) {
            if (contractInvoiceData.isEmpty()) {
                return reportDatalist;
            }
            var contracts = contractServiceAdapter.getContractsByIdsAnyStatus(new ArrayList<>(contractInvoiceData.keySet()));
            for (ContractOuterClass.Contract contract : contracts) {
                var invoiceSourceReportData = SourceReportData.builder().contract(contract).contractData(contractInvoiceData.get(contract.getId())).build();
                reportDatalist.add(invoiceSourceReportData);
            }
            return reportDatalist;
        }
        // second invoice works
        log.info("Retrieving data to generate ISR for second invoice. companyIds={}", companyIdsString);
        List<CompanyPayrollWrapper> companyPayrolls = payrollServiceAdapter
                .getCompaniesPayroll(inputCompanyIds, month, year);
        Collection<CreditNoteDto> fetchedCreditNoteDtos = creditNoteFetcherForSourceReport
                .fetchCreditNoteForCompanySourceReport(input, companies);

        // exclude credit note of a company if second invoice is present
        fetchedCreditNoteDtos = excludeCreditNoteForCompanyHavingSecondInvoice(secondInvoicePairs, fetchedCreditNoteDtos);

        Map<Long, ContractInvoiceOrCreditNoteData> contractInvoiceAndCreditNoteData = CreditNoteExtractorForSourceReport
                .extractCreditNotesDataForSourceReport(fetchedCreditNoteDtos,
                        CreditNoteExtractorForSourceReport.CREDIT_NOTE_PAYABLE_ITEM_TYPES_FOR_ISR, new ArrayList<>(), contractInvoiceData);
        Map<Long, CompensationOuterClass.Compensation> compensations = new HashMap<>(compensationServiceAdapter
                .getActiveCompensationsByContractIds(new ArrayList<>(contractInvoiceAndCreditNoteData.keySet())));

        companyPayrolls.stream()
                .map(CompanyPayrollWrapper::getMemberPays)
                .filter(memberPays -> !CollectionUtils.isEmpty(memberPays))
                .forEach(memberPays -> {
                    // Compute compensations
                    var contractIds = memberPays.stream()
                            .filter(memberPay -> memberPay.getContract() != null)
                            .map(memberPay -> memberPay.getContract().getId())
                            .collect(Collectors.toSet());
                    if (!contractInvoiceAndCreditNoteData.keySet().containsAll(contractIds)) {
                        var payrollContractCompensations = compensationServiceAdapter.getActiveCompensationsByContractIds(contractIds.stream().toList());
                        compensations.putAll(payrollContractCompensations);
                    }

                    // Compute & append ISR data
                    var contractsMap = contractServiceAdapter.getContractsByIdsAnyStatus(contractIds.stream().toList()).stream()
                            .filter(contract -> contract.getType() == ContractOuterClass.ContractType.EMPLOYEE)
                            .collect(Collectors.toMap(ContractOuterClass.Contract::getId, Function.identity()));
                    memberPays
                            .stream()
                            .filter(mp -> mp.getContract() != null && contractsMap.containsKey(mp.getContract().getId())) // filtering contract (to be EMPLOYEE)
                            .filter(mp -> mp.getPayrollCycle() == null || mp.getPayrollCycle().getCycleType() != Payroll.PayrollCycleType.OFF_CYCLE) // filtering payroll cycle
                            .sorted(sortByContract.thenComparing(sortByPayCycle))
                            .forEach(memberPay -> {
                                Long contractId = memberPay.getContract().getId();
                                log.info("Generating ISR for type 2 with contract data exists {} for contract ID {}",
                                        contractInvoiceAndCreditNoteData.containsKey(contractId), contractId
                                );
                                var invoiceSourceReportData = SourceReportData.builder()
                                        .companyMemberPayWrapper(memberPay)
                                        .contract(contractsMap.get(contractId))
                                        .payFrequency(compensations.containsKey(contractId) ? getPayFrequency(compensations.get(contractId).getBasePay().getPayFrequency()) : "")
                                        .contractData(contractInvoiceAndCreditNoteData.get(contractId))
                                        .build();

                                reportDatalist.add(invoiceSourceReportData);
                                contractInvoiceAndCreditNoteData.remove(contractId);
                            });
                });


        if (!contractInvoiceAndCreditNoteData.isEmpty()) {
            List<ContractOuterClass.Contract> contractsWithNoPayroll = contractServiceAdapter.getContractsByIdsAnyStatus(new ArrayList<>(contractInvoiceAndCreditNoteData.keySet()));

            for (ContractOuterClass.Contract contract : contractsWithNoPayroll) {
                Long contractId = contract.getId();
                log.info("Generating ISR for type 2 with contract data exists {} for contract ID {}",
                        contractInvoiceAndCreditNoteData.containsKey(contractId), contractId
                );
                reportDatalist.add(SourceReportData.builder()
                        .contract(contract)
                        .payFrequency(compensations.containsKey(contract.getId()) ? getPayFrequency(compensations.get(contract.getId()).getBasePay().getPayFrequency()) : "")
                        .contractData(contractInvoiceAndCreditNoteData.get(contract.getId()))
                        .build());

            }
        }

        return reportDatalist;
    }

    private String getPayFrequency(final CompensationOuterClass.PayFrequency payFrequency) {
        return payFrequency == null ? "" : payFrequency.name();
    }

    private Collection<CreditNoteDto> excludeCreditNoteForCompanyHavingSecondInvoice(List<Pair<JpaCompanyPayable, JpaInvoice>> secondInvoicePairs, Collection<CreditNoteDto> fetchedCreditNoteDtos) {
        if (secondInvoicePairs == null || secondInvoicePairs.stream().allMatch(p -> p.getLeft() == null && p.getRight() == null)) {
            return fetchedCreditNoteDtos;
        }
        List<CreditNoteDto> creditNoteDtosWithoutSecondInvoice = new ArrayList<>();
        for (CreditNoteDto creditNoteDto : fetchedCreditNoteDtos) {
            var companyId = creditNoteDto.getCompanyId();
            if (companyId == null) {
                log.warn("Company ID is NULL for credit note with externalId {}", creditNoteDto.getExternalId());
                continue;
            }
            boolean existSecondInvoice = false;
            for (Pair<JpaCompanyPayable, JpaInvoice> pair : secondInvoicePairs) {
                if (pair.getLeft() == null) {
                    continue;
                }
                if (companyId.equals(pair.getLeft().getCompanyId())) {
                    existSecondInvoice = true;
                    break;
                }
            }
            if (!existSecondInvoice) {
                creditNoteDtosWithoutSecondInvoice.add(creditNoteDto);
            }
        }
        return creditNoteDtosWithoutSecondInvoice;
    }

    private List<Pair<JpaCompanyPayable, JpaInvoice>> fetchFirstInvoicesForCompanySourceReport(MonthYearInput payrollMonthYear,
                                                                                               Collection<com.multiplier.core.payable.company.Company> companies) {
        List<Pair<JpaCompanyPayable, JpaInvoice>> fetchedInvoices = new ArrayList<>();
        assert companies != null;
        for (var company : companies) {
            fetchedInvoices.addAll(firstInvoiceService.getFirstInvoices(company.getId(), payrollMonthYear)); // get first invoice data
        }
        return fetchedInvoices;
    }

    private List<Pair<JpaCompanyPayable, JpaInvoice>> fetchSecondInvoicesForCompanySourceReport(MonthYearInput payrollMonthYear,
                                                                                                Collection<com.multiplier.core.payable.company.Company> companies) {
        List<Pair<JpaCompanyPayable, JpaInvoice>> fetchedInvoices = new ArrayList<>();
        assert companies != null;
        for (var company : companies) {
            fetchedInvoices.add(getSecondInvoice(company.getId(), payrollMonthYear));
        }
        return fetchedInvoices;
    }

    private Map<Long, ContractInvoiceOrCreditNoteData> extractInvoiceDataForSourceReport(List<Pair<JpaCompanyPayable, JpaInvoice>> fetchedInvoicePairs,
                                                                                         List<JpaInvoicingError> invoicingErrors) {

        return InvoiceHelper
                .extractInfoFromPreviousInvoices(fetchedInvoicePairs, Set.of(PayableItemType.MEMBER_MANAGEMENT_FEE, PayableItemType.MEMBER_PAYROLL_COST),
                        invoicingErrors);
    }

    private List<JpaCompanyPayableGenerationHistory> saveJpaCompanyPayableGenerationHistory(List<JpaCompanyPayable> companyPayables,
                                                                                            List<JpaInvoicingError> invoicingErrors,
                                                                                            CompanyPayableInput companyPayableInput,
                                                                                            MonthYearInput monthYearInput) {

        var jpaCompanyPayableHistories = new ArrayList<JpaCompanyPayableGenerationHistory>();
        var companyIdErrorMap = invoicingErrors
                .stream()
                .collect(Collectors.groupingBy((JpaInvoicingError::getCompanyId)));
        var successFullInvoiceCreatedCompanyIds = new HashSet<Long>();

        //scenarios for which the invoices are generated.
        for (JpaCompanyPayable companyPayable : companyPayables) {
            var companyId = companyPayable.getCompanyId();

            successFullInvoiceCreatedCompanyIds.add(companyId);

            var jpaCompanyPayableHistory = JpaCompanyPayableGenerationHistory
                    .builder()
                    .companyId(companyId)
                    .companyPayableId(companyPayable.getId())
                    .jpaInvoicingErrors(companyIdErrorMap.get(companyId))
                    .year(monthYearInput.getYear())
                    .month(monthYearInput.getMonth())
                    .invoiceReference(companyPayableInput.getInvoiceReference())
                    .request(companyPayableInput)
                    .build();

            ofNullable(companyIdErrorMap.get(companyId))
                    .orElse(List.of())
                    .forEach(jpaInvoicingError -> jpaInvoicingError.setCompanyPayableGenerationHistory(jpaCompanyPayableHistory));

            jpaCompanyPayableHistories.add(jpaCompanyPayableHistory);
        }

        //find companyIds for which invoices were not created
        var unsuccessfulCompanyIds = companyPayableInput.getCompanyIds()
                .stream()
                .filter(companyId -> !successFullInvoiceCreatedCompanyIds.contains(companyId))
                .collect(Collectors.toList());

        for (Long companyId : unsuccessfulCompanyIds) {
            var jpaCompanyPayableHistory = JpaCompanyPayableGenerationHistory
                    .builder()
                    .companyId(companyId)
                    .companyPayableId(null)
                    .jpaInvoicingErrors(companyIdErrorMap.get(companyId))
                    .year(monthYearInput.getYear())
                    .month(monthYearInput.getMonth())
                    .invoiceReference(companyPayableInput.getInvoiceReference())
                    .request(companyPayableInput)
                    .build();

            ofNullable(companyIdErrorMap.get(companyId))
                    .orElse(List.of())
                    .forEach(jpaInvoicingError -> jpaInvoicingError.setCompanyPayableGenerationHistory(jpaCompanyPayableHistory));

            jpaCompanyPayableHistories.add(jpaCompanyPayableHistory);
        }

        return companyPayableGenerationHistoryService.saveAll(jpaCompanyPayableHistories);
    }

    @Transactional
    public void bulkUpdateCompanyPayableStatus(Set<Long> companyPayableIds, PayableStatus status) {
        val companyPayables = jpaCompanyPayableRepository.findAllById(companyPayableIds);
        companyPayables.forEach(payable -> payable.setStatus(status));
        jpaCompanyPayableRepository.saveAll(companyPayables)
                .forEach(en -> companyPayableBroadcaster.publishPayableUpdateEvent(en.getId(), EventType.STATUS_UPDATED));
    }

    @Transactional
    public void bulkUpdateCompanyPayableTotalAmount(Map<Long, Double> companyPayableIdToTotalAmount) {
        if (companyPayableIdToTotalAmount == null) {
            log.info("Can not update company payable amount for null input");
            return;
        }
        val companyPayables = jpaCompanyPayableRepository.findAllById(companyPayableIdToTotalAmount.keySet());
        companyPayables.forEach(payable -> payable.setTotalAmount(companyPayableIdToTotalAmount.get(payable.getId())));
        jpaCompanyPayableRepository.saveAll(companyPayables);
    }

    private void autoSubmit(Map<JpaCompanyPayable, InvoiceGenerateService.CreateInvoiceParameterHolder> createInvoiceRequests,
                            Map<String, InvoiceGenerationAdapter.InvoiceResponseDataHolder> createInvoiceResponses,
                            List<JpaInvoicingError> invoicingErrors, List<ErrorHolder> errorHolders,
                            List<InvoiceAnomalyDetectorRequest> iadRequests) {

        log.info("Checking if invoice/s can be auto submit.");
        var filCreateInvoiceRequests = createInvoiceRequests.entrySet().stream()
                .filter(req -> Boolean.TRUE.equals(req.getValue().getCompanyPayableInput().getAutoSubmit()))
                .filter(req -> Integer.valueOf(FIRST_INVOICE_TYPE).equals(req.getValue().getCompanyPayableInput().getInvoiceType())) // TODO  TEMPORARILY ONLY ALLOW 1st invoice
                .filter(createInvoiceRequest -> hasIadPassed(iadRequests, createInvoiceRequest.getKey().getCompanyId()))
                .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

        var filCreateInvoiceResponses = createInvoiceResponses.entrySet().stream()
                .filter(response -> response.getValue().getErrorMessage() == null
                        || response.getValue().getErrorMessage().isEmpty())
                .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

        for (var entry : filCreateInvoiceRequests.entrySet()) {
            var createInvoiceRequest = entry.getValue();
            var monthYearInput = convertInvoiceGenerationDate(createInvoiceRequest.getCompanyPayableInput());
            var invoiceAdapter = getAdapter();
            var identifier = invoiceAdapter
                    .getUniqueIdentifier(createInvoiceRequest.getCustomerId(), invoiceGenerateService.generateReference(createInvoiceRequest.getCompanyPayableInput()));

            log.info("Checking if company id: {} invoice can be auto submit.", createInvoiceRequest.getCompanyId());
            InvoiceGenerationAdapter.InvoiceResponseDataHolder createInvoiceResponse = filCreateInvoiceResponses.get(identifier);

            try {
                invoiceAdapter.updateInvoiceStatus(createInvoiceResponse.getExternalInvoiceId(), InvoiceStatus.AUTHORIZED,
                        true);
            } catch (NetsuiteAdapterException ex) {
                log.error("Company id: {} failed to autosubmit with email and status AUTHORISED; Status DRAFT will remain.",
                        createInvoiceRequest.getCompanyId(), ex);
                var error = createJpaInvoicingErrorObject(
                        createInvoiceRequest.getCompanyId(),
                        null,
                        InvoiceGenerationErrorCode.MPE_NETSUITE_ERROR,
                        monthYearInput.getYear(),
                        monthYearInput.getMonth(),
                        ex.getMessage());

                invoicingErrors.add(error);
                errorHolders.add(new ErrorHolder(identifier, "", ex.toString()));
            }
        }
    }

    /**
     * Checks if intent to create AUTHORIZED invoice (aka autoSubmit) will be executed.
     *
     * @return True if there are no negative IAD results for a given invoice, else returns False
     */
    private boolean hasIadPassed(List<InvoiceAnomalyDetectorRequest> iadRequests, Long companyId) {

        try {
            // Gets iad request which was associated with current invoice to be generated
            var iadRequestForGivenInvoice = iadRequests.stream()
                    .filter(iadReq -> iadReq.getPayable().getCompanyId().equals(companyId))
                    .findFirst();

            // If there was no iad request at all -> something went wrong, so better fallback to DRAFT and not proceed with autoSubmit
            if (iadRequestForGivenInvoice.isEmpty()) {
                return false;
            }

            // Conduct IAD for all the rules and return a list of IAD results
            var iadResults = iadService.detectAndSaveAnomalies(iadRequestForGivenInvoice.get());

            // Returns TRUE only if all IAD rules were a SUCCESS  (Must be > 0 rules count)
            return !iadResults.isEmpty() && iadResults.stream()
                    .allMatch(iadResult -> AnomalyResultType.SUCCESS == iadResult.getResult());

        } catch (Exception ex) {
            log.error("Invoice Anomaly Detector failed, hence no autoSubmit will happen for companyId: {}", companyId, ex);
            return false; // When in doubt - do not allow autoSubmit
        }

    }

    private InvoiceGenerationAdapter getAdapter() {
        return invoiceGenerationFactory.getAdapter();
    }

    /**
     * For RelatedCompanyPayablesDataLoader
     */
    @Transactional
    public Map<ContractSnapshot, List<CompanyPayable>> getContractSnapshotToRelatedCompanyPayablesMap(Set<ContractSnapshot> contractSnapshots) {
        log.info("[getContractSnapshotToRelatedCompanyPayablesMap] with contractSnapshots: " + contractSnapshots);

        if (CollectionUtils.isEmpty(contractSnapshots)) {
            return Map.of();
        }

        Map<Long, Payroll.PayrollCycle> idToPayrollCycle = payrollServiceAdapter.getPayrollCycleOutputDetails(getPayrollCycleIds(contractSnapshots), true)
                .stream()
                .collect(toMap(
                        Payroll.PayrollCycle::getId,
                        Function.identity()
                ));

        Map<Long, Long> contractIdToCompanyId = contractServiceAdapter.getContractIdToCompanyIdMap(getContractIds(contractSnapshots));

        Map<ContractSnapshot, List<CompanyPayable>> resultMap = new HashMap<>();

        for (val cs : contractSnapshots) {
            val payrollCycle = idToPayrollCycle.get(cs.getPayrollCycle().getId());
            if (payrollCycle == null) {
                log.warn("[getContractSnapshotToRelatedCompanyPayablesMap] payrollCycle is not found for cs.payrollCycle.id={}, so ignoring this cs id={}", cs.getPayrollCycle().getId(), cs.getId());
                continue;
            }

            val contractCompanyId = contractIdToCompanyId.get(cs.getContractId());
            if (contractCompanyId == null) {
                log.warn("[getContractSnapshotToRelatedCompanyPayablesMap] companyId is not found for cs.contractId={}, so ignoring this cs id={}", cs.getContractId(), cs.getId());
                continue;
            }

            val jpaCompanyPayables = jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonth(
                    contractCompanyId,
                    payrollCycle.getStartDate().getYear(),
                    payrollCycle.getStartDate().getMonth()
            );
            val relatedJpaCompanyPayables = jpaCompanyPayables.stream()
                    .filter(jpaCP -> isCompanyPayableRelatedToContract(jpaCP, cs.getContractId()))
                    .collect(Collectors.toList());
            resultMap.put(cs, payableMapper.map(relatedJpaCompanyPayables));
        }

        log.info("[getContractSnapshotToRelatedCompanyPayablesMap] resultMap={}", resultMap);
        return resultMap;
    }

    /**
     * If `lineItem.contractId` is available, compare to `contractId`,
     * else compare `contractId` to the leading number in `lineItem.description`
     *
     * @return true if jpaCP is "related" to contractId
     */
    private static boolean isCompanyPayableRelatedToContract(JpaCompanyPayable jpaCP, Long contractId) {
        if (jpaCP.getInvoice() == null || CollectionUtils.isEmpty(jpaCP.getInvoice().getLineItems())) {
            return false;
        }
        return jpaCP.getInvoice()
                .getLineItems()
                .stream()
                .filter(Objects::nonNull)
                .anyMatch(jpaLI -> isLineItemRelatedToContract(jpaLI, contractId));
    }

    private static boolean isLineItemRelatedToContract(JpaInvoiceLineItem jpaLI, Long contractId) {
        if (jpaLI.getContractId() != null) {
            return Objects.equals(jpaLI.getContractId(), contractId);
        }
        return StringUtils.stripToEmpty(jpaLI.getDescription()).matches("^" + contractId + "(\\s-)*.*");
    }

    private static Set<Long> getPayrollCycleIds(Set<ContractSnapshot> contractSnapshots) {
        return contractSnapshots.stream()
                .map(cs -> cs.getPayrollCycle().getId())
                .collect(Collectors.toSet());
    }

    private static Set<Long> getContractIds(Set<ContractSnapshot> contractSnapshots) {
        return contractSnapshots.stream()
                .map(ContractSnapshot::getContractId)
                .collect(Collectors.toSet());
    }

    /**
     * Get first invoice status of inputted payroll payments
     *
     * @param payrollPayments
     * @return <ul>
     * <li>Empty map if empty pps OR no corresponding invoice status is found for inputed pp</li>
     * <li>Map of inputed pp to its invoice status</li>
     * </ul>
     */
    public Map<PayrollPayment, InvoiceDetail> getInvoiceDetails(Set<PayrollPayment> payrollPayments) {
        if (CollectionUtils.isEmpty(payrollPayments)) return Collections.emptyMap();

        // Ensure that all values in payroll payment is valid (have both filters)
        payrollPayments = payrollPayments.stream()
                .filter(pp -> {
                    try {
                        return pp.getContract().getId() != null
                                && pp.getPayrollCyclePayment().getPayrollCycle().getPayrollMonth() != null;
                    } catch (NullPointerException e) {
                        log.info(e.getMessage());
                        return false;
                    }
                })
                .collect(Collectors.toSet());

        // Grouping contractIds by payrollMonth to reduce DB call later
        Map<MonthYear, Set<Long>> payrollMonthToContractIdsMap = payrollPayments.stream()
                .map(pp -> {
                    var payrollMonth = pp.getPayrollCyclePayment().getPayrollCycle().getPayrollMonth();
                    var contractId = pp.getContract().getId();
                    return Pair.of(payrollMonth, contractId);
                })
                .collect(Collectors.groupingBy(Pair::getLeft,
                        Collectors.mapping(Pair::getRight, Collectors.toSet())));
        log.info("payrollMonthToContractIdsMap size: {}", payrollMonthToContractIdsMap.size());

        // Grouping returned results by payrollMonth to handle same contractId having multiple payrollMonths collision
        Map<MonthYear, Map<Long, InvoiceDetail>> payrollMonthToMapOfContractIdToInvoiceDetailMap
                = payrollMonthToContractIdsMap.entrySet().stream()
                .map(entrySet -> {
                    var payrollMonth = entrySet.getKey();
                    var contractIds = entrySet.getValue();
                    var contractIdToInvoiceDetailMap = invoiceFetcher.getInvoiceDetailBasedOnContracts(contractIds, payrollMonth.getMonth(), payrollMonth.getYear());

                    if (MapUtils.isEmpty(contractIdToInvoiceDetailMap)) {
                        log.info("contractIdToInvoiceDetailMap is null for contractIds: {}, payrollMonth: {}", contractIds, payrollMonth);
                        return null;
                    }

                    return Pair.of(payrollMonth, contractIdToInvoiceDetailMap);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Pair::getLeft, Pair::getRight));
        log.info("payrollMonthToMapOfContractIdToInvoiceDetailMap size: {}", payrollMonthToMapOfContractIdToInvoiceDetailMap.size());

        if (MapUtils.isEmpty(payrollMonthToMapOfContractIdToInvoiceDetailMap)) return Collections.emptyMap();

        // Get the invoice status for all payroll payments for each cycle and return
        Map<PayrollPayment, InvoiceDetail> payrollPaymentToInvoiceDetailMap = payrollPayments.stream()
                .map(pp -> {
                    var payrollMonth = pp.getPayrollCyclePayment().getPayrollCycle().getPayrollMonth();
                    var contractId = pp.getContract().getId();
                    var contractIdToInvoiceDetailMap = payrollMonthToMapOfContractIdToInvoiceDetailMap.get(payrollMonth);

                    if (!contractIdToInvoiceDetailMap.containsKey(contractId)) {
                        log.info("No contractId with value: {} in contractIdToInvoiceDetailMap", contractId);
                        return null;
                    }

                    var invoiceStatus = contractIdToInvoiceDetailMap.get(contractId);
                    return Pair.of(pp, invoiceStatus);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Pair::getLeft, Pair::getRight));
        log.info("payrollPaymentToInvoiceDetailMap size: {}", payrollPaymentToInvoiceDetailMap.size());

        return payrollPaymentToInvoiceDetailMap;
    }

    private void generateAndUpdateInvoiceWithSourceReportLink(List<ISRFileMetaData> isrFileMetaDataList,
                                                              List<IsrDataHolder> isrDataHolderList,
                                                              List<JpaInvoicingError> invoicingErrors,
                                                              List<ErrorHolder> errorHolders) {
        if (isrFileMetaDataList.isEmpty()) {
            return;
        }
        List<ISRFileMetaData> isrFileMetaDataListWithCodeAndLink = invoiceSourceReportCompositionService.populateAuthCodeAndEmailLink(isrFileMetaDataList);
        List<ISRFileMetaData> updatedIsrFileMetaDataList = isrFileMetaDataListWithCodeAndLink.stream()
                .filter(isrFileMetaData -> !isrFileMetaData.isExisting())
                .collect(Collectors.toList());


        invoiceSourceReportCompositionService.createMetadataForInvoiceSourceReports(updatedIsrFileMetaDataList);

        if (isrFileMetaDataListWithCodeAndLink.isEmpty()) {
            for (ISRFileMetaData isrFileMetaData : isrFileMetaDataList) {
                String errorMessage = "Fail to generate link for Source Report for company ID " + isrFileMetaData.getCompanyId()
                        + " for month " + isrFileMetaData.getMonth() + " and year " + isrFileMetaData.getYear();
                log.error(errorMessage);
                var error = createJpaInvoicingErrorObject(
                        isrFileMetaData.getCompanyId(),
                        null,
                        InvoiceGenerationErrorCode.MPE_NETSUITE_ERROR,
                        isrFileMetaData.getYear(),
                        isrFileMetaData.getMonth(),
                        errorMessage);

                invoicingErrors.add(error);
                errorHolders.add(new ErrorHolder(COMPANY, String.valueOf(isrFileMetaData.getCompanyId()),
                        errorMessage));
            }
            return;
        }

        var companyIdToIsrDataHolder = isrDataHolderList.stream()
                .collect(toMap(
                        IsrDataHolder::getCompanyId,
                        Function.identity()
                ));

        for (ISRFileMetaData isrFileMetaData : isrFileMetaDataListWithCodeAndLink) {
            long companyIdToUpdate = isrFileMetaData.getCompanyId();
            if (!companyIdToIsrDataHolder.containsKey(companyIdToUpdate)) {
                log.error("Fail to get external ID for company ID " + companyIdToUpdate);
                continue;
            }
            var isrDataHolder = companyIdToIsrDataHolder.get(companyIdToUpdate);
            String externalIdToUpdate = isrDataHolder.getExternalId();
            String sourceReportLink = isrFileMetaData.getSourceReportLink();
            if (RecordType.INVOICE.equals(isrDataHolder.getRecordType())) {
                log.info("Update Invoice external ID {} for company ID {}", externalIdToUpdate, companyIdToUpdate);
                val invoiceAdapter = getAdapter();
                try {
                    invoiceAdapter.addInvoiceSourceReportLink(externalIdToUpdate, sourceReportLink);
                } catch (NetsuiteAdapterException ex) {
                    log.error("Invoice ID {} with external ID {} fails to add Source Report link.",
                            companyIdToUpdate, externalIdToUpdate, ex);
                    var error = createJpaInvoicingErrorObject(
                            isrFileMetaData.getCompanyId(),
                            null,
                            InvoiceGenerationErrorCode.MPE_NETSUITE_ERROR,
                            isrFileMetaData.getYear(),
                            isrFileMetaData.getMonth(),
                            ex.getMessage());

                    invoicingErrors.add(error);
                    errorHolders.add(new ErrorHolder(COMPANY, String.valueOf(isrFileMetaData.getCompanyId()), ex.toString()));
                }
                continue;
            }

            // update NS credit note
            log.info("Update Credit Note external ID {} for company ID {}", externalIdToUpdate, companyIdToUpdate);
            negativeInvoiceCreditNoteService.update(externalIdToUpdate, UpdateCreditNoteRequest.builder()
                    .externalId(externalIdToUpdate).invoiceSourceReportLink(sourceReportLink).customerId(isrDataHolder.getCustomerId())
                    .build());
        }
    }

    /**
     * As of 2023-09-28, only company payable with Second Invoice may have Invoice Soure Report to download
     */
    private boolean isReportSupportedInCompanyPayable(CompanyPayable companyPayable) {
        return companyPayable.getInvoice() != null && companyPayable.getInvoice().getType() != null &&
                companyPayable.getInvoice().getType().equals(com.multiplier.payable.types.InvoiceType.SALARY);
    }

    private List<CompanyPayable> updateCompanyPayablesWithAvailableReports(List<CompanyPayable> companyPayables) {
        return invoiceSourceReportService.updateCompanyPayablesWithAvailableReports(companyPayables);
    }

    private boolean validateCompanyBindingAndPricing(Long companyId, MonthYearInput monthYearInput,
                                                     List<JpaInvoicingError> jpaInvoicingErrors, List<ErrorHolder> errorHolders) {

        Optional<JpaCompanyBinding> jpaCompanyBindingOptional = companyBindingService.getBinding(companyId);
        JpaPricing jpaPricing = pricingRepository.findByCompanyId(companyId);

        if (jpaCompanyBindingOptional.isEmpty()) {
            log.error("Company binding not found for companyId : " + companyId);

            var jpaInvoicingError = createJpaInvoicingErrorObject(companyId,
                    null,
                    InvoiceGenerationErrorCode.MPE_COMPANY_BINDING_NOT_FOUND,
                    monthYearInput.getYear(),
                    monthYearInput.getMonth(),
                    null
            );

            jpaInvoicingErrors.add(jpaInvoicingError);
            errorHolders.add(ErrorHolder.getKnownError(InvoiceError.NOT_FOUND_EXTERNAL_COMPANY_BINDING, companyId.toString()));
            return false;
        }

        if (jpaPricing == null || jpaPricing.getBillingCurrencyCode() == null) {
            log.error("Pricing not found for companyId : " + companyId);

            var jpaInvoicingError = createJpaInvoicingErrorObject(companyId,
                    null,
                    InvoiceGenerationErrorCode.MPE_COMPANY_PRICING_NOT_CORRECT,
                    monthYearInput.getYear(),
                    monthYearInput.getMonth(),
                    null
            );

            jpaInvoicingErrors.add(jpaInvoicingError);
            errorHolders.add(ErrorHolder.getKnownError(InvoiceError.NOT_FOUND_COMPANY_PRICING, companyId.toString()));
        }

        return true;
    }

    private List<ContractOuterClass.Contract> getEligibleContracts(Long companyId, MonthYearInput monthYearInput,
                                                                   CompanyPayableInput companyPayableInput,
                                                                   List<JpaInvoice> previousFirstInvoices,
                                                                   List<JpaInvoicingError> jpaInvoicingErrors,
                                                                   List<ErrorHolder> errorHolders) {

        var firstInvoiceContracts = firstInvoiceService.extractContractIdsFromInvoices(previousFirstInvoices);

        var allEligibleContracts = getFirstInvoiceEligibleContractsProvider(companyPayableInput)
                .getFirstInvoiceEligibleContracts(companyId, monthYearInput, companyPayableInput);

        var remainingEligibleContracts = allEligibleContracts.stream()
                .filter(contract -> !firstInvoiceContracts.contains(contract.getId()))
                .collect(Collectors.toList());

        if (remainingEligibleContracts.isEmpty()) {
            var jpaInvoicingError = createJpaInvoicingErrorObject(
                    companyId,
                    null,
                    InvoiceGenerationErrorCode.MPE_ELIGIBLE_CONTRACTS_NOT_FOUND,
                    monthYearInput.getYear(),
                    monthYearInput.getMonth(),
                    null
            );

            var errorReason = allEligibleContracts.isEmpty() ?
                    "The eligible contract is empty after removing ones present in previous first invoice(s)" :
                    "The eligible contract is empty for invoice generation";
            jpaInvoicingErrors.add(jpaInvoicingError);
            errorHolders.add(new ErrorHolder(COMPANY, String.valueOf(companyId), errorReason));
        }

        return remainingEligibleContracts;
    }

    List<ManagementFee> calculateManagementFeesForGrossInvoice(Long companyId,
                                                               List<ContractOuterClass.Contract> contracts,
                                                               MonthYear invoiceMonth,
                                                               List<JpaInvoicingError> jpaInvoicingErrors,
                                                               List<ErrorHolder> errorHolders) {
        try {
            return pricingRuleEngine.getManagementFees(companyId, contracts.stream()
                            .map(ContractOuterClass.Contract::getId)
                            .toList(),
                    invoiceMonth,
                    true);

        } catch (Exception ex) {
            var error = createJpaInvoicingErrorObject(
                    companyId,
                    null,
                    InvoiceGenerationErrorCode.MPE_MANAGEMENT_FEE_CALCULATION_ERROR,
                    invoiceMonth.getYear(),
                    invoiceMonth.getMonth(),
                    null
            );
            jpaInvoicingErrors.add(error);
            errorHolders.add(ErrorHolder.getKnownError(InvoiceError.MANAGEMENT_FEE_CALCULATION_ERROR, companyId.toString()));
            log.error("Error encountered during calculation of management fee on first invoice on company {}. Error: ",
                    companyId, ex);
            throw new InvoiceGenerationException(InvoiceGenerationErrorCode.MPE_MANAGEMENT_FEE_CALCULATION_ERROR);
        }
    }


    @Transactional
    public void voidFreelancerCompanyPayable(@org.jetbrains.annotations.NotNull Long flCompanyPayableID) {
        log.info("Trying to void flCompanyPayable={}", flCompanyPayableID);
        List<JpaCompanyPayable> validCompanyPayables = jpaCompanyPayableRepository.findByIdInAndStatusNotInAndTypeIn(
                List.of(flCompanyPayableID), List.of(PayableStatus.DELETED, PayableStatus.VOIDED), List.of(CompanyPayableType.FREELANCER)
        );

        if (validCompanyPayables.isEmpty()) {
            log.warn("No valid fl company payable found for deletion or voiding out of flCompanyPayableID={}", flCompanyPayableID);
            // we intentionally not throw an exception
            // as the expected state of the request is already reached
            // this is useful for handling bundle deletion cases
            return;
        }

        if (validCompanyPayables.size() != 1) {
            log.warn("found more than one valid fl company payable found for deletion or voiding out of flCompanyPayableID={}", flCompanyPayableID);
            throw new MplBusinessException(PayableErrorCode.MULTIPLE_COMPANY_PAYABLES_FOUND,
                "found more than one valid fl company payable found for deletion or voiding out of flCompanyPayableID=" + flCompanyPayableID);
        }

        if (validCompanyPayables.get(0).getStatus() == PayableStatus.PAID) {
            log.warn("No valid fl company payable found for deletion or voiding out of flCompanyPayableID={}, cause the payable is in a paid state", flCompanyPayableID);
            throw new MplBusinessException(PayableErrorCode.COMPANY_PAYABLE_ALREADY_PAID,
                "No valid fl company payable found for deletion or voiding out of flCompanyPayableID=" + flCompanyPayableID + " cause the payable is in a paid state");
        }

        log.info("Found valid companyPayable={} for flCompanyPayableIds={} to mark as VOIDED/DELETED", validCompanyPayables.size(), flCompanyPayableID);

        // FL Company Payable, whenever we void company payable
        // we should void the external invoice, which voids the company payable as well
        // always 1 entry at max
        validCompanyPayables.forEach(
                flCP -> invoiceGenerateService.voidInvoice(flCP.getInvoice().getExternalId())
        );

        log.info("Successfully voided/ deleted flCompanyPayableID={}", flCompanyPayableID);
    }

    public void handleCompanyPayableDueAmountChange(Long id) {
        log.info("invoice due amount event received for company payable id={}", id);
        jpaInvoiceRepository.findByCompanyPayableId(id).ifPresent(invoice -> {
            companyPayableBroadcaster.publishPayableUpdateEvent(
                    invoice.getCompanyPayable().getId(),
                    EventType.DUE_AMOUNT_UPDATED
            );
        });
    }
}
