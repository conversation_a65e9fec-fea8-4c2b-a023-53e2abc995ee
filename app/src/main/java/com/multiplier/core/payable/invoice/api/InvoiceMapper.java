package com.multiplier.core.payable.invoice.api;

import com.netsuite.suitetalk.proxy.v2023_1.platform.core.BooleanCustomFieldRef;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.CustomFieldList;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.CustomFieldRef;
import com.netsuite.suitetalk.proxy.v2023_1.transactions.sales.Invoice;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class InvoiceMapper {

    private final InvoiceStatusMapper invoiceStatusMapper;

    public Invoice map(String invoiceId, UpdateInvoiceApiRequest request) {
        var invoice = new Invoice();
        invoice.setInternalId(invoiceId);
        if (request.getStatus() != null) {
            var recordRef = invoiceStatusMapper.map(request.getStatus());
            invoice.setApprovalStatus(recordRef);
        }
        if (request.getSendEmail() != null) {
            var platformApproved = request.getSendEmail();
            var booleanCustomFieldRef = new BooleanCustomFieldRef(null, "custbody_multi_platform_approve", platformApproved);
            var customFieldRef = new CustomFieldRef[]{booleanCustomFieldRef};
            var customFieldList = new CustomFieldList();
            customFieldList.setCustomField(customFieldRef);
            invoice.setCustomFieldList(customFieldList);
        }
        return invoice;
    }

}
