package com.multiplier.core.payable.company;

import com.multiplier.payable.types.*;

import com.multiplier.payable.types.DocumentData;
import lombok.*;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

@Builder
@Setter
@Getter
@Jacksonized
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class ACHCompanyPayinMethod implements CompanyPayInMethod, PayInMethod {
    private long id;
    private Company company;
    private PayInMethodType payInMethodType;
    private PayInMethodContext context;
    private Boolean isActive;
    private Boolean isEnabled;
    private PayInMethodDefinitionStep currentStep;
    private CompanyPayInMethodWorkflowStatus currentStatus;
    private List<CompanyPayInMethodWorkflowStep> workflowSteps;
    private List<CompanyPayInMethodBankDetails> bankDetails;
    private List<DocumentData> documents;
    private List<KeyValue> data;
}
