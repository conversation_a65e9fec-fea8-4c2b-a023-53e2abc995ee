package com.multiplier.core.payable.creditnote.database;

import com.multiplier.payable.types.CreditNoteReason;
import com.multiplier.payable.types.CreditNoteStatus;
import com.multiplier.payable.types.DateRange;
import com.multiplier.payable.types.Sort;
import lombok.*;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

@Builder(toBuilder = true)
@Getter
@Jacksonized
@EqualsAndHashCode
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
public class CreditNoteQueryDto {

    private final List<Long> ids;

    private final CreditNoteStatus status;

    private final CreditNoteReason reason;

    private final List<Long> invoiceIds;

    private final List<Long> companyIds;

    private final DateRange createdDateRange;

    private final String reference;

    private final String creditNoteNo;

    private final Integer pageSize;

    private final Integer pageNumber;

    private final List<Sort> sort;

    private final List<CreditNoteStatus> statuses;
}
