package com.multiplier.core.payable.graphql.dataloader;

import com.multiplier.core.util.ExecutorUtil;
import com.multiplier.core.payable.service.InvoiceGenerateService;
import com.multiplier.payable.types.Invoice;
import com.netflix.graphql.dgs.DgsDataLoader;
import lombok.RequiredArgsConstructor;
import org.dataloader.MappedBatchLoader;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;

@RequiredArgsConstructor
@DgsDataLoader(name = "invoice", maxBatchSize = 2000)
public class InvoiceByCompanyPayableIdDataLoader implements MappedBatchLoader<Long, Invoice> {

    private final InvoiceGenerateService invoiceGenerateService;

    @Override
    public CompletionStage<Map<Long, Invoice>> load(final Set<Long> companyPayableIds) {
        Executor executor = ExecutorUtil.getNewSecurityContextExecutor();
        return CompletableFuture.supplyAsync(() ->
                invoiceGenerateService.getInvoicesForCompanyPayableIds(companyPayableIds), executor);
    }

}
