package com.multiplier.core.payable.repository;

import com.multiplier.core.payable.repository.model.JpaCompanyPayable;
import com.multiplier.core.payable.repository.model.JpaContractDepositPayable;
import com.multiplier.payable.engine.contractinvoice.ContractInvoice;
import com.multiplier.payable.types.PayableStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface JpaContractDepositPayableRepository extends JpaRepository<JpaContractDepositPayable, Long> {

    List<JpaContractDepositPayable> findAllByContractId(Long contractId);

    List<JpaContractDepositPayable> findByContractIdIn(Set<Long> contractIds);

    Optional<JpaContractDepositPayable> findFirstByCompanyPayable(JpaCompanyPayable companyPayable);

    Optional<JpaContractDepositPayable> findFirstByCompanyPayableId(Long companyPayableId);

    @Query("""
                SELECT new com.multiplier.payable.engine.contractinvoice.ContractInvoice(cp.id, i.id, i.externalId AS externalInvoiceId)
                FROM JpaContractDepositPayable cpp
                INNER JOIN cpp.companyPayable cp
                INNER JOIN cp.invoice i
                WHERE cpp.contractId = :contractId
                AND cp.status NOT IN :payableStatuses
            """)
    List<ContractInvoice> findContractInvoiceByContractIdAndPayableStatusNotIn(Long contractId, List<PayableStatus> payableStatuses);

    @Query("""
                SELECT cp
                FROM JpaContractDepositPayable cpp
                INNER JOIN cpp.companyPayable cp
                WHERE cpp.contractId = :contractId
                AND cp.status NOT IN :payableStatuses
            """)
    List<JpaCompanyPayable> findContractPayables(Long contractId, List<PayableStatus> payableStatuses);

}
