package com.multiplier.core.payable.service.query.builder;

import com.multiplier.core.payable.repository.model.JpaCompanyPayable;
import com.multiplier.core.payable.utils.SpecificationBuilder;
import com.multiplier.payable.types.CompanyPayableFilters;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class CompanyPayableSpecificationBuilder implements SpecificationBuilder<JpaCompanyPayable, CompanyPayableFilters> {

    @Override
    public Specification<JpaCompanyPayable> build(CompanyPayableFilters filters) {
        var spec = Specification.<JpaCompanyPayable>where(null);

        /**
         * TODO Himanshu This will filter credit notes showing in ops. Will remove when refactored.
         */
        spec = spec.and(((root, query, builder) -> root.join(CompanyPayableFields.INVOICE).isNotNull()));

        if (CollectionUtils.isNotEmpty(filters.getPayableIds())) {
            spec = spec.and((root, query, builder) -> builder
                    .in(root.get(CompanyPayableFields.ID))
                    .value(filters.getPayableIds()));
        }
        if (filters.getCompanyId() != null) {
            if (filters.getCompanyIds() == null) {
                filters.setCompanyIds(List.of(filters.getCompanyId()));
            } else {
                List<Long> companyIds = new ArrayList<>(filters.getCompanyIds());
                companyIds.add(filters.getCompanyId());
                filters.setCompanyIds(companyIds);
            }
        }
        if (CollectionUtils.isNotEmpty(filters.getCompanyIds())) {
            spec = spec.and((root, query, builder) -> builder
                    .in(root.get(CompanyPayableFields.COMPANY_ID))
                    .value(filters.getCompanyIds()));
        }
        if (filters.getMonthYear() != null) {
            if (filters.getMonthYear().getMonth() != null) {
                spec = spec.and((root, query, builder) -> builder
                        .equal(root.get(CompanyPayableFields.MONTH), filters.getMonthYear().getMonth()));
            }
            if (filters.getMonthYear().getYear() != null) {
                spec = spec.and((root, query, builder) -> builder
                        .equal(root.get(CompanyPayableFields.YEAR), filters.getMonthYear().getYear()));
            }
        }
        if (StringUtils.isNotBlank(filters.getInvoiceNo())) {
            spec = spec.and((root, query, builder) -> builder.like(
                    root.join(CompanyPayableFields.INVOICE).get(InvoiceFields.INVOICE_NO).as(String.class),
                    "%" + filters.getInvoiceNo().trim() + "%"));
        }
        if (StringUtils.isNotBlank(filters.getReference())) {
            spec = spec.and((root, query, builder) -> builder.like(
                    root.join(CompanyPayableFields.INVOICE).get(InvoiceFields.REFERENCE).as(String.class),
                    "%" + filters.getReference().trim() + "%"));
        }
        if (filters.getPayableType() != null) {
            spec = spec.and((root, query, builder) -> builder
                    .equal(root.get(CompanyPayableFields.TYPE), filters.getPayableType()));
        }
        if (filters.getCreatedDateRange() != null) {
            spec = (filters.getCreatedDateRange().getStartDate() == null)
                    ? spec
                    : spec.and((root, query, builder) -> builder.greaterThanOrEqualTo(root.get(CompanyPayableFields.CREATED_ON), filters.getCreatedDateRange().getStartDate()));

            spec = (filters.getCreatedDateRange().getEndDate() == null)
                    ? spec
                    : spec.and((root, query, builder) -> builder.lessThanOrEqualTo(root.get(CompanyPayableFields.CREATED_ON), filters.getCreatedDateRange().getEndDate()));
        }
        if (CollectionUtils.isNotEmpty(filters.getStatuses())) {
            spec = spec.and((root, query, builder) -> builder
                    .in(root.get(CompanyPayableFields.STATUS))
                    .value(filters.getStatuses()));
        }
        return spec;
    }

    public static final class CompanyPayableFields {
        static final String ID = "id";
        static final String COMPANY_ID = "companyId";
        static final String MONTH = "month";
        static final String YEAR = "year";
        static final String INVOICE = "invoice";
        static final String STATUS = "status";
        static final String TYPE = "type";
        static final String CREATED_ON = "createdOn";
    }

    public static final class InvoiceFields {
        static final String INVOICE_NO = "invoiceNo";
        static final String REFERENCE = "reference";
    }
}
