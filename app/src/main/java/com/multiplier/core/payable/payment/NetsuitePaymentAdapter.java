package com.multiplier.core.payable.payment;

import com.multiplier.core.payable.adapters.netsuite.client.NetsuiteMappings;
import com.multiplier.core.payable.adapters.netsuite.exception.NetsuiteAdapterException;
import com.multiplier.core.payable.adapters.netsuite.webservices.NetsuiteWebserviceClient;
import com.multiplier.core.payable.adapters.netsuite.webservices.NetsuiteWebserviceException;
import com.multiplier.core.payable.creditnote.api.ApiResponseParser;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.RecordRef;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.types.RecordType;
import com.netsuite.suitetalk.proxy.v2023_1.platform.messages.WriteResponse;
import com.netsuite.suitetalk.proxy.v2023_1.transactions.customers.CustomerPayment;
import com.netsuite.suitetalk.proxy.v2023_1.transactions.customers.CustomerPaymentApply;
import com.netsuite.suitetalk.proxy.v2023_1.transactions.customers.CustomerPaymentApplyList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.ZoneId;
import java.util.*;

import static com.multiplier.core.payable.payment.TimeZoneUtil.offsetTimeZone;

@Slf4j
@Component
@RequiredArgsConstructor
public class NetsuitePaymentAdapter implements PaymentAdapter {

    private static final TimeZone timeZoneSingapore = TimeZone.getTimeZone(ZoneId.of("Asia/Singapore"));

    private final NetsuiteWebserviceClient netsuiteWebserviceClient;
    private final ApiResponseParser apiResponseParser;

    @Override
    public CreatePaymentResponse create(final CreatePaymentRequest createPaymentRequest) {
        val amount = createPaymentRequest.amount();
        val invoiceId = createPaymentRequest.externalInvoiceId();
        val accountCode = createPaymentRequest.accountCode();
        val paymentDate = isPaymentDateEqualsEpochTime(createPaymentRequest.paymentDateInSGT())
                ? offsetTimeZone(new Date(), TimeZone.getDefault(), timeZoneSingapore)
                : createPaymentRequest.paymentDateInSGT();

        log.info("[NetsuiteAdapter] Adding payment on Netsuite for invoiceId: {} " +
                "with amount = {} " +
                "into account code = {} " +
                "with payment date = {}",
                invoiceId, amount, accountCode, paymentDate);

        try {
            val invoice = Optional.ofNullable((com.netsuite.suitetalk.proxy.v2023_1.transactions.sales.Invoice) netsuiteWebserviceClient.getInvoice(invoiceId))
                    .orElseThrow(() -> new NetsuiteAdapterException("Cannot find Invoice on Netsuite for invoice with id " + invoiceId));
            val customerId = invoice.getEntity().getInternalId();

            val accountIdOnNetsuite = NetsuiteMappings.getAccountCodeFromOldGlCode(accountCode);
            log.info("Paying for invoice id {} with amount {} to account id on NS {}", invoiceId, amount, accountIdOnNetsuite);
            com.netsuite.suitetalk.proxy.v2023_1.transactions.customers.CustomerPayment customerPayment =
                    new com.netsuite.suitetalk.proxy.v2023_1.transactions.customers.CustomerPayment();
            var customerPaymentApply = new CustomerPaymentApply();
            customerPaymentApply.setAmount(amount);
            customerPaymentApply.setDoc(Long.valueOf(invoiceId));
            CustomerPaymentApply[] customerPaymentApplies = List.of(customerPaymentApply).toArray(new CustomerPaymentApply[]{});
            CustomerPaymentApplyList customerPaymentApplyList = new CustomerPaymentApplyList();
            customerPaymentApplyList.setApply(customerPaymentApplies);

            customerPayment.setApplyList(customerPaymentApplyList);
            customerPayment.setAccount(new RecordRef(null, accountIdOnNetsuite, null, RecordType.account));
            customerPayment.setUndepFunds(false);
            customerPayment.setCustomer(new RecordRef(null, customerId, null, RecordType.customer));

            Calendar calendar = Calendar.getInstance(timeZoneSingapore);

            customerPayment.setTranDate(paymentDate == null ? null : determinePaymentDateForAccountingPeriod(paymentDate, calendar));

            WriteResponse writeResponse = netsuiteWebserviceClient.addPayment(customerPayment);
            val externalPaymentId = apiResponseParser.getInternalId(writeResponse);
            log.debug("Successfully add payment on Netsuite for invoice id {}", invoice);
            return new CreatePaymentResponse(externalPaymentId);
        } catch (NetsuiteWebserviceException re) {
            log.error("[NetsuiteAdapter] Error occur during addPayment WS call. invoiceId: {}, errorMessage: {}"
                    , invoiceId, re.getMessage());
            throw new NetsuiteAdapterException(String.format("Error occur during addPayment WS call. {%s}", re.getMessage()), re);
        } catch (NetsuiteAdapterException ne) {
            log.error("[NetsuiteAdapter] Error adding payment to Netsuite Invoice. invoiceId: {}, errorMessage: {}"
                    , invoiceId, ne.getMessage());
            throw ne;
        } catch (Exception e) {
            log.error("[NetsuiteAdapter] Netsuite addPayment failed. invoiceId: {}", invoiceId, e);
            throw new NetsuiteAdapterException(String.format("Netsuite createPayment failed. {%s}", e.getMessage()));
        }
    }

//    @Override
//    public DeletePaymentResponse delete(final DeletePaymentRequest deletePaymentRequest) {
//        try {
//            var writeResponse = netsuiteWebserviceClient.deletePayment(deletePaymentRequest.externalPaymentId());
//            val externalPaymentId =  apiResponseParser.getInternalId(writeResponse);
//            return new DeletePaymentResponse(externalPaymentId);
//        } catch (NetsuiteWebserviceException re) {
//            log.error("Error occur during delete payment WS call. externalPaymentId: {}, errorMessage: {}"
//                    , deletePaymentRequest.externalPaymentId(), re.getMessage());
//            throw new NetsuiteAdapterException(String.format("Error occur during deletePayment WS call. {%s}", re.getMessage()), re);
//        } catch (NetsuiteAdapterException ne) {
//            log.error("[NetsuiteAdapter] Error deleting payment to Netsuite Invoice. externalPaymentId: {}, errorMessage: {}"
//                    , deletePaymentRequest.externalPaymentId(), ne.getMessage());
//            throw ne;
//        }
//    }

    @Override
    public PaymentDto get(final String externalId) {
        if (!StringUtils.hasText(externalId)) {
            throw new NetsuiteAdapterException("Cannot find any payment for external id = " + externalId);
        }

        try {
            val netsuiteCustomerPayment = netsuiteWebserviceClient.getPayment(externalId);
            return getPaymentDto(netsuiteCustomerPayment);
        } catch (NetsuiteWebserviceException re) {
            log.error("Error occur during get payment WS call. externalPaymentId: {}, errorMessage: {}"
                    , externalId, re.getMessage());
            throw new NetsuiteAdapterException(String.format("Error occur during get payment WS call. {%s}", re.getMessage()), re);
        }
    }

    //Maybe improve in future into a full mapper.
    private PaymentDto getPaymentDto(@NotNull CustomerPayment customerPayment) {
        return new PaymentDto(customerPayment.getTranId(), customerPayment.getTranDate());
    }

    /**
     * Checking if the payment date is null. gRPC doesn't send null date but rather send start of epoch as the date. It's
     * hard to get the proper timezone and then compare the time to epoch, hence I've added a safe date of 2000-01-01 so
     * that we can easily compare the start of epoch seconds.
     * @param paymentDateInSGT
     * @return
     */
    private boolean isPaymentDateEqualsEpochTime(Calendar paymentDateInSGT) {
        Calendar bufferedTime = Calendar.getInstance();
        bufferedTime.set(2000, Calendar.JANUARY, 1, 0, 0, 0);
        bufferedTime.set(Calendar.MILLISECOND, 0);

        return paymentDateInSGT == null || bufferedTime.compareTo(paymentDateInSGT) > 0;
    }

    /**
     * Determines the appropriate payment date for accounting purposes.
     * If the requested payment date is in a different month than the current date,
     * returns the 1st day of the current month to ensure it's in the correct accounting period.
     * Otherwise, returns the requested payment date unchanged.
     *
     * @param requestedPaymentDate The payment date requested
     * @return The adjusted payment date for proper accounting
     */
    private Calendar determinePaymentDateForAccountingPeriod(Calendar requestedPaymentDate, Calendar today) {
        log.info("Today: {}, Requested Payment Date: {}", today.getTime(), requestedPaymentDate.getTime());

        // Check if payment date is in a different month than today
        boolean differentMonth = today.get(Calendar.MONTH) != requestedPaymentDate.get(Calendar.MONTH) ||
                today.get(Calendar.YEAR) != requestedPaymentDate.get(Calendar.YEAR);

        if (!differentMonth) {
            return requestedPaymentDate; // Same month, use requested date
        }
        
        // Create date on 1st of current month with time set to midnight
        today.set(Calendar.DAY_OF_MONTH, 1);
        today.set(Calendar.HOUR_OF_DAY, 0);
        today.set(Calendar.MINUTE, 0);
        today.set(Calendar.SECOND, 0);
        today.set(Calendar.MILLISECOND, 0);

        log.info("Payment date adjusted from {} to {} to match current accounting period",
                requestedPaymentDate.getTime(), today.getTime());

        return today;
    }

}
