package com.multiplier.core.payable.msa.dto;

import com.multiplier.core.payable.pricing.DiscountTerm;
import lombok.*;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

@Builder
@Getter
@Jacksonized
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class MsaAddendumChangeDiscountTerms {
    private List<DiscountTerm> oldValue;
    private List<DiscountTerm> newValue;
}