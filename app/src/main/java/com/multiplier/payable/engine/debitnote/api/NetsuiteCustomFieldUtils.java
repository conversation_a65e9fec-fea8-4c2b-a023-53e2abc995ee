package com.multiplier.payable.engine.debitnote.api;

import com.multiplier.payable.engine.currency.CurrencyCode;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.CustomFieldList;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.CustomFieldRef;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.DateCustomFieldRef;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.StringCustomFieldRef;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public final class NetsuiteCustomFieldUtils {
    private static final String CUSTOM_COL_BASE_CURRENCY = "custcol_mlt_base_currency";
    private static final String CUSTOM_COL_AMOUNT_BASE_CURRENCY = "custcol_mlt_amount_base_currency";
    private static final String CUSTOM_COL_START_DATE = "custcol_multi_rev_rec_start_date";
    private static final String CUSTOM_COL_END_DATE = "custcol_multi_rev_rec_end_date";
    private static final String CUSTOM_COL_LINE_ITEM_IDS = "custcol_mlt_line_item_ids";
    private static final String CUSTOM_COL_EXPENSE_CONTRACY_ID = "custcol_contract_id";
    private static final String CUSTOM_COL_EXPENSE_EMPLOYEE_NAME = "custcol2";
    private static final String DEFAULT_TIMEZONE = "UTC";
    private static final TimeZone timeZone = TimeZone.getTimeZone(DEFAULT_TIMEZONE);

    private static final Function<CurrencyCode, CustomFieldRef> baseCurrencyFunction = currencyCode ->
            new StringCustomFieldRef(null, CUSTOM_COL_BASE_CURRENCY, currencyCode.name());

    private static final Function<Double, CustomFieldRef> amountInBaseCurrencyFunction = amount ->
            new StringCustomFieldRef(null, CUSTOM_COL_AMOUNT_BASE_CURRENCY,
                    new DecimalFormat("0.00").format(amount));

    private static final Function<LocalDate, CustomFieldRef> revRecStartDateFunction = startDate ->
            new DateCustomFieldRef(null, CUSTOM_COL_START_DATE, getCalenderDate(startDate));

    private static final Function<LocalDate, CustomFieldRef> revRecEndDateFunction = endDate ->
            new DateCustomFieldRef(null, CUSTOM_COL_END_DATE, getCalenderDate(endDate));

    private static final Function<List<UUID>, CustomFieldRef> companyPayableIdsFunction = companyPayableIds ->
            new StringCustomFieldRef(
                    null,
                    CUSTOM_COL_LINE_ITEM_IDS,
                    companyPayableIds
                            .stream()
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .collect(Collectors.joining(","))
            );

    private static final Function<String, CustomFieldRef> contractIdInExpenseItem = contractId ->
            new StringCustomFieldRef(null, CUSTOM_COL_EXPENSE_CONTRACY_ID, contractId);

    private static final Function<String, CustomFieldRef> employeeNameInExpenseItem = employeeName ->
            new StringCustomFieldRef(null, CUSTOM_COL_EXPENSE_EMPLOYEE_NAME, employeeName);

    private NetsuiteCustomFieldUtils() {

    }

    public static CustomFieldRef buildBaseCurrency(CurrencyCode currencyCode) {
        return buildCustomField(currencyCode, baseCurrencyFunction);
    }

    public static CustomFieldRef buildAmountInBaseCurrency(Double amount) {
        return buildCustomField(amount, amountInBaseCurrencyFunction);
    }

    public static CustomFieldRef buildRevRecStartDate(LocalDate startDate) {
        return buildCustomField(startDate, revRecStartDateFunction);
    }

    public static CustomFieldRef buildRevRecEndDate(LocalDate endDate) {
        return buildCustomField(endDate, revRecEndDateFunction);
    }

    public static CustomFieldRef buildCompanyPayableIdsReference(List<UUID> companyPayableIds) {
        return buildCustomField(companyPayableIds, companyPayableIdsFunction);
    }

    public static CustomFieldRef populateContractIdInExpenseItem(String contractId) {
        return buildCustomField(contractId, contractIdInExpenseItem);
    }

    public static CustomFieldRef populateEmployeeNameInExpenseItem(String contractId) {
        return buildCustomField(contractId, employeeNameInExpenseItem);
    }

    public static CustomFieldList compileCustomFields(CustomFieldRef... fieldRefs) {
        var nonNullFieldRefs = Arrays.stream(fieldRefs)
                .filter(Objects::nonNull)
                .toArray(CustomFieldRef[]::new);

        CustomFieldList customFieldList = new CustomFieldList();
        customFieldList.setCustomField(nonNullFieldRefs);
        return customFieldList;
    }

    private static <FIELD> CustomFieldRef buildCustomField(FIELD field, Function<FIELD, CustomFieldRef> fieldRefFunction) {
        if (Objects.isNull(field)) {
            return null;
        }

        return fieldRefFunction.apply(field);
    }

    private static Calendar getCalenderDate(LocalDate referenceDate) {
        var calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTimeZone(timeZone);
        calendar.set(referenceDate.getYear(), referenceDate.getMonthValue() - 1, referenceDate.getDayOfMonth());
        return calendar;
    }

}
