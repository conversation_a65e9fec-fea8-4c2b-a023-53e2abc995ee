package com.multiplier.payable.severance.history

import com.multiplier.payable.severance.history.payable.CompanyPayableSeveranceHistoryInput
import com.multiplier.payable.severance.history.payable.CompanyPayableSeveranceHistoryService
import mu.KotlinLogging
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.temporal.TemporalAdjusters

@Component
@EnableSchedulerLock(defaultLockAtLeastFor = "PT0M", defaultLockAtMostFor = "PT2H")
@ConditionalOnProperty(prefix = "contract-severance-history.scheduler", name = ["enabled"], havingValue = "true", matchIfMissing = true)
class ContractSeveranceHistoryScheduler(
    private val companyPayableSeveranceHistoryServices: List<CompanyPayableSeveranceHistoryService>,
    private val config: ContractSeveranceHistorySchedulerConfig,
    private val dateProvider: () -> LocalDate = { LocalDate.now() }
) {

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    @Scheduled(
        cron = "#{@contractSeveranceHistorySchedulerConfig.cronExpression}",
        zone = "#{@contractSeveranceHistorySchedulerConfig.timeZone}"
    )
    @SchedulerLock(
        name = "contractSeveranceHistoryScheduler",
        lockAtLeastFor = "PT1M",
        lockAtMostFor = "PT2H" // Lock for at most 2 hours
    )
    fun schedule() {
        logger.info { "Starting contract severance history scheduler with config: enabled=${config.enabled}, cron: ${config.cronExpression}, timeZone: ${config.timeZone}, batchSize: ${config.batchSize}" }

        try {
        val startDate = dateProvider()

        collectSeveranceHistoryForGivenMonth(
            getCurrentMonthStartDate(startDate),
            getCurrentMonthEndDate(startDate),
            batchSize = config.batchSize
        )

        collectSeveranceHistoryForGivenMonth(
            getLastMonthStartDate(startDate),
            getLastMonthEndDate(startDate),
            batchSize = config.batchSize
        )

        logger.info { "Contract severance history scheduler completed successfully" }
        } catch (e: Exception) {
            logger.error(e) { "Error in contract severance history scheduler: ${e.message}" }
        }
    }

    private fun collectSeveranceHistoryForGivenMonth(
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
        batchSize: Int
    ) {
        logger.info { "Collecting severance history for month = $startDate to $endDate" }
        companyPayableSeveranceHistoryServices.forEach {
            it.getContractSeveranceHistoryRecords(
                CompanyPayableSeveranceHistoryInput(
                    startDate = startDate,
                    endDate = endDate,
                    batchSize = batchSize
                )
            )
        }
    }

    private fun getCurrentMonthStartDate(
        currentDate: LocalDate
    ): ZonedDateTime {
        return currentDate
            .withDayOfMonth(1)                     // Set day to the 1st of the month
            .atStartOfDay(getZoneId())  // Convert to start of day in system's time zone
    }

    private fun getCurrentMonthEndDate(
        currentDate: LocalDate
    ): ZonedDateTime {
        val lastDayOfMonth = currentDate.with(TemporalAdjusters.lastDayOfMonth())
        val endOfDay = LocalTime.MAX // 23:59:59.*********
        return ZonedDateTime.of(lastDayOfMonth, endOfDay, getZoneId())
    }

    private fun getLastMonthStartDate(
        currentDate: LocalDate
    ): ZonedDateTime {
        return currentDate
            .minusMonths(1)
            .withDayOfMonth(1)
            .atStartOfDay(getZoneId())
    }

    private fun getLastMonthEndDate(
        currentDate: LocalDate
    ): ZonedDateTime {
        val lastMonthDate = currentDate.minusMonths(1)
        val lastDayOfLastMonth = lastMonthDate.with(TemporalAdjusters.lastDayOfMonth())
        return ZonedDateTime.of(lastDayOfLastMonth, LocalTime.MAX, getZoneId())
    }

    private fun getZoneId(): ZoneId {
        return ZoneId.of(config.timeZone)
    }

}