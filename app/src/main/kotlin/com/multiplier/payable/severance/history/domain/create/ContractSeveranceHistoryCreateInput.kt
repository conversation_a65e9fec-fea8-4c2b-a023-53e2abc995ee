package com.multiplier.payable.severance.history.domain.create

import com.multiplier.payable.types.Amount
import java.time.ZonedDateTime

data class ContractSeveranceHistoryCreateInput(
    val companyPayableId: Long,
    val startDate: ZonedDateTime,
    val endDate: ZonedDateTime,
    val contractId: Long,
    val amount: Amount,
    val amountInContractCurrency: Amount,
) {

    fun validate() {
        require(amount.amount != null) {
            "The amount must be non-null"
        }
        require(amount.currency != null) {
            "The amount currency must be non-null"
        }
        require(amountInContractCurrency.amount != null) {
            "The contract currency amount must be non-null"
        }
        require(amountInContractCurrency.currency != null) {
            "The contract currency must be non-null"
        }
    }

}
