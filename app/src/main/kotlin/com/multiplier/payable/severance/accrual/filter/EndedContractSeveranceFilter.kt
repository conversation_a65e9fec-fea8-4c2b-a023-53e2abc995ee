package com.multiplier.payable.severance.accrual.filter

import com.multiplier.contract.schema.contract.ContractOuterClass
import org.springframework.stereotype.Component

@Component
class EndedContractSeveranceFilter : SeveranceFilter {
    override fun shouldFilterOut(input: SeveranceFilterInput): <PERSON>olean {
        return input.contract.status == ContractOuterClass.ContractStatus.ENDED
    }
}