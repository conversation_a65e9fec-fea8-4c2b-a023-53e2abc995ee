package com.multiplier.payable.severance.history.payable.invoice

import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.severance.history.payable.CompanyPayableSeveranceLineItemFinder
import com.multiplier.payable.severance.history.payable.ContractPayableSeveranceLineItem
import com.multiplier.payable.types.Amount
import com.multiplier.payable.types.CurrencyCode
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.time.ZoneId

@Component
class InvoiceSeveranceLineItemFinder: CompanyPayableSeveranceLineItemFinder<InvoiceDTO> {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    override fun getSeveranceLineItems(
        dto: InvoiceDTO
    ): List<ContractPayableSeveranceLineItem> {
        logger.info { "Finding severance line items for invoice id = ${dto.id}" }

        return try {
            dto.lineItems
                .filter { it.itemType == LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL }
                .map { ContractPayableSeveranceLineItem(
                    contractId = it.contractId,
                    startTime = it.startPayCycleDate.atStartOfDay(ZoneId.of("UTC")),
                    endTime = it.endPayCycleDate.atStartOfDay(ZoneId.of("UTC")),
                    amount = Amount.newBuilder()
                        .currency(dto.billingCurrencyCode)
                        .amount(it.grossAmount)
                        .build(),
                    amountInContractCurrency = createAmountInContractCurrency(it),
                    companyPayableId = dto.companyPayableId,
                ) }
                .toList()
                .also { logger.info { "Found severance line items for contractIds = ${it.forEach { it.contractId }}" +
                        " for invoice id = ${dto.id}" } }
        } catch (e: Exception) {
            logger.error(e) { "Error while mapping severance line items for invoice id = ${dto.id}" }
            throw InvoiceSeveranceLineItemMappingException("Company Payable Id = ${dto.companyPayableId}")
        }
    }

    private fun createAmountInContractCurrency(lineItemDTO: LineItemDTO): Amount {
        logger.info { "Creating amount in contract currency. Contract Currency = ${lineItemDTO.baseCurrency}" +
                " and unitAmount = ${lineItemDTO.unitAmount} " +
                "and quantity = ${lineItemDTO.quantity}" }

        return Amount.newBuilder()
            .currency(CurrencyCode.valueOf(lineItemDTO.baseCurrency))
            .amount(lineItemDTO.amountInBaseCurrency * lineItemDTO.quantity)
            .build()
    }

}