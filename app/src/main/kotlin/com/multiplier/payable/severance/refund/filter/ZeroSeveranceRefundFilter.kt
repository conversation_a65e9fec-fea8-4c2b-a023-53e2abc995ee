package com.multiplier.payable.severance.refund.filter

import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class ZeroSeveranceRefundFilter : SeveranceRefundFilter {

    private companion object {
        val logger = KotlinLogging.logger {}
    }

    override fun shouldFilterOut(input: SeveranceRefundFilterInput): Boolean {
        val isZero = input.severanceRecord.severance == 0.0
        if (isZero) {
            logger.info {
                "Filtered out severance with zero amount for contractId=${input.severanceRecord.contractId}, " +
                        "severanceRecord=${input.severanceRecord}"
            }
        }
        return isZero
    }
}