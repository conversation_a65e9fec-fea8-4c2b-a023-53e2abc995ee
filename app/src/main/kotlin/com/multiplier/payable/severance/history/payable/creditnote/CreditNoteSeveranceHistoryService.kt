package com.multiplier.payable.severance.history.payable.creditnote

import com.multiplier.core.payable.creditnote.database.CreditNoteQueryDto
import com.multiplier.core.payable.creditnote.database.CreditNoteQueryService
import com.multiplier.payable.severance.history.payable.CompanyPayableSeveranceCreateService
import com.multiplier.payable.severance.history.payable.CompanyPayableSeveranceHistoryInput
import com.multiplier.payable.severance.history.payable.CompanyPayableSeveranceHistoryService
import com.multiplier.payable.severance.history.port.ContractSeveranceHistory
import com.multiplier.payable.types.CreditNoteStatus
import com.multiplier.payable.types.DateRange
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class CreditNoteSeveranceHistoryService(
    private val creditNoteQueryService: CreditNoteQueryService,
    private val creditNoteSeveranceLineItemFinder: CreditNoteSeveranceLineItemFinder,
    private val companyPayableSeveranceCreateService: CompanyPayableSeveranceCreateService,
) : CompanyPayableSeveranceHistoryService {

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    override fun getContractSeveranceHistoryRecords(
        companyPayableSeveranceHistoryInput: CompanyPayableSeveranceHistoryInput
    ): List<ContractSeveranceHistory> {
        logger.info { "Getting credit note severance history records for input = $companyPayableSeveranceHistoryInput" }
        val creditNoteIds = creditNoteQueryService.getCreditNotesIds(
            createCreditNoteQueryDto(companyPayableSeveranceHistoryInput)
        )

        val batchSize = companyPayableSeveranceHistoryInput.batchSize

        // break the credit note ids in batches
        val creditNoteIdsBatches = creditNoteIds.chunked(batchSize)

        return creditNoteIdsBatches.map { creditNoteIdsBatch ->
            logger.info { "Getting credit note severance history records for credit note ids = $creditNoteIdsBatch" }
            try {
                // get credit note severance history records for each batch
                val creditNotesIdsMap = creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(creditNoteIdsBatch)

                val contractSeveranceLineItems = creditNotesIdsMap
                    .map { creditNoteEntry ->
                        try {
                            creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNoteEntry.value)
                        } catch (e: CreditNoteSeveranceLineItemMappingException) {
                            logger.error(e) { "Error getting severance line items for credit note ID = ${creditNoteEntry.key}" }
                            emptyList()
                        }
                    }
                    .flatten()

                companyPayableSeveranceCreateService.create(contractSeveranceLineItems)
            } catch (e: Exception) {
                logger.error { "Ignoring batch with credit note ids = $creditNoteIdsBatch for mapping due to error: ${e.message}" }
                emptyList()
            }
        }.flatten()

    }

    private fun createCreditNoteQueryDto(
        companyPayableSeveranceHistoryInput: CompanyPayableSeveranceHistoryInput
    ): CreditNoteQueryDto {
        // Create builder
        val builder = CreditNoteQueryDto.builder()
            .statuses(listOf(CreditNoteStatus.FULLY_APPLIED, CreditNoteStatus.DRAFT))
            .createdDateRange(
                DateRange.newBuilder()
                    .startDate(companyPayableSeveranceHistoryInput.startDate.toLocalDateTime())
                    .endDate(companyPayableSeveranceHistoryInput.endDate.toLocalDateTime())
                    .build()
            )

        // Conditionally add companyIds only if not null and not empty
        companyPayableSeveranceHistoryInput.companyIds
            .takeIf { it.isNotEmpty() }
            .let { builder.companyIds(it) }

        // Return built DTO
        return builder.build()
    }
}