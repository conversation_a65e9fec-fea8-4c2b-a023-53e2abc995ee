package com.multiplier.payable.netsuite.transaction.appevent.publisher

import mu.KotlinLogging
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component

/**
 * An [ApplicationEventPublisher] that publishes [TransactionAppEvent].
 */
@Component
class TransactionAppEventPublisher(
    private val eventPublisher: ApplicationEventPublisher,
) {

    companion object {
        private val log = KotlinLogging.logger {}
    }

    fun publish(context: TransactionAppEventPublisherContext) {
        log.info { "Publish an app event with $context" }

        eventPublisher.publishEvent(
            TransactionAppEvent(
                source = this,
                companyPayableId = context.companyPayableId,
                companyId = context.companyId,
                month = context.month,
                year = context.year,
            )
        )
    }
}