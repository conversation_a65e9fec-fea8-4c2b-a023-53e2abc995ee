package com.multiplier.payable.netsuite.transaction.appevent.handler

import com.multiplier.payable.netsuite.transaction.appevent.publisher.TransactionAppEvent
import com.multiplier.payable.netsuite.transaction.kafka.producer.TransactionKafkaProducer
import com.multiplier.payable.netsuite.transaction.kafka.producer.TransactionKafkaProducerContext
import mu.KotlinLogging
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

/**
 * A class that handles [TransactionAppEvent].
 */
@Component
class TransactionAppEventHandler(
    private val transactionKafkaProducer: TransactionKafkaProducer,
) {

    companion object {
        private val log = KotlinLogging.logger {}
    }

    @TransactionalEventListener(value = [TransactionAppEvent::class], phase = TransactionPhase.AFTER_COMMIT)
    fun handle(appEvent: TransactionAppEvent) {
        requireNotNull(appEvent.companyPayableId) {
            "companyPayableId must not be null. Please check if the publishers have checked the companyPayableId!"
        }
        log.info { "Handle transaction app event with $appEvent" }

        val context = TransactionKafkaProducerContext(
            companyPayableId = appEvent.companyPayableId,
            companyId = appEvent.companyId,
            month = appEvent.month,
            year = appEvent.year,
        )
        transactionKafkaProducer.produce(context)
    }
}