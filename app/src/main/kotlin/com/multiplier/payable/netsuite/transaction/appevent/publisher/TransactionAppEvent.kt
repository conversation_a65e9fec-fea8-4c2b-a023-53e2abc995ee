package com.multiplier.payable.netsuite.transaction.appevent.publisher

import org.springframework.context.ApplicationEvent
import java.util.Objects

/**
 * An [ApplicationEvent] for Netsuite transaction.
 */
class TransactionAppEvent(
    source: Any,
    val companyPayableId: Long? = null,
    val companyId: Long,
    val month: Int,
    val year: Int,
) : ApplicationEvent(source) {

    override fun toString(): String {
        return "companyPayableId $companyPayableId, " +
            "companyId $companyId, " +
            "month $month, " +
            "year $year"
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is TransactionAppEvent) return false

        return companyPayableId == other.companyPayableId
            && companyId == other.companyId
            && month == other.month
            && year == other.year
    }

    override fun hashCode(): Int {
        return Objects.hash(companyPayableId, companyId, month, year)
    }
}