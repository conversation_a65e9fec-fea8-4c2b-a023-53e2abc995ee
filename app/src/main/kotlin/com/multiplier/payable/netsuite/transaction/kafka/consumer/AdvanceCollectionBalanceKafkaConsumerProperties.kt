package com.multiplier.payable.netsuite.transaction.kafka.consumer

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "platform.advance-collection-balance.kafka.consumer")
data class AdvanceCollectionBalanceKafkaConsumerProperties(
    override val bootstrapServers: String,
    override val groupId: String,
    override val maxPollRecords: Int,
    override val concurrency: Int,
    override val autoStartup: Boolean,
    override val maxPollIntervalMs: Long,
) : NetsuiteTransactionConsumerProperties
