package com.multiplier.payable.tax

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.companypayable.database.PayableItemTypeMapper
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.PayableItemType

object TaxIdentifierKeyUtils {
    fun getCompanyCountryCode(company: com.multiplier.core.payable.company.Company): CountryCode {
        requireNotNull(company.primaryEntity) { "Primary entity must not be null for companyId: ${company.id}" }
        requireNotNull(company.primaryEntity.address) { "Address must not be null for companyId: ${company.id}" }
        requireNotNull(company.primaryEntity.address.country) { "Country must not be null for companyId: ${company.id}" }
        return company.primaryEntity.address.country
    }

    fun mapLineItemTypeToPayableItemType(
        lineItemType: String,
        payableItemTypeMapper: PayableItemTypeMapper,
    ): PayableItemType {
        val lineItemTypeEnum = LineItemType.valueOf(lineItemType)
        return payableItemTypeMapper.map(lineItemTypeEnum)
    }
}
