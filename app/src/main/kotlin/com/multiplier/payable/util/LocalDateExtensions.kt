package com.multiplier.payable.util

import com.multiplier.payable.engine.domain.aggregates.DateRange
import java.time.LocalDate
import java.time.LocalTime
import java.time.temporal.TemporalAdjusters

fun LocalDate.getCurrentMonthDateRange(): DateRange {
    val firstDayOfMonth = this.withDayOfMonth(1).atStartOfDay()
    val lastDayOfMonth = this.with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX)

    return DateRange(firstDayOfMonth, lastDayOfMonth)
}
