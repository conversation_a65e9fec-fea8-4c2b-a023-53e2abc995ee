package com.multiplier.payable.util

import com.google.protobuf.Timestamp
import java.time.Instant
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

class TimeUtils {
    companion object {
        private val isoDateFormatter = DateTimeFormatter.ISO_LOCAL_DATE
        private val isoDateTimeFormatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME

        @JvmStatic
        fun toIsoDateString(timestamp: Timestamp): String {
            return Instant.ofEpochSecond(timestamp.seconds, timestamp.nanos.toLong())
                .atZone(ZoneOffset.UTC)
                .toLocalDate()
                .format(isoDateFormatter)
        }

        @JvmStatic
        fun toIsoDateTimeString(timestamp: Timestamp): String {
            return Instant.ofEpochSecond(timestamp.seconds, timestamp.nanos.toLong())
                .atZone(ZoneOffset.UTC)
                .format(isoDateTimeFormatter)
        }

        @JvmStatic
        fun fromIsoDate(dateStr: String): Timestamp {
            val localDate = LocalDate.parse(dateStr, isoDateFormatter)
            val instant = localDate.atStartOfDay(ZoneOffset.UTC).toInstant()
            return Timestamp.newBuilder()
                .setSeconds(instant.epochSecond)
                .setNanos(instant.nano)
                .build()
        }

        @JvmStatic
        fun fromIsoDateTime(dateTimeStr: String): Timestamp {
            val instant = OffsetDateTime.parse(dateTimeStr, isoDateTimeFormatter).toInstant()
            return Timestamp.newBuilder()
                .setSeconds(instant.epochSecond)
                .setNanos(instant.nano)
                .build()
        }

        @JvmStatic
        fun toInstant(timestamp: Timestamp): Instant =
            Instant.ofEpochSecond(timestamp.seconds, timestamp.nanos.toLong())

        @JvmStatic
        fun fromInstant(instant: Instant): Timestamp =
            Timestamp.newBuilder()
                .setSeconds(instant.epochSecond)
                .setNanos(instant.nano)
                .build()
    }
}
