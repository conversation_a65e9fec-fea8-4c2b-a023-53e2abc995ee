package com.multiplier.payable.kafka

import com.multiplier.core.company.schema.kafka.CompanyLegalEntityMessage
import com.multiplier.core.company.schema.kafka.CompanyLegalEntityStatus
import org.springframework.stereotype.Component

@Component
class LegalEntityEventMapper {

    fun map(events: List<CompanyLegalEntityMessage>): List<LegalEntityEvent> {
        return events.map { map(it) }
    }

    fun map(event: CompanyLegalEntityMessage): LegalEntityEvent {
        return LegalEntityEvent(
            entityId = event.legalEntityInfo.entityId,
            companyId = event.legalEntityInfo.companyId,
            status = map(event.legalEntityInfo.status),
            primary = event.legalEntityInfo.isPrimary
        )
    }

    fun map(status: CompanyLegalEntityStatus): LegalEntityEventStatus {
        return LegalEntityEventStatus.valueOf(status.name)
    }
}