package com.multiplier.payable.kafka

import com.multiplier.core.company.schema.kafka.CompanyLegalEntityMessage
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.messaging.handler.annotation.Payload
import org.springframework.stereotype.Component

@Component
class LegalEntityKafkaListener(
    private val processor: LegalEntityKafkaMessageProcessor,
) {
    companion object {
        private const val LISTENER_ID = "LegalEntityKafkaListener"
        val log: Logger = LoggerFactory.getLogger(this::class.java.name)
    }

    @KafkaListener(
        id = LISTENER_ID,
        topics = ["#{'\${platform.legal-entity.kafka.topic}'}"],
        containerFactory = "legalEntityConsumerFactory"
    )
    fun consumeLegalEntityEvent(
        @Payload kafkaMessages: List<CompanyLegalEntityMessage>,
        acknowledgment: Acknowledgment,
    ) {
        log.info("KafkaListener legalEntityEvent is called with : {}"
            , kafkaMessages.joinToString { it.toString() })

        try {
            processor.handle(kafkaMessages)
        } catch (e: Exception) {
            log.error("Exception happened while processing legal entity event. " +
                    "Please fix it manually. messages = "
                    + kafkaMessages.joinToString { it.toString() }, e)
        } finally {
            acknowledgment.acknowledge()
        }
    }

}