package com.multiplier.payable.kafka

import com.multiplier.core.config.featureflag.FeatureFlagService
import lombok.RequiredArgsConstructor
import lombok.extern.slf4j.Slf4j
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.util.*

@Component
@Slf4j
@RequiredArgsConstructor
class CompanyUpdateScheduler (
    private val kafkaListener: CompanyUpdateKafkaListener,
    private val featureFlagService: FeatureFlagService,
) {

    @Scheduled(cron = "\${spring.scheduler.contactUpdate.cronTime}")
    fun handleContactUpdateData() {
        val jobId = UUID.randomUUID().toString().replace("-", StringUtils.EMPTY).uppercase(Locale.getDefault())
        log.info("Contact Update scheduled job started {}", jobId)

        val feature = featureFlagService.feature("enable-payable-contact-update-scheduler", emptyMap())

        if (feature.on) {
            kafkaListener.startOrResumeListener()
        } else {
            log.info("Contact Update scheduled job is disabled. Nothing to do...")
        }

        log.info("Contact Update scheduled job ended {}", jobId)
    }

    companion object {
        val log: Logger = LoggerFactory.getLogger(this::class.java.name)
    }
}