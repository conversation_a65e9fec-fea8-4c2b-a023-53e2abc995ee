package com.multiplier.payable.kafka.properties

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "platform.legal-entity.kafka")
data class LegalEntityKafkaProperties (
    var bootstrapServers: String,
    var topic: String,
    var keySerializer: String,
    var valueSerializer: String,
    var maxBlockMs: Int,
    var retryCount: Int,
    var retryBackoffMs: Int,
)