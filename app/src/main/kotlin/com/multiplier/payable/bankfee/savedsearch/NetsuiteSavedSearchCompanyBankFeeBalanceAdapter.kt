package com.multiplier.payable.bankfee.savedsearch

import com.multiplier.common.exception.toBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.payable.bankfee.CompanyBankFeeBalance
import com.multiplier.payable.bankfee.CompanyBankFeeBalanceAdapter
import com.multiplier.payable.bankfee.savedsearch.application.NetsuiteSavedSearchCompanyBankFeeBalanceReadUseCase
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.types.CurrencyCode
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class NetsuiteSavedSearchCompanyBankFeeBalanceAdapter(
    private val netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase: NetsuiteSavedSearchCompanyBankFeeBalanceReadUseCase,
): CompanyBankFeeBalanceAdapter {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    override fun getBankFee(
        companyId: Long,
        entityId: Long,
        monthYear: MonthYear
    ): CompanyBankFeeBalance {
        logger.info { "Getting bank fee for companyId [$companyId], entityId [$entityId], monthYear [$monthYear]" }

        val entities = netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase.findByCompanyIdAndEntityIdAndMonthAndYear(
            companyId = companyId,
            entityId = entityId,
            month = monthYear.month,
            year = monthYear.year
        )

        if (entities.isNotEmpty()) {
            logger.info { "Found ${entities.size} bank fee balance entities with IDs: [${entities.mapNotNull { it.id }.joinToString(", ")}] for companyId [$companyId], entityId [$entityId], monthYear [$monthYear]" }

            // Validate currency consistency and aggregate amounts
            val aggregatedAmount = aggregateAmountsWithCurrencyValidation(entities, companyId, entityId, monthYear)

            // Collect all entity IDs that contributed to the aggregation
            val entityIds = entities.mapNotNull { it.id }

            // Use the first entity as the base for other properties
            val firstEntity = entities.first()

            return CompanyBankFeeBalance(
                ids = entityIds,
                companyId = firstEntity.companyId,
                entityId = firstEntity.entityId,
                month = firstEntity.month,
                year = firstEntity.year,
                amount = aggregatedAmount,
            )
        }

        return CompanyBankFeeBalance(
            ids = emptyList(),
            companyId = companyId,
            entityId = entityId,
            month = monthYear.month,
            year = monthYear.year,
            amount = Amount(BigDecimal.ZERO, CurrencyCode.USD)
        )
    }

    /**
     * Aggregates amounts from multiple entities with currency validation.
     *
     * @param entities List of bank fee balance entities to aggregate
     * @param companyId Company ID for error context
     * @param entityId Entity ID for error context
     * @param monthYear Month/Year for error context
     * @return Aggregated amount with consistent currency
     * @throws BusinessException if currencies are inconsistent across entities
     */
    private fun aggregateAmountsWithCurrencyValidation(
        entities: List<com.multiplier.payable.bankfee.savedsearch.domain.NetsuiteSavedSearchCompanyBankFeeBalanceEntity>,
        companyId: Long,
        entityId: Long,
        monthYear: MonthYear
    ): Amount {
        // Get the base currency from the first entity
        val baseCurrency = entities.first().amount.currency
        logger.info { "Base currency for aggregation: $baseCurrency" }

        // Validate all entities have the same currency
        val inconsistentCurrencies = entities.filter { it.amount.currency != baseCurrency }
        if (inconsistentCurrencies.isNotEmpty()) {
            val currencyDetails = entities.map { "ID: ${it.id}, Currency: ${it.amount.currency}, Amount: ${it.amount.value}, PaymentVoucherId: ${it.paymentVoucherId}" }
            logger.error { "Currency mismatch detected in bank fee balance entities: $currencyDetails" }

            throw PayableErrorCode.CURRENCY_MISMATCH_EXCEPTION.toBusinessException(
                "Currency mismatch detected in bank fee balance entities. All entities must have the same currency.",
                context = mapOf(
                    "companyId" to companyId,
                    "entityId" to entityId,
                    "month" to monthYear.month,
                    "year" to monthYear.year,
                    "baseCurrency" to baseCurrency.name,
                    "inconsistentEntities" to inconsistentCurrencies.map {
                        mapOf(
                            "id" to it.id,
                            "currency" to it.amount.currency.name,
                            "amount" to it.amount.value.toString(),
                            "paymentVoucherId" to it.paymentVoucherId
                        )
                    }
                )
            )
        }

        // Aggregate amounts
        val totalAmount = entities.sumOf { it.amount.value }
        logger.info { "Aggregated ${entities.size} entities with IDs: [${entities.mapNotNull { it.id }.joinToString(", ")}] with total amount: $totalAmount $baseCurrency" }

        return Amount(
            value = totalAmount,
            currency = baseCurrency
        )
    }

}