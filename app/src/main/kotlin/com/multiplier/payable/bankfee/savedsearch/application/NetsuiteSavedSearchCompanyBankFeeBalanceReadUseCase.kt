package com.multiplier.payable.bankfee.savedsearch.application

import com.multiplier.payable.bankfee.savedsearch.domain.NetsuiteSavedSearchCompanyBankFeeBalanceEntity
import com.multiplier.payable.bankfee.savedsearch.domain.NetsuiteSavedSearchCompanyBankFeeBalanceRepository
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class NetsuiteSavedSearchCompanyBankFeeBalanceReadUseCase(
    private val netsuiteSavedSearchCompanyBankFeeBalanceRepository: NetsuiteSavedSearchCompanyBankFeeBalanceRepository
) {

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    fun findByCompanyIdAndEntityIdAndMonthAndYear(
        companyId: Long,
        entityId: Long,
        month: Int,
        year: Int
    ): List<NetsuiteSavedSearchCompanyBankFeeBalanceEntity> {
        logger.info { "Finding bank fee balances for companyId: $companyId, entityId: $entityId, month: $month, year: $year" }

        val entities = netsuiteSavedSearchCompanyBankFeeBalanceRepository.findByCompanyIdAndEntityIdAndMonthAndYear(
            companyId, entityId, month, year
        )

        logger.info { "Found ${entities.size} bank fee balance entities with IDs: [${entities.mapNotNull { it.id }.joinToString(", ")}] for companyId: $companyId, entityId: $entityId, month: $month, year: $year" }

        return entities
    }
}