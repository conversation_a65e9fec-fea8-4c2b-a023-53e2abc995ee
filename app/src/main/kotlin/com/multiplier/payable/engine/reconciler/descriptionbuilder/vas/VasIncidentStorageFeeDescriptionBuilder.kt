package com.multiplier.payable.engine.reconciler.descriptionbuilder.vas

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilder
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import com.multiplier.payable.types.CountryCode
import org.springframework.stereotype.Service

/**
 * A [PayableItemDescriptionBuilder] for [LineItemType.VAS_INCIDENT_STORAGE_FEE].
 * Returns different descriptions based on the country code:
 * - "Storage Service Fee - India" for India
 * - "Storage Service Fee - Others" for all other countries
 */
@Service
class VasIncidentStorageFeeDescriptionBuilder : PayableItemDescriptionBuilder {
    override val lineItemType: LineItemType
        get() = LineItemType.VAS_INCIDENT_STORAGE_FEE

    override fun build(context: PayableItemDescriptionBuilderContext): String =
        when (context.incidentWrapper?.incident?.countryCode) {
            CountryCode.IND -> "Storage Service Fee - India"
            else -> "Storage Service Fee - Others"
        }
}
