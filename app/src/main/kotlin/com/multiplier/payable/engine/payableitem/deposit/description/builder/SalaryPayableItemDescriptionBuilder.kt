package com.multiplier.payable.engine.payableitem.deposit.description.builder

import com.multiplier.core.payable.adapters.DepositServiceAdapter
import com.multiplier.payable.engine.common.TimeUnit
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.deposit.DepositPayableItemDescriptionBuilderContext
import com.multiplier.payable.engine.deposit.DepositType
import com.multiplier.payable.engine.deposit.SalaryPeriodPolicyFeed
import com.multiplier.payable.engine.payableitem.deposit.description.label.builder.SalaryPayableItemDescriptionLabelBuilder
import com.multiplier.payable.engine.reconciler.descriptionbuilder.AmountFormatter
import org.springframework.stereotype.Component

@Component
class SalaryPayableItemDescriptionBuilder(
    private val labelBuilder: SalaryPayableItemDescriptionLabelBuilder,
    private val depositServiceAdapter: DepositServiceAdapter,
    private val amountFormatter: AmountFormatter,
) : DepositPayableItemDescriptionBuilder {
    companion object {
        private const val DEFAULT_DESCRIPTION_FORMAT = "%s: %s %s"
        private const val ENHANCED_DESCRIPTION_FORMAT =
            "Deposit - Salary \nTotal: %s \nSalary: %s \n Contributions: %s \nPeriod Covered: %s"
    }

    override fun getType(): DepositType = DepositType.SALARY

    override fun build(context: DepositPayableItemDescriptionBuilderContext): String =
        if (context.deposit.contractType == ContractType.EMPLOYEE && context.enhancedDescriptionEnabled) {
            enhancedSalaryDescription(context)
        } else {
            val label = labelBuilder.build(context.deposit.contractType, context.companyId)
            val amountStr = amountFormatter.format(context.deposit.amount)
            DEFAULT_DESCRIPTION_FORMAT.format(label, context.deposit.currencyCode, amountStr)
        }

    private fun enhancedSalaryDescription(context: DepositPayableItemDescriptionBuilderContext): String {
        val deposit = context.deposit
        val currency = deposit.currencyCode
        val feed =
            depositServiceAdapter.getDepositFeed(
                deposit.depositId!!,
                deposit.type,
                TimeUnit.MONTHS,
            ) as SalaryPeriodPolicyFeed

        return ENHANCED_DESCRIPTION_FORMAT.format(
            DescriptionHelpers.formatAmount(currency, deposit.amount),
            DescriptionHelpers.formatAmount(feed.grossAmount),
            DescriptionHelpers.formatAmount(feed.contributionAmount),
            DescriptionHelpers.formatTimePeriod(feed.period),
        )
    }
}
