package com.multiplier.payable.engine.reconciler.descriptionbuilder

import com.multiplier.core.payable.adapters.api.LineItemType
import org.springframework.stereotype.Service

@Service
class OrderFormAdvanceAdjustmentAorFreelancerPayableItemDescriptionBuilder(
    private val amountFormatter: AmountFormatter,
) : PayableItemDescriptionBuilder {
    override val lineItemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR_FREELANCER

    override fun build(context: PayableItemDescriptionBuilderContext): String {
        val formattedAmount = amountFormatter.format(context.amountInBaseCurrency)
        return "Order Form Advance Adjustment for AOR Freelancer services - ${context.currencyCode} $formattedAmount"
    }
}
