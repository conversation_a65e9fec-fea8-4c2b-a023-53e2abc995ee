package com.multiplier.payable.engine.payableitem

import com.multiplier.core.payable.adapters.api.LineItemType
import org.springframework.stereotype.Component

/**
 * Returns the mapper according to lineItemType
 */
@Component
class PayableItemStoreDtoToJpaPayableItemStoreMapperFactory(
    private val defaultPayableItemStoreDtoToJpaPayableItemStoreMapper: DefaultPayableItemStoreDtoToJpaPayableItemStoreMapper,
    private val noCountryPayableItemStoreDtoToJpaPayableItemStoreMapper: NoCountryPayableItemStoreDtoToJpaPayableItemStoreMapper,
) {
    companion object {
        private val ITEM_TYPE_WITH_NO_COUNTRY_REQUIRED =
            listOf(
                LineItemType.PAYMENT_FEE,
                LineItemType.PLATFORM_FEE,
                LineItemType.VAS_INCIDENT_DISCOUNT,
                LineItemType.ORDER_FORM_ADVANCE_EOR,
                LineItemType.ORDER_FORM_ADVANCE_GLOBAL_PAYROLL,
                LineItemType.ORDER_FORM_ADVANCE_AOR,
                LineItemType.BANK_FEE
            )
    }

    fun getMapperForLineItemType(lineItemType: LineItemType?): PayableItemStoreDtoToJpaPayableItemStoreMapper =
        when {
            lineItemType in ITEM_TYPE_WITH_NO_COUNTRY_REQUIRED -> noCountryPayableItemStoreDtoToJpaPayableItemStoreMapper
            else -> defaultPayableItemStoreDtoToJpaPayableItemStoreMapper
        }
}
