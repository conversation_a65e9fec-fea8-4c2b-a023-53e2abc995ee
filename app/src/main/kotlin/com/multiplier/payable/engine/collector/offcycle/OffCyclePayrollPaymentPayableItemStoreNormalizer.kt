package com.multiplier.payable.engine.collector.offcycle

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.PayableItemStoreNormalizer
import com.multiplier.payable.engine.collector.ProcessedCollectorInput
import com.multiplier.payable.engine.collector.data.ProcessedOffCycleCollectorInput
import com.multiplier.payable.engine.collector.gp.DefaultDataKey
import com.multiplier.payable.engine.collector.payroll.ContractPayroll
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import org.springframework.stereotype.Component

@Component
class OffCyclePayrollPaymentPayableItemStoreNormalizer(
    private val objectMapper: ObjectMapper,
) : PayableItemStoreNormalizer<ContractPayroll> {

    override fun normalize(
        data: ContractPayroll,
        processedInput: ProcessedCollectorInput,
    ): PayableItemStoreDto {
        processedInput as ProcessedOffCycleCollectorInput

        val key = DefaultDataKey(
            lineItemType = processedInput.lineItemType,
            companyId = data.companyId,
            oTime = processedInput.originalTimestamp.toEpochMilli(),
            month = data.startDate.monthValue,
            year = data.startDate.year,
            entityId = processedInput.entityId,
        )

        val monthYear = processedInput.timeQueryDuration
        val amount = calculateAmount(data, processedInput.lineItemType)

        return PayableItemStoreDto(
            amount = amount,
            currency = data.currencyCode,
            contractId = data.contractId,
            companyId = data.companyId,
            transactionId = processedInput.transactionId,
            itemType = processedInput.lineItemType,
            month = data.startDate.monthValue,
            year = data.startDate.year,
            itemData = objectMapper.writeValueAsString(data),
            periodStartDate = monthYear.from.toLocalDate(),
            periodEndDate = monthYear.to.toLocalDate(),
            versionId = key.computeHash(),
            originalTimestamp = key.oTime,
            countryCode = data.countryCode,
            entityId = processedInput.entityId,
        )
    }

    private fun calculateAmount(data: ContractPayroll, lineItemType: LineItemType): Double {
        return when (lineItemType) {
            LineItemType.PAYROLL_OFFCYCLE_EXPENSE -> data.totalExpenseAmount ?: 0.0
            LineItemType.PAYROLL_OFFCYCLE_COST -> (data.amountTotalCost) - (data.totalExpenseAmount ?: 0.0)
            else -> throw IllegalArgumentException("Unsupported line item type: $lineItemType")
        }
    }
}
