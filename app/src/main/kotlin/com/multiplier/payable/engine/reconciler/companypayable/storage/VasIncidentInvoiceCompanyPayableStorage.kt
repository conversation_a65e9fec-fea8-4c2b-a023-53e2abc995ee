package com.multiplier.payable.engine.reconciler.companypayable.storage

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.payable.engine.fx.FxConverter
import com.multiplier.payable.engine.reconciler.companypayable.builder.CompanyPayableEntityBuilder
import com.multiplier.payable.engine.reconciler.companypayable.collector.CompanyPayableEntityDataCollector
import com.multiplier.payable.types.PayableStatus
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Qualifier("vas-incident-invoice")
class VasIncidentInvoiceCompanyPayableStorage(
    private val entityBuilder: CompanyPayableEntityBuilder,
    private val entityDataCollector: CompanyPayableEntityDataCollector,
    private val payableRepository: JpaCompanyPayableRepository,
    private val fxConverter: FxConverter,
) : CompanyPayableStorage {
    @Transactional
    override fun exchangeAndStore(context: CompanyPayableStorageContext): Long {
        val exchangedContext =
            context.copy(
                items = fxConverter.convertWithOnDemandToppedUpRate(context.items),
            )
        val entityBuilderContext = entityDataCollector.collect(exchangedContext)
        val entity = entityBuilder.build(entityBuilderContext)

        val command = context.command!!
        val preference = command.preference
        entity.status = if (preference?.isAuthorised == true) PayableStatus.AUTHORIZED else PayableStatus.DRAFT
        entity.date = preference?.createdDate ?: context.invoiceDate

        val savedCompanyPayable = payableRepository.save(entity)

        return savedCompanyPayable.id
    }
}

