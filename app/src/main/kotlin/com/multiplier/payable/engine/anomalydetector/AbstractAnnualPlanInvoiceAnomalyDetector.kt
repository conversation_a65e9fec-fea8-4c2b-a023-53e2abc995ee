package com.multiplier.payable.engine.anomalydetector

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.payable.engine.anomalydetector.provider.AnnualManagementFeeProvider
import com.multiplier.payable.engine.anomalydetector.request.AnnualManagementFeeData
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging

abstract class AbstractAnnualPlanInvoiceAnomalyDetector(
    private val annualManagementFeeProvider: AnnualManagementFeeProvider,
    private val featureFlagService: FeatureFlagService,
) : AnomalyDetector {
    companion object {
        private val log = KotlinLogging.logger {}
    }

    abstract override val transactionType: TransactionType

    override fun handle(command: InvoiceCommand) {
        if (!iadEnabledByFeatureFlag()) {
            log.info { "Skipping anomaly detection for command $command as feature flag is disabled" }
            return
        }
        log.info { "Annual Plan Invoice Anomaly detecting with command=$command" }

        val annualFeeData = annualManagementFeeProvider.get(command)

        val overlapResult = AnnualManagementFeeData.findOverlapRanges(annualFeeData)
        if (overlapResult.isEmpty()) {
            log.info { "No anomaly detected for command=$command" }
            return
        }
        overlapResult.forEach { (contractId, payableIds) ->
            log.error { "Annual Management Fee for contract ID $contractId has duplicate date range" +
                " during ANNUAL PLAN INVOICE generation in payable IDs $payableIds" }
        }
    }

    private fun iadEnabledByFeatureFlag(): Boolean {
        return featureFlagService
            .feature(GrowthBookFlagConstants.ENABLE_IAD_ANNUAL_FEE_FLAG_NAME, mapOf())
            .on
    }
}
