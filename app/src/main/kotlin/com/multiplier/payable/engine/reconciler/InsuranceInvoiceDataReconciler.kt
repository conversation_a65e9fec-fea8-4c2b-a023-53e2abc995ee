package com.multiplier.payable.engine.reconciler

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorage
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorageContext
import com.multiplier.payable.engine.reconciler.data.invoice.InvoiceDataProviderFactory
import com.multiplier.payable.engine.reconciler.data.item.ItemStoreDataProviderFactory
import com.multiplier.payable.engine.reconciler.diff.reconciler.InvoiceDiffReconcilerFactory
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class InsuranceInvoiceDataReconciler(
    private val templateProvider: TransactionTemplateProvider,
    private val invoiceDataProviderFactory: InvoiceDataProviderFactory,
    private val itemStoreDataProviderFactory: ItemStoreDataProviderFactory,
    private val invoiceDiffReconcilerFactory: InvoiceDiffReconcilerFactory,
    private val companyPayableStorage: CompanyPayableStorage,
) : DataReconciler {
    private val log = LoggerFactory.getLogger(this::class.java)
    override val transactionType: TransactionType
        get() = TransactionType.INSURANCE_INVOICE

    override fun handle(command: InvoiceCommand) {
        log.info("Process getItems for input: $command")
        val template = templateProvider.findTemplateFor(command.transactionType, command.companyId)
        val invoicedItems = invoiceDataProviderFactory.get(transactionType)
            .fetchAndAggregateInvoiceItems(command)
        val latestItems = itemStoreDataProviderFactory.get(transactionType)
            .fetchLatest(command, template.lineItemTypes)
        val payableItems = invoiceDiffReconcilerFactory.get(transactionType)
            .reconcileAndValidate(command, template, invoicedItems, latestItems)

        val storageContext = CompanyPayableStorageContext(
            transactionId = command.transactionId,
            invoiceDate = command.transactionDate,
            monthYear = command.getMonthYear(),
            items = payableItems,
            companyId = command.companyId,
            cycle = command.cycle,
            cycleDuration = command.dateRange,
            transactionType = command.transactionType,
            skipIsrGeneration = template.skipIsrGeneration,
        )
        companyPayableStorage.exchangeAndStore(storageContext)
        log.info(
            "Complete storing ${payableItems.size} payable items for" +
                    " companyId: ${command.companyId}, transactionId: ${command.transactionId}"
        )
    }
}