package com.multiplier.payable.engine.transaction.template.v2.companytransactiontemplate.application

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.template.v2.companytransactiontemplate.domain.CompanyTransactionTemplateRepository
import org.jboss.logging.Logger
import org.springframework.stereotype.Component

@Component
class CompanyTransactionTemplateReadUseCase(
    private val companyTransactionTemplateRepository: CompanyTransactionTemplateRepository,
    private val companyTransactionTemplateDtoMapper: CompanyTransactionTemplateDtoMapper,
) {

    private companion object {
        private val logger = Logger.getLogger(this::class.java)
    }

    fun read(companyId: Long, transactionType: TransactionType): CompanyTransactionTemplateDto? {
        logger.info("Reading company transaction template for companyId = $companyId and transactionType = $transactionType")
        val companyTransactionTemplate =
            companyTransactionTemplateRepository.findByCompanyIdAndTransactionType(companyId, transactionType)

        if (companyTransactionTemplate != null) {
            logger.info("Company Specific template present for $companyId and $transactionType : ${companyTransactionTemplate.id}")
            return companyTransactionTemplateDtoMapper.mapToDto(companyTransactionTemplate)
        }

        logger.info("Company specific template not found for $companyId and $transactionType")
        return null
    }
}