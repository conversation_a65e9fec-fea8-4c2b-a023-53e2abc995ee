package com.multiplier.payable.engine.formatter.template

import com.multiplier.payable.engine.formatter.DataFormatterSortingOrder
import lombok.Builder

@Builder
data class DataFormatterTemplate(
    val name: String,
    val field: String? = null,
    val filter: String? = null,
    val order: Long,
    val supportedItemTypes: List<String>? = null,
    val additionalData: DataFormatterAdditionalDataTemplate? = null,
) {
    data class DataFormatterAdditionalDataTemplate(
        val text: String? = null,
        val sortOrder: DataFormatterSortingOrder? = null,
        val targetLineItemType: String? = null,
    )
}
