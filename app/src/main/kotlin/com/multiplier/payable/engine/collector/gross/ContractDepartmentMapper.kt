package com.multiplier.payable.engine.collector.gross

import com.multiplier.core.util.dto.orgmanagementservice.ContractDepartmentWrapper
import com.multiplier.payable.engine.payableitem.ContractDepartment
import org.springframework.stereotype.Component

@Component
class ContractDepartmentMapper {
    fun map(grpcContractDepartment: ContractDepartmentWrapper): ContractDepartment {
        return ContractDepartment(
            id = grpcContractDepartment.department.id,
            name = grpcContractDepartment.department.name,
        )
    }
}
