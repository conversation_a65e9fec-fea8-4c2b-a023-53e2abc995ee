package com.multiplier.payable.engine.vas.vasmanagementfee

import com.multiplier.payable.engine.vas.Incident
import java.time.LocalDateTime

data class IncidentManagementFeeCalculateInput(
    val companyId: Long,
    val incident: Incident,
    val startDate: LocalDateTime,
    val endDate: LocalDateTime,
    val lineItemCodes: List<String> = emptyList(),
    val excludeBillingIds: List<Long> = emptyList()
)