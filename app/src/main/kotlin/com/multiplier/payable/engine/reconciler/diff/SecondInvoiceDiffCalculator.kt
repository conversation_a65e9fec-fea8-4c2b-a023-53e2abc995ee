package com.multiplier.payable.engine.reconciler.diff

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.adapters.api.LineItemType.getValueAddedTaxLineItems
import com.multiplier.payable.engine.payableitem.PayableItem
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * A [DiffCalculator] for second invoice.
 */
@Component
class SecondInvoiceDiffCalculator(
    private val managementFeeDiffHelper: ManagementFeeDiffHelper,
    private val grossItemDiffHelper: GrossItemDiffHelper,
    private val valueAddedTaxDiffCalculator: ValueAddedTaxDiffCalculator,
) : DiffCalculator<List<PayableItem>> {
    companion object {
        val log: Logger = LoggerFactory.getLogger(this::class.java.name)
    }

    override fun calculate(
        secondInvoiceItems: List<PayableItem>,
        firstInvoiceItems: List<PayableItem>,
    ): Diff<List<PayableItem>> {
        val combinedItems = secondInvoiceItems + firstInvoiceItems
        val newManagementFees = managementFeeDiffHelper.getNew(combinedItems)
        val refundedManagementFees = managementFeeDiffHelper.getRefunds(combinedItems)
        val adjustedManagementFees = managementFeeDiffHelper.getAdjustments(combinedItems)
        val zeroAdjustmentManagementFees =
            managementFeeDiffHelper.getAdjustmentManagementFeeWithZeroAmountDifference(combinedItems)
        val grossItems = grossItemDiffHelper.getGrossItems(combinedItems)
        val bonusItems = grossItemDiffHelper.getBonusItems(combinedItems)
        val paySupplementItemsBilled = grossItemDiffHelper.getPaySupplementItems(combinedItems)
        val vatItems = valueAddedTaxDiffCalculator.calculate(
            newVATPayableItems = secondInvoiceItems
                .filter { getValueAddedTaxLineItems().contains(it.lineItemType) },
            invoicedVATPayableItems = firstInvoiceItems
                .filter { getValueAddedTaxLineItems().contains(it.lineItemType) },
            zeroAmountAdjustmentManagementFees = zeroAdjustmentManagementFees
        )

        val itemsToDelete = refundedManagementFees + grossItems + bonusItems + paySupplementItemsBilled
        val negativeItems = getNegativeItems(combinedItems)

        val itemsToCreate =
            getItemsToCreate(
                combinedItems = combinedItems,
                itemsToAddOnList = newManagementFees + vatItems,
                negativeItems = negativeItems
            )

        return Diff(itemsToDelete, adjustedManagementFees, itemsToCreate)
    }

    private fun getNegativeItems(
        combinedItems: List<PayableItem>
    ): List<PayableItem> =
        combinedItems.filter { it.lineItemType == LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL_REFUND.name }

    private fun getItemsToCreate(
        combinedItems: List<PayableItem>,
        itemsToAddOnList: List<PayableItem>,
        negativeItems: List<PayableItem>
    ): List<PayableItem> =
        combinedItems
            .asSequence()
            .filterNot {
                it.lineItemType == LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name
                        || getValueAddedTaxLineItems().contains(it.lineItemType)
            }
            .filterNot { it.isBilled!! } // we don't want to create new items for billed items
            .plus(itemsToAddOnList)
            .filter { it.amountInBaseCurrency > 0.0 }
            .plus(negativeItems)
            .toList()
}
