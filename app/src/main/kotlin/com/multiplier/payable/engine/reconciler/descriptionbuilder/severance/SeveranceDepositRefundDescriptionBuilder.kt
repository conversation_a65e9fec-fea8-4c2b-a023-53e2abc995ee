package com.multiplier.payable.engine.reconciler.descriptionbuilder.severance

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.AmountFormatter
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilder
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import org.springframework.stereotype.Service
import kotlin.math.absoluteValue

@Service
class SeveranceDepositRefundDescriptionBuilder(
    private val amountFormatter: AmountFormatter,
) : PayableItemDescriptionBuilder {
    override val lineItemType = LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL_REFUND

    override fun build(context: PayableItemDescriptionBuilderContext): String {
        val formattedAmount = amountFormatter.format(context.amountInBaseCurrency.absoluteValue)
        return """
            Severance accrual adjustment: - ${context.currencyCode} $formattedAmount
        """.trimIndent()
    }
}