package com.multiplier.payable.engine.anomalydetector.rules.processor

import com.multiplier.core.anomalydetector.AnomalyReportMapper
import com.multiplier.core.anomalydetector.model.AnomalyResultType
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyResult
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.request.IADRequestBuilder
import com.multiplier.payable.engine.anomalydetector.rules.DetectionRuleType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class AnomalyDetectionRulesProcessor(
    private val anomalyDetectionRules: List<AnomalyDetectionRule>,
    private val defaultIADRequestBuilder: IADRe<PERSON><PERSON>uilder,
    private val jpaAnomalyReportRepository: JpaAnomalyReportRepository,
) {
    companion object {
        private val log = KotlinLogging.logger {}
    }

    fun detect(
        command: InvoiceCommand,
        ruleTypes: List<DetectionRuleType>,
    ): Boolean {
        val filteredRules = anomalyDetectionRules.filter { it.type in ruleTypes }

        try {
            val fetchedDataList = defaultIADRequestBuilder.build(command)

            return fetchedDataList.all { fetchedData ->
                // Collect all rule results for this request
                val ruleResults = mutableListOf<Pair<AnomalyDetectionResult, AnomalyDetectionRule>>()

                filteredRules.forEach { rule ->
                    try {
                        val result = rule.detect(command, fetchedData)
                        ruleResults.add(result to rule)
                    } catch (e: Exception) {
                        log.error(
                            "Rule ${rule.type} failed - " +
                                "companyId: ${command.companyId}, " +
                                "transactionId: ${command.transactionId}, " +
                                "transactionType: ${command.transactionType}", e
                        )
                        // Add failure result so this rule counts as failed but others continue
                        val failureResult = AnomalyDetectionResult(
                            success = false,
                            messages = listOf("Rule execution failed: ${e.message}"),
                            severity = AnomalyResultType.ERROR
                        )
                        ruleResults.add(failureResult to rule)
                    }
                }

                // Save consolidated anomaly report for this request
                saveConsolidatedAnomalyReport(ruleResults, fetchedData)

                // Consider WARNING as non-blocking (success = true), only ERROR blocks processing
                ruleResults.all { (result, _) -> result.success || result.severity == AnomalyResultType.WARN }
            }
        } catch (e: Exception) {
            log.error(
                "Failed to build IAD requests - " +
                    "companyId: ${command.companyId}, " +
                    "transactionId: ${command.transactionId}, " +
                    "transactionType: ${command.transactionType}", e
            )
            return false
        }
    }

    /**
     * Saves consolidated anomaly report for all rule results.
     * This creates a single report with multiple results, matching the behavior of the old InvoiceAnomalyDetectorService.
     */
    private fun saveConsolidatedAnomalyReport(
        ruleResults: List<Pair<AnomalyDetectionResult, AnomalyDetectionRule>>,
        request: InvoiceAnomalyDetectorRequest,
    ) {
        // Only save if we have valid request data
        if (request.payable == null) {
            log.warn { "Cannot save anomaly report - payable is null" }
            return
        }

        // Convert all rule results to InvoiceAnomalyResult, excluding WARN results per existing logic
        val invoiceAnomalyResults = ruleResults
            .filter { (result, _) -> result.severity != AnomalyResultType.WARN }
            .mapNotNull { (result, rule) ->
                try {
                    convertToInvoiceAnomalyResult(result, rule, request)
                } catch (e: Exception) {
                    log.error(e) { "Failed to convert rule result to InvoiceAnomalyResult for rule ${rule.type}" }
                    null
                }
            }

        // Only save if we have results to save
        if (invoiceAnomalyResults.isNotEmpty()) {
            try {
                val report = AnomalyReportMapper.map(invoiceAnomalyResults, request.payable.id)
                log.info { "Saving consolidated anomaly detection report for companyPayableId=${request.payable.id} with ${invoiceAnomalyResults.size} results" }
                jpaAnomalyReportRepository.save(report)
            } catch (e: Exception) {
                log.error(e) { "Failed to save consolidated anomaly report for companyPayableId=${request.payable.id}" }
            }
        } else {
            log.debug { "No anomaly results to save for companyPayableId=${request.payable.id} (all results were WARN or filtered out)" }
        }
    }

    /**
     * Converts AnomalyDetectionResult to InvoiceAnomalyResult.
     * Moved from BaseAnomalyDetectionRule for centralized conversion logic.
     */
    private fun convertToInvoiceAnomalyResult(
        result: AnomalyDetectionResult,
        rule: AnomalyDetectionRule,
        request: InvoiceAnomalyDetectorRequest,
    ): InvoiceAnomalyResult =
        InvoiceAnomalyResult.builder()
            .payable(request.payable)
            .type(getDetectorTypeForRule(rule))
            .result(result.severity)
            .anomalyResultMessage(result.messages)
            .build()

    /**
     * Gets the detector type for a rule. This is needed for rules that don't extend BaseAnomalyDetectionRule.
     */
    private fun getDetectorTypeForRule(rule: AnomalyDetectionRule): com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType {
        return if (rule is BaseAnomalyDetectionRule) {
            rule.detectorType
        } else {
            // Fallback mapping for rules that don't extend BaseAnomalyDetectionRule
            // This should be rare, but we handle it gracefully
            com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType.DEFAULT_RULE
        }
    }
}
