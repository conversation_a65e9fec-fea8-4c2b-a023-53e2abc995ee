package com.multiplier.payable.engine.inspector

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.JpaTransactionCommandLogRepository
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.inspector.dto.FinancialTransactionInspectorResponse
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.mapper.CompanyPayableMapper
import org.springframework.stereotype.Component

@Component
class DefaultFinancialTransactionInspector(
    val jpaCompanyPayableRepository: JpaCompanyPayableRepository,
    private val transactionCommandRepository: JpaTransactionCommandLogRepository,
    private val companyPayableMapper: CompanyPayableMapper,
) : FinancialTransactionInspector {

    private val objectMapper = ObjectMapper().apply {
        registerModule(JavaTimeModule())
        registerKotlinModule()
    }

    override fun supportedTypes(): Set<TransactionType> = TransactionType.entries
        .filter { it != TransactionType.VENDOR_BILL }
        .toSet()

    override fun inspect(transactionId: String): FinancialTransactionInspectorResponse {
        val payables = jpaCompanyPayableRepository.findByTransactionId(transactionId)
        val transactions = payables.map { companyPayableMapper.toFinancialTransaction(it) }
        val commandLogs = transactionCommandRepository.findByTransactionId(transactionId)
        val sortedCommandLogs = commandLogs.sortedBy { it.updatedOn } // by default ascending order
        val serializedCommands = sortedCommandLogs.map { it.command }
        val commands = serializedCommands.map { objectMapper.readValue(it, InvoiceCommand::class.java) }
        return FinancialTransactionInspectorResponse(
            commands = commands,
            transactions = transactions
        )
    }
}