package com.multiplier.payable.engine.collector.deposit

import com.multiplier.core.payable.adapters.DepositCalculationBreakdown
import com.multiplier.payable.engine.contract.Contract
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.deposit.Deposit
import com.multiplier.payable.engine.deposit.DepositBatch
import com.multiplier.payable.engine.deposit.DepositType
import org.springframework.stereotype.Service

@Service
class GrpcDepositToEngineDepositMapper {
    fun map(
        depositCalculationBreakdown: DepositCalculationBreakdown,
        contract: Contract,
    ): DepositBatch? {
        if (depositCalculationBreakdown.total == 0.0) {
            return null
        }
        val deposits =
            DepositType.entries.filterNot { getAmountByType(depositCalculationBreakdown, it) == null }
                .map {
                    Deposit(
                        type = it,
                        amount = getAmountByType(depositCalculationBreakdown, it)!!,
                        currencyCode = contract.currencyCode,
                        contractType = ContractType.valueOf(contract.type.name),
                        additionalLeavesInDays = depositCalculationBreakdown.additionalLeavesInDays ?: 0.0,
                        noticePeriod = depositCalculationBreakdown.numberOfMonths ?: 0.0,
                    )
                }.toList()
        return DepositBatch(
            total = depositCalculationBreakdown.total,
            contract = contract,
            deposits = deposits,
            calculatedTime = System.currentTimeMillis(),
        )
    }

    private fun getAmountByType(
        depositCalculationBreakdown: DepositCalculationBreakdown,
        type: DepositType,
    ): Double? {
        return when (type) {
            DepositType.SALARY -> depositCalculationBreakdown.salaryCost
            DepositType.LEAVE -> depositCalculationBreakdown.additionalLeaveCost
            DepositType.FIXED_PAY -> depositCalculationBreakdown.fixedAdditionalPayCost
        }
    }
}
