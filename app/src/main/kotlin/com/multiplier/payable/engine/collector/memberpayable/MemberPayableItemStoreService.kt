package com.multiplier.payable.engine.collector.memberpayable

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.util.toLocalDateInUTC
import com.multiplier.payable.engine.domain.aggregates.MonthYearDuration
import com.multiplier.payable.engine.memberpayable.MemberPayable
import com.multiplier.payable.engine.memberpayable.managmentfee.MemberPayableManagementFeeBill
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.types.Amount
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class MemberPayableItemStoreService(
    private val payableItemStoreService: PayableItemStoreService,
    private val objectMapper: ObjectMapper
) {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    fun normalizeAndSaveBills(
        transactionId: String,
        memberPayableBillingMap: Map<MemberPayable, List<MemberPayableManagementFeeBill>>,
        monthYearDuration: MonthYearDuration,
        type: LineItemType,
    ) {

        logger.info {
            "Start normalization and saving for transactionId: $transactionId"
        }

        val monthYear = monthYearDuration.from.invoiceMonthYear()

        val memberPayableEntryGroupByKey = memberPayableBillingMap.entries.groupBy {
            MemberPayableKey(
                companyId = it.key.companyId,
                lineItemType = type,
                oTime = it.key.fetchedTime.toEpochMilli()
            )
        }

        memberPayableEntryGroupByKey.forEach{ (key, memberPayableBillingEntries) ->
            logger.info(
                "Normalizing and saving data for " +
                        "transactionId = $transactionId " +
                        "itemType = $type " +
                        "companyId = ${key.companyId}" +
                        "month and year = $monthYear"
            )

            val payableItemStoreDtos = memberPayableBillingEntries.map {
                val memberPayable = it.key
                it.value.map {memberPayableManagementFeeBill ->
                    PayableItemStoreDto(
                        amount = memberPayableManagementFeeBill.amount.amount,
                        currency = memberPayableManagementFeeBill.amount.currency,
                        companyId = memberPayable.companyId,
                        contractId = memberPayable.contract.id,
                        countryCode = memberPayable.countryCode.toString(),
                        transactionId = transactionId,
                        month = monthYear.month,
                        year = monthYear.year,
                        itemType = type,
                        periodStartDate = memberPayableManagementFeeBill.startDate.toLocalDateInUTC(),
                        periodEndDate = memberPayableManagementFeeBill.endDate.toLocalDateInUTC(),
                        versionId = key.computeHash(),
                        originalTimestamp = key.oTime,
                        itemData = convertJson(it.key, memberPayableManagementFeeBill.amount),
                    ).also {
                        logger.info { "payable item store dto object = $it" }
                    }
                }
            }

            try {
                payableItemStoreService.saveAndIgnoreDuplication(payableItemStoreDtos.flatten())
            } catch (e: Exception) {
                logger.error(e) { "Error collect data for key = $key, transactionId = $transactionId" }
                throw e
            }

        }

    }

    fun normalizeAndSave(
        transactionId: String,
        memberPayableAmountMap: Map<MemberPayable, Amount>,
        monthYearDuration: MonthYearDuration,
        type: LineItemType,
    ) {
        logger.info {
            "Start normalization and saving for transactionId: $transactionId"
        }

        val monthYear = monthYearDuration.from.invoiceMonthYear()

        val memberPayableEntryGroupByKey = memberPayableAmountMap.entries.groupBy {
            MemberPayableKey(
                companyId = it.key.companyId,
                lineItemType = type,
                oTime = it.key.fetchedTime.toEpochMilli()
            )
        }

        memberPayableEntryGroupByKey.forEach { (key, memberPayableEntries) ->
            logger.info(
                "Normalizing and saving data for " +
                        "transactionId = $transactionId " +
                        "itemType = $type " +
                        "companyId = ${key.companyId}" +
                        "month and year = $monthYear"
            )

            val payableItemStoreDtos = memberPayableEntries.map {
                val memberPayable = it.key
                val amount = it.value
                PayableItemStoreDto(
                    amount = amount.amount,
                    currency = amount.currency,
                    companyId = memberPayable.companyId,
                    contractId = memberPayable.contract.id,
                    countryCode = memberPayable.countryCode.toString(),
                    transactionId = transactionId,
                    month = monthYear.month,
                    year = monthYear.year,
                    itemType = type,
                    periodStartDate = memberPayable.getStartDate(),
                    periodEndDate = memberPayable.getEndDate(),
                    versionId = key.computeHash(),
                    originalTimestamp = key.oTime,
                    itemData = convertJson(it.key, it.value),
                )
            }

            try {
                payableItemStoreService.saveAndIgnoreDuplication(payableItemStoreDtos)
            } catch (e: Exception) {
                logger.error(e) { "Error collect data for key = $key, transactionId = $transactionId" }
                throw e
            }
        }
    }

    private fun convertJson(
        memberPayable: MemberPayable,
        amount: Amount
    ): String {
        logger.info { "Creating json for memberPayableId = ${memberPayable.id}" }
        return objectMapper.writeValueAsString(
            mapOf(
                "memberPayable" to memberPayable,
                "amount" to amount
            )
        )
    }
}