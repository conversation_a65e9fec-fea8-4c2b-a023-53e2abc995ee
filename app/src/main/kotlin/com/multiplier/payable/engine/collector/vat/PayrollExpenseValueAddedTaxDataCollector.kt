package com.multiplier.payable.engine.collector.vat

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.DefaultProcessedCollectorInput
import com.multiplier.payable.engine.collector.payroll.ContractPayroll
import com.multiplier.payable.engine.collector.payroll.ContractPayrollService
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.vat.adapter.ValueAddedTaxCodeAdapter
import org.springframework.stereotype.Component

@Component
class PayrollExpenseValueAddedTaxDataCollector(
    dataCollectorInputProcessor: DataCollectorInputProcessor<DefaultProcessedCollectorInput>,
    contractPayrollService: ContractPayrollService,
    valueAddedTaxCodeAdapter: ValueAddedTaxCodeAdapter,
    objectMapper: ObjectMapper,
    payableItemStoreService: PayableItemStoreService,
): PayrollValueAddedTaxDataCollector(
    dataCollectorInputProcessor,
    contractPayrollService,
    valueAddedTaxCodeAdapter,
    objectMapper,
    payableItemStoreService
) {

    override fun getSupportedType(): LineItemType = LineItemType.VAT_PAYROLL_EXPENSE

    override fun getPayrollCostAmount(contractPayroll: ContractPayroll): Double {
        return contractPayroll.totalExpenseAmount ?: 0.0
    }

}