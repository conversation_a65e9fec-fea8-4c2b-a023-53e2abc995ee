package com.multiplier.payable.engine.reconciler.descriptionbuilder.adjustment.gp

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilder
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import org.springframework.stereotype.Component

@Component
class YearEndDocumentationAdjustmentDescriptionBuilder : PayableItemDescriptionBuilder {

    override val lineItemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_YEAR_END_DOCUMENTATION

    override fun build(context: PayableItemDescriptionBuilderContext): String {
        return """
            Year-end Documentation Fee Advance Adjustment (for ${context.noOfMembers} Members) ${context.countryCode}: ${context.currencyCode} ${context.amountInBaseCurrency}
        """.trimIndent()
    }
}
