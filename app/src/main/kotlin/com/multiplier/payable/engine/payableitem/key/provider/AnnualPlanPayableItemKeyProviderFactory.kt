package com.multiplier.payable.engine.payableitem.key.provider

import com.multiplier.payable.engine.domain.entities.TransactionType
import org.springframework.stereotype.Component

@Component
class AnnualPlanPayableItemKeyProviderFactory(
    private val annualPlanPayableItemKeyProvider: AnnualPlanPayableItemKeyProvider,
    private val annualPlanAorPayableItemKeyProvider: AnnualPlanAorPayableItemKeyProvider,
) {
    fun get(transactionType: TransactionType): PayableItemKeyProvider {
        return when (transactionType) {
            TransactionType.ANNUAL_PLAN_INVOICE -> annualPlanPayableItemKeyProvider
            TransactionType.ANNUAL_PLAN_AOR_INVOICE -> annualPlanAorPayableItemKeyProvider
            else -> throw IllegalArgumentException("No key provider found for transaction type: $transactionType")
        }
    }
}