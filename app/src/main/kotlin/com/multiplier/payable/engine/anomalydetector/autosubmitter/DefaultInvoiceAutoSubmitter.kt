package com.multiplier.payable.engine.anomalydetector.autosubmitter

import com.multiplier.core.payable.adapters.netsuite.exception.NetsuiteAdapterException
import com.multiplier.core.payable.invoice.api.InvoiceAdapter
import com.multiplier.core.payable.invoice.api.InvoiceStatus
import com.multiplier.core.payable.invoice.api.UpdateInvoiceApiRequest
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class DefaultInvoiceAutoSubmitter(
    private val invoiceAdapter: InvoiceAdapter,
) : InvoiceAutoSubmitter {

    companion object {
        private val log = KotlinLogging.logger {}
    }

    override fun submit(command: InvoiceCommand, invoiceExternalId: String) {
        try {
            val updateRequest = UpdateInvoiceApiRequest.builder()
                .status(InvoiceStatus.AUTHORIZED)
                .sendEmail(true)
                .build()
            invoiceAdapter.update(invoiceExternalId, updateRequest)
        } catch (e: NetsuiteAdapterException) {
            log.warn { "Invoice external id: $invoiceExternalId failed to auto-submit with email and status AUTHORISED; Status DRAFT will remain." }
            command.appendWarning("Invoice external id: $invoiceExternalId failed to auto-submit with email and status AUTHORISED; Status DRAFT will remain.")
        }
    }
}