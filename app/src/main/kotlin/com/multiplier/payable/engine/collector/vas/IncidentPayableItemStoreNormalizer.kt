package com.multiplier.payable.engine.collector.vas

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.payable.engine.collector.PayableItemStoreNormalizer
import com.multiplier.payable.engine.collector.ProcessedCollectorInput
import com.multiplier.payable.engine.collector.data.ProcessedIncidentCollectorInput
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.engine.vas.HasContractId
import com.multiplier.payable.engine.vas.Incident
import com.multiplier.payable.engine.vas.IncidentAmountChargePolicy
import org.springframework.stereotype.Component

@Component
class IncidentPayableItemStoreNormalizer(
    private val objectMapper: ObjectMapper,
) : PayableItemStoreNormalizer<Incident> {
    override fun normalize(
        data: Incident,
        processedInput: ProcessedCollectorInput,
    ): PayableItemStoreDto {
        processedInput as ProcessedIncidentCollectorInput

        val oTime = processedInput.originalTimestamp.toEpochMilli()
        val key = IncidentKey(processedInput.lineItemType, processedInput.companyId, oTime)

        // Get contractId using the IncidentNormalizer
        val contractId = extractContractId(data)

        // Fallback to incident's amount when chargePolicyRate is null for backward compatibility
        val amountChargePolicyRate =
            data.chargePolicy?.let {
                require(it is IncidentAmountChargePolicy) {
                    "Unsupported charge policy type: ${it.javaClass.name}"
                }
                it.amount
            } ?: data.amount

        return PayableItemStoreDto(
            amount = amountChargePolicyRate.value.toDouble(),
            currency = amountChargePolicyRate.currency,
            companyId = processedInput.companyId,
            contractId = contractId,
            transactionId = processedInput.transactionId,
            month = data.incidentTime.monthValue,
            year = data.incidentTime.year,
            itemData = objectMapper.writeValueAsString(data),
            itemType = processedInput.lineItemType,
            periodStartDate = data.incidentTime.toLocalDate(),
            periodEndDate = data.incidentTime.toLocalDate(),
            versionId = key.computeHash(),
            originalTimestamp = key.oTime,
            countryCode = data.countryCode.toString(),
        )
    }

    fun extractContractId(incident: Incident): Long? =
        if (incident is HasContractId) {
            incident.contractId
        } else {
            -1
        }
}
