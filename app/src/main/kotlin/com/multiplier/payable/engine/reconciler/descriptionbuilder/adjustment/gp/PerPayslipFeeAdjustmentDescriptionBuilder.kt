package com.multiplier.payable.engine.reconciler.descriptionbuilder.adjustment.gp

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilder
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import org.springframework.stereotype.Component

@Component
class PerPayslipFeeAdjustmentDescriptionBuilder : PayableItemDescriptionBuilder {

    override val lineItemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PER_PAYSLIP_FEE

    override fun build(context: PayableItemDescriptionBuilderContext): String {
        val payslipsCount = context.payslipCount ?: "N/A"
        return """
            Payslip Fee Advance Adjustment (for $payslipsCount payslips) ${context.countryCode ?: "N/A"}: ${context.currencyCode} ${context.amountInBaseCurrency}
        """.trimIndent()
    }
}
