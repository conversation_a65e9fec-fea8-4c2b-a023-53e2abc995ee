package com.multiplier.payable.engine.offcycle

import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.types.CountryCode

data class OffCycleManagementFee(
    val contractId: Long,
    val amount: Amount,
    val originalFee: Double,
    val appliedDiscount: String?,
    val countryCode: CountryCode?,
    val companyId: Long,
    val month: Int,
    val year: Int,
    val payrollCycleId: Long?,
)
