package com.multiplier.payable.engine.transaction.handler

import com.multiplier.core.payable.pricing.adapter.PricingServiceAdapter
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.generator.FinancialTransactionGenerator
import com.multiplier.payable.engine.transaction.version.calculator.FirstInvoiceTransactionCountCalculator
import org.springframework.stereotype.Service

@Service
class FirstInvoiceHandler(
    private val invoiceTransactionBuilder: InvoiceTransactionBuilder,
    private val pricingServiceAdapter: PricingServiceAdapter,
    private val firstInvoiceTransactionCountCalculator: FirstInvoiceTransactionCountCalculator,
    private val invoiceGenerator: FinancialTransactionGenerator,
) : FinancialTransactionHandler {

    override val transactionType: TransactionType
        get() = TransactionType.FIRST_INVOICE

    override fun handle(command: InvoiceCommand) {
        val pricing = pricingServiceAdapter.getCompanyPricing(command.companyId)
        val dueDate = command.transactionDate.plusDays(pricing.paymentTermInDays?.toLong() ?: 7L)
        val currentTransactionCount = firstInvoiceTransactionCountCalculator.getCount(command)
        val enrichedCommand = command.copy(
            billingCurrencyCode = CurrencyCode.valueOf(pricing.billingCurrencyCode.name),
            dueDate = dueDate,
            transactionCount = currentTransactionCount,
        )
        val invoicesToGenerate = invoiceTransactionBuilder.build(enrichedCommand)
        invoicesToGenerate.forEach(invoiceGenerator::generate)
    }
}