package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.payable.engine.anomalydetector.GrowthBookFlagConstants
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.provider.AnnualManagementFeeProvider
import com.multiplier.payable.engine.anomalydetector.request.AnnualManagementFeeData
import com.multiplier.payable.engine.anomalydetector.rules.processor.AnomalyDetectionRule
import com.multiplier.payable.engine.anomalydetector.rules.processor.BaseAnomalyDetectionRule
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component

@Component
class AnnualPlanDateRangeDuplicateRule(
    private val transactionTemplateProvider: TransactionTemplateProvider,
    @Qualifier("annualManagementFeeEorProvider")
    private val annualManagementFeeProvider: AnnualManagementFeeProvider,
    featureFlagService: FeatureFlagService,
    jpaAnomalyReportRepository: JpaAnomalyReportRepository,
) : BaseAnomalyDetectionRule(featureFlagService, jpaAnomalyReportRepository) {
    override val ruleName: String = "AnnualPlanDateRangeDuplicateRule"
    override val featureFlagName: String = GrowthBookFlagConstants.ENABLE_IAD_ANNUAL_FEE_FLAG_NAME
    override val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.ANNUAL_PLAN_DUPLICATE_DATE_RANGE
    companion object {
        private val log = KotlinLogging.logger {}
    }

    override val type: DetectionRuleType = DetectionRuleType.ANNUAL_PLAN_DUPLICATE_DATE_RANGE

    override fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult =
        try {
            if (!mergedWithAnnualPlanTemplate(command)) {
                log.info { "Annual Plan is not merged to Second Invoice for company ${command.companyId}" }
                createSuccessResult(listOf("No annual plan template merging detected"))
            } else {
                log.info { "Second Invoice Anomaly detecting with command=$command" }

                val annualFeeData = annualManagementFeeProvider.get(command)
                log.info {
                    "Found annual fee data for command=$command from " +
                        "payable ids=${annualFeeData.joinToString { it.payableId.toString() }}"
                }

                // Detect anomalies
                val overlapResult = AnnualManagementFeeData.findOverlapRanges(annualFeeData)

                if (overlapResult.isEmpty()) {
                    log.info { "No anomaly detected for command=$command" }
                    createSuccessResult(listOf("No overlapping date ranges detected"))
                } else {
                    val messages =
                        overlapResult.map { (contractId, payableIds) ->
                            val message =
                                "IAD for Second Invoice AP line failed. Annual Management Fee for " +
                                    "contract ID $contractId has duplicate date range during " +
                                    "SECOND INVOICE generation in payable IDs $payableIds"
                            log.error { message }
                            message
                        }
                    createFailureResult(messages)
                }
            }
        } catch (e: Exception) {
            log.error(e) { "IAD for Second Invoice AP line failed for $command" }
            createFailureResult(listOf("Exception occurred during anomaly detection: ${e.localizedMessage}"))
        }

    private fun mergedWithAnnualPlanTemplate(command: InvoiceCommand): Boolean {
        val mainTemplate = transactionTemplateProvider.findTemplateFor(command.transactionType, command.companyId)
        val mergedTransactionTypes = mainTemplate.mergedTransactionTypes
        return mergedTransactionTypes.any { it == TransactionType.ANNUAL_PLAN_INVOICE }
    }
}
