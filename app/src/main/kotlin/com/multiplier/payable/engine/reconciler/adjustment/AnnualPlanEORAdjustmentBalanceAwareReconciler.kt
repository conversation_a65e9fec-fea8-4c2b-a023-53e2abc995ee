package com.multiplier.payable.engine.reconciler.adjustment

import com.multiplier.common.exception.toBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.adapters.pricing.ReferenceTargetType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.ledger.AdvanceCollectionLedger
import com.multiplier.payable.ledger.domain.AdvanceCollectionProduct
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component

@Component
@Qualifier("eor")
class AnnualPlanEORAdjustmentBalanceAwareReconciler(
    ledger: AdvanceCollectionLedger,
) : AbstractAnnualPlanAdjustmentBalanceAwareReconciler(ledger) {
    override fun getSupportedLineCode(): String {
        return "SERVICE_FEE"
    }

    override fun supportedAdjustmentItemType(): LineItemType {
        return LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR_SERVICE_FEE
    }

    override fun buildAdvanceCollectionProduct(payableItem: PayableItem): AdvanceCollectionProduct {
        return AdvanceCollectionProduct(
            lineCode = getSupportedLineCode(),
            dimensions = mapOf(
                "OFFERING" to "EOR",
                "COUNTRY" to requireNotNull(payableItem.countryCode),
                "VISA_STATUS" to requireNotNull(payableItem.countryWorkStatus?.name) {
                    throw PayableErrorCode.INVALID_COUNTRY_WORK_STATUS.toBusinessException("Country work status cannot be null for EOR")
                }
            ),
            targetType = ReferenceTargetType.SUBSCRIPTION_PRODUCT.name,
        )
    }
}