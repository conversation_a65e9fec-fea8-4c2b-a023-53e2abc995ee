package com.multiplier.payable.engine.vendorBill.api

import com.netsuite.suitetalk.proxy.v2023_1.platform.common.AccountingBookDetailList
import com.netsuite.suitetalk.proxy.v2023_1.platform.common.Address
import com.netsuite.suitetalk.proxy.v2023_1.platform.common.InstallmentList
import com.netsuite.suitetalk.proxy.v2023_1.platform.common.PurchLandedCostList
import com.netsuite.suitetalk.proxy.v2023_1.platform.common.TaxDetailsList
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.CustomFieldList
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.RecordRef
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.RecordRefList
import com.netsuite.suitetalk.proxy.v2023_1.transactions.purchases.VendorBill
import com.netsuite.suitetalk.proxy.v2023_1.transactions.purchases.VendorBillExpense
import com.netsuite.suitetalk.proxy.v2023_1.transactions.purchases.VendorBillExpenseList
import com.netsuite.suitetalk.proxy.v2023_1.transactions.purchases.VendorBillItemList
import org.springframework.stereotype.Component
import java.util.Calendar
import java.util.GregorianCalendar

@Component
class VendorBillStubs {

    fun createMockVendorBill(): VendorBill {
        val vendorBill = VendorBill()

        // Basic fields
//        vendorBill.externalId = "VB-MOCK-001"
        vendorBill.tranId = "VendorBill-MOCK-Prakhar-001"
//        vendorBill.createdDate = GregorianCalendar(2024, Calendar.FEBRUARY, 10)
        vendorBill.tranDate = GregorianCalendar(2025, Calendar.FEBRUARY, 20)
        vendorBill.dueDate = GregorianCalendar(2024, Calendar.FEBRUARY, 29)
//        vendorBill.memo = "Mock Vendor Bill"
        vendorBill.currencyName = "INR"
        vendorBill.currency = createMockRecordRef("7")
        vendorBill.exchangeRate = 1.0

        // RecordRefs
        vendorBill.entity = createMockRecordRef("25471") // Vendor iD for India Payroll vendor - MTP01031
        vendorBill.account = createMockRecordRef("2933") // Account  -> 201-002 Wages Payable
//        vendorBill.subsidiary = createMockRecordRef("789")
//        vendorBill.postingPeriod = createMockRecordRef("987")
//        vendorBill.terms = createMockRecordRef("654")
//        vendorBill._class = createMockRecordRef("321") // _class is a valid field name in NetSuite
        vendorBill.department = createMockRecordRef("528")
//        vendorBill.location = createMockRecordRef("202")
        vendorBill.location = createMockRecordRef("1")
        // Addresses
        vendorBill.billingAddress = createMockAddress("Mock Company", "123 Main St", "Anytown", "CA", "91234")

        // Numbers
//        vendorBill.userTotal = 1000.0
//        vendorBill.taxTotal = 100.0
//        vendorBill.discountAmount = 0.0

        // Booleans
//        vendorBill.paymentHold = false
//        vendorBill.taxRegOverride = false
//        vendorBill.taxDetailsOverride = false
//        vendorBill.landedCostPerLine = false
//        vendorBill.overrideInstallments = false

        // Lists (initialize as empty)
        vendorBill.expenseList = getExpenseList()
//        vendorBill.accountingBookDetailList = AccountingBookDetailList()
//        vendorBill.itemList = getBilledItems()
//        vendorBill.installmentList = InstallmentList()
//        vendorBill.landedCostsList = PurchLandedCostList()
//        vendorBill.purchaseOrderList = RecordRefList()
//        vendorBill.taxDetailsList = TaxDetailsList()
//        vendorBill.customFieldList = CustomFieldList()

        return vendorBill
    }

    private fun getExpenseList(): VendorBillExpenseList {
        return VendorBillExpenseList(arrayOf(getExpenseItem()), false)
    }

    private fun getExpenseItem(): VendorBillExpense {
        val vendorBillExpense = VendorBillExpense()
        vendorBillExpense.account = createMockRecordRef("2486")
        vendorBillExpense.amount = 100.0
        vendorBillExpense.memo = "Jan'24|535215 |Prakhar |Advance pay"
        return vendorBillExpense
    }
    private fun createMockRecordRef(internalId: String): RecordRef {
        val recordRef = RecordRef()
        recordRef.internalId = internalId
        return recordRef
    }

    private fun createMockAddress(addressee: String, addr1: String, city: String, state: String, zip: String): Address {
        val address = Address()
        address.addressee = addressee
        address.addr1 = addr1
        address.city = city
        address.state = state
        address.zip = zip
        return address
    }
}