package com.multiplier.payable.engine.reconciler.companypayable.storage

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.fx.FxConverter
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.payableitem.key.provider.AnnualPlanPayableItemKeyProviderFactory
import com.multiplier.payable.engine.reconciler.companypayable.builder.CompanyPayableEntityBuilder
import com.multiplier.payable.engine.reconciler.companypayable.collector.CompanyPayableEntityDataCollector
import com.multiplier.payable.util.getSignFrom
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import kotlin.math.absoluteValue

@Service
@Qualifier("annual-plan")
class AnnualPlanCompanyPayableStorage(
    private val entityBuilder: CompanyPayableEntityBuilder,
    private val entityDataCollector: CompanyPayableEntityDataCollector,
    private val payableRepository: JpaCompanyPayableRepository,
    private val fxConverter: FxConverter,
    private val annualPlanPayableItemKeyProvider: AnnualPlanPayableItemKeyProviderFactory,
) : CompanyPayableStorage {
    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    @Transactional
    override fun exchangeAndStore(context: CompanyPayableStorageContext): Long {
        logger.info { "Exchanging and storing annual plan payable items for context=$context" }
        val foreignExchangedItems = fxConverter.convert(context.items.map { payableItem ->
            val absAmount = payableItem.amountInBaseCurrency.absoluteValue
            payableItem.copy(amountInBaseCurrency = absAmount)
        })
        val signAwareExchangedItems = adjustSign(context.transactionType, context.items, foreignExchangedItems)
        val exchangedContext = context.copy(items = signAwareExchangedItems)

        val entityBuilderContext = entityDataCollector.collect(exchangedContext)
        val entity = entityBuilder.build(entityBuilderContext)

        val savedCompanyPayable = payableRepository.save(entity)
        return savedCompanyPayable.id
    }

    private fun adjustSign(
        transactionType: TransactionType,
        originalItems: List<PayableItem>,
        foreignExchangedItems: List<PayableItem>
    ): List<PayableItem> {
        val payableItemKeyProvider = annualPlanPayableItemKeyProvider.get(transactionType)
        val originalItemsByKey = originalItems.associateBy { payableItemKeyProvider.getKey(it) }
        return foreignExchangedItems.map { foreignExchangedItem ->
            val originalItem =
                originalItemsByKey.getValue(payableItemKeyProvider.getKey(foreignExchangedItem))
            foreignExchangedItem.copy(
                amountInBaseCurrency = foreignExchangedItem.amountInBaseCurrency.getSignFrom(originalItem.amountInBaseCurrency),
                billableCost = foreignExchangedItem.billableCost!!.getSignFrom(originalItem.amountInBaseCurrency)
            )
        }
    }
}
