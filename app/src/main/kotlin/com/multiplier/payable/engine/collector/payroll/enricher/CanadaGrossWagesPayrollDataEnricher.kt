package com.multiplier.payable.engine.collector.payroll.enricher

import com.multiplier.core.util.dto.payroll.CompanyPayrollWrapper
import com.multiplier.payable.engine.collector.payroll.PayrollAttributeKey
import com.multiplier.payable.engine.collector.payroll.PayrollAttributeKey.CANADA_GROSS_WAGES
import com.multiplier.payable.engine.collector.payroll.PayrollDataEnricher
import com.multiplier.payable.types.CountryCode
import org.springframework.stereotype.Component

@Component("grossWages")
class CanadaGrossWagesPayrollDataEnricher : PayrollDataEnricher {
    override fun enrich(companyPayrollWrapper: CompanyPayrollWrapper, attributes: List<PayrollAttributeKey>): CompanyPayrollWrapper {
        if (attributes.isEmpty()) {
            return companyPayrollWrapper
        }
        if (!attributes.contains(CANADA_GROSS_WAGES)) {
            return companyPayrollWrapper
        }
        val enrichedMemberPays = companyPayrollWrapper.memberPays
            .map {
                if (CountryCode.valueOf(it.contract.country) != CountryCode.CAN) {
                    it
                }
                else {
                    it.copy(
                        payComponents = it.payComponents.plus(
                            CANADA_GROSS_WAGES.toComponentName() to (it.grossAmount ?: 0.0)
                        )
                    )
                }
            }
        return companyPayrollWrapper.copy(memberPays = enrichedMemberPays)
    }
}