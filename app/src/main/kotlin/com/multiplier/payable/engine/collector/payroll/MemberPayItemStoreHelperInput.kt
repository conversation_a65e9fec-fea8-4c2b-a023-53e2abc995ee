package com.multiplier.payable.engine.collector.payroll

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.util.dto.payroll.CompanyPayrollWrapper
import com.multiplier.payable.engine.domain.aggregates.MonthYear

data class MemberPayItemStoreHelperInput(
    val transactionId: String,
    val companyPayrollWrappers: List<CompanyPayrollWrapper>,
    val monthYear: MonthYear,
    val itemType: LineItemType
)
