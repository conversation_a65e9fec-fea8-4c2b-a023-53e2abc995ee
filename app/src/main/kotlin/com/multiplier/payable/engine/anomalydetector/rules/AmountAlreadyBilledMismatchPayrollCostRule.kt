package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.provider.NetsuiteAlreadyBilledAmountService
import com.multiplier.payable.engine.anomalydetector.rules.processor.BaseAnomalyDetectionRule
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.util.Locale

/**
 * Checks if the system's "already billed payroll cost" amount matches NetSuite's "already billed" amount.
 *
 * This rule validates that BILLED_GROSS_SALARY and BILLED_GROSS_SALARY_SUPPLEMENTARY line items 
 * (which represent amounts billed in the first invoice for the current period) match the corresponding 
 * GROSS_SALARY amounts in NetSuite's first invoices for the same period and contract.
 *
 * Logic:
 * - BILLED_GROSS_SALARY.unitAmount = Amount from current period's first invoice
 * - BILLED_GROSS_SALARY_SUPPLEMENTARY.unitAmount = Amount from current period's first invoice
 * - NetSuite GROSS_SALARY = Amount from current period's first invoice in NetSuite
 * - These should be identical for the same contract and period
 */
@Component
class AmountAlreadyBilledMismatchPayrollCostRule(
    featureFlagService: FeatureFlagService,
    private val netsuiteAlreadyBilledAmountService: NetsuiteAlreadyBilledAmountService,
) : BaseAnomalyDetectionRule(featureFlagService) {

    companion object {
        private val log = KotlinLogging.logger {}
        private val TARGET_LINE_ITEM_TYPES = setOf(
            LineItemType.BILLED_GROSS_SALARY,
            LineItemType.BILLED_GROSS_SALARY_SUPPLEMENTARY
        )
    }

    override val type: DetectionRuleType = DetectionRuleType.AMOUNT_ALREADY_BILLED_MISMATCH_PAYROLL_COST
    override val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.AMOUNT_ALREADY_BILLED_MISMATCH_PAYROLL_COST
    override val ruleName: String = "AmountAlreadyBilledMismatchPayrollCost"
    override val featureFlagName: String = "ENABLE_AMOUNT_ALREADY_BILLED_MISMATCH_PAYROLL_COST_CHECK"

    override fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult {
        val invoiceDTO = request.invoiceDTO!!
        val companyPayable = request.payable!!
        val anomalies = mutableListOf<String>()

        return try {
            log.info { "Checking for amount already billed mismatch in payroll costs for company ${command.companyId}" }

            // Extract BILLED_GROSS_SALARY and BILLED_GROSS_SALARY_SUPPLEMENTARY line items from current invoice
            val billedPayrollCostItems = invoiceDTO.lineItems
                .filter { it.itemType in TARGET_LINE_ITEM_TYPES }

            if (billedPayrollCostItems.isEmpty()) {
                val warningMessage = "No BILLED_GROSS_SALARY or BILLED_GROSS_SALARY_SUPPLEMENTARY line items found - validation skipped"
                log.info { warningMessage }
                return createWarningResult(listOf(warningMessage))
            }

            log.info { "Found ${billedPayrollCostItems.size} billed payroll cost line items to validate" }

            // For each billed payroll cost line item, compare with NetSuite data
            billedPayrollCostItems.forEach { lineItem ->
                val contractId = lineItem.contractId
                if (contractId == null) {
                    log.warn { "Billed payroll cost line item has null contract ID, skipping validation" }
                    return@forEach
                }

                val systemAlreadyBilledAmount = lineItem.unitAmount
                if (systemAlreadyBilledAmount == null) {
                    log.warn { "Billed payroll cost line item has null unit amount for contract $contractId, skipping validation" }
                    return@forEach
                }

                // Fetch NetSuite first invoice data for comparison
                val netsuiteAlreadyBilledAmount = netsuiteAlreadyBilledAmountService.getNetsuiteAlreadyBilledAmount(
                    command,
                    contractId,
                    companyPayable.year!!,
                    companyPayable.month!!,
                    LineItemType.GROSS_SALARY
                )

                if (netsuiteAlreadyBilledAmount == null) {
                    log.warn { "Could not fetch NetSuite first invoice payroll cost amount for contract $contractId, skipping validation" }
                    return@forEach
                }

                // Compare amounts - billed payroll cost should match NetSuite GROSS_SALARY exactly
                if (systemAlreadyBilledAmount != netsuiteAlreadyBilledAmount) {
                    val currency = invoiceDTO.billingCurrencyCode?.name ?: "UNKNOWN"
                    val message = String.format(
                        Locale.ENGLISH,
                        "System's already billed payroll cost amount %.2f %s does not match NetSuite's first invoice amount %.2f %s for contract %d",
                        systemAlreadyBilledAmount,
                        currency,
                        netsuiteAlreadyBilledAmount,
                        currency,
                        contractId
                    )
                    anomalies.add(message)
                    log.error { message }
                }
            }

            when {
                anomalies.isNotEmpty() -> createFailureResult(anomalies)
                else -> {
                    val successMessage = "Amount validation passed - all ${billedPayrollCostItems.size} billed payroll cost amounts match NetSuite first invoice amounts"
                    log.info { successMessage }
                    createSuccessResult(listOf(successMessage))
                }
            }
        } catch (e: Exception) {
            val message = "Error checking amount already billed mismatch for payroll costs: ${e.message}"
            log.error(e) { message }
            createFailureResult(listOf(message))
        }
    }


}
