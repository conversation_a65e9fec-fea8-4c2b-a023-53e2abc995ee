package com.multiplier.payable.engine.gateway

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "platform.invoice-engine.kafka.producer")
class KafkaTransactionGatewayProperties(
    var bootstrapServers: String,
    var keySerializer: String,
    var valueSerializer: String,
    var maxBlockMs: Int,
    var retryCount: Int,
    var retryBackoffMs: Int
)