package com.multiplier.payable.engine.collector.payroll

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.util.dto.payroll.CompanyMemberPayWrapper
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import org.springframework.stereotype.Component

@Component
class TotalAmountCostMinusExpenseCompanyPayrollItemStoreHelper(
    private val payableItemStoreService: PayableItemStoreService,
    private val objectMapper: ObjectMapper
) : CompanyPayrollItemStoreHelper(payableItemStoreService, objectMapper) {

    override fun getAmount(memberPay: CompanyMemberPayWrapper) : Double {
        val totalExpenseAmount = memberPay.totalExpenseAmount ?: 0.00
        return memberPay.amountTotalCost!!.minus(totalExpenseAmount) //Unlikely that the amountTotalCost will be null
    }

    override fun isItemBillable(memberPay: CompanyMemberPayWrapper): Boolean {
        return true
    }
}