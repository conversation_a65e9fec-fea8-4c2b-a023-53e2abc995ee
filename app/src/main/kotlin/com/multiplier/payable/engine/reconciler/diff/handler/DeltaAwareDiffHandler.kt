package com.multiplier.payable.engine.reconciler.diff.handler

import com.multiplier.payable.engine.payableitem.PayableItem
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import java.util.stream.Stream

@Service
@Qualifier("deltaAware")
class DeltaAwareDiffHandler(
    val addHandler: <PERSON>d<PERSON><PERSON><PERSON>,
    val updateHandler: UpdateHandler,
    val deleteHandler: DeleteHandler,
) : DiffHandler<List<PayableItem>> {

    override fun handle(context: DiffHandlerContext<List<PayableItem>>): List<PayableItem> {
        return Stream.of(
            addHandler.handle(context),
            updateHandler.handle(context),
            deleteHandler.handle(context)
        )
            .flatMap { it.stream() }
            .toList()
    }
}