package com.multiplier.payable.engine.debitnote.api

import lombok.Builder
import lombok.EqualsAndHashCode
import lombok.Getter
import lombok.extern.jackson.Jacksonized
import java.time.LocalDate

@Builder(toBuilder = true)
@Getter
@Jacksonized
@EqualsAndHashCode
data class DebitNoteApiResponse(
        val externalId: String,
        val debitNoteNo: String? = null,
        val customerId: String? = null,
        val date: LocalDate? = null,
        val reference: String? = null,
        val status: String? = null,
)
