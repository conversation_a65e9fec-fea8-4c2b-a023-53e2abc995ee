package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.rules.processor.BaseAnomalyDetectionRule
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.time.format.DateTimeFormatter
import java.util.Locale

/**
 * Checks if the invoice reference text contains the correct MMM and YY parts for the billing period
 */
@Component
class ReferenceTextCheckRule(
    featureFlagService: FeatureFlagService,
) : BaseAnomalyDetectionRule(featureFlagService) {
    companion object {
        private val log = KotlinLogging.logger {}
        private val REFERENCE_DATE_FORMATTER = DateTimeFormatter.ofPattern("MMM''yy", Locale.ENGLISH)
    }

    override val type: DetectionRuleType = DetectionRuleType.REFERENCE_TEXT_CHECK
    override val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.REFERENCE_TEXT_CHECK
    override val ruleName: String = "ReferenceTextCheck"
    override val featureFlagName: String = "ENABLE_REFERENCE_TEXT_CHECK"

    override fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult {
        val invoiceDTO = request.invoiceDTO!!
        val companyPayable = request.payable!!
        val invoiceReference = invoiceDTO.reference
        val anomalies = mutableListOf<String>()

        if (invoiceReference.isNullOrBlank()) {
            val message = "Invoice reference text is missing"
            anomalies.add(message)
            log.error { message }
            return createFailureResult(anomalies)
        }

        // Get billing period from company payable (not current date)
        val billingMonth = companyPayable.month
        val billingYear = companyPayable.year

        if (billingMonth == null || billingYear == null) {
            val message = "Billing month or year is missing from company payable"
            anomalies.add(message)
            log.error { message }
            return createFailureResult(anomalies)
        }

        val expectedMonth = generateExpectedMonth(billingMonth)
        val expectedYear = generateExpectedYear(billingYear)

        val containsMonth = invoiceReference.contains(expectedMonth, ignoreCase = true)
        val containsYear = invoiceReference.contains(expectedYear, ignoreCase = true)

        if (!containsMonth || !containsYear) {
            val message = String.format(
                Locale.ENGLISH,
                "Reference text should contain '%s' and '%s' for billing period %02d/%d but found '%s'",
                expectedMonth,
                expectedYear,
                billingMonth,
                billingYear,
                invoiceReference
            )
            anomalies.add(message)
            log.error { message }
        }

        return if (anomalies.isNotEmpty()) {
            createFailureResult(anomalies)
        } else {
            logger.info { "No $ruleName anomalies detected" }
            createSuccessResult(listOf("Reference text validation passed - expected format found"))
        }
    }

    /**
     * Generates the expected month part in "MMM" format based on the billing month
     */
    private fun generateExpectedMonth(month: Int): String {
        val billingDate = java.time.LocalDate.of(2024, month, 1) // Year doesn't matter for month formatting
        return billingDate.format(java.time.format.DateTimeFormatter.ofPattern("MMM", Locale.ENGLISH))
    }

    /**
     * Generates the expected year part in "YY" format based on the billing year
     */
    private fun generateExpectedYear(year: Int): String {
        val billingDate = java.time.LocalDate.of(year, 1, 1) // Month doesn't matter for year formatting
        return billingDate.format(java.time.format.DateTimeFormatter.ofPattern("yy", Locale.ENGLISH))
    }
}
