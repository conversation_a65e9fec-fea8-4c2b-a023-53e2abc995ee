package com.multiplier.payable.engine.collector.data

import com.multiplier.payable.engine.collector.ProcessedCollectorInput
import com.multiplier.payable.engine.domain.aggregates.MonthYearDuration
import java.time.LocalDateTime

class DefaultProcessedCollectorInput(
    override val transactionId: String,
    override val transactionDate: LocalDateTime,
    val timeQueryDuration: MonthYearDuration,
    val companyIds: Set<Long>,
) : ProcessedCollectorInput
