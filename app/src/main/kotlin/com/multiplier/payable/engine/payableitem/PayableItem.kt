package com.multiplier.payable.engine.payableitem

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.contract.CountryWorkStatus
import com.multiplier.payable.engine.deposit.Deposit
import com.multiplier.payable.engine.domain.aggregates.AnnualSeatPaymentTerm
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.PayrollCycleFrequency
import com.multiplier.payable.engine.memberpayable.MemberPayable
import com.multiplier.payable.engine.vas.Incident
import com.multiplier.payable.engine.vas.IncidentFeeDiscount
import com.multiplier.payable.engine.vas.IncidentManagementFee
import com.multiplier.payable.engine.collector.bankfee.BankFeeCalculatorResponse
import com.multiplier.payable.util.mathRounding
import lombok.extern.slf4j.Slf4j
import org.apache.commons.text.WordUtils
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Month
import java.time.format.TextStyle
import java.util.*

@Slf4j
data class PayableItem(
    val companyPayableLineItemIds: List<UUID> = mutableListOf(),
    val month: Int,
    val year: Int,
    val lineItemType: String,
    val contractId: Long? = null,
    val employmentType: String? = null,
    val companyId: Long? = null,
    var description: String? = null,
    val amountInBaseCurrency: Double,
    val baseCurrency: String,
    val billableCost: Double? = null, // Nullable before Fx converter
    val billingCurrency: String? = null,
    val versionId: String? = null,
    val originalTimestamp: Long,
    val cycle: InvoiceCycle,
    val countryCode: String? = null,
    var itemCount: Int = 1,
    val periodStartDate: LocalDate? = null,
    val periodEndDate: LocalDate? = null,
    val countryName: String? = null,
    var taxType: String? = null,
    var insuranceType: String? = null,
    var memberName: String? = null,
    var contractDepartment: ContractDepartment? = null,
    var feeType: String? = null,
    var deposit: Deposit? = null,
    val annualSeatPaymentTerm: AnnualSeatPaymentTerm? = null,
    var payCycleCount: Int = 0,
    val countryWorkStatus: CountryWorkStatus? = null,
    val payrollCycleId: Long? = null,
    val payrollCycleDateRange: DateRange? = null,
    var applyFx: Boolean? = true,
    var isBilled: Boolean? = false,
    var billedInvoiceNo: String? = null,
    var payrollCycleFrequency: PayrollCycleFrequency? = null,
    var entityId: Long? = null,
    var billId: String? = null,
    var minimumCommitedPayslips: Int? = null,
    var payslipCount: Int? = null,
    var noOfMembers: Int? = null,
    var memberPayable: MemberPayable? = null,
    var billingDuration: DateRange? = null,
    var usageDuration: DateRange? = null,
    var incident: Incident? = null,
    var incidentFeeDiscount: IncidentFeeDiscount? = null,
    var incidentManagementFee: IncidentManagementFee? = null,
    val orderAdvancePercentage: Double? = null,
    val matchingAdvanceCollectionLineTaxCode: String? = null,
    val transactionId: String? = null,
    var bankFeeCalculatorResponse: BankFeeCalculatorResponse? = null,
    ) {
    fun revertAmountAndMigrateToNewVersion(newVersionId: String?): PayableItem {
        return copy(
            amountInBaseCurrency = -amountInBaseCurrency,
            billableCost = billableCost?.let { -it },
            versionId = newVersionId,
        )
    }

    fun batchKey(): PayableItemKey {
        return PayableItemKey(
            month = month,
            year = year,
            itemType = lineItemType,
            companyId = companyId,
        )
    }

    @Suppress("unused")
    fun getMonthInThreeLetters(): String {
        return Month.of(month).getDisplayName(TextStyle.SHORT_STANDALONE, Locale.US)
    }

    @Suppress("unused")
    fun getPayrollMonthInThreeLetters(): String {
        val monthValue = payrollCycleDateRange?.endDate?.monthValue ?: month
        return Month.of(monthValue).getDisplayName(TextStyle.SHORT_STANDALONE, Locale.US)
    }

    @Suppress("unused")
    fun getYearInTwoDigits(): String {
        return (year % 2000).toString()
    }

    @Suppress("unused")
    fun getCountryCodeAndCountryWorkStatusDescription(): String {
        return "(${countryCode!!}${if (countryWorkStatus == CountryWorkStatus.REQUIRES_VISA) " Expat" else ""})"
    }

    @Suppress("unused")
    fun getCountryCodeInUpperCase(): String {
        return countryCode?.uppercase() ?: "Empty Country Code"
    }

    @Suppress("unused")
    fun getAnnualBillingItemDescription(): String {
        // skip creating item description for 1 year frequency
        if (annualSeatPaymentTerm!!.timeUnit == "YEAR") return ""
        return " (Billed Every ${annualSeatPaymentTerm.interval} ${getAnnualTimeUnitCamelCaseSingularOrPlural()}: ${annualSeatPaymentTerm.periodNumber}/${annualSeatPaymentTerm.periodCount})"
    }

    private fun getAnnualTimeUnitCamelCaseSingularOrPlural(): String {
        val timeUnit = annualSeatPaymentTerm!!.timeUnit.lowercase()
            .replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
        return if (annualSeatPaymentTerm.interval <= 1) timeUnit else "${timeUnit}s"
    }

    @Suppress("unused")
    fun getAnnualMemberSingularOrPlural(): String {
        return if (itemCount <= 1) "member" else "members"
    }

    @Suppress("unused")
    fun getInsuranceTypePascalCase(): String {
        return insuranceType!!.let { WordUtils.capitalizeFully(it.lowercase()) }
    }

    fun hasContract(): Boolean = contractId != null && contractId != -1L

    fun getCycleInTitleCase(): String {
        return when (cycle) {
            InvoiceCycle.WEEKLY -> "Weekly"
            InvoiceCycle.SEMIMONTHLY -> "Semi-monthly"
            InvoiceCycle.BIWEEKLY -> "Bi-weekly"
            InvoiceCycle.MONTHLY -> "Monthly"
            InvoiceCycle.YEARLY -> "Yearly"
            else -> cycle.name.lowercase().replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
        }
    }

    @Suppress("unused")
    fun getAmountRoundedUp2Decimals(): String {
        return amountInBaseCurrency.mathRounding(2)
    }

    @Suppress("unused")
    fun getAORContractType(): String {
        // Determine the base word ("Contractor" or "Freelancer")
        val baseWord = if (lineItemType == LineItemType.ANNUAL_MANAGEMENT_FEE_AOR_CONTRACTOR.name) {
            "Contractor"
        } else {
            "Freelancer"
        }

        return if (itemCount > 1) {
            "${baseWord}s"
        } else {
            baseWord
        }
    }
}

fun List<PayableItem>.getBillingDurationMinStart(): LocalDateTime {
    return this.minOf { requireNotNull(it.billingDuration).startDate }
}

fun List<PayableItem>.getBillingDurationMaxEnd(): LocalDateTime {
    return this.maxOf { requireNotNull(it.billingDuration).endDate }
}
