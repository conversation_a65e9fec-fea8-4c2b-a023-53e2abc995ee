package com.multiplier.payable.engine.domain.entities

import com.multiplier.core.payable.company.OfferingCode
import com.multiplier.payable.engine.contract.CountryWorkStatus
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.AnnualSeatPaymentTerm
import java.time.LocalDateTime

data class AnnualSeatFee(
    val companyId: Long,
    val planId: Long,
    val contractId: Long? = -1,
    val countryCode: String,
    val period: DateRange, // period of seat
    val amount: Double,
    val currencyCode: String,
    val seatCount: Int = 0,
    val discountApplied: String? = null, // justification, we may not need to store this
    val calculatedDateTime: LocalDateTime,
    val seatStatus: SeatStatus,
    val pricingPlanStatus: PricingPlanStatus,
    var annualSeatPaymentTerm: AnnualSeatPaymentTerm,
    val countryWorkStatus: CountryWorkStatus? = null,
    val offeringCode: OfferingCode,
    val lineCode: String,
    val seatId: Long?,
) {
    fun isEor(): Boolean = offeringCode == OfferingCode.EOR && lineCode == "SERVICE_FEE"
    fun isAor(): Boolean = offeringCode == OfferingCode.AOR && lineCode == "CONTRACTOR_SERVICE_FEE"
    fun isFreelancer(): Boolean = offeringCode == OfferingCode.AOR && lineCode == "FREELANCER_SERVICE_FEE"
}
