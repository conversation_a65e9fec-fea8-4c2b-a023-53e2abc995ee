package com.multiplier.payable.engine.reconciler.descriptionbuilder.vas

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilder
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import org.springframework.stereotype.Service

/**
 * A [PayableItemDescriptionBuilder] for [LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE].
 */
@Service
class VasIncidentLegalConsultationFeeDescriptionBuilder : PayableItemDescriptionBuilder {
    override val lineItemType: LineItemType
        get() = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE

    override fun build(context: PayableItemDescriptionBuilderContext): String = "Legal Consultation Fee"
}
