package com.multiplier.payable.engine.collector.gross

import com.multiplier.payable.engine.currency.CurrencyCode
import java.math.BigDecimal

data class HistoryPayroll(
    val contractId: Long,
    val grossSalary: BigDecimal,
    val currencyCode: CurrencyCode,
    val countryCode: String,
    val versionId: String,
    val originalTimestamp: Long,
    val companyId: Long,
    val month: Int,
    val year: Int,
) {
    fun batchKey(): PayrollBatchKey {
        return PayrollBatchKey(
            companyId = companyId,
            month = month,
            year = year,
        )
    }
}
