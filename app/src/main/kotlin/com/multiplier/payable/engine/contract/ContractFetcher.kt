package com.multiplier.payable.engine.contract

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.core.payable.adapters.ContractServiceAdapter
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.springframework.stereotype.Service

interface ContractFetcher {
    fun getEligibleContracts(invoiceCommand: InvoiceCommand): List<Contract>
}

@Service
class DefaultContractFetcher(
    val contractServiceAdapter: ContractServiceAdapter,
    val grpcContractMapper: ContractFromGrpcMapper,
) : ContractFetcher {
    private val elgibleContractStatus =
        setOf(
            ContractOuterClass.ContractStatus.ACTIVE,
            ContractOuterClass.ContractStatus.ONBOARDING,
            ContractOuterClass.ContractStatus.OFFBOARDING,
        )

    override fun getEligibleContracts(invoiceCommand: InvoiceCommand): List<Contract> {
        return when (invoiceCommand.transactionType) {
            TransactionType.FIRST_INVOICE,
            -> {
                if (invoiceCommand.forceContractIds.isEmpty()) {
                    contractServiceAdapter.findContractsEligibleForInvoiceGenerationForGivenCompanyAndMonth(
                        companyId = invoiceCommand.companyId,
                        month = invoiceCommand.dateRange.endDate.monthValue,
                        year = invoiceCommand.dateRange.endDate.year,
                    ).map { grpcContractMapper.map(it) }
                } else {
                    findForcedContracts(invoiceCommand)
                }
            }

            else -> {
                emptyList()
            }
        }
    }

    private fun findForcedContracts(invoiceCommand: InvoiceCommand): List<Contract> =
        contractServiceAdapter.getContractsByIdsAnyStatus(invoiceCommand.forceContractIds.toList())
            .filter { it.companyId == invoiceCommand.companyId }
            .filter { elgibleContractStatus.contains(it.status) }
            .map { grpcContractMapper.map(it) }
}
