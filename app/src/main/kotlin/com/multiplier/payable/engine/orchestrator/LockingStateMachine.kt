package com.multiplier.payable.engine.orchestrator

import com.multiplier.payable.engine.TransactionCommand
import com.multiplier.payable.engine.TransactionStatus
import com.multiplier.payable.lock.distributed.DistributedLock
import mu.KotlinLogging
import java.time.Duration
import java.util.function.Consumer

class LockingStateMachine(
    private val baseStateMachine: SimpleStateMachine,
    private val lockSupplier: (TransactionCommand) -> DistributedLock,
    private val lockStates: Set<TransactionStatus> = setOf(TransactionStatus.INIT),
    private val releaseStates: Set<TransactionStatus> = setOf(TransactionStatus.ERROR, TransactionStatus.DONE),
    private val leaseTime: Duration,
) : StateMachine {

    companion object {
        private val log = KotlinLogging.logger { }
    }

    override fun next(cur: TransactionStatus, action: TransactionAction): TransitionAction {
        val baseTransition = baseStateMachine.next(cur, action)
        val decoratedAction = decorateActionWithLocking(cur, baseTransition)

        return baseTransition.copy(action = decoratedAction)
    }

    private fun decorateActionWithLocking(
        currentStatus: TransactionStatus,
        transition: TransitionAction
    ): Consumer<TransactionCommand> {
        return when {
            currentStatus in lockStates -> decorateWithLockAcquisition(transition.action)
            transition.targetStatus in releaseStates -> decorateWithLockRelease(transition.action)
            else -> decorateWithExceptionHandling(transition.action)
        }
    }

    private fun decorateWithLockAcquisition(baseAction: Consumer<TransactionCommand>): Consumer<TransactionCommand> {
        val exceptionHandlingAction = decorateWithExceptionHandling(baseAction)
        return Consumer { command ->
            val lock = lockSupplier(command)
            lock.lock(leaseTime)
            log.info {
                "Acquired lock '${lock.name}' " +
                        "for transaction ${command.transactionId} with lease time $leaseTime"
            }
            exceptionHandlingAction.accept(command)
        }
    }

    private fun decorateWithLockRelease(baseAction: Consumer<TransactionCommand>): Consumer<TransactionCommand> {
        return Consumer { command ->
            val lock = lockSupplier(command)
            try {
                baseAction.accept(command)
            } finally {
                lock.unlock()
            }
        }
    }

    private fun decorateWithExceptionHandling(baseAction: Consumer<TransactionCommand>): Consumer<TransactionCommand> {
        return Consumer { command ->
            val lock = lockSupplier(command)
            try {
                baseAction.accept(command)
            } catch (e: Exception) {
                log.warn(e) {
                    "Exception occurred during action execution for " +
                            "transaction ${command.transactionId}, releasing lock as safety measure"
                }
                lock.unlock()
                throw e
            }
        }
    }
}
