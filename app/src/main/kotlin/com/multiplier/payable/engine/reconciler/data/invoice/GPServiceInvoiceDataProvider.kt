package com.multiplier.payable.engine.reconciler.data.invoice

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.service.InvoiceFetcher
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.mapper.CompanyPayableMapper
import com.multiplier.payable.types.PayableStatus
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class GPServiceInvoiceDataProvider(
    private val invoiceFetcher: InvoiceFetcher,
    private val jpaCompanyPayableRepository: JpaCompanyPayableRepository,
    private val mapper: CompanyPayableMapper,
) : InvoiceDataProvider {

    companion object {
        private val logger = KotlinLogging.logger { }
        private val INACTIVE_PAYABLE_STATUS = listOf(PayableStatus.VOIDED, PayableStatus.DELETED)
    }

    override fun transactionType(): TransactionType {
        return TransactionType.GP_SERVICE_INVOICE
    }

    override fun fetchActiveInvoices(command: InvoiceCommand, dateRange: DateRange?): List<CompanyPayable> {
        val start = dateRange?.startDate ?: command.dateRange.startDate
        val end = dateRange?.endDate ?: command.dateRange.endDate

        logger.info { "Fetching active GP Service Invoices for companyId=${command.companyId}, range=[$start to $end]" }

        val invoicePairs = invoiceFetcher.getGPServiceInvoices(command.companyId, start, end)

        val companyPayableIds = invoicePairs.map { it.left }

        if (companyPayableIds.isEmpty()) {
            logger.info { "No active invoices found for GP Service within date range" }
            return emptyList()
        }

        val payables = jpaCompanyPayableRepository
            .findByIdInAndStatusNotIn(companyPayableIds, INACTIVE_PAYABLE_STATUS)

        logger.info { "Found ${payables.size} valid payables from ${invoicePairs.size} invoices" }

        return mapper.mapCompanyPayables(payables)
    }

    override fun fetchActiveInvoices(command: InvoiceCommand): List<CompanyPayable> {
        return fetchActiveInvoices(command, command.dateRange)
    }

    override fun aggregateInvoiceItems(invoicedItems: List<PayableItem>): List<PayableItem> {
        return invoicedItems
    }
}
