package com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.application

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.template.CountryTransactionTemplate
import com.multiplier.payable.types.CountryCode
import java.util.UUID

data class CountryTransactionTemplateDto(
    val id: UUID,
    val transactionType: TransactionType,
    val country: CountryCode,
    val description: String,
    val config: CountryTransactionTemplate,
)