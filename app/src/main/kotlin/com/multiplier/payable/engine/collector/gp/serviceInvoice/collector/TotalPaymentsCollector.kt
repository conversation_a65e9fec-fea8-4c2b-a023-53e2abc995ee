package com.multiplier.payable.engine.collector.gp.serviceInvoice.collector

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataCollector
import com.multiplier.payable.engine.collector.gp.serviceInvoice.service.TotalPaymentsService
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class TotalPaymentsCollector(
    private val totalPaymentsService: TotalPaymentsService
) : DataCollector {

    override fun handle(command: InvoiceCommand) {
        log.info { "Handling TotalPayments for transactionId: ${command.transactionId}, companyId: ${command.companyId}" }

        try {
            val totalPayments = totalPaymentsService.getTotalPayments(command)
            totalPaymentsService.normalizeAndSave(command.transactionId, totalPayments, command, getSupportedType())
        } catch (e: Exception) {
            log.error(e) { "Error while handling TotalPayments for transactionId: ${command.transactionId}" }
            throw e
        }

        log.info { "Completed handling TotalPayments for transactionId: ${command.transactionId}" }
    }

    override fun rollback(command: InvoiceCommand) {
        log.warn { "Rolling back TotalPayments for transactionId: ${command.transactionId}" }
        // Add rollback logic if needed
    }

    override fun getSupportedType(): LineItemType = LineItemType.TOTAL_PAYMENTS
}
