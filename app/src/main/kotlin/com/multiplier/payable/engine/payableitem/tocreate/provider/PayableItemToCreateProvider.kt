package com.multiplier.payable.engine.payableitem.tocreate.provider

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.payableitem.key.provider.PayableItemKeyProviderFactory
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class PayableItemToCreateProvider(
    private val payableItemKeyProviderFactory: PayableItemKeyProviderFactory,
) {

    private companion object {
        private val logger = KotlinLogging.logger {}
    }

    fun get(
        transactionType: TransactionType,
        latestItems: List<PayableItem>,
        invoicedItems: List<PayableItem>,
    ): List<PayableItem> {
        val payableItemKeyProvider = payableItemKeyProviderFactory.get(transactionType)
        val invoicedKeys = invoicedItems.map { payableItemKeyProvider.getKey(it) }
        return latestItems.filterNot { invoicedKeys.contains(payableItemKeyProvider.getKey(it)) }
            .also {
                logger.info { "Latest items count : ${latestItems.size}." +
                        " Invoiced Items count: ${invoicedItems.size}." +
                        " Items to create = ${it.size}"}
            }
    }
}