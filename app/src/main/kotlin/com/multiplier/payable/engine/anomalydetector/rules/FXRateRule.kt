package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.repository.model.JpaPayableItem
import com.multiplier.core.payable.repository.model.JpaPayableItemData
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.rules.processor.BaseAnomalyDetectionRule
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.types.PayableItemType
import org.springframework.stereotype.Component
import java.util.Locale

/**
 * Checks if the FX rates used in the invoice are correct
 */
@Component
class FXRateRule(
    featureFlagService: FeatureFlagService,
) : BaseAnomalyDetectionRule(featureFlagService) {
    override val ruleName: String = "FXRate"
    override val featureFlagName: String = "ENABLE_FX_RATE_CHECK"
    override val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.FX_RATE
    override val type: DetectionRuleType = DetectionRuleType.FX_RATE

    /**
     * Rule-specific detection logic.
     */
    override fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult {
        // Get the invoice data (we know it's not null because isRequestValid was called)
        val companyPayableItems = request.payable.items!!
        val invoiceDTO = request.invoiceDTO!!

        // Track any anomalies found
        val anomalies = mutableListOf<String>()
        val billingCurrency = invoiceDTO.billingCurrencyCode

        val lineItemDTOCache: Map<String, LineItemDTO> =
            invoiceDTO.lineItems
                .associateBy { it.description }

        // Check FX rates for each line item
        companyPayableItems.forEach { lineItem ->
            checkFXRate(lineItem, anomalies, billingCurrency, lineItemDTOCache)
        }

        // Return appropriate result based on anomalies
        return if (anomalies.isNotEmpty()) {
            createFailureResult(anomalies)
        } else {
            logger.info { "No $ruleName anomalies detected" }
            createSuccessResult(listOf("FX rates are correct"))
        }
    }

    /**
     * Checks the FX rate for a line item
     * @param jpaPayableItem The line item to check
     * @param anomalies List to add anomalies to
     */
    private fun checkFXRate(
        jpaPayableItem: JpaPayableItem,
        anomalies: MutableList<String>,
        billingCurrency: CurrencyCode,
        lineItemDTOCache: Map<String, LineItemDTO>,
    ) {
        val descriptionToMatch: String = jpaPayableItem.description
        val currencyCode: CurrencyCode = jpaPayableItem.currencyCode
        if (currencyCode != billingCurrency) {
            return
        }
        var lineItemDTO: LineItemDTO? = lineItemDTOCache[descriptionToMatch]
        if (jpaPayableItem.type == PayableItemType.MEMBER_MANAGEMENT_FEE) {
            lineItemDTO = getLineItemDTOForManagementFee(jpaPayableItem.itemData, lineItemDTOCache)
        }
        if (lineItemDTO == null) {
            return
        }
        val isCostEqual = lineItemDTO.unitAmount == jpaPayableItem.totalCost
        if (!isCostEqual) {
            val message =
                String.format(
                    Locale.ENGLISH,
                    "Fx rate used is not 1 for %s to %s",
                    jpaPayableItem.totalCost,
                    lineItemDTO.unitAmount,
                )
            anomalies.add(message)
            logger.error { message }
        }
    }

    private fun getLineItemDTOForManagementFee(
        itemData: Set<JpaPayableItemData>,
        descPayableItemCache: Map<String, LineItemDTO>,
    ): LineItemDTO? {
        val firstItem = itemData.firstOrNull() ?: return null

        return descPayableItemCache.keys
            .firstOrNull { desc ->
                desc.contains(firstItem.contractId.toString()) && desc.contains("Management Fee")
            }?.let { descPayableItemCache[it] }
    }
}
