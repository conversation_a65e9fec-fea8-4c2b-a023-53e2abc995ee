package com.multiplier.payable.engine.collector.additional.mfee

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataCollector
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
internal class AdditionalPayrollFeeDataCollector(
    private val additionalPayrollManagementFeeAdapter: AdditionalPayrollManagementFeeAdapter,
    private val itemStoreService: PayableItemStoreService,
    private val objectMapper: ObjectMapper,
) : DataCollector {
    companion object {
        private val log = KotlinLogging.logger {}
    }

    override fun getSupportedType(): LineItemType = LineItemType.ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL

    override fun handle(command: InvoiceCommand) {
        val additionalManagementFees = getAdditionalPayrollFees(
            companyId = command.companyId,
            dateRange = command.dateRange,
        )
        saveAdditionalFee(
            transactionId = command.transactionId,
            additionalManagementFees = additionalManagementFees,
            invoiceMonthYear = command.dateRange.toMonthYearDuration().from,
        )
    }

    protected fun getAdditionalPayrollFees(
        companyId: Long,
        dateRange: DateRange,
    ): List<AdditionalManagementFee> {
        return additionalPayrollManagementFeeAdapter.getAdditionalPayrollFees(
            companyId = companyId,
            month = dateRange.startDate.monthValue,
            year = dateRange.startDate.year,
        )
    }

    protected fun saveAdditionalFee(
        transactionId: String,
        additionalManagementFees: List<AdditionalManagementFee>,
        invoiceMonthYear: MonthYear,
    ): List<PayableItemStoreDto> {
        if (additionalManagementFees.isEmpty()) {
            return listOf()
        }

        val payableItemStores = mutableListOf<PayableItemStoreDto>()
        val dateRange = invoiceMonthYear.invoiceMonthRange()
        val additionalManagementFeesByKey =
            additionalManagementFees.groupBy {
                AdditionalMFeeKey(
                    companyId = it.companyId,
                    month = invoiceMonthYear.month,
                    year = invoiceMonthYear.year,
                    oTime = it.calculatedTime,
                )
            }
        additionalManagementFeesByKey.forEach { (key, additionalManagementFees) ->
            val companyPayableItems =
                additionalManagementFees
                    .map { additionalManagementFee ->
                        PayableItemStoreDto(
                            month = invoiceMonthYear.month,
                            year = invoiceMonthYear.year,
                            itemType = getSupportedType(),
                            itemData = objectMapper.writeValueAsString(additionalManagementFee),
                            companyId = additionalManagementFee.companyId,
                            contractId = additionalManagementFee.contractId,
                            amount = additionalManagementFee.amountTotalCost,
                            currency = additionalManagementFee.currencyCode,
                            versionId = key.computeHash(),
                            originalTimestamp = key.oTime,
                            cycle = InvoiceCycle.MONTHLY.ordinal,
                            countryCode = additionalManagementFee.countryCode,
                            periodStartDate = dateRange.startDate.toLocalDate(),
                            periodEndDate = dateRange.endDate.toLocalDate(),
                        )
                    }.toList()
            try {
                val savedPayableItems =
                    itemStoreService.saveAndIgnoreDuplication(companyPayableItems)
                payableItemStores.addAll(savedPayableItems)
            } catch (e: Exception) {
                log.error(e) { "Error collect data for key = $key, transactionId = $transactionId" }
            }
        }

        return payableItemStores
    }
}
