package com.multiplier.payable.engine.reconciler.order

import com.multiplier.common.exception.toBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.reconciler.DataReconciler
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorage
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorageContext
import com.multiplier.payable.engine.reconciler.data.invoice.InvoiceDataProvider
import com.multiplier.payable.engine.reconciler.data.item.ItemStoreDataProvider
import com.multiplier.payable.engine.reconciler.diff.reconciler.InvoiceDiffReconciler
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component

@Component
class OrderFormAdvanceDataReconciler(
    private val templateProvider: TransactionTemplateProvider,
    @Qualifier("order-form-advance") private val activeInvoiceProvider: InvoiceDataProvider,
    @Qualifier("order-form-advance") private val itemStoreProvider: ItemStoreDataProvider,
    @Qualifier("order-form-advance") private val diffReconciler: InvoiceDiffReconciler,
    private val companyPayableStorage: CompanyPayableStorage,
) : DataReconciler {
    private companion object {
        private val log = KotlinLogging.logger {}
    }

    override val transactionType = TransactionType.ORDER_FORM_ADVANCE

    override fun handle(command: InvoiceCommand) {
        val invoicedItems = activeInvoiceProvider.fetchAndAggregateInvoiceItems(command)
        val template = templateProvider.findTemplateFor(command.transactionType, command.companyId)
        val latestItems = itemStoreProvider.fetchLatest(command, template.lineItemTypes)
        val diffItemsToInvoice = diffReconciler.reconcile(command, template, invoicedItems, latestItems)
        require (diffItemsToInvoice.isNotEmpty()) {
            throw PayableErrorCode.INVOICING_NO_DELTA_ITEMS.toBusinessException("No new items or lack of delta found for transactionId=${command.transactionId}")
        }

        val companyPayableStorageContext =
            buildCompanyPayableStorage(command, diffItemsToInvoice, template.skipIsrGeneration)
        companyPayableStorage.exchangeAndStore(companyPayableStorageContext).also {
            log.info(
                "Complete storing ${diffItemsToInvoice.size} payable items for" +
                        " companyId: ${command.companyId}, transactionId: ${command.transactionId}"
            )
        }
    }

    private fun buildCompanyPayableStorage(
        command: InvoiceCommand,
        diffItemsToInvoice: List<PayableItem>,
        shouldSkipIsr: Boolean,
    ): CompanyPayableStorageContext {
        return CompanyPayableStorageContext(
            transactionId = command.transactionId,
            invoiceDate = command.transactionDate,
            monthYear = command.getMonthYear(),
            items = diffItemsToInvoice,
            companyId = command.companyId,
            cycle = command.cycle,
            cycleDuration = DateRange(
                startDate = command.dateRange.startDate,
                endDate = command.dateRange.endDate,
            ),
            transactionType = command.transactionType,
            skipIsrGeneration = shouldSkipIsr,
        )
    }

}