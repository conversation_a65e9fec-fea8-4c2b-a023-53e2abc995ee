package com.multiplier.payable.engine.reconciler.descriptionbuilder

import com.multiplier.core.payable.adapters.api.LineItemType
import org.springframework.stereotype.Component

@Component
class TotalPaymentsDescriptionBuilder : PayableItemDescriptionBuilder {

    override val lineItemType = LineItemType.TOTAL_PAYMENTS

    override fun build(context: PayableItemDescriptionBuilderContext): String {
        val noOfMembers = context.noOfMembers ?: "Unknown"
        return """
            Payment Fee (for $noOfMembers Members) ${context.countryCode}: ${context.currencyCode} ${context.amountInBaseCurrency}
        """.trimIndent()
    }
}
