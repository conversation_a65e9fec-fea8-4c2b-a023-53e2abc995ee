package com.multiplier.payable.engine.vas

import com.google.protobuf.Timestamp
import com.multiplier.grpc.common.country.v2.Country
import com.multiplier.grpc.common.currency.v2.Currency
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.vasincidental.grpc.schema.VasIncidental
import com.multiplier.vasincidental.grpc.schema.VasIncidental.GrpcAmount
import com.multiplier.vasincidental.grpc.schema.VasIncidental.GrpcIncident
import com.multiplier.vasincidental.grpc.schema.VasIncidental.GrpcIncidentFeeDiscount
import com.multiplier.vasincidental.grpc.schema.VasIncidental.GrpcIncidentFeeDiscountType
import com.multiplier.vasincidental.grpc.schema.VasIncidental.GrpcIncidentType
import com.multiplier.vasincidental.grpc.schema.VasIncidental.QuantityUnit
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.ZoneOffset

@Component
class IncidentMapper {
    fun map(grpcIncident: GrpcIncident): Incident {
        val type = grpcIncident.type.toDomain()

        return when (type) {
            IncidentType.STORAGE ->
                StorageIncident(
                    id = grpcIncident.id,
                    amount = grpcIncident.amount.toDomain(),
                    description = grpcIncident.description,
                    incidentTime = grpcIncident.incidentTime.toLocalDateTime(),
                    type = type,
                    countryCode = grpcIncident.countryCode.toDomain(),
                    chargePolicy = grpcIncident.getPolicyRate() as? IncidentAmountChargePolicy,
                )

            IncidentType.LEGAL_CONSULTATION, IncidentType.CONTRACT_CUSTOMISATION ->
                LegalIncident(
                    id = grpcIncident.id,
                    amount = grpcIncident.amount.toDomain(),
                    description = grpcIncident.description,
                    incidentTime = grpcIncident.incidentTime.toLocalDateTime(),
                    type = type,
                    countryCode = grpcIncident.countryCode.toDomain(),
                    chargePolicy = grpcIncident.getPolicyRate() as? IncidentQuantityChargePolicy,
                )

            IncidentType.OTHERS_SERVICE, IncidentType.OTHERS_SERVICE_FEE, IncidentType.EXPAT_SETUP,
            ->
                OthersIncident(
                    id = grpcIncident.id,
                    amount = grpcIncident.amount.toDomain(),
                    description = grpcIncident.description,
                    incidentTime = grpcIncident.incidentTime.toLocalDateTime(),
                    type = type,
                    countryCode = grpcIncident.countryCode.toDomain(),
                    chargePolicy = grpcIncident.getPolicyRate() as? IncidentAmountChargePolicy,
                )

            IncidentType.LAPTOP, IncidentType.MONITOR, IncidentType.ACCESSORIES ->
                AssetIncident(
                    id = grpcIncident.id,
                    amount = grpcIncident.amount.toDomain(),
                    description = grpcIncident.description,
                    incidentTime = grpcIncident.incidentTime.toLocalDateTime(),
                    type = type,
                    contractId = grpcIncident.assetIncident.contractId,
                    countryCode = grpcIncident.countryCode.toDomain(),
                    chargePolicy = grpcIncident.getPolicyRate() as? IncidentAmountChargePolicy,
                )

            IncidentType.PICKUP_DELIVERY ->
                LogisticsIncident(
                    id = grpcIncident.id,
                    amount = grpcIncident.amount.toDomain(),
                    description = grpcIncident.description,
                    incidentTime = grpcIncident.incidentTime.toLocalDateTime(),
                    type = type,
                    countryCode = grpcIncident.countryCode.toDomain(),
                    chargePolicy = grpcIncident.getPolicyRate() as? IncidentAmountChargePolicy,
                )
        }
    }

    fun map(grpcIncidentDiscount: GrpcIncidentFeeDiscount): IncidentFeeDiscount =
        when (val type = grpcIncidentDiscount.type.toDomain()) {
            IncidentFeeDiscountType.FIXED -> {
                FixedIncidentFeeDiscount(
                    id = grpcIncidentDiscount.id,
                    appliedIncidentIds = grpcIncidentDiscount.appliedIncidentIdsList,
                    description = grpcIncidentDiscount.description,
                    type = type,
                    amount = grpcIncidentDiscount.fixedFeeDiscount.amount.toDomain(),
                )
            }

            IncidentFeeDiscountType.PERCENTAGE -> {
                PercentageIncidentFeeDiscount(
                    id = grpcIncidentDiscount.id,
                    appliedIncidentIds = grpcIncidentDiscount.appliedIncidentIdsList,
                    description = grpcIncidentDiscount.description,
                    type = type,
                    percentage = grpcIncidentDiscount.percentageFeeDiscount.percentage,
                )
            }
        }
}

fun IncidentType.toGrpc(): GrpcIncidentType =
    when (this) {
        IncidentType.LAPTOP -> GrpcIncidentType.LAPTOP
        IncidentType.MONITOR -> GrpcIncidentType.MONITOR
        IncidentType.ACCESSORIES -> GrpcIncidentType.ACCESSORIES
        IncidentType.PICKUP_DELIVERY -> GrpcIncidentType.PICKUP_DELIVERY
        IncidentType.STORAGE -> GrpcIncidentType.STORAGE
        IncidentType.CONTRACT_CUSTOMISATION -> GrpcIncidentType.CONTRACT_CUSTOMISATION
        IncidentType.LEGAL_CONSULTATION -> GrpcIncidentType.LEGAL_CONSULTATION
        IncidentType.OTHERS_SERVICE -> GrpcIncidentType.OTHERS_SERVICE
        IncidentType.OTHERS_SERVICE_FEE -> GrpcIncidentType.OTHERS_SERVICE_FEE
        IncidentType.EXPAT_SETUP -> GrpcIncidentType.EXPAT_SETUP
    }

fun GrpcIncidentType.toDomain(): IncidentType =
    when (this) {
        GrpcIncidentType.LAPTOP -> IncidentType.LAPTOP
        GrpcIncidentType.MONITOR -> IncidentType.MONITOR
        GrpcIncidentType.ACCESSORIES -> IncidentType.ACCESSORIES
        GrpcIncidentType.PICKUP_DELIVERY -> IncidentType.PICKUP_DELIVERY
        GrpcIncidentType.STORAGE -> IncidentType.STORAGE
        GrpcIncidentType.CONTRACT_CUSTOMISATION -> IncidentType.CONTRACT_CUSTOMISATION
        GrpcIncidentType.LEGAL_CONSULTATION -> IncidentType.LEGAL_CONSULTATION
        GrpcIncidentType.OTHERS_SERVICE -> IncidentType.OTHERS_SERVICE
        GrpcIncidentType.OTHERS_SERVICE_FEE -> IncidentType.OTHERS_SERVICE_FEE
        GrpcIncidentType.EXPAT_SETUP -> IncidentType.EXPAT_SETUP
        else -> throw UnsupportedOperationException("Unsupported incident type: $this")
    }

fun GrpcIncidentFeeDiscountType.toDomain(): IncidentFeeDiscountType =
    when (this) {
        GrpcIncidentFeeDiscountType.FIXED -> IncidentFeeDiscountType.FIXED
        GrpcIncidentFeeDiscountType.PERCENTAGE -> IncidentFeeDiscountType.PERCENTAGE
        else -> throw UnsupportedOperationException("Unsupported incident discount type: $this")
    }

fun GrpcAmount.toDomain(): Amount =
    Amount(
        value = BigDecimal(this.value),
        currency = this.currency.toDomain(),
    )

fun Timestamp.toLocalDateTime(): LocalDateTime = LocalDateTime.ofEpochSecond(this.seconds, this.nanos, ZoneOffset.UTC)

fun Country.CountryCode.toDomain(): CountryCode = CountryCode.valueOf(this.name.removePrefix("COUNTRY_CODE_"))

fun Currency.CurrencyCode.toDomain(): CurrencyCode = CurrencyCode.valueOf(this.name.removePrefix("CURRENCY_CODE_"))

fun QuantityUnit.toDomain(): IncidentQuantityUnit =
    when (this) {
        QuantityUnit.HOURS -> IncidentQuantityUnit.HOURS
        else -> error("Unsupported quantity unit: $this")
    }

fun GrpcIncident.getPolicyRate(): IncidentChargePolicy? =
    if (hasChargePolicy()) {
        when (chargePolicy.rateCase) {
            VasIncidental.IncidentChargePolicy.RateCase.AMOUNT -> {
                val amount = chargePolicy.amount
                IncidentAmountChargePolicy(
                    amount =
                        Amount(
                            value = BigDecimal(amount.value),
                            currency = amount.currency.toDomain(),
                        ),
                )
            }

            VasIncidental.IncidentChargePolicy.RateCase.QUANTITY -> {
                val quantity = chargePolicy.quantity
                IncidentQuantityChargePolicy(
                    value = quantity.value,
                    unit = quantity.unit.toDomain(),
                )
            }

            else -> error("Unsupported charge policy rate: ${chargePolicy.rateCase}")
        }
    } else {
        null
    }
