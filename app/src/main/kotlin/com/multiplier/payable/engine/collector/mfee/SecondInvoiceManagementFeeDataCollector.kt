package com.multiplier.payable.engine.collector.mfee

import com.multiplier.core.config.featureflag.FeatureFlagService
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.service.ForcedManagementFeeService
import com.multiplier.core.util.dto.mfee.ManagementFeeWrapper
import com.multiplier.payable.engine.collector.DataCollector
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.ProcessedCollectorInput
import com.multiplier.payable.engine.collector.data.DefaultProcessedCollectorInput
import com.multiplier.payable.engine.collector.payroll.ContractPayrollService
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.domain.aggregates.MonthYearDuration
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class SecondInvoiceManagementFeeDataCollector(
    private val dataCollectorInputProcessor: DataCollectorInputProcessor<DefaultProcessedCollectorInput>,
    private val contractPayrollService: ContractPayrollService,
    private val forcedManagementFeeService: ForcedManagementFeeService,
    private val managementFeeNormalizeStoreService: ManagementFeeNormalizeStoreService,
    private val secondInvoiceEligibleContractsExtractor: EligibleContractsExtractor,
    private val featureFlagService: FeatureFlagService,
) : DataCollector {
    companion object {
        private val log = KotlinLogging.logger {}
        private const val ENABLE_REVENUE_LEAKAGE_PREVENTION = "fix-revenue-leakage"
    }

    override fun handle(command: InvoiceCommand) {
        val processedInput = dataCollectorInputProcessor.process(command)
        handle(processedInput)
    }

    private fun handle(processedInput: ProcessedCollectorInput) {
        require(processedInput is DefaultProcessedCollectorInput) {
            "Non-supported collector processed input for ${getSupportedType()}"
        }
        log.info {
            "Handle processed input for transactionId: ${processedInput.transactionId}, " +
                "companyIds: ${processedInput.companyIds}, " +
                "lineItemType: ${getSupportedType()}"
        }

        val monthYear = processedInput.timeQueryDuration.from.invoiceMonthYear()
        val companyIds = processedInput.companyIds
        val aggregatedCalculatedManagementFees = mutableListOf<ManagementFeeWrapper>()

        for (companyId in companyIds) {
            val calculatedManagementFees =
                if (isRevenueLeakagePreventionEnabled(companyId)) {
                    log.info { "Calculating second invoice management fees with revenue leakage prevention enabled." }
                    val eligibleContracts = secondInvoiceEligibleContractsExtractor.extract(companyId, monthYear.month, monthYear.year)
                    val contractIdToCountryMap = eligibleContracts.associate { it.id to it.country }

                    calculateForcedManagementFees(
                        companyId,
                        eligibleContracts.map { it.id },
                        contractIdToCountryMap,
                        processedInput.timeQueryDuration,
                    )
                } else {
                    log.info { "Calculating second invoice management fees based on payroll data." }
                    val payrollContracts =
                        contractPayrollService
                            .getContractPayrolls(listOf(companyId), monthYear.month, monthYear.year)
                            .filter { it.type == ContractType.EMPLOYEE && !it.isOffCycle }
                            .distinctBy { it.contractId }

                    val contractIdToCountryMap = payrollContracts.associate { it.contractId to it.countryCode }
                    calculateForcedManagementFees(
                        companyId,
                        payrollContracts.map { it.contractId },
                        contractIdToCountryMap,
                        processedInput.timeQueryDuration,
                    )
                }
            aggregatedCalculatedManagementFees.addAll(calculatedManagementFees)
        }

        managementFeeNormalizeStoreService.normalizeAndSave(
            processedInput.transactionId,
            aggregatedCalculatedManagementFees,
            getSupportedType(),
        )
    }

    override fun getSupportedType(): LineItemType = LineItemType.MANAGEMENT_FEE_EOR_PAYROLL

    private fun calculateForcedManagementFees(
        companyId: Long,
        contractIds: List<Long>,
        contractToCountryMap: Map<Long, String>,
        monthYearDuration: MonthYearDuration,
    ): List<ManagementFeeWrapper> {
        val monthYear = monthYearDuration.from
        val managementFees = forcedManagementFeeService.calculate(companyId, contractIds, monthYear.month, monthYear.year)
        val calculatedTime = System.currentTimeMillis()

        return managementFees.map {
            ManagementFeeWrapper(
                contractId = it.contract.id,
                amountTotalCost = it.discountedFee,
                companyId = companyId,
                originalFee = it.originalFee,
                appliedDiscount = it.appliedDiscount,
                invoiceDuration = monthYearDuration,
                currencyCode = "USD",
                countryCode = contractToCountryMap[it.contract.id],
                calculatedTime = calculatedTime,
            )
        }
    }

    private fun isRevenueLeakagePreventionEnabled(companyId: Long): Boolean =
        featureFlagService.feature(ENABLE_REVENUE_LEAKAGE_PREVENTION, mapOf("company" to companyId)).on
}
