package com.multiplier.payable.engine.collector.vas

import com.multiplier.core.payable.adapters.VasIncidentServiceAdapter
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataCollector
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.ProcessedIncidentCollectorInput
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.vas.FixedIncidentFeeDiscount
import com.multiplier.payable.engine.vas.IncidentFeeDiscount
import com.multiplier.payable.engine.vas.PercentageIncidentFeeDiscount
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.time.Instant

@Component
class IncidentFeeDiscountDataCollector(
    private val vasIncidentService: VasIncidentServiceAdapter,
    private val dataCollectorInputProcessor: DataCollectorInputProcessor<ProcessedIncidentCollectorInput>,
    private val payableItemStoreService: PayableItemStoreService,
    private val fixedIncidentFeeDiscountItemStoreNormalizer: FixedIncidentFeeDiscountPayableItemStoreNormalizer,
) : DataCollector {
    private companion object {
        private val log = KotlinLogging.logger {}
    }

    override fun getSupportedType(): LineItemType = LineItemType.VAS_INCIDENT_DISCOUNT

    override fun handle(command: InvoiceCommand) {
        val processedInput = dataCollectorInputProcessor.process(command)
        processedInput.lineItemType = getSupportedType()
        processedInput.originalTimestamp = Instant.now()
        log.info { "Collecting data itemType for: ${getSupportedType()} on transactionId: ${processedInput.transactionId}" }

        val incidentDiscounts = vasIncidentService.getIncidentFeeDiscounts(processedInput.transactionId)
        normalizeAndSave(incidentDiscounts, processedInput)
    }

    private fun normalizeAndSave(
        incidentDiscounts: List<IncidentFeeDiscount>,
        processedInput: ProcessedIncidentCollectorInput,
    ) {
        val payableItemStoreDtos =
            incidentDiscounts.map {
                when (it) {
                    is FixedIncidentFeeDiscount ->
                        fixedIncidentFeeDiscountItemStoreNormalizer.normalize(it, processedInput)

                    is PercentageIncidentFeeDiscount -> error("Percentage incident fee discount is not supported for now")

                    else -> error("Unsupported discount type: ${it.javaClass.name}")
                }
            }

        payableItemStoreService.saveAndIgnoreDuplication(payableItemStoreDtos)
    }
}
