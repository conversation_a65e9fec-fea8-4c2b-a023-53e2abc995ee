package com.multiplier.payable.engine.anomalydetector.request

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.invoice.database.InvoiceDtoMapper
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaInvoice
import com.multiplier.payable.engine.anomalydetector.provider.InvoiceAwareCompanyPayableProvider
import com.multiplier.payable.engine.anomalydetector.provider.NetsuiteInvoiceProvider
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class DefaultIADRequestBuilder(
    private val companyPayableRepository: JpaCompanyPayableRepository,
    private val invoiceDtoMapper: InvoiceDtoMapper,
    private val netsuiteInvoiceProvider: NetsuiteInvoiceProvider,
    private val invoiceAwareCompanyPayableProvider: InvoiceAwareCompanyPayableProvider,
    private val objectMapper: ObjectMapper,
) : IADRequestBuilder {
    companion object {
        private val log = KotlinLogging.logger {}
    }

    override fun build(command: InvoiceCommand): List<InvoiceAnomalyDetectorRequest> {
        log.info { "Building anomaly detector request for command=$command" }
        val jpaCompanyPayables = getCompanyPayables(command.transactionId)
        val jpaInvoices = extractInvoices(jpaCompanyPayables)

        val jpaInvoicesWithNetsuiteInfo = netsuiteInvoiceProvider.getByBatch(jpaInvoices)
        val companyPayablesWithNetsuiteInfo =
            invoiceAwareCompanyPayableProvider.get(jpaCompanyPayables, jpaInvoicesWithNetsuiteInfo)

        val invoiceDtos = invoiceDtoMapper.map(companyPayablesWithNetsuiteInfo, jpaInvoicesWithNetsuiteInfo)

        val whitelistContractIds = getWhitelistContractIds(command)

        return build(companyPayablesWithNetsuiteInfo, invoiceDtos, whitelistContractIds)
            .also {
                log.info { "Completed building anomaly detector request for command=$command, built IAD request=$it" }
            }
    }

    private fun getWhitelistContractIds(command: InvoiceCommand): Map<MonthYear, Set<Long>> {
        if (command.transactionType != TransactionType.FIRST_INVOICE) {
            return emptyMap()
        }

        val contextHolder =
            command.tracingContext[InvoiceCommand.FIRST_INVOICE_MANAGEMENT_FEE_CONTEXT_HOLDER]
                ?: return emptyMap()

        return try {
            val typeRef = object : TypeReference<Map<String, Map<String, Set<Long>>>>() {}
            val result: Map<String, Map<MonthYear, Set<Long>>> =
                objectMapper
                    .readValue(contextHolder, typeRef)
                    .mapValues { (_, value) -> value.mapKeys { MonthYear.parseMonthYear(it.key) } }
            result["annualPlanDiscountContractIds"] ?: emptyMap()
        } catch (e: Exception) {
            log.error(e) { "Error parsing trace $contextHolder, command = $command" }
            emptyMap()
        }
    }

    private fun getCompanyPayables(transactionId: String): List<JpaCompanyPayable> {
        val jpaCompanyPayables = companyPayableRepository.findByTransactionId(transactionId)
        require(jpaCompanyPayables.isNotEmpty()) { "No company payables found for transaction id $transactionId" }
        return jpaCompanyPayables
    }

    private fun extractInvoices(jpaCompanyPayables: List<JpaCompanyPayable>): List<JpaInvoice> {
        val jpaInvoices = jpaCompanyPayables.mapNotNull { it.invoice }
        require(jpaInvoices.isNotEmpty()) { "No invoices found for transaction id ${jpaCompanyPayables.first().transactionId}" }
        return jpaInvoices
    }

    private fun build(
        companyPayables: List<JpaCompanyPayable>,
        invoices: List<InvoiceDTO>,
        whitelistContractIds: Map<MonthYear, Set<Long>>,
    ): List<InvoiceAnomalyDetectorRequest> {
        val invoiceDtosByCompanyPayableId = invoices.associateBy { it.companyPayableId }

        return companyPayables.map { companyPayable ->
            val month = companyPayable.month
            val year = companyPayable.year
            if (month != null && year != null) {
                val monthYear = MonthYear(month, year)
                val cycleWhitelistContractIds =
                    whitelistContractIds[monthYear] ?: emptySet()
                InvoiceAnomalyDetectorRequest
                    .builder()
                    .payable(companyPayable)
                    .invoiceDTO(invoiceDtosByCompanyPayableId[companyPayable.id])
                    .whitelistContractIds(cycleWhitelistContractIds)
                    .build()
            }

            InvoiceAnomalyDetectorRequest
                .builder()
                .payable(companyPayable)
                .invoiceDTO(invoiceDtosByCompanyPayableId[companyPayable.id])
                .build()
        }
    }
}
