package com.multiplier.payable.engine.collector.severance

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataCollector
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.ProcessedCollectorInput
import com.multiplier.payable.engine.collector.data.DefaultProcessedCollectorInput
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.engine.severance.Severance
import com.multiplier.payable.engine.severance.EndedContractSeveranceRefundService
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class SeveranceRefundDataCollectorHandler(
    private val dataCollectorInputProcessor: DataCollectorInputProcessor<DefaultProcessedCollectorInput>,
    private val endedContractSeveranceRefundService: EndedContractSeveranceRefundService,
    private val payableItemStoreService: PayableItemStoreService,
    private val severancePayableItemStoreNormalizer: SeverancePayableItemStoreNormalizer
) : DataCollector {

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    override fun getSupportedType(): LineItemType = LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL_REFUND


    override fun handle(command: InvoiceCommand) {
        val processedInput = dataCollectorInputProcessor.process(command)

        logger.info {
            "Handle processed input for transactionId: ${processedInput.transactionId}, " +
                    "companyIds: ${processedInput.companyIds}, " +
                    "lineItemType: ${getSupportedType()}"
        }

        val allSeverances = endedContractSeveranceRefundService.getSeverancesToRefund(
            processedInput.companyIds.toList()
        )
        val nonZeroSeverances = allSeverances.filterNot { it.severance == 0.0 }.map { severance ->
            severance.copy(
                severance = severance.severance * -1
            )
        }
        normalizeAndSave(nonZeroSeverances, processedInput)
    }

    private fun normalizeAndSave(
        severances: List<Severance>,
        processedInput: ProcessedCollectorInput,
    ) {

        logger.info(
            "Normalizing and saving data for " +
                    "transactionId = ${processedInput.transactionId} " +
                    "itemType = ${getSupportedType()} "
        )

        val payableItemStoreDtos =
            severances.map { severancePayableItemStoreNormalizer.normalize(it, processedInput, getSupportedType()) }

        payableItemStoreService.saveAndIgnoreDuplication(payableItemStoreDtos)

    }
}
