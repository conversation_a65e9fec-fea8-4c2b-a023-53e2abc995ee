package com.multiplier.payable.engine.reconciler.data.item

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.AnnualPayableItemMapper
import com.multiplier.payable.engine.payableitem.JpaPayableItemStoreRepository
import org.springframework.stereotype.Service

/**
 * Data provider for Annual Plan AOR items.
 * Extends the abstract base class for annual plan providers.
 */
@Service
class AnnualPlanAORItemStoreDataProvider(
    repository: JpaPayableItemStoreRepository,
    mapper: AnnualPayableItemMapper,
) : AbstractAnnualPlanItemStoreDataProvider(repository, mapper) {
    override fun transactionType(): TransactionType {
        return TransactionType.ANNUAL_PLAN_AOR_INVOICE
    }
}
