package com.multiplier.payable.engine.vas

import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.types.CountryCode
import java.time.LocalDateTime

interface HasContractId {
    val contractId: Long
}

sealed interface Incident {
    val id: Long
    val amount: Amount
    val description: String
    val incidentTime: LocalDateTime
    val type: IncidentType
    val countryCode: CountryCode
    val chargePolicy: IncidentChargePolicy?
}

data class AssetIncident(
    override val id: Long,
    override val amount: Amount,
    override val description: String,
    override val incidentTime: LocalDateTime,
    override val type: IncidentType,
    override val contractId: Long,
    override val countryCode: CountryCode,
    override val chargePolicy: IncidentAmountChargePolicy?,
) : Incident,
    HasContractId

data class LogisticsIncident(
    override val id: Long,
    override val amount: Amount,
    override val description: String,
    override val incidentTime: LocalDateTime,
    override val type: IncidentType,
    override val countryCode: CountryCode,
    override val chargePolicy: IncidentAmountChargePolicy?,
) : Incident

data class StorageIncident(
    override val id: Long,
    override val amount: Amount,
    override val description: String,
    override val incidentTime: LocalDateTime,
    override val type: IncidentType,
    override val countryCode: CountryCode,
    override val chargePolicy: IncidentAmountChargePolicy?,
) : Incident

data class OthersIncident(
    override val id: Long,
    override val amount: Amount,
    override val description: String,
    override val incidentTime: LocalDateTime,
    override val type: IncidentType,
    override val countryCode: CountryCode,
    override val chargePolicy: IncidentAmountChargePolicy?,
) : Incident

data class LegalIncident(
    override val id: Long,
    override val amount: Amount,
    override val description: String,
    override val incidentTime: LocalDateTime,
    override val type: IncidentType,
    override val countryCode: CountryCode,
    override val chargePolicy: IncidentQuantityChargePolicy?,
) : Incident

enum class IncidentType {
    LAPTOP,
    MONITOR,
    ACCESSORIES,
    PICKUP_DELIVERY,
    STORAGE,
    CONTRACT_CUSTOMISATION,
    LEGAL_CONSULTATION,
    OTHERS_SERVICE_FEE,
    OTHERS_SERVICE,
    EXPAT_SETUP,
}
