package com.multiplier.payable.engine.anomalydetector.rules.processor

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.AnomalyResultType
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.rules.DetectionRuleType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KLogger
import mu.KotlinLogging

/**
 * Base class for anomaly detection rules.
 * Provides common functionality and processing logic.
 * Individual saving has been removed - results are now collected and saved consolidated by AnomalyDetectionRulesProcessor.
 */
open class BaseAnomalyDetectionRule(
    protected val featureFlagService: FeatureFlagService,
) : AnomalyDetectionRule {
    companion object {
        private val log = KotlinLogging.logger {}
    }

    /**
     * The rule name, used for logging
     */
    open val ruleName: String = "DefaultRule"

    /**
     * The feature flag name to check if the rule is enabled
     */
    open val featureFlagName: String = "DEFAULT_FLAG"

    /**
     * The detector type for this rule
     * Default placeholder - concrete classes should override this
     */
    open val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.DEFAULT_RULE

    /**
     * The detection rule type for this rule
     */
    override val type: DetectionRuleType = DetectionRuleType.DEFAULT_RULE

    /**
     * The logger to use
     */
    protected open val logger: KLogger = log

    /**
     * Detects anomalies in the given request.
     * This implementation handles common checks and delegates to doDetect for rule-specific logic.
     * Note: Individual saving has been removed - results are now collected and saved consolidated by AnomalyDetectionRulesProcessor.
     */
    override fun detect(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult =
        try {
            val result =
                when {
                    // Case 1: Feature flag is disabled
                    !isFeatureFlagEnabled() -> {
                        logger.info { "Skipping $ruleName anomaly detection as feature flag is disabled" }
                        createWarningResult(listOf("Rule '$ruleName' is disabled by feature flag"))
                    }

                    // Case 2: Invoice data or company payable is null
                    !isRequestValid(request) -> {
                        logger.info { "Invoice data or company payable is null, skipping check" }
                        createWarningResult(listOf("Invoice data or company payable is null - validation skipped"))
                    }

                    // Case 3: Perform the actual check
                    else -> {
                        logger.info { "Detecting $ruleName anomalies" }
                        validateRule(command, request)
                    }
                }

            result
        } catch (e: Exception) {
            logger.error(e) { "$ruleName anomaly detection failed" }
            createExceptionResult(e)
        }

    /**
     * Rule-specific detection logic.
     * Subclasses should override this method to perform their specific checks.
     */
    protected open fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult {
        // Default implementation - returns success
        // Subclasses should override this method
        return createSuccessResult(listOf("Default validation passed - no specific rule implementation"))
    }

    /**
     * Checks if the request data is valid for processing.
     * @param request The request to check
     * @return True if the request is valid, false otherwise
     */
    protected open fun isRequestValid(request: InvoiceAnomalyDetectorRequest): Boolean =
        request.invoiceDTO != null && request.payable != null

    /**
     * Checks if the feature flag is enabled.
     * @return True if the feature flag is enabled, false otherwise
     */
    protected open fun isFeatureFlagEnabled(): Boolean {
        val featureResult = featureFlagService.feature(featureFlagName, mapOf())
        val isEnabled = featureResult.on

        logger.debug { "Feature flag check: ruleName='$ruleName', flagName='$featureFlagName', enabled=$isEnabled" }

        if (!isEnabled) {
            logger.info { "Rule '$ruleName' is disabled by feature flag '$featureFlagName'" }
        }

        return isEnabled
    }

    /**
     * Creates a success result with optional messages.
     * @param messages The success messages (optional)
     * @return A success result
     */
    protected fun createSuccessResult(messages: List<String> = emptyList()): AnomalyDetectionResult =
        AnomalyDetectionResult(
            success = true,
            messages = messages,
            severity = AnomalyResultType.SUCCESS,
        )

    /**
     * Creates a failure result with the given messages.
     * @param messages The error messages
     * @return A failure result
     */
    protected fun createFailureResult(messages: List<String>): AnomalyDetectionResult =
        AnomalyDetectionResult(
            success = false,
            messages = messages,
            severity = AnomalyResultType.ERROR,
        )

    /**
     * Creates a warning result with the given messages.
     * @param messages The warning messages
     * @return A warning result
     */
    protected fun createWarningResult(messages: List<String>): AnomalyDetectionResult =
        AnomalyDetectionResult(
            success = true, // WARNING doesn't block processing
            messages = messages,
            severity = AnomalyResultType.WARN,
        )

    /**
     * Creates a failure result for an exception.
     * @param e The exception
     * @return A failure result
     */
    private fun createExceptionResult(e: Exception): AnomalyDetectionResult =
        AnomalyDetectionResult(
            success = false,
            messages = listOf("Exception occurred during $ruleName anomaly detection: ${e.localizedMessage}"),
            severity = AnomalyResultType.ERROR,
        )


}
