package com.multiplier.payable.engine.reconciler.descriptionbuilder

import com.multiplier.core.payable.adapters.api.LineItemType
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Service
class FundingPayableItemDescriptionBuilder(
    private val amountFormatter: AmountFormatter,
) : PayableItemDescriptionBuilder {

    override val lineItemType: LineItemType
        get() = LineItemType.GP_FUND_REQUEST

    override fun build(context: PayableItemDescriptionBuilderContext): String {
        val monthYear = context.monthYear
        val formatter = DateTimeFormatter.ofPattern("MMM''yy")
        val dateDescription =
            LocalDate.of(monthYear.year, monthYear.month, 1).format(formatter)
        val formattedAmount = amountFormatter.format(context.amountInBaseCurrency)
        return "$dateDescription ${context.cycle} Payroll Cost " +
                "- ${context.contractId} - ${context.countryName} - ${context.currencyCode} $formattedAmount"
    }
}
