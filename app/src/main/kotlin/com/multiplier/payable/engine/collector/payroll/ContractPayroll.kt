package com.multiplier.payable.engine.collector.payroll

import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.types.CurrencyCode
import java.time.LocalDate

data class ContractPayroll(
    val companyId: Long,
    val contractId: Long,
    val amountTotalCost: Double,
    val totalExpenseAmount: Double?,
    val totalSeveranceAccruals: Double?,
    val currencyCode: CurrencyCode,
    val countryCode: String,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val type: ContractType,
    val isOffCycle: Boolean,
    val payCycleCount: Int?,
    val fetchedTime: Long,
    val payrollCycleId: Long? = null,
    val payComponents: Map<String, Double> = emptyMap(),
    val hasBillingException : Boolean? = false,
)
