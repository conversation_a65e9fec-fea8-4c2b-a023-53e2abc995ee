package com.multiplier.payable.engine.reconciler

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorage
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorageContext
import com.multiplier.payable.engine.reconciler.data.invoice.InvoiceDataProviderFactory
import com.multiplier.payable.engine.reconciler.data.item.ItemStoreDataProviderFactory
import com.multiplier.payable.engine.reconciler.diff.reconciler.InvoiceDiffReconcilerFactory
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

@Service
class VasBgvInvoiceDataReconciler(
    private val templateProvider: TransactionTemplateProvider,
    private val invoiceDataProviderFactory: InvoiceDataProviderFactory,
    private val itemStoreDataProviderFactory: ItemStoreDataProviderFactory,
    private val invoiceDiffReconcilerFactory: InvoiceDiffReconcilerFactory,
    @Qualifier("vas-bgv-invoice") private val companyPayableStorage: CompanyPayableStorage,
) : DataReconciler {
    companion object {
        private val log = KotlinLogging.logger { }
    }

    override val transactionType: TransactionType
        get() = TransactionType.VAS_BACKGROUND_VERIFICATION_INVOICE

    override fun handle(command: InvoiceCommand) {
        log.info("Processing vas bgv invoice items reconciler for input: $command")
        val template = templateProvider.findTemplateFor(command.transactionType, command.companyId)

        // Fetch previously invoiced items for deduplication
        val invoicedItems = invoiceDataProviderFactory.get(transactionType)
            .fetchAndAggregateInvoiceItems(command)

        // Fetch latest items from data collectors
        val latestItems = itemStoreDataProviderFactory
            .get(transactionType)
            .fetchLatest(command, template.lineItemTypes)
        log.info("Latest items: ${latestItems.size}, Previously invoiced items: ${invoicedItems.size}")

        // Use diff reconciler to calculate items that need to be billed (deduplication)
        val diffPayableItems = invoiceDiffReconcilerFactory.get(transactionType)
            .reconcileAndValidate(command, template, invoicedItems, latestItems)

        val filteredLineItems = filterZeroAmountLineItems(diffPayableItems)

        log.info("Creating ${filteredLineItems.size} new payable items for companyId: ${command.companyId} transactionId: ${command.transactionId}")

        val storageContext =
            CompanyPayableStorageContext(
                transactionId = command.transactionId,
                invoiceDate = command.transactionDate,
                monthYear = command.getMonthYear(),
                items = filteredLineItems,
                companyId = command.companyId,
                cycle = command.cycle,
                cycleDuration = command.dateRange,
                transactionType = transactionType,
                command = command,
            )
        companyPayableStorage.exchangeAndStore(storageContext)
        log.info(
            "Complete storing ${filteredLineItems.size} payable items for" +
                " companyId: ${command.companyId}, transactionId: ${command.transactionId}",
        )
    }

    private fun filterZeroAmountLineItems(items: List<PayableItem>): List<PayableItem> {
        return items.filter { it.amountInBaseCurrency != 0.0 }
    }
}
