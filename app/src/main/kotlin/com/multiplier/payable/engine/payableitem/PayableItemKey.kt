package com.multiplier.payable.engine.payableitem

import com.multiplier.payable.engine.domain.aggregates.AnnualSeatPaymentTerm
import java.time.LocalDate

data class PayableItemKey(
    val month: Int? = null,
    val year: Int? = null,
    val itemType: String,
    val contractId: Long? = null,
    val annualSeatPaymentTerm: AnnualSeatPaymentTerm? = null,
    val companyId: Long? = null,
    val periodStartDate: LocalDate? = null,
    val periodEndDate: LocalDate? = null,
    val entityId: Long? = null,
    val billId: String? = null,
    val payrollCycleId: Long? = null,
)