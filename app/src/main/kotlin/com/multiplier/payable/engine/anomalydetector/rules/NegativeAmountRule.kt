package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.rules.processor.BaseAnomalyDetectionRule
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.springframework.stereotype.Component

/**
 * Checks if an invoice has a negative amount, indicating it's a credit note
 */
@Component
class NegativeAmountRule(
    featureFlagService: FeatureFlagService,
) : BaseAnomalyDetectionRule(featureFlagService) {
    override val ruleName: String = "NegativeAmount"
    override val featureFlagName: String = "ENABLE_CREDIT_NOTE_CHECK"
    override val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.NEGATIVE_AMOUNT

    override val type: DetectionRuleType = DetectionRuleType.NEGATIVE_AMOUNT

    /**
     * Rule-specific detection logic.
     */
    override fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult {
        // Get the invoice data (we know it's not null because isRequestValid was called)
        val companyPayable = request.payable!!

        // Get the invoice amount
        val invoiceAmount = companyPayable.totalAmount

        // Track any anomalies found
        val anomalies = mutableListOf<String>()

        // Check if the invoice amount is negative
        if (invoiceAmount < 0 && request.invoiceDTO.totalAmount < 0) {
            val message = "Invoice amount is negative. Credit notes need manual intervention"
            anomalies.add(message)
            logger.error { message }
        }

        // Return appropriate result based on anomalies
        return if (anomalies.isNotEmpty()) {
            createFailureResult(anomalies)
        } else {
            logger.info { "No $ruleName anomalies detected" }
            createSuccessResult(listOf("Invoice amount is positive - no negative amount anomalies detected"))
        }
    }
}
