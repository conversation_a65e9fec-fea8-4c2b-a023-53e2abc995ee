package com.multiplier.payable.engine.inspector

import com.multiplier.core.payable.repository.JpaTransactionCommandLogRepository
import com.multiplier.core.payable.repository.filter.TransactionCommandLogFilter
import com.multiplier.core.payable.repository.specification.TransactionCommandLogSpecifications
import com.multiplier.payable.types.FinancialTransactionFilter
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class FinancialTransactionSearchService(
    private val transactionCommandRepository: JpaTransactionCommandLogRepository
) {
    private val log = KotlinLogging.logger {}

    /**
     * Search for financial transactions based on the provided filter criteria.
     * Returns unique transaction IDs that match the filter.
     */
    fun search(filter: FinancialTransactionFilter): List<String> {
        log.info { "Searching for financial transactions with filter: $filter" }

        val specFilter = TransactionCommandLogFilter(
            filter.transactionIds?.toList(),
            filter.transactionTypes?.map { it.name }?.toList(),
            filter.payrollCycleIds?.map { it.toLong() }?.toList()
        )

        log.info {
            "Search parameters - transactionIds: ${specFilter.transactionIds}, " +
                    "transactionTypes: ${specFilter.transactionTypes}, " +
                    "payrollCycleIds: ${specFilter.payrollCycleIds}"
        }

        val spec = TransactionCommandLogSpecifications.fromFilter(specFilter)
        val commandLogs = transactionCommandRepository.findAll(spec)

        log.info { "Found ${commandLogs.size} command logs matching the filter" }

        // Extract unique transaction IDs
        return commandLogs.map { it.transactionId }.distinct().also {
            log.info { "Found ${it.size} unique transaction IDs: $it" }
        }
    }
}
