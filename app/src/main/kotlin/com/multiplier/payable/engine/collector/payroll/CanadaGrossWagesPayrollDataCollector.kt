package com.multiplier.payable.engine.collector.payroll

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.DefaultProcessedCollectorInput
import org.springframework.stereotype.Component

@Component
internal class CanadaGrossWagesPayrollDataCollector(
    dataCollectorInputProcessor: DataCollectorInputProcessor<DefaultProcessedCollectorInput>,
    contractPayrollService: ContractPayrollService,
    contractPayrollItemStoreService: ContractPayrollItemStoreService,
) : CanadaPayrollDataCollector(dataCollectorInputProcessor, contractPayrollService, contractPayrollItemStoreService) {
    override fun getSupportedType(): LineItemType {
        return LineItemType.CANADA_GROSS_WAGES
    }

    override fun getPayrollAttributes(): List<PayrollAttributeKey> {
        return listOf(PayrollAttributeKey.CANADA_GROSS_WAGES)
    }
}