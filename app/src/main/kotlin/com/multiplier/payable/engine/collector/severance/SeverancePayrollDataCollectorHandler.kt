package com.multiplier.payable.engine.collector.severance

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.ProcessedCollectorInput
import com.multiplier.payable.engine.collector.data.DefaultProcessedCollectorInput
import com.multiplier.payable.engine.collector.payroll.*
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.domain.aggregates.MonthYearDuration
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class SeverancePayrollDataCollectorHandler(
    private val contractPayrollService: ContractPayrollService,
    private val contractPayrollItemStoreService: ContractPayrollItemStoreService,
) {
    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    fun handle(processedInput: ProcessedCollectorInput, lineItemType: LineItemType) {

        require(processedInput is DefaultProcessedCollectorInput) {
            "Non-supported collector processed input for $lineItemType"
        }

        logger.info {
            "Handle processed input for transactionId: ${processedInput.transactionId}, " +
                    "companyIds: ${processedInput.companyIds}, " +
                    "lineItemType: $lineItemType"
        }

        val monthYearDuration = processedInput.timeQueryDuration
        val monthYear = processedInput.timeQueryDuration.from.invoiceMonthYear()
        val companyIds = processedInput.companyIds
        val month = monthYear.month
        val year = monthYear.year

        val contractPayrolls = contractPayrollService.getContractPayrolls(
            companyIds = companyIds.toList(),
            month = month,
            year = year
        )
            .filter { it.type == ContractType.EMPLOYEE && it.isOffCycle.not() } // In payrollData collector this condition is there so added here too.

        normalizeAndSave(processedInput.transactionId, contractPayrolls, monthYearDuration, lineItemType)
    }

    fun normalizeAndSave(
        transactionId: String,
        contractPayrolls: List<ContractPayroll>,
        monthYearDuration: MonthYearDuration,
        lineItemType: LineItemType,
    ) {
        contractPayrollItemStoreService.normalizeAndSaveForCompanyPayrolls(
            ContractPayrollsItemStoreInput(
                transactionId = transactionId,
                contractPayrolls = contractPayrolls,
                monthYearDuration = monthYearDuration,
                itemType = lineItemType,
                isBillable = { it.totalSeveranceAccruals != null },
                amountCost = { it.totalSeveranceAccruals ?: 0.0 }
            )
        )
    }
}