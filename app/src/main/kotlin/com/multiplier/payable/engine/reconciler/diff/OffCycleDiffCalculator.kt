package com.multiplier.payable.engine.reconciler.diff

import com.multiplier.payable.engine.payableitem.PayableItem
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class OffCycleDiffCalculator : DiffCalculator<List<PayableItem>> {

    companion object {
        val log: Logger = LoggerFactory.getLogger(this::class.java.name)
    }

    override fun calculate(
        first: List<PayableItem>,
        second: List<PayableItem>,
    ): Diff<List<PayableItem>> {
        val alreadyInvoicedPayrollCycleIds = second.map { it.payrollCycleId }.toSet()
        val payablesToBeCreated = first.filter { newInvoiceItem ->
            newInvoiceItem.payrollCycleId !in alreadyInvoicedPayrollCycleIds
        }
        val toBeUpdated = mutableListOf<PayableItem>()
        val toBeDeleted = mutableListOf<PayableItem>()
        return Diff(toBeDeleted, toBeUpdated, payablesToBeCreated)
    }
}
