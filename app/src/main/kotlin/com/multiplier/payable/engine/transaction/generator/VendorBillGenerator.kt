package com.multiplier.payable.engine.transaction.generator

import com.multiplier.core.payable.adapters.CountryServiceAdapter
import com.multiplier.core.payable.adapters.netsuite.client.NetsuiteMappings
import com.multiplier.core.payable.adapters.netsuite.dto.VendorBillDTO
import com.multiplier.core.payable.adapters.netsuite.dto.VendorBillItemDTO
import com.multiplier.core.payable.repository.ExpenseBillRepository
import com.multiplier.core.payable.repository.filter.ExpenseBillFilter
import com.multiplier.core.payable.repository.model.ExpenseBillStatus
import com.multiplier.core.payable.service.ExpenseBillFilterService
import com.multiplier.core.payable.service.vendorbill.itemstore.ParsedDate
import com.multiplier.country.schema.Country
import com.multiplier.payable.engine.domain.entities.FinancialTransaction
import com.multiplier.payable.engine.domain.entities.VendorBillTransaction
import com.multiplier.payable.engine.exception.ExpenseBillGenerationErrorCode
import com.multiplier.payable.engine.exception.ExpenseBillGenerationException
import com.multiplier.payable.engine.reconciler.multiplierPayable.dto.MultiplierPayableDTO
import com.multiplier.payable.engine.reconciler.multiplierPayable.storage.MultiplierPayableStorageService
import com.multiplier.payable.engine.vendorBill.api.VendorBillAdapter
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import java.time.LocalDate

private val log = KotlinLogging.logger { }

data class VendorBillGenerationResult(
    val externalId: String,
    val vendorId: String,
    val accountId: String,
    val departmentId: String,
    val locationId: String? = "",
    val currency: String,
    val memo: String,
    val countryCode: String?,
    val items: List<VendorBillItemDTO>
)

@Service
@Qualifier("vendorBillGenerator")
class VendorBillGenerator(
    private val vendorBillAdapter: VendorBillAdapter,
    private val multiplierPayableStorageService: MultiplierPayableStorageService,
    private val countryServiceAdapter: CountryServiceAdapter,
    private val expenseBillFilterService: ExpenseBillFilterService,
    private val expenseBillRepository: ExpenseBillRepository,
) : FinancialTransactionGenerator {

    override fun generate(financialTransaction: FinancialTransaction): String {
        check(financialTransaction is VendorBillTransaction) { "Can only generate type VendorBill" }
        log.info {
            "Generating Vendor Bill for payrollCycleId: ${financialTransaction.payrollCycleId}, " +
                    "CountryCode: ${financialTransaction.countryCode} and Vendor :" +
                    " ${financialTransaction.vendorBillType}"
        }

        val result = createOrUpdateVendorBill(financialTransaction)
        return result.externalId
    }

    fun createOrUpdateVendorBill(financialTransaction: VendorBillTransaction): VendorBillGenerationResult {
        val multiplierPayables = multiplierPayableStorageService
            .findByTransactionId(financialTransaction.transactionId)

        if (multiplierPayables.isEmpty()) {
            log.error { "No payable found for transactionId ${financialTransaction.transactionId}" }
            throw ExpenseBillGenerationException(
                errorCode = ExpenseBillGenerationErrorCode.MULTIPLIER_PAYABLE_INVALID_REQUEST,
                "Multiplier payable does not exist for this transactionId ${financialTransaction.transactionId}",
                null
            )
        }
        val multiplierPayable =
            multiplierPayables.firstOrNull { it.lineItemType == financialTransaction.vendorBillType }
                ?: throw ExpenseBillGenerationException(
                    errorCode = ExpenseBillGenerationErrorCode.MULTIPLIER_PAYABLE_INVALID_REQUEST,
                    "No matching payable found for vendor bill type ${financialTransaction.vendorBillType}", null
                )
        val vendorBillDTO = createVendorBillDTO(financialTransaction, multiplierPayable)
        log.info("Netsuite Vendor bill DTO: $vendorBillDTO")
        val filter = ExpenseBillFilter(
            payrollCycleId = financialTransaction.payrollCycleId,
            vendorType = financialTransaction.vendorBillType
        )
        val existingVendorBill = expenseBillFilterService.findByFilter(filter).firstOrNull()
        val response = if (existingVendorBill != null) {
            log.info("Updating existing Vendor Bill with externalid ${existingVendorBill.externalId}")
            vendorBillDTO.externalId = existingVendorBill.externalId
            vendorBillAdapter.update(vendorBillDTO)
        } else {
            log.info("Creating new Vendor Bill")
            vendorBillAdapter.create(vendorBillDTO)
        }
        log.info { "Netsuite Vendor bill response external id: ${response.id}" }

        return VendorBillGenerationResult(
            externalId = response.id,
            vendorId = vendorBillDTO.vendorId,
            accountId = vendorBillDTO.accountId,
            departmentId = vendorBillDTO.departmentId,
            locationId = vendorBillDTO.locationId,
            currency = financialTransaction.currency.name,
            memo = vendorBillDTO.reference,
            countryCode = financialTransaction.countryCode?.name,
            items = vendorBillDTO.items
        )
    }

    private fun createVendorBillDTO(
        financialTransaction: VendorBillTransaction,
        multiplierPayable: MultiplierPayableDTO
    ): VendorBillDTO {
        val vendorBillDTO = VendorBillDTO.builder()
            .transactionId(financialTransaction.transactionId)
            .transactionDate(financialTransaction.date.toLocalDate())
            .dueDate(financialTransaction.dueDate.toLocalDate())
            .vendorId(NetsuiteMappings.getVendorId(financialTransaction.vendorBillType))
            .accountId(NetsuiteMappings.getWagesPayableAccountId())
            .departmentId(NetsuiteMappings.getPayrollDepartmentId())
            .locationId(NetsuiteMappings.getLocationId(financialTransaction.countryCode.toString()))
            .currency(financialTransaction.currency)
            .reference(financialTransaction.reference)
            .payrollCycleId(financialTransaction.payrollCycleId)
            .countryCode(financialTransaction.countryCode?.name)
            .status(ExpenseBillStatus.valueOf(multiplierPayable.status.toString()))
            .build()

        if (multiplierPayable.items.isNotEmpty()) {
            val items = multiplierPayable.items.map { item ->
                VendorBillItemDTO.builder()
                    .accountId(NetsuiteMappings.getExpenseListAccountId())
                    .amount(item.amount)
                    .memo(item.description)
                    .contractId(item.contractId.toString())
                    .employeeName(item.employeeName)
                    .payrollStartDate(convertParsedDateToLocalDate(item.payrollMetaData.startDate))
                    .payrollEndDate(convertParsedDateToLocalDate(item.payrollMetaData.endDate))
                    .currency(item.currencyCode)
                    .countryName(
                        countryServiceAdapter
                            .getCountryNameByCode(Country.GrpcCountryCode.valueOf(item.countryCode.toString()))
                    )
                    .build()
            }
            vendorBillDTO.items = items
        } else {
            throw ExpenseBillGenerationException(
                errorCode = ExpenseBillGenerationErrorCode.MULTIPLIER_PAYABLE_INVALID_REQUEST,
                message = "Vendor Bill API requires at least one item",
                metadata = null
            )
        }

        return vendorBillDTO
    }

    private fun convertParsedDateToLocalDate(parsedDate: ParsedDate): LocalDate {
        return LocalDate.of(parsedDate.year, parsedDate.month, parsedDate.day)
    }
}