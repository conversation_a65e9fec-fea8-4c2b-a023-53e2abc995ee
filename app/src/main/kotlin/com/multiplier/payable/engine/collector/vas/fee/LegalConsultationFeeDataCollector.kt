package com.multiplier.payable.engine.collector.vas.fee

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.vas.AbstractVasIncidentManagementFeeDataCollector
import com.multiplier.payable.engine.collector.vas.VasIncidentManagementFeeCollector
import com.multiplier.payable.engine.vas.IncidentType
import org.springframework.stereotype.Component

@Component
class LegalConsultationFeeDataCollector(
    vasIncidentManagementFeeCollector: VasIncidentManagementFeeCollector,
) : AbstractVasIncidentManagementFeeDataCollector(vasIncidentManagementFeeCollector) {
    override fun getSupportedType(): LineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE

    override fun getIncidentType(): IncidentType = IncidentType.LEGAL_CONSULTATION
}
