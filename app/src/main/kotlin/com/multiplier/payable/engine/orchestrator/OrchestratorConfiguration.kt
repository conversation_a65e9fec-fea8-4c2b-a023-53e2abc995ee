package com.multiplier.payable.engine.orchestrator

import com.multiplier.payable.engine.TransactionCommand
import com.multiplier.payable.engine.TransactionCommandHandler
import com.multiplier.payable.engine.TransactionStatus
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.lock.distributed.DistributedLockProvider
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class OrchestratorConfiguration(

    @Qualifier("data-collector-factory")
    private val dataCollector: TransactionCommandHandler<TransactionCommand>,

    @Qualifier("data-reconciler-factory")
    private val dataReconciler: TransactionCommandHandler<TransactionCommand>,

    @Qualifier("transaction-handler-factory")
    private val invoiceGenerator: TransactionCommandHandler<TransactionCommand>,

    @Qualifier("anomaly-detector-factory")
    private val anomalyDetector: TransactionCommandHandler<TransactionCommand>,

    @Qualifier("isr-generator")
    private val isrGenerator: TransactionCommandHandler<TransactionCommand>,

    @Qualifier("expense-bill-data-collector-factory")
    private val expenseBillDataCollector: TransactionCommandHandler<TransactionCommand>,

    @Qualifier("expense-bill-reconciler-factory")
    private val expenseBillReconciler: TransactionCommandHandler<TransactionCommand>,

    @Qualifier("expense-bill-generator-factory")
    private val expenseBillGenerator: TransactionCommandHandler<TransactionCommand>,

    private val distributedLockProvider: DistributedLockProvider,

    private val configurationProperties: InvoiceOrchestratorConfigurationProperties,
) {

    @Bean
    fun stateMachineSupplier(): (TransactionCommand) -> StateMachine {
        return {
            when (it.transactionType) {
                TransactionType.FIRST_INVOICE -> {
                    firstInvoiceStateMachine()
                }

                TransactionType.SECOND_INVOICE -> {
                    secondInvoiceStateMachine()
                }

                TransactionType.VENDOR_BILL -> {
                    vendorBillStateMachine()
                }

                TransactionType.ORDER_FORM_ADVANCE -> {
                    orderFormAdvanceStateMachine()
                }

                TransactionType.ANNUAL_PLAN_AOR_INVOICE -> {
                    aorAnnualPlanInvoiceStateMachine()
                }

                TransactionType.VAS_INCIDENT_INVOICE -> {
                    vasBgvInvoiceStateMachine()
                }

                TransactionType.VAS_BACKGROUND_VERIFICATION_INVOICE -> {
                    vasBgvInvoiceStateMachine()
                }

                else -> {
                    commonStateMachine()
                }
            }
        }
    }

    @Bean
    @Qualifier("common")
    fun commonStateMachine(): StateMachine {
        val stateMachine = SimpleStateMachine()
        registerCommonTransitions(stateMachine)
        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.INVOICE_GENERATING,
            toState = TransactionStatus.DONE
        ) { }

        return stateMachine
    }

    @Bean
    @Qualifier("orderFormAdvance")
    fun orderFormAdvanceStateMachine(): StateMachine {
        val stateMachine = SimpleStateMachine()
        registerCommonTransitions(stateMachine)
        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.INVOICE_GENERATING,
            toState = TransactionStatus.DONE
        ) { }

        return stateMachine
    }

    @Bean
    @Qualifier("vasBgvInvoice")
    fun vasBgvInvoiceStateMachine(): StateMachine {
        val stateMachine = SimpleStateMachine()
        registerCommonTransitions(stateMachine)
        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.INVOICE_GENERATING,
            toState = TransactionStatus.DONE
        ) { }

        return stateMachine
    }

    @Bean
    @Qualifier("aorAnnualPlanInvoice")
    fun aorAnnualPlanInvoiceStateMachine(): StateMachine {
        val stateMachine = SimpleStateMachine()
        registerCommonTransitions(stateMachine)

        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.INVOICE_GENERATING,
            toState = TransactionStatus.ANOMALY_DETECTING
        ) { anomalyDetector.handle(it) }

        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.ANOMALY_DETECTING,
            toState = TransactionStatus.DONE,
        ) { }

        stateMachine.register(
            action = TransactionAction.STOP,
            fromState = TransactionStatus.ANOMALY_DETECTING,
            toState = TransactionStatus.ERROR
        ) { }

        return LockingStateMachine(
            baseStateMachine = stateMachine,
            lockSupplier = {
                distributedLockProvider.getLock(
                    nameSpace = "aor-ap-invoice",
                    key = (it as InvoiceCommand).companyId.toString(),
                )
            },
            leaseTime = configurationProperties.aorAnnualPlanInvoice.leaseTime,
        )
    }

    @Bean
    @Qualifier("firstInvoice")
    fun firstInvoiceStateMachine(): StateMachine {
        val stateMachine = SimpleStateMachine()
        registerCommonTransitions(stateMachine)

        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.INVOICE_GENERATING,
            toState = TransactionStatus.ANOMALY_DETECTING
        ) { anomalyDetector.handle(it) }

        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.ANOMALY_DETECTING,
            toState = TransactionStatus.DONE
        ) { }

        stateMachine.register(
            action = TransactionAction.STOP,
            fromState = TransactionStatus.ANOMALY_DETECTING,
            toState = TransactionStatus.ERROR,
            trigger = { }
        )

        return stateMachine
    }

    @Bean
    @Qualifier("secondInvoice")
    fun secondInvoiceStateMachine(): StateMachine {
        val stateMachine = SimpleStateMachine()
        registerCommonTransitions(stateMachine)
        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.INVOICE_GENERATING,
            toState = TransactionStatus.ANOMALY_DETECTING
        ) { anomalyDetector.handle(it) }

        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.ANOMALY_DETECTING,
            toState = TransactionStatus.ISR_GENERATING,
        ) { isrGenerator.handle(it) }

        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.ISR_GENERATING,
            toState = TransactionStatus.DONE
        ) { }

        stateMachine.register(
            action = TransactionAction.STOP,
            fromState = TransactionStatus.ANOMALY_DETECTING,
            toState = TransactionStatus.ERROR
        ) { }

        stateMachine.register(
            action = TransactionAction.STOP,
            fromState = TransactionStatus.ISR_GENERATING,
            toState = TransactionStatus.ERROR
        ) { }

        return stateMachine
    }

    @Bean
    @Qualifier("vendorBill")
    fun vendorBillStateMachine(): StateMachine {
        val stateMachine = SimpleStateMachine()

        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.INIT,
            toState = TransactionStatus.DATA_COLLECTING
        ) { expenseBillDataCollector.handle(it) }

        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.DATA_COLLECTING,
            toState = TransactionStatus.DATA_RECONCILING
        ) { expenseBillReconciler.handle(it) }

        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.DATA_RECONCILING,
            toState = TransactionStatus.EXPENSE_BILL_GENERATING
        ) { expenseBillGenerator.handle(it) }

        stateMachine.register(
            action = TransactionAction.STOP,
            fromState = TransactionStatus.DATA_COLLECTING,
            toState = TransactionStatus.ERROR,
            trigger = rollBack()
        )

        stateMachine.register(
            action = TransactionAction.STOP,
            fromState = TransactionStatus.DATA_RECONCILING,
            toState = TransactionStatus.ERROR,
            trigger = rollBack()
        )

        stateMachine.register(
            action = TransactionAction.STOP,
            fromState = TransactionStatus.EXPENSE_BILL_GENERATING,
            toState = TransactionStatus.ERROR,
            trigger = rollBack()
        )
        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.EXPENSE_BILL_GENERATING,
            toState = TransactionStatus.DONE
        ) { }
        return stateMachine
    }

    private fun registerCommonTransitions(stateMachine: SimpleStateMachine) {
        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.INIT,
            toState = TransactionStatus.DATA_COLLECTING
        ) { dataCollector.handle(it) }

        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.DATA_COLLECTING,
            toState = TransactionStatus.DATA_RECONCILING
        ) { dataReconciler.handle(it) }

        stateMachine.register(
            action = TransactionAction.COMMIT,
            fromState = TransactionStatus.DATA_RECONCILING,
            toState = TransactionStatus.INVOICE_GENERATING
        ) { invoiceGenerator.handle(it) }

        stateMachine.register(
            action = TransactionAction.STOP,
            fromState = TransactionStatus.DATA_COLLECTING,
            toState = TransactionStatus.ERROR,
            trigger = rollBack()
        )

        stateMachine.register(
            action = TransactionAction.STOP,
            fromState = TransactionStatus.DATA_RECONCILING,
            toState = TransactionStatus.ERROR,
            trigger = rollBack()
        )

        stateMachine.register(
            action = TransactionAction.STOP,
            fromState = TransactionStatus.INVOICE_GENERATING,
            toState = TransactionStatus.ERROR,
            trigger = rollBack()
        )
    }

    private fun rollBack(): (t: TransactionCommand) -> Unit = {
        when (it.transactionType) {
            TransactionType.VENDOR_BILL -> {
                expenseBillGenerator.rollback(it)
                expenseBillReconciler.rollback(it)
                expenseBillDataCollector.rollback(it)
            }

            else -> {
                invoiceGenerator.rollback(it)
                dataReconciler.rollback(it)
                dataCollector.rollback(it)
            }
        }
    }
}