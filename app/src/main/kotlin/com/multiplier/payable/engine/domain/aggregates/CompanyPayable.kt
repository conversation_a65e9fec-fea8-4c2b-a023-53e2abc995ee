package com.multiplier.payable.engine.domain.aggregates

import com.multiplier.core.payable.repository.model.PayableSource
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.types.PayableStatus

data class CompanyPayable(
    val id: Long,
    val companyId: Long,
    val items: List<PayableItem>,
    val itemType: TransactionType,
    val transactionId: String?,
    // TODO: Remove the dependency from graph when JpaCompanyPayable status (entity) dependency is removed.
    val status: PayableStatus,
    val month: Int? = null,
    val year: Int? = null,
    val entityId: Long? = null,
    val source: PayableSource = PayableSource.SYSTEM
)
