package com.multiplier.payable.engine.collector.billed.firstinvoice

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.service.InvoiceFetcher
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.DefaultProcessedCollectorInput
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import org.springframework.stereotype.Service

@Service
class BilledGrossSalaryDataCollector(
    dataCollectorInputProcessor: DataCollectorInputProcessor<DefaultProcessedCollectorInput>,
    payableItemStoreService: PayableItemStoreService,
    invoiceFetcher: InvoiceFetcher,
    itemMapper: BilledFirstInvoiceItemMapper
) : AbstractBilledDataCollector(
    dataCollectorInputProcessor,
    payableItemStoreService,
    invoiceFetcher,
    itemMapper
) {

    override fun getSupportedType(): LineItemType = LineItemType.BILLED_GROSS_SALARY
    override fun getBilledLineItemType(): LineItemType = LineItemType.GROSS_SALARY
}