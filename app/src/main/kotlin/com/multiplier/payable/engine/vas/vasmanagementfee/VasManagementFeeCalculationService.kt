package com.multiplier.payable.engine.vas.vasmanagementfee

import com.multiplier.billing.grpc.billing.Billing
import com.multiplier.core.payable.adapters.BillingServiceAdapter
import com.multiplier.payable.engine.vas.IncidentType
import com.multiplier.payable.types.Amount
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.util.add
import mu.KotlinLogging
import org.springframework.stereotype.Service
import java.time.ZoneOffset

@Service
class VasManagementFeeCalculationService(
    private val billingServiceAdapter: BillingServiceAdapter,
    private val incidentManagementFeeBillingMapper: IncidentManagementFeeBillingMapper,
) {
    companion object {
        private const val INCIDENT_ID_LABEL_KEY = "incident_id"
        private val logger = KotlinLogging.logger {}

        // Line item codes for different VAS service types
        private const val ASSET_SERVICE_FEE = "ASSET_SERVICE_FEE"
        private const val LOGISTICS_SERVICE_FEE = "LOGISTICS_SERVICE_FEE"
        private const val LEGAL_SERVICE_FEE = "LEGAL_ADVISORY_SERVICE_FEE"
        private const val STORAGE_SERVICE_FEE = "STORAGE_SERVICE_FEE"
    }

    fun calculate(input: IncidentManagementFeeCalculateInput): List<IncidentManagementFeeBill> {
        logger.info { "Calculating VAS management fee for incident ${input.incident.id} of company ${input.companyId}" }

        return getBillingsForIncident(input)
            .map { billedItem -> incidentManagementFeeBillingMapper.mapBilling(billedItem, input.incident.id) }
    }

    fun calculateAmount(input: IncidentManagementFeeCalculateInput): Amount {
        val totalAmount =
            calculate(input)
                .map(IncidentManagementFeeBill::amount)
                .fold(createZeroAmount()) { acc, amount -> acc.add(amount) }

        logger.info { "VAS Management Fee Amount = $totalAmount for incident ${input.incident.id}" }
        return totalAmount
    }

    private fun getBillingsForIncident(input: IncidentManagementFeeCalculateInput): List<Billing.BilledItem> {
        val lineItemCode = getLineItemCodeForIncidentType(input.incident.type)
        logger.info { "Getting billed items for incident type ${input.incident.type} of company ${input.companyId}" }

        return fetchBillingResults(input, lineItemCode)
            .flatMap(Billing.BillingResultPerLineCode::getItemsList)
            .filter { billedItem -> billedItem.belongsToIncident(input.incident.id) }
    }

    private fun fetchBillingResults(
        input: IncidentManagementFeeCalculateInput,
        lineItemCode: String,
    ): List<Billing.BillingResultPerLineCode> {
        logger.info { "Fetching billed items for company ${input.companyId}" }

        return billingServiceAdapter
            .generateBills(
                companyId = input.companyId,
                startDateSeconds = input.startDate.toInstant(ZoneOffset.UTC).epochSecond,
                endDateSeconds = input.endDate.toInstant(ZoneOffset.UTC).epochSecond,
                lineItemCodes = listOf(lineItemCode),
            ).filter { it.lineCode == lineItemCode }
    }

    private fun Billing.BilledItem.belongsToIncident(incidentId: Long): Boolean =
        usageList.any { usage ->
            usage.labelsList.any { label ->
                label.key == INCIDENT_ID_LABEL_KEY && label.value == incidentId.toString()
            }
        }

    private fun getLineItemCodeForIncidentType(incidentType: IncidentType): String =
        when (incidentType) {
            IncidentType.LAPTOP, IncidentType.MONITOR, IncidentType.ACCESSORIES -> ASSET_SERVICE_FEE
            IncidentType.LEGAL_CONSULTATION, IncidentType.CONTRACT_CUSTOMISATION -> LEGAL_SERVICE_FEE
            IncidentType.PICKUP_DELIVERY -> LOGISTICS_SERVICE_FEE
            IncidentType.STORAGE -> STORAGE_SERVICE_FEE
            else -> throw IllegalArgumentException("Unsupported incident type: $incidentType")
        }

    private fun createZeroAmount(): Amount =
        Amount
            .newBuilder()
            .amount(0.00)
            .currency(CurrencyCode.USD)
            .build()
}
