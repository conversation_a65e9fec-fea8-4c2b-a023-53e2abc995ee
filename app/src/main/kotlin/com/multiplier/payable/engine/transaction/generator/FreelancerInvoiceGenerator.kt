package com.multiplier.payable.engine.transaction.generator

import com.multiplier.core.payable.invoice.api.InvoiceAdapter
import com.multiplier.core.payable.invoice.database.JpaInvoiceRepository
import com.multiplier.core.payable.mapper.GrpcInvoiceMapper.Companion.logger
import com.multiplier.core.payable.service.exception.InvoiceGenerationErrorCode
import com.multiplier.core.payable.service.exception.InvoiceGenerationException
import com.multiplier.payable.engine.domain.entities.FinancialTransaction
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

/**
 * A [FinancialTransactionGenerator] that generates both internal and external invoices.
 */
@Service
class FreelancerInvoiceGenerator(
    private val externalInvoiceGenerator: FinancialTransactionGenerator,
    private val internalFinancialTransactionGenerator: FinancialTransactionGenerator,
    private val jpaInvoiceRepository: JpaInvoiceRepository,
    private val invoiceAdapter: InvoiceAdapter,
) : FinancialTransactionGenerator {
    @Transactional
    override fun generate(financialTransaction: FinancialTransaction): String {
        val externalId = externalInvoiceGenerator.generate(financialTransaction)
        val internalId = internalFinancialTransactionGenerator.generate(financialTransaction)
        logger.info { "Fetching invoice details from adapter for invoiceId: $externalId" }
        val fetchedInvoice = invoiceAdapter.get(externalId)
        logger.info { "Fetched invoice details: $fetchedInvoice" }


        val jpaInvoice =
            jpaInvoiceRepository.findById(internalId.toLong())
                .orElseThrow {
                    InvoiceGenerationException(
                        InvoiceGenerationErrorCode.MPE_INVOICE_NOT_FOUND,
                        financialTransaction.companyId,
                        -1,
                        "Cannot find Invoice with ID $internalId for transaction ID ${financialTransaction.transactionId}",
                    )
                }

        fetchedInvoice?.let {
            jpaInvoice.apply {
                totalAmount = BigDecimal.valueOf(fetchedInvoice.totalAmount ?: 0.0)
                invoiceNo = fetchedInvoice.invoiceNo ?: ""
                amountDue = BigDecimal.valueOf(fetchedInvoice.amountDue ?: fetchedInvoice.totalAmount ?: 0.0)
            }
        }

        jpaInvoice.externalId = externalId
        jpaInvoiceRepository.save(jpaInvoice)

        return internalId
    }
}
