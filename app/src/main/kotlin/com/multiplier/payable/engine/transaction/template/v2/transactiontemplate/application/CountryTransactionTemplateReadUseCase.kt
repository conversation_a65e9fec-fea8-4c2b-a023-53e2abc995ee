package com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.application

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.domain.CountryTransactionTemplateFilter
import com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.domain.JpaCountryTransactionTemplateRepository
import com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.domain.TransactionTemplateId
import com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.domain.TransactionTemplateNotFoundException
import com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.service.CountryTransactionTemplateServiceImpl
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Service
class CountryTransactionTemplateReadUseCase(
    private val repository: JpaCountryTransactionTemplateRepository,
    private val countryTransactionTemplateServiceImpl: CountryTransactionTemplateServiceImpl,
    private val dtoMapper: CountryTransactionTemplateDtoMapper,
) {

    private companion object {
        private val log = LoggerFactory.getLogger(this::class.java)
    }

    @Transactional(readOnly = true)
    fun getCountryTransactionTemplate(id: UUID): CountryTransactionTemplateDto {
        log.info("Finding country transaction template for id = $id")
        return repository.findById(TransactionTemplateId(id))
            .map { dtoMapper.mapToDto(it) }
            .orElseThrow {
                TransactionTemplateNotFoundException(
                    "Country transaction" +
                            " template not found for $id"
                )
            }
    }

    @Transactional(readOnly = true)
    fun getTemplateByTransactionTypeAndCountry(
        transactionType: TransactionType,
        country: String
    ): CountryTransactionTemplateDto {
        log.info("Getting country transaction template for transactionType = $transactionType and country = $country")
        val filter = CountryTransactionTemplateFilter(transactionType, country)
        return countryTransactionTemplateServiceImpl.findTemplateByFilter(filter)
            .map { dtoMapper.mapToDto(it) }
            .orElseThrow {
                TransactionTemplateNotFoundException(
                    "Country transaction template not found " +
                            "for transactionType = $transactionType and country = $country"
                )
            }
    }
}