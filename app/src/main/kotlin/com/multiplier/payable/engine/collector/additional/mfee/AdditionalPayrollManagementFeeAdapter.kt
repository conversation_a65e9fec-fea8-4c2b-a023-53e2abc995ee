package com.multiplier.payable.engine.collector.additional.mfee

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.core.payable.adapters.PayrollServiceAdapter
import com.multiplier.payable.engine.collector.FetchedTimeProvider
import com.multiplier.payable.engine.domain.entities.PayrollCycleFrequency
import com.multiplier.payroll.schema.Payroll
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class AdditionalPayrollManagementFeeAdapter(
    private val payrollServiceAdapter: PayrollServiceAdapter,
    private val fetchedTimeProvider: FetchedTimeProvider,
    private val additionalPayrollFeeMapper: AdditionalPayrollFeeMapper,
) {
    companion object {
        private val log = KotlinLogging.logger {}
        private val ELIGIBLE_PAYROLL_CYCLE_FREQUENCIES = listOf(
            PayrollCycleFrequency.SEMIMONTHLY,
            PayrollCycleFrequency.BIWEEKLY,
        )
    }

    fun getAdditionalPayrollFees(
        companyId: Long,
        month: Int,
        year: Int,
    ): List<AdditionalManagementFee> {
        log.info("Getting additional payroll fee for companyId: $companyId, month: $month, year: $year")

        val payrolls =
            payrollServiceAdapter.getCompaniesPayroll(
                companyIds = listOf(companyId),
                month = month,
                year = year,
            )

        val fetchedTime = fetchedTimeProvider.getFetchedTime()

        require(payrolls.all { it?.companyId != null && it.companyId == companyId }) {
            "Company id is null or different in company payroll"
        }

        val contractToEligibleAdditionalFeePayrolls = payrolls
            .filterNotNull()
            .flatMap { companyPayroll ->
                companyPayroll.memberPays
                    .filter { it.contract.type == ContractOuterClass.ContractType.EMPLOYEE }
                    .filter { it.payrollCycle != null && it.payrollCycle.cycleType != Payroll.PayrollCycleType.OFF_CYCLE }
                    .filter { it.payrollCycle != null && PayrollCycleFrequency.valueOf(it.payrollCycle.frequency) in ELIGIBLE_PAYROLL_CYCLE_FREQUENCIES }
            }.groupBy { it.contract.id }

        val additionalManagementFees = mutableListOf<AdditionalManagementFee>()
        for ((contractId, contractMemberPays) in contractToEligibleAdditionalFeePayrolls.entries) {
            if (contractMemberPays.size <= 1) {
                continue
            }
            log.info("Contract $contractId has multiple eligible payrolls")
            additionalManagementFees.add(
                additionalPayrollFeeMapper.mapGrpcMemberPayToAddtionalMgtFee(
                    contractMemberPays.first(),
                    companyId,
                    fetchedTime,
                )
            )
        }

        return additionalManagementFees
    }
}
