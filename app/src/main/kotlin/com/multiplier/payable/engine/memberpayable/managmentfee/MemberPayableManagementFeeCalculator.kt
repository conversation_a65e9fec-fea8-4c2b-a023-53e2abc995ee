package com.multiplier.payable.engine.memberpayable.managmentfee

import com.multiplier.payable.types.Amount

interface MemberPayableManagementFeeCalculator {

    fun calculate(
        memberPayableManagementFeeCalculatorInput: MemberPayableManagementFeeCalculatorInput,
    ): MemberPayableManagementFeeCalculatorResponse

    fun calculateAmount(
        memberPayableManagementFeeCalculatorInput: MemberPayableManagementFeeCalculatorInput,
    ): Amount

}