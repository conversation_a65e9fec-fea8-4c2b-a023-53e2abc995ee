package com.multiplier.payable.engine.migration

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.JpaPayableItemData
import com.multiplier.payable.types.CompanyPayableType
import com.multiplier.payable.types.PayableStatus
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalTime

@Component
class AnnualCompanyPayablePeriodDateRangeRefiller(
    private val jpaCompanyPayableRepository: JpaCompanyPayableRepository,
) : DataRefiller {

    @Transactional
    override fun refill(companyIds: Collection<Long>) {
        for (companyId in companyIds) {
            val companyPayables = jpaCompanyPayableRepository.findByCompanyIdAndTypeAndStatusNotInOrderById(
                companyId,
                CompanyPayableType.ANNUAL_PLAN,
                setOf(PayableStatus.DELETED, PayableStatus.VOIDED)
            )

            companyPayables.forEach { companyPayable ->
                val updatedPeriodDateRangeItems = companyPayable.items.map { payableItem ->
                    val updatedPeriodDateRangeItemData = payableItem.itemData.map { modifyStartAndEndDate(it) }.toSet()

                    payableItem.itemData = updatedPeriodDateRangeItemData
                    payableItem
                }.toSet()

                companyPayable.items = updatedPeriodDateRangeItems

                jpaCompanyPayableRepository.save(companyPayable)
            }
        }
    }

    private fun modifyStartAndEndDate(itemData: JpaPayableItemData?): JpaPayableItemData? {
        itemData?.annualSeatPaymentTerm?.let {
            val updatedDateRange = it.periodDateRange.copy(
                startDate = it.periodDateRange.startDate.toLocalDate().atStartOfDay(),
                endDate = it.periodDateRange.endDate.toLocalDate().atTime(LocalTime.MAX),
            )

            itemData.annualSeatPaymentTerm = it.copy(
                periodDateRange = updatedDateRange
            )
        }

        return itemData
    }
}