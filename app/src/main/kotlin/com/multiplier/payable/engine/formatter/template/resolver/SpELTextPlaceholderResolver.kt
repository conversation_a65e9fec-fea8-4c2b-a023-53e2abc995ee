package com.multiplier.payable.engine.formatter.template.resolver

import com.multiplier.payable.engine.PayableEngineException
import com.multiplier.payable.engine.transaction.template.FinancialTransactionFormatterContext
import org.springframework.expression.ExpressionParser
import org.springframework.expression.spel.standard.SpelExpressionParser
import org.springframework.expression.spel.support.StandardEvaluationContext
import org.springframework.stereotype.Service

/**
 * Implementation of the [TextPlaceholderResolver] interface that uses
 * Spring Expression Language (SpEL) to resolve text placeholders in a template.
 *
 * This resolver evaluates SpEL expressions embedded within placeholders in
 * the template and substitutes them with the corresponding values from the
 * provided [FinancialTransactionFormatterContext].
 *
 * Example usage:
 * ```kotlin
 *   // Create a financial transaction context
 *   val context = FinancialTransactionContext()
 *   // Populate the context with necessary values
 *   // ...
 *
 *   // Create an instance of SpELTextPlaceholderResolver
 *   val resolver = SpELTextPlaceholderResolver()
 *
 *   // Resolve placeholders in the template using SpEL expressions
 *   val resolvedText = resolver.resolve("template-to-resolve.", context)
 *   println(resolvedText)
 * ```
 *
 * Note: Ensure that the SpEL expressions in the template are valid and match
 * the properties available in the [FinancialTransactionFormatterContext].
 *
 * @see TextPlaceholderResolver
 * @see FinancialTransactionFormatterContext
 * @since 1.0
 */
@Service
class SpELTextPlaceholderResolver :
    TextPlaceholderResolver {

    val expressionParser: ExpressionParser = SpelExpressionParser()

    override fun resolve(template: String, context: FinancialTransactionFormatterContext): String {
        val evaluationContext = StandardEvaluationContext(context)
        val textExpression = expressionParser.parseExpression(template)
        return textExpression.getValue(evaluationContext)?.toString()
            ?: throw PayableEngineException("Null value returned for the [$template] and the [$context]")
    }
}