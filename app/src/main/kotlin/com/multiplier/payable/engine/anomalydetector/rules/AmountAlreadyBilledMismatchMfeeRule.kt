package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.provider.NetsuiteAlreadyBilledAmountService
import com.multiplier.payable.engine.anomalydetector.rules.processor.BaseAnomalyDetectionRule
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.util.*

/**
 * Checks if the system's "already billed management fee" amount matches NetSuite's "already billed" amount.
 *
 * This rule validates that BILLED_MANAGEMENT_FEE line items (which represent amounts billed in the first invoice
 * for the current period) match the corresponding MANAGEMENT_FEE_EOR amounts in NetSuite's first invoices
 * for the same period and contract.
 *
 * Logic:
 * - BILLED_MANAGEMENT_FEE.unitAmount = Amount from current period's first invoice
 * - NetSuite MANAGEMENT_FEE_EOR = Amount from current period's first invoice in NetSuite
 * - These should be identical for the same contract and period
 */
@Component
class AmountAlreadyBilledMismatchMfeeRule(
    featureFlagService: FeatureFlagService,
    private val netsuiteAlreadyBilledAmountService: NetsuiteAlreadyBilledAmountService,
) : BaseAnomalyDetectionRule(featureFlagService) {

    companion object {
        private val log = KotlinLogging.logger {}
        private val TARGET_LINE_ITEM_TYPE = LineItemType.BILLED_MANAGEMENT_FEE
    }

    override val type: DetectionRuleType = DetectionRuleType.AMOUNT_ALREADY_BILLED_MISMATCH_MFEE
    override val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.AMOUNT_ALREADY_BILLED_MISMATCH_MFEE
    override val ruleName: String = "AmountAlreadyBilledMismatchMfee"
    override val featureFlagName: String = "ENABLE_AMOUNT_ALREADY_BILLED_MISMATCH_MFEE_CHECK"

    override fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult {
        val invoiceDTO = request.invoiceDTO!!
        val companyPayable = request.payable!!
        val anomalies = mutableListOf<String>()

        return try {
            log.info { "Checking for amount already billed mismatch in management fees for company ${command.companyId}" }

            // Extract BILLED_MANAGEMENT_FEE line items from current invoice
            val billedManagementFeeItems = invoiceDTO.lineItems
                .filter { it.itemType == TARGET_LINE_ITEM_TYPE }

            if (billedManagementFeeItems.isEmpty()) {
                val warningMessage = "No BILLED_MANAGEMENT_FEE line items found - validation skipped"
                log.info { warningMessage }
                return createWarningResult(listOf(warningMessage))
            }

            log.info { "Found ${billedManagementFeeItems.size} BILLED_MANAGEMENT_FEE line items to validate" }

            // For each BILLED_MANAGEMENT_FEE line item, compare with NetSuite data
            billedManagementFeeItems.forEach { lineItem ->
                val contractId = lineItem.contractId
                if (contractId == null) {
                    log.warn { "BILLED_MANAGEMENT_FEE line item has null contract ID, skipping validation" }
                    return@forEach
                }

                val systemAlreadyBilledAmount = lineItem.unitAmount
                if (systemAlreadyBilledAmount == null) {
                    log.warn { "BILLED_MANAGEMENT_FEE line item has null unit amount for contract $contractId, skipping validation" }
                    return@forEach
                }

                // Fetch NetSuite first invoice data for comparison
                val netsuiteAlreadyBilledAmount = netsuiteAlreadyBilledAmountService.getNetsuiteAlreadyBilledAmount(
                    command,
                    contractId,
                    companyPayable.year!!,
                    companyPayable.month!!,
                    LineItemType.MANAGEMENT_FEE_EOR
                )

                if (netsuiteAlreadyBilledAmount == null) {
                    log.warn { "Could not fetch NetSuite first invoice management fee amount for contract $contractId, skipping validation" }
                    return@forEach
                }

                // Compare amounts - BILLED_MANAGEMENT_FEE should match NetSuite MANAGEMENT_FEE_EOR exactly
                if (systemAlreadyBilledAmount != netsuiteAlreadyBilledAmount) {
                    val currency = invoiceDTO.billingCurrencyCode?.name ?: "UNKNOWN"
                    val message = String.format(
                        Locale.ENGLISH,
                        "System's already billed management fee amount %.2f %s does not match NetSuite's first invoice amount %.2f %s for contract %d",
                        systemAlreadyBilledAmount,
                        currency,
                        netsuiteAlreadyBilledAmount,
                        currency,
                        contractId
                    )
                    anomalies.add(message)
                    log.error { message }
                }
            }

            when {
                anomalies.isNotEmpty() -> createFailureResult(anomalies)
                else -> {
                    val successMessage = "Amount validation passed - all ${billedManagementFeeItems.size} BILLED_MANAGEMENT_FEE amounts match NetSuite first invoice amounts"
                    log.info { successMessage }
                    createSuccessResult(listOf(successMessage))
                }
            }
        } catch (e: Exception) {
            val message = "Error checking amount already billed mismatch for management fees: ${e.message}"
            log.error(e) { message }
            createFailureResult(listOf(message))
        }
    }


}
