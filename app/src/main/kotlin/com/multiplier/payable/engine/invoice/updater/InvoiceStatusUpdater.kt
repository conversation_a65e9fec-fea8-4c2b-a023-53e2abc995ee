package com.multiplier.payable.engine.invoice.updater

import com.multiplier.core.payable.invoice.database.JpaInvoiceRepository
import com.multiplier.payable.types.InvoiceStatus
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class InvoiceStatusUpdater(
    private val invoiceRepository: JpaInvoiceRepository,
) {

    companion object {
        val log: Logger = LoggerFactory.getLogger(this::class.java.name)
    }

    fun update(context: InvoiceStatusUpdaterContext) {
        log.info("Update invoice status with id ${context.id} and status ${context.status}")

        val jpaInvoice = invoiceRepository.findById(context.id)
            .orElseThrow { IllegalArgumentException("Invoice with ${context.id} not found") }
        jpaInvoice.status = InvoiceStatus.DELETED
        invoiceRepository.save(jpaInvoice)
    }
}