package com.multiplier.payable.engine.formatter

import com.multiplier.payable.engine.payableitem.PayableItem
import org.springframework.expression.Expression
import org.springframework.expression.spel.support.StandardEvaluationContext

abstract class AbstractDataFormatter : DataFormatter {

    lateinit var dataFormatterParam: DataFormatterParam

    abstract fun newInstance(): AbstractDataFormatter

    data class FilterPayableItemsResult(
        val filteredPayableItems: List<PayableItem>,
        val remainingItems: List<PayableItem>,
    )

    protected fun applyFilter(
        filterExpression: Expression?,
        payableItems: List<PayableItem>,
    ): FilterPayableItemsResult {
        val filteredItems = payableItems.filter { isFilterSatisfied(filterExpression, it) }
        return FilterPayableItemsResult(filteredItems, payableItems.minus(filteredItems))
    }

    private fun isFilterSatisfied(filterExpression: Expression?, item: PayableItem): Boolean {
        val expressionContext = StandardEvaluationContext(item)
        val result = filterExpression?.getValue(expressionContext, Boolean::class.java)
        return result ?: true
    }

    override fun initialize(dataFormatterParam: DataFormatterParam): DataFormatter {
        val newInstance = newInstance()
        newInstance.dataFormatterParam = dataFormatterParam
        return newInstance
    }
}