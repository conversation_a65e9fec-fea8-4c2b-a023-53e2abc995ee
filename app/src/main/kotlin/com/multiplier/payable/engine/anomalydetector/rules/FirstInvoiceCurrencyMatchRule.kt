package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.core.payable.invoice.database.JpaInvoiceRepository
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.rules.processor.BaseAnomalyDetectionRule
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CompanyPayableType
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.types.InvoiceStatus
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.util.*

/**
 * Checks if the second invoice currency matches the currency used in ALL first invoices
 * Validates currency consistency between first and second invoices for the same company/period
 * Also validates that ALL first invoices for the same period use the same currency
 * FAIL if:
 * - Multiple first invoices have different currencies
 * - Second invoice currency differs from the consistent first invoice currency
 */
@Component
class FirstInvoiceCurrencyMatchRule(
    private val jpaInvoiceRepository: JpaInvoiceRepository,
    private val jpaCompanyPayableRepository: JpaCompanyPayableRepository,
    featureFlagService: FeatureFlagService,
) : BaseAnomalyDetectionRule(featureFlagService) {
    companion object {
        private val log = KotlinLogging.logger {}
        private val INACTIVE_INVOICE_STATUSES = setOf(InvoiceStatus.VOIDED, InvoiceStatus.DELETED)
    }

    override val type: DetectionRuleType = DetectionRuleType.FIRST_INVOICE_CURRENCY_MATCH
    override val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.FIRST_INVOICE_CURRENCY_MATCH
    override val ruleName: String = "FirstInvoiceCurrencyMatch"
    override val featureFlagName: String = "ENABLE_FIRST_INVOICE_CURRENCY_MATCH"

    /**
     * Rule-specific detection logic.
     * Compares the current (second) invoice currency with ALL first invoice currencies for the same company/period.
     * Validates that all first invoices have consistent currency and matches second invoice currency.
     */
    override fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult {
        // Get the invoice data and company payable (we know they're not null because isRequestValid was called)
        val invoiceDTO = request.invoiceDTO!!
        val companyPayable = request.payable!!

        // Track any anomalies found
        val anomalies = mutableListOf<String>()

        return try {
            // Get current (second) invoice currency
            val currentInvoiceCurrency = invoiceDTO.billingCurrencyCode
            if (currentInvoiceCurrency == null) {
                val message = "Current invoice billing currency is null for company ${command.companyId}"
                log.warn { message }
                anomalies.add(message)
                return createFailureResult(anomalies)
            }

            // Get and validate first invoice currencies for the same company and period
            val firstInvoiceCurrencyResult = validateFirstInvoiceCurrencies(command, companyPayable)

            // Handle different result scenarios
            when (firstInvoiceCurrencyResult.status) {
                FirstInvoiceCurrencyStatus.NO_FIRST_INVOICES -> {
                    val message = "No first invoices found for company ${command.companyId}, month ${companyPayable.month}, year ${companyPayable.year} - validation skipped"
                    log.info { message }
                    return createWarningResult(listOf(message))
                }
                FirstInvoiceCurrencyStatus.INCONSISTENT_CURRENCIES -> {
                    val message = String.format(
                        Locale.ENGLISH,
                        "Multiple first invoices found with different currencies: %s for company %d, month %d, year %d. All first invoices must use the same currency",
                        firstInvoiceCurrencyResult.currencies.joinToString(", "),
                        command.companyId,
                        companyPayable.month,
                        companyPayable.year
                    )
                    anomalies.add(message)
                    log.error { message }
                    return createFailureResult(anomalies)
                }
                FirstInvoiceCurrencyStatus.CONSISTENT_CURRENCY -> {
                    val firstInvoiceCurrency = firstInvoiceCurrencyResult.commonCurrency!!

                    // Log the currencies being compared
                    log.info { "Comparing invoice currencies for company ${command.companyId}: first_invoices=$firstInvoiceCurrency (${firstInvoiceCurrencyResult.currencies.size} invoices), second_invoice=$currentInvoiceCurrency" }

                    // Compare the currencies - FAIL if they don't match
                    when (currentInvoiceCurrency != firstInvoiceCurrency) {
                        true -> {
                            val message = String.format(
                                Locale.ENGLISH,
                                "Second invoice currency %s does not match consistent first invoice currency %s. Currency mismatch detected between first and second invoices",
                                currentInvoiceCurrency,
                                firstInvoiceCurrency
                            )
                            anomalies.add(message)
                            log.error { message }
                            createFailureResult(anomalies)
                        }
                        false -> {
                            val successMessage = "Currency validation passed - second invoice currency $currentInvoiceCurrency matches first invoice currency (validated against ${firstInvoiceCurrencyResult.currencies.size} first invoices)"
                            log.info { successMessage }
                            createSuccessResult(listOf(successMessage))
                        }
                    }
                }
            }
        } catch (e: Exception) {
            val message = "Error during first invoice currency match check for company ${command.companyId}: ${e.message}"
            log.error(e) { message }
            anomalies.add(message)
            createFailureResult(anomalies)
        }
    }

    /**
     * Validates currencies of ALL first invoices for the same company and period
     * Returns result indicating whether currencies are consistent and what the common currency is
     */
    private fun validateFirstInvoiceCurrencies(command: InvoiceCommand, companyPayable: JpaCompanyPayable): FirstInvoiceCurrencyResult {
        return try {
            // Use billing period from company payable, not transaction date
            val billingMonth = companyPayable.month
            val billingYear = companyPayable.year

            if (billingMonth == null || billingYear == null) {
                log.warn { "Billing month or year is missing from company payable for company ${command.companyId}" }
                return FirstInvoiceCurrencyResult(FirstInvoiceCurrencyStatus.NO_FIRST_INVOICES, emptyList(), null)
            }

            // Find first invoice company payables for the same company and billing period
            val firstInvoicePayables = jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
                command.companyId,
                billingYear!!,
                billingMonth!!,
                CompanyPayableType.FIRST_INVOICE,
                setOf(com.multiplier.payable.types.PayableStatus.VOIDED, com.multiplier.payable.types.PayableStatus.DELETED)
            )

            if (firstInvoicePayables.isEmpty()) {
                log.warn { "No first invoice payables found for company ${command.companyId} in $billingMonth/$billingYear" }
                return FirstInvoiceCurrencyResult(FirstInvoiceCurrencyStatus.NO_FIRST_INVOICES, emptyList(), null)
            }

            // Extract currencies from ALL first invoice payables
            val currencies = mutableListOf<CurrencyCode>()
            val payablesWithInvoices = mutableListOf<JpaCompanyPayable>()

            for (payable in firstInvoicePayables) {
                // Verify that actual invoice exists for this payable
                val invoice = jpaInvoiceRepository.findByCompanyPayableId(payable.id!!)
                if (invoice.isPresent) {
                    payable.currency?.let { currency ->
                        currencies.add(currency)
                        payablesWithInvoices.add(payable)
                    }
                } else {
                    log.warn { "No invoice found for first invoice payable ${payable.id}, skipping currency validation" }
                }
            }

            if (currencies.isEmpty()) {
                log.warn { "No valid first invoice currencies found for company ${command.companyId} in $billingMonth/$billingYear" }
                return FirstInvoiceCurrencyResult(FirstInvoiceCurrencyStatus.NO_FIRST_INVOICES, emptyList(), null)
            }

            // Check if all currencies are the same
            val uniqueCurrencies = currencies.distinct()

            return when (uniqueCurrencies.size) {
                1 -> {
                    val commonCurrency = uniqueCurrencies.first()
                    log.debug { "Found consistent first invoice currency: $commonCurrency for company ${command.companyId} (${currencies.size} invoices)" }
                    FirstInvoiceCurrencyResult(FirstInvoiceCurrencyStatus.CONSISTENT_CURRENCY, currencies, commonCurrency)
                }
                else -> {
                    log.error { "Found inconsistent first invoice currencies for company ${command.companyId}: ${uniqueCurrencies.joinToString(", ")}" }
                    FirstInvoiceCurrencyResult(FirstInvoiceCurrencyStatus.INCONSISTENT_CURRENCIES, currencies, null)
                }
            }
        } catch (e: Exception) {
            log.error(e) { "Failed to validate first invoice currencies for company ${command.companyId}: ${e.message}" }
            FirstInvoiceCurrencyResult(FirstInvoiceCurrencyStatus.NO_FIRST_INVOICES, emptyList(), null)
        }
    }
}

/**
 * Enum representing the status of first invoice currency validation
 */
private enum class FirstInvoiceCurrencyStatus {
    NO_FIRST_INVOICES,      // No first invoices found for the period
    INCONSISTENT_CURRENCIES, // Multiple first invoices with different currencies
    CONSISTENT_CURRENCY      // All first invoices have the same currency
}

/**
 * Data class representing the result of first invoice currency validation
 */
private data class FirstInvoiceCurrencyResult(
    val status: FirstInvoiceCurrencyStatus,
    val currencies: List<CurrencyCode>,  // All currencies found (for logging/error messages)
    val commonCurrency: CurrencyCode?    // The common currency if all are consistent, null otherwise
)
