package com.multiplier.payable.engine.collector.vat

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.adapters.netsuite.client.NetsuiteMappings
import com.multiplier.core.payable.companypayable.database.CompanyPayableService
import com.multiplier.core.payable.repository.model.JpaInvoiceLineItem
import com.multiplier.core.payable.service.InvoiceFetcher
import com.multiplier.payable.engine.collector.DataCollector
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.DefaultProcessedCollectorInput
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.MonthYearDuration
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.vat.adapter.GetValueAddedTaxCodeAmountInput
import com.multiplier.payable.vat.adapter.ValueAddedTaxCodeAdapter
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class BilledManagementFeeValueAddedTaxDataCollector(
    private val dataCollectorInputProcessor: DataCollectorInputProcessor<DefaultProcessedCollectorInput>,
    private val invoiceFetcher: InvoiceFetcher,
    private val objectMapper: ObjectMapper,
    private val valueAddedTaxCodeAdapter: ValueAddedTaxCodeAdapter,
    private val payableItemStoreService: PayableItemStoreService,
    private val companyPayableService: CompanyPayableService,
): DataCollector {
    private companion object {
        private val log = KotlinLogging.logger {}
        private val MANAGEMENT_FEE_EOR = LineItemType.MANAGEMENT_FEE_EOR
    }

    override fun getSupportedType() = LineItemType.VAT_BILLED_MANAGEMENT_FEE

    override fun handle(command: InvoiceCommand) {
        log.info { "Handling invoice command : $command" }
        val processedInput = dataCollectorInputProcessor.process(command)
        val monthYearDuration = processedInput.timeQueryDuration
        val companyIds = processedInput.companyIds

        val fetchedInvoicesManagementFeeValueAddedTaxItemStoreGroupedByKey =
            fetchManagementFeeInvoicedLineItems(companyIds, monthYearDuration)

        fetchedInvoicesManagementFeeValueAddedTaxItemStoreGroupedByKey.forEach {
            log.info { "Saving payable items for ${getSupportedType()} for ${it.key.companyId}" }
            payableItemStoreService.saveAndIgnoreDuplication(it.value)
        }
    }

    private fun fetchManagementFeeInvoicedLineItems(
        companyIds: Set<Long>,
        monthYearDuration: MonthYearDuration
    ): Map<ContractValueAddedTaxKey, List<PayableItemStoreDto>> {
        val startMonthYear = monthYearDuration.from.invoiceMonthYear()
        val invoiceDateRange = monthYearDuration.toDateRange()

        return companyIds.map {
            ContractValueAddedTaxKey(
                companyId = it,
                oTime = System.currentTimeMillis(),
                monthYear = startMonthYear,
                itemType = getSupportedType()
            )
        }.associateWith { key ->
            log.info { "Fetching invoices for monthYearDuration=$monthYearDuration and companyID=${key.companyId}" }
            val invoices =
                invoiceFetcher.getFirstInvoicesForCompany(key.companyId, startMonthYear.year, startMonthYear.month)

            val invoiceNoToEligibleLineItems = invoices
                .associate { it.invoiceNo to it.lineItems }
                .mapValues { (_, lineItems) ->
                    lineItems.filter { it.itemType == MANAGEMENT_FEE_EOR }
                }

            val invoiceNumberToCompanyPayableIdMap = invoices.associate { it.invoiceNo to it.companyPayable.id }

            invoiceNoToEligibleLineItems
                .map { (invoiceNo, lineItems) ->

                    val companyPayableDto = companyPayableService.get(invoiceNumberToCompanyPayableIdMap[invoiceNo])
                    val billingCurrency = companyPayableDto.currency

                    lineItems.map {

                        val contractValueAddedTax = getContractValueAddedTax(key, it, invoiceDateRange, billingCurrency)

                        val metadata = mapOf(
                            "billedInvoiceNo" to invoiceNo,
                            "isBilled" to true,
                            "applyFx" to false,
                            "billedAmount" to contractValueAddedTax.amountTotalCost,
                        )

                        PayableItemStoreDto(
                            companyId = key.companyId,
                            contractId = it.contractId,
                            month = startMonthYear.month,
                            year = startMonthYear.year,
                            periodStartDate = invoiceDateRange.startDate.toLocalDate(),
                            periodEndDate = invoiceDateRange.endDate.toLocalDate(),
                            versionId = key.computeHash(),
                            originalTimestamp = key.oTime,
                            amount = contractValueAddedTax.amountTotalCost,
                            currency = contractValueAddedTax.currencyCode,
                            countryCode = NetsuiteMappings.getCountryCode(it.countryName),
                            itemType = getSupportedType(),
                            itemData = objectMapper.writeValueAsString(
                                mapOf(
                                    "metadata" to metadata,
                                    "itemData" to it
                                )
                            )
                        )
                    }
                }.flatten()
        }
    }

    private fun getContractValueAddedTax(
        key: ContractValueAddedTaxKey,
        jpaInvoiceLineItem: JpaInvoiceLineItem,
        invoiceDateRange: DateRange,
        billingCurrency: CurrencyCode
    ): ContractValueAddedTax {
        val contractValueAddedTaxAmount = valueAddedTaxCodeAdapter.getValueAddedTaxAmount(
            GetValueAddedTaxCodeAmountInput(
                companyId = key.companyId,
                countryCode = CountryCode.valueOf(NetsuiteMappings.getCountryCode(jpaInvoiceLineItem.countryName)),
                amount = jpaInvoiceLineItem.unitPrice
            )
        )

        log.info { "VAT amount for contract id: ${jpaInvoiceLineItem.contractId} for amount: ${jpaInvoiceLineItem.unitPrice} " +
                "is $contractValueAddedTaxAmount" }

        val contractValueAddedTax = ContractValueAddedTax(
            companyId = key.companyId,
            contractId = jpaInvoiceLineItem.contractId,
            amountTotalCost = contractValueAddedTaxAmount,
            countryCode = CountryCode.valueOf(NetsuiteMappings.getCountryCode(jpaInvoiceLineItem.countryName)),
            currencyCode = billingCurrency,
            fetchedTime = key.oTime,
            startDate = invoiceDateRange.startDate.toLocalDate(),
            endDate = invoiceDateRange.endDate.toLocalDate(),
        )

        return contractValueAddedTax
    }
}