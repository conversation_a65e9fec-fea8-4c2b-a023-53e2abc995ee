package com.multiplier.payable.engine.rollback

import com.multiplier.core.payable.companypayable.database.CompanyPayableService
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class OffCycleInvoiceRollbackService(
    private val companyPayableService: CompanyPayableService,
) {

    fun rollbackReconciliation(invoiceCommand: InvoiceCommand) {
        log.info(
            "Rollback data for off-cycle invoice reconciliation with transactionId {}", invoiceCommand.transactionId
        )
        markCompanyPayableDeleted(invoiceCommand)
    }

    fun rollbackInvoiceGeneration(invoiceCommand: InvoiceCommand) {
        log.info(
            "Rollback data for off-cycle invoice invoice creation with transactionId {}",
            invoiceCommand.transactionId
        )
        markCompanyPayableDeleted(invoiceCommand)
    }

    fun markCompanyPayableDeleted(command: InvoiceCommand) {
        log.info("Marking company payable as DELETED for transactionId: {}", command.transactionId)

        val companyPayables = companyPayableService.getCompanyPayableByTransactionId(command.transactionId).toList()
        var lastException: Exception? = null

        companyPayables.forEach { companyPayable ->
            try {
                companyPayableService.markCompanyPayableAsDeleted(companyPayable.id)
            } catch (e: Exception) {
                log.error("Failed to mark company payable {} as deleted: {}", companyPayable.id, e.message)
                lastException = e
            }
        }

        // Re-throw the last exception if any occurred
        lastException?.let { throw it }
    }
}
