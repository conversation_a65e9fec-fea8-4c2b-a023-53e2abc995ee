package com.multiplier.payable.engine.reconciler

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.reconciler.adjustment.AdjustmentBalanceAwareReconcilerFactory
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorage
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorageContext
import com.multiplier.payable.engine.reconciler.data.invoice.InvoiceDataProviderFactory
import com.multiplier.payable.engine.reconciler.data.item.ItemStoreDataProviderFactory
import com.multiplier.payable.engine.reconciler.diff.reconciler.InvoiceDiffReconcilerFactory
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import mu.KotlinLogging

abstract class AbstractAnnualPlanInvoiceDataReconciler(
    private val templateProvider: TransactionTemplateProvider,
    private val invoiceDataProviderFactory: InvoiceDataProviderFactory,
    private val itemStoreDataProviderFactory: ItemStoreDataProviderFactory,
    private val invoiceDiffReconcilerFactory: InvoiceDiffReconcilerFactory,
    private val adjustmentBalanceAwareReconcilerFactory: AdjustmentBalanceAwareReconcilerFactory,
    private val companyPayableStorage: CompanyPayableStorage,
    private val dataSplitter: DataSplitter,
) : DataReconciler {
    companion object {
        private val log = KotlinLogging.logger {}
    }

    abstract override val transactionType: TransactionType

    override fun handle(command: InvoiceCommand) {
        log.info("[${this.javaClass.simpleName}] Process getItems for input: $command")
        val template = templateProvider.findTemplateFor(command.transactionType, command.companyId)

        val invoicedItems = invoiceDataProviderFactory.get(setOf(transactionType, TransactionType.SECOND_INVOICE))
            .flatMap { it.value.fetchAndAggregateInvoiceItems(command) }
        val items = itemStoreDataProviderFactory.get(transactionType)
            .fetchLatest(command, template.lineItemTypes)
        val (adjustmentItems, nonAdjustmentItems) = splitOutAdjustments(items)

        val diffNonAdjustmentItems = invoiceDiffReconcilerFactory.get(transactionType)
            .reconcileAndValidate(command, template, invoicedItems, nonAdjustmentItems)

        val maskingSeats = diffNonAdjustmentItems.map { requireNotNull(it.annualSeatPaymentTerm).seatId }
        val diffAwareAdjustmentItems = adjustmentItems.filter { requireNotNull(it.annualSeatPaymentTerm).seatId in maskingSeats }
        val balanceAwareDiffAdjustments = if (diffAwareAdjustmentItems.isNotEmpty()) {
            template.lineItemTypes.filter { it in LineItemType.getAdvanceCollectionAdjustmentLineItemTypes() }.map {
                adjustmentBalanceAwareReconcilerFactory.get(it)
                    .reconcile(diffAwareAdjustmentItems)
            }.flatten()
        } else emptyList()
        val diffPayableItems = diffNonAdjustmentItems + balanceAwareDiffAdjustments

        val splitPayableItems = dataSplitter.split(command.transactionId, template, diffPayableItems)
        log.info(
            "[${this.javaClass.simpleName}] Splitting ${diffPayableItems.size} payable items (including adjustments) into ${splitPayableItems.size} groups for" +
                    " companyId: ${command.companyId}, transactionId: ${command.transactionId}",
        )
        splitPayableItems.forEach {
            val storageContext =
                CompanyPayableStorageContext(
                    transactionId = command.transactionId,
                    invoiceDate = command.transactionDate,
                    monthYear = command.getMonthYear(),
                    items = it,
                    companyId = command.companyId,
                    cycle = command.cycle,
                    cycleDuration = calculateCycleDateRanges(diffPayableItems),
                    transactionType = transactionType,
                    skipIsrGeneration = template.skipIsrGeneration,
                )
            companyPayableStorage.exchangeAndStore(storageContext)
        }.also {
            log.info(
                "[${this.javaClass.simpleName}] Complete storing ${diffPayableItems.size} payable items (including adjustments) for" +
                        " companyId: ${command.companyId}, transactionId: ${command.transactionId}",
            )
        }
    }

    private fun splitOutAdjustments(items: List<PayableItem>): Pair<List<PayableItem>, List<PayableItem>> {
        return items.partition { LineItemType.valueOf(it.lineItemType) in LineItemType.getAdvanceCollectionAdjustmentLineItemTypes() }
    }

    private fun calculateCycleDateRanges(items: List<PayableItem>): DateRange {
        assertPaymentTerms(items)
        val startDate = items.minOfOrNull { it.annualSeatPaymentTerm!!.periodDateRange.startDate }
        val endDate = items.maxOfOrNull { it.annualSeatPaymentTerm!!.periodDateRange.endDate }

        requireNotNull(startDate) { "${this::class.simpleName} Deduced startDate must not be null." }
        requireNotNull(endDate) { "${this::class.simpleName} Deduced endDate must not be null." }

        return DateRange(startDate, endDate)
    }

    private fun assertPaymentTerms(items: List<PayableItem>) {
        items.forEach {
            requireNotNull(it.annualSeatPaymentTerm)
            {
                "${it::class.simpleName} annualSeatPaymentTerm must not be null for " +
                        "companyId: ${it.companyId}, versionId: ${it.versionId}"
            }
        }
    }
}
