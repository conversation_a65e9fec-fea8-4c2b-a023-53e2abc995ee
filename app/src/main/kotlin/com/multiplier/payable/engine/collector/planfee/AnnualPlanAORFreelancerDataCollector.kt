package com.multiplier.payable.engine.collector.planfee

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.payable.adapters.NewPricingServiceAdapter
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.AnnualPlanProcessedCollectorInput
import com.multiplier.payable.engine.domain.entities.AnnualSeatFee
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

@Service
@Qualifier("annual-plan-aor-freelancer")
class AnnualPlanAORFreelancerDataCollector(
    dataCollectorInputProcessor: DataCollectorInputProcessor<AnnualPlanProcessedCollectorInput>,
    pricingServiceAdapter: NewPricingServiceAdapter,
    annualSeatFeeToPayableItemStoreMapper: AnnualSeatFeeToPayableItemStoreMapper,
    payableItemStoreService: PayableItemStoreService,
    objectMapper: ObjectMapper,
) : AbstractAnnualPlanDataCollector(
    dataCollectorInputProcessor,
    pricingServiceAdapter,
    annualSeatFeeToPayableItemStoreMapper,
    payableItemStoreService,
    objectMapper
) {
    override fun filterPlan(annualSeatFee: AnnualSeatFee): Boolean {
        return annualSeatFee.isFreelancer()
    }

    override fun getLogPrefix(): String {
        return "AOR_FREELANCER"
    }

    override fun getSupportedType(): LineItemType {
        return LineItemType.ANNUAL_MANAGEMENT_FEE_AOR_FREELANCER
    }
}
