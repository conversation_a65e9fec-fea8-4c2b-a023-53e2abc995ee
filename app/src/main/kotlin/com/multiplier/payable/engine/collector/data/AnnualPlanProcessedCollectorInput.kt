package com.multiplier.payable.engine.collector.data

import com.multiplier.payable.engine.collector.ProcessedCollectorInput
import com.multiplier.payable.engine.domain.aggregates.DateRange
import java.time.LocalDateTime

data class AnnualPlanProcessedCollectorInput(
    override val transactionId: String,
    override val transactionDate: LocalDateTime,
    val dateRange: DateRange,
    val companyIds: Set<Long>
) : ProcessedCollectorInput
