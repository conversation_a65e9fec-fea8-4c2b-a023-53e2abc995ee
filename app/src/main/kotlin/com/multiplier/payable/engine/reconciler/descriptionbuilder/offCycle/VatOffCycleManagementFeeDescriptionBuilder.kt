package com.multiplier.payable.engine.reconciler.descriptionbuilder.offCycle

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilder
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import org.springframework.stereotype.Component

@Component
class VatOffCycleManagementFeeDescriptionBuilder : PayableItemDescriptionBuilder {
    override val lineItemType: LineItemType
        get() = LineItemType.VAT_PAYROLL_OFFCYCLE_MANAGEMENT_FEE

    override fun build(context: PayableItemDescriptionBuilderContext): String {
        return "Management Fee Value Added Tax for ${context.contractId}: ${context.currencyCode} ${context.amountInBaseCurrency}"
    }
}
