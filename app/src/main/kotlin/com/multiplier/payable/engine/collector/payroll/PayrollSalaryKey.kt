package com.multiplier.payable.engine.collector.payroll

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataKey
import com.multiplier.payable.engine.domain.aggregates.MonthYear

internal data class PayrollSalaryKey(
    val companyId: Long,
    val monthYear: MonthYear,
    val itemType: LineItemType,
    override val oTime: Long
): DataKey {
    override fun computeHash(): String {
        return hashCode().toString()
    }
}
