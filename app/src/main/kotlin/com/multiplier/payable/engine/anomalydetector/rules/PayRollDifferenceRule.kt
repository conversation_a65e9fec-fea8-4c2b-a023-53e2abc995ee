package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.core.payable.adapters.PayrollServiceAdapter
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.util.dto.payroll.CompanyMemberPayWrapper
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.helper.PayrollData
import com.multiplier.payable.engine.anomalydetector.rules.processor.BaseAnomalyDetectionRule
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.springframework.stereotype.Component

@Component
class PayRollDifferenceRule(
    private val payrollServiceAdapter: PayrollServiceAdapter,
    featureFlagService: FeatureFlagService,
) : BaseAnomalyDetectionRule(featureFlagService) {

    override val ruleName: String = "PayRollDifference"
    override val featureFlagName: String = "ENABLE_PAYROLL_DIFFERENCE_CHECK"
    override val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.PAYROLL_DIFFERENCE
    override val type: DetectionRuleType = DetectionRuleType.PAYROLL_DIFFERENCE

    override fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult {
        val companyPayable = request.payable
        val companyPayroll =
            payrollServiceAdapter.getCompaniesPayroll(
                listOf(companyPayable.companyId),
                companyPayable.month,
                companyPayable.year,
            )

        if (companyPayroll.isEmpty()) {
            val message = "Payroll data is empty for company ${companyPayable.companyId}"
            logger.error { message }
            return AnomalyDetectionResult(success = false, messages = listOf(message))
        }

        val payrollLookup = companyPayroll[0]?.let { buildPayrollLookup(it.memberPays) }
        val errorMessages = payrollLookup?.let { validateInvoices(request.invoiceDTO.lineItems, it) } ?: emptyList()

        // Log errors if there are any
        return if (errorMessages.isNotEmpty()) {
            errorMessages.forEach { logger.error { it } }
            AnomalyDetectionResult(success = false, messages = errorMessages)
        } else {
            val validatedLineItems = request.invoiceDTO.lineItems
                .filter {
                    it.itemType == LineItemType.EOR_SALARY_DISBURSEMENT ||
                        it.itemType == LineItemType.EOR_EXPENSE_DISBURSEMENT
                }
            val successMessage = "Payroll amounts validation passed - ${validatedLineItems.size} line items validated successfully against payroll data"
            logger.info { successMessage }
            AnomalyDetectionResult(success = true, messages = listOf(successMessage))
        }
    }

    private fun buildPayrollLookup(companyPayrollWrapper: List<CompanyMemberPayWrapper>): Map<String, PayrollData> {
        return companyPayrollWrapper
            .mapNotNull { memberPayWrapper ->
                val payrollCycle = memberPayWrapper.payrollCycle ?: return@mapNotNull null
                val startDate = payrollCycle.startDate ?: return@mapNotNull null
                val endDate = payrollCycle.endDate ?: return@mapNotNull null
                val contract = memberPayWrapper.contract

                val payrollData =
                    PayrollData(
                        contract.id,
                        memberPayWrapper.totalExpenseAmount,
                        memberPayWrapper.amountTotalCost,
                        endDate.month,
                        endDate.year,
                        startDate.day,
                        endDate.day,
                    )
                val key = getKey(payrollData.contractId, payrollData.startDate, payrollData.endDate)
                key to payrollData
            }.toMap()
    }

    private fun validateInvoices(
        lineItems: List<LineItemDTO>,
        payrollLookup: Map<String, PayrollData>,
    ): List<String> {
        return lineItems
            .filter {
                it.itemType == LineItemType.EOR_SALARY_DISBURSEMENT ||
                    it.itemType == LineItemType.EOR_EXPENSE_DISBURSEMENT
            }.mapNotNull { lineItem ->
                val key =
                    getKey(
                        lineItem.contractId,
                        lineItem.startPayCycleDate.dayOfMonth,
                        lineItem.endPayCycleDate.dayOfMonth,
                    )
                val expectedPayrollData =
                    payrollLookup[key]
                        ?: return@mapNotNull "No payroll data found for contract ID: ${lineItem.contractId}"

                val expectedAmount =
                    when (lineItem.itemType) {
                        LineItemType.EOR_SALARY_DISBURSEMENT ->
                            expectedPayrollData.amountTotalCost?.minus(expectedPayrollData.totalExpenseAmount!!)

                        LineItemType.EOR_EXPENSE_DISBURSEMENT ->
                            expectedPayrollData.totalExpenseAmount

                        else -> null
                    }

                if (expectedAmount != lineItem.amountInBaseCurrency) {
                    "Mismatch for contract ID: ${lineItem.contractId} " +
                        "Expected: $expectedAmount, Found: ${lineItem.amountInBaseCurrency}"
                } else {
                    null
                }
            }
    }

    private fun getKey(
        contractId: Long,
        startDate: Int,
        endDate: Int,
    ): String = "$contractId-$startDate-$endDate"
}
