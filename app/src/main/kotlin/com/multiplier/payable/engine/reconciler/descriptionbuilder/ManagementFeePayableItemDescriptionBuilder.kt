package com.multiplier.payable.engine.reconciler.descriptionbuilder

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.entities.FeeType
import org.springframework.stereotype.Service

/**
 * A [PayableItemDescriptionBuilder] for [LineItemType.MANAGEMENT_FEE_EOR].
 */
@Service
class ManagementFeePayableItemDescriptionBuilder(
    private val amountFormatter: AmountFormatter,
) : PayableItemDescriptionBuilder {
    override val lineItemType: LineItemType
        get() = LineItemType.MANAGEMENT_FEE_EOR

    override fun build(context: PayableItemDescriptionBuilderContext): String {
        val formattedAmount = amountFormatter.format(context.amountInBaseCurrency)

        if (context.feeType.isNullOrEmpty()) {
            return "Management Fee: ${context.currencyCode} $formattedAmount"
        }

        return when (context.feeType) {
            FeeType.REFUND.name -> "Management Fee Refund: ${context.currencyCode} $formattedAmount"
            FeeType.ADJUSTMENT.name -> "Management Fee already billed: ${context.currencyCode} $formattedAmount (${context.invoiceNo})"
            else -> throw IllegalArgumentException("non supported fee type for management fee description builder.")
        }
    }
}
