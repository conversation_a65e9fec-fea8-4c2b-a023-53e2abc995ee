package com.multiplier.payable.engine.reconciler.diff

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.entities.FeeType
import com.multiplier.payable.engine.payableitem.PayableItem
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class ManagementFeeDiffHelper() {
    companion object {
        val MANAGEMENT_FEE_TYPES_V2 =
            listOf(LineItemType.BILLED_MANAGEMENT_FEE.name, LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name)
        private val logger = KotlinLogging.logger {  }
    }

    fun getRefunds(payableItems: List<PayableItem>): List<PayableItem> {
        return filterByLineItemType(payableItems, getRefundManagementFeeType())
            .map { it.copy(feeType = FeeType.REFUND.name) }
    }

    fun getNew(payableItems: List<PayableItem>): List<PayableItem> {
        return filterByLineItemType(payableItems, LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name)
            .map { it.copy(feeType = FeeType.NEW.name) }
    }

    fun getAdjustments(payableItems: List<PayableItem>): List<PayableItem> {
        return payableItems
            .filter { adjustmentManagementFeeTypes().contains(it.lineItemType) }
            .groupBy { it.contractId }
            .mapValues { entry ->
                entry.value.sortedByDescending{ it.isBilled } // to make sure billed_ones are the first element
            }
            .filter { it.value.size == 2 }
            .filterValues(isNonZeroNetAmount)
            .values.flatten()
            .map { it.copy(feeType = FeeType.ADJUSTMENT.name) }
    }

    fun getAdjustmentManagementFeeWithZeroAmountDifference(payableItems: List<PayableItem>): List<PayableItem> {
        return payableItems
            .filter { adjustmentManagementFeeTypes().contains(it.lineItemType) }
            .groupBy { it.contractId }
            .mapValues { entry ->
                entry.value.sortedByDescending{ it.isBilled } // to make sure billed_ones are the first element
            }
            .filter { it.value.size == 2 }
            .filterValues{ //we want only net zero amount values
                val value = isNonZeroNetAmount
                !value(it)
            }
            .values
            .flatten()
            .also {
                logger.info { "Found contractIds = ${it.map { it.contractId }} " +
                        "where management fee is charged in first invoice" }
            }
    }

    private fun filterByLineItemType(
        payableItems: List<PayableItem>,
        lineItemType: String,
    ): List<PayableItem> {
        return payableItems
            .filter { MANAGEMENT_FEE_TYPES_V2.contains(it.lineItemType) }
            .groupBy { it.contractId }
            .filterValues { it.size == 1 }
            .flatMap { it.value }
            .filter { it.lineItemType == lineItemType }
    }

    private val isNonZeroNetAmount: (List<PayableItem>) -> Boolean = {
        val oldItem = it[0]
        val newItem = it[1]
        val netAmount = oldItem.amountInBaseCurrency - newItem.amountInBaseCurrency
        netAmount != 0.0
    }

    private fun adjustmentManagementFeeTypes(): List<String> {
        return MANAGEMENT_FEE_TYPES_V2
    }

    private fun getRefundManagementFeeType(): String {
        return LineItemType.BILLED_MANAGEMENT_FEE.name
    }
}
