package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.core.payable.service.PricingService
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.rules.processor.BaseAnomalyDetectionRule
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.util.Locale

/**
 * Checks if the invoice billing currency matches the expected currency for the company
 */
@Component
class BillingCurrencyRule(
    private val pricingService: PricingService,
    featureFlagService: FeatureFlagService,
) : BaseAnomalyDetectionRule(featureFlagService) {
    companion object {
        private val log = KotlinLogging.logger {}
    }

    override val type: DetectionRuleType = DetectionRuleType.BILLING_CURRENCY
    override val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.BILLING_CURRENCY
    override val ruleName: String
        get() = "BillingCurrency"
    override val featureFlagName: String
        get() = "ENABLE_BILLING_CURRENCY_CHECK"

    override fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult {
        // Get the invoice data
        val invoiceDTO = request.invoiceDTO!!
        val companyPayable = request.payable!!

        // Get the invoice billing currency and company billing currency
        val invoiceBillingCurrency = invoiceDTO.billingCurrencyCode
        val companyId = companyPayable.companyId
        val companyBillingCurrency = pricingService.getCompanyBillingCurrency(companyId)

        // Track any anomalies found
        val anomalies = mutableListOf<String>()

        // Compare the currencies
        if (invoiceBillingCurrency != companyBillingCurrency) {
            val message =
                String.format(
                    Locale.ENGLISH,
                    "Invoice Billing currency is expected to be %s but is %s",
                    companyBillingCurrency,
                    invoiceBillingCurrency,
                )
            anomalies.add(message)
            log.error { message }
        }

        return if (anomalies.isNotEmpty()) {
            createFailureResult(anomalies)
        } else {
            logger.info { "No $ruleName anomalies detected" }
            createSuccessResult(listOf("Billing currencies match"))
        }
    }
}
