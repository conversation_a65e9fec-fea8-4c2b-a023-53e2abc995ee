package com.multiplier.payable.engine.rollback

import com.multiplier.core.payable.companypayable.database.CompanyPayableService
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Service


private val log = KotlinLogging.logger {}


@Service
class FundingInvoiceRollbackService(
    private val companyPayableService: CompanyPayableService) {


    fun rollbackReconciliation(invoiceCommand: InvoiceCommand) {
        log.info("Rollback data for Funding invoice reconciliation with transactionId {}", invoiceCommand.transactionId)
        markCompanyPayableDeleted(invoiceCommand)
    }

    fun rollbackInvoiceGeneration(invoiceCommand: InvoiceCommand) {
        log.info("Rollback data for Funding invoice invoice creation with transactionId {}",
            invoiceCommand.transactionId)
        markCompanyPayableDeleted(invoiceCommand)
    }

    fun markCompanyPayableDeleted(command: InvoiceCommand) {
        log.info("Marking company payable as DELETED for transactionId: {}", command.transactionId)

        companyPayableService.getCompanyPayableByTransactionId(command.transactionId).forEach {
            companyPayableService.markCompanyPayableAsDeleted(it.id)
        }
    }
}