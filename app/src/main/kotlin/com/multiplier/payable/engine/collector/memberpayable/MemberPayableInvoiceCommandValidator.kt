package com.multiplier.payable.engine.collector.memberpayable

import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.springframework.stereotype.Component

@Component
class MemberPayableInvoiceCommandValidator {

    private companion object {
        private val log = mu.KotlinLogging.logger {}
    }

    fun validate(command: InvoiceCommand) {

        log.info { "Validating invoice command for member payable generation: $command" }

        requireNotNull(command.memberPayableInvoiceCommand) { "MemberPayableInvoiceCommand is required" }
        require(command.memberPayableInvoiceCommand.memberPayableIds.isNotEmpty()) {"No memberPayableIds provided"}

        log.info { "Invoice command for member payable generation is valid. MemberPayableInvoiceCommand: ${command.memberPayableInvoiceCommand}" }
    }

}