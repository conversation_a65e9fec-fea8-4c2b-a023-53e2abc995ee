package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.core.payable.adapters.PayrollServiceAdapter
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.rules.processor.BaseAnomalyDetectionRule
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.service.exception.ValidationException
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.util.Locale

/**
 * Checks if payroll output (functional) currency matches FX conversion base currency
 * Validates currency consistency between payroll system and invoice processing
 * FAIL if currencies do not match - indicates serious system inconsistency
 */
@Component
class BaseCurrencyCheckRule(
    private val payrollServiceAdapter: PayrollServiceAdapter,
    featureFlagService: FeatureFlagService,
) : BaseAnomalyDetectionRule(featureFlagService) {
    companion object {
        private val log = KotlinLogging.logger {}
    }

    override val type: DetectionRuleType = DetectionRuleType.BASE_CURRENCY_CHECK
    override val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.BASE_CURRENCY_CHECK
    override val ruleName: String = "BaseCurrencyCheck"
    override val featureFlagName: String = "ENABLE_BASE_CURRENCY_CHECK"

    /**
     * Rule-specific detection logic.
     * Compares payroll output currency with FX conversion base currency from invoice line items.
     */
    override fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult {
        // Get the invoice data and company payable (we know they're not null because isRequestValid was called)
        val invoiceDTO = request.invoiceDTO!!
        val companyPayable = request.payable!!

        // Track any anomalies found
        val anomalies = mutableListOf<String>()

        return try {
            // Get payroll output currency from payroll system
            val payrollOutputCurrency = getPayrollOutputCurrency(command, companyPayable)
            if (payrollOutputCurrency == null) {
                val message = "Unable to determine payroll output currency for company ${command.companyId}, month ${companyPayable.month}, year ${companyPayable.year}"
                log.warn { message }
                anomalies.add(message)
                return createFailureResult(anomalies)
            }

            // Get FX conversion base currency from invoice line items
            val fxConversionBaseCurrency = getFxConversionBaseCurrency(invoiceDTO)
            if (fxConversionBaseCurrency == null) {
                val message = "Unable to determine FX conversion base currency from invoice line items"
                log.warn { message }
                anomalies.add(message)
                return createFailureResult(anomalies)
            }

            // Log the currencies being compared
            log.info { "Comparing currencies for company ${command.companyId}: payroll=$payrollOutputCurrency, fx_base=$fxConversionBaseCurrency" }

            // Compare the currencies - FAIL if they don't match
            when (payrollOutputCurrency != fxConversionBaseCurrency) {
                true -> {
                    val message = String.format(
                        Locale.ENGLISH,
                        "Payroll output currency %s does not match FX conversion base currency %s. Currency mismatch detected between payroll system and invoice processing",
                        payrollOutputCurrency,
                        fxConversionBaseCurrency
                    )
                    anomalies.add(message)
                    log.error { message }
                    createFailureResult(anomalies)
                }
                false -> {
                    val successMessage = "Base currency validation passed - payroll output currency $payrollOutputCurrency matches FX conversion base currency"
                    log.info { successMessage }
                    createSuccessResult(listOf(successMessage))
                }
            }
        } catch (e: Exception) {
            val message = "Error during base currency check: ${e.message}"
            log.error(e) { message }
            anomalies.add(message)
            createFailureResult(anomalies)
        }
    }

    /**
     * Gets the payroll output (functional) currency from the payroll system
     * Uses CompanyMemberPayWrapper.getCurrency() to get payroll output currency
     */
    private fun getPayrollOutputCurrency(command: InvoiceCommand, companyPayable: JpaCompanyPayable): CurrencyCode? {
        return try {
            // Use billing period from company payable, not transaction date
            val billingMonth = companyPayable.month
            val billingYear = companyPayable.year

            if (billingMonth == null || billingYear == null) {
                val errorMessage = "Billing month or year is missing from company payable for company ${command.companyId}. Month: $billingMonth, Year: $billingYear"
                log.error { errorMessage }
                throw ValidationException(errorMessage)
            }

            // Get company payroll data for the billing period
            val companyPayrolls = payrollServiceAdapter.getCompaniesPayroll(
                companyIds = listOf(command.companyId),
                month = billingMonth,
                year = billingYear
            )
            log.debug { "Fetched company payrolls for company ${command.companyId} in $billingMonth/$billingYear" }

            // Extract currency from the first member pay in the payroll
            // This represents the payroll output (functional) currency
            val payrollCurrency = companyPayrolls
                .filterNotNull()
                .firstOrNull()
                ?.memberPays
                ?.firstOrNull()
                ?.currency

            when (payrollCurrency) {
                null -> {
                    log.warn { "No payroll currency found for company ${command.companyId} in $billingMonth/$billingYear" }
                    null
                }
                else -> {
                    log.debug { "Found payroll output currency: $payrollCurrency for company ${command.companyId}" }
                    payrollCurrency
                }
            }
        } catch (e: Exception) {
            log.error(e) { "Failed to fetch payroll currency for company ${command.companyId}: ${e.message}" }
            null
        }
    }

    /**
     * Gets the FX conversion base currency from the invoice
     * Uses InvoiceDTO.lineItems[].baseCurrency as FX conversion base currency
     * Fallback to billingCurrencyCode if no baseCurrency in line items
     */
    private fun getFxConversionBaseCurrency(invoiceDTO: com.multiplier.core.payable.adapters.api.InvoiceDTO): CurrencyCode? {
        return try {
            // First try to get base currency from line items (primary source)
            val baseCurrencyFromLineItems = invoiceDTO.lineItems
                ?.firstOrNull { !it.baseCurrency.isNullOrBlank() }
                ?.baseCurrency

            when {
                !baseCurrencyFromLineItems.isNullOrBlank() -> {
                    val currency = CurrencyCode.valueOf(baseCurrencyFromLineItems)
                    log.debug { "Found FX conversion base currency from line items: $currency" }
                    currency
                }
                invoiceDTO.billingCurrencyCode != null -> {
                    // Fallback to billing currency if no base currency in line items
                    val billingCurrency = invoiceDTO.billingCurrencyCode
                    log.debug { "Using billing currency as FX conversion base currency: $billingCurrency" }
                    billingCurrency
                }
                else -> {
                    log.warn { "No FX conversion base currency found in invoice" }
                    null
                }
            }
        } catch (e: Exception) {
            log.error(e) { "Failed to determine FX conversion base currency: ${e.message}" }
            null
        }
    }
}
