package com.multiplier.payable.engine.reconciler.diff.reconciler

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.service.exception.InvoiceGenerationErrorCode
import com.multiplier.core.payable.service.exception.InvoiceGenerationException
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import mu.KotlinLogging
import org.apache.commons.collections4.CollectionUtils

private val log = KotlinLogging.logger {}

interface InvoiceDiffReconciler {

    fun reconcile(
        command: InvoiceCommand,
        template: TransactionTemplate,
        old: List<PayableItem>,
        new: List<PayableItem>,
    ): List<PayableItem>

    fun reconcileAndValidate(
        command: InvoiceCommand,
        template: TransactionTemplate,
        old: List<PayableItem>,
        new: List<PayableItem>,
    ): List<PayableItem> {
        val payableItems = reconcile(command, template, old, new)
        log.info { "InvoiceDiffReconciler reconcileAndValidate payableItems of " +
                "size: ${payableItems.size} for companyId: ${command.companyId} and type: ${command.transactionType}" }
        return validate(command, payableItems)
    }

    fun validate(
        command: InvoiceCommand,
        payableItems: List<PayableItem>
    ): List<PayableItem> {
        if (CollectionUtils.isNotEmpty(payableItems)) {
            return payableItems
        }

        throw InvoiceGenerationException(
            InvoiceGenerationErrorCode.MPE_NO_PAYABLE_ITEM_GENERATED,
            "${InvoiceGenerationErrorCode.MPE_NO_PAYABLE_ITEM_GENERATED.exceptionMessage} " +
                    "Invoice Type: ${command.transactionType}, Transaction ID: ${command.transactionId}"
        )
    }

    fun applicableItem(
        template: TransactionTemplate,
        item: PayableItem
    ) = template.lineItemTypes.contains(LineItemType.valueOf(item.lineItemType))

    fun transactionType(): TransactionType
}