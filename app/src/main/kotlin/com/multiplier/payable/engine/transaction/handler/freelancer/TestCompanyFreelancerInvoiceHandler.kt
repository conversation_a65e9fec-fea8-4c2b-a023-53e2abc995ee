package com.multiplier.payable.engine.transaction.handler.freelancer

import com.multiplier.core.payable.invoice.database.JpaInvoiceRepository
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.ExternalSystem
import com.multiplier.core.payable.repository.model.InvoiceType
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaInvoice
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.InvoiceStatus
import com.multiplier.payable.types.PayableStatus
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID

@Component
class TestCompanyFreelancerInvoiceHandler(
    private val jpaCompanyPayableRepository: JpaCompanyPayableRepository,
    private val jpaInvoiceRepository: JpaInvoiceRepository,
): CompanyTypeFreelancerInvoiceHandler {

    private companion object {
        private val logger = KotlinLogging.logger {}
    }

    override fun handle(command: InvoiceCommand) {
        logger.info { "Handling Test Company Freelancer Invoice for companyId: ${command.companyId}" }

        // Calculate total amount from line items
        // Since we don't have direct access to line items here, we'll use the company payables
        // that were created during the data reconciliation phase
        val companyPayables = jpaCompanyPayableRepository.findByTransactionId(command.transactionId)
        val dueDate = LocalDate.now().plusDays(7)

        if (companyPayables.isEmpty()) {
            logger.error { "No company payables found for transaction ID: ${command.transactionId}" +
                    " for test company: ${command.companyId}" }
            return
        }

        companyPayables.forEach { jpaCompanyPayable ->
            // Create a dummy invoice for the test company without line items
            val jpaInvoice = JpaInvoice.builder()
                .status(InvoiceStatus.AUTHORIZED)
                .createdDate(command.transactionDate)
                .dueDate(dueDate.atStartOfDay())
                .reference(generateReference())
                .companyPayable(jpaCompanyPayable)
                .invoiceNo("INV-0000 (Test Company)")
                .externalId("INV-0000 (Test Company)-" + UUID.randomUUID().toString())
                .emailSent(false)
                .externalInvoiceGenerated(false)
                .amountPaid(BigDecimal.ZERO)
                .amountDue(getTotalAmount(jpaCompanyPayable))
                .totalAmount(getTotalAmount(jpaCompanyPayable))
                .externalSystem(ExternalSystem.NETSUITE)
                .type(InvoiceType.FREELANCER)
                .build()

            // Save the invoice
            jpaInvoiceRepository.save(jpaInvoice)

            // Update the company payable with the invoice reference
            jpaCompanyPayable.invoice = jpaInvoice
            jpaCompanyPayable.status = PayableStatus.AUTHORIZED

            // Save the company payable
            val savedEntity = jpaCompanyPayableRepository.save(jpaCompanyPayable)

            logger.info { "Created test company freelancer invoice for company payable ID: ${savedEntity.id}" }
        }
    }


    internal fun generateReference(): String {
        return "Contractor Invoice - ${getMonthAndYearForInvoice()}"
    }

    private fun getMonthAndYearForInvoice(): String {
        val today = LocalDateTime.now()
        val formatter = DateTimeFormatter.ofPattern("MMM''yy")
        return today.format(formatter)
    }

    internal fun getTotalAmount(
        jpaCompanyPayable: JpaCompanyPayable
    ): BigDecimal {
        return jpaCompanyPayable.items
            .map { BigDecimal.valueOf(it.billableCost ?: 0.0) }
            .fold(BigDecimal.ZERO) { acc, value -> acc.add(value) }
    }
}