package com.multiplier.payable.engine.reconciler.diff.reconciler

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.reconciler.diff.DepositInvoiceDiffCalculator
import com.multiplier.payable.engine.reconciler.diff.handler.AddOnlyDiffHandler
import com.multiplier.payable.engine.reconciler.diff.handler.DefaultDiffHandlerContext
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import org.springframework.stereotype.Component

@Component
class DepositInvoiceDiffReconciler(
    private val depositInvoiceDiffCalculator: DepositInvoiceDiffCalculator,
    private val addOnlyDiffHandler: AddOnlyDiffHandler,
) : InvoiceDiffReconciler {

    override fun reconcile(
        command: InvoiceCommand,
        template: TransactionTemplate,
        old: List<PayableItem>,
        new: List<PayableItem>,
    ): List<PayableItem> {
        val diff = depositInvoiceDiffCalculator.calculate(
            new.filter { applicableItem(template, it) },
            old.filter { applicableItem(template, it) }
        )
        return addOnlyDiffHandler
            .handle(DefaultDiffHandlerContext(diff))
    }

    override fun transactionType(): TransactionType {
        return TransactionType.DEPOSIT_INVOICE
    }
}