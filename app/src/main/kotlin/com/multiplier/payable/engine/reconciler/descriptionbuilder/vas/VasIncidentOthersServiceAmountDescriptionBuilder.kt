package com.multiplier.payable.engine.reconciler.descriptionbuilder.vas

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilder
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import com.multiplier.payable.engine.reconciler.descriptionbuilder.requireIncidentDescription
import org.springframework.stereotype.Service

/**
 * A [PayableItemDescriptionBuilder] for [LineItemType.VAS_INCIDENT_OTHERS_SERVICE_AMOUNT].
 */
@Service
class VasIncidentOthersServiceAmountDescriptionBuilder : PayableItemDescriptionBuilder {
    override val lineItemType: LineItemType
        get() = LineItemType.VAS_INCIDENT_OTHERS_SERVICE_AMOUNT

    override fun build(context: PayableItemDescriptionBuilderContext): String = context.requireIncidentDescription()
}
