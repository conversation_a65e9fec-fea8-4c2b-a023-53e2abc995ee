package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.core.payable.service.PricingService
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.rules.processor.BaseAnomalyDetectionRule
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.util.Locale

/**
 * Checks if the invoice due date matches the expected due date based on company payment terms
 */
@Component
class PaymentTermsRule(
    private val pricingService: PricingService,
    featureFlagService: FeatureFlagService,
) : BaseAnomalyDetectionRule(featureFlagService) {
    companion object {
        private val log = KotlinLogging.logger {}
        private const val DEFAULT_PAYMENT_TERMS_DAYS = 7
    }

    override val type: DetectionRuleType = DetectionRuleType.PAYMENT_TERMS
    override val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.PAYMENT_TERMS
    override val ruleName: String = "PaymentTerms"
    override val featureFlagName: String = "ENABLE_PAYMENT_TERMS_CHECK"

    /**
     * Rule-specific detection logic.
     */
    override fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult {
        // Get the invoice data and company payable (we know they're not null because isRequestValid was called)
        val invoiceDTO = request.invoiceDTO!!
        val companyPayable = request.payable!!

        // Get required data
        val invoiceDate = invoiceDTO.date
        val invoiceDueDate = invoiceDTO.dueDate
        val companyId = companyPayable.companyId

        // Track any anomalies found
        val anomalies = mutableListOf<String>()

        // Validate that we have the required data
        if (invoiceDate == null) {
            val message = "Invoice date is missing"
            anomalies.add(message)
            log.error { message }
            return createFailureResult(anomalies)
        }

        if (invoiceDueDate == null) {
            val message = "Invoice due date is missing"
            anomalies.add(message)
            log.error { message }
            return createFailureResult(anomalies)
        }

        // Get company payment terms
        val paymentTermsInDays = try {
            getCompanyPaymentTerms(companyId)
        } catch (e: Exception) {
            val message = "Unable to fetch company payment terms for company ID $companyId: ${e.message}"
            anomalies.add(message)
            log.error(e) { message }
            return createFailureResult(anomalies)
        }

        // Calculate expected due date
        val expectedDueDate = calculateExpectedDueDate(invoiceDate, paymentTermsInDays)

        // Compare actual vs expected due date
        if (!invoiceDueDate.isEqual(expectedDueDate)) {
            val message = String.format(
                Locale.ENGLISH,
                "Invoice due date '%s' does not match expected due date '%s' based on payment terms (%d days)",
                invoiceDueDate,
                expectedDueDate,
                paymentTermsInDays
            )
            anomalies.add(message)
            log.error { message }
        }

        return if (anomalies.isNotEmpty()) {
            createFailureResult(anomalies)
        } else {
            logger.info { "No $ruleName anomalies detected" }
            createSuccessResult(listOf("Payment terms validation passed"))
        }
    }

    /**
     * Gets the payment terms for a company, with fallback to default
     */
    private fun getCompanyPaymentTerms(companyId: Long): Int {
        return try {
            val pricing = pricingService.getCompanyPricing(companyId)
            pricing?.paymentTermInDays ?: DEFAULT_PAYMENT_TERMS_DAYS
        } catch (e: Exception) {
            log.warn(e) { "Failed to get company pricing for company ID $companyId, using default payment terms" }
            DEFAULT_PAYMENT_TERMS_DAYS
        }
    }

    /**
     * Calculates the expected due date based on invoice date and payment terms
     */
    private fun calculateExpectedDueDate(invoiceDate: LocalDate, paymentTermsInDays: Int): LocalDate {
        return invoiceDate.plusDays(paymentTermsInDays.toLong())
    }
}
