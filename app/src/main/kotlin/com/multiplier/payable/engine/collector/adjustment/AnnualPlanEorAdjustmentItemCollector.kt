package com.multiplier.payable.engine.collector.adjustment

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.payable.adapters.NewPricingServiceAdapter
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.AnnualPlanProcessedCollectorInput
import com.multiplier.payable.engine.collector.planfee.AnnualSeatFeeToPayableItemStoreMapper
import com.multiplier.payable.engine.domain.entities.AnnualSeatFee
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import org.springframework.stereotype.Component

@Component
class AnnualPlanEorAdjustmentItemCollector(
    dataCollectorInputProcessor: DataCollectorInputProcessor<AnnualPlanProcessedCollectorInput>,
    pricingServiceAdapter: NewPricingServiceAdapter,
    annualSeatFeeToPayableItemStoreMapper: AnnualSeatFeeToPayableItemStoreMapper,
    payableItemStoreService: PayableItemStoreService,
    objectMapper: ObjectMapper,
) : AbstractAnnualPlanAdjustmentItemCollector(
    dataCollectorInputProcessor,
    pricingServiceAdapter,
    annualSeatFeeToPayableItemStoreMapper,
    payableItemStoreService,
    objectMapper
) {
    override fun filterPlan(annualSeatFee: AnnualSeatFee): Boolean {
        return annualSeatFee.isEor()
    }

    override fun getSupportedType(): LineItemType {
        return LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR_SERVICE_FEE
    }
}
