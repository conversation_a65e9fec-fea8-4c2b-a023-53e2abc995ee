package com.multiplier.payable.engine.transaction.handler

import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.rollback.FundingInvoiceRollbackService
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.generator.FinancialTransactionGenerator
import com.multiplier.payable.ledger.provider.CompanyPrimaryEntityProvider
import com.multiplier.payable.pricing.EntityPricingAdapter
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class FundingInvoiceHandler(
    private val invoiceTransactionBuilder: InvoiceTransactionBuilder,
    private val entityPricingAdapter: EntityPricingAdapter,
    private val fundingInvoiceGenerator: FinancialTransactionGenerator,
    private val fundingInvoiceRollbackService: FundingInvoiceRollbackService,
    private val companyPrimaryEntityProvider: CompanyPrimaryEntityProvider,
) : FinancialTransactionHandler {
    override val transactionType: TransactionType
        get() = TransactionType.GP_FUNDING_INVOICE

    override fun handle(command: InvoiceCommand) {
        val entityId = command.entityId ?: companyPrimaryEntityProvider.get(command.companyId)
        val pricing = entityPricingAdapter.getPricing(
            transactionType = transactionType,
            companyId = command.companyId,
            entityId = entityId
        )
        val dueDate = command.transactionDate.plusDays(pricing.paymentTermInDays?.toLong() ?: 7L)
        val commandWithDueDateAndBillingCurrencyCode = command.copy(
            billingCurrencyCode = CurrencyCode.valueOf(pricing.billingCurrencyCode.name),
            dueDate = dueDate
        )
        val invoicesToGenerate = invoiceTransactionBuilder.build(commandWithDueDateAndBillingCurrencyCode)
        invoicesToGenerate.forEach(fundingInvoiceGenerator::generate)
    }

    override fun rollback(command: InvoiceCommand) {
        log.info("Rollback data for Funding invoice with transactionId {}", command.transactionId)
        fundingInvoiceRollbackService.rollbackInvoiceGeneration(command)
    }

}