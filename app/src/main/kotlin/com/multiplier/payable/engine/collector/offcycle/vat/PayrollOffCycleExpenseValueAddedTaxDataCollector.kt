package com.multiplier.payable.engine.collector.offcycle.vat

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataCollector
import com.multiplier.payable.engine.collector.payroll.ContractPayroll
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.springframework.stereotype.Component

@Component
class PayrollOffCycleExpenseValueAddedTaxDataCollector(
    private val offCycleVatDataCollectionService: OffCycleVatDataCollectionService,
) : DataCollector, OffCycleVatCalculationStrategy {

    override fun getSupportedType(): LineItemType = LineItemType.VAT_PAYROLL_OFFCYCLE_EXPENSE

    override fun getPayrollCostAmount(contractPayroll: ContractPayroll): Double {
        return contractPayroll.totalExpenseAmount ?: 0.0
    }

    override fun handle(command: InvoiceCommand) {
        offCycleVatDataCollectionService.handleOffCycleVatCollection(command, this)
    }
}