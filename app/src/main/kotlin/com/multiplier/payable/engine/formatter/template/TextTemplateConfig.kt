package com.multiplier.payable.engine.formatter.template

/**
 * [{
 *   "template": "TextTemplateFormatter",
 *   "field": "description",
 *   "criteria": "itemType: PAYROLL_SALARY",
 *   "order": 1,
 *   "textTemplate": "${memberName} - ${amountInBaseCurrency} ${baseCurrencyCode} Gross Salary."
 * },
 * {
 *  *   "template": "TextTemplateFormatter",
 *  *   "field": "description",
 *  *   "criteria": "itemType: MANAGEMENT_FEE",
 *  *   "order": 2,
 *  *   "textTemplate": "${memberName} - ${amountInBaseCurrency} USD Management Fee."
 *  * }
 * ]
 */
data class TextTemplateConfig(
        val order: Int,

        /**
         * the template class name e.g. TextTemplateFormatter
         */
        val template: String,

        /**
         * The field to be set (e.g., "description")
         */
        val field: String,

        /**
         * The criteria to check before applying the formatter
         * "criteria": "itemType: Gross, itemType: M_FEE, contractId = 34538" // "Or" criteria
         * if the criteria is empty: Apply to all
         */
        val criteria: String,

        /**
         * "M. Fee : ${amountInBaseCurrency} USD"
         */
        val textTemplate: String
)