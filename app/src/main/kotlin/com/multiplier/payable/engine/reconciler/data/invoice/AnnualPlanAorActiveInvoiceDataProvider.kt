package com.multiplier.payable.engine.reconciler.data.invoice

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.mapper.CompanyPayableMapper
import com.multiplier.payable.types.InvoiceStatus
import com.multiplier.payable.types.PayableStatus
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

@Qualifier("annual-plan-aor")
@Service
class AnnualPlanAorActiveInvoiceDataProvider(
    private val jpaCompanyPayableRepository: JpaCompanyPayableRepository,
    private val mapper: CompanyPayableMapper,
) : InvoiceDataProvider {
    companion object {
        private val INACTIVE_INVOICE_TYPES = listOf(InvoiceStatus.VOIDED, InvoiceStatus.DELETED)
        private val INACTIVE_PAYABLE_STATUS = listOf(PayableStatus.VOIDED, PayableStatus.DELETED)
    }

    override fun transactionType(): TransactionType {
        return TransactionType.ANNUAL_PLAN_AOR_INVOICE
    }

    override fun fetchActiveInvoices(command: InvoiceCommand): List<CompanyPayable> {
        val jpaCompanyPayables = jpaCompanyPayableRepository.findByCompanyIdAndTypeAndDateRangeOverlap(
            /* companyId = */ command.companyId,
            /* type = */ mapFromType(transactionType()),
            /* startDate = */ command.dateRange.startDate.toLocalDate(),
            /* endDate = */ command.dateRange.endDate.toLocalDate(),
        ).stream()
            .filter { !INACTIVE_PAYABLE_STATUS.contains(it.status) }
            .filter { !INACTIVE_INVOICE_TYPES.contains(it.invoice?.status ?: InvoiceStatus.DELETED) }
            .toList()

        return mapper.mapCompanyPayables(jpaCompanyPayables)
    }
}
