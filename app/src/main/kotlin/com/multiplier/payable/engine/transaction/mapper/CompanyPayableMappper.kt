package com.multiplier.payable.engine.transaction.mapper

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.companypayable.database.LineItemTypeMapper
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaInvoice
import com.multiplier.core.payable.repository.model.JpaPayableItem
import com.multiplier.core.util.IgnoreUnmappedMapperConfig
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.contract.CountryWorkStatus
import com.multiplier.payable.engine.deposit.Deposit
import com.multiplier.payable.engine.domain.aggregates.AnnualSeatPaymentTerm
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.DefaultFinancialTransaction
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.ContractDepartment
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.types.CompanyPayableType
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.PayableItemType
import org.apache.commons.collections.CollectionUtils
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Named
import org.mapstruct.factory.Mappers
import java.util.*

@Mapper(componentModel = "spring", config = IgnoreUnmappedMapperConfig::class)
abstract class CompanyPayableMapper {
    fun toFinancialTransaction(companyPayable: JpaCompanyPayable) =
        when (companyPayable.type) {
            CompanyPayableType.GLOBAL_PAYROLL_FUNDING -> toDefaultFinancialTransaction(companyPayable)
            CompanyPayableType.ANNUAL_PLAN -> toDefaultFinancialTransaction(companyPayable)
            CompanyPayableType.ANNUAL_PLAN_AOR -> toDefaultFinancialTransaction(companyPayable)
            else -> throw IllegalArgumentException("Unsupported transaction type: ${companyPayable.type}")
        }

    @Mapping(source = "invoice.dueDate", target = "dueDate")
    @Mapping(source = "invoice.reference", target = "reference")
    @Mapping(source = "id", target = "companyPayableId")
    @Mapping(expression = "java(mapPayableItems(companyPayable, companyPayable.getItems()))", target = "items")
    @Mapping(source = "invoice.externalId", target = "invoiceId")
    abstract fun toDefaultFinancialTransaction(companyPayable: JpaCompanyPayable): DefaultFinancialTransaction

    @Mapping(expression = "java(mapPayableItems(companyPayable, companyPayable.getItems()))", target = "items")
    @Mapping(source = "type", target = "itemType", qualifiedByName = ["mapToTransactionType"])
    @Mapping(source = "status", target = "status")
    @Mapping(source = "source", target = "source")
    abstract fun mapCompanyPayable(companyPayable: JpaCompanyPayable): CompanyPayable

    @Mapping(source = "companyPayable.type", target = "itemType", qualifiedByName = ["mapToTransactionType"])
    @Mapping(source = "items", target = "items")
    @Mapping(source = "companyPayable.status", target = "status")
    abstract fun mapCompanyPayable(
        companyPayable: JpaCompanyPayable,
        items: Collection<PayableItem>,
    ): CompanyPayable

    abstract fun mapCompanyPayables(companyPayables: List<JpaCompanyPayable>): List<CompanyPayable>

    @Mapping(source = "companyPayable.month", target = "month")
    @Mapping(source = "companyPayable.year", target = "year")
    @Mapping(source = "item.type", target = "lineItemType", qualifiedByName = ["mapItemType"])
    @Mapping(expression = "java(item.getContractId())", target = "contractId")
    @Mapping(expression = "java(fromContractTypeToEmploymentType(item.getContractType()))", target = "employmentType")
    @Mapping(source = "companyPayable.companyId", target = "companyId")
    @Mapping(source = "item.description", target = "description")
    @Mapping(source = "item.totalCost", target = "amountInBaseCurrency")
    @Mapping(expression = "java(item.getCurrencyCode().name())", target = "baseCurrency")
    @Mapping(source = "item.billableCost", target = "billableCost")
    @Mapping(source = "item.versionId", target = "versionId")
    @Mapping(source = "item.originalTimestamp", target = "originalTimestamp")
    @Mapping(
        source = "item", target = "cycle", qualifiedByName = ["mapCycle"],
    )
    @Mapping(expression = "java(item.getStartPayCycleDate())", target = "periodStartDate")
    @Mapping(expression = "java(item.getEndPayCycleDate())", target = "periodEndDate")
    @Mapping(expression = "java(item.getCountryName())", target = "countryName")
    @Mapping(expression = "java(item.getTaxType())", target = "taxType")
    @Mapping(source = "item", target = "countryWorkStatus", qualifiedByName = ["mapCountryWorkStatus"])
    @Mapping(source = "item", target = "annualSeatPaymentTerm", qualifiedByName = ["mapAnnualSeatPaymentTerm"])
    @Mapping(source = "item", target = "insuranceType", qualifiedByName = ["mapInsuranceType"])
    @Mapping(source = "item", target = "memberName", qualifiedByName = ["mapMemberName"])
    @Mapping(source = "item", target = "contractDepartment", qualifiedByName = ["mapContractDepartment"])
    @Mapping(source = "item", target = "deposit", qualifiedByName = ["mapDeposit"])
    @Mapping(source = "item", target = "matchingAdvanceCollectionLineTaxCode", qualifiedByName = ["mapMatchingAdvanceCollectionLineTaxCode"])
    @Mapping(target = "itemCount", constant = "1")
    @Mapping(target = "billingCurrency", source = "item.billedCurrencyCode")
    @Mapping(target = "companyPayableLineItemIds", source = "item", qualifiedByName = ["mapCompanyPayableLineItemId"])
    abstract fun mapPayableItem(
        companyPayable: JpaCompanyPayable,
        item: JpaPayableItem,
    ): PayableItem

    fun mapManualInsurancePayables(manualPayables: List<JpaCompanyPayable>): List<CompanyPayable> {
        return manualPayables
            .filter { it.invoice != null && CollectionUtils.isNotEmpty(it.invoice.lineItems) }
            .map {
                val payableItems = mapInsurancePayableItemsFromInvoice(it, it.invoice)
                mapCompanyPayable(it, payableItems)
            }
    }

    fun mapPayableItems(
        companyPayable: JpaCompanyPayable,
        items: Collection<JpaPayableItem>?,
    ): List<PayableItem> {
        if (CollectionUtils.isEmpty(items)) {
            return emptyList()
        }

        return items!!.map { mapPayableItem(companyPayable, it) }
    }

    @Named("fromContractTypeToEmploymentType")
    fun fromContractTypeToEmploymentType(contractType: ContractType?) =
        when (contractType) {
            ContractType.EMPLOYEE -> "EOR"
            ContractType.CONTRACTOR -> "AOR"
            ContractType.FREELANCER -> "Freelancer"
            ContractType.HR_MEMBER -> "HR Member"
            else -> "Unknown"
        }

    @Named("mapItemType")
    fun mapItemType(payableItemType: PayableItemType): LineItemType {
        return Mappers.getMapper(LineItemTypeMapper::class.java)
            .map(payableItemType)
    }

    // only supporting these, throw exception if not fall into these
    @Named("mapToTransactionType")
    fun mapType(type: CompanyPayableType): TransactionType {
        return when (type) {
            CompanyPayableType.FIRST_INVOICE -> TransactionType.FIRST_INVOICE
            CompanyPayableType.SECOND_INVOICE -> TransactionType.SECOND_INVOICE
            CompanyPayableType.DEPOSIT -> TransactionType.DEPOSIT_INVOICE
            CompanyPayableType.GLOBAL_PAYROLL_FUNDING -> TransactionType.GP_FUNDING_INVOICE
            CompanyPayableType.INSURANCE -> TransactionType.INSURANCE_INVOICE
            CompanyPayableType.ANNUAL_PLAN -> TransactionType.ANNUAL_PLAN_INVOICE
            CompanyPayableType.ANNUAL_PLAN_AOR -> TransactionType.ANNUAL_PLAN_AOR_INVOICE
            CompanyPayableType.GP_SERVICE_INVOICE -> TransactionType.GP_SERVICE_INVOICE
            CompanyPayableType.FREELANCER -> TransactionType.FREELANCER_INVOICE
            CompanyPayableType.ORDER_FORM_ADVANCE -> TransactionType.ORDER_FORM_ADVANCE
            CompanyPayableType.VAS_INCIDENT_INVOICE -> TransactionType.VAS_INCIDENT_INVOICE
            CompanyPayableType.PAYROLL_OFFCYCLE_INVOICE -> TransactionType.PAYROLL_OFFCYCLE_INVOICE
            CompanyPayableType.VAS_BACKGROUND_VERIFICATION_INVOICE -> TransactionType.VAS_BACKGROUND_VERIFICATION_INVOICE
            else -> throw IllegalArgumentException("Unsupported mapping for CompanyPayableType: $type")
        }
    }

    @Named("mapAnnualSeatPaymentTerm")
    fun mapAnnualSeatPaymentTerm(item: JpaPayableItem): AnnualSeatPaymentTerm? {
        return item.itemData?.firstOrNull()?.annualSeatPaymentTerm
    }

    @Named("mapCountryWorkStatus")
    fun mapCountryWorkStatus(item: JpaPayableItem): CountryWorkStatus? {
        return item.itemData?.firstOrNull()?.countryWorkStatus
    }

    @Named("mapInsuranceType")
    fun mapInsuranceType(item: JpaPayableItem): String? {
        if (CollectionUtils.isEmpty(item.itemData)) {
            return null
        }
        return item.itemData.map { it.insuranceType }.singleOrNull()
    }

    @Named("mapMemberName")
    fun mapMemberName(item: JpaPayableItem): String? {
        if (CollectionUtils.isEmpty(item.itemData)) {
            return null
        }
        return item.itemData.map { it.memberName }.singleOrNull()
    }

    @Named("mapContractDepartment")
    fun mapContractDepartment(item: JpaPayableItem): ContractDepartment? {
        if (CollectionUtils.isEmpty(item.itemData)) {
            return null
        }
        return item.itemData.firstNotNullOfOrNull { it.contractDepartment }
    }

    private fun mapInsurancePayableItemsFromInvoice(
        companyPayable: JpaCompanyPayable,
        invoice: JpaInvoice,
    ): List<PayableItem> {
        val payableItems =
            invoice.lineItems
                .filter { it.contractId != null }
                .map { invoiceItem ->
                    PayableItem(
                        month = companyPayable.month,
                        year = companyPayable.year,
                        lineItemType = LineItemType.INSURANCE_PREMIUM.name,
                        contractId = invoiceItem.contractId,
                        amountInBaseCurrency = -1.0,
                        baseCurrency = "",
                        countryCode = "",
                        cycle = InvoiceCycle.MONTHLY,
                        originalTimestamp = -1,
                        companyId = companyPayable.companyId,
                        companyPayableLineItemIds = invoiceItem.companyPayableLineItemIds ?: mutableListOf(),
                    )
                }
        return payableItems
    }

    @Named("mapDeposit")
    fun mapDeposit(item: JpaPayableItem): Deposit? {
        if (item.itemData == null) {
            return null
        }
        return item.itemData.firstNotNullOfOrNull { it.deposit }
    }

    @Named("mapMatchingAdvanceCollectionLineTaxCode")
    fun mapMatchingAdvanceCollectionLineTaxCode(item: JpaPayableItem): String? {
        if (item.itemData == null) {
            return null
        }
        return item.itemData.firstNotNullOfOrNull { it.matchingAdvanceCollectionLineTaxCode }
    }

    fun mapCountry(countryCode: CountryCode?): String? {
        return countryCode?.name
    }

    @Named("mapCycle")
    fun mapCycle(item: JpaPayableItem): InvoiceCycle {
        return item.cycle?.let { InvoiceCycle.valueOf(it) } ?: InvoiceCycle.MONTHLY
    }

    @Named("mapCompanyPayableLineItemId")
    fun mapCompanyPayableLineItemId(item: JpaPayableItem): List<UUID> {
        return mutableListOf(item.id)
    }

}
