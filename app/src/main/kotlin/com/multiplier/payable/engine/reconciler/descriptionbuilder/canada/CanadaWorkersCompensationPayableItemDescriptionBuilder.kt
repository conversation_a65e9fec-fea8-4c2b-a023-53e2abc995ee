package com.multiplier.payable.engine.reconciler.descriptionbuilder.canada

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.AmountFormatter
import com.multiplier.payable.engine.reconciler.descriptionbuilder.CanadaPayrollPayableItemDescriptionBuilder
import org.springframework.stereotype.Service

/**
 * A [CanadaPayrollPayableItemDescriptionBuilder] for [LineItemType.CANADA_WORKERS_COMPENSATION].
 */
@Service
class CanadaWorkersCompensationPayableItemDescriptionBuilder: CanadaPayrollPayableItemDescriptionBuilder() {

    override val lineItemType: LineItemType
        get() = LineItemType.CANADA_WORKERS_COMPENSATION

    override fun getPrefix(): String {
        return "Worker's Compensation"
    }
}
