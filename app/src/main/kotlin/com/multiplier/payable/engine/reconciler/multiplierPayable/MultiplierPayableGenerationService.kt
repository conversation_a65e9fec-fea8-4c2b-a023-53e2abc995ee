package com.multiplier.payable.engine.reconciler.multiplierPayable

import com.multiplier.core.payable.expenseBill.ExpenseBillLineItemType
import com.multiplier.core.payable.repository.model.MultiplierPayableStatus
import com.multiplier.core.payable.service.vendorbill.itemstore.MultiplierPayableItemStoreDto
import com.multiplier.payable.engine.reconciler.descriptionbuilder.multiplierPayable.MultiplierPayableItemDescriptionBuilderContext
import com.multiplier.payable.engine.reconciler.descriptionbuilder.multiplierPayable.MultiplierPayableItemDescriptionBuilderFactory
import com.multiplier.payable.engine.reconciler.multiplierPayable.dto.MultiplierPayableDTO
import com.multiplier.payable.engine.reconciler.multiplierPayable.dto.MultiplierPayableItemDTO
import org.springframework.stereotype.Service

@Service
class MultiplierPayableGenerationService(
    private val descriptionBuilderFactory: MultiplierPayableItemDescriptionBuilderFactory
) {

    fun generateMultiplierPayable(
        commandTransactionId: String,
        payrollCycleId: Long,
        lineItemType: ExpenseBillLineItemType,
        items: List<MultiplierPayableItemStoreDto>
    ): MultiplierPayableDTO {
        val itemsDTO = items.map { item ->
            val context = MultiplierPayableItemDescriptionBuilderContext(
                contractId = item.contractId,
                payrollMetaData = item.metadata.payroll,
                memberName = item.metadata.employeeName,
                refName = lineItemType.refName
            )

            val builder = descriptionBuilderFactory.get(lineItemType)

            val description = builder.build(context)

            MultiplierPayableItemDTO(
                id = item.id,
                contractId = item.contractId,
                expenseBillLineItemType = item.itemType,
                description = description,
                amount = item.amount,
                currencyCode = item.currency,
                employeeName = item.metadata.employeeName,
                countryCode = item.countryCode,
                payrollMetaData = item.metadata.payroll
            )
        }.toSet()

        return MultiplierPayableDTO(
            status = MultiplierPayableStatus.APPROVED,
            lineItemType = lineItemType,
            transactionId = commandTransactionId,
            payrollCycleId = payrollCycleId,
            items = itemsDTO
        )
    }
}

