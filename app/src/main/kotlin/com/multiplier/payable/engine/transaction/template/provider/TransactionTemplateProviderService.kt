package com.multiplier.payable.engine.transaction.template.provider

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Service

@Service
@Primary
class TransactionTemplateProviderService(
    private val transactionTemplateProviderFactory: TransactionTemplateProviderFactory
): TransactionTemplateProvider {

    private companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }

    override fun findTemplateFor(transactionType: TransactionType, companyId: Long): TransactionTemplate {
        return transactionTemplateProviderFactory.getTransactionTemplateProvider()
            .findTemplateFor(transactionType, companyId)
    }

    override fun findDefaultTemplate(transactionType: TransactionType): TransactionTemplate {
        return transactionTemplateProviderFactory.getTransactionTemplateProvider()
            .findDefaultTemplate(transactionType)
    }


}