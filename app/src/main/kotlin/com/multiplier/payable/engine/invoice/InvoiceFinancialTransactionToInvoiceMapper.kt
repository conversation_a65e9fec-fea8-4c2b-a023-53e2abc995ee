package com.multiplier.payable.engine.invoice

import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.entities.Invoice
import com.multiplier.payable.engine.payableitem.PayableItem
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Named

@Mapper(componentModel = "spring")
abstract class InvoiceFinancialTransactionToInvoiceMapper {
    @Mapping(target = "customerId", source = "externalCustomerId")
    @Mapping(target = "billingCurrencyCode", source = "transaction.currency")
    @Mapping(target = "subsidiaryName", defaultValue = "Multiplier Technologies Pte Ltd", source = "transaction.subsidiaryName")
    @Mapping(
        expression = "java(map(transaction.getItems()))",
        target = "lineItems",
    )
    abstract fun map(
        transaction: Invoice,
        externalCustomerId: String,
    ): InvoiceDTO

    @Mapping(source = "periodStartDate", target = "startPayCycleDate")
    @Mapping(source = "periodEndDate", target = "endPayCycleDate")
    @Mapping(source = "billableCost", target = "unitAmount")
    @Mapping(source = "taxType", target = "taxType")
    @Mapping(source = "itemCount", target = "quantity")
    @Mapping(target = "itemType", source = "lineItemType", qualifiedByName = ["mapItemType"])
    @Mapping(target = "companyPayableLineItemIds", source = "companyPayableLineItemIds")
    abstract fun map(item: PayableItem): LineItemDTO

    @Named("mapItemType")
    fun mapStringToLineItemType(lineItemType: String): LineItemType {
        return LineItemType.valueOf(lineItemType)
    }

    abstract fun map(items: List<PayableItem>): List<LineItemDTO>
}
