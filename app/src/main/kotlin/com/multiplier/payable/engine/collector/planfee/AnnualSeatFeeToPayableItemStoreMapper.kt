package com.multiplier.payable.engine.collector.planfee

import com.multiplier.core.util.IgnoreUnmappedMapperConfig
import com.multiplier.payable.engine.domain.entities.AnnualSeatFee
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.types.CurrencyCode
import org.mapstruct.Mapper
import java.time.LocalDateTime

@Mapper(componentModel = "spring", config = IgnoreUnmappedMapperConfig::class)
abstract class AnnualSeatFeeToPayableItemStoreMapper {
    fun map(
        transactionId: String,
        transactionDate: LocalDateTime,
        annualSeatFees: Map<AnnualSeatFeeDataKey, AnnualSeatFee>,
        itemDataMap: Map<AnnualSeatFeeDataKey, String?>,
    ): List<PayableItemStoreDto> =
        annualSeatFees
            .map {
                map(
                    transactionId = transactionId,
                    transactionDate = transactionDate,
                    key = it.key,
                    annualSeatFee = it.value,
                    itemData = itemDataMap[it.key],
                )
            }

    fun map(
        transactionId: String,
        transactionDate: LocalDateTime,
        key: AnnualSeatFeeDataKey,
        annualSeatFee: AnnualSeatFee,
        itemData: String?,
    ): PayableItemStoreDto =
        PayableItemStoreDto(
            amount = annualSeatFee.amount,
            currency = CurrencyCode.valueOf(annualSeatFee.currencyCode),
            companyId = key.companyId,
            transactionId = transactionId,
            month = transactionDate.monthValue,
            year = transactionDate.year,
            contractId = annualSeatFee.contractId!!,
            annualSeatPaymentTerm = annualSeatFee.annualSeatPaymentTerm,
            countryCode = annualSeatFee.countryCode,
            itemType = key.lineItemType,
            itemData = itemData ?: "{}",
            periodStartDate = annualSeatFee.period.startDate.toLocalDate(),
            periodEndDate = annualSeatFee.period.endDate.toLocalDate(),
            versionId = key.computeHash(),
            originalTimestamp = key.oTime,
        )
}
