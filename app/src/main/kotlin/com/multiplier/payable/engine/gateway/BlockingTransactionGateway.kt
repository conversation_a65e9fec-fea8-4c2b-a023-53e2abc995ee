package com.multiplier.payable.engine.gateway

import com.multiplier.payable.engine.TransactionCommand
import com.multiplier.payable.engine.TransactionCommandHandler
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service

@Service
@Qualifier("blocking")
class BlockingTransactionGateway(
    @Lazy @Qualifier("blocking-handler") val handler: TransactionCommandHandler<TransactionCommand>,
) : TransactionGateway<TransactionCommand> {
    private val log = LoggerFactory.getLogger(this::class.java)

    override fun fire(message: TransactionGatewayMessage<TransactionCommand>) {
        log.info("Publish message = {}", message)
        handler.handle(message.payload)
    }
}