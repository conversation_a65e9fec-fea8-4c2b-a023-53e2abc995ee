package com.multiplier.payable.engine.collector.memberpayable.managementfee

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.adapters.memberpayable.MemberPayableAdapter
import com.multiplier.payable.engine.collector.DataCollector
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.ProcessedMemberPayableCollectorInput
import com.multiplier.payable.engine.collector.memberpayable.MemberPayableItemStoreService
import com.multiplier.payable.engine.memberpayable.MemberPayable
import com.multiplier.payable.engine.memberpayable.managmentfee.MemberPayableManagementFeeBillingResponse
import com.multiplier.payable.engine.memberpayable.managmentfee.MemberPayableManagementFeeCalculator
import com.multiplier.payable.engine.memberpayable.managmentfee.MemberPayableManagementFeeCalculatorInput
import com.multiplier.payable.engine.memberpayable.managmentfee.MemberPayableManagementFeePricingResponse
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.springframework.stereotype.Component
import java.time.ZoneOffset

@Component
class MemberPayableManagementFeeDataCollector(
    private val dataCollectorInputProcessor: DataCollectorInputProcessor<ProcessedMemberPayableCollectorInput>,
    private val memberPayableAdapter: MemberPayableAdapter,
    private val memberPayableManagementFeeCalculator: MemberPayableManagementFeeCalculator,
    private val memberPayableItemStoreService: MemberPayableItemStoreService,
): DataCollector {

    private companion object {
        private val log = mu.KotlinLogging.logger {}
    }

    override fun getSupportedType(): LineItemType = LineItemType.MANAGEMENT_FEE_FREELANCER

    override fun handle(command: InvoiceCommand) {
        val processedInput = dataCollectorInputProcessor.process(command)
        handle(processedInput)
    }

    private fun handle(processedInput: ProcessedMemberPayableCollectorInput) {
        log.info { "Start handling member payable management fee data collector " +
                "for transaction id: ${processedInput.transactionId}" +
                "for member payable ids = ${processedInput.memberPayableInvoiceCommand.memberPayableIds}" }

        memberPayableAdapter.getMemberPayablesByIds(
            memberPayableIds = processedInput.memberPayableInvoiceCommand.memberPayableIds,
        ).forEach {
            calculateManagementFee(it, processedInput)
        }

    }

    private fun calculateManagementFee(
        memberPayable: MemberPayable,
        input: ProcessedMemberPayableCollectorInput
    ) {
        val memberPayableManagementFee = memberPayableManagementFeeCalculator.calculate(
            MemberPayableManagementFeeCalculatorInput(
                memberPayable = memberPayable,
                invoicingStartDate = input.timeQueryDuration.toDateRange().startDate.toInstant(ZoneOffset.UTC),
                invoicingEndDate = input.timeQueryDuration.toDateRange().endDate.toInstant(ZoneOffset.UTC),
            )
        )

        return when (memberPayableManagementFee) {
            is MemberPayableManagementFeePricingResponse -> memberPayableItemStoreService.normalizeAndSave(
                transactionId = input.transactionId,
                memberPayableAmountMap = mapOf(memberPayable to memberPayableManagementFee.amount),
                monthYearDuration = input.timeQueryDuration,
                type = getSupportedType()
            )

            is MemberPayableManagementFeeBillingResponse -> memberPayableItemStoreService.normalizeAndSaveBills(
                transactionId = input.transactionId,
                memberPayableBillingMap = mapOf(memberPayable to memberPayableManagementFee.billedItems),
                monthYearDuration = input.timeQueryDuration,
                type = getSupportedType()
            )

            else -> error("Unexpected response type: ${memberPayableManagementFee::class}")
        }
    }
}