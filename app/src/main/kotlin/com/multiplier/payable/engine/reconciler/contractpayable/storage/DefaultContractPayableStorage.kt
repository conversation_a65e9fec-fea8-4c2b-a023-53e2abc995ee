package com.multiplier.payable.engine.reconciler.contractpayable.storage

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.JpaContractDepositPayableRepository
import com.multiplier.core.payable.repository.model.JpaContractDepositPayable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class DefaultContractPayableStorage(
    private val jpaCompanyPayableRepository: JpaCompanyPayableRepository,
    private val jpaContractDepositPayableRepository: JpaContractDepositPayableRepository,
) : ContractPayableStorage {

    @Transactional
    override fun store(context: ContractPayableStorageContext) {

        val jpaCompanyPayable = jpaCompanyPayableRepository.findById(context.companyPayableId)
            .orElseThrow { IllegalArgumentException("Company payable with ${context.companyPayableId} not found") }

        val contractDepositPayable = JpaContractDepositPayable.builder()
            .contractId(context.contractId)
            .companyPayable(jpaCompanyPayable)
            .build()

        jpaContractDepositPayableRepository.save(contractDepositPayable)
    }
}
