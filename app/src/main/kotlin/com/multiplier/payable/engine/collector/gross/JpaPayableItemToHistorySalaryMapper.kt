package com.multiplier.payable.engine.collector.gross

import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.payableitem.JpaPayableItemStore
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class JpaPayableItemToHistorySalaryMapper {
    fun map(item: JpaPayableItemStore): HistoryPayroll {
        return HistoryPayroll(
            contractId = item.contractId!!,
            grossSalary = BigDecimal(item.amount),
            currencyCode = CurrencyCode.valueOf(item.currency.name),
            countryCode = item.countryCode.name,
            originalTimestamp = item.originalTimestamp.time,
            versionId = item.versionId,
            companyId = item.companyId,
            month = item.month,
            year = item.year,
        )
    }
}
