package com.multiplier.payable.engine.deposit

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.currency.CurrencyCode
import java.io.Serializable

@JsonIgnoreProperties(ignoreUnknown = true)
data class Deposit(
    val depositId: Long? = null,
    val type: DepositType,
    val amount: Double,
    val currencyCode: CurrencyCode,
    val contractType: ContractType,
    val additionalLeavesInDays: Double = 0.0,
    val noticePeriod: Double = 1.0,
) : Serializable {
    companion object {
        private const val serialVersionUID = 1L
    }
}
