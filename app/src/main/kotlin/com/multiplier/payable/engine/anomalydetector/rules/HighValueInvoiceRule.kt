package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.fetcher.IADCompanyPayableFetcher
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.core.currency.CurrencyServiceV2
import com.multiplier.core.currency.data.Amount
import com.multiplier.core.currency.data.ConversionInstruction
import com.multiplier.core.currency.data.CustomisationInstruction
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.rules.processor.BaseAnomalyDetectionRule
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.types.PayableStatus
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.LocalDate
import java.util.Locale

/**
 * Checks if the invoice amount exceeds historical thresholds using per-employee normalization.
 * This rule compares both absolute amounts and per-employee amounts to avoid false positives
 * for companies with varying employee counts or recent growth.
 */
@Component
class HighValueInvoiceRule(
    private val iadCompanyPayableFetcher: IADCompanyPayableFetcher,
    private val currencyServiceV2: CurrencyServiceV2,
    featureFlagService: FeatureFlagService,
) : BaseAnomalyDetectionRule(featureFlagService) {
    override val ruleName: String = "HighValueInvoice"
    override val featureFlagName: String = "ENABLE_HIGH_VALUE_INVOICE_CHECK"
    override val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.HIGH_VALUE_INVOICE

    companion object {
        private val log = KotlinLogging.logger {}

        // Default absolute threshold in USD (fallback)
        private const val DEFAULT_ABSOLUTE_THRESHOLD_USD = 100000.0

        // Historical threshold multiplier (150% of historical max)
        private const val THRESHOLD_MULTIPLIER = 1.5

        // Minimum historical months required for comparison
        private const val MINIMUM_HISTORICAL_MONTHS = 3

        // Employee-related line item types for counting unique contracts
        private val EMPLOYEE_LINE_ITEM_TYPES = setOf(
            LineItemType.GROSS_SALARY,
            LineItemType.BILLED_GROSS_SALARY,
            LineItemType.BILLED_GROSS_SALARY_SUPPLEMENTARY,
            LineItemType.EOR_SALARY_DISBURSEMENT,
            LineItemType.EOR_EXPENSE_DISBURSEMENT
        )

        // Statuses to exclude from historical data (only include clean, processed invoices)
        private val INACTIVE_PAYABLE_STATUSES = setOf(
            PayableStatus.DRAFT,
            PayableStatus.PENDING,
            PayableStatus.VOIDED,
            PayableStatus.DELETED
        )
    }

    override val type: DetectionRuleType = DetectionRuleType.HIGH_VALUE_INVOICE

    /**
     * Rule-specific detection logic using per-employee normalization and historical comparison.
     */
    override fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult {
        // Get the invoice data (we know it's not null because isRequestValid was called)
        val companyPayable = request.payable!!
        val invoiceDTO = request.invoiceDTO!!

        // Get the invoice amount and currency
        val invoiceAmount = companyPayable.totalAmount
        val currencyCode = companyPayable.currency
        val companyId = companyPayable.companyId

        // Track any anomalies found
        val anomalies = mutableListOf<String>()

        // Get current employee count from invoice line items
        val currentEmployeeCount = getEmployeeCountFromInvoice(invoiceDTO)
        log.info { "Current employee count for company $companyId: $currentEmployeeCount" }

        // Get historical data for comparison
        val historicalInvoices = getHistoricalInvoices(companyId, companyPayable.month, companyPayable.year)
        log.info { "Found ${historicalInvoices.size} historical invoices for company $companyId" }

        return when {
            // Case 1: Insufficient historical data - use absolute threshold as fallback
            historicalInvoices.size < MINIMUM_HISTORICAL_MONTHS -> {
                log.info { "Insufficient historical data (${historicalInvoices.size} months), using absolute threshold" }
                checkAbsoluteThreshold(invoiceAmount, currencyCode, companyId, anomalies)
            }

            // Case 2: No employees detected - use absolute threshold
            currentEmployeeCount == 0 -> {
                log.info { "No employees detected in current invoice, using absolute threshold" }
                checkAbsoluteThreshold(invoiceAmount, currencyCode, companyId, anomalies)
            }

            // Case 3: Use per-employee historical comparison
            else -> {
                checkPerEmployeeThreshold(
                    invoiceAmount,
                    currencyCode,
                    currentEmployeeCount,
                    historicalInvoices,
                    companyId,
                    anomalies
                )
            }
        }
    }

    /**
     * Checks absolute threshold (fallback method) - converts invoice amount to USD for comparison
     */
    private fun checkAbsoluteThreshold(
        invoiceAmount: Double,
        currencyCode: CurrencyCode,
        companyId: Long,
        anomalies: MutableList<String>
    ): AnomalyDetectionResult {
        // Convert invoice amount to USD for comparison with USD threshold
        val invoiceAmountInUSD = convertToUSD(invoiceAmount, currencyCode, companyId)

        if (invoiceAmountInUSD > DEFAULT_ABSOLUTE_THRESHOLD_USD) {
            val message = String.format(
                Locale.ENGLISH,
                "Invoice amount %.2f %s (%.2f USD) exceeds absolute threshold %.2f USD. Invoice needs manual intervention",
                invoiceAmount,
                currencyCode,
                invoiceAmountInUSD,
                DEFAULT_ABSOLUTE_THRESHOLD_USD
            )
            anomalies.add(message)
            log.warn { message }
        }

        return if (anomalies.isNotEmpty()) {
            createFailureResult(anomalies)
        } else {
            log.info { "No $ruleName anomalies detected (absolute threshold check)" }
            createSuccessResult(listOf("All invoice amounts are within acceptable absolute thresholds"))
        }
    }

    /**
     * Checks per-employee threshold using historical comparison
     */
    private fun checkPerEmployeeThreshold(
        invoiceAmount: Double,
        currencyCode: CurrencyCode,
        currentEmployeeCount: Int,
        historicalInvoices: List<JpaCompanyPayable>,
        companyId: Long,
        anomalies: MutableList<String>
    ): AnomalyDetectionResult {
        val currentPerEmployee = invoiceAmount / currentEmployeeCount

        // Get historical per-employee amounts
        val historicalPerEmployeeAmounts = historicalInvoices.mapNotNull { payable ->
            payable.invoice?.let { invoice ->
                val employeeCount = getEmployeeCountFromJpaInvoice(invoice)
                if (employeeCount > 0) {
                    payable.totalAmount / employeeCount
                } else null
            }
        }

        if (historicalPerEmployeeAmounts.isEmpty()) {
            log.info { "No valid historical per-employee data found, falling back to absolute threshold" }
            return checkAbsoluteThreshold(invoiceAmount, currencyCode, companyId, anomalies)
        }

        // Calculate threshold as 150% of historical maximum per-employee amount
        val maxHistoricalPerEmployee = historicalPerEmployeeAmounts.maxOrNull() ?: 0.0
        val perEmployeeThreshold = maxHistoricalPerEmployee * THRESHOLD_MULTIPLIER

        log.info {
            "Per-employee analysis: current=%.2f, historical_max=%.2f, threshold=%.2f".format(
                currentPerEmployee, maxHistoricalPerEmployee, perEmployeeThreshold
            )
        }

        if (currentPerEmployee > perEmployeeThreshold) {
            val message = String.format(
                Locale.ENGLISH,
                "Per-employee amount %.2f %s exceeds historical threshold %.2f %s (%.1f%% of historical max). " +
                "Current: %.2f %s for %d employees vs historical max: %.2f %s per employee",
                currentPerEmployee,
                currencyCode,
                perEmployeeThreshold,
                currencyCode,
                (THRESHOLD_MULTIPLIER * 100),
                invoiceAmount,
                currencyCode,
                currentEmployeeCount,
                maxHistoricalPerEmployee,
                currencyCode
            )
            anomalies.add(message)
            log.warn { message }
        }

        return if (anomalies.isNotEmpty()) {
            createFailureResult(anomalies)
        } else {
            log.info { "No $ruleName anomalies detected (per-employee threshold check)" }
            createSuccessResult(listOf("All invoice amounts are within acceptable per-employee thresholds"))
        }
    }

    /**
     * Gets employee count from current invoice by counting unique contract IDs in employee-related line items
     */
    private fun getEmployeeCountFromInvoice(invoiceDTO: com.multiplier.core.payable.adapters.api.InvoiceDTO): Int {
        return invoiceDTO.lineItems
            ?.filter { lineItem ->
                lineItem.itemType in EMPLOYEE_LINE_ITEM_TYPES && lineItem.contractId != null
            }
            ?.mapNotNull { it.contractId }
            ?.toSet()
            ?.size ?: 0
    }

    /**
     * Gets employee count from historical JPA invoice by counting unique contract IDs
     */
    private fun getEmployeeCountFromJpaInvoice(invoice: com.multiplier.core.payable.repository.model.JpaInvoice): Int {
        return invoice.lineItems
            ?.filter { lineItem ->
                lineItem.itemType in EMPLOYEE_LINE_ITEM_TYPES && lineItem.contractId != null
            }
            ?.mapNotNull { it.contractId }
            ?.toSet()
            ?.size ?: 0
    }

    /**
     * Gets historical invoices for the last 6 months (excluding current month)
     */
    private fun getHistoricalInvoices(companyId: Long, currentMonth: Int, currentYear: Int): List<JpaCompanyPayable> {
        val historicalInvoices = mutableListOf<JpaCompanyPayable>()

        // Get invoices from the last 6 months (excluding current month)
        for (monthsBack in 1..6) {
            val targetDate = LocalDate.of(currentYear, currentMonth, 1).minusMonths(monthsBack.toLong())

            try {
                // Create a dummy company payable for the target date
                val dummyPayable = JpaCompanyPayable.builder()
                    .companyId(companyId)
                    .month(targetDate.monthValue)
                    .year(targetDate.year)
                    .build()

                val monthlyPayables = iadCompanyPayableFetcher.getInvoicesForReqTime(
                    dummyPayable,
                    false // We're manually calculating the target date
                ).filter { !INACTIVE_PAYABLE_STATUSES.contains(it.status) }

                historicalInvoices.addAll(monthlyPayables)
                log.debug { "Found ${monthlyPayables.size} invoices for ${targetDate.year}-${targetDate.monthValue}" }
            } catch (e: Exception) {
                log.warn(e) { "Failed to fetch historical invoices for ${targetDate.year}-${targetDate.monthValue}" }
            }
        }

        log.info { "Retrieved ${historicalInvoices.size} total historical invoices for company $companyId" }
        return historicalInvoices
    }



    /**
     * Converts the given amount from the source currency to USD
     */
    private fun convertToUSD(amount: Double, fromCurrency: CurrencyCode, companyId: Long): Double {
        // If already in USD, no conversion needed
        if (fromCurrency == CurrencyCode.USD) {
            return amount
        }

        return try {
            val sourceAmount = Amount(BigDecimal.valueOf(amount), fromCurrency)
            val conversionResponse = currencyServiceV2.convertCurrency(
                sourceAmount = sourceAmount,
                targetCurrency = CurrencyCode.USD,
                conversionInstruction = ConversionInstruction(),
                customisationInstruction = CustomisationInstruction()
            )

            val convertedAmount = conversionResponse.convertedAmount.amount.toDouble()
            log.info {
                "Converted %.2f %s to %.2f USD for company %d".format(
                    amount, fromCurrency, convertedAmount, companyId
                )
            }
            convertedAmount
        } catch (e: Exception) {
            log.warn(e) {
                "Failed to convert %.2f %s to USD for company %d, using original amount".format(
                    amount, fromCurrency, companyId
                )
            }
            // Fallback: return original amount (this will likely trigger the threshold, which is safer)
            amount
        }
    }
}
