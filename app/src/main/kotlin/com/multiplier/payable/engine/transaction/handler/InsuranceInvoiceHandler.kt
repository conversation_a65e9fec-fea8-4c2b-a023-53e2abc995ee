package com.multiplier.payable.engine.transaction.handler

import com.multiplier.core.payable.pricing.adapter.PricingServiceAdapter
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.generator.FinancialTransactionGenerator
import org.springframework.stereotype.Service
import java.time.LocalDateTime

@Service
class InsuranceInvoiceHandler(
    private val invoiceTransactionBuilder: InvoiceTransactionBuilder,
    private val pricingServiceAdapter: PricingServiceAdapter,
    private val invoiceGenerator: FinancialTransactionGenerator,
) : FinancialTransactionHandler {

    override val transactionType: TransactionType
        get() = TransactionType.INSURANCE_INVOICE

    override fun handle(command: InvoiceCommand) {
        val pricing = pricingServiceAdapter.getCompanyPricing(command.companyId)
        val dueDate = LocalDateTime.now().plusDays(pricing.paymentTermInDays?.toLong() ?: 7L)
        val commandWithDueDateAndBillingCurrencyCode = command.copy(
            billingCurrencyCode = CurrencyCode.valueOf(pricing.billingCurrencyCode.name),
            dueDate = dueDate
        )
        val invoicesToGenerate = invoiceTransactionBuilder.build(commandWithDueDateAndBillingCurrencyCode)
        invoicesToGenerate.forEach(invoiceGenerator::generate)
    }
}