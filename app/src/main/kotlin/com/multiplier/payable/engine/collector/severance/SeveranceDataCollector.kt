package com.multiplier.payable.engine.collector.severance

import com.multiplier.core.config.featureflag.FeatureFlag
import com.multiplier.core.config.featureflag.FeatureFlagService
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataCollector
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.DefaultProcessedCollectorInput
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class SeveranceDataCollector(
    private val dataCollectorInputProcessor: DataCollectorInputProcessor<DefaultProcessedCollectorInput>,
    private val severancePayrollDataCollectorHandler: SeverancePayrollDataCollectorHandler,
    private val severanceServiceDataCollectorHandler: SeveranceServiceDataCollectorHandler,
    private val featureFlagService: FeatureFlagService
) : DataCollector {

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    override fun getSupportedType(): LineItemType = LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL

    override fun handle(command: InvoiceCommand) {
        val processedInput = dataCollectorInputProcessor.process(command)
        val supportedType = getSupportedType()
        if (isSeveranceServiceEnabled()) {
            logger.info { "calling severance service to collect severance" }
            severanceServiceDataCollectorHandler.handle(processedInput, supportedType)
        } else {
            logger.info { "calling payroll service to collect severance" }
            severancePayrollDataCollectorHandler.handle(processedInput, supportedType)
        }
    }

    private fun isSeveranceServiceEnabled(): Boolean =
        featureFlagService.feature(FeatureFlag.SEVERANCE_SERVICE_ENABLED.flagName, emptyMap()).on
}
