package com.multiplier.payable.engine.payableitem.deposit.description.builder

import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.deposit.DepositPayableItemDescriptionBuilderContext
import com.multiplier.payable.engine.deposit.DepositType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.AmountFormatter
import org.springframework.stereotype.Component

@Component
class FixedPayPayableItemDescriptionBuilder(
    private val amountFormatter: AmountFormatter,
) : DepositPayableItemDescriptionBuilder {
    companion object {
        private const val ENHANCED_DESCRIPTION_FORMAT = "Deposit - Additional Fixed Pay \nTotal: %s"
        private const val DEFAULT_DESCRIPTION_FORMAT = "%s: %s %s"
    }

    override fun getType(): DepositType = DepositType.FIXED_PAY

    override fun build(context: DepositPayableItemDescriptionBuilderContext): String =
        if (context.deposit.contractType == ContractType.EMPLOYEE && context.enhancedDescriptionEnabled) {
            enhancedSalaryDescription(context)
        } else {
            val label = getLabel()
            val amountStr = amountFormatter.format(context.deposit.amount)
            DEFAULT_DESCRIPTION_FORMAT.format(label, context.deposit.currencyCode, amountStr)
        }

    private fun enhancedSalaryDescription(context: DepositPayableItemDescriptionBuilderContext): String {
        val deposit = context.deposit
        val formattedAmount = "${deposit.currencyCode} ${amountFormatter.format(deposit.amount)}"
        return ENHANCED_DESCRIPTION_FORMAT.format(formattedAmount)
    }

    private fun getLabel(): String = "Additional compensation"
}