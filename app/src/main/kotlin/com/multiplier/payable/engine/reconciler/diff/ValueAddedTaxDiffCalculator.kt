package com.multiplier.payable.engine.reconciler.diff

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.payableitem.PayableItem
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class ValueAddedTaxDiffCalculator {

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    fun calculate(
        newVATPayableItems: List<PayableItem>,
        invoicedVATPayableItems: List<PayableItem>,
        zeroAmountAdjustmentManagementFees: List<PayableItem>,
    ): List<PayableItem> {

        logger.info { "Total items for VAT: ${newVATPayableItems.size}" }

        val newVATItemsExpectManagementFee = newVATPayableItems
            .filter {
                it.lineItemType != LineItemType.VAT_MANAGEMENT_FEE_EOR_PAYROLL.name
                        && it.lineItemType != LineItemType.VAT_BILLED_MANAGEMENT_FEE.name
            }

        logger.info { "VAT items apart from management fee = ${newVATPayableItems.size}" }

        val alreadyBilledZeroDeltaManagementFeeVAT = findManagementFeeVATWhereManagementFeeHasZeroDelta(
            zeroAmountAdjustmentManagementFees,
            invoicedVATPayableItems
        )

        logger.info { "VAT items for already billed management fee with zero delta. " +
                "contractIds = ${alreadyBilledZeroDeltaManagementFeeVAT.map { it.contractId }}" }

        val newManagementFeeVATItems = filterManagementFeeVATItemsForAlreadyBilledZeroDeltaItems(
            newVATPayableItems.filter { it.lineItemType == LineItemType.VAT_MANAGEMENT_FEE_EOR_PAYROLL.name },
            alreadyBilledZeroDeltaManagementFeeVAT
        )

        logger.info { "VAT items for management fee without non delta management fees " +
                "contractIds = ${newManagementFeeVATItems.map { it.contractId }}" }

        return newVATItemsExpectManagementFee +
                alreadyBilledZeroDeltaManagementFeeVAT +
                newManagementFeeVATItems
    }

    private fun findManagementFeeVATWhereManagementFeeHasZeroDelta(
        zeroAmountAdjustmentManagementFees: List<PayableItem>,
        invoicedVATPayableItems: List<PayableItem>,
    ): List<PayableItem> {
        val contractIds = zeroAmountAdjustmentManagementFees.map { it.contractId }
        return invoicedVATPayableItems.filter { contractIds.contains(it.contractId) }
            .also { logger.info { "Applying VAT on already billed management fee line items for ${it.map { it.contractId }}" } }
    }

    private fun filterManagementFeeVATItemsForAlreadyBilledZeroDeltaItems(
        newManagementFeeVATPayableItems: List<PayableItem>,
        alreadyBilledManagementFeeVATItems: List<PayableItem>,
    ): List<PayableItem> {
        val contractIds = alreadyBilledManagementFeeVATItems.map { it.contractId }
        return newManagementFeeVATPayableItems.filterNot { contractIds.contains(it.contractId) }
            .also { logger.info { "Applying VAT on new management fee line items for ${it.map { it.contractId }}" } }
    }
}