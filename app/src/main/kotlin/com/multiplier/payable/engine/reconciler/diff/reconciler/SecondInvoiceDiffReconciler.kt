package com.multiplier.payable.engine.reconciler.diff.reconciler

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.reconciler.diff.SecondInvoiceDiffCalculator
import com.multiplier.payable.engine.reconciler.diff.SecondInvoiceTrinetDiffCalculator
import com.multiplier.payable.engine.reconciler.diff.handler.DefaultDiffHandlerContext
import com.multiplier.payable.engine.reconciler.diff.handler.DeltaAwareDiffHandler
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import org.springframework.stereotype.Component

@Component
class SecondInvoiceDiffReconciler(
    private val secondInvoiceDiffCalculator: SecondInvoiceDiffCalculator,
    private val secondInvoiceTrinetDiffCalculator: SecondInvoiceTrinetDiffCalculator,
    private val deltaAwareDiffHandler: DeltaAwareDiffHandler,
) : InvoiceDiffReconciler {

    override fun reconcile(
        command: InvoiceCommand,
        template: TransactionTemplate,
        old: List<PayableItem>,
        new: List<PayableItem>,
    ): List<PayableItem> {
        val diff = if (template.allowMultipleInvoiceSameMonth) {
            secondInvoiceTrinetDiffCalculator.calculate(
                new.filter { applicableItem(template, it) },
                old.filter { applicableItem(template, it) }
            )
        } else {
            secondInvoiceDiffCalculator.calculate(
                new.filter { applicableItem(template, it) },
                old.filter { applicableItem(template, it) }
            )
        }
        return deltaAwareDiffHandler
            .handle(DefaultDiffHandlerContext(diff))
    }

    override fun transactionType(): TransactionType {
        return TransactionType.SECOND_INVOICE
    }

}