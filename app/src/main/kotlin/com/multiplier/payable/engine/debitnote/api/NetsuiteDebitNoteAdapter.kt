package com.multiplier.payable.engine.debitnote.api

import com.multiplier.core.payable.adapters.netsuite.exception.NetsuiteAdapterException
import com.multiplier.core.payable.adapters.netsuite.webservices.NetsuiteWebserviceClient
import com.multiplier.core.payable.adapters.netsuite.webservices.NetsuiteWebserviceException
import com.multiplier.core.payable.creditnote.api.ApiResponseParser
import lombok.RequiredArgsConstructor
import org.springframework.stereotype.Component

@Component
@RequiredArgsConstructor
class NetsuiteDebitNoteAdapter(
        private val customSaleMapper: CustomSaleMapper,
        private val netsuiteWebserviceClient: NetsuiteWebserviceClient,
        private val apiResponseParser: ApiResponseParser,
) : DebitNoteAdapter {
    override fun create(request: CreateDebitNoteApiRequest): DebitNoteApiResponse {
        return try {
            val customSale = customSaleMapper.map(request)
            val response = netsuiteWebserviceClient.createCustomSale(customSale)
            val debitNoteId = apiResponseParser.getInternalId(response)

            DebitNoteApiResponse(externalId = debitNoteId)
        } catch (ne: NetsuiteWebserviceException) {
            throw NetsuiteAdapterException(ne.message, ne)
        }
    }

    override fun delete(debitNoteId: String) {
        try {
            netsuiteWebserviceClient.deleteCustomSale(debitNoteId)
        } catch (e: NetsuiteWebserviceException) {
            throw NetsuiteAdapterException(e.message, e)
        }
    }
}
