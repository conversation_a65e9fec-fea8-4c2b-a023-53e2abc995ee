package com.multiplier.payable.engine.feature

import com.multiplier.core.config.featureflag.FeatureFlagConstant
import com.multiplier.core.config.featureflag.FeatureFlagService
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.time.LocalDate

/**
 * Checker to determine if Deposit V2 feature is enabled for a company.
 *
 * @param featureFlagService Service to check feature flags.
 */
@Component
class DepositV2Checker(
    private val featureFlagService: FeatureFlagService
) {

    private val cutoffDate = LocalDate.of(2023, 10, 16)

    companion object {
        private val log = KotlinLogging.logger {}
    }

    /**
     * Checks if Deposit V2 is enabled for the given company ID.
     *
     * @param companyId The ID of the company to check.
     * @return `true` if Deposit V2 is enabled, `false` otherwise.
     */
    fun isV2Enabled(companyId: Long): Boolean {
        log.debug("Checking if Deposit V2 is enabled for companyId: {}", companyId)

        // TODO Get MSA signed date via gRPC
        // val msaSignedDate: LocalDate? = companyOnboardingService.getMSASignedDate(context.companyId)
        val msaSignedDate: LocalDate? = null

        val isBeforeCutoff = msaSignedBeforeCutoff(msaSignedDate)
        val featureEnabled = featureFlagService.feature(
            "contract-deposit-v2",
            mapOf(FeatureFlagConstant.PARAMS.COMPANY to companyId)
        ).on

        log.debug(
            "MSA signed date: {}, Before cutoff: {}, Feature enabled: {}",
            msaSignedDate,
            isBeforeCutoff,
            featureEnabled
        )

        return !isBeforeCutoff && featureEnabled
    }

    /**
     * Checks if the MSA was signed before the cutoff date.
     *
     * @param msaSignedDate The date when the MSA was signed.
     * @return `true` if the MSA was signed before the cutoff date, `false` otherwise.
     */
    private fun msaSignedBeforeCutoff(msaSignedDate: LocalDate?): Boolean {
        val isBeforeCutoff = msaSignedDate?.isBefore(cutoffDate) ?: false
        log.debug("MSA signed date: {}, isBeforeCutoff: {}", msaSignedDate, isBeforeCutoff)
        return isBeforeCutoff
    }
}
