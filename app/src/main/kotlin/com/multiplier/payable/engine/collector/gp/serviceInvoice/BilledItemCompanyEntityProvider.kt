package com.multiplier.payable.engine.collector.gp.serviceInvoice

import com.multiplier.billing.grpc.billing.Billing.BilledItem
import com.multiplier.common.exception.MplBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.payable.engine.transaction.InvoiceCommand
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class BilledItemCompanyEntityProvider {

    private companion object {
        private val logger = KotlinLogging.logger {  }
        private const val ENTITY_ID_KEY = "entity_id"
    }

    fun getCompanyEntityId(
        billedItem: BilledItem,
        command: InvoiceCommand,
    ): Long {
        val commandEntityId = command.entityId
        val billedItemEntityId = billedItem.entityId

        logger.info { "Entity id from command = $commandEntityId. EntityId from billed item = $billedItemEntityId. transactionId = ${command.transactionId}" }

        val meterEntityId = getEntityIdFromMeter(billedItem)

        return when {
            // No metering entity found -> fallback to billed item entity
            meterEntityId == null -> {
                commandEntityId?.let { logger.info { "Entity Id from command: $it" } }
                logger.info { "No entity id found in metering. Returning billedItemEntityId = $billedItemEntityId. Bill id = ${billedItem.id}" }
                billedItemEntityId
            }

            // No command entity -> use metering entity
            commandEntityId == null -> {
                logger.info { "Using entity id from metering: $meterEntityId. Bill id = ${billedItem.id}" }
                meterEntityId
            }

            // Both entities present -> validate they match
            commandEntityId == meterEntityId -> {
                logger.info { "Command and metering entity ids match: $meterEntityId. Bill id = ${billedItem.id}" }
                meterEntityId
            }

            // Mismatch between command and metering entities -> error
            else -> throw MplBusinessException(
                PayableErrorCode.COMPANY_ENTITY_ID_MISMATCH,
                "Entity ID mismatch: command entity ID ($commandEntityId) doesn't match meter entity ID ($meterEntityId) for bill ${billedItem.id}"
            )
        }

    }

    private fun getEntityIdFromMeter(billedItem: BilledItem): Long? {
        val entityIds = billedItem.usageList.flatMap { usage ->
            usage.labelsList
                .filter { it.key == ENTITY_ID_KEY }
                .mapNotNull { it.value }
                .filter { it.isNotBlank() }
        }

        return when {
            entityIds.isEmpty() -> {
                logger.info { "No entity ids found in the metering data. billId = ${billedItem.id}" }
                null
            }

            entityIds.distinct().size == 1 -> {
                val entityId = entityIds.first().toLong()
                logger.info { "Found entity id from metering data. entityId = $entityId. billId = ${billedItem.id}" }
                entityId
            }

            else -> {
                val distinctIds = entityIds.distinct()
                logger.error { "Found multiple distinct entity ids in the metering data. entityIds = $distinctIds. Bill Id: ${billedItem.id}" }
                throw MplBusinessException(
                    PayableErrorCode.MULTIPLE_ENTITIES_FOUND_IN_METER,
                    "Multiple entity IDs found in metering data for bill ${billedItem.id}: $distinctIds"
                )
            }
        }
    }
}