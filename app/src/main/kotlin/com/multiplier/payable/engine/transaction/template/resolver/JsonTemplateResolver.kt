package com.multiplier.payable.engine.transaction.template.resolver

import com.google.common.annotations.VisibleForTesting
import com.google.gson.GsonBuilder
import com.multiplier.payable.engine.formatter.DataFormatter
import com.multiplier.payable.engine.formatter.template.DataFormatterTemplate
import com.multiplier.payable.engine.formatter.template.resolver.DataFormatterTemplateResolver
import com.multiplier.payable.engine.formatter.template.resolver.TextPlaceholderResolver
import com.multiplier.payable.engine.splitter.ItemSplitter
import com.multiplier.payable.engine.splitter.template.SelectorSplitterTemplate
import com.multiplier.payable.engine.splitter.template.resolver.SelectorSplitterTemplateResolver
import com.multiplier.payable.engine.transaction.template.DefaultTransactionTemplate
import com.multiplier.payable.engine.transaction.template.FxConfig
import com.multiplier.payable.engine.transaction.template.FxConfigDeserializer
import com.multiplier.payable.engine.transaction.template.JsonTemplate
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import org.springframework.stereotype.Component

@Component
class JsonTemplateResolver(
    private val dataFormatterTemplateResolver: DataFormatterTemplateResolver,
    private val textPlaceholderResolver: TextPlaceholderResolver,
    private val selectorSplitterTemplateResolver: SelectorSplitterTemplateResolver,
) : TemplateResolver {
    override fun resolve(jsonString: String): TransactionTemplate {
        val gson = GsonBuilder()
            .registerTypeAdapter(FxConfig::class.java, FxConfigDeserializer())
            .create()
        val jsonTemplate: JsonTemplate = gson.fromJson(jsonString, JsonTemplate::class.java)
        val dataFormatter = getItemFormatter(jsonTemplate.dataFormatters)
        val payableFormatter = getPayableFormatter(jsonTemplate.payableFormatters)
        val itemSplitter = getItemSplitter(jsonTemplate.selectorSplitters)

        return DefaultTransactionTemplate(
            dataFormatter = dataFormatter,
            payableFormatter = payableFormatter,
            referenceTemplate = jsonTemplate.referenceTemplate,
            textPlaceholderResolver = textPlaceholderResolver,
            identifier = jsonTemplate.identifier,
            lineItemTypes = jsonTemplate.lineItemTypes,
            itemSplitter = itemSplitter,
            mergedTransactionTypes = jsonTemplate.mergedTransactionTypes ?: emptyList(),
            lookUpTransactionTypes = jsonTemplate.lookUpTransactionTypes ?: emptyList(),
            fxConfig = jsonTemplate.fxConfig,
            skipIsrGeneration = jsonTemplate.skipIsrGeneration,
            allowMultipleInvoiceSameMonth = jsonTemplate.allowMultipleInvoiceSameMonth,
        )
    }

    @VisibleForTesting
    fun getItemFormatter(dataFormatterTemplates: List<DataFormatterTemplate>): DataFormatter {
        return dataFormatterTemplateResolver.resolve(dataFormatterTemplates)
    }

    fun getPayableFormatter(dataFormatterTemplates: List<DataFormatterTemplate>?): DataFormatter? {
        if (dataFormatterTemplates.isNullOrEmpty()) return null
        return dataFormatterTemplateResolver.resolve(dataFormatterTemplates)
    }

    fun getItemSplitter(selectorSplitterTemplates: List<SelectorSplitterTemplate>?): ItemSplitter {
        return selectorSplitterTemplateResolver.resolve(selectorSplitterTemplates)
    }
}
