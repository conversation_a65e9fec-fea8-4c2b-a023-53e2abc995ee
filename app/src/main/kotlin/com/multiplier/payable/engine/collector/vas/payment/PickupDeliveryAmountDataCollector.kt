package com.multiplier.payable.engine.collector.vas.payment

import com.multiplier.core.payable.adapters.VasIncidentServiceAdapter
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.ProcessedIncidentCollectorInput
import com.multiplier.payable.engine.collector.vas.AbstractVasIncidentPaymentDataCollector
import com.multiplier.payable.engine.collector.vas.IncidentPayableItemStoreNormalizer
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.engine.vas.IncidentType
import org.springframework.stereotype.Component

@Component
class PickupDeliveryAmountDataCollector(
    dataCollectorInputProcessor: DataCollectorInputProcessor<ProcessedIncidentCollectorInput>,
    vasIncidentService: VasIncidentServiceAdapter,
    payableItemStoreService: PayableItemStoreService,
    incidentPayableItemStoreNormalizer: IncidentPayableItemStoreNormalizer,
) : AbstractVasIncidentPaymentDataCollector(
        dataCollectorInputProcessor,
        vasIncidentService,
        payableItemStoreService,
        incidentPayableItemStoreNormalizer,
    ) {
    override fun getSupportedType(): LineItemType = LineItemType.VAS_INCIDENT_PICKUP_DELIVERY_AMOUNT

    override fun getIncidentType(): IncidentType = IncidentType.PICKUP_DELIVERY
}
