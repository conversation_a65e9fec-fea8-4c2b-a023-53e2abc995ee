package com.multiplier.payable.engine.transaction.handler

import com.multiplier.core.payable.pricing.adapter.PricingServiceAdapter
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.reconciler.data.invoice.InvoiceDataProvider
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.generator.FinancialTransactionGenerator
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

@Service
class SecondInvoiceHandler(
    private val invoiceTransactionBuilder: InvoiceTransactionBuilder,
    private val pricingServiceAdapter: PricingServiceAdapter,
    @Qualifier("secondInvoice") private val invoiceGenerator: FinancialTransactionGenerator,
    private val secondInvoiceDataProvider: InvoiceDataProvider,
) : FinancialTransactionHandler {
    override val transactionType: TransactionType
        get() = TransactionType.SECOND_INVOICE

    override fun handle(command: InvoiceCommand) {
        val pricing = pricingServiceAdapter.getCompanyPricing(command.companyId)
        val dueDate = command.transactionDate.plusDays(pricing.paymentTermInDays?.toLong() ?: 7L)
        val activeExistingSecondPayables = secondInvoiceDataProvider.fetchActiveInvoices(command)
            .filter { it.itemType == TransactionType.SECOND_INVOICE }
        val commandWithDueDateAndBillingCurrencyCode = command.copy(
            billingCurrencyCode = CurrencyCode.valueOf(pricing.billingCurrencyCode.name),
            dueDate = dueDate,
            transactionCount = if (activeExistingSecondPayables.isNotEmpty()) activeExistingSecondPayables.size + 1 else null,
        )
        val invoicesToGenerate = invoiceTransactionBuilder.build(commandWithDueDateAndBillingCurrencyCode)

        invoicesToGenerate.forEach(invoiceGenerator::generate)
    }
}