package com.multiplier.payable.engine.collector.payroll

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class ContractPayrollItemStoreService(
    private val payableItemStoreService: PayableItemStoreService,
    private val objectMapper: ObjectMapper,
) {

    fun normalizeAndSaveForCompanyPayrolls(
        contractPayrollsItemStoreInput: ContractPayrollsItemStoreInput,
    ) {
        val transactionId = contractPayrollsItemStoreInput.transactionId
        val monthYearDuration = contractPayrollsItemStoreInput.monthYearDuration
        val itemType = contractPayrollsItemStoreInput.itemType
        val contractPayrolls = contractPayrollsItemStoreInput.contractPayrolls
        val monthYear = monthYearDuration.from.invoiceMonthYear()
        val contractPayrollRolesGroupedByPayrollKey = contractPayrolls.groupBy {
            PayrollSalaryKey(
                companyId = it.companyId,
                monthYear = monthYear,
                itemType = itemType,
                oTime = it.fetchedTime,
            )
        }

        contractPayrollRolesGroupedByPayrollKey.forEach { (payrollSalaryKey, contractPayrolls) ->
            val companyId = payrollSalaryKey.companyId

            log.info(
                "Normalizing and saving data for " +
                        "transactionId = $transactionId " +
                        "itemType = $itemType " +
                        "companyId = ${payrollSalaryKey.companyId}"
            )

            val monthYearDateRange = monthYearDuration.toDateRange()
            val payableItemStoreDtos = contractPayrolls
                .filter { contractPayrollsItemStoreInput.isBillable.test(it) }
                .map {
                    PayableItemStoreDto(
                        month = monthYear.month,
                        year = monthYear.year,
                        itemType = itemType,
                        itemData = objectMapper.writeValueAsString(it),
                        companyId = companyId,
                        contractId = it.contractId,
                        amount = contractPayrollsItemStoreInput.amountCost.apply(it),
                        currency = it.currencyCode,
                        versionId = payrollSalaryKey.computeHash(),
                        originalTimestamp = payrollSalaryKey.oTime,
                        countryCode = it.countryCode,

                        // All payroll items will have invoicing period with beginning of the month to end of the month
                        periodStartDate = monthYearDateRange.startDate.toLocalDate(),
                        periodEndDate = monthYearDateRange.endDate.toLocalDate(),
                    )
                }
                .toList()

            try {
                payableItemStoreService.saveAndIgnoreDuplication(payableItemStoreDtos)
            } catch (e: Exception) {
                log.error(e) { "Error collect data for key = $payrollSalaryKey, transactionId = $transactionId" }
            }
        }
    }

    private companion object {
        private val log = KotlinLogging.logger {}
    }
}