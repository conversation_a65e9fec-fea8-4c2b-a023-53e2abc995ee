package com.multiplier.payable.engine

import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Named
import java.time.LocalDateTime

@Mapper(componentModel = "spring")
abstract class TransactionCommandStatusChangeEventMapper {

    @Mapping(target = "transactionDate", expression = "java(this.mapTransactionDate(command))")
    @Mapping(target = "companyId", expression  = "java(this.mapCompanyId(command))")
    abstract fun mapCommandToEvent(command: TransactionCommand): TransactionStateChangeEvent

    @Named("mapTransactionDate")
    protected fun mapTransactionDate(command: TransactionCommand): LocalDateTime {
        if (command is InvoiceCommand) {
            return command.transactionDate
        }
        throw IllegalArgumentException("Unknown command type ${command::class.simpleName}")
    }

    @Named("mapCompanyId")
    protected fun mapCompanyId(command: TransactionCommand): Long {
        if (command is InvoiceCommand) {
            return command.companyId
        }
        throw IllegalArgumentException("Unknown command type ${command::class.simpleName}")
    }
}