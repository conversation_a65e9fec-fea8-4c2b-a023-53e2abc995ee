package com.multiplier.payable.engine.reconciler.descriptionbuilder.adjustment.gp

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilder
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import org.springframework.stereotype.Component

@Component
class PayslipMinimumCommitmentAdjustmentDescriptionBuilder : PayableItemDescriptionBuilder {

    override val lineItemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PAYSLIP_MINIMUM_COMMITMENT

    override fun build(context: PayableItemDescriptionBuilderContext): String {
        return """
            Minimum Payslip Advance Adjustment (${context.countryCode ?: "N/A"}) ${context.currencyCode} ${context.amountInBaseCurrency}
        """.trimIndent()
    }
}
