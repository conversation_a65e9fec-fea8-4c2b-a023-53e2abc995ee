package com.multiplier.payable.engine.collector.gp.serviceInvoice.model

import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode

data class TotalPayment(
    val transactionId: String,
    val billId: String,
    val companyId: Long,
    val amount: Double,
    val noOfMembers: Int,
    val currencyCode: CurrencyCode,
    val calculatedTime: Long,
    val countryCode: CountryCode,
    val month: Int,
    val year: Int,
    val billingDuration: DurationRange? = null,
    val usageDuration: DurationRange? = null,
    val entityId: Long,
)
