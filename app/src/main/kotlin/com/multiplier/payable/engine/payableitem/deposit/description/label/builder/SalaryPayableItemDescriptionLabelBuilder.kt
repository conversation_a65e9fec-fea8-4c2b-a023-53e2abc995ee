package com.multiplier.payable.engine.payableitem.deposit.description.label.builder

import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.feature.DepositV2Checker
import org.springframework.stereotype.Component

/**
 * Component responsible for building description labels for salary payable items.
 *
 * @param depositV2Checker Checker for determining if Deposit V2 is enabled.
 */
@Component
class SalaryPayableItemDescriptionLabelBuilder(
    private val depositV2Checker: DepositV2Checker
) {

    /**
     * Builds the description label based on the contract type or deposit version.
     *
     * @param contractType the type of the contract
     * @param companyId the ID of the company
     * @return the description label for the invoice
     */
    fun build(contractType: ContractType, companyId: Long): String {
        return when {
            contractType == ContractType.CONTRACTOR -> "Compensation"
            depositV2Checker.isV2Enabled(companyId) -> "Salary (includes Employer Contribution)"
            else -> "Deposit:"
        }
    }
}
