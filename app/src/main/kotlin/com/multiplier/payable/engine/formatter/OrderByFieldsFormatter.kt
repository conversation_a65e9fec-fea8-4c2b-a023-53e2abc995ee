package com.multiplier.payable.engine.formatter

import com.multiplier.payable.engine.formatter.accessor.PropertyAccessor
import com.multiplier.payable.engine.payableitem.PayableItem
import mu.KotlinLogging
import org.springframework.stereotype.Component
import kotlin.reflect.full.memberProperties

@Component
class OrderByFieldsFormatter(
    private val propertyAccessor: PropertyAccessor<PayableItem>,
) :
    AbstractDataFormatter() {

    private val fieldDelimiter = ","
    private val memberAccessDelimiter = "."

    companion object {
        private val logger = KotlinLogging.logger { }
        private val DEFAULT_SORTING_ORDER = DataFormatterSortingOrder.ASCENDING
        private val DEFAULT_NULLS_SORTING_ORDER = DataFormatterNullSortingOrder.NULLS_LAST
    }

    override fun newInstance(): AbstractDataFormatter {
        return OrderByFieldsFormatter(propertyAccessor)
    }

    override fun getOrder(): Long {
        return dataFormatterParam.order
    }

    /**
     * Similar sorting logic to SQL. Formats the list of Payable items by sorting by fields (priority of sorting: highest to lowest from: 1st -> last field respectively).
     *
     * @param items The list of Payable items to be formatted.
     * @return sorted list by criteria
     */
    override fun format(items: List<PayableItem>): List<PayableItem> {
        requireNotNull(dataFormatterParam.field) { "Data Formatter field must not be NULL" }

        val orderByFields = parseFields(dataFormatterParam.field)
        val fieldGetters = orderByFields.map { field -> propertyAccessor.createPropertyGetter(field, memberAccessDelimiter) }
            .filterIsInstance<(PayableItem) -> Comparable<Any>?>()

        if (fieldGetters.size != orderByFields.size) {
            logger.error { "[${this::class.simpleName}] Cannot perform ordering operation fully. One of the following fields is invalid for ordering: ${dataFormatterParam.field}" }
        }

        val (filteredPayableItems, remainingItems) = applyFilter(dataFormatterParam.getFilterExpression(), items)

        val formattedPayableItems = sortByFieldsInConfiguredDirectionOrDefault(fieldGetters, filteredPayableItems)
        logger.info { "[${this::class.simpleName}] Sorted ${formattedPayableItems.size} payable items. Remain ${remainingItems.size} payable items unsorted" }

        return formattedPayableItems.plus(remainingItems)
    }

    private fun sortByFieldsInConfiguredDirectionOrDefault(
        fieldGetters: List<(PayableItem) -> Comparable<Any>?>,
        filteredPayableItems: List<PayableItem>
    ): List<PayableItem> {
        val sortOrder = dataFormatterParam.additionalParam?.sortOrder ?: DEFAULT_SORTING_ORDER
        val nullSortOrder = dataFormatterParam.additionalParam?.nullSortOrder ?: DEFAULT_NULLS_SORTING_ORDER

        val comparators = fieldGetters.map { fieldGetter ->
            buildComparator(fieldGetter, sortOrder, nullSortOrder)
        }

        val finalComparator = if (comparators.isNotEmpty()) {
            comparators.reduce { acc, next -> acc.thenComparing(next) }
        } else {
            Comparator { _, _ -> 0 }
        }

        return filteredPayableItems.sortedWith(finalComparator)
    }

    private fun buildComparator(
        fieldGetter: (PayableItem) -> Comparable<Any>?,
        sortOrder: DataFormatterSortingOrder,
        nullSortOrder: DataFormatterNullSortingOrder
    ): Comparator<PayableItem> {
        val baseComparator = if (sortOrder == DataFormatterSortingOrder.ASCENDING) {
            Comparator.naturalOrder<Comparable<Any>>()
        } else {
            Comparator.reverseOrder()
        }

        val fullComparator = when (nullSortOrder) {
            DataFormatterNullSortingOrder.NULLS_FIRST -> Comparator.nullsFirst(baseComparator)
            DataFormatterNullSortingOrder.NULLS_LAST -> Comparator.nullsLast(baseComparator)
        }

        return Comparator { leftItem, rightItem ->
            val leftItemFieldValue = fieldGetter(leftItem)
            val rightItemFieldValue = fieldGetter(rightItem)
            fullComparator.compare(leftItemFieldValue, rightItemFieldValue)
        }
    }

    private fun parseFields(field: String?): List<String> {
        if (field.isNullOrEmpty())
            throw IllegalArgumentException("[${this::class.simpleName}] Once declared usage in template, field must not be null or empty")
        return field.split(fieldDelimiter).map { it.trim() }
    }
}