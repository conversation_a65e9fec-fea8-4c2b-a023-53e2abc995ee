package com.multiplier.payable.engine.inspector

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.multiplier.core.payable.repository.JpaTransactionCommandLogRepository
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.inspector.dto.FinancialTransactionInspectorResponse
import com.multiplier.payable.engine.transaction.ExpenseBillCommand
import org.springframework.stereotype.Component

@Component
class ExpenseBillTransactionInspector(
    private val transactionCommandRepository: JpaTransactionCommandLogRepository
) : FinancialTransactionInspector {

    private val objectMapper: ObjectMapper = ObjectMapper().apply {
        registerModule(JavaTimeModule())
        registerKotlinModule()
    }

    override fun supportedTypes(): Set<TransactionType> = setOf(TransactionType.VENDOR_BILL)

    override fun inspect(transactionId: String): FinancialTransactionInspectorResponse {
        val commandLogs = transactionCommandRepository.findByTransactionId(transactionId).sortedBy { it.updatedOn }
        val commands = commandLogs.map { objectMapper.readValue(it.command, ExpenseBillCommand::class.java) }

        return FinancialTransactionInspectorResponse(commands = commands, transactions = emptyList())
    }
}
