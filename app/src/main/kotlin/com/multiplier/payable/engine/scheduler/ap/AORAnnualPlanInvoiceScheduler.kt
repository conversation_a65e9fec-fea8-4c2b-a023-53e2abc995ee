package com.multiplier.payable.engine.scheduler.ap

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.orchestrator.TransactionTriggeringType
import com.multiplier.payable.engine.scheduler.InvoiceScheduler
import com.multiplier.payable.engine.scheduler.TransactionCommandContext
import com.multiplier.payable.engine.transaction.GenerateFinancialTransactionCommand
import mu.KotlinLogging
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.apache.commons.lang3.time.StopWatch
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDateTime

@Profile(value = ["!local", "!debug"])
@Component
class AORAnnualPlanInvoiceScheduler(
    private val transactionCommandContextProvider: AORAnnualPlanTransactionCommandContextProvider,
    private val featureFlagService: FeatureFlagService,
    @Qualifier("sync") val blockingGenerateTransactionCommand: GenerateFinancialTransactionCommand,
) : InvoiceScheduler {

    companion object {
        private val logger = KotlinLogging.logger {}
        private const val FEATURE_FLAG_NAME = "aor-ap-invoice-scheduler-enabled"
    }

    @Scheduled(cron = "\${orchestrator.invoice.aor-annual-plan-invoice.scheduler.cron-time}")
    @SchedulerLock(
        name = "aorAnnualPlanInvoiceScheduler",
        lockAtLeastFor = "PT10M",
        lockAtMostFor = "PT14M"
    )
    override fun run() {

        if (isEnabledByFeatureFlag().not()) {
            logger.info { "Skipping AOR annual plan invoice scheduler as feature flag is disabled" }
            return
        }

        logger.info { "AOR annual plan invoice scheduler started" }

        transactionCommandContextProvider.poll()
            .takeIf { it.isNotEmpty() }
            ?.also { logger.info { "Found ${it.size} context to process" } }
            ?.forEach { process(it) }
            ?: run {
                logger.info { "No context to process" }
                return
            }
    }

    private fun process(context: TransactionCommandContext) {
        logger.info { "Generating invoice for $context" }
        val stopWatch = StopWatch()
        try {
            stopWatch.start()
            blockingGenerateTransactionCommand.generate(
                transactionType = getTransactionType(),
                companyIds = listOf(context.companyId),
                dateRange = context.dateRange,
                transactionDate = LocalDateTime.now(),
                cycle = InvoiceCycle.MONTHLY,
                forcedContractIdsByCompanyId = emptyMap(),
                autoSubmit = false,
                depositCommand = null,
                companyToEntityIdsMap = emptyMap(),
                memberPayableInvoiceCommand = null,
                incidentsInvoiceCommand = null,
                preference = null,
                transactionTriggeringType = TransactionTriggeringType.SCHEDULED,
                traces = context.traces,
            )
        } catch (e: Exception) {
            logger.error(e) { "Error generating invoice for $context" }
        } finally {
            stopWatch.stop()
            logger.info { "Finished generating invoice for $context in ${stopWatch.time}ms" }
        }
    }

    override fun getTransactionType(): TransactionType {
        return TransactionType.ANNUAL_PLAN_AOR_INVOICE
    }

    private fun isEnabledByFeatureFlag(): Boolean {
        return featureFlagService
            .feature(FEATURE_FLAG_NAME, emptyMap())
            .on
    }
}
