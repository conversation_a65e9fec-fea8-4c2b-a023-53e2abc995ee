package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.fetcher.IADCompanyPayableFetcher
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.repository.model.JpaInvoiceLineItem
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.rules.processor.BaseAnomalyDetectionRule
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.PayableStatus
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.util.Locale

/**
 * Checks if line item per-unit amounts exceed historical per-unit thresholds based on past invoice data.
 * This rule compares per-unit costs (unitAmount/unitPrice) rather than total amounts to avoid false positives
 * for companies with varying quantities or many employees.
 *
 * Only uses AUTHORIZED, PAID, and PARTIALLY_PAID historical invoices to ensure clean threshold calculation
 * and avoid including anomalous data from problematic invoices.
 */
@Component
class HighValueItemRule(
    private val iadCompanyPayableFetcher: IADCompanyPayableFetcher,
    featureFlagService: FeatureFlagService,
    jpaAnomalyReportRepository: JpaAnomalyReportRepository,
) : BaseAnomalyDetectionRule(featureFlagService, jpaAnomalyReportRepository) {
    companion object {
        private val log = KotlinLogging.logger {}
        private const val THRESHOLD_MULTIPLIER = 1.5 // 150% of historical max
        private const val MINIMUM_HISTORICAL_MONTHS = 3
        private const val HISTORICAL_MONTHS_TO_FETCH = 6

        // Only include payables with "clean" statuses for historical threshold calculation
        private val VALID_HISTORICAL_PAYABLE_STATUSES = setOf(
            PayableStatus.AUTHORIZED,  // Approved invoices
            PayableStatus.PAID,        // Paid invoices (definitely clean)
            PayableStatus.PARTIALLY_PAID // Being paid (likely clean)
        )
    }

    override val type: DetectionRuleType = DetectionRuleType.HIGH_VALUE_ITEM
    override val detectorType: InvoiceAnomalyDetectorType = InvoiceAnomalyDetectorType.HIGH_VALUE_ITEM
    override val ruleName: String = "HighValueItem"
    override val featureFlagName: String = "ENABLE_HIGH_VALUE_ITEM_CHECK"

    /**
     * Rule-specific detection logic.
     */
    override fun validateRule(
        command: InvoiceCommand,
        request: InvoiceAnomalyDetectorRequest,
    ): AnomalyDetectionResult {
        // Get the invoice data and company payable (we know they're not null because isRequestValid was called)
        val invoiceDTO = request.invoiceDTO!!
        val companyPayable = request.payable!!

        // Get required data
        val lineItems = invoiceDTO.lineItems
        val companyId = companyPayable.companyId

        // Track any anomalies found
        val anomalies = mutableListOf<String>()

        // Validate that we have line items to check
        if (lineItems.isNullOrEmpty()) {
            log.info { "No line items found in invoice, skipping high value item check" }
            return createWarningResult(listOf("No line items found in invoice - validation skipped"))
        }

        // Get historical data for threshold calculation
        val historicalThresholds = try {
            getHistoricalThresholds(companyId, companyPayable.month, companyPayable.year, companyPayable)
        } catch (e: Exception) {
            val message = "Unable to fetch historical data for company ID $companyId: ${e.message}"
            log.warn(e) { message }
            // Return warning if we can't get historical data (graceful degradation)
            return createWarningResult(listOf("Historical data unavailable - validation skipped (graceful degradation)"))
        }

        // If we don't have sufficient historical data, skip the check
        if (historicalThresholds.isEmpty()) {
            log.info { "Insufficient historical data for company ID $companyId, skipping high value item check" }
            return createWarningResult(listOf("Insufficient historical data - validation skipped"))
        }

        // Check each line item against historical per-unit thresholds
        lineItems.forEach { lineItem ->
            val perUnitAmount = calculatePerUnitAmount(lineItem)
            val lineItemType = lineItem.itemType
            val currency = lineItem.baseCurrency ?: "Unknown"

            if (lineItemType != null && perUnitAmount > 0) {
                val threshold = historicalThresholds[lineItemType]
                if (threshold != null && perUnitAmount > threshold) {
                    val percentageIncrease = (perUnitAmount / (threshold / THRESHOLD_MULTIPLIER)) * 100
                    val message = String.format(
                        Locale.ENGLISH,
                        "Line item '%s' per-unit amount %s %.2f exceeds historical per-unit threshold %s %.2f (%.1f%% of historical max)",
                        lineItem.description ?: lineItemType.name,
                        currency,
                        perUnitAmount,
                        currency,
                        threshold,
                        percentageIncrease
                    )
                    anomalies.add(message)
                    log.warn { message }
                }
            }
        }

        return if (anomalies.isNotEmpty()) {
            // Use WARNING instead of FAIL - this should alert but not block processing
            createWarningResult(anomalies)
        } else {
            logger.info { "No $ruleName anomalies detected" }
            createSuccessResult(listOf("All line item amounts are within acceptable per-unit thresholds"))
        }
    }

    /**
     * Gets historical per-unit thresholds for each line item type based on past invoice data
     */
    private fun getHistoricalThresholds(companyId: Long, currentMonth: Int, currentYear: Int, companyPayable: com.multiplier.core.payable.repository.model.JpaCompanyPayable): Map<LineItemType, Double> {
        val historicalLineItems = getHistoricalLineItems(companyId, currentMonth, currentYear, companyPayable)

        if (historicalLineItems.isEmpty()) {
            log.info { "No historical line items found for company ID $companyId" }
            return emptyMap()
        }

        // Group by line item type and calculate per-unit thresholds
        val thresholdsByType = historicalLineItems
            .filter { it.itemType != null }
            .groupBy { it.itemType }
            .mapValues { (itemType, items) ->
                val perUnitAmounts = items.map { calculatePerUnitAmount(it) }.filter { it > 0 }
                if (perUnitAmounts.isNotEmpty()) {
                    val maxPerUnitAmount = perUnitAmounts.maxOrNull() ?: 0.0
                    val threshold = maxPerUnitAmount * THRESHOLD_MULTIPLIER
                    log.debug { "Historical per-unit threshold for $itemType: $threshold (max per-unit: $maxPerUnitAmount, count: ${perUnitAmounts.size})" }
                    threshold
                } else {
                    0.0
                }
            }
            .filter { it.value > 0 }

        log.info { "Calculated per-unit thresholds for ${thresholdsByType.size} line item types for company ID $companyId" }
        return thresholdsByType
    }

    /**
     * Fetches historical line items for the past 6 months
     */
    private fun getHistoricalLineItems(companyId: Long, currentMonth: Int, currentYear: Int, companyPayable: com.multiplier.core.payable.repository.model.JpaCompanyPayable): List<JpaInvoiceLineItem> {
        val historicalLineItems = mutableListOf<JpaInvoiceLineItem>()
        var monthsWithData = 0

        // Get last 6 months of data
        for (i in 1..HISTORICAL_MONTHS_TO_FETCH) {
            val targetDate = LocalDate.of(currentYear, currentMonth, 1).minusMonths(i.toLong())

            try {
                // Create a dummy company payable for the target date
                val dummyPayable = com.multiplier.core.payable.repository.model.JpaCompanyPayable.builder()
                    .companyId(companyPayable.companyId)
                    .month(targetDate.monthValue)
                    .year(targetDate.year)
                    .build()

                val historicalPayables = iadCompanyPayableFetcher.getInvoicesForReqTime(
                    dummyPayable,
                    false // We're manually calculating the target date
                ).filter { VALID_HISTORICAL_PAYABLE_STATUSES.contains(it.status) }

                var monthLineItemCount = 0
                historicalPayables.forEach { payable ->
                    payable.invoice?.lineItems?.let { lineItems ->
                        historicalLineItems.addAll(lineItems)
                        monthLineItemCount += lineItems.size
                    }
                }

                if (monthLineItemCount > 0) {
                    monthsWithData++
                    log.debug { "Found $monthLineItemCount line items for ${targetDate.year}-${targetDate.monthValue}" }
                }
            } catch (e: Exception) {
                log.warn(e) { "Failed to fetch historical data for ${targetDate.year}-${targetDate.monthValue}" }
            }
        }

        log.info { "Fetched ${historicalLineItems.size} historical line items from $monthsWithData months for company ID $companyId" }

        // Only proceed if we have sufficient historical data
        return if (monthsWithData >= MINIMUM_HISTORICAL_MONTHS) {
            historicalLineItems
        } else {
            log.info { "Insufficient historical data: only $monthsWithData months available (minimum: $MINIMUM_HISTORICAL_MONTHS)" }
            emptyList()
        }
    }

    /**
     * Calculates per-unit amount for a line item from current invoice (LineItemDTO)
     */
    private fun calculatePerUnitAmount(lineItem: LineItemDTO): Double {
        return lineItem.unitAmount ?: 0.0
    }

    /**
     * Calculates per-unit amount for a historical line item (JpaInvoiceLineItem)
     */
    private fun calculatePerUnitAmount(jpaLineItem: JpaInvoiceLineItem): Double {
        return jpaLineItem.unitPrice ?: 0.0
    }


}

