package com.multiplier.payable.engine.transaction.template

import com.multiplier.payable.engine.domain.entities.TransactionType
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.SequenceGenerator
import jakarta.persistence.Table

@Entity
@Table(schema = "payable", name = "transaction_template")
@SequenceGenerator(
    name = "transaction_template_meta_data_id_seq_gen",
    schema = "payable",
    sequenceName = "transaction_template_meta_data_id_seq",
    allocationSize = 1,
    initialValue = 1,
)
class JpaTransactionTemplate(
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "transaction_template_meta_data_id_seq_gen")
    var id: Long? = null,
    @Enumerated(EnumType.STRING)
    var transactionType: TransactionType,
    var companyId: Long?,
    var templateIdentifier: String,
    var config: String,
)
