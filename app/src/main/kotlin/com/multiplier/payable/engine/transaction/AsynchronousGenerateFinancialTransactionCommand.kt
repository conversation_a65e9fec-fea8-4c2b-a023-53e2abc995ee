package com.multiplier.payable.engine.transaction

import com.multiplier.payable.engine.TransactionCommand
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.gateway.KafkaTransactionGatewayMessage
import com.multiplier.payable.engine.gateway.TransactionGateway
import com.multiplier.payable.engine.orchestrator.Orchestrator
import com.multiplier.payable.engine.orchestrator.TransactionTriggeringType
import com.multiplier.payable.engine.transaction.preference.FinancialTransactionPreference
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.LocalDateTime

@Service
@Qualifier("async")
class AsynchronousGenerateFinancialTransactionCommand(
    private val orchestrator: Orchestrator,
    private val transactionIdProvider: TransactionIdProvider,
    @Qualifier("kafka") private val transactionGateway: TransactionGateway<TransactionCommand>,
    @Value("\${platform.invoice-engine.kafka.topic}") private val invoiceEngineKafkaTopic: String
) : GenerateFinancialTransactionCommand {
    companion object {
        private val log = KotlinLogging.logger { }
    }

    override fun generate(
        transactionType: TransactionType,
        companyIds: List<Long>,
        dateRange: DateRange,
        transactionDate: LocalDateTime?,
        cycle: InvoiceCycle,
        forcedContractIdsByCompanyId: Map<Long, Set<Long>>,
        autoSubmit: Boolean,
        depositCommand: DepositInvoiceCommand?,
        companyToEntityIdsMap: Map<Long, Set<Long>>,
        memberPayableInvoiceCommand: MemberPayableInvoiceCommand?,
        incidentsInvoiceCommand: IncidentsInvoiceCommand?,
        preference: FinancialTransactionPreference?,
        transactionTriggeringType: TransactionTriggeringType,
        traces: Map<String, String>,
    ): List<InvoiceCommand> {
        log.info {
            "Start asynchronous transaction generation for transactionType: $transactionType, companyIds: $companyIds, " +
                    "dateRange: $dateRange, invoiceDate: $transactionDate, cycle: ${cycle.name}"
        }

        val messages = companyIds.flatMap { companyId ->
            val entityIds = companyToEntityIdsMap.getOrDefault(companyId, setOf(null))
            entityIds.map { entityId ->
                KafkaTransactionGatewayMessage(
                    payload = InvoiceCommand(
                        transactionId = transactionIdProvider.generate(incidentsInvoiceCommand),
                        transactionType = transactionType,
                        transactionTriggeringType = transactionTriggeringType,
                        companyId = companyId,
                        dateRange = dateRange,
                        transactionDate = transactionDate ?: LocalDateTime.now(),
                        cycle = cycle,
                        forceContractIds = getForcedContractIds(forcedContractIdsByCompanyId, companyId),
                        autoSubmit = autoSubmit,
                        depositCommand = depositCommand,
                        entityId = entityId,
                        memberPayableInvoiceCommand = memberPayableInvoiceCommand,
                        incidentsInvoiceCommand = incidentsInvoiceCommand,
                        preference = preference,
                    ).also { command -> traces.forEach { command.appendTrace(it.key, it.value) }},
                    topic = invoiceEngineKafkaTopic
                )
            }
        }

        return messages.map { message ->
            executeTransaction(message)
        }
    }

    private fun executeTransaction(message: KafkaTransactionGatewayMessage): InvoiceCommand {
        val command = message.payload as InvoiceCommand
        val result = command.copy()
        try {
            orchestrator.startTransaction(command)
            transactionGateway.fire(message)
        } catch (e: Exception) {
            log.error(e) { "Error generating invoice asynchronously, command = $command" }
            result.appendError("Error generating invoice asynchronously: ${e.message}")
        }
        return result
    }

    private fun getForcedContractIds(forcedContractIdsByCompanyId: Map<Long, Set<Long>>, companyId: Long) =
        forcedContractIdsByCompanyId.getOrDefault(companyId, emptySet())

}