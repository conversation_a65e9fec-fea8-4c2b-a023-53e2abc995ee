package com.multiplier.payable.engine.reconciler.descriptionbuilder

import com.multiplier.core.payable.adapters.api.LineItemType
import org.springframework.stereotype.Component

@Component
class PayslipMinimumCommitmentDescriptionBuilder : PayableItemDescriptionBuilder {

    override val lineItemType = LineItemType.PAYSLIP_MINIMUM_COMMITMENT

    override fun build(context: PayableItemDescriptionBuilderContext): String {
        return """
            Minimum Payslip Fee (${context.countryCode ?: context.currencyCode}) ${context.currencyCode} ${context.amountInBaseCurrency}
        """.trimIndent()
    }
}
