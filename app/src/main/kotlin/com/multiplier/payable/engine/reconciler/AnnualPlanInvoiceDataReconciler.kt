package com.multiplier.payable.engine.reconciler

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.reconciler.adjustment.AdjustmentBalanceAwareReconcilerFactory
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorage
import com.multiplier.payable.engine.reconciler.data.invoice.InvoiceDataProviderFactory
import com.multiplier.payable.engine.reconciler.data.item.ItemStoreDataProviderFactory
import com.multiplier.payable.engine.reconciler.diff.reconciler.InvoiceDiffReconcilerFactory
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

@Service("annual-plan")
class AnnualPlanInvoiceDataReconciler(
    templateProvider: TransactionTemplateProvider,
    invoiceDataProviderFactory: InvoiceDataProviderFactory,
    itemStoreDataProviderFactory: ItemStoreDataProviderFactory,
    invoiceDiffReconcilerFactory: InvoiceDiffReconcilerFactory,
    @Qualifier("annual-plan") companyPayableStorage: CompanyPayableStorage,
    dataSplitter: DataSplitter,
    adjustmentBalanceAwareReconcilerFactory: AdjustmentBalanceAwareReconcilerFactory,
) : AbstractAnnualPlanInvoiceDataReconciler(
    templateProvider,
    invoiceDataProviderFactory,
    itemStoreDataProviderFactory,
    invoiceDiffReconcilerFactory,
    adjustmentBalanceAwareReconcilerFactory,
    companyPayableStorage,
    dataSplitter
) {
    override val transactionType: TransactionType
        get() = TransactionType.ANNUAL_PLAN_INVOICE
}
