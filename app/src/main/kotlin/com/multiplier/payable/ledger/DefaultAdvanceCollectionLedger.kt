package com.multiplier.payable.ledger

import com.multiplier.common.exception.toBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.core.payable.currencyexchange.CurrencyExchangeV2Service
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.ledger.domain.AdvanceCollectionBalance
import com.multiplier.payable.ledger.domain.AdvanceCollectionEntry
import com.multiplier.payable.ledger.domain.AdvanceCollectionEntryStateMachine
import com.multiplier.payable.ledger.domain.AdvanceCollectionEntryStatus
import com.multiplier.payable.ledger.domain.AdvanceCollectionEntryTransitionEventType
import com.multiplier.payable.ledger.domain.AdvanceCollectionProduct
import com.multiplier.payable.ledger.provider.DefaultAdvanceCollectionBalanceDataProvider
import com.multiplier.payable.ledger.storage.AdvanceCollectionBalanceStorage
import com.multiplier.payable.ledger.storage.AdvanceCollectionEntryStorage
import com.multiplier.payable.lock.distributed.DistributedLockProvider
import mu.KotlinLogging
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Duration

@Component
class DefaultAdvanceCollectionLedger(
    private val balanceStorage: AdvanceCollectionBalanceStorage,
    private val entryStorage: AdvanceCollectionEntryStorage,
    private val balanceProvider: DefaultAdvanceCollectionBalanceDataProvider,
    private val currencyExchangeV2Service: CurrencyExchangeV2Service,
    private val stateMachine: AdvanceCollectionEntryStateMachine,
    private val lockProvider: DistributedLockProvider
) : AdvanceCollectionLedger {

    companion object {
        private val logger = KotlinLogging.logger { }
        private const val LOCK_NAMESPACE = "advance-collection-balance"
        private val TIMEOUT = Duration.ofMinutes(1)
    }

    override fun hasBalance(
        companyId: Long,
        entityId: Long?,
        advanceCollectionProduct: AdvanceCollectionProduct
    ): Boolean {
        val balances = balanceProvider.query(companyId, entityId, advanceCollectionProduct)
        return balances.isNotEmpty()
    }

    @Transactional
    override fun tryReserve(
        transactionId: String,
        companyId: Long,
        entityId: Long?,
        advanceCollectionProduct: AdvanceCollectionProduct,
        amount: Amount
    ): AdvanceCollectionReservedAmount {
        logger.info(
            "Try reserve $amount for transaction $transactionId, company $companyId, " +
                    "entity $entityId, product $advanceCollectionProduct"
        )
        val foundBalances = balanceProvider.query(companyId, entityId, advanceCollectionProduct)
        if (foundBalances.isEmpty() || foundBalances.size > 1) {
            logger.info("Found ${foundBalances.size} balances for transaction $transactionId")
            throw PayableErrorCode.ADVANCE_COLLECTION_BALANCE_NOT_AVAILABLE
                .toBusinessException("Cannot acquire balance for transaction $transactionId")
        }

        val balance = foundBalances.first()
        val lock = lockProvider.getLock(LOCK_NAMESPACE, balance.id.toString())
        if (lock.tryLock(TIMEOUT).not()) {
            throw PayableErrorCode.ADVANCE_COLLECTION_BALANCE_NOT_AVAILABLE
                .toBusinessException(
                    "Cannot acquire advance collection balance for transaction $transactionId, " +
                            "company $companyId, entity $entityId, product $advanceCollectionProduct"
                )
        }
        try {
            var reservingAmount = amount
            if (amount.currency != balance.balance.currency) {
                reservingAmount = Amount(
                    value = currencyExchangeV2Service.exchange(
                        amount.value,
                        amount.currency,
                        balance.balance.currency,
                        companyId
                    ),
                    currency = balance.balance.currency
                )
            }

            val result = balance.reserve(transactionId, reservingAmount)
            if (result.isReserved().not()) {
                return result
            }

            balanceStorage.store(result.currentBalance)
            val reservedEntry = entryStorage.append(requireNotNull(result.reservedEntry))
            return result.copy(
                reservedEntry = reservedEntry
            )
        } finally {
            lock.unlock()
        }
    }

    @Transactional
    override fun rollBack(entry: AdvanceCollectionEntry) {
        val lock = lockProvider.getLock(LOCK_NAMESPACE, entry.balanceId.toString())
        if (lock.tryLock(TIMEOUT).not()) {
            throw PayableErrorCode.ADVANCE_COLLECTION_BALANCE_NOT_AVAILABLE
                .toBusinessException("Cannot acquire advance collection balance for balance id ${entry.balanceId}")
        }

        try {
            val balance = balanceProvider.findExisting(entry.balanceId)
            val reversedAmount = entry.amount.negate()
            val roledBackBalance = balance.add(reversedAmount)
            balanceStorage.store(roledBackBalance)

            entryStorage.store(
                entry.copy(
                    status = stateMachine.next(
                        entry.status,
                        AdvanceCollectionEntryTransitionEventType.ROLLBACK
                    )
                )
            )
            entryStorage.append(
                AdvanceCollectionEntry(
                    transactionId = entry.transactionId,
                    balanceId = entry.balanceId,
                    amount = reversedAmount,
                    note = "Rolled back ${entry.id}",
                    status = AdvanceCollectionEntryStatus.COMMITED,
                )
            )
        } finally {
            lock.unlock()
        }
    }

    override fun commit(entry: AdvanceCollectionEntry) {
        if (stateMachine.isValidTransition(
                currentState = entry.status,
                action = AdvanceCollectionEntryTransitionEventType.COMMIT
            ).not()
        ) {
            return
        }

        entryStorage.store(
            entry.copy(
                status = stateMachine.next(
                    entry.status,
                    AdvanceCollectionEntryTransitionEventType.COMMIT
                )
            )
        )
    }

    @Transactional
    override fun registerBalance(
        companyId: Long,
        entityId: Long,
        advanceCollectionProduct: AdvanceCollectionProduct,
        amount: Amount,
        metadata: AdvanceCollectionBalanceMetadata
    ) {
        if (hasBalance(companyId, entityId, advanceCollectionProduct)) {
            throw PayableErrorCode.ADVANCE_COLLECTION_BALANCE_EXISTS
                .toBusinessException(
                    "Advance collection balance exists already for company id: $companyId," +
                            " entity id: $entityId," +
                            " product: $advanceCollectionProduct"
                )
        }

        val balance = AdvanceCollectionBalance(
            companyId = companyId,
            entityId = entityId,
            advanceCollectionProduct = advanceCollectionProduct,
            balance = amount,
            metadata = metadata
        )
        try {
            val registeredBalance = balanceStorage.store(balance)
            entryStorage.append(
                AdvanceCollectionEntry(
                    balanceId = requireNotNull(registeredBalance.id),
                    amount = amount.value,
                    note = "Initial balance",
                    status = AdvanceCollectionEntryStatus.COMMITED,
                    transactionId = requireNotNull(metadata.transactionId)
                )
            )
        } catch (e: DataIntegrityViolationException) {
            logger.warn(e) { "Ignore duplicated balance $balance" }
        }
    }
}