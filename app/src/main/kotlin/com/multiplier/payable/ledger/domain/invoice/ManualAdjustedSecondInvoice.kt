package com.multiplier.payable.ledger.domain.invoice

import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable

class ManualAdjustedSecondInvoice(
    override val payable: CompanyPayable,
    val invoice: InvoiceDTO
) : AdjustedSecondInvoice {

    fun getAdjustedLineItems(): List<LineItemDTO> {
        return invoice.lineItems
            .filter { it.itemType in LineItemType.getAdvanceCollectionAdjustmentLineItemTypes() }
    }
}