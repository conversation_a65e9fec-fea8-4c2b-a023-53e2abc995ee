package com.multiplier.payable.ledger.domain.invoice

import com.multiplier.common.exception.toBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.core.payable.adapters.billing.BilledItemWrapper
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.ledger.AdvanceCollectionBalanceMetadata

data class AdvanceCollectionInvoice(
    val transactionId: String,
    val invoiceNo: String,
    val lines: List<AdvanceCollectionInvoiceLine>,
    val collectionItems: List<PayableItem>
) {

    fun hasCollectionLines(): Boolean {
        return lines.isNotEmpty()
    }

    fun findLine(payableLineId: String): AdvanceCollectionInvoiceLine? {
        return lines.find { it.payableItemIds?.contains(payableLineId) == true }
    }

    fun metadataOf(bill: BilledItemWrapper): AdvanceCollectionBalanceMetadata? {
        val referencePayableItem =
            collectionItems.find { it.billId?.toLong() == bill.billId } ?: return null
        require(referencePayableItem.companyPayableLineItemIds.size == 1) {
            throw PayableErrorCode.INVALID_COMPANY_PAYABLE_ITEM.toBusinessException(
                "Company payable item must have exactly one line item id. Item = $referencePayableItem"
            )
        }

        val referenceInvoiceLine = findLine(
            referencePayableItem.companyPayableLineItemIds.first().toString()
        ) ?: return null
        return AdvanceCollectionBalanceMetadata(
            transactionId = transactionId,
            invoiceNo = invoiceNo,
            referenceLine = referenceInvoiceLine
        )
    }

    fun billIds(): Set<Long> {
        return collectionItems.mapNotNull { it.billId?.toLong() }.toSet()
    }
}