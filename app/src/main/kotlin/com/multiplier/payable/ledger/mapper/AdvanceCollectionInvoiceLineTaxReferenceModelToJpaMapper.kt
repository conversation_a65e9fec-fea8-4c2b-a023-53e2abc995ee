package com.multiplier.payable.ledger.mapper

import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionInvoiceLineTaxReference
import org.springframework.stereotype.Component

@Component
class AdvanceCollectionInvoiceLineTaxReferenceModelToJpaMapper {

    fun map(taxReference: AdvanceCollectionInvoiceLineTaxReference): JpaAdvanceCollectionInvoiceLineTaxReference {
        return JpaAdvanceCollectionInvoiceLineTaxReference(
            taxCode = taxReference.taxCode,
            taxRate = taxReference.taxRate,
            taxType = taxReference.taxType,
            taxAmount = taxReference.taxAmount
        )
    }
}
