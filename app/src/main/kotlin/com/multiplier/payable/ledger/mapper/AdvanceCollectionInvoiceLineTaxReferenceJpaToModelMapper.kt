package com.multiplier.payable.ledger.mapper

import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionInvoiceLineTaxReference
import org.springframework.stereotype.Component

@Component
class AdvanceCollectionInvoiceLineTaxReferenceJpaToModelMapper {

    fun map(taxReference: JpaAdvanceCollectionInvoiceLineTaxReference): AdvanceCollectionInvoiceLineTaxReference {
        return AdvanceCollectionInvoiceLineTaxReference(
            taxCode = taxReference.taxCode,
            taxRate = taxReference.taxRate,
            taxType = taxReference.taxType,
            taxAmount = taxReference.taxAmount
        )
    }
}
