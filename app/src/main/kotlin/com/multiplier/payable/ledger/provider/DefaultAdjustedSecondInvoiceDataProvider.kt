package com.multiplier.payable.ledger.provider

import com.multiplier.common.exception.toBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.core.payable.creditnote.database.CreditNoteService
import com.multiplier.core.payable.invoice.database.InvoiceService
import com.multiplier.core.payable.repository.model.PayableSource
import com.multiplier.payable.engine.transaction.data.CompanyPayableDataProvider
import com.multiplier.payable.ledger.domain.invoice.AdjustedSecondInvoice
import com.multiplier.payable.ledger.domain.invoice.ManualAdjustedSecondInvoice
import com.multiplier.payable.ledger.domain.invoice.ManualAdjustedSecondInvoiceCreditNote
import com.multiplier.payable.ledger.domain.invoice.PlatformAdjustedSecondInvoice
import com.multiplier.payable.ledger.domain.invoice.PlatformAdjustedSecondInvoiceCreditNote
import org.apache.commons.collections.CollectionUtils
import org.springframework.stereotype.Component

@Component
class DefaultAdjustedSecondInvoiceDataProvider(
    private val creditNoteService: CreditNoteService,
    private val invoiceService: InvoiceService,
    private val companyPayableDataProvider: CompanyPayableDataProvider
) : AdjustedSecondInvoiceDataProvider {

    override fun findExisting(payableId: Long): AdjustedSecondInvoice {
        val payableIds = setOf(payableId)
        val payables = companyPayableDataProvider.findByPayableIds(payableIds)
        if (payables.isEmpty()) {
            throw PayableErrorCode.COMPANY_PAYABLE_NOT_FOUND
                .toBusinessException("Company payable $payableId not found")
        }

        val invoices = invoiceService.getByCompanyPayableIds(payableIds)
        val payable = payables[0]
        if (CollectionUtils.isNotEmpty(invoices)) {
            return if (PayableSource.SYSTEM == payable.source) {
                PlatformAdjustedSecondInvoice(payable)
            } else {
                ManualAdjustedSecondInvoice(payable, invoices[0])
            }
        } else {
            val creditNotes = creditNoteService.getByCompanyPayableIds(payableIds)
            if (CollectionUtils.isNotEmpty(creditNotes)) {
                return if (PayableSource.SYSTEM == payable.source) {
                    PlatformAdjustedSecondInvoiceCreditNote(payable)
                } else {
                    ManualAdjustedSecondInvoiceCreditNote(payable, creditNotes[0])
                }
            } else {
                throw PayableErrorCode.INVOICE_NOT_FOUND
                    .toBusinessException("Invoice not found for company payable $payableId")
            }
        }
    }
}