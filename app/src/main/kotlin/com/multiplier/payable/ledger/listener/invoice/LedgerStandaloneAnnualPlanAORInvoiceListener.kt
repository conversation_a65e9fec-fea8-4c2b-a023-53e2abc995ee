package com.multiplier.payable.ledger.listener.invoice

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.ledger.AdvanceCollectionLedger
import com.multiplier.payable.ledger.provider.AdvanceCollectionEntryDataProvider
import org.springframework.stereotype.Component

@Component
class LedgerStandaloneAnnualPlanAORInvoiceListener(
    override val ledger: AdvanceCollectionLedger,
    override val advanceCollectionEntryDataProvider: AdvanceCollectionEntryDataProvider,
    override val entryDataProvider: AdvanceCollectionEntryDataProvider
) : LedgerStandaloneInvoiceListener {

    override fun transactionType(): TransactionType {
        return TransactionType.ANNUAL_PLAN_AOR_INVOICE
    }
}