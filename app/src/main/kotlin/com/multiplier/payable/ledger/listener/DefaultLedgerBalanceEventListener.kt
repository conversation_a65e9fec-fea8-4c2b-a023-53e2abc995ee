package com.multiplier.payable.ledger.listener

import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.payable.engine.transaction.data.CompanyPayableDataProvider
import com.multiplier.payable.ledger.listener.invoice.LedgerInvoiceListenerFactory
import mu.KotlinLogging
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class DefaultLedgerBalanceEventListener(
    private val companyPayableDataProvider: CompanyPayableDataProvider,
    private val ledgerInvoiceListenerFactory: LedgerInvoiceListenerFactory
) : LedgerBalanceEventListener {

    companion object {
        private val logger = KotlinLogging.logger {}
    }

    @Transactional
    override fun onEvent(event: LedgerBalanceEvent) {
        val payables = companyPayableDataProvider.findByPayableIds(
            setOf(event.payableId),
            ::isNotSkippedPayable
        )
        if (payables.isEmpty()) {
            return
        }

        val payable = payables.first()
        if (LedgerInvoiceListenerFactory.SUPPORTED_TRANSACTION_TYPES.contains(payable.itemType).not()) {
            return
        }

        val ledgerInvoiceListener = ledgerInvoiceListenerFactory.get(payable.itemType)
        when (payable.status) {
            in LedgerInvoiceListener.ROLLBACK_PAYABLE_STATUSES -> ledgerInvoiceListener.onRollback(payable)
            in LedgerInvoiceListener.COMMIT_PAYABLE_STATUSES -> ledgerInvoiceListener.onCommit(payable)

            else -> {
                logger.info {
                    "Skip reconciling ledger balance with" +
                            "incoming payable id = ${payable.id}, status = ${payable.status}"
                }
            }
        }
    }

    private fun isNotSkippedPayable(companyPayable: JpaCompanyPayable): Boolean =
        (companyPayable.isManualTransaction || companyPayable.isLegacyTransaction).not()
}