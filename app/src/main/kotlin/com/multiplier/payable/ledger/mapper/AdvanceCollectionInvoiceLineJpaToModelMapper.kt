package com.multiplier.payable.ledger.mapper

import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionInvoiceLine
import org.springframework.stereotype.Component

@Component
class AdvanceCollectionInvoiceLineJpaToModelMapper(
    private val taxReferenceJpaToModelMapper: AdvanceCollectionInvoiceLineTaxReferenceJpaToModelMapper
) {
    fun map(jpaEntity: JpaAdvanceCollectionInvoiceLine): AdvanceCollectionInvoiceLine {
        return AdvanceCollectionInvoiceLine(
            itemType = jpaEntity.itemType,
            taxReference = taxReferenceJpaToModelMapper.map(jpaEntity.taxReference),
            unitPrice = jpaEntity.unitPrice,
            currency = jpaEntity.currency,
            description = jpaEntity.description,
            payableItemIds = jpaEntity.payableItemIds
        )
    }
}
