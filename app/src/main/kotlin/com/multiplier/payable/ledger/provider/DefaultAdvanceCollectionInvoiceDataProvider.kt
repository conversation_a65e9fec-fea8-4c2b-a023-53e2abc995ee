package com.multiplier.payable.ledger.provider

import com.multiplier.common.exception.toBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.invoice.database.JpaInvoiceRepository
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoice
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import org.springframework.stereotype.Component
import java.math.BigDecimal
import kotlin.jvm.optionals.getOrNull

@Component
class DefaultAdvanceCollectionInvoiceDataProvider(
    private val jpaInvoiceRepository: JpaInvoiceRepository,
) : AdvanceCollectionInvoiceDataProvider {

    override fun findExistingInvoice(payable: CompanyPayable): AdvanceCollectionInvoice {
        val companyPayableId = payable.id
        val jpaInvoice = jpaInvoiceRepository.findByCompanyPayableId(companyPayableId).getOrNull()
            ?: throw PayableErrorCode.INVOICE_NOT_FOUND
                .toBusinessException("Invoice not found for company payable $companyPayableId")
        val collectionLines = LineItemType.getAdvanceCollectionRegisteredLineItemTypes()
        val collectionItems = payable.items
            .filter { LineItemType.valueOf(it.lineItemType) in collectionLines }

        val invoiceLines = jpaInvoice.lineItems
            .filter { it.itemType in collectionLines }
            .map {
                AdvanceCollectionInvoiceLine(
                    itemType = it.itemType.name,
                    taxReference = AdvanceCollectionInvoiceLineTaxReference(
                        taxCode = it.taxCode,
                        taxRate = it.taxRate,
                        taxAmount = BigDecimal.valueOf(it.taxAmount),
                        taxType = it.taxType,
                    ),
                    unitPrice = BigDecimal.valueOf(it.unitPrice),
                    currency = it.baseCurrency,
                    description = it.description,
                    payableItemIds = it.companyPayableLineItemIds.map { id -> id.toString() }.toSet()
                )
            }
        return AdvanceCollectionInvoice(
            transactionId = requireNotNull(payable.transactionId),
            invoiceNo = jpaInvoice.invoiceNo,
            lines = invoiceLines,
            collectionItems = collectionItems
        )
    }
}