package com.multiplier.payable.ledger.listener.invoice

import com.multiplier.common.exception.toBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.ledger.AdvanceCollectionLedger
import com.multiplier.payable.ledger.domain.invoice.ManualAdjustedSecondInvoice
import com.multiplier.payable.ledger.domain.invoice.ManualAdjustedSecondInvoiceCreditNote
import com.multiplier.payable.ledger.domain.invoice.PlatformAdjustedSecondInvoice
import com.multiplier.payable.ledger.domain.invoice.PlatformAdjustedSecondInvoiceCreditNote
import com.multiplier.payable.ledger.provider.AdjustedSecondInvoiceDataProvider
import com.multiplier.payable.ledger.provider.AdvanceCollectionEntryDataProvider
import org.springframework.stereotype.Component

@Component
class LedgerSecondInvoiceListener(
    override val ledger: AdvanceCollectionLedger,
    override val advanceCollectionEntryDataProvider: AdvanceCollectionEntryDataProvider,
    override val entryDataProvider: AdvanceCollectionEntryDataProvider,
    private val secondInvoiceDataProvider: AdjustedSecondInvoiceDataProvider,
) : LedgerStandaloneInvoiceListener {

    override fun onCommit(payable: CompanyPayable) {
        val secondInvoice = secondInvoiceDataProvider.findExisting(payable.id)
        when (secondInvoice) {
            is PlatformAdjustedSecondInvoice -> {
                super.onCommit(payable)
            }

            is PlatformAdjustedSecondInvoiceCreditNote -> {
                super.onCommit(payable)
            }

            is ManualAdjustedSecondInvoice -> {
                if (secondInvoice.getAdjustedLineItems().isEmpty()) {
                    return
                }
                throw PayableErrorCode.UNSUPPORTED_OPERATION
                    .toBusinessException(
                        "Adjustment not supported for manual second invoice," +
                                " payable id = ${payable.id}"
                    )
            }

            is ManualAdjustedSecondInvoiceCreditNote -> {
                if (secondInvoice.getAdjustedLineItems().isEmpty()) {
                    return;
                }
                throw PayableErrorCode.UNSUPPORTED_OPERATION
                    .toBusinessException(
                        "Adjustment not supported for manual credit note," +
                                " payable id = ${payable.id}"
                    )
            }
        }
    }

    override fun onRollback(payable: CompanyPayable) {
        val secondInvoice = secondInvoiceDataProvider.findExisting(payable.id)
        when (secondInvoice) {
            is PlatformAdjustedSecondInvoice -> {
                super.onRollback(payable)
            }

            is PlatformAdjustedSecondInvoiceCreditNote -> {
                super.onRollback(payable)
            }

            is ManualAdjustedSecondInvoice -> {
                if (secondInvoice.getAdjustedLineItems().isEmpty()) {
                    return
                }
                throw PayableErrorCode.UNSUPPORTED_OPERATION
                    .toBusinessException(
                        "Rollback adjustment not supported for manual second invoice," +
                                " payable id = ${payable.id}"
                    )
            }

            is ManualAdjustedSecondInvoiceCreditNote -> {
                if (secondInvoice.getAdjustedLineItems().isEmpty()) {
                    return;
                }
                throw PayableErrorCode.UNSUPPORTED_OPERATION
                    .toBusinessException(
                        "Rollback adjustment not supported for manual credit note," +
                                " payable id = ${payable.id}"
                    )
            }
        }
    }

    override fun transactionType(): TransactionType {
        return TransactionType.SECOND_INVOICE
    }
}