package com.multiplier.payable.ledger.mapper

import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionInvoiceLine
import org.springframework.stereotype.Component

@Component
class AdvanceCollectionInvoiceLineModelToJpaMapper(
    private val taxReferenceModelToJpaMapper: AdvanceCollectionInvoiceLineTaxReferenceModelToJpaMapper
) {
    fun map(referenceLine: AdvanceCollectionInvoiceLine?): JpaAdvanceCollectionInvoiceLine? {
        if (referenceLine == null) {
            return null
        }
        return JpaAdvanceCollectionInvoiceLine(
            itemType = referenceLine.itemType,
            taxReference = taxReferenceModelToJpaMapper.map(referenceLine.taxReference),
            unitPrice = referenceLine.unitPrice,
            currency = referenceLine.currency,
            description = referenceLine.description,
            payableItemIds = referenceLine.payableItemIds
        )
    }
}
