package com.multiplier.payable.ledger.listener.invoice

import com.multiplier.common.exception.toBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.core.payable.adapters.BillingServiceAdapter
import com.multiplier.core.payable.adapters.pricing.ReferenceChargePolicy
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.ledger.AdvanceCollectionLedger
import com.multiplier.payable.ledger.domain.AdvanceCollectionProduct
import com.multiplier.payable.ledger.listener.LedgerInvoiceListener
import com.multiplier.payable.ledger.provider.AdvanceCollectionInvoiceDataProvider
import com.multiplier.payable.ledger.provider.CompanyPrimaryEntityProvider
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class LedgerAdvanceCollectionInvoiceListener(
    private val ledger: AdvanceCollectionLedger,
    private val billingServiceAdapter: BillingServiceAdapter,
    private val advanceCollectionInvoiceDataProvider: AdvanceCollectionInvoiceDataProvider,
    private val companyPrimaryEntityProvider: CompanyPrimaryEntityProvider,
) : LedgerInvoiceListener {

    companion object {
        private val log = KotlinLogging.logger { }
    }

    override fun onCommit(payable: CompanyPayable) {
        log.info("Registering lines for payable id = ${payable.id}")
        val invoice = advanceCollectionInvoiceDataProvider.findExistingInvoice(payable)
        if (invoice.hasCollectionLines().not()) return

        val billedItems = billingServiceAdapter.getBillsByIds(invoice.billIds())
        val primaryEntities = billedItems.map { it.companyId }
            .toSet()
            .associateWith { companyPrimaryEntityProvider.get(it) }

        for (bill in billedItems) {
            val balanceMetadata = invoice.metadataOf(bill) ?: continue
            val chargePolicy = bill.companyProduct.chargePolicy
            require(chargePolicy is ReferenceChargePolicy)
            val referenceBills = bill.referenceBills
            for (referenceBill in referenceBills) {
                val companyId = referenceBill.companyId
                val primaryEntityId = requireNotNull(primaryEntities[referenceBill.companyId])
                val referencedCompanyProduct = referenceBill.companyProduct
                val targetReference = chargePolicy.findTarget(
                    referencedCompanyProduct.lineCode,
                    referencedCompanyProduct.dimensions
                ) ?: continue
                val advanceCollectionProduct = AdvanceCollectionProduct(
                    lineCode = referencedCompanyProduct.lineCode,
                    targetType = targetReference.referenceTargetType.name,
                    dimensions = referencedCompanyProduct.dimensions
                )
                log.info(
                    "Registering balance for company id = $companyId, " +
                            "entity id = $primaryEntityId, advance collection product = $advanceCollectionProduct, " +
                            "billing amount = ${referenceBill.billingAmount}, balance metadata = $balanceMetadata"
                )
                ledger.registerBalance(
                    companyId = companyId,
                    entityId = primaryEntityId,
                    advanceCollectionProduct = advanceCollectionProduct,
                    amount = referenceBill.billingAmount,
                    metadata = balanceMetadata
                )
            }
        }
    }

    override fun onRollback(payable: CompanyPayable) {
        throw PayableErrorCode.ADVANCE_COLLECTION_BALANCE_ROLLBACK_NOT_SUPPORTED
            .toBusinessException("Rollback not supported for advance collection balance, payable id = ${payable.id}")
    }

    override fun transactionType(): TransactionType {
        return TransactionType.ORDER_FORM_ADVANCE
    }
}