package com.multiplier.payable.ledger.listener

import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.types.PayableStatus

interface LedgerInvoiceListener {

    companion object {
        val ROLLBACK_PAYABLE_STATUSES = setOf(
            PayableStatus.DELETED,
            PayableStatus.VOIDED
        )
        val COMMIT_PAYABLE_STATUSES = setOf(
            PayableStatus.PAID
        )
    }

    fun checkAndCommit(payable: CompanyPayable) {
        if (isValid(payable).not()) return
        if (payable.status in COMMIT_PAYABLE_STATUSES) {
            onCommit(payable)
        }
    }

    fun checkAndRollback(payable: CompanyPayable) {
        if (isValid(payable).not()) return
        if (payable.status in ROLLBACK_PAYABLE_STATUSES) {
            onRollback(payable)
        }
    }

    fun onCommit(payable: CompanyPayable)

    fun onRollback(payable: CompanyPayable)

    fun isValid(payable: CompanyPayable): Boolean {
        return transactionType() == payable.itemType
    }

    fun transactionType(): TransactionType
}