package com.multiplier.payable.ledger.listener

import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.types.PayableStatus

interface LedgerInvoiceListener {

    companion object {
        val ROLLBACK_PAYABLE_STATUSES = setOf(
            PayableStatus.DELETED,
            PayableStatus.VOIDED
        )
        val COMMIT_PAYABLE_STATUSES = setOf(
            PayableStatus.PAID
        )
    }

    fun onCommit(payable: CompanyPayable)

    fun onRollback(payable: CompanyPayable)

    fun transactionType(): TransactionType
}