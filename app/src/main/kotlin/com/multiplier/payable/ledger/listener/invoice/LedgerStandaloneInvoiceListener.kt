package com.multiplier.payable.ledger.listener.invoice

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.ledger.AdvanceCollectionLedger
import com.multiplier.payable.ledger.listener.LedgerInvoiceListener
import com.multiplier.payable.ledger.provider.AdvanceCollectionEntryDataProvider
import mu.KotlinLogging

interface LedgerStandaloneInvoiceListener : LedgerInvoiceListener {

    val ledger: AdvanceCollectionLedger
    val advanceCollectionEntryDataProvider: AdvanceCollectionEntryDataProvider
    val entryDataProvider: AdvanceCollectionEntryDataProvider

    companion object {
        private val log = KotlinLogging.logger { }
    }

    override fun onCommit(payable: CompanyPayable) {
        log.info("Commit adjustment line for payable id = ${payable.id}")
        val adjustmentLines = LineItemType.getAdvanceCollectionAdjustmentLineItemTypes().toSet()
        val adjustmentItems = payable.items.filter {
            LineItemType.valueOf(it.lineItemType) in adjustmentLines
        }
        if (adjustmentItems.isEmpty()) {
            log.info { "No adjustment lines found for payable id = ${payable.id}" }
            return
        }

        val transactionId = requireNotNull(payable.transactionId)
        val entries = advanceCollectionEntryDataProvider.findByTransactionId(transactionId)
        if (entries.size != adjustmentItems.size) {
            log.error {
                "Number of entries found for transaction id = $transactionId " +
                        "does not match the number of adjustment items. " +
                        "Entries found = ${entries.size}, adjustment items = ${adjustmentItems.size}"
            }
            return
        }

        entries.forEach { entry ->
            try {
                ledger.commit(entry)
            } catch (e: Exception) {
                log.error(e) { "Failed to commit entry $entry for payable id = ${payable.id}" }
            }
        }
    }

    override fun onRollback(payable: CompanyPayable) {
        log.info("Rollback adjustment line for payable id = ${payable.id}")
        val transactionId = requireNotNull(payable.transactionId)
        val entries = entryDataProvider.findByTransactionId(transactionId)
        entries.forEach { entry ->
            ledger.rollBack(entry)
        }
    }
}