package com.multiplier.payable.ledger.listener.invoice

import com.multiplier.common.exception.toBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.ledger.listener.LedgerInvoiceListener
import org.springframework.stereotype.Component

@Component
class LedgerInvoiceListenerFactory(
    private val listeners: List<LedgerInvoiceListener>
) {
    companion object {
        val SUPPORTED_TRANSACTION_TYPES = setOf(
            TransactionType.ORDER_FORM_ADVANCE,
            TransactionType.SECOND_INVOICE,
            TransactionType.ANNUAL_PLAN_AOR_INVOICE,
            TransactionType.ANNUAL_PLAN_INVOICE,
            TransactionType.GP_SERVICE_INVOICE
        )
    }

    private var listenerMap = mutableMapOf<TransactionType, LedgerInvoiceListener>()

    init {
        listeners.forEach { listener ->
            listenerMap[listener.transactionType()] = listener
        }
    }

    fun get(transactionType: TransactionType): LedgerInvoiceListener {
        return listenerMap[transactionType]
            ?: throw PayableErrorCode.NOT_SUPPORTED_INVOICE_TYPE
                .toBusinessException("No ledger invoice listener found for transaction type: $transactionType")
    }
}