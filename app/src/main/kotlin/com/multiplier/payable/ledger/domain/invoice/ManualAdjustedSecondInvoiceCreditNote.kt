package com.multiplier.payable.ledger.domain.invoice

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.creditnote.database.CreditNoteDto
import com.multiplier.core.payable.creditnote.database.CreditNoteItemDto
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable

class ManualAdjustedSecondInvoiceCreditNote(
    override val payable: CompanyPayable,
    val creditNote: CreditNoteDto
) : AdjustedSecondInvoice {

    fun getAdjustedLineItems(): List<CreditNoteItemDto> {
        return creditNote.items.filter {
            it.itemType in LineItemType.getAdvanceCollectionAdjustmentLineItemTypes()
        }
    }
}