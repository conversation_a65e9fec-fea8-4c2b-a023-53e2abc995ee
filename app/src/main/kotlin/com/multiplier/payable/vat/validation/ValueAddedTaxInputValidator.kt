package com.multiplier.payable.vat.validation

import com.multiplier.payable.vat.ValueAddedTax
import com.multiplier.payable.vat.ValueAddedTaxRateType
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class ValueAddedTaxInputValidator {

    companion object {
        private const val MIN_VAT_VALUE = 0
        private const val MAX_PERCENTAGE_VAT_VALUE = 50
        private val logger = KotlinLogging.logger {  }
    }

    fun validate(valueAddedTax: ValueAddedTax) {
        if (valueAddedTax.rate < MIN_VAT_VALUE){
            throw ValueAddedTaxException("valueAddedTax.rate must be greater than or equal to $MIN_VAT_VALUE")
        }

        if (
            valueAddedTax.rateType == ValueAddedTaxRateType.PERCENTAGE
                && valueAddedTax.rate > MAX_PERCENTAGE_VAT_VALUE
        ) {
            throw ValueAddedTaxException("valueAddedTax.rate must be less than or equal to " +
                "$MAX_PERCENTAGE_VAT_VALUE for type ${ValueAddedTaxRateType.PERCENTAGE}")
        }
        logger.info { "Input validated." }
    }
}