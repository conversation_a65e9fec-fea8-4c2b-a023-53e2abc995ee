package com.multiplier.payable.vat.company.application

import com.multiplier.payable.vat.company.CompanyCountryValueAddedTaxAggregate
import com.multiplier.payable.vat.company.CompanyCountryValueAddedTaxRepository
import com.multiplier.payable.vat.company.application.input.CompanyCountryValueAddedTaxFindInput
import com.multiplier.payable.vat.company.application.input.CompanyCountryValueAddedTaxSaveInput
import com.multiplier.payable.vat.validation.ValueAddedTaxInputValidator
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class CompanyCountryValueAddedTaxCreateUseCase(
    private val companyCountryValueAddedTaxRepository: CompanyCountryValueAddedTaxRepository,
    private val valueAddedTaxInputValidator: ValueAddedTaxInputValidator
) {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    fun create(companyCountryValueAddedTaxSaveInput: CompanyCountryValueAddedTaxSaveInput) {

        logger.info { "Finding VAT = $companyCountryValueAddedTaxSaveInput" }

        val companyValueAddedTaxEntity =  companyCountryValueAddedTaxRepository.find(
            CompanyCountryValueAddedTaxFindInput(
                companyCountryValueAddedTaxSaveInput.companyId,
                companyCountryValueAddedTaxSaveInput.countryCode
            )
        )

        val companyCountryValueAddedTaxAggregate = CompanyCountryValueAddedTaxAggregate(companyValueAddedTaxEntity)

        companyCountryValueAddedTaxAggregate.create(
            companyCountryValueAddedTaxSaveInput,
            companyCountryValueAddedTaxRepository,
            valueAddedTaxInputValidator
        )
    }
}