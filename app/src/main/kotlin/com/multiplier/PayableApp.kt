package com.multiplier

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.context.annotation.Bean
import org.springframework.retry.annotation.EnableRetry
import reactivefeign.spring.config.EnableReactiveFeignClients
import java.time.Clock

@SpringBootApplication
@EnableFeignClients
@EnableRetry
@EnableReactiveFeignClients
@ConfigurationPropertiesScan(basePackageClasses = [PayableApp::class])
class PayableApp {
    @Bean
    fun clock(): Clock? {
        return Clock.systemDefaultZone()
    }
}

fun main(args: Array<String>) {
    runApplication<PayableApp>(*args)
}
