package com.multiplier.dispute.facade

import com.multiplier.dispute.category.DisputeCategoryToApiMapper
import com.multiplier.dispute.database.CreateDisputeRequest
import com.multiplier.dispute.origin.api.DisputeOriginType
import com.multiplier.dispute.origin.database.CreateDisputeOriginRequest
import com.multiplier.dispute.subcategory.DisputeSubCategoryToApiMapper
import com.multiplier.payable.types.CreateDisputeInput
import org.springframework.stereotype.Component

/**
 * A mapper that maps [CreateDisputeInput] to [CreateDisputeRequest].
 */
@Component
class CreateDisputeRequestMapper(
    private val categoryToApiMapper: DisputeCategoryToApiMapper,
    private val subCategoryToApiMapper: DisputeSubCategoryToApiMapper,
) {

    fun map(createDisputeInput: CreateDisputeInput): CreateDisputeRequest {
        return CreateDisputeRequest(
            category = categoryToApiMapper.map(createDisputeInput.reasonCategory),
            subCategory = subCategoryToApiMapper.map(createDisputeInput.reasonSubCategory),
            description = createDisputeInput.description,
            disputeOrigin = CreateDisputeOriginRequest(
                type = DisputeOriginType.valueOf(createDisputeInput.originType.name),
                originId = createDisputeInput.originId,
                companyId = createDisputeInput.companyId
            )
        )
    }
}