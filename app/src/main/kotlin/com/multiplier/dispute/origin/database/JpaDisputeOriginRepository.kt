package com.multiplier.dispute.origin.database;

import com.multiplier.dispute.origin.api.DisputeOriginType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface JpaDisputeOriginRepository : JpaRepository<JpaDisputeOrigin, Long> {

    fun findByOriginId(originId: Long): Optional<JpaDisputeOrigin>

    fun findByType(type: DisputeOriginType): List<JpaDisputeOrigin>

    fun findByTypeAndCompanyIdIn(type: DisputeOriginType, companyIds: List<Long>): List<JpaDisputeOrigin>

    fun findByOriginIdAndType(originId: Long, type: DisputeOriginType): Optional<JpaDisputeOrigin>

    fun findByOriginIdAndTypeAndCompanyId(originId: Long, type: DisputeOriginType, companyId: Long): Optional<JpaDisputeOrigin>
}