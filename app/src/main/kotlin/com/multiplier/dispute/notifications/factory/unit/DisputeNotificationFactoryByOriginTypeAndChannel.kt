package com.multiplier.dispute.notifications.factory.unit

import com.multiplier.dispute.notifications.enumeration.DisputeNotificationChannel
import com.multiplier.dispute.origin.api.DisputeOriginType

fun interface DisputeNotificationFactoryByOriginTypeAndChannel {
    fun getHandler(
        businessType: DisputeOriginType,
        channelType: DisputeNotificationChannel,
    ): DisputeNotificationHandlerByOriginTypeAndChannel
}