package com.multiplier.dispute.notifications.factory.unit

import com.multiplier.dispute.api.Dispute
import com.multiplier.dispute.notifications.enumeration.DisputeNotificationChannel
import com.multiplier.dispute.origin.api.DisputeOriginType

interface DisputeNotificationHandlerByOriginTypeAndChannel {
    // TODO: make this one single qualifier data class
    val disputeOriginType: DisputeOriginType
    val channelType: DisputeNotificationChannel

    fun handle(disputeDto: Dispute)
}