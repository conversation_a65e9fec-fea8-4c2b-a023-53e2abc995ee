package com.multiplier.dispute.notifications.factory.business.impl

import com.multiplier.dispute.api.Dispute
import com.multiplier.dispute.notifications.config.channel.DisputeNotificationChannelConfig
import com.multiplier.dispute.notifications.factory.business.DisputeNotificationHandlerByOriginType
import com.multiplier.dispute.notifications.factory.unit.DisputeNotificationFactoryByOriginTypeAndChannel
import com.multiplier.dispute.origin.api.DisputeOriginType
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class CompanyPayableDisputeHandler(
    private val disputeNotificationFactoryByOriginTypeAndChannel: DisputeNotificationFactoryByOriginTypeAndChannel,
    private val notificationChannelConfig: DisputeNotificationChannelConfig,
) : DisputeNotificationHandlerByOriginType {
    companion object {
        private val log = LoggerFactory.getLogger(this::class.java)
    }

    override val disputeOriginType = DisputeOriginType.COMPANY_PAYABLE

    override fun handle(disputeDto: Dispute) {
        val channels = notificationChannelConfig.getChannelsForOriginType(disputeOriginType)
        // Iterate over all attached channels for company_payable and handle the dispute notification for each
        channels.forEach { channelType ->
            val originAndChannelHandler =
                disputeNotificationFactoryByOriginTypeAndChannel.getHandler(disputeOriginType, channelType)
            log.debug("Retrieved originAndChannelHandler: {}", originAndChannelHandler)
            originAndChannelHandler.handle(disputeDto)
        }
    }
}
