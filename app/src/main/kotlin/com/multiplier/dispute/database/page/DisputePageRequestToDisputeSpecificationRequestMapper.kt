package com.multiplier.dispute.database.page

import com.multiplier.dispute.database.specification.DisputeSpecificationRequest
import org.springframework.stereotype.Component

/**
 * A mapper that maps [DisputePageRequest] to [DisputeSpecificationRequest].
 */
@Component
class DisputePageRequestToDisputeSpecificationRequestMapper {

    fun map(request: DisputePageRequest, disputeOriginIds: List<Long>): DisputeSpecificationRequest {
        return return DisputeSpecificationRequest(
            userExperience = request.userExperience,
            statuses = request.statuses,
            disputeOriginIds = disputeOriginIds
        )
    }
}