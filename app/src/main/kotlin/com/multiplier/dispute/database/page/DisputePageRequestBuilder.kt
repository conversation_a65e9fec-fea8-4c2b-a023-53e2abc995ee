package com.multiplier.dispute.database.page

import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Component

/**
 * A class that builds [PageRequest] from [DisputePageRequest].
 */
@Component
class DisputePageRequestBuilder(
    private val disputeSortOrderBuilder: DisputeSortOrderBuilder,
) {

    fun build(disputePageRequest: DisputePageRequest): PageRequest {
        val sortOrders = disputePageRequest.sortOrders
        if (sortOrders.isNullOrEmpty()) {
            return PageRequest.of(
                disputePageRequest.pageNumber,
                disputePageRequest.pageSize,
            )
        }
        val sort = disputeSortOrderBuilder.build(sortOrders)
        return PageRequest.of(
            disputePageRequest.pageNumber,
            disputePageRequest.pageSize,
            sort
        )
    }
}