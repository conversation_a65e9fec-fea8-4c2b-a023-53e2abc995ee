package com.multiplier.dispute.database.specification

import com.multiplier.dispute.database.JpaDispute
import jakarta.persistence.criteria.CriteriaBuilder
import jakarta.persistence.criteria.CriteriaQuery
import jakarta.persistence.criteria.Path
import jakarta.persistence.criteria.Root
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Component

@Component
class InSpecificationBuilder {

    fun <T> build(columnName: String, columnValues: List<T>): Specification<JpaDispute> {
        return Specification {
                root: Root<JpaDispute>,
                _: CriteriaQuery<*>,
                criteriaBuilder: CriteriaBuilder,
            ->
            val path: Path<T> = root[columnName]
            val pathPredicate = criteriaBuilder.`in`(path)
            columnValues.forEach { pathPredicate.value(it) }
            pathPredicate
        }
    }
}