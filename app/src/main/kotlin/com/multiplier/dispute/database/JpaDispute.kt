package com.multiplier.dispute.database

import com.multiplier.dispute.api.DisputeCategory
import com.multiplier.dispute.api.DisputeStatus
import com.multiplier.dispute.api.DisputeSubCategory
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EntityListeners
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.SequenceGenerator
import jakarta.persistence.Table
import org.hibernate.envers.Audited
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

@Entity
@Table(schema = "platform", name = "dispute")
@SequenceGenerator(
    name = "dispute_seq_gen",
    schema = "platform", sequenceName = "dispute_seq", allocationSize = 1, initialValue = 1
)
@Audited
@EntityListeners(AuditingEntityListener::class)
data class JpaDispute(

    @Id
    @Column(nullable = false)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "dispute_seq_gen")
    val id: Long = 0,

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    var status: DisputeStatus,

    @Column(nullable = true)
    @Enumerated(EnumType.STRING)
    val category: DisputeCategory,

    @Column(nullable = true)
    @Enumerated(EnumType.STRING)
    val subCategory: DisputeSubCategory? = null,

    @Column(nullable = true)
    val description: String?,

    @Column(nullable = false)
    val disputeOriginId: Long,

    @Column(nullable = false)
    @CreatedBy
    var createdBy: Long? = null,

    @Column(nullable = false)
    @CreatedDate
    var createdOn: LocalDateTime? = null,

    @Column(nullable = false)
    @LastModifiedBy
    var updatedBy: Long? = null,

    @Column(nullable = false)
    @LastModifiedDate
    var updatedOn: LocalDateTime? = null,

    )