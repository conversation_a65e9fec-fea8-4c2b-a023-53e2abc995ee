package com.multiplier.dispute.database.specification

import com.multiplier.core.util.UserExperience
import com.multiplier.dispute.database.JpaDispute
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Component

/**
 * A builder class that contains shared logic for implementations of [DisputeSpecificationBuilder].
 * Purposes:
 * + Stay away from inheritance because it's coupling and not a good practice
 *  ~ See https://softwareengineering.stackexchange.com/questions/134097/why-should-i-prefer-composition-over-inheritance#134115
 * + Favor composition over inheritance - Effective Java, Item 18
 *  ~ See https://github.com/Multiplier-Core/payable-service/pull/170/files#r1450178702
 */
@Component
class BaseDisputeSpecificationBuilder(
    private val inSpecificationBuilder: InSpecificationBuilder,
) {

    fun build(request: DisputeSpecificationRequest): Specification<JpaDispute> {
        var specs = ArrayList<Specification<JpaDispute>>()
        var resultSpec = getDefaultSpec()

        if (request.statuses.isNotEmpty()) {
            val statuses = request.statuses
            specs.add(inSpecificationBuilder.build("status", statuses))
        }

        if (request.userExperience == UserExperience.CUSTOMER) {
            if (request.disputeOriginIds.isEmpty()) {
                // Invalidate the query for safety
                specs.add(inSpecificationBuilder.build("disputeOriginId", listOf(-1)))
            } else {
                val originIds = request.disputeOriginIds
                specs.add(inSpecificationBuilder.build("disputeOriginId", originIds))
            }
        }

        if (request.userExperience == UserExperience.OPERATIONS
            && request.disputeOriginIds.isNotEmpty()
        ) {
            val originIds = request.disputeOriginIds
            specs.add(inSpecificationBuilder.build("disputeOriginId", originIds))
        }

        for (spec in specs) {
            resultSpec = resultSpec.and(spec)
        }
        return resultSpec
    }

    private fun getDefaultSpec(): Specification<JpaDispute> {
        return Specification.where(null)
    }
}