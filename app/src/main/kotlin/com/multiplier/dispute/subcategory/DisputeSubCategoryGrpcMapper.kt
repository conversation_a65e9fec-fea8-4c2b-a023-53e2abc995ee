package com.multiplier.dispute.subcategory

import com.multiplier.dispute.api.DisputeSubCategory
import com.multiplier.dispute.grpc.schema.DisputeOuterClass
import org.springframework.stereotype.Component

@Component
class DisputeSubCategoryGrpcMapper {

    fun mapToGrpcSubCategory(disputeSubCategory: DisputeSubCategory?): DisputeOuterClass.DisputeSubCategory? {
        if (disputeSubCategory == null) {
            return null
        }

        return when (disputeSubCategory) {
            DisputeSubCategory.INVOICE_SERVICE_AMOUNT_INCORRECT ->
                DisputeOuterClass.DisputeSubCategory.SUBCATEGORY_INVOICE_SERVICE_AMOUNT_INCORRECT
            DisputeSubCategory.INVOICE_BILLING_CURRENCY_INCORRECT ->
                DisputeOuterClass.DisputeSubCategory.SUBCATEGORY_INVOICE_BILLING_CURRENCY_INCORRECT
            DisputeSubCategory.INVOICE_MANAGEMENT_FEE_INCORRECT ->
                DisputeOuterClass.DisputeSubCategory.SUBCATEGORY_INVOICE_MANAGEMENT_FEE_INCORRECT
            DisputeSubCategory.INVOICE_OTHER_FEES ->
                DisputeOuterClass.DisputeSubCategory.SUBCATEGORY_INVOICE_OTHER_FEES
            DisputeSubCategory.OTHER ->
                DisputeOuterClass.DisputeSubCategory.SUBCATEGORY_OTHER
        }
    }
}