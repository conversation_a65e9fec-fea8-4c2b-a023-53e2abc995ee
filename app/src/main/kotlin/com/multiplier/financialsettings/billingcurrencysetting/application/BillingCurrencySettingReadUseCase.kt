package com.multiplier.financialsettings.billingcurrencysetting.application

import com.multiplier.core.payable.pricing.adapter.PricingServiceAdapter
import com.multiplier.financialsettings.application.dto.BillingCurrencySettingDimensionInput
import com.multiplier.financialsettings.application.dto.FinancialSettingDimensionInput
import com.multiplier.financialsettings.billingcurrencysetting.domain.BillingCurrencySettingRepository
import com.multiplier.financialsettings.billingcurrencysetting.domain.BillingCurrencySettingDimensionValidator
import com.multiplier.financialsettings.domain.exception.GlobalDefaultSettingNotFoundException
import com.multiplier.financialsettings.domain.exception.NoMatchingSettingFoundException
import com.multiplier.financialsettings.domain.model.FinancialSettingResponseStrategy
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class BillingCurrencySettingReadUseCase(
    private val billingCurrencySettingRepository: BillingCurrencySettingRepository,
    private val billingCurrencyDimensionValidator: BillingCurrencySettingDimensionValidator,
    private val pricingServiceAdapter: PricingServiceAdapter
) {

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    fun read(
        companyId: Long,
        contextId: Long?,
        dimensionInput: FinancialSettingDimensionInput,
        strategy: FinancialSettingResponseStrategy
    ): String {
        logger.info { "Reading billing currency setting for companyId: $companyId, contextId: $contextId, dimensionInput: $dimensionInput, strategy: $strategy" }

        if (contextId == null) {
            return handleNullContextId(companyId, strategy)
        }

        return when (strategy) {
            FinancialSettingResponseStrategy.MATCH -> {
                findMatchedValue(contextId, dimensionInput)
                    ?: throw NoMatchingSettingFoundException()
            }
            FinancialSettingResponseStrategy.FALLBACK -> {
                findMatchedValue(contextId, dimensionInput)
                    ?: findGlobalValue(contextId)
                    ?: run {
                        logger.info { "No entity default found for contextId: $contextId. Fetching global default from pricing service." }
                        getGlobalDefaultBillingCurrency(companyId)
                    }
            }
        }
    }

    /**
     * Handles the case when contextId is null based on the response strategy.
     */
    private fun handleNullContextId(companyId: Long, strategy: FinancialSettingResponseStrategy): String {
        return when (strategy) {
            FinancialSettingResponseStrategy.MATCH -> {
                logger.info { "CompanyEntityContext not found with MATCH strategy. Throwing exception." }
                throw NoMatchingSettingFoundException()
            }
            FinancialSettingResponseStrategy.FALLBACK -> {
                logger.info { "CompanyEntityContext not found with FALLBACK strategy. Fetching global default from pricing service." }
                getGlobalDefaultBillingCurrency(companyId)
            }
        }
    }

    /**
     * Finds an exact match for the given dimensions.
     * Returns null if no exact match is found.
     */
    private fun findMatchedValue(contextId: Long, dimensionInput: FinancialSettingDimensionInput): String? {
        logger.info { "Finding matched value for contextId: $contextId, dimensionInput: $dimensionInput" }

        return when (dimensionInput) {
            is BillingCurrencySettingDimensionInput -> {
                // Get string value for database query (either enum name or "GLOBAL")
                val transactionTypeString = billingCurrencyDimensionValidator.getTransactionTypeStringForQuery(dimensionInput)
                logger.info { "Querying with transaction type: $transactionTypeString" }

                val exactMatch = billingCurrencySettingRepository.findByContextIdAndTransactionType(contextId, transactionTypeString)
                if (exactMatch != null) {
                    logger.info { "Found exact match for contextId: $contextId, transactionType: $transactionTypeString" }
                    return exactMatch.currencyCode
                }

                logger.info { "No exact match found for contextId: $contextId, transactionType: $transactionTypeString" }
                null
            }
            else -> {
                logger.warn { "Unexpected dimension input type: ${dimensionInput::class.simpleName}" }
                null
            }
        }
    }

    /**
     * Finds the global setting (with all dimensions set to GLOBAL).
     * Returns null if no global setting is found.
     */
    private fun findGlobalValue(contextId: Long): String? {
        logger.info { "Finding global value for contextId: $contextId" }

        // Find setting with "GLOBAL" transaction type
        val globalSetting = billingCurrencySettingRepository.findByContextId(contextId)
        if (globalSetting != null) {
            logger.info { "Found global setting for contextId: $contextId" }
            return globalSetting.currencyCode
        }

        logger.info { "No global setting found for contextId: $contextId" }
        return null
    }

    /**
     * Fetches the global default billing currency from the pricing service.
     * Returns the currency code as string if found, throws exception if not found.
     */
    private fun getGlobalDefaultBillingCurrency(companyId: Long): String {
        logger.info { "Fetching global default billing currency for companyId: $companyId" }

        val currencyCode = pricingServiceAdapter.getBillingCurrencyCode(companyId)
        if (currencyCode != null) {
            logger.info { "Found global default billing currency: $currencyCode for companyId: $companyId" }
            return currencyCode.name
        }

        logger.warn { "No billing currency found in pricing service for companyId: $companyId" }
        throw GlobalDefaultSettingNotFoundException()
    }
}
