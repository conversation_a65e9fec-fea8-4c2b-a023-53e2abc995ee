package com.multiplier.financialsettings.billingcurrencysetting.db

import jakarta.persistence.*
import org.hibernate.envers.Audited
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

@Entity
@Table(name = "billing_currency_settings", schema = "payable")
@SequenceGenerator(
    name = "billing_currency_settings_seq_gen",
    schema = "payable",
    sequenceName = "billing_currency_settings_seq",
    allocationSize = 1,
    initialValue = 1
)
@EntityListeners(AuditingEntityListener::class)
@Audited
data class JpaBillingCurrencySetting(
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "billing_currency_settings_seq_gen")
    val id: Long? = null,

    @Column(name = "company_entity_context_id", nullable = false)
    val companyEntityContextId: Long,

    @Column(name = "transaction_type", nullable = false)
    val transactionType: String,

    @Column(name = "billing_currency_code", nullable = false)
    val billingCurrencyCode: String,

    @Column(nullable = false, updatable = false)
    @CreatedBy
    var createdBy: Long? = null,

    @Column(nullable = false, updatable = false)
    @CreatedDate
    var createdOn: LocalDateTime? = null,

    @Column(nullable = false)
    @LastModifiedBy
    var updatedBy: Long? = null,

    @Column(nullable = false)
    @LastModifiedDate
    var updatedOn: LocalDateTime? = null
)
