package com.multiplier.financialsettings.billingcurrencysetting.db

import com.multiplier.financialsettings.billingcurrencysetting.domain.BillingCurrencySetting
import com.multiplier.financialsettings.billingcurrencysetting.domain.BillingCurrencySettingRepository
import mu.KotlinLogging
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class BillingCurrencySettingRepositoryImpl(
    private val jpaRepository: JpaBillingCurrencySettingRepository
) : BillingCurrencySettingRepository {

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    @Transactional(readOnly = true)
    override fun findByContextIdAndTransactionType(contextId: Long, transactionType: String): BillingCurrencySetting? {
        logger.info { "Finding billing currency setting for contextId: $contextId, transactionType: $transactionType" }
        return jpaRepository.findByCompanyEntityContextIdAndTransactionType(contextId, transactionType)
            ?.let { mapJpaToEntity(it) }
    }

    @Transactional(readOnly = true)
    override fun findByContextId(contextId: Long): BillingCurrencySetting? {
        logger.info { "Finding global fallback for contextId: $contextId" }
        return jpaRepository.findByContextId(contextId)
            ?.let { mapJpaToEntity(it) }
    }

    private fun mapJpaToEntity(jpa: JpaBillingCurrencySetting): BillingCurrencySetting {
        return BillingCurrencySetting(
            id = jpa.id,
            companyEntityContextId = jpa.companyEntityContextId,
            transactionType = jpa.transactionType,
            currencyCode = jpa.billingCurrencyCode
        )
    }
}
