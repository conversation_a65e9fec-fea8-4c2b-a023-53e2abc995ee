package com.multiplier.financialsettings.domain.service.validator

import com.multiplier.financialsettings.application.dto.FinancialSettingDimensionInput
import com.multiplier.financialsettings.domain.model.FinancialSettingDimension
import com.multiplier.financialsettings.domain.model.FinancialSettingType

/**
 * Interface for validating and converting dimension inputs for specific financial setting types.
 * Each implementation handles validation logic for a specific setting type.
 */
interface FinancialSettingDimensionValidator {

    /**
     * The financial setting type this validator handles
     */
    val settingType: FinancialSettingType

    /**
     * Validates that the dimension input is appropriate for the setting type
     * and converts it to the internal FinancialSettingDimension representation.
     *
     * @param dimensionInput The dimension input to validate and convert
     * @return List of validated and converted dimensions
     * @throws InvalidDimensionInputException if the input is invalid for this setting type
     */
    fun validateAndConvertDimensions(dimensionInput: FinancialSettingDimensionInput): List<FinancialSettingDimension>
    
    /**
     * Validates that all provided dimensions are supported for the setting type.
     * This is an additional validation layer for internal dimension objects.
     *
     * @param dimensions The dimensions to validate
     * @throws UnsupportedDimensionForSettingTypeException if any dimension is not supported
     */
    fun validateDimensionSupport(dimensions: List<FinancialSettingDimension>)
}
