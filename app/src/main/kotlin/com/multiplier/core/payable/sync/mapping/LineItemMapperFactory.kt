package com.multiplier.core.payable.sync.mapping

import com.multiplier.core.payable.companypayable.database.CompanyPayableDto
import com.multiplier.payable.types.CompanyPayableType
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
internal class LineItemMapperFactory(
    private val vasIncidentInvoiceMapper: VasIncidentInvoiceLineItemMapper,
    private val defaultLineItemMapper: DefaultLineItemMapper
) {

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    fun selectInvoiceLineItemMapper(companyPayableDto: CompanyPayableDto?): InvoiceLineItemMapper {
        if (companyPayableDto == null) {
            logger.info { "No company payable found, will use default mapper" }
            return defaultLineItemMapper
        }

        return when (companyPayableDto.type) {
            CompanyPayableType.VAS_INCIDENT_INVOICE -> {
                logger.info { "Selected VAS incident mapper for company payable type: ${companyPayableDto.type}" }
                vasIncidentInvoiceMapper
            }
            else -> {
                logger.info { "No specific mapper for company payable type: ${companyPayableDto.type}, will use default" }
                defaultLineItemMapper
            }
        }
    }

    fun selectCreditNoteLineItemMapper(companyPayableDto: CompanyPayableDto?): CreditNoteLineItemMapper {
        if (companyPayableDto == null) {
            logger.info { "No company payable found, will use default mapper" }
            return defaultLineItemMapper
        }

        return when (companyPayableDto.type) {
            CompanyPayableType.VAS_INCIDENT_INVOICE -> {
                logger.info { "Selected VAS incident mapper for credit note with company payable type: ${companyPayableDto.type}" }
                vasIncidentInvoiceMapper as CreditNoteLineItemMapper
            }
            else -> {
                logger.info { "No specific mapper for credit note with company payable type: ${companyPayableDto.type}, will use default" }
                defaultLineItemMapper
            }
        }
    }

}
