package com.multiplier.core.payable.report.database

import com.multiplier.core.payable.companypayable.database.CompanyPayableDto
import org.springframework.stereotype.Component

/**
 * A mapper that maps [CompanyPayableDto] to [InvoiceSourceReportKeyDto].
 */
@Component
class CompanyPayableDtoToInvoiceSourceReportKeyDtoMapper {

    fun map(companyPayableDto: CompanyPayableDto): InvoiceSourceReportKeyDto {
        return InvoiceSourceReportKeyDto.builder()
            .companyId(companyPayableDto.companyId)
            .month(companyPayableDto.month)
            .year(companyPayableDto.year)
            .payableId(companyPayableDto.id)
            .build()
    }

    fun map(companyPayableDtos: List<CompanyPayableDto>): List<InvoiceSourceReportKeyDto> {
        return companyPayableDtos.map(::map)
    }
}