package com.multiplier.core.payable.invoice.database

import com.multiplier.core.payable.repository.model.InvoiceType
import com.multiplier.payable.types.InvoiceStatus
import java.time.LocalDate

data class InvoiceFilters(
    val companyId : Long? = null,
    @Deprecated("use due date range instead")
    val dueDate : LocalDate? = null,
    @Deprecated("use criteria fields instead")
    val invoiceTypes : Set<InvoiceType>? = null,
    val invoiceStatuses : Set<InvoiceStatus>,
    val inclusionCriteria: InvoiceIncludeCriteria? = null,
    val exclusionCriteria: InvoiceExcludeCriteria? = null,
    val dueDateRange: DateRange? = null,
    val invoiceDateRange: DateRange? = null,
    val companyIds: List<Long>? = null,
)

data class DateRange(
    val toDate : LocalDate,
    val fromDate : LocalDate?,
)

data class InvoiceExcludeCriteria(
    val invoiceTypes : Set<InvoiceType> = setOf(),
)

data class InvoiceIncludeCriteria(
    val invoiceTypes : Set<InvoiceType> = setOf(),
)


