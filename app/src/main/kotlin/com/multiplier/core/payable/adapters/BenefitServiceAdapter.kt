package com.multiplier.core.payable.adapters

import com.multiplier.core.payable.service.exception.EntityNotFoundException
import com.multiplier.core.schema.contract.Contract.ContractStatus
import com.multiplier.core.schema.grpc.benefit.Benefit
import com.multiplier.core.schema.grpc.benefit.Benefit.GrpcBenefit
import com.multiplier.core.schema.grpc.benefit.BenefitServiceGrpc.BenefitServiceBlockingStub
import com.multiplier.core.util.dto.core.benefit.InsurancePremiumWrapper
import com.multiplier.payable.engine.collector.core.benefit.GrpcContractBenefitMapper
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.domain.aggregates.MonthYearDuration
import com.multiplier.payable.util.DateUtils
import io.grpc.Status
import io.grpc.StatusRuntimeException
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface BenefitServiceAdapter {
    fun getBenefitByID(id: String): GrpcBenefit
    fun getContractBenefitsByContractIds(
        contractIds: List<Long>,
    ): Map<Long, List<Benefit.GrpcContractBenefit>>

    fun getActiveContractBenefitsForDuration(
        companyId: Long,
        duration: MonthYearDuration,
    ): List<InsurancePremiumWrapper>

    fun getActiveContractBenefitsForMonthYear(
        companyId: Long,
        monthYear: MonthYear,
        overrideBatchTime: Long? = System.currentTimeMillis(),
    ): List<InsurancePremiumWrapper>
}

@Service
class DefaultBenefitServiceAdapter(
    private val contractBenefitMapper: GrpcContractBenefitMapper,
) : BenefitServiceAdapter {

    @GrpcClient("core-service")
    lateinit var stub: BenefitServiceBlockingStub

    override fun getBenefitByID(id: String): GrpcBenefit {
        val request = com.multiplier.core.schema.grpc.benefit.Benefit.GetBenefitByIDInput.newBuilder()
            .setId(id)
            .build()

        return stub.getBenefitByID(request)
    }

    override fun getContractBenefitsByContractIds(
        contractIds: List<Long>,
    ): Map<Long, List<Benefit.GrpcContractBenefit>> {
        val request = Benefit.GetBenefitForContractsInput.newBuilder()
            .addAllContractIds(contractIds)
            .build();

        return stub.getContractBenefitsByContractIds(request)
            .contractBenefitMapList.associateBy({ it.contractId }, { it.contractBenefitList })
    }

    override fun getActiveContractBenefitsForDuration(
        companyId: Long,
        duration: MonthYearDuration,
    ): List<InsurancePremiumWrapper> {
        val batchTime = System.currentTimeMillis()

        val monthsBetween = DateUtils.generateMonthsBetween(
            duration.from,
            duration.to
        )

        return monthsBetween.map { monthYear ->
            getActiveContractBenefitsForMonthYear(companyId, monthYear, batchTime)
        }.flatten()
    }

    override fun getActiveContractBenefitsForMonthYear(
        companyId: Long,
        monthYear: MonthYear,
        overrideBatchTime: Long?,
    ): List<InsurancePremiumWrapper> {
        try {
            val request = Benefit.GetBenefitForCompaniesInput.newBuilder()
                .addAllCompanyIds(listOf(companyId))
                .setMonth(monthYear.month)
                .setYear(monthYear.year)
                .addAllContractStatuses(setOf(ContractStatus.ACTIVE))
                .build()

            val response = stub.getContractBenefitsByCompanyIds(request)

            val benefits =
                response.companyBenefitMapList.first { it.companyId == companyId }.contractBenefitList

            return if (benefits.isEmpty()) emptyList()
            else {
                contractBenefitMapper.toInsurancePremiums(companyId, benefits, overrideBatchTime)
            }
        } catch (e: StatusRuntimeException) {
            if (e.status.code === Status.NOT_FOUND.code) {
                throw EntityNotFoundException(
                    "No contract benefits found for the company id = $companyId," +
                            " year = ${monthYear.year}, month = ${monthYear.month}"
                )
            } else {
                throw e
            }
        }
    }
}