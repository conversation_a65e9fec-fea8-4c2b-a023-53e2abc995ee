package com.multiplier.core.payable.config

import com.multiplier.common.DateScalar
import com.multiplier.common.DateTimeScalar
import com.multiplier.common.IdScalar
import com.multiplier.common.TimeScalar
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsRuntimeWiring
import graphql.scalars.id.UUIDScalar
import graphql.scalars.`object`.JsonScalar
import graphql.schema.GraphQLScalarType
import graphql.schema.idl.RuntimeWiring

@DgsComponent
class RuntimeScalarWiring {

    private val dateTimeScalar = GraphQLScalarType.newScalar()
        .name("DateTime")
        .description("DateTime type")
        .coercing(DateTimeScalar())
        .build()

    private val idScalar = GraphQLScalarType.newScalar()
        .name("ID")
        .description("ID type")
        .coercing(IdScalar())
        .build()

    private val dateScalar = GraphQLScalarType.newScalar()
        .name("Date")
        .description("Date Type")
        .coercing(DateScalar())
        .build()

    private val timeScalar = GraphQLScalarType.newScalar()
        .name("Time")
        .description("Time Type")
        .coercing(TimeScalar())
        .build()

    private val jsonScalar = GraphQLScalarType.newScalar()
        .name("JSON")
        .description("JSON Type")
        .coercing(JsonScalar.INSTANCE.coercing)
        .build()

    @DgsRuntimeWiring
    fun addScalar(builder: RuntimeWiring.Builder): RuntimeWiring.Builder {
        return builder.scalar(dateTimeScalar)
            .scalar(dateScalar)
            .scalar(idScalar)
            .scalar(timeScalar)
            .scalar(jsonScalar)
            .scalar(UUIDScalar.INSTANCE)
    }
}