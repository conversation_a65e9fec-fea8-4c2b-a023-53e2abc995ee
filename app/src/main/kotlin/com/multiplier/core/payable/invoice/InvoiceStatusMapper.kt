package com.multiplier.core.payable.invoice

import com.multiplier.core.payable.service.exception.ValidationException
import com.multiplier.payable.grpc.schema.GrpcInvoiceStatus
import com.multiplier.payable.types.InvoiceStatus
import java.util.stream.Collectors

fun mapInvoiceStatuesFromGrpcStatuses(grpcInvoiceStatuses: List<GrpcInvoiceStatus>): Set<InvoiceStatus> {
    return grpcInvoiceStatuses.stream()
        .map { mapInvoiceStatusFromGrpcInvoiceStatus(it) }
        .collect(Collectors.toSet())
}

fun InvoiceStatus.mapGrpcInvoiceStatusFromInvoiceStatus(): GrpcInvoiceStatus {
    return when (this) {
        InvoiceStatus.PAID -> GrpcInvoiceStatus.INVOICE_STATUS_PAID
        InvoiceStatus.VOIDED -> GrpcInvoiceStatus.INVOICE_STATUS_VOIDED
        InvoiceStatus.DRAFT -> GrpcInvoiceStatus.INVOICE_STATUS_DRAFT
        InvoiceStatus.DELETED -> GrpcInvoiceStatus.INVOICE_STATUS_DELETED
        InvoiceStatus.AUTHORIZED -> GrpcInvoiceStatus.INVOICE_STATUS_AUTHORIZED
        else -> throw ValidationException("No corresponding grpc status for invoice status = $this")
    }
}

private fun mapInvoiceStatusFromGrpcInvoiceStatus(grpcInvoiceStatus: GrpcInvoiceStatus): InvoiceStatus {
    return when (grpcInvoiceStatus) {
        GrpcInvoiceStatus.INVOICE_STATUS_PAID -> InvoiceStatus.PAID
        GrpcInvoiceStatus.INVOICE_STATUS_VOIDED -> InvoiceStatus.VOIDED
        GrpcInvoiceStatus.INVOICE_STATUS_DELETED -> InvoiceStatus.DELETED
        GrpcInvoiceStatus.INVOICE_STATUS_AUTHORIZED -> InvoiceStatus.AUTHORIZED
        GrpcInvoiceStatus.INVOICE_STATUS_DRAFT -> InvoiceStatus.DRAFT
        else -> throw ValidationException("No corresponding domain invoice status for grpcInvoiceStatus = $grpcInvoiceStatus")
    }
}