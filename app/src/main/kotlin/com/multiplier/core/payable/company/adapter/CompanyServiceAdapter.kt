package com.multiplier.core.payable.company.adapter

import com.multiplier.common.exception.toBusinessException
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass.CreateOrUpdateDraftMsaAddendumRequest
import com.multiplier.company.schema.grpc.CompanyServiceGrpc
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.core.payable.company.Company
import com.multiplier.core.payable.company.CompanyUser
import com.multiplier.core.payable.company.mapper.CompanyFromGrpcMapper
import com.multiplier.core.payable.company.mapper.GrpcPricingInputFromGraphMapper
import com.multiplier.core.payable.pricing.DiscountType
import com.multiplier.core.payable.service.CompanyGraphToServiceMapper
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.DiscountTerm
import com.multiplier.payable.types.PricingInput
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.LocalDate

interface CompanyServiceAdapter {
    fun recordMSADataChanged(
        companyId: Long,
        hasDataChanged: Boolean,
    )

    fun getReadableDiscount(discountTerm: DiscountTerm): String?

    fun getCompanyById(id: Long): Company

    fun getCompanyPrimaryEntityCountryCodeOrThrow(id: Long): CountryCode

    fun getCompanyByIdWithoutException(id: Long): Company?

    fun getCompanies(ids: Set<Long?>?): List<Company?>?

    fun getActiveCompanies(): List<Company>

    fun getActiveCompaniesByIds(companyIds: Collection<Long>): List<Company>

    fun getCompanyAdmins(companyId: Long): List<CompanyUser>

    fun isTestCompany(companyId: Long): Boolean

    fun getAllCompanyBillingContactsOrderByLastBillingRoleUpdateDesc(companyId: Long): List<CompanyUser?>?

    fun getPayrollCutoffDate(): Int

    fun getAllCompanyAdmins(companyId: Long?): List<CompanyUser>

    fun isMsaAddendumFeatureEnabled(companyId: Long): Boolean

    fun createOrUpdateDraftMsaAddendum(
        companyId: Long,
        pricingInput: PricingInput,
    )

    fun getCompanyMsaSignedDate(companyId: Long): LocalDate

    fun getLegalEntityNames(companyId: Long): Map<Long, String>
}

@Service
class DefaultCompanyServiceAdapter : CompanyServiceAdapter {
    @GrpcClient("company-service")
    lateinit var stub: CompanyServiceGrpc.CompanyServiceBlockingStub

    @Autowired
    lateinit var grpcPricingInputFromGraphMapper: GrpcPricingInputFromGraphMapper

    @Autowired
    lateinit var companyFromGrpcMapper: CompanyFromGrpcMapper

    @Autowired
    lateinit var companyGraphToServiceMapper: CompanyGraphToServiceMapper

    private val log = org.slf4j.LoggerFactory.getLogger(DefaultCompanyServiceAdapter::class.java)

    override fun recordMSADataChanged(
        companyId: Long,
        hasDataChanged: Boolean,
    ) {
        val request =
            com.multiplier.company.schema.grpc.CompanyOuterClass.RecordMSADataChangedRequest
                .newBuilder()
                .setCompanyId(companyId)
                .setHasDataChanged(hasDataChanged)
                .build()

        stub.recordMSADataChanged(request)
    }

    // TODO: extract a mapper of this out in mapstruct for this one (not do mapping here)
    override fun getReadableDiscount(discountTerm: DiscountTerm): String? {
        val requestBuilder = CompanyOuterClass.GetReadableDiscountRequest.newBuilder()
        if (discountTerm.id != null) {
            requestBuilder.setId(discountTerm.id)
        }
        requestBuilder.setDiscount(discountTerm.discount)
        if (discountTerm.discountType != null) {
            requestBuilder.setDiscountType(
                grpcPricingInputFromGraphMapper.mapDiscountType(
                    DiscountType.valueOf(
                        discountTerm.discountType.name,
                    ),
                ),
            )
        }
        if (discountTerm.discountRules != null) {
            requestBuilder.addAllDiscountRules(
                discountTerm.discountRules.map { discountRule ->
                    grpcPricingInputFromGraphMapper.mapToDiscountRuleUsingDiscountRule(
                        companyGraphToServiceMapper.mapToDiscountRule(discountRule),
                    )
                },
            )
        }
        return stub.getReadableDiscount(requestBuilder.build()).value
    }

    override fun getCompanyPrimaryEntityCountryCodeOrThrow(id: Long): CountryCode {
        val company = getCompanyById(id)
        requireNotNull(company.primaryEntity?.address?.country) {
            throw PayableErrorCode.INVALID_COMPANY_PRIMARY_ENTITY.toBusinessException("Primary entity country code must not be null") }
        return company.primaryEntity.address.country
    }

    override fun getCompanyById(id: Long): Company {
        val request =
            CompanyOuterClass.GetCompanyByIdRequest.newBuilder()
                .setId(id)
                .build()
        val resp = stub.getCompanyById(request)
        return this.companyFromGrpcMapper.map(resp)
    }

    override fun getCompanyByIdWithoutException(id: Long): Company? {
        val request =
            CompanyOuterClass.GetCompanyByIdWithoutExceptionRequest.newBuilder()
                .setId(id)
                .build()
        val resp = stub.getCompanyByIdWithoutException(request)
        return this.companyFromGrpcMapper.map(resp)
    }

    override fun getCompanies(ids: Set<Long?>?): List<Company?>? {
        val request =
            CompanyOuterClass.GetCompaniesRequest.newBuilder()
                .addAllIds(ids)
                .build()
        val resp = stub.getCompanies(request)
        return resp.companiesList.map { companyOuter -> this.companyFromGrpcMapper.map(companyOuter) }
    }

    override fun getActiveCompanies(): List<Company> {
        val resp = stub.getActiveCompanies(com.google.protobuf.Empty.newBuilder().build())
        return resp.companiesList.map { companyOuter -> this.companyFromGrpcMapper.map(companyOuter) }
    }

    override fun getActiveCompaniesByIds(companyIds: Collection<Long>): List<Company> {
        val request =
            CompanyOuterClass.GetActiveCompaniesByIdsRequest.newBuilder()
                .addAllIds(companyIds)
                .build()
        val resp = stub.getActiveCompaniesByIds(request)
        return resp.companiesList.map { companyOuter -> this.companyFromGrpcMapper.map(companyOuter) }
    }

    override fun getCompanyAdmins(companyId: Long): List<CompanyUser> {
        val request =
            CompanyOuterClass.GetCompanyRequest.newBuilder()
                .setId(companyId)
                .build()
        val resp = stub.getCompanyAdmins(request)
        return resp.usersList.map { userOuter -> companyFromGrpcMapper.mapCompanyUser(userOuter) }
    }

    override fun isTestCompany(companyId: Long): Boolean {
        val request =
            CompanyOuterClass.GetCompanyRequest.newBuilder()
                .setId(companyId)
                .build()
        val resp = stub.isTestCompany(request)
        return resp.isTest
    }

    override fun getAllCompanyBillingContactsOrderByLastBillingRoleUpdateDesc(companyId: Long): List<CompanyUser?>? {
        val request =
            CompanyOuterClass.GetCompanyRequest.newBuilder()
                .setId(companyId)
                .build()
        val resp = stub.getAllCompanyBillingContactsOrderByLastBillingRoleUpdateDesc(request)
        return resp.usersList.map { userOuter -> companyFromGrpcMapper.mapCompanyUser(userOuter) }
    }

    override fun getPayrollCutoffDate(): Int {
        val resp = stub.getPayrollCutoffDate(com.google.protobuf.Empty.newBuilder().build())
        return resp.value
    }

    override fun getAllCompanyAdmins(companyId: Long?): List<CompanyUser> {
        val request =
            companyId?.let { nonNullCompanyId ->
                CompanyOuterClass.GetCompanyRequest.newBuilder().setId(nonNullCompanyId).build()
            }
        val resp = stub.getAllCompanyAdmins(request)
        return resp.usersList.map { userOuter -> companyFromGrpcMapper.mapCompanyUser(userOuter) }
    }

    override fun isMsaAddendumFeatureEnabled(companyId: Long): Boolean {
        val request =
            CompanyOuterClass.IsMsaAddendumFeatureEnabledRequest.newBuilder()
                .setCompanyId(companyId)
                .build()
        return stub.isMsaAddendumFeatureEnabled(request).enabled
    }

    override fun createOrUpdateDraftMsaAddendum(
        companyId: Long,
        pricingInput: PricingInput,
    ) {
        val requestBuilder = CreateOrUpdateDraftMsaAddendumRequest.newBuilder()

        requestBuilder.setCompanyId(companyId)
        try {
            requestBuilder.setPricingInput(grpcPricingInputFromGraphMapper.map(pricingInput))
        } catch (e: IllegalArgumentException) {
            log.error("There's no corresponding currency code for ${pricingInput.billingCurrencyCode}")
            throw e
        }
        val request = requestBuilder.build()
        stub.createOrUpdateDraftMsaAddendum(request)
    }

    override fun getCompanyMsaSignedDate(companyId: Long): LocalDate {
        val request =
            CompanyOuterClass.GetCompanyMsaSignedDateInput.newBuilder()
                .setCompanyId(companyId)
                .build()
        val result = stub.getCompanyMsaSignedDate(request)
        return companyFromGrpcMapper.mapLocalDate(result.date)
    }

    override fun getLegalEntityNames(companyId: Long): Map<Long, String> {
        val request = CompanyOuterClass.GetLegalEntitiesRequest.newBuilder().addCompanyIds(companyId).build()
        val result = stub.getLegalEntities(request)


        return companyFromGrpcMapper.mapLegalEntitiesNames(result.entitiesList)
    }
}
