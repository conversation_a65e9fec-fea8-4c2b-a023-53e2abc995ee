package com.multiplier.core.payable.report

import com.multiplier.core.payable.adapters.CompanyBindingAdapter
import com.multiplier.core.payable.company.adapter.CompanyServiceAdapter
import com.multiplier.core.payable.report.composition.InvoiceSourceReportCompositionService
import com.multiplier.core.payable.report.dataholder.IsrDataHolder
import com.multiplier.core.payable.report.exception.IsrGenerationException
import com.multiplier.core.payable.service.dataholder.ISRFileMetaData
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.reconciler.data.invoice.InvoiceDataProvider
import com.multiplier.payable.engine.splitter.context.SplitterContext
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.data.CompanyPayableDataProvider
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import mu.KotlinLogging
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Component

@Component
class InvoiceSourceReportGenerationDataProvider(
    private val companyPayableDataProvider: CompanyPayableDataProvider,
    private val companyBindingAdapter: CompanyBindingAdapter,
    private val companyServiceAdapter: CompanyServiceAdapter,
    private val invoiceSourceReportCompositionService: InvoiceSourceReportCompositionService,
    private val transactionTemplateProvider: TransactionTemplateProvider,
    private val isrExternalReferenceDataProvider: IsrExternalReferenceDataProvider,
    private val secondInvoiceDataProvider: InvoiceDataProvider,
) {
    companion object {
        private val log = KotlinLogging.logger {}
    }

    fun createInvoiceSourceReportData(command: InvoiceCommand): List<IsrDataHolder> {
        log.info { "Generating ISR data for command=[$command]" }
        val transactionId = command.transactionId

        val existingSecondPayables = secondInvoiceDataProvider.fetchActiveInvoices(command)
        val companyPayables = companyPayableDataProvider.findByTransactionId(transactionId).sortedBy { it.id }
        log.info { "Found [${existingSecondPayables.size}] existing second payables and [${companyPayables.size}] company payables for ISR data generation, transactionId=[$transactionId] " }
        val existingPayableSize = existingSecondPayables.size

        val customerId =
            companyBindingAdapter.getCustomerIdForCompanyId(command.companyId)
                ?: throw IllegalStateException("No customer found for companyId = ${command.companyId}")

        return companyPayables.mapIndexed { index, companyPayable ->
            val indexConsideredExistingPayables = if (existingPayableSize > 0) index + existingPayableSize else 0
            log.info { "Generating ISR data for company payable=[$companyPayable], index=[$indexConsideredExistingPayables]" }

            buildSingleISRData(
                payable = companyPayable,
                command = command,
                customerId = customerId,
                index = indexConsideredExistingPayables,
            )
        }
    }

    private fun buildSingleISRData(
        payable: CompanyPayable,
        command: InvoiceCommand,
        customerId: String?,
        index: Int,
    ): IsrDataHolder {
        val companyPayableId = payable.id
        val companyId = command.companyId
        val template =
            transactionTemplateProvider.findTemplateFor(
                command.transactionType,
                command.companyId,
            )
        log.info {
            "Found selectors=[${template.itemSplitter.selectors}] for template identifier=[${template.identifier}] to build single ISR data, transactionId=[${command.transactionId}]"
        }

        val isrMetadata = isrMetadata(command, payable, template, index)
        val externalReference = isrExternalReferenceDataProvider.find(companyPayableId)

        return IsrDataHolder
            .builder()
            .companyId(companyId)
            .customerId(customerId)
            .isrFileMetaData(isrMetadata)
            .externalId(externalReference.externalId)
            .recordType(externalReference.recordType)
            .build()
    }

    private fun isrMetadata(
        command: InvoiceCommand,
        payable: CompanyPayable,
        template: TransactionTemplate,
        index: Int,
    ): ISRFileMetaData {
        val companyId = command.companyId
        val companyPayableId = payable.id
        val displayName = companyServiceAdapter.getCompanyById(companyId).displayName

        val fileSuffixes = getFileSuffixes(command, payable, template, index)
        log.info { "Generated file suffixes=[$fileSuffixes], transactionId=[${command.transactionId}]" }

        if (payable.month == null || payable.year == null) {
            throw IsrGenerationException(
                companyId,
                null,
                null,
                "Month and year must be set for company payable with id=[${payable.id}]",
            )
        }

        val isrMetadata =
            ISRFileMetaData
                .builder()
                .companyId(companyId)
                .companyDisplayName(displayName)
                .month(payable.month)
                .year(payable.year)
                .companyPayableId(companyPayableId)
                .fileSuffixes(fileSuffixes)
                .build()

        return invoiceSourceReportCompositionService
            .populateAuthCodeAndEmailLink(listOf(isrMetadata))
            .iterator()
            .next()
    }

    private fun getFileSuffixes(
        command: InvoiceCommand,
        payable: CompanyPayable,
        template: TransactionTemplate,
        index: Int,
    ): List<String> {
        log.info { "Calculate file suffixes for command=[$command]" }
        if (CollectionUtils.isEmpty(template.itemSplitter.selectors)) {
            log.info {
                "There's no selectors defined for template identifier=[${template.identifier}], transactionId=[${command.transactionId}]. Skipping suffixes retrieval"
            }
            if (index > 0) {
                return listOf(index.toString())
            }
            return emptyList()
        }

        val selectors = template.itemSplitter.selectors
        log.info {
            "Retrieved selectors=[$selectors] for template identifier=[${template.identifier}], transactionId=[${command.transactionId}]"
        }

        val splitterContext =
            SplitterContext(
                transactionId = command.transactionId,
                payableItems = payable.items,
            )
        var enrichedContext = splitterContext
        selectors
            .forEach { enrichedContext = it.contextEnricher.enrich(enrichedContext) }
        val item = payable.items.first { it.contractId != null && it.contractId != -1L }

        var fileSuffixes = selectors.mapNotNull { it.selectAndTranslate()(item, enrichedContext) }
        if (index > 0) {
            fileSuffixes = fileSuffixes + index.toString()
        }
        return fileSuffixes
    }
}
