package com.multiplier.core.payable.adapters

import com.multiplier.payable.types.ContractType
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import java.time.LocalDate

data class GenerateDepositForContractInput(
    val contractId: Long,
    val companyId: Long,
    val memberId: Long,
    val contractType: ContractType,
    val countryCode: CountryCode,
    val currencyCode: CurrencyCode,
    val startOn: LocalDate,
    val depositDueDate: LocalDate? = null,
)