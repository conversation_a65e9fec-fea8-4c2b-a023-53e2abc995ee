package com.multiplier.core.payable.calculator

import com.google.type.Date
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.performance.PerformanceReviewOuterClass.SalaryReviewDto
import com.multiplier.core.payable.adapters.PerformanceReviewServiceAdapter
import com.multiplier.core.payable.service.exception.ValidationException
import com.multiplier.core.toLocalDate
import com.multiplier.payable.types.MonthYearInput
import jakarta.validation.constraints.NotNull
import lombok.RequiredArgsConstructor
import lombok.extern.slf4j.Slf4j
import mu.KotlinLogging
import org.apache.commons.lang3.tuple.Pair
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode
import java.time.LocalDate
import java.time.YearMonth
import java.util.*
import java.util.stream.Collectors
import kotlin.math.min

private val logger = KotlinLogging.logger {  }

@Service
@RequiredArgsConstructor
@Slf4j
class PayableCompensationCalculator {

    private var performanceReviewServiceAdapter: PerformanceReviewServiceAdapter? = null

    @Autowired
    fun setPerformanceReviewServiceAdapter(@Lazy performanceReviewServiceAdapter: PerformanceReviewServiceAdapter) {
        this.performanceReviewServiceAdapter = performanceReviewServiceAdapter
    }

    fun calculateBasePayMonthlyAmount(
        basePayComponent: CompensationOuterClass.CompensationPayComponent,
        contract: ContractOuterClass.Contract,
        monthYearInput: MonthYearInput?,
    ): Double {
        val divisionFactor: BigDecimal = getFactorBasedOnFrequencyOfCompensation(basePayComponent.frequency)
        val rateType = basePayComponent.rateType

        if ((rateType != CompensationOuterClass.RateType.CTC &&
                    rateType != CompensationOuterClass.RateType.GROSS) || (basePayComponent.name != "basePay")) {
            throw ValidationException("The base pay amount calculation failed for CompensationPayComponent as $basePayComponent")
        }

        var revisedPayAmount: BigDecimal? = BigDecimal(0)

        val startDateAndEndDateForInvoicing: Pair<Int, Int> = getStartAndEndOfInvoicingMonthForContract(contract, monthYearInput)
        val workedDays = startDateAndEndDateForInvoicing.right - startDateAndEndDateForInvoicing.left + 1
        //adding 1 to include the value of the day

        //we are not considering hourly pay frequencies.
        if (basePayComponent.frequency != CompensationOuterClass.RateFrequency.HOURLY) {
            revisedPayAmount = calculateRevisedPayAmount(contract.id,
                basePayComponent.amount,
                divisionFactor,
                monthYearInput,
                startDateAndEndDateForInvoicing.right)
            logger.debug { "Calculated revisedPayAmount $revisedPayAmount for ${basePayComponent.frequency} frequency"}
        }

        return BigDecimal.valueOf(basePayComponent.amount)
            .divide(divisionFactor, 2, RoundingMode.HALF_EVEN)
            .divide(BigDecimal.valueOf(30), MathContext.DECIMAL64) //makes it daily
            .multiply(BigDecimal.valueOf(workedDays.toLong()))
            .add(revisedPayAmount)
            .setScale(2, RoundingMode.HALF_EVEN)
            .toDouble()
    }

    private fun calculateRevisedPayAmount(
        id: Long,
        amount: Double,
        divisionFactor: BigDecimal,
        monthYearInput: MonthYearInput?,
        right: Int
    ): BigDecimal {
        val salaryReviews: List<Pair<LocalDate, Double>> =
            performanceReviewServiceAdapter!!.getApprovedSalaryReviewsForContract(id)
                .stream()
                .map<Pair<LocalDate, Double>> { performanceReviewPair: SalaryReviewDto ->
                    Pair.of(
                        mapToLocalDate(performanceReviewPair.effectiveDate),
                        performanceReviewPair.amount
                    )
                }
                .collect(Collectors.toList())

        if (salaryReviews.isNotEmpty()) {
            return getRevisedPayment(salaryReviews, amount, divisionFactor, monthYearInput, right)
        }

        return BigDecimal(0);
    }

    private fun getFactorBasedOnFrequencyOfCompensation(rateFrequency: CompensationOuterClass.RateFrequency): BigDecimal {
        return when (rateFrequency) {
            CompensationOuterClass.RateFrequency.MONTHLY -> BigDecimal(1)
            CompensationOuterClass.RateFrequency.ANNUALLY -> BigDecimal(12)
            CompensationOuterClass.RateFrequency.HALFYEARLY -> BigDecimal(6)
            CompensationOuterClass.RateFrequency.TRI_ANNUALLY -> BigDecimal(4)
            CompensationOuterClass.RateFrequency.QUATERLY -> BigDecimal(3)
            CompensationOuterClass.RateFrequency.BI_MONTHLY -> BigDecimal(2)
            CompensationOuterClass.RateFrequency.SEMIMONTHLY -> BigDecimal(1).divide(
                BigDecimal(2),
                MathContext.DECIMAL64
            ) //effective multiplication
            CompensationOuterClass.RateFrequency.WEEKLY -> BigDecimal(1).divide(
                BigDecimal(4),
                MathContext.DECIMAL64
            ) //effective multiplication
            CompensationOuterClass.RateFrequency.DAILY -> BigDecimal(1).divide(
                BigDecimal(30),
                MathContext.DECIMAL64
            ) //effective multiplication
            CompensationOuterClass.RateFrequency.HOURLY -> BigDecimal(1).divide(BigDecimal(160))
            else -> throw ValidationException("The rate frequency is not supported by base pay caluclation $rateFrequency")
        }
    }

    fun getStartAndEndOfInvoicingMonthForContract(
        contract: ContractOuterClass.Contract,
        monthYearInput: @NotNull MonthYearInput?,
    ): Pair<Int, Int> {
        val yearMonth = YearMonth.of(monthYearInput!!.year, monthYearInput!!.month)
        var startDate = 1
        var endDate = getNumberOfDaysInMonthYear(monthYearInput)

        val startedOn = if ((!contract.hasStartOn())) null else contract.startOn.toLocalDate()
        val endedOn = if ((!contract.hasEndedOn())) null else contract.endedOn.toLocalDate()
        val endOn = if ((!contract.hasEndOn())) null else contract.endOn.toLocalDate()

        // if contract has no start or end date.
        if (Objects.isNull(endedOn) && Objects.isNull(startedOn)) {
            logger.debug { "calculated startDate: $startDate endDate: $endDate" }
            return Pair.of(startDate, endDate)
        }

        if (!Objects.isNull(startedOn) && startedOn!!.isAfter(LocalDate.of(yearMonth.year, yearMonth.month, 1))) {
            startDate = startedOn.dayOfMonth
        }

        if ((!Objects.isNull(endedOn) && endedOn!!.isBefore(LocalDate.of(yearMonth.year, yearMonth.month, yearMonth.lengthOfMonth())))) {
            endDate = endedOn.dayOfMonth
        }

        if (!Objects.isNull(endOn) && endOn!!.isBefore(LocalDate.of(yearMonth.year, yearMonth.month, yearMonth.lengthOfMonth()))) {
            endDate = min(endDate.toDouble(), endOn.dayOfMonth.toDouble()).toInt()
        }

        if (startDate >= endDate) {
            startDate = endDate
        }

        logger.debug { "calculated startDate: $startDate endDate: $endDate" }
        return Pair.of(startDate, endDate)
    }

    private fun mapToLocalDate(date: Date): LocalDate {
        return LocalDate.of(date.year, date.month, date.day)
    }

    fun getRevisedPayment(
        salaryReviews: List<Pair<LocalDate, Double>>,
        baseAmount: Double,
        divisionFactor: BigDecimal?,
        monthYearInput: MonthYearInput?,
        endDateForInvoicing: Int,
    ): BigDecimal {
        var newBasePay = 0.0
        var effectedDays = 0
        if (salaryReviews.isEmpty()) {
            return BigDecimal(0)
        }

        for (salaryReview in salaryReviews) {
            val effectiveDate = salaryReview.left
            if (effectiveDate.year == monthYearInput!!.year &&
                effectiveDate.month.value == monthYearInput.month &&
                effectiveDate.dayOfMonth <= endDateForInvoicing) {
                newBasePay = baseAmount + ((salaryReview.right / 100) * baseAmount)
                effectedDays = (endDateForInvoicing - salaryReview.left.dayOfMonth) + 1 //adding 1 to include the performace review date as well
                break
            }
        }
        val monthlyDifference = BigDecimal.valueOf(newBasePay - baseAmount).divide(divisionFactor, 2, RoundingMode.HALF_EVEN)

        return monthlyDifference.multiply(BigDecimal.valueOf(effectedDays.toLong()))
            .divide(BigDecimal.valueOf(getNumberOfDaysInMonthYear(monthYearInput).toLong()), 2, RoundingMode.HALF_EVEN)
    }

    // TODO - implement proper month year calculation
    fun getNumberOfDaysInMonthYear(monthYearInput: MonthYearInput?): Int {
        return 30
    }
}