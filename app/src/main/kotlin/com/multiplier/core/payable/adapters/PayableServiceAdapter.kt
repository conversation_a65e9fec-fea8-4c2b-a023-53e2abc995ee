package com.multiplier.core.payable.adapters

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.company.ManagementFee
import com.multiplier.core.payable.freelancerinvoice.MemberPayableExternalInvoiceService
import com.multiplier.core.payable.invoice.database.InvoiceFilters
import com.multiplier.core.payable.invoice.database.InvoiceReadService
import com.multiplier.core.payable.invoice.database.InvoiceService
import com.multiplier.core.payable.pricing.adapter.PricingServiceAdapter
import com.multiplier.core.payable.repository.model.InvoiceType
import com.multiplier.core.payable.service.ContractDepositPayableService
import com.multiplier.core.payable.service.InvoiceDocumentService
import com.multiplier.core.payable.service.InvoiceGenerateService
import com.multiplier.core.payable.service.PayableService
import com.multiplier.core.payable.service.exception.ValidationException
import com.multiplier.payable.grpc.schema.ExternalInvoiceAddPaymentResponse
import com.multiplier.payable.grpc.schema.ExternalInvoiceCreationResponse
import com.multiplier.payable.grpc.schema.FreelancerInvoiceLineItem
import com.multiplier.payable.grpc.schema.IsDepositInvoicePaidResponse
import com.multiplier.payable.types.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.*

interface PayableServiceAdapter {
    fun generateDepositForContract(
        input: GenerateDepositForContractInput
    ): CompanyPayable?

    fun deleteOrVoidContractDepositPayables(contractId: Long?)

    fun sendCompanyPayableToContact(contractId: Long?)

    fun getCompanyPayableById(id: Long?): CompanyPayable?

    fun getCompanyPayablesByIds(ids: Set<Long>?): List<CompanyPayable>?

    fun bulkUpdateCompanyPayableStatus(
        ids: Set<Long>?,
        status: PayableStatus?,
    )

    fun getBillingCurrencyCode(companyId: Long?): CurrencyCode?

    fun getCompanyManagementFees(
        companyId: Long?,
        contractId: List<Long>?,
        monthYear: MonthYear?,
        isForced: Boolean = false,
    ): List<ManagementFee>?

    fun getCompanyManagementFeesEstimation(
        companyId: Long?,
        contractIds: List<Long>,
        refMonthYear: MonthYear?,
    ) : List<ManagementFee>?

    fun getManagementFeeByCountryAndContractType(
        companyId: Long?,
        countryCode: CountryCode?,
        contractType: ContractType?,
    ): Double?

    fun bulkUpdateCompanyPayableTotalAmount(companyPayableIdToTotalAmount: Map<Long, Double>?)

    fun createExternalInvoiceForMemberPayables(
        lineItems: List<FreelancerInvoiceLineItem>?,
        companyId: Long?,
    ): ExternalInvoiceCreationResponse

    fun deleteOrVoidExternalInvoice(invoiceId: String?)

    fun addFullPaymentToExternalInvoices(
        companyPayableIds: Set<Long>?,
        paidToAccountCode: String?,
        paymentDateInSingaporeTimezone: Calendar?,
    ): ExternalInvoiceAddPaymentResponse

    fun updateExternalInvoiceProcessingFee(
        invoiceId: String?,
        billingCurrency: String?,
        processingFeeLineItem: FreelancerInvoiceLineItem?,
    )

    fun getInvoiceLink(invoiceId: String): String?

    /**
     * Get external invoice PDF for a given invoice ID
     * @param invoiceId invoice ID
     * @return invoice's external PDF file data in Base-64
     */
    fun getInvoiceFile(invoiceId: String): String

    fun getIsDepositPaidForContractId(contractId: Long): IsDepositInvoicePaidResponse

    fun getPricing(companyId: Long?): Pricing?

    fun getInvoicesByFilters(invoiceFilters: InvoiceFilters): List<InvoiceDTO>

    /**
     * Only to void or delete the freelancer company payables
     */
    fun voidFreelancerCompanyPayable(flCompanyPayableID: Long)

    fun getInvoicesByIds(invoiceIds: List<Long>): Map<Long, InvoiceDTO>

    fun getInvoiceByIdsWithErrorHandling(invoiceIds: List<Long>): Map<Long, InvoiceDTO>
}

@Service
class DefaultPayableServiceAdapter : PayableServiceAdapter {

    @Autowired
    private lateinit var invoiceService: InvoiceService

    @Autowired
    private lateinit var pricingServiceAdapter: PricingServiceAdapter

    @Autowired
    private lateinit var payableService: PayableService

    @Autowired
    private lateinit var contractDepositPayableService: ContractDepositPayableService

    @Autowired
    private lateinit var memberPayableExternalInvoiceService: MemberPayableExternalInvoiceService

    @Autowired
    private lateinit var invoiceGenerateService: InvoiceGenerateService

    @Autowired
    private lateinit var invoiceDocumentService: InvoiceDocumentService

    @Autowired
    private lateinit var currentUser: CurrentUser

    @Autowired
    private lateinit var invoiceReadService: InvoiceReadService

    override fun generateDepositForContract(
        input: GenerateDepositForContractInput
    ): CompanyPayable? {
        return contractDepositPayableService.generateDepositForContract(input)
    }

    override fun deleteOrVoidContractDepositPayables(contractId: Long?) {
        contractDepositPayableService.deleteOrVoidContractDepositPayables(contractId)
    }

    override fun sendCompanyPayableToContact(contractId: Long?) {
        contractDepositPayableService.sendAllContractDepositInvoicesToContactEmail(contractId)
    }

    override fun getCompanyPayableById(id: Long?): CompanyPayable? {
        return payableService.getCompanyPayableById(id)
    }

    override fun getCompanyPayablesByIds(ids: Set<Long>?): List<CompanyPayable>? {
        return payableService.getCompanyPayablesByIds(ids)
    }

    override fun bulkUpdateCompanyPayableStatus(
        ids: Set<Long>?,
        status: PayableStatus?,
    ) {
        payableService.bulkUpdateCompanyPayableStatus(ids, status)
    }

    override fun getBillingCurrencyCode(companyId: Long?): CurrencyCode? {
        return pricingServiceAdapter.getBillingCurrencyCode(companyId)
    }

    override fun getCompanyManagementFees(
        companyId: Long?,
        contractId: List<Long>?,
        monthYear: MonthYear?,
        isForced: Boolean,
    ): List<ManagementFee>? {
        return pricingServiceAdapter.getCompanyManagementFees(companyId, contractId, monthYear, isForced)
    }

    override fun getCompanyManagementFeesEstimation(
        companyId: Long?,
        contractIds: List<Long>,
        refMonthYear: MonthYear?,
    ): List<ManagementFee>? {
        return pricingServiceAdapter.getManagementFeeEstimation(companyId, contractIds, refMonthYear)
    }

    override fun getManagementFeeByCountryAndContractType(
        companyId: Long?,
        countryCode: CountryCode?,
        contractType: ContractType?,
    ): Double? {
        return pricingServiceAdapter.getManagementFeeByCountryAndContractType(companyId, countryCode, contractType)
    }

    override fun createExternalInvoiceForMemberPayables(
        lineItems: List<FreelancerInvoiceLineItem>?,
        companyId: Long?,
    ): ExternalInvoiceCreationResponse {
        if (lineItems?.isNotEmpty() != true) {
            throw ValidationException("Line items are empty to create an external invoice")
        }

        val companyIdNonNull =
            companyId ?: currentUser.context?.scopes?.companyId
                ?: throw ValidationException("Company ID cannot be NULL to create Invoice for Member Payables")

        return memberPayableExternalInvoiceService.createExternalInvoiceForMemberPayables(companyIdNonNull, lineItems)
    }

    override fun deleteOrVoidExternalInvoice(invoiceId: String?) {
        invoiceGenerateService.voidInvoice(invoiceId)
    }

    override fun updateExternalInvoiceProcessingFee(
        invoiceId: String?,
        billingCurrency: String?,
        processingFeeLineItem: FreelancerInvoiceLineItem?,
    ) {
        memberPayableExternalInvoiceService.updateExternalInvoiceProcessingFee(invoiceId, billingCurrency, processingFeeLineItem)
    }

    override fun getInvoiceLink(invoiceId: String): String? {
        return invoiceGenerateService.getInvoiceLink(invoiceId)
    }

    override fun getInvoiceFile(invoiceId: String): String {
        return invoiceDocumentService.getInvoiceFile(invoiceId)
    }

    override fun addFullPaymentToExternalInvoices(
        companyPayableIds: Set<Long>?,
        paidToAccountCode: String?,
        paymentDateInSingaporeTimezone: Calendar?,
    ): ExternalInvoiceAddPaymentResponse {
        return invoiceGenerateService.addFullPaymentToExternalInvoices(companyPayableIds, paidToAccountCode, paymentDateInSingaporeTimezone)
    }

    override fun bulkUpdateCompanyPayableTotalAmount(companyPayableIdToTotalAmount: Map<Long, Double>?) {
        payableService.bulkUpdateCompanyPayableTotalAmount(companyPayableIdToTotalAmount)
    }

    override fun getIsDepositPaidForContractId(contractId: Long): IsDepositInvoicePaidResponse {
        val invoice = invoiceService.getByContractIdAndType(contractId, InvoiceType.DEPOSIT)
        val isDepositPaid = invoice != null && InvoiceStatus.PAID.equals(invoice.status)
        var invoiceId = "NA"
        var invoiceNo = "NA"
        if (invoice != null) {
            invoiceId = invoice.id.toString()
            invoiceNo = invoice.invoiceNo
        }
        return IsDepositInvoicePaidResponse.newBuilder()
            .setIsDepositInvoicePaid(isDepositPaid)
            .setInvoiceId(invoiceId)
            .setInvoiceNo(invoiceNo)
            .build()
    }

    override fun getPricing(companyId: Long?): Pricing? {
        return pricingServiceAdapter.getPricing(companyId)
    }

    override fun getInvoicesByFilters(invoiceFilters: InvoiceFilters): List<InvoiceDTO> {
        return invoiceReadService.getInvoicesByFilters(invoiceFilters)
    }

    override fun voidFreelancerCompanyPayable(flCompanyPayableID: Long) {
        payableService.voidFreelancerCompanyPayable(flCompanyPayableID)
    }

    override fun getInvoicesByIds(invoiceIds: List<Long>): Map<Long, InvoiceDTO> {
        return invoiceReadService.getInvoicesByIds(invoiceIds)
    }

    override fun getInvoiceByIdsWithErrorHandling(invoiceIds: List<Long>): Map<Long, InvoiceDTO> {
        return invoiceReadService.getInvoicesByIdsWithErrorHandling(invoiceIds)
    }
}
