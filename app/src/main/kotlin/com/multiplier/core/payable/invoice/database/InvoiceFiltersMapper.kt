package com.multiplier.core.payable.invoice.database

import com.multiplier.core.payable.invoice.fromGrpcInvoiceTypeToInvoiceType
import com.multiplier.core.payable.invoice.fromGrpcInvoiceTypesToInvoiceTypes
import com.multiplier.core.payable.invoice.mapInvoiceStatuesFromGrpcStatuses
import com.multiplier.core.payable.payment.TimeZoneUtil
import com.multiplier.core.payable.repository.model.InvoiceType
import com.multiplier.payable.grpc.schema.GetInvoicesByFiltersRequest
import java.time.LocalDate

fun fromGrpcInvoiceFilters(grpcInvoiceFilters : GetInvoicesByFiltersRequest) : InvoiceFilters {
    return InvoiceFilters(
        companyId = grpcInvoiceFilters.companyId,
        dueDate = fromDueDate(grpcInvoiceFilters),
        invoiceTypes = fromInvoiceTypeCriteria(grpcInvoiceFilters),
        invoiceStatuses = mapInvoiceStatuesFromGrpcStatuses(grpcInvoiceFilters.statusList),
        exclusionCriteria = fromExclusionCriteria(grpcInvoiceFilters),
        inclusionCriteria = fromInclusionCriteria(grpcInvoiceFilters),
        dueDateRange = fromDueDateRange(grpcInvoiceFilters)
    )
}

fun fromDueDate(grpcInvoiceFilters : GetInvoicesByFiltersRequest) : LocalDate? {
    if (grpcInvoiceFilters.hasDueDateRange()){
        return null
    }
    if (grpcInvoiceFilters.hasDueDate()){
        return TimeZoneUtil.convertTimezoneToLocalDateInUTC(grpcInvoiceFilters.dueDate)
    }
    return null
}

fun fromDueDateRange(grpcInvoiceFilters : GetInvoicesByFiltersRequest) : DateRange? {
    if (grpcInvoiceFilters.hasDueDateRange() && grpcInvoiceFilters.dueDateRange.hasFrom()){
        return DateRange(
            toDate = requireNotNull(TimeZoneUtil.convertTimezoneToLocalDateInUTC(grpcInvoiceFilters.dueDateRange.to)),
            fromDate = TimeZoneUtil.convertTimezoneToLocalDateInUTC(grpcInvoiceFilters.dueDateRange.from),
        )
    }
    if (grpcInvoiceFilters.hasDueDateRange()){
        return DateRange(
            toDate = requireNotNull(TimeZoneUtil.convertTimezoneToLocalDateInUTC(grpcInvoiceFilters.dueDateRange.to)),
            fromDate = null
        )
    }
    return null
}

// we explicitly don't set deprecated type selector if criteria present
fun fromInvoiceTypeCriteria(grpcInvoiceFilters : GetInvoicesByFiltersRequest) : Set<InvoiceType>? {
    if (grpcInvoiceFilters.hasInclusionCriteria() || grpcInvoiceFilters.hasExclusionCriteria()){
        return null
    }
    return fromGrpcInvoiceTypesToInvoiceTypes(grpcInvoiceFilters.invoiceTypeList)
}

fun fromExclusionCriteria(grpcInvoiceFilters : GetInvoicesByFiltersRequest) : InvoiceExcludeCriteria? {
    if (grpcInvoiceFilters.hasExclusionCriteria()){
        return InvoiceExcludeCriteria(grpcInvoiceFilters
            .exclusionCriteria.invoiceTypesList
            .map { fromGrpcInvoiceTypeToInvoiceType(it) }
            .toSet()
        )
    }
    return null
}

fun fromInclusionCriteria(grpcInvoiceFilters : GetInvoicesByFiltersRequest) : InvoiceIncludeCriteria? {
    if (grpcInvoiceFilters.hasInclusionCriteria()){
        return InvoiceIncludeCriteria(grpcInvoiceFilters
            .inclusionCriteria.invoiceTypesList
            .map { fromGrpcInvoiceTypeToInvoiceType(it) }
            .toSet()
        )
    }
    return null
}