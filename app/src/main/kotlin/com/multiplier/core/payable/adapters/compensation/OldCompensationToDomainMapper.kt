package com.multiplier.core.payable.adapters.compensation

import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.payable.types.CurrencyCode
import org.springframework.stereotype.Component

@Component
internal class OldCompensationToDomainMapper {
    fun map(
        compensation: CompensationOuterClass.Compensation,
        contractId: Long,
    ): Compensation = compensation.toDomain(contractId)

    fun CompensationOuterClass.Compensation.toDomain(contractId: Long): Compensation {
        val basePay = this.basePay.toDomain()
        return Compensation(
            contractId = contractId,
            basePay = basePay,
            otherPays =
                this.additionalPaysList
                    .filter { it.currency.isNotBlank() } //Some compensation details have no currency as they're variable and we don't need them
                    .map { it.toDomain() },
            source = CompensationSource.CONTRACT_SERVICE,
        )
    }

    fun CompensationOuterClass.CompensationPayComponent.toDomain(): CompensationPayComponent =
        CompensationPayComponent(
            id = this.id.toString(),
            name = this.name,
            amount = this.amount,
            currencyCode = CurrencyCode.valueOf(this.currency),
            category = mapCategory(this.name),
            frequency = this.frequency.toDomain(),
            payFrequency = this.payFrequency.toDomain(),
            rateType = this.rateType.toDomain(),
        )

    fun CompensationOuterClass.RateFrequency.toDomain(): RateFrequency =
        when (this) {
            CompensationOuterClass.RateFrequency.ANNUALLY -> RateFrequency.ANNUALLY
            CompensationOuterClass.RateFrequency.HALFYEARLY -> RateFrequency.SEMIANNUALLY
            CompensationOuterClass.RateFrequency.QUATERLY -> RateFrequency.QUARTERLY
            CompensationOuterClass.RateFrequency.MONTHLY -> RateFrequency.MONTHLY
            CompensationOuterClass.RateFrequency.SEMIMONTHLY -> RateFrequency.SEMIMONTHLY
            CompensationOuterClass.RateFrequency.BI_WEEKLY -> RateFrequency.BIWEEKLY
            CompensationOuterClass.RateFrequency.WEEKLY -> RateFrequency.WEEKLY
            CompensationOuterClass.RateFrequency.DAILY -> RateFrequency.DAILY
            CompensationOuterClass.RateFrequency.HOURLY -> RateFrequency.HOURLY
            CompensationOuterClass.RateFrequency.ONCE -> RateFrequency.ONETIME
            CompensationOuterClass.RateFrequency.BI_MONTHLY -> RateFrequency.BI_MONTHLY
            CompensationOuterClass.RateFrequency.TRI_ANNUALLY -> RateFrequency.TRI_ANNUALLY
            CompensationOuterClass.RateFrequency.CUSTOM -> RateFrequency.CUSTOM
            else -> throw IllegalArgumentException("Unsupported rateFrequency: $this")
        }

    fun CompensationOuterClass.PayFrequency.toDomain(): PayFrequency =
        when (this) {
            CompensationOuterClass.PayFrequency.PAY_FREQUENCY_MONTHLY -> PayFrequency.MONTHLY
            CompensationOuterClass.PayFrequency.PAY_FREQUENCY_SEMIMONTHLY -> PayFrequency.SEMI_MONTHLY
            CompensationOuterClass.PayFrequency.PAY_FREQUENCY_WEEKLY -> PayFrequency.WEEKLY
            CompensationOuterClass.PayFrequency.PAY_FREQUENCY_BIWEEKLY -> PayFrequency.BI_WEEKLY
            CompensationOuterClass.PayFrequency.PAY_FREQUENCY_NULL -> PayFrequency.NOT_SET
            else -> throw IllegalArgumentException("Unsupported payFrequency: $this")
        }

    fun CompensationOuterClass.RateType.toDomain(): RateType =
        when (this) {
            CompensationOuterClass.RateType.CTC -> RateType.CTC
            CompensationOuterClass.RateType.GROSS -> RateType.GROSS
            CompensationOuterClass.RateType.NET -> RateType.NET
            else -> RateType.NULL
        }

    /*
    On contract-service, there is no direct mappings yet with compensation category.
    So far we're not utilizing this field on our code yet just for future reference
     */
    private fun mapCategory(name: String): CompensationCategory =
        when (name) {
            Compensation.PROBATION_BASE_PAY -> CompensationCategory.CONTRACT_BASE_PAY
            Compensation.BASE_PAY -> CompensationCategory.CONTRACT_BASE_PAY
            Compensation.MONTH_13TH -> CompensationCategory.CATEGORY_MONTH_PAY_13TH_14TH
            Compensation.MONTH_14TH -> CompensationCategory.CATEGORY_MONTH_PAY_13TH_14TH
            else -> CompensationCategory.PAY_SUPPLEMENT
        }
}
