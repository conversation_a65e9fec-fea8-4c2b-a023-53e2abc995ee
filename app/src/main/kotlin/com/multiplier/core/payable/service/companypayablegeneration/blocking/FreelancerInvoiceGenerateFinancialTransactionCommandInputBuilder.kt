package com.multiplier.core.payable.service.companypayablegeneration.blocking

import com.multiplier.core.payable.service.toDomain
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.GenerateFinancialTransactionCommandInput
import com.multiplier.payable.engine.transaction.MemberPayableInvoiceCommand
import com.multiplier.payable.grpc.schema.GrpcGenerateCompanyPayableFromEngineV2Request
import com.multiplier.payable.grpc.schema.GrpcInvoiceType
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.temporal.TemporalAdjusters

@Component
class FreelancerInvoiceGenerateFinancialTransactionCommandInputBuilder: GenerateFinancialTransactionCommandInputBuilder {

    private companion object {
        private val log = KotlinLogging.logger { }
    }

    override fun build(request: GrpcGenerateCompanyPayableFromEngineV2Request): GenerateFinancialTransactionCommandInput {
        require(request.memberPayablesRequest!= null) { "memberPayablesRequest cannot be null" }
        require(request.memberPayablesRequest.memberPayableIdsList.isNotEmpty()) { "memberPayableIdsList cannot be empty" }
        require(request.memberPayablesRequest.bundlePaymentMethodInput!= null) { "bundlePaymentMethodInput cannot be null" }
        require(request.memberPayablesRequest.bundlePaymentMethodInput.paymentMethod!= null) { "paymentMethod cannot be null" }

        val grpcGenerateCompanyPayableForMemberPayable = request.memberPayablesRequest

        val (startDate, endDate) = getStartAndEndDate(LocalDateTime.now())

        return GenerateFinancialTransactionCommandInput(
            transactionType = TransactionType.FREELANCER_INVOICE,
            companyIds = listOf(grpcGenerateCompanyPayableForMemberPayable.companyId),
            dateRange = DateRange(startDate, endDate),
            transactionDate = LocalDateTime.now(),
            cycle = InvoiceCycle.MONTHLY,
            forcedContractIdsByCompanyId = emptyMap(),
            autoSubmit = true,
            depositId = null,
            companyIdToEntityIdsMap = emptyMap(),
            memberPayableInvoiceCommand = MemberPayableInvoiceCommand(
                memberPayableIds = grpcGenerateCompanyPayableForMemberPayable.memberPayableIdsList,
                paymentMethod = grpcGenerateCompanyPayableForMemberPayable.bundlePaymentMethodInput.paymentMethod.toDomain(),
                shouldAddBankTransferFee = grpcGenerateCompanyPayableForMemberPayable.bundlePaymentMethodInput.shouldAddBankTransferFee,
            ),
        ).also { log.info { "Generated company payable for deposit request. Response = $it" } }
    }

    override fun getType(): GrpcInvoiceType = GrpcInvoiceType.FREELANCER_INVOICE

    private fun getStartAndEndDate(dateTime: LocalDateTime): Pair<LocalDateTime, LocalDateTime> {
        val startDate = dateTime.withDayOfMonth(1)
            .withHour(0)
            .withMinute(0)
            .withSecond(0)
            .withNano(0)

        val endDate = dateTime
            .with(TemporalAdjusters.lastDayOfMonth())
            .with(LocalTime.MAX)

        return startDate to endDate
    }

}