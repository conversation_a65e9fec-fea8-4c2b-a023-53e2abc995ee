package com.multiplier.core.payable.expenseBill

import com.multiplier.core.payable.adapters.netsuite.models.CountryCode

enum class ExpenseBillLineItemType(val refName: String, val countryCode: CountryCode, val netsuiteCode: String) {
    // Albania
    ALBANIA_PAYROLL("Albania - Albania Payroll", CountryCode.ALB, "MTP00914"),
    ALBANIA_PAYROLL_STAT_PAY("Albania - Albania Payroll- STAT Pay", CountryCode.ALB, "MTP01460"),
    // Argentina
    ARGENTINA_CHRISTMAS_BONUS_ACCRUAL("Argentina - Argentina Christmas Bonus Accrual", CountryCode.ARG, "MTP01605"),
    ARGENTINA_PAYROLL("Argentina - Argentina Payroll", CountryCode.ARG, "MTP01601"),
    ARGENTINA_STAT_PAY_INCOME_TAX("Argentina - Argentina Stat Pay - Income Tax", CountryCode.ARG, "MTP01602"),
    ARGENTINA_STAT_PAY_SOCIAL_SECURITY("Argentina - Argentina Stat Pay - Social Security", CountryCode.ARG, "MTP01603"),
    ARGENTINA_VACATIONS_ACCRUAL("Argentina - Argentina Vacations Accrual", CountryCode.ARG, "MTP01604"),
    // Australia
    ACT_REVENUE_OFFICE("Australia - ACT Revenue Office", CountryCode.AUS, "MTP01795"),
    AUSTRALIA_PAYROLL("Australia - Australia Payroll", CountryCode.AUS, "MTP00888"),
    AUSTRALIA_PAYROLL_PAYG("Australia - Australia Payroll - PAYG", CountryCode.AUS, "MTP01343"),
    AUSTRALIA_PAYROLL_VIC("Australia - Australia Payroll - VIC", CountryCode.AUS, "MTP01354"),
    QUICKSUPER("Australia - QuickSuper", CountryCode.AUS, "MTP01344"),
    REVENUE_NSW("Australia - Revenue NSW", CountryCode.AUS, "MTP01339"),
    REVENUE_NT("Australia - Revenue NT", CountryCode.AUS, "MTP01790"),
    REVENUE_QLD("Australia - Revenue QLD", CountryCode.AUS, "MTP01340"),
    REVENUE_SA("Australia - Revenue SA", CountryCode.AUS, "MTP01341"),
    REVENUE_WA("Australia - Revenue WA", CountryCode.AUS, "MTP01342"),
    // Belgium
    BELGIUM_PAYROLL("Belgium - Belgium Payroll", CountryCode.BEL, "MTP00933"),
    BELGIUM_PAYROLL_SOCIAL_SECURITY("Belgium - Belgium Payroll- Social Security", CountryCode.BEL, "MTP02023"),
    BELGIUM_PAYROLL_TAX("Belgium - Belgium Payroll - Tax", CountryCode.BEL, "MTP01461"),
    // Brazil
    BRAZIL_CAJU_MEAL_AND_FOOD_VOUCHERS("Brazil - Brazil CAJU Meal and Food Vouchers", CountryCode.BRA, "MTP01473"),
    BRAZIL_EMPLOYER_COST("Brazil - Brazil Employer Cost", CountryCode.BRA, "MTP01769"),
    BRAZIL_PAYROLL("Brazil - Brazil Payroll", CountryCode.BRA, "MTP00895"),
    BRAZIL_PRIVATE_PENSIONS("Brazil - Brazil Private Pensions", CountryCode.BRA, "MTP01500"),
    BRAZIL_STATUTORY_PAY_FGTS("Brazil - Brazil Statutory Pay - FGTS", CountryCode.BRA, "MTP01403"),
    BRAZIL_STATUTORY_PAY_INSS_AND_IRRF("Brazil - Brazil Statutory Pay - INSS and IRRF", CountryCode.BRA, "MTP01401"),
    BRAZIL_STATUTORY_PAY_LIDER_SAUDE("Brazil - Brazil Statutory Pay - Lider Saude", CountryCode.BRA, "MTP01893"),
    BRAZIL_STATUTORY_PAY_LIFE_INSURANCE("Brazil - Brazil Statutory Pay - Life Insurance", CountryCode.BRA, "MTP01466"),
    BRAZIL_VOUCHERS_PENSION_FEE("Brazil - Brazil Vouchers & Pension fee", CountryCode.BRA, "MTP01521"),
    // Barbados
    BARBADOS_PAYROLL("Barbados - Barbados Payroll", CountryCode.BRB, "MTP01678"),
    BARBADOS_STATUTORY_PAY_INCOME_TAX("Barbados - Barbados Statutory Pay - Income Tax", CountryCode.BRB, "MTP01679"),
    BARBADOS_STATUTORY_PAY_SOCIAL_SECURITY("Barbados - Barbados Statutory Pay - Social Security", CountryCode.BRB, "MTP01680"),
    // Canada
    CANADA_PAYROLL_NET_PAY("Canada - Canada Payroll (Net Pay)", CountryCode.CAN, "MTP00905"),
    CANADA_PAYROLL_RETIREMENT_SAVINGS_PLAN("Canada - Canada Payroll Retirement Savings Plan", CountryCode.CAN, "MTP01441"),
    CANADA_REVENUE_AGENCY_CRA_STAT_PAY("Canada - Canada Revenue Agency CRA (Stat Pay)", CountryCode.CAN, "MTP01402"),
    CANADA_REVENU_QUEBEC_STAT_PAY("Canada - Canada Revenu Quebec (Stat Pay)", CountryCode.CAN, "MTP01531"),
    CANADA_STATUTORY_PAY_EHT_BC("Canada - Canada Statutory Pay - EHT BC", CountryCode.CAN, "MTP01729"),
    CANADA_STATUTORY_PAY_EHT_ONTARIO("Canada - Canada Statutory Pay - EHT Ontario", CountryCode.CAN, "MTP01616"),
    CANADA_WCB_WORKSAFE_BC_STAT_PAY("Canada - Canada WCB WorkSafe BC (Stat Pay)", CountryCode.CAN, "MTP01522"),
    CANADA_WSIB_WORKPLACE_SAFETY_AND_INSURANCE_BOARD_STAT_PAY("Canada - Canada WSIB Workplace Safety and Insurance Board (Stat Pay)", CountryCode.CAN, "MTP01591"),
    // Switzerland
    SWITZERLAND_PAYROLL("Switzerland - Switzerland Payroll", CountryCode.CHE, "MTP00882"),
    SWITZERLAND_STATUTORY_PAYMENT("Switzerland - Switzerland Statutory Payment", CountryCode.CHE, "MTP01330"),
    // Chile
    CHILE_PAYROLL("Chile - Chile Payroll", CountryCode.CHL, "MTP00896"),
    CHILE_STATUTORY_PAY_INCOME_TAX("Chile - Chile Statutory Pay - Income Tax", CountryCode.CHL, "MTP01413"),
    CHILE_STATUTORY_PAY_SOCIAL_SECURITY("Chile - Chile Statutory Pay - Social Security", CountryCode.CHL, "MTP01417"),
    // China
    CHINA_PAYROLL("China - China Payroll", CountryCode.CHN, "MTP00927"),
    CHINA_PAYROLL_STATUTORY("China - China Payroll - Statutory", CountryCode.CHN, "MTP01345"),
    // Colombia
    COLOMBIA_PAYROLL("Colombia - Colombia Payroll", CountryCode.COL, "MTP00906"),
    COLOMBIA_PAYROLL_STAT("Colombia - Colombia Payroll - STAT", CountryCode.COL, "MTP01471"),
    COLOMBIA_STATUTORY_PAY_SOCIAL_SECURITY("Colombia - Colombia Statutory Pay - Social Security", CountryCode.COL, "MTP01544"),
    COLOMBIA_VACATIONS_ACCRUAL("Colombia - Colombia Vacations Accrual", CountryCode.COL, "MTP01571"),
    COLOMBIA_AFC("Colombia - Colombia AFC", CountryCode.COL, "MTP01765"),

    // Costa Rica
    COSTA_RICA_ER_CHARGE("Costa Rica - Costa Rica ER Charge", CountryCode.CRI, "MTP01994"),
    COSTA_RICA_PAYROLL("Costa Rica - Costa Rica Payroll", CountryCode.CRI, "MTP00897"),
    COSTA_RICA_STATUTORY_PAY_INCOME_TAX("Costa Rica - Costa Rica Statutory Pay - Income Tax", CountryCode.CRI, "MTP01418"),
    COSTA_RICA_STATUTORY_PAY_SOCIAL_SECURITY("Costa Rica - Costa Rica Statutory Pay - Social Security", CountryCode.CRI, "MTP01419"),
    COSTA_RICA_VACATIONS_ACCRUAL("Costa Rica - Costa Rica Vacations Accrual", CountryCode.CRI, "MTP01421"),
    COSTA_RICA_CHRISTMAS_BONUS_ACCRUAL("Costa Rica - Costa Rica Christmas Bonus Accrual", CountryCode.CRI, "MTP01422"),

    // Cyprus
    CYPRUS_PAYROLL("Cyprus - Cyprus Payroll", CountryCode.CYP, "MTP01059"),
    CYPRUS_STAT_PAYROLL("Cyprus - Cyprus Stat Payroll", CountryCode.CYP, "MTP01357"),
    // Dominican Republic
    DOMINICAN_REPUBLIC_PAYROLL("Dominican Republic - Dominican Republic Payroll", CountryCode.DOM, "MTP01408"),
    DOMINICAN_REPUBLIC_STATUTORY_PAY_INCOME_TAX("Dominican Republic - Dominican Republic Statutory Pay - Income Tax", CountryCode.DOM, "MTP01467"),
    DOMINICAN_REPUBLIC_STATUTORY_PAY_SS_INFOTEP("Dominican Republic - Dominican Republic Statutory Pay - SS INFOTEP", CountryCode.DOM, "MTP01468"),
    DOMINICAN_REPUBLIC_STATUTORY_PAY_SS_TSS_MP("Dominican Republic - Dominican Republic Statutory Pay - SS TSS MP", CountryCode.DOM, "MTP01469"),
    // Spain
    SPAIN_PAYROLL("Spain - Spain Payroll", CountryCode.ESP, "MTP00916"),
    SPAIN_PAYROLL_STATSOCIAL_SECURITY("Spain - Spain Payroll STAT-Social Security", CountryCode.ESP, "MTP02020"),
    SPAIN_PAYROLL_STAT_PAYMENT("Spain - Spain Payroll STAT Payment", CountryCode.ESP, "MTP01332"),
    // Estonia
    ESTONIA_PAYROLL("Estonia - Estonia Payroll", CountryCode.EST, "MTP00898"),
    ESTONIA_PAYROLL_TAX("Estonia - Estonia Payroll - Tax", CountryCode.EST, "MTP01369"),
    // France
    FRANCE_PAYROLL("France - France Payroll", CountryCode.FRA, "MTP01290"),
    FRANCE_PAYROLL_WITHHOLDING_TAX("France - France Payroll - Withholding Tax", CountryCode.FRA, "MTP01454"),
    FRANCE_STATUTORY_PAYROLL_CONSTITUTION_DELA_RESERVE_FINANCIERE("France - France Statutory Payroll - Constitution dela Reserve financiere", CountryCode.FRA, "MTP02087"),
    FRANCE_STATUTORY_PAYROLL_RSU("France - France Statutory Payroll - RSU", CountryCode.FRA, "MTP02088"),
    FRANCE_STATUTORY_PAYROLL_SOCIAL_SECURITY("France - France Statutory Payroll - Social Security", CountryCode.FRA, "MTP01455"),
    // United Kingdom
    CHILD_MAINTENANCE_SERVICE_DEO_UK("United Kingdom - Child Maintenance Service (DEO) UK", CountryCode.GBR, "MTP01866"),
    HMRC_UK("United Kingdom - HMRC - UK", CountryCode.GBR, "MTP01487"),
    NESTUK("United Kingdom - NEST-UK", CountryCode.GBR, "MTP01615"),
    UK_P11D_ADJUSTMENT("United Kingdom - UK P11D Adjustment", CountryCode.GBR, "MTP02068"),
    UK_PAYROLL("United Kingdom - UK Payroll", CountryCode.GBR, "MTP01053"),
    // Greece
    GREECE_PAYROLL("Greece - Greece Payroll", CountryCode.GRC, "MTP00934"),
    GREECE_PAYROLL_STAT_SOCIAL_SECURITY("Greece - Greece Payroll- STAT Social Security", CountryCode.GRC, "MTP02016"),
    GREECE_PAYROLL_STAT_TAX("Greece - Greece Payroll - STAT TAX", CountryCode.GRC, "MTP01462"),
    // Guatemala
    GUATEMALA_13TH_SALARY_ACCRUAL("Guatemala - Guatemala - 13th Salary Accrual", CountryCode.GTM, "MTP01434"),
    GUATEMALA_14TH_SALARY_ACCRUAL("Guatemala - Guatemala - 14th Salary Accrual", CountryCode.GTM, "MTP01435"),
    GUATEMALA_PAYROLL("Guatemala - Guatemala Payroll", CountryCode.GTM, "MTP00902"),
    GUATEMALA_STATUTORY_PAY_INCOME_TAX("Guatemala - Guatemala Statutory Pay - Income Tax", CountryCode.GTM, "MTP01431"),
    GUATEMALA_STATUTORY_PAY_SOCIAL_SECURITY("Guatemala - Guatemala Statutory Pay - Social Security", CountryCode.GTM, "MTP01432"),
    GUATEMALA_VACATIONS_ACCRUAL("Guatemala - Guatemala - Vacations Accrual", CountryCode.GTM, "MTP01433"),
    // Honduras
    HONDURAS_13TH_SALARY_ACCRUAL("Honduras - Honduras 13Th Salary Accrual", CountryCode.HND, "MTP01445"),
    HONDURAS_14TH_SALARY_ACCRUAL("Honduras - Honduras 14Th Salary Accrual", CountryCode.HND, "MTP01446"),
    HONDURAS_PAYROLL("Honduras - Honduras Payroll", CountryCode.HND, "MTP01111"),
    HONDURAS_STATUTORY_PAY_INCOME_TAX("Honduras - Honduras Statutory Pay - Income Tax", CountryCode.HND, "MTP01443"),
    HONDURAS_STATUTORY_PAY_SOCIAL_SECURITY("Honduras - Honduras Statutory Pay - Social Security", CountryCode.HND, "MTP01444"),
    // Croatia
    CROATIA_PAYROLL("Croatia - CROATIA Payroll", CountryCode.HRV, "MTP00924"),
    CROATIA_STATUTORY_PAYMENT("Croatia - Croatia Statutory Payment", CountryCode.HRV, "MTP01488"),
    // Hungary
    HUNGARY_PAYROLL("Hungary - Hungary Payroll", CountryCode.HUN, "MTP01599"),
    HUNGARY_PAYROLL_STAT("Hungary - Hungary Payroll STAT", CountryCode.HUN, "MTP01600"),
    // Indonesia
    INDONESIA_PAYROLL("Indonesia - Indonesia Payroll", CountryCode.IDN, "MTP01052"),
    INDONESIA_PAYROLL_STATUTORY("Indonesia - Indonesia Payroll - Statutory", CountryCode.IDN, "MTP01346"),
    // India
    INDIA_PAYROLL("India - India - Payroll", CountryCode.IND, "MTP01031"),
    INDIA_PAYROLL_STATUTORY_ESIC("India - India Payroll Statutory - ESIC", CountryCode.IND, "MTP01700"),
    INDIA_PAYROLL_STATUTORY_LWF("India - India Payroll Statutory - LWF", CountryCode.IND, "MTP01702"),
    INDIA_PAYROLL_STATUTORY_PF("India - India Payroll Statutory - PF", CountryCode.IND, "MTP01701"),
    INDIA_PAYROLL_STATUTORY_PTAX("India - India Payroll Statutory - PTAX", CountryCode.IND, "MTP01699"),
    INDIA_PAYROLL_STATUTORY_TDS("India - India Payroll Statutory - TDS", CountryCode.IND, "MTP01698"),
    // Ireland
    IRELAND_PAYROLL("Ireland - Ireland Payroll", CountryCode.IRL, "MTP00913"),
    IRELAND_PAYROLL_STAT("Ireland - Ireland Payroll - STAT", CountryCode.IRL, "MTP01463"),
    // Israel
    ISRAEL_PAYROLL("Israel - Israel Payroll", CountryCode.ISR, "MTP00920"),
    ISRAEL_STATUTORY_PAYMENTS_IRSEE("Israel - Israel Statutory Payments - IRS-EE", CountryCode.ISR, "MTP02064"),
    ISRAEL_STATUTORY_PAYMENTS_PENSION("Israel - Israel Statutory Payments - PENSION", CountryCode.ISR, "MTP01450"),
    ISRAEL_STATUTORY_PAYMENTS_SOCIAL_SECURITY("Israel - Israel Statutory Payments - SOCIAL SECURITY", CountryCode.ISR, "MTP02065"),
    // Italy
    ITALY_PAYROLL("Italy - Italy Payroll", CountryCode.ITA, "MTP00899"),
    ITALY_PAYROLLTFR("Italy - Italy Payroll-TFR", CountryCode.ITA, "MTP02073"),
    ITALY_PAYROLL_STAT("Italy - Italy Payroll - STAT", CountryCode.ITA, "MTP01337"),
    // Japan
    JAPAN_PAYROLL("Japan - Japan Payroll", CountryCode.JPN, "MTP00929"),
    JAPAN_PAYROLL_STATUTORY("Japan - Japan Payroll - Statutory", CountryCode.JPN, "MTP01355 "),
    // Kazakhstan
    KAZAKHSTAN_PAYROLL("Kazakhstan - Kazakhstan Payroll", CountryCode.KAZ, "MTP01244"),
    KAZAKHSTAN_STATUTORY_PAY_INCOME_TAX("Kazakhstan - Kazakhstan Statutory Pay - Income Tax", CountryCode.KAZ, "MTP01440"),
    KAZAKHSTAN_STATUTORY_PAY_SOCIAL_SECURITY("Kazakhstan - Kazakhstan Statutory Pay - Social Security", CountryCode.KAZ, "MTP01442"),
    // Kenya
    KENYA_PAYROLL("Kenya - Kenya Payroll", CountryCode.KEN, "MTP01033"),
    KENYA_STATUTORY_PAYMENT("Kenya - Kenya Statutory Payment", CountryCode.KEN, "MTP01329"),
    // Cambodia
    CAMBODIA_PAYROLL("Cambodia - CAMBODIA Payroll", CountryCode.KHM, "MTP00923"),
    CAMBODIA_STATNSSF_INJURY_HEALTHCARE_STAFF_PENSION("Cambodia - Cambodia Stat-NSSF, Injury, Healthcare, Staff Pension", CountryCode.KHM, "MTP01409"),
    // Saint Lucia
    SAINT_LUCIA_PAYROLL("Saint Lucia - Saint Lucia Payroll", CountryCode.LCA, "MTP01894"),
    SAINT_LUCIA_STATUTORY_PAY_INCOME_TAX("Saint Lucia - Saint Lucia Statutory Pay - Income Tax", CountryCode.LCA, "MTP01895"),
    SAINT_LUCIA_STATUTORY_PAY_SOCIAL_SECURITY("Saint Lucia - Saint Lucia Statutory Pay - Social Security", CountryCode.LCA, "MTP01896"),
    // Sri Lanka
    SRI_LANKA_PAYROLL("Sri Lanka - Sri Lanka Payroll", CountryCode.LKA, "MTP00936"),
    SRI_LANKA_STATEPF_ETF_TAX("Sri Lanka - Sri Lanka Stat-EPF, ETF, Tax", CountryCode.LKA, "MTP01412"),
    // Latvia
    LATVIA_PAYROLL("Latvia - Latvia Payroll", CountryCode.LVA, "MTP00911"),
    VALSTS_BUDZETS_VID_LATVIA_STAT_PAY("Latvia - Valsts budzets VID (Latvia Stat Pay)", CountryCode.LVA, "MTP01361"),
    // Mexico
    MEXICO_PAYROLL("Mexico - Mexico Payroll", CountryCode.MEX, "MTP00903"),
    MEXICO_SIPARE_SOCIAL_SECURITY("Mexico - Mexico - SIPARE Social Security", CountryCode.MEX, "MTP01394"),
    MEXICO_STAT_PAY_INCOME_TAX("Mexico - Mexico Stat Pay - Income Tax", CountryCode.MEX, "MTP01395"),
    MEXICO_STAT_PAY_LOCAL_PAYROLL_TAX("Mexico - Mexico Stat Pay - Local Payroll Tax", CountryCode.MEX, "MTP01393"),
    // Mozambique
    MOZAMBIQUE_PAYROLL("Mozambique - Mozambique Payroll", CountryCode.MOZ, "MTP01502"),
    MOZAMBIQUE_STATUTORY_PAYMENT("Mozambique - Mozambique - Statutory payment", CountryCode.MOZ, "MTP01506"),
    MOZAMBIQUE_STATUTORY_PAYMENTINSS("Mozambique - Mozambique - Statutory payment-INSS", CountryCode.MOZ, "MTP01656"),
    // Mauritius
    MAURITIUS_PAYROLL("Mauritius - Mauritius Payroll", CountryCode.MUS, "MTP01749"),
    MAURITIUS_STAT("Mauritius - Mauritius Stat", CountryCode.MUS, "MTP01750"),
    // Malaysia
    MALAYSIA_PAYROLL("Malaysia - Malaysia Payroll", CountryCode.MYS, "MTP00921"),
    MALAYSIA_STAT_EPF_HRDF_PCB_PTPN_SOCSO_EIS_ZAKAT("Malaysia - Malaysia Stat - EPF, HRDF, PCB, PTPN, SOCSO & EIS, ZAKAT", CountryCode.MYS, "MTP01439"),
    // Nigeria
    NIGERIA_PAYROLL("Nigeria - Nigeria Payroll", CountryCode.NGA, "MTP00925"),
    NIGERIA_STATUTORY_PAYMENTS("Nigeria - Nigeria Statutory Payments", CountryCode.NGA, "MTP01383"),
    // Netherlands
    NETHERLANDS_PAYROLL("Netherlands - Netherlands Payroll", CountryCode.NLD, "MTP00900"),
    NETHERLANDS_PAYROLL_HOLIDAY_ALLOWANCE("Netherlands - Netherlands Payroll - Holiday Allowance", CountryCode.NLD, "MTP02074"),
    NETHERLANDS_PAYROLL_PENSION_CONTRIBUTION("Netherlands - Netherlands Payroll - Pension Contribution", CountryCode.NLD, "MTP02018"),
    NETHERLANDS_PAYROLL_STAT("Netherlands - Netherlands Payroll - STAT", CountryCode.NLD, "MTP01465"),
    // New Zealand
    NEW_ZEALAND_PAYROLL("New Zealand - New Zealand Payroll", CountryCode.NZL, "MTP00889"),
    NEW_ZEALAND_PAYROLL_STATUTORY("New Zealand - New Zealand Payroll - Statutory", CountryCode.NZL, "MTP01348 "),
    // Pakistan
    PAKISTAN_PAYROLL("Pakistan - Pakistan Payroll", CountryCode.PAK, "MTP01087"),
    PAKISTAN_PAYROLL_EOBI("Pakistan - Pakistan Payroll EOBI", CountryCode.PAK, "MTP01353"),
    PAKISTAN_PAYROLL_TAX("Pakistan - Pakistan Payroll Tax", CountryCode.PAK, "MTP01352"),
    // Panama
    PANAMA_BANK_DISCOUNT("Panama - Panama Bank Discount", CountryCode.PAN, "MTP01995"),
    PANAMA_PAYROLL("Panama - Panama Payroll", CountryCode.PAN, "MTP01720"),
    PANAMA_STATUTORY_PAY_INCOME_TAX("Panama - Panama Statutory Pay - Income Tax", CountryCode.PAN, "MTP01721"),
    PANAMA_STATUTORY_PAY_SOCIAL_SECURITY("Panama - Panama Statutory Pay - Social Security", CountryCode.PAN, "MTP01722"),
    // Peru
    ER_COST("Peru - ER Cost", CountryCode.PER, "MTP01831"),
    PERU_PAYROLL("Peru - Peru Payroll", CountryCode.PER, "MTP00901"),
    PERU_STATUTORY_PAY_INCOME_TAX("Peru - Peru Statutory Pay - Income Tax", CountryCode.PER, "MTP01451"),
    PERU_STATUTORY_PAY_SOCIAL_SECURITY("Peru - Peru Statutory Pay - Social Security", CountryCode.PER, "MTP01452"),
    // Philippines
    PHILIPPINES_HDMF_LOAN_PAYMENT("Philippines - Philippines HDMF Loan Payment", CountryCode.PHL, "MTP01372"),
    PHILIPPINES_HDMF_PAYMENT("Philippines - Philippines HDMF Payment", CountryCode.PHL, "MTP01371"),
    PHILIPPINES_PAYROLL("Philippines - Philippines - Payroll", CountryCode.PHL, "MTP01030"),
    PHILIPPINES_PAYROLL_TAX_PAYMENT("Philippines - Philippines Payroll Tax Payment", CountryCode.PHL, "MTP01367"),
    PHILIPPINES_PHIC_PAYMENT("Philippines - Philippines PHIC Payment", CountryCode.PHL, "MTP01365"),
    PHILIPPINES_SSS_LOAN_PAYMENT("Philippines - Philippines SSS Loan Payment", CountryCode.PHL, "MTP01366"),
    PHILIPPINES_SSS_PAYMENT("Philippines - Philippines SSS Payment", CountryCode.PHL, "MTP01364"),
    // Poland
    PFRON_STATE_FUND_FOR_THE_REHABILITATION_OF_DISABLED_PERSONS_POLAND("Poland - PFRON - State Fund for the Rehabilitation of Disabled Persons - Poland", CountryCode.POL, "MTP01902"),
    POLAND_PAYROLL("Poland - Poland Payroll", CountryCode.POL, "MTP00922"),
    POLAND_STATUTORY_PAYPERSONAL_INCOME_TAX("Poland - Poland Statutory Pay-Personal income tax", CountryCode.POL, "MTP01386"),
    POLAND_STATUTORY_PAY_MONTHLY_ZUS_PAYMENT("Poland - Poland Statutory pay - Monthly ZUS payment", CountryCode.POL, "MTP01384"),
    POLAND_STATUTORY_PAY_PPK_CONTRIBUTION("Poland - Poland Statutory Pay- PPK contribution", CountryCode.POL, "MTP01385"),
    // Puerto Rico
    PUERTO_RICO_PAYROLL("Puerto Rico - Puerto Rico Payroll", CountryCode.PRI, "MTP01494"),
    PUERTO_RICO_STATUTORY_PAY_INCOME_TAX("Puerto Rico - Puerto Rico Statutory Pay - Income Tax", CountryCode.PRI, "MTP01495"),
    PUERTO_RICO_STATUTORY_PAY_SOCIAL_SECURITY("Puerto Rico - Puerto Rico Statutory Pay - Social Security", CountryCode.PRI, "MTP01496"),
    PUERTO_RICO_VACATIONS_ACCRUAL("Puerto Rico - Puerto Rico Vacations Accrual", CountryCode.PRI, "MTP01575"),

    // Rwanda
    RWANDA_PAYROLL("Rwanda - Rwanda Payroll", CountryCode.RWA, "MTP01035"),
    RWANDA_STATUTORY_PAYMENT("Rwanda - Rwanda Statutory Payment", CountryCode.RWA, "MTP01328"),
    // Senegal
    SENEGAL_PAYROLL("Senegal - Senegal Payroll", CountryCode.SEN, "MTP01829"),
    SENEGAL_PAYROLL_EMPLOYEE_STATUTORY_PAYMENTS("Senegal - Senegal Payroll - Employee Statutory Payments", CountryCode.SEN, "MTP01830"),
    SENEGAL_PAYROLL_EMPLOYER_STATUTORY_PAYMENTS("Senegal - Senegal Payroll - Employer Statutory Payments", CountryCode.SEN, "MTP02062"),
    // Singapore
    SINGAPORE_PAYROLL_MT_SG("Singapore - Singapore Payroll (MT SG)", CountryCode.SGP, "MTP00891 "),
    SINGAPORE_PAYROLL_STATUTORY("Singapore - Singapore Payroll - Statutory", CountryCode.SGP, "MTP01349 "),
    // Serbia
    SERBIA_PAYROLL("Serbia - Serbia Payroll", CountryCode.SRB, "MTP00883"),
    SERBIA_STATUTORY_PAYMENT("Serbia - Serbia Statutory Payment", CountryCode.SRB, "MTP01327"),
    // Slovakia
    SLOVAKIA_PAYROLL("Slovakia - Slovakia Payroll", CountryCode.SVK, "MTP01758"),
    SLOVAKIA_PAYROLL_STATUTORY("Slovakia - Slovakia Payroll - Statutory", CountryCode.SVK, "MTP01759"),
    // Taiwan
    TAIWAN_PAYROLL("Taiwan - Taiwan Payroll", CountryCode.TWN, "MTP00892 "),
    TAIWAN_PAYROLL_STATUTORY("Taiwan - Taiwan Payroll - Statutory", CountryCode.TWN, "MTP01350 "),
    // Tanzania
    TANZANIA_PAYROLL("Tanzania - Tanzania Payroll", CountryCode.TZA, "MTP01213"),
    TANZANIA_PAYROLL_STATUTORY_CONTRIBUTIONS("Tanzania - Tanzania Payroll - Statutory Contributions", CountryCode.TZA, "MTP02063"),
    // Ukraine
    UKRAINE_PAYROLL("Ukraine - Ukraine Payroll", CountryCode.UKR, "MTP01077"),
    UKRAINE_STATUTORY_PAY_INCOME_TAX("Ukraine - Ukraine Statutory Pay - Income Tax", CountryCode.UKR, "MTP01376"),
    UKRAINE_STATUTORY_PAY_SOCIAL_SECURITY("Ukraine - Ukraine Statutory Pay - Social Security", CountryCode.UKR, "MTP01377"),
    // Uruguay
    URUGUAY_13TH_SALARY_ACCRUAL("Uruguay - Uruguay 13th Salary Accrual", CountryCode.URY, "MTP01484"),
    URUGUAY_ER_CHARGE("Uruguay - Uruguay ER Charge", CountryCode.URY, "MTP02028"),
    URUGUAY_PAYROLL("Uruguay - Uruguay Payroll", CountryCode.URY, "MTP01127"),
    URUGUAY_STATUTORY_PAY_INCOME_TAX("Uruguay - Uruguay Statutory Pay - Income Tax", CountryCode.URY, "MTP01406"),
    URUGUAY_STATUTORY_PAY_SOCIAL_SECURITY("Uruguay - Uruguay Statutory Pay - Social Security", CountryCode.URY, "MTP01407"),
    URUGUAY_VACATION_ACCRUAL("Uruguay - Uruguay Vacation Accrual", CountryCode.URY, "MTP01483"),
    // United States
    UNITED_STATES_PAYROLL("United States - United States Payroll", CountryCode.USA, "MTP00894"),
    UNITED_STATES_PAYROLL_401K("United States - United States Payroll - 401K", CountryCode.USA, "MTP01791"),
    UNITED_STATES_PAYROLL_STATUTORY_DEDUCTIONS("United States - United states- Payroll Statutory Deductions", CountryCode.USA, "MTP01404"),
    // Vietnam
    BAO_HIEM_XA_HOI_QUAN_1("Vietnam - BAO HIEM XA HOI QUAN 1", CountryCode.VNM, "MTP01428"),
    CONG_DOAN_VIET_NAM("Vietnam - CONG DOAN VIET NAM", CountryCode.VNM, "MTP01429"),
    VIETNAM_PAYROLL("Vietnam - Vietnam Payroll", CountryCode.VNM, "MTP01051"),
    VP_KBNN_HO_CHI_MINH("Vietnam - VP KBNN Ho Chi Minh", CountryCode.VNM, "MTP01430"),
    // Bangladesh
    BANGLADESH_PAYROLL("Bangladesh - Bangladesh Payroll", CountryCode.BGD, "MTP00926"),
    GOVERNMENT_COLLECTION_ACCOUNT_BANGLADESH("Bangladesh - Government Collection Account Bangladesh", CountryCode.BGD, "MTP01358"),
    // Bulgaria
    BULGARIA_PAYROLL("Bulgaria - Bulgaria Payroll", CountryCode.BGR, "MTP00908"),
    BULGARIA_STAT_PAYROLL("Bulgaria - Bulgaria Stat Payroll", CountryCode.BGR, "MTP01356"),
    // Czech Republic
    CZECH_REPUBLIC_PAYROLL("Czech Republic - Czech Republic Payroll", CountryCode.CZE, "MTP00919"),
    CZECH_REPUBLIC_STAT_PAY_HEALTH_INSURANCE("Czech Republic - Czech Republic Stat Pay - Health Insurance", CountryCode.CZE, "MTP01380"),
    CZECH_REPUBLIC_STAT_PAY_INCOME_TAX("Czech Republic - Czech Republic Stat Pay - Income Tax", CountryCode.CZE, "MTP01378"),
    CZECH_REPUBLIC_STAT_PAY_SOCIAL_SECURITY("Czech Republic - Czech Republic Stat Pay - Social Security", CountryCode.CZE, "MTP01379"),
    // Denmark
    DENMARK_PAYROLL("Denmark - Denmark Payroll", CountryCode.DNK, "MTP00909"),
    DENMARK_PAYROLLATP_CONTRIBUTION("Denmark - Denmark Payroll-ATP Contribution", CountryCode.DNK, "MTP02109"),
    DENMARK_PAYROLLSAMLET_BETALING("Denmark - DenMark Payroll-Samlet Betaling", CountryCode.DNK, "MTP02108"),
    DENMARK_STAT_PAYROLL("Denmark - Denmark Stat Payroll", CountryCode.DNK, "MTP01360"),
    // Egypt
    EGYPT_PAYROLL("Egypt - Egypt - Payroll", CountryCode.EGY, "MTP01551"),
    EGYPT_STATUTORY_PAYROLL("Egypt - Egypt - Statutory Payroll", CountryCode.EGY, "MTP01552"),
    // El Salvador
    EL_SALVADOR_PAYROLL("El Salvador - El Salvador Payroll", CountryCode.SLV, "MTP01207"),
    EL_SALVADOR_STATUTORY_PAY_INCOME_TAX("El Salvador - El Salvador Statutory Pay - Income Tax", CountryCode.SLV, "MTP01423"),
    EL_SALVADOR_STATUTORY_PAY_SOCIAL_SECURITY("El Salvador - El Salvador Statutory Pay - Social Security", CountryCode.SLV, "MTP01424"),
    EL_SALVADOR_VACATIONS_ACCRUAL("El Salvador - El Salvador - Vacations Accrual", CountryCode.SLV, "MTP01425"),
    EL_SALVADOR_13TH_SALARY_ACCRUAL("El Salvador - El Salvador 13th Salary Accrual", CountryCode.SLV, "MTP01426"),
    EL_SALVADOR_COMPENSATION_BONUS_ACCRUAL("El Salvador - El Salvador Compensation Bonus Accrual", CountryCode.SLV, "MTP01427"),
    // Germany
    GERMANY_PAYROLL("Germany - Germany Payroll", CountryCode.DEU, "MTP00910"),
    GERMANY_STAT_PAYROLL("Germany - Germany Stat Payroll", CountryCode.DEU, "MTP01359"),
    // Gibraltar
    GIBRALTAR_PAYROLL("Gibraltar - Gibraltar Payroll", CountryCode.GIB, "MTP02066"),
    HM_GOVERNMENT_OF_GIBRALTAR_INCOME_TAX_OFFICE("Gibraltar - HM Government of Gibraltar - Income Tax Office", CountryCode.GIB, "MTP01919"),
    // Guatemala and Honduras entries already exist above
    // Adding remaining countries
    // Hong Kong
    HONG_KONG_PAYROLL("Hong Kong - Hong Kong Payroll", CountryCode.HKG, "MTP00928"),
    HONGKONG_STATMPF("Hong Kong - Hongkong Stat-MPF", CountryCode.HKG, "MTP01410"),
    // Isle of Man
    ISLE_OF_MAN_NET_PAY("Isle of Man - Isle of Man Net pay", CountryCode.IMN, "MTP02113"),
    ISLE_OF_MAN_STATUTORY_PAY("Isle of Man - Isle of Man Statutory pay", CountryCode.IMN, "MTP01381"),
    // South Korea
    SOUTH_KOREA_PAYROLL("South Korea - South Korea Payroll", CountryCode.KOR, "MTP00930"),
    KOREA_PAYROLL_STATUTORY_4SSI("South Korea - Korea Payroll Statutory - 4SSI", CountryCode.KOR, "MTP01347"),
    KOREA_PAYROLL_STATUTORY_INCOME_TAX_NATIONAL("South Korea - Korea Payroll Statutory - Income tax (National)", CountryCode.KOR, "MTP02070"),
    KOREA_PAYROLL_STATUTORY_LOCAL_TAX("South Korea - Korea Payroll Statutory - Local Tax", CountryCode.KOR, "MTP02072"),
    KOREA_PAYROLL_STATUTORY_RESIDENT_TAX_LOCAL("South Korea - Korea Payroll Statutory - Resident tax (Local)", CountryCode.KOR, "MTP02071"),
    // Lithuania
    LITHUANIA_PAYROLL("Lithuania - Lithuania Payroll", CountryCode.LTU, "MTP00904"),
    LITHUANIA_STAT("Lithuania - Lithuania STAT", CountryCode.LTU, "MTP01663"),
    // Madagascar
    MADAGASCAR_PAYROLL("Madagascar - Madagascar Payroll", CountryCode.MDG, "MTP01503"),
    MADAGASCAR_STATUTORY_PAYMENTS("Madagascar - Madagascar - Statutory payments", CountryCode.MDG, "MTP01507"),
    // Malta
    MALTA_PAYROLL("Malta - Malta Payroll", CountryCode.MLT, "MTP00935"),
    MALTA_PAYROLL_STAT("Malta - Malta Payroll - STAT", CountryCode.MLT, "MTP01334"),
    // Philippines
    PHILIPPINES_HMO_PAYMENT("Philippines - Philippines HMO Payment", CountryCode.PHL, "MTP01592"),
    // Portugal
    PORTUGAL_PAYROLL("Portugal - Portugal Payroll", CountryCode.PRT, "MTP00912"),
    PORTUGAL_PAYROLL_WORKER_INSURANCE("Portugal - Portugal Payroll - Worker Insurance", CountryCode.PRT, "MTP01658"),
    PORTUGAL_STAT_PAY_INCOME_TAX("Portugal - Portugal Stat Pay - Income Tax", CountryCode.PRT, "MTP01389"),
    PORTUGAL_STAT_PAY_SOCIAL_SECURITY("Portugal - Portugal Stat Pay - Social Security", CountryCode.PRT, "MTP01388"),
    // Romania
    ROMANIA_PAYROLL("Romania - Romania Payroll", CountryCode.ROU, "MTP00915"),
    ROMANIA_STATUTORY_PAY_HEALTH_CONTRIBUTION("Romania - Romania Statutory Pay - Health Contribution", CountryCode.ROU, "MTP01392"),
    ROMANIA_STATUTORY_PAY_INCOME_TAX("Romania - Romania Statutory Pay - Income Tax", CountryCode.ROU, "MTP01390"),
    ROMANIA_STATUTORY_PAY_SOCIAL_SECURITY("Romania - Romania Statutory Pay - Social Security", CountryCode.ROU, "MTP01391"),
    // South Africa
    SOUTH_AFRICA_PAYROLL("South Africa - South Africa Payroll", CountryCode.ZAF, "MTP00918"),
    SOUTH_AFRICA_PAYROLL_RECOVERY("South Africa - South Africa Payroll Recovery", CountryCode.ZAF, "MTP01400"),
    SOUTH_AFRICA_STATUTORY_PAY("South Africa - South Africa Statutory Pay", CountryCode.ZAF, "MTP01399"),
    // Thailand
    THAILAND_PAYROLL("Thailand - Thailand Payroll", CountryCode.THA, "MTP00893"),
    THAILAND_PAYROLL_STATUTORY("Thailand - Thailand Payroll - Statutory", CountryCode.THA, "MTP01351"),
    // Turkey
    TURKEY_PAYROLL("Turkey - Turkey Payroll", CountryCode.TUR, "MTP00917"),
    TURKEY_STATINCOME_STAMP_TAX("Turkey - Turkey Stat-Income & Stamp Tax", CountryCode.TUR, "MTP01411"),
    // UAE
    UAE_PAYROLL("UAE - UAE Payroll", CountryCode.ARE, "MTP00937"),
    // Uganda
    UGANDA_PAYROLL("Uganda - Uganda Payroll", CountryCode.UGA, "MTP01137"),
    UGANDA_STATUTORY_PAY_PAYE_NSSF("Uganda - Uganda Statutory Pay - PAYE & NSSF", CountryCode.UGA, "MTP01387"),

    // Bolivia
    BOLIVIA_PAYROLL("Bolivia - Bolivia Payroll", CountryCode.BOL, "MTP02089"),
    BOLIVIA_STATUTORY_PAY_SOCIAL_SECURITY("Bolivia - Bolivia Statutory Pay - Social Security", CountryCode.BOL, "MTP02091"),
    BOLIVIA_CHRISTMAS_BONUS_ACCRUAL("Bolivia - Bolivia Christmas Bonus Accrual", CountryCode.BOL, "MTP02092"),
    BOLIVIA_SEVERANCE_ACCRUAL("Bolivia - Bolivia Severance Accrual", CountryCode.BOL, "MTP02093"),

    DEFAULT("DEFAULT", CountryCode.USA, "");

    companion object {
        fun getLineItemTypes(countryCode: CountryCode): List<ExpenseBillLineItemType> {
            return entries.filter { it.countryCode == countryCode }
        }
    }
}
