package com.multiplier.core.payable.sync.manualsync

import com.multiplier.core.payable.adapters.netsuite.client.NetsuiteRestletClient
import com.multiplier.core.payable.creditnote.database.CreditNoteService
import com.multiplier.core.payable.invoice.database.InvoiceService
import com.multiplier.payable.types.RecordType
import com.multiplier.payable.types.ResyncFinancialTransactionsInput
import feign.FeignException
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger { }

@Service
class NetsuiteManualSyncService(
    private val netsuiteRestletClient: NetsuiteRestletClient,
    private val invoiceService: InvoiceService,
    private val creditNoteService: CreditNoteService,
) {
    fun resync(resyncFinancialTransactionsInput: ResyncFinancialTransactionsInput): Boolean {
        val records =
            resyncFinancialTransactionsInput.records
                .groupBy { it.type }
                .mapValues { (_, record) -> record.map { it.id } }

        val invoiceExternalIds =
            invoiceService
                .getAllByInvoiceNumbers(records[RecordType.INVOICE])
                .map { it.externalId }
        val debitNoteExternalIds =
            invoiceService
                .getAllByInvoiceNumbers(records[RecordType.DEBIT_NOTE])
                .map { it.externalId }
        val creditNotesExternalIds =
            creditNoteService
                .getAllByCreditNoteNumbers(records[RecordType.CREDIT_NOTE])
                .map { it.externalId }

        log.info {
            "financial transaction ids to be resync: invoices: $invoiceExternalIds, debit notes: $debitNoteExternalIds, credit notes: $creditNotesExternalIds"
        }

        val commaSeperatedIds = (invoiceExternalIds + debitNoteExternalIds + creditNotesExternalIds).joinToString(",")
        log.info { "request: $commaSeperatedIds" }
        try {
            netsuiteRestletClient.resyncFinancialTransactions(commaSeperatedIds)
            return true
        } catch (fe: FeignException) {
            log.error { "Error encountered during resync: $fe" }
            return false
        }
    }
}