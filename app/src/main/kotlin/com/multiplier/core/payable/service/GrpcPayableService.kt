package com.multiplier.core.payable.service

import com.google.protobuf.DoubleValue
import com.google.protobuf.Timestamp
import com.multiplier.core.config.featureflag.FeatureFlagService
import com.multiplier.core.payable.adapters.GenerateDepositForContractInput
import com.multiplier.core.payable.adapters.PayableServiceAdapter
import com.multiplier.core.payable.invoice.database.fromGrpcInvoiceFilters
import com.multiplier.core.payable.invoice.filter.DepositInvoiceFilter
import com.multiplier.core.payable.invoice.filter.InvoiceFetcherProvider
import com.multiplier.core.payable.mapper.GrpcDateTimeMapper
import com.multiplier.core.payable.mapper.GrpcFinancialTransactionMapper
import com.multiplier.core.payable.mapper.GrpcInvoiceMapper
import com.multiplier.core.payable.mapper.GrpcManagementFeeMapper
import com.multiplier.core.payable.mapper.GrpcPayableMapper
import com.multiplier.core.payable.mapper.GrpcPayableMapperV2
import com.multiplier.core.payable.mapper.GrpcPricingMapper
import com.multiplier.core.payable.mapper.toDomain
import com.multiplier.core.payable.pricing.OrderFormPricingService
import com.multiplier.core.payable.pricing.adapter.PricingServiceAdapter
import com.multiplier.core.schema.currency.Currency
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.GenerateExpenseBillTransactionCommand
import com.multiplier.payable.engine.transaction.GenerateFinancialTransactionCommand
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.grpc.schema.AddFullPaymentToExternalInvoicesRequest
import com.multiplier.payable.grpc.schema.BulkUpdateCompanyPayableStatusRequest
import com.multiplier.payable.grpc.schema.BulkUpdateCompanyPayableTotalAmountRequest
import com.multiplier.payable.grpc.schema.CompanyPayableVoidErrorResponse
import com.multiplier.payable.grpc.schema.CompanyPayableVoidRequest
import com.multiplier.payable.grpc.schema.CompanyPayableVoidResponse
import com.multiplier.payable.grpc.schema.CompanyPayableVoidSuccessResponse
import com.multiplier.payable.grpc.schema.CompanyPricingEntry
import com.multiplier.payable.grpc.schema.CreateExternalInvoiceForMemberPayablesRequest
import com.multiplier.payable.grpc.schema.Date
import com.multiplier.payable.grpc.schema.DeleteOrVoidContractDepositPayablesRequest
import com.multiplier.payable.grpc.schema.DeleteOrVoidExternalInvoiceRequest
import com.multiplier.payable.grpc.schema.Empty
import com.multiplier.payable.grpc.schema.ExternalInvoiceAddPaymentResponse
import com.multiplier.payable.grpc.schema.ExternalInvoiceCreationResponse
import com.multiplier.payable.grpc.schema.GenerateAnnualPlanRequest
import com.multiplier.payable.grpc.schema.GenerateDepositForContractRequest
import com.multiplier.payable.grpc.schema.GenerateDepositForContractResponse
import com.multiplier.payable.grpc.schema.GenerateFinancialTransactionResponse
import com.multiplier.payable.grpc.schema.GenerateServiceInvoiceRequest
import com.multiplier.payable.grpc.schema.GenerateVendorBillInvoiceRequest
import com.multiplier.payable.grpc.schema.GenerateVendorBillInvoiceResponse
import com.multiplier.payable.grpc.schema.GetBillingCurrencyCodeRequest
import com.multiplier.payable.grpc.schema.GetBillingCurrencyCodeResponse
import com.multiplier.payable.grpc.schema.GetCompanyManagementFeesEstimationRequest
import com.multiplier.payable.grpc.schema.GetCompanyManagementFeesRequest
import com.multiplier.payable.grpc.schema.GetCompanyManagementFeesResponse
import com.multiplier.payable.grpc.schema.GetCompanyPayableByIdRequest
import com.multiplier.payable.grpc.schema.GetCompanyPayableByIdResponse
import com.multiplier.payable.grpc.schema.GetCompanyPayablesByIdsRequest
import com.multiplier.payable.grpc.schema.GetCompanyPayablesByIdsResponse
import com.multiplier.payable.grpc.schema.GetCompanyPricingRequest
import com.multiplier.payable.grpc.schema.GetCompanyPricingResponse
import com.multiplier.payable.grpc.schema.GetCountryPricingRequest
import com.multiplier.payable.grpc.schema.GetCountryPricingResponse
import com.multiplier.payable.grpc.schema.GetDepositTermForCompaniesRequest
import com.multiplier.payable.grpc.schema.GetDepositTermForCompaniesResponse
import com.multiplier.payable.grpc.schema.GetDiscountTermsInput
import com.multiplier.payable.grpc.schema.GetDiscountTermsOutput
import com.multiplier.payable.grpc.schema.GetEmployeePricingInput
import com.multiplier.payable.grpc.schema.GetEmployeePricingOutput
import com.multiplier.payable.grpc.schema.GetGlobalPricingInput
import com.multiplier.payable.grpc.schema.GetGlobalPricingOutput
import com.multiplier.payable.grpc.schema.GetInvoiceFileRequest
import com.multiplier.payable.grpc.schema.GetInvoiceFileResponse
import com.multiplier.payable.grpc.schema.GetInvoiceLinkRequest
import com.multiplier.payable.grpc.schema.GetInvoiceLinkResponse
import com.multiplier.payable.grpc.schema.GetInvoicesByFiltersRequest
import com.multiplier.payable.grpc.schema.GetInvoicesByFiltersResponse
import com.multiplier.payable.grpc.schema.GetInvoicesByIdsRequest
import com.multiplier.payable.grpc.schema.GetInvoicesByIdsResponse
import com.multiplier.payable.grpc.schema.GetManagementFeeByCountryAndContractTypeRequest
import com.multiplier.payable.grpc.schema.GetManagementFeeByCountryAndContractTypeResponse
import com.multiplier.payable.grpc.schema.GetPricingInput
import com.multiplier.payable.grpc.schema.GetRegionPricingRequest
import com.multiplier.payable.grpc.schema.GetRegionPricingResponse
import com.multiplier.payable.grpc.schema.GrpcCompanyPayable
import com.multiplier.payable.grpc.schema.GrpcCompanyPayableType
import com.multiplier.payable.grpc.schema.GrpcCreatePricingInput
import com.multiplier.payable.grpc.schema.GrpcDepositTerm
import com.multiplier.payable.grpc.schema.GrpcGenerateCompanyPayableFromEngineV2BlockingResponse
import com.multiplier.payable.grpc.schema.GrpcGenerateCompanyPayableFromEngineV2Request
import com.multiplier.payable.grpc.schema.GrpcInvoiceById
import com.multiplier.payable.grpc.schema.GrpcMonthYear
import com.multiplier.payable.grpc.schema.GrpcPricing
import com.multiplier.payable.grpc.schema.IsDepositInvoicePaidRequest
import com.multiplier.payable.grpc.schema.IsDepositInvoicePaidResponse
import com.multiplier.payable.grpc.schema.PayableServiceGrpc
import com.multiplier.payable.grpc.schema.SendCompanyPayableToContactRequest
import com.multiplier.payable.grpc.schema.UpdateCompanyPricingRequest
import com.multiplier.payable.grpc.schema.UpdateExternalInvoiceProcessingFeeRequest
import com.multiplier.payable.grpc.schema.VoidFreelancerCompanyPayableRequest
import com.multiplier.payable.service.void.CompanyPayableVoidException
import com.multiplier.payable.service.void.VoidCompanyPayableService
import com.multiplier.payable.service.void.toGrpc
import com.multiplier.payable.types.CompanyPayableType
import com.multiplier.payable.types.ContractType
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.types.MonthYear
import com.multiplier.payable.types.PayableStatus
import com.multiplier.payable.types.Pricing
import io.grpc.Status
import io.grpc.stub.StreamObserver
import net.devh.boot.grpc.server.service.GrpcService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*
import java.util.stream.Collectors

@GrpcService
class GrpcPayableService(
    val internalPayableService: PayableServiceAdapter,
    val grpcPayableMapper: GrpcPayableMapper,
    val grpcPricingMapper: GrpcPricingMapper,
    val grpcManagementFeeMapper: GrpcManagementFeeMapper,
    val pricingServiceAdapter: PricingServiceAdapter,
    val orderFormPricingService: OrderFormPricingService,
    @Qualifier("async") val asyncGenerateTransactionCommand: GenerateFinancialTransactionCommand,
    @Qualifier("sync") val blockingGenerateTransactionCommand: GenerateFinancialTransactionCommand,
    @Qualifier("sync") private val blockingGenerateExpenseBillTransactionCommand: GenerateExpenseBillTransactionCommand,
    @Qualifier("async") private val asyncGenerateExpenseBillTransactionCommand: GenerateExpenseBillTransactionCommand,
    val featureFlagService: FeatureFlagService,
    val grpcFinancialTransactionMapper: GrpcFinancialTransactionMapper,
    val grpcDateTimeMapper: GrpcDateTimeMapper,
    val invoiceDtoToGrpcInvoiceMapper: InvoiceDtoToGrpcInvoiceMapper,
    private val companyPayableGenerationFromEngineV2BlockingOrchestrator: CompanyPayableGenerationFromEngineV2BlockingOrchestrator,
    val voidCompanyPayableService: VoidCompanyPayableService,
    private val grpcPayableMapperV2: GrpcPayableMapperV2,
    private val grpcInvoiceMapper: GrpcInvoiceMapper,
    private val invoiceFetcherProvider: InvoiceFetcherProvider
) : PayableServiceGrpc.PayableServiceImplBase() {
    private val log = LoggerFactory.getLogger(GrpcPayableService::class.java)

    companion object {
        val PAYABLE_GRPC_MAPPER = "get-company-payable-grpc-mapper"
    }

    override fun getCompanyPayableById(
        request: GetCompanyPayableByIdRequest?,
        responseObserver: StreamObserver<GetCompanyPayableByIdResponse>?,
    ) {
        log.info("Method getCompanyPayableById called in company-service")

        try {
            var companyPayableId = request?.companyPayableId
            val companyPayable = internalPayableService.getCompanyPayableById(companyPayableId)

            val grpcCompanyPayable =
                if (companyPayable != null) {
                    if (checkIfPayableMapperV2Enabled()) {
                        grpcPayableMapperV2.mapGraphToGrpc(companyPayable)
                    } else {
                        grpcPayableMapper.map(companyPayable)
                    }
                } else {
                    GrpcCompanyPayable.getDefaultInstance()
                }

            responseObserver?.onNext(
                GetCompanyPayableByIdResponse.newBuilder()
                    .setCompanyPayable(grpcCompanyPayable)
                    .build(),
            )

            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing getCompanyPayableById", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getCompanyPayablesByIds(
        request: GetCompanyPayablesByIdsRequest?,
        responseObserver: StreamObserver<GetCompanyPayablesByIdsResponse>?,
    ) {
        log.info("Method getCompanyPayablesByIds called in company-service")

        try {
            var companyPayableByIds = request?.companyPayableIdList

            var companyPayables = internalPayableService.getCompanyPayablesByIds(companyPayableByIds?.toSet())

            var grpcCompanyPayables = grpcPayableMapper.mapCompanyPayables(companyPayables ?: listOf())

            responseObserver?.onNext(
                GetCompanyPayablesByIdsResponse.newBuilder()
                    .addAllCompanyPayable(grpcCompanyPayables)
                    .build(),
            )

            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing getCompanyPayablesByIds", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }


    override fun voidFreelancerCompanyPayable(
        request: VoidFreelancerCompanyPayableRequest,
        responseObserver: StreamObserver<Empty>
    ) {
        log.info("Method voidFreelancerCompanyPayable")
        try {
            internalPayableService.voidFreelancerCompanyPayable(request.freelancerCompanyPayableId)
            responseObserver.onNext(Empty.getDefaultInstance())
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error(
                "Exception occurred while executing voidFreelancerCompanyPayable for flCompanyPayable=${request.freelancerCompanyPayableId}",
                e
            )
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    @Deprecated("[date: 06082024] Deprecated as no one should directly update the status of a company payable")
    override fun bulkUpdateCompanyPayableStatus(
        request: BulkUpdateCompanyPayableStatusRequest?,
        responseObserver: StreamObserver<Empty>?,
    ) {
        log.info("Method bulkUpdateCompanyPayableStatus called in company-service")

        try {
            var companyPayableByIds = request?.companyPayableIdsList

            var companyPayableStatus = request?.status?.name?.let { PayableStatus.valueOf(it) }

            internalPayableService.bulkUpdateCompanyPayableStatus(companyPayableByIds?.toSet(), companyPayableStatus)
            responseObserver?.onNext(Empty.getDefaultInstance())
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing bulkUpdateCompanyPayableStatus", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getBillingCurrencyCode(
        request: GetBillingCurrencyCodeRequest?,
        responseObserver: StreamObserver<GetBillingCurrencyCodeResponse>?,
    ) {
        log.info("Method getBillingCurrencyCode called in company-service")

        try {
            var companyId = request?.companyId

            var currencyCode = internalPayableService.getBillingCurrencyCode(companyId)

            var grpcCurrency =
                if (currencyCode == null) {
                    Currency.CurrencyCode.NULL_CURRENCY
                } else {
                    Currency.CurrencyCode.valueOf(currencyCode.name)
                }

            responseObserver?.onNext(
                GetBillingCurrencyCodeResponse.newBuilder()
                    .setBillingCurrency(grpcCurrency.name)
                    .build(),
            )

            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing getBillingCurrencyCode", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getManagementFeesEstimation(
        request: GetCompanyManagementFeesEstimationRequest?,
        responseObserver: StreamObserver<GetCompanyManagementFeesResponse>?
    ) {
        return getCompanyManagementFees(
            request?.companyId,
            request?.contractIdsList ?: emptyList(),
            request?.monthYear,
            request?.isForced ?: false,
            request?.includeOnboarding ?: false,
            responseObserver
        )
    }

    override fun getCompanyManagementFees(
        request: GetCompanyManagementFeesRequest?,
        responseObserver: StreamObserver<GetCompanyManagementFeesResponse>?,
    ) {
        requireNotNull(request) { "The request cannot be null." }
        return getCompanyManagementFees(
            request.companyId, request.contractIdsList, request.monthYear, request.isForced,
            false, responseObserver
        )
    }

    private fun getCompanyManagementFees(
        companyId: Long?, contractIds: List<Long>, monthYear: GrpcMonthYear?, isForced: Boolean,
        includeOnboarding: Boolean,
        responseObserver: StreamObserver<GetCompanyManagementFeesResponse>?,
    ) {
        log.info("Method getCompanyManagementFees called in payable-service")

        try {

            val refMonthYear =
                MonthYear.newBuilder()
                    .year(monthYear?.year).month(monthYear?.month).build()

            val managementFees = if (includeOnboarding) {
                internalPayableService.getCompanyManagementFeesEstimation(
                    companyId,
                    contractIds,
                    refMonthYear,
                )
            } else {
                internalPayableService.getCompanyManagementFees(companyId, contractIds, refMonthYear, isForced)

            }

            var grpcManagementFees =
                managementFees?.stream()
                    ?.map { mf -> grpcManagementFeeMapper.map(mf) }
                    ?.collect(Collectors.toList())

            responseObserver?.onNext(
                GetCompanyManagementFeesResponse.newBuilder()
                    .addAllManagementFees(grpcManagementFees)
                    .build(),
            )

            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing getCompanyManagementFees. Exception:  $e")
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getManagementFeeByCountryAndContractType(
        request: GetManagementFeeByCountryAndContractTypeRequest?,
        responseObserver: StreamObserver<GetManagementFeeByCountryAndContractTypeResponse>?,
    ) {
        log.info("Method getManagementFeeByCountryAndContractType called in company-service")

        try {
            var companyId = request?.companyId
            var countryCode = request?.countryCode
            var contractType = request?.contractType

            var managementFee =
                internalPayableService.getManagementFeeByCountryAndContractType(
                    companyId,
                    countryCode?.let { CountryCode.valueOf(it) },
                    contractType?.let { ContractType.valueOf(it) },
                )

            responseObserver?.onNext(
                GetManagementFeeByCountryAndContractTypeResponse.newBuilder()
                    .apply {
                        if (managementFee != null) {
                            setManagementFee(DoubleValue.of(managementFee))
                        }
                    }
                    .build(),
            )

            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing getManagementFeeByCountryAndContractType. Exception:  $e")
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun generateDepositForContract(
        request: GenerateDepositForContractRequest,
        responseObserver: StreamObserver<GenerateDepositForContractResponse>,
    ) {
        log.info("Method generateDepositForContract called in company-service")

        try {
            val companyPayable = internalPayableService.generateDepositForContract(request.mapToDomain())

            if (companyPayable == null) {
                responseObserver.onNext(GenerateDepositForContractResponse.getDefaultInstance())
            } else {
                responseObserver.onNext(
                    GenerateDepositForContractResponse.newBuilder()
                        .setCompanyPayable(grpcPayableMapper.map(companyPayable)).build()
                )
            }

            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing generateDepositForContract", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun sendCompanyPayableToContact(
        request: SendCompanyPayableToContactRequest?,
        responseObserver: StreamObserver<Empty>?,
    ) {
        log.info("Method sendCompanyPayableToContact called in company-service")

        try {
            var contractId = request?.contractId

            internalPayableService.sendCompanyPayableToContact(contractId)
            responseObserver?.onNext(Empty.getDefaultInstance())
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing sendCompanyPayableToContact", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun deleteOrVoidContractDepositPayables(
        request: DeleteOrVoidContractDepositPayablesRequest?,
        responseObserver: StreamObserver<Empty>?,
    ) {
        log.info("Method deleteOrVoidContractDepositPayables called in company-service")

        try {
            var contractId = request?.contractId

            internalPayableService.deleteOrVoidContractDepositPayables(contractId)

            responseObserver?.onNext(Empty.getDefaultInstance())
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing deleteOrVoidContractDepositPayables", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun bulkUpdateCompanyPayableTotalAmount(
        request: BulkUpdateCompanyPayableTotalAmountRequest?,
        responseObserver: StreamObserver<Empty>?,
    ) {
        log.info("Method bulkUpdateCompanyPayableTotalAmount called in company-service")

        try {
            var companyIdToTotalAmountMap = request?.companyPayableIdToTotalAmountMap

            internalPayableService.bulkUpdateCompanyPayableTotalAmount(companyIdToTotalAmountMap?.toMap())
            responseObserver?.onNext(Empty.getDefaultInstance())
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing bulkUpdateCompanyPayableTotalAmount", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun createExternalInvoiceForMemberPayables(
        request: CreateExternalInvoiceForMemberPayablesRequest,
        responseObserver: StreamObserver<ExternalInvoiceCreationResponse>,
    ) {
        log.info("Method createExternalInvoiceForMemberPayables called in company-service")

        try {
            var freelancerInvoiceLineItems = request.freelancerInvoiceLineItemsList
            var companyId: Long? = null

            if (request.hasCompanyId()) {
                companyId = request.companyId
            }

            var externalInvoiceCreationResponse =
                internalPayableService.createExternalInvoiceForMemberPayables(
                    freelancerInvoiceLineItems?.toList(),
                    companyId,
                )

            responseObserver.onNext(externalInvoiceCreationResponse)
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing createExternalInvoiceForMemberPayables", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun deleteOrVoidExternalInvoice(
        request: DeleteOrVoidExternalInvoiceRequest,
        responseObserver: StreamObserver<Empty>?,
    ) {
        log.info("Method deleteOrVoidExternalInvoice called in company-service")

        try {
            val invoiceId = request.invoiceId
            internalPayableService.deleteOrVoidExternalInvoice(invoiceId)

            responseObserver?.onNext(Empty.getDefaultInstance())
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing deleteOrVoidExternalInvoice", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun addFullPaymentToExternalInvoices(
        request: AddFullPaymentToExternalInvoicesRequest?,
        responseObserver: StreamObserver<ExternalInvoiceAddPaymentResponse>?,
    ) {
        log.info("Method addFullPaymentToExternalInvoices called in company-service")

        try {
            var companyPayableIds = request?.companyPayableIdsList
            var paidToAccountCode = request?.paidToAccountCode
            val paymentDateInSingaporeTimezone = request?.paymentDateInSingaporeTimezone

            val addPaymentResponse =
                internalPayableService.addFullPaymentToExternalInvoices(
                    companyPayableIds?.toSet(),
                    paidToAccountCode,
                    paymentDateInSingaporeTimezone?.toCalendar(),
                )

            responseObserver?.onNext(addPaymentResponse)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing addFullPaymentToExternalInvoices", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun updateExternalInvoiceProcessingFee(
        request: UpdateExternalInvoiceProcessingFeeRequest?,
        responseObserver: StreamObserver<Empty>?,
    ) {
        log.info("Method updateExternalInvoiceProcessingFee called in company-service")

        try {
            var invoiceId = request?.invoiceId
            var billingCurrency = request?.billingCurrency
            var processingFeeLineItem = request?.processingFeeLineItem

            internalPayableService.updateExternalInvoiceProcessingFee(invoiceId, billingCurrency, processingFeeLineItem)
            responseObserver?.onNext(Empty.getDefaultInstance())
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing updateExternalInvoiceProcessingFee", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getInvoiceLink(
        request: GetInvoiceLinkRequest,
        responseObserver: StreamObserver<GetInvoiceLinkResponse>?,
    ) {
        log.info("Method getInvoiceLink called in company-service")

        try {
            val invoiceId = request.invoiceId
            val invoiceLink = internalPayableService.getInvoiceLink(invoiceId)
            val response =
                GetInvoiceLinkResponse.newBuilder()
                    .setInvoiceLink(invoiceLink ?: "")
                    .build()

            responseObserver?.onNext(response)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing getInvoiceLink", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getInvoiceFile(
        request: GetInvoiceFileRequest,
        responseObserver: StreamObserver<GetInvoiceFileResponse>?,
    ) {
        log.info("Method getInvoiceFile called in company-service")
        try {
            val invoiceId = request.invoiceId
            val invoiceFile = internalPayableService.getInvoiceFile(invoiceId)
            val response =
                GetInvoiceFileResponse.newBuilder()
                    .setInvoiceFile(invoiceFile)
                    .build()

            responseObserver?.onNext(response)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing getInvoiceFile", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getDiscountTerms(
        request: GetDiscountTermsInput?,
        responseObserver: StreamObserver<GetDiscountTermsOutput>?,
    ) {
        log.info("Method getDiscountTerms is called in payable-service")

        try {
            val schemaDiscountTerms = pricingServiceAdapter.getDiscountTerms(request?.companyId)
            val outerDiscountTerm = schemaDiscountTerms.map { item -> grpcPayableMapper.mapDiscountTerm(item) }
            val resp =
                GetDiscountTermsOutput.newBuilder()
                    .addAllDiscountTerm(outerDiscountTerm)
                    .build()
            responseObserver?.onNext(resp)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred: ", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getIsDepositInvoicePaidByContractId(
        request: IsDepositInvoicePaidRequest,
        responseObserver: StreamObserver<IsDepositInvoicePaidResponse>?,
    ) {
        log.info("Method getIsDepositInvoicePaidByContractId called in company-service")
        try {
            val contractId = request.contractId
            val response = internalPayableService.getIsDepositPaidForContractId(contractId)

            responseObserver?.onNext(response)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred while executing getIsDepositInvoicePaidByContractId", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getEmployeePricing(
        request: GetEmployeePricingInput?,
        responseObserver: StreamObserver<GetEmployeePricingOutput>?,
    ) {
        log.info("Method getEmployeePricing is called in payable-service")
        try {
            val schemaEmployeePricing = pricingServiceAdapter.getEmployeePricing(request?.companyId)
            val outerEmployeePricing =
                schemaEmployeePricing.map { item -> grpcPayableMapper.mapEmployeePricing(item) }
            val resp =
                GetEmployeePricingOutput.newBuilder()
                    .addAllEmployeePricing(outerEmployeePricing)
                    .build()
            responseObserver?.onNext(resp)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred: ", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getPricing(
        request: GetPricingInput?,
        responseObserver: StreamObserver<GrpcPricing>?,
    ) {
        log.info("Method getPricing is called in payable-service")
        try {
            val schemaPricing = pricingServiceAdapter.getPricing(request?.companyId)
            if (schemaPricing == null) {
                log.error("Pricing not found for companyId: ${request?.companyId}")
                responseObserver?.onError(
                    GrpcExceptionWrapper(
                        Status.NOT_FOUND.withDescription("Pricing not found for companyId: ${request?.companyId}"),
                        IllegalStateException("Pricing not found for companyId: ${request?.companyId}")
                    )
                )
                return
            }
            log.info("SchemaPricing: $schemaPricing")

            val grpcPricing = grpcPricingMapper.map(schemaPricing)
            log.info("OuterPricing: $grpcPricing")

            responseObserver?.onNext(grpcPricing)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Fail to get pricing: ", e)
            responseObserver?.onError(
                GrpcExceptionWrapper(
                    Status.INTERNAL.withDescription("Fail to get pricing"), e
                )
            )
        }
    }

    override fun getGlobalPricing(
        request: GetGlobalPricingInput?,
        responseObserver: StreamObserver<GetGlobalPricingOutput>?,
    ) {
        log.info("Method getGlobalPricing is called in payable-service")
        try {
            val schemaGlobalPricings = pricingServiceAdapter.getGlobalPricing(request?.companyId)
            val outerGlobalPricings = schemaGlobalPricings.map { item -> grpcPayableMapper.mapGlobalPricing(item) }
            val resp =
                GetGlobalPricingOutput.newBuilder()
                    .addAllGlobalPricings(outerGlobalPricings)
                    .build()
            responseObserver?.onNext(resp)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred: ", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun createPricing(
        request: GrpcCreatePricingInput?,
        responseObserver: StreamObserver<Empty>?,
    ) {
        log.info("Method createPricing is called in payable-service")
        try {
            val schemaPricingInput = grpcPayableMapper.mapPricingInput(request?.pricingInput!!)
            pricingServiceAdapter.createPricing(schemaPricingInput, request.companyId)
            responseObserver?.onNext(Empty.getDefaultInstance())
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred: ", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getCountryPricing(
        request: GetCountryPricingRequest?,
        responseObserver: StreamObserver<GetCountryPricingResponse>?,
    ) {
        log.info("Method getCountryPricing is called in payable-service")
        try {
            val schemaCountryPricing =
                pricingServiceAdapter.getCountryPricing(CountryCode.valueOf(request?.countryCode!!))
            val outerCountryPricing =
                schemaCountryPricing.map { item -> grpcPayableMapper.mapEmployeePricing(item) }
            val resp =
                GetCountryPricingResponse.newBuilder()
                    .addAllEmployeePricings(outerCountryPricing)
                    .build()
            responseObserver?.onNext(resp)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred: ", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getCompanyPricing(
        request: GetCompanyPricingRequest?,
        responseObserver: StreamObserver<GetCompanyPricingResponse>?,
    ) {
        log.info("Method getCompanyPricing is called in payable-service")
        try {
            val schemaCompanyPricing = pricingServiceAdapter.getCompanyPricing(request?.companyId)
            val outerCompanyPricing = grpcPricingMapper.map(schemaCompanyPricing)
            val resp =
                GetCompanyPricingResponse.newBuilder()
                    .setCompanyPricing(outerCompanyPricing)
                    .build()
            responseObserver?.onNext(resp)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred: ", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    private fun Pricing.toGrpcDepositTerm(): GrpcDepositTerm {
        val depositTerm = this.depositTerm

        val builder = GrpcDepositTerm.newBuilder()

        if (depositTerm != null) {
            depositTerm.termInMonths?.let {
                if (it > 0) {
                    builder.setTermInMonths(it)
                }
            }
            depositTerm.maxDeposit?.let {
                if (it > 0) {
                    builder.setMaxDeposit(it)
                }
            }
        }

        return builder.build()
    }

    private fun Map.Entry<Long, Pricing>.toGrpcDepositTerm(): CompanyPricingEntry {
        return CompanyPricingEntry.newBuilder()
            .setCompanyId(this.key)
            .setDepositTerm(this.value.toGrpcDepositTerm())
            .build()
    }

    override fun getDepositTermForCompanies(
        request: GetDepositTermForCompaniesRequest?,
        responseObserver: StreamObserver<GetDepositTermForCompaniesResponse>?,
    ) {
        log.info("Method getDepositTermForCompanies is called in payable-service")
        try {
            val companyIds = request?.companyIdsList.orEmpty()

            val companyDepositMapping = pricingServiceAdapter.getDepositForCompanies(companyIds)

            val response =
                GetDepositTermForCompaniesResponse.newBuilder()
                    .addAllCompanyPricingEntries(
                        companyDepositMapping
                            .map { it.toGrpcDepositTerm() },
                    )
                    .build()

            responseObserver?.onNext(response)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred: ", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getRegionPricing(
        request: GetRegionPricingRequest?,
        responseObserver: StreamObserver<GetRegionPricingResponse>?,
    ) {
        log.info("Method getRegionPricing is called in payable-service")
        try {
            val schemaRegionPricing =
                pricingServiceAdapter.getRegionPricing(grpcPayableMapper.mapRegion(request?.region!!))
            val outerRegionPricing = schemaRegionPricing.map { item -> grpcPayableMapper.mapEmployeePricing(item) }
            val resp =
                GetRegionPricingResponse.newBuilder()
                    .addAllEmployeePricings(outerRegionPricing)
                    .build()
            responseObserver?.onNext(resp)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred: ", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun updateCompanyPricing(
        request: UpdateCompanyPricingRequest,
        responseObserver: StreamObserver<GrpcPricing>?,
    ) {
        log.info("Method updateCompanyPricing is called in payable-service")
        try {
            val companyId = request.companyId
            val companySfdAccountNo = request.companySfdcAccountNo
            val shouldCreateDraftMsaAddendum = request.shouldCreateDraftMsaAddendum
            val pricingInput = grpcPayableMapper.mapPricingInput(request.pricingInput!!)
            val result = pricingServiceAdapter.updateCompanyPricingExternalGrpc(
                companyId,
                pricingInput,
                companySfdAccountNo,
                shouldCreateDraftMsaAddendum!!,
            )
            val resp = grpcPricingMapper.map(result)
            responseObserver?.onNext(resp)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred: ", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getInvoicesByFilters(
        request: GetInvoicesByFiltersRequest,
        responseObserver: StreamObserver<GetInvoicesByFiltersResponse>,
    ) {
        log.info("GetCompanyPayablesByCondition called with parameters = {}", request)
        try {
            val invoiceDtos = when (request.customFiltersCase) {
                GetInvoicesByFiltersRequest.CustomFiltersCase.CUSTOMFILTERS_NOT_SET -> {
                    val invoiceFilters = fromGrpcInvoiceFilters(request)
                    internalPayableService.getInvoicesByFilters(invoiceFilters)
                }
                GetInvoicesByFiltersRequest.CustomFiltersCase.DEPOSITINVOICEFILTER -> {
                    val filter = request.depositInvoiceFilter.toDepositInvoiceFilter()
                    val fetcher = invoiceFetcherProvider.getByFilter(DepositInvoiceFilter::class)
                    fetcher.fetch(filter)
                }
            }


            // return the response to grpc domain
            val grpcInvoices = invoiceDtoToGrpcInvoiceMapper.map(invoiceDtos)

            responseObserver.onNext(
                GetInvoicesByFiltersResponse.newBuilder()
                    .addAllInvoices(grpcInvoices)
                    .build(),
            )

            responseObserver.onCompleted()
            log.info("Returned the invoices = {} for request = {}", grpcInvoices.size, request)
        } catch (e: Exception) {
            log.error("[getCompanyPayablesByConditions] Exception occurred while return payables", e)
            responseObserver.onError(e)
        }
    }

    private fun Timestamp.toCalendar(): Calendar? {
        val zonedDateTime =
            Instant.ofEpochSecond(seconds, nanos.toLong())
                .atZone(ZoneId.of("Asia/Singapore"))
        return Calendar.getInstance(TimeZone.getTimeZone(ZoneId.of("Asia/Singapore"))).apply {
            timeInMillis = zonedDateTime.toInstant().toEpochMilli()
        }
    }

    override fun generateAnnualPlanInvoice(
        request: GenerateAnnualPlanRequest,
        responseObserver: StreamObserver<GenerateFinancialTransactionResponse>?,
    ) {
        log.info("Method generateFinancialTransaction is called in payable-service")

        try {
            val commands: List<InvoiceCommand>;
            if (enableAsyncAnnualPlanInvoiceGeneration()) {
                commands = asyncGenerateTransactionCommand.generate(
                    transactionType = TransactionType.ANNUAL_PLAN_INVOICE,
                    companyIds = request.companyIdsList,
                    dateRange = grpcDateTimeMapper.toDateRange(request.dateRange),
                    transactionDate = LocalDateTime.now(),
                    cycle = InvoiceCycle.YEARLY,
                    forcedContractIdsByCompanyId = emptyMap(),
                    autoSubmit = false,
                    null,
                    emptyMap(),
                    null,
                    incidentsInvoiceCommand = null,
                    preference = null,
                )
            } else {
                commands = blockingGenerateTransactionCommand.generate(
                    transactionType = TransactionType.ANNUAL_PLAN_INVOICE,
                    companyIds = request.companyIdsList,
                    dateRange = grpcDateTimeMapper.toDateRange(request.dateRange),
                    transactionDate = LocalDateTime.now(),
                    cycle = InvoiceCycle.YEARLY,
                    forcedContractIdsByCompanyId = emptyMap(),
                    autoSubmit = false,
                    null,
                    emptyMap(),
                    null,
                    incidentsInvoiceCommand = null,
                    preference = null,
                )
            }

            val responseCommands = commands.map { grpcFinancialTransactionMapper.toGrpcTransactionCommand(it) }

            val response =
                GenerateFinancialTransactionResponse.newBuilder()
                    .addAllCommands(responseCommands)
                    .build()

            responseObserver?.onNext(response)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred: ", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun generateCompanyPayableFromEngineV2Blocking(
        request: GrpcGenerateCompanyPayableFromEngineV2Request,
        responseObserver: StreamObserver<GrpcGenerateCompanyPayableFromEngineV2BlockingResponse>,
    ) {
        log.info("Method generateCompanyPayableFromEngineV2Sync is called in payable-service")
        try {

            val response = companyPayableGenerationFromEngineV2BlockingOrchestrator.orchestrate(request)

            log.info("Generated company payable for request: $request. Response size = ${response.companyPayableCount}")
            responseObserver.onNext(response)
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred: ", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    /**
     * this is upsertEORPricing. This will only take the parameters for EOR pricing.
     */
    override fun upsertPricing(
        request: GrpcCreatePricingInput,
        responseObserver: StreamObserver<GrpcPricing>?
    ) {
        log.info("Method upsertPricing is called in payable-service")

        try {
            log.info("Upsert pricing for companyId: ${request.companyId} and grpc input = ${request.pricingInput}")
            val pricingInput = request.pricingInput.toDomain()
            log.info("Mapped pricing from grpc is $pricingInput")
            val response = orderFormPricingService.backfillPricingFromOrderForm(request.companyId, pricingInput)

            responseObserver?.onNext(grpcPricingMapper.map(response))
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred: ", e)
            responseObserver?.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    private fun enableAsyncAnnualPlanInvoiceGeneration(): Boolean {
        val feature = featureFlagService.feature("enable-async-annual-plan-invoice-generation", emptyMap())
        return feature.on
    }

    override fun voidCompanyPayable(
        request: CompanyPayableVoidRequest,
        responseObserver: StreamObserver<CompanyPayableVoidResponse>
    ) {
        try {
            log.info("Method voidCompanyPayable is called in payable-service")
            val voidedCompanyPayable = voidCompanyPayableService.void(request.companyPayableId, request.type.toDomain())
            responseObserver.onNext(
                CompanyPayableVoidResponse
                    .newBuilder()
                    .setSuccess(
                        CompanyPayableVoidSuccessResponse
                            .newBuilder()
                            .setCompanyPayable(grpcPayableMapper.map(voidedCompanyPayable))
                            .build(),
                    ).build(),
            )
            responseObserver.onCompleted()
        } catch (re: CompanyPayableVoidException) {
            log.warn("CompanyPayableVoidException occurred: ${re.reason}", re)
            responseObserver.onNext(
                CompanyPayableVoidResponse
                    .newBuilder()
                    .setError(
                        CompanyPayableVoidErrorResponse
                            .newBuilder()
                            .setReason(re.reason.toGrpc())
                            .build(),
                    ).build(),
            )
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("Exception occurred: ", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    private fun checkIfPayableMapperV2Enabled(): Boolean {
        return featureFlagService.feature(PAYABLE_GRPC_MAPPER, mapOf())
            .on
            .also {
                log.info("Feature flag get-company-payable-grpc-mapper is $it")
            }
    }

    override fun generateServiceInvoice(
        request: GenerateServiceInvoiceRequest?,
        responseObserver: StreamObserver<GenerateFinancialTransactionResponse>?
    ) {
        log.info("Method generateServiceInvoice is called in payable-service")

        // Validate the request
        if (request == null || request.companyIdsList.isEmpty() || request.dateRange == null) {
            log.error("Invalid request: companyIds or dateRange is missing")
            responseObserver?.onError(
                IllegalArgumentException("Request must contain at least one companyId and a valid dateRange")
            )
            return
        }

        val companyIds = request.companyIdsList
        val dateRange = grpcDateTimeMapper.toDateRange(request.dateRange)

        try {
            val commands = blockingGenerateTransactionCommand.generate(
                transactionType = TransactionType.GP_SERVICE_INVOICE,
                companyIds = companyIds,
                dateRange = dateRange,
                transactionDate = LocalDateTime.now(),
                cycle = InvoiceCycle.MONTHLY,
                forcedContractIdsByCompanyId = emptyMap(),
                autoSubmit = false,
                null,
                emptyMap(),
                null,
                incidentsInvoiceCommand = null,
                preference = null,
            )

            val responseCommands = commands.map { grpcFinancialTransactionMapper.toGrpcTransactionCommand(it) }

            val response = GenerateFinancialTransactionResponse.newBuilder()
                .addAllCommands(responseCommands)
                .build()

            responseObserver?.onNext(response)
            responseObserver?.onCompleted()

        } catch (e: Exception) {
            log.error("Error generating service invoice", e)
            responseObserver?.onError(e)
        }
    }

    override fun generateVendorBillInvoice(
        request: GenerateVendorBillInvoiceRequest,
        responseObserver: StreamObserver<GenerateVendorBillInvoiceResponse>?
    ) {
        log.info(" Method generateVendorBillInvoice is called in payable-service")

        try {
            val countryCode = com.multiplier.core.payable.adapters.netsuite.models.CountryCode.getCountryCodeWithoutPrefix(request.countryCode.name)
            val command = blockingGenerateExpenseBillTransactionCommand.generate(
                payrollCycleId = request.payrollCycleId,
                countryCode = countryCode,
                transactionDate = LocalDateTime.now(),
                dueDate = null
            )

            val response = GenerateVendorBillInvoiceResponse.newBuilder()
                .setGenerated(true)
                .setErrorMessage("")
                .build()
            responseObserver?.onNext(response)
            responseObserver?.onCompleted()
        } catch (e: Exception) {
            log.error("Error generating vendor bill invoice", e)
            val response = GenerateVendorBillInvoiceResponse.newBuilder()
                .setGenerated(false)
                .setErrorMessage(e.message ?: "Unknown error occurred")
                .build()
            responseObserver?.onNext(response)
            responseObserver?.onCompleted()
        }
    }

    override fun getInvoicesByIds(
        request: GetInvoicesByIdsRequest,
        responseObserver: StreamObserver<GetInvoicesByIdsResponse>
    ) {
        log.info("[getInvoicesByIds] called in payable-service")

        try {

            val invoicesByIds = internalPayableService.getInvoicesByIds(request.idsList)
            val grpcInvoicesByIds = invoicesByIds.entries
                .map {
                    GrpcInvoiceById.newBuilder()
                        .setId(it.key)
                        .setGrpcInvoice(grpcInvoiceMapper.mapInvoiceDtoToGrpc(it.value))
                        .build()
                }
            responseObserver.onNext(
                GetInvoicesByIdsResponse.newBuilder()
                    .addAllGrpcInvoiceById(grpcInvoicesByIds)
                    .build()
            )
            responseObserver.onCompleted()

        }catch (e: Exception){
            log.error("Error fetching invoices", e)
            responseObserver.onError(e)
        }
    }

    private fun GrpcCompanyPayableType.toDomain(): CompanyPayableType =
        when (this) {
            GrpcCompanyPayableType.ANNUAL_PLAN -> CompanyPayableType.ANNUAL_PLAN
            GrpcCompanyPayableType.ANNUAL_PLAN_AOR -> CompanyPayableType.ANNUAL_PLAN_AOR
            GrpcCompanyPayableType.UNKNOWN_COMPANY_PAYABLE_TYPE -> CompanyPayableType.UNKNOWN
            GrpcCompanyPayableType.FIRST_INVOICE -> CompanyPayableType.FIRST_INVOICE
            GrpcCompanyPayableType.SECOND_INVOICE -> CompanyPayableType.SECOND_INVOICE
            GrpcCompanyPayableType.FREELANCER_COMPANY_PAYABLE -> CompanyPayableType.FREELANCER
            GrpcCompanyPayableType.DEPOSIT -> CompanyPayableType.DEPOSIT
            GrpcCompanyPayableType.GLOBAL_PAYROLL_FUNDING -> CompanyPayableType.GLOBAL_PAYROLL_FUNDING
            GrpcCompanyPayableType.INSURANCE -> CompanyPayableType.INSURANCE
            GrpcCompanyPayableType.UNRECOGNIZED -> CompanyPayableType.ANNUAL_PLAN
            GrpcCompanyPayableType.GP_SERVICE_INVOICE -> CompanyPayableType.GP_SERVICE_INVOICE
            GrpcCompanyPayableType.VAS_INCIDENT_PAYABLE -> CompanyPayableType.VAS_INCIDENT_INVOICE
            GrpcCompanyPayableType.PAYROLL_OFFCYCLE_PAYABLE -> CompanyPayableType.PAYROLL_OFFCYCLE_INVOICE
            GrpcCompanyPayableType.VAS_BACKGROUND_VERIFICATION_PAYABLE -> CompanyPayableType.VAS_BACKGROUND_VERIFICATION_INVOICE
        }

    private fun Date.toLocalDate(): LocalDate {
        return LocalDate.of(year, month, day)
    }

    private fun GenerateDepositForContractRequest.mapToDomain() = GenerateDepositForContractInput(
        contractId = contractId,
        companyId = companyId,
        memberId = memberId,
        contractType = ContractType.valueOf(contractType),
        countryCode = CountryCode.valueOf(countryCode),
        currencyCode = CurrencyCode.valueOf(currencyCode),
        startOn = startOn.toLocalDate(),
        depositDueDate = if (hasDepositDueDate()) depositDueDate.toLocalDate() else null,
    )
}