package com.multiplier.core.payable.adapters

import com.multiplier.core.payable.docgen.DocGenRestClient
import com.multiplier.core.payable.docgen.model.DocumentResponse
import com.multiplier.core.payable.docgen.model.DocumentShareRequest
import com.multiplier.core.payable.docgen.model.DocumentShareResponse
import org.springframework.stereotype.Service

interface DocgenServiceAdapter {
    fun getDocument(id: Long): DocumentResponse
    fun shareDocument(documentId: Long?, value: DocumentShareRequest?): DocumentShareResponse?
}

@Service
class DefaultDocgenServiceAdapterImpl(val docgenClient: DocGenRestClient) : DocgenServiceAdapter {

    override fun getDocument(id: Long): DocumentResponse {
        return docgenClient.getDocument(id)
    }

    override fun shareDocument(documentId: Long?, value: DocumentShareRequest?): DocumentShareResponse? {
        return if (documentId == null) {
            null
        } else docgenClient.shareDocument(documentId, value)
    }
}
