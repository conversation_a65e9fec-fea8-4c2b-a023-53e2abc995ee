package com.multiplier.core.payable.adapters.order

import com.multiplier.core.mapper.order.CompanyOrderGrpcToDomainMapper
import com.multiplier.productcatalogue.grpc.schema.order.CompanyOrderServiceGrpc.CompanyOrderServiceBlockingStub
import com.multiplier.productcatalogue.grpc.schema.order.GetCompanyOrderByIdRequest
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Component

@Component
class CompanyOrderAdapter(
    @GrpcClient("product-catalogue-service")
    private val stub: CompanyOrderServiceBlockingStub,
    private val companyOrderGrpcToDomainMapper: CompanyOrderGrpcToDomainMapper,
) {
    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    fun getCompanyOrder(orderId: Long): CompanyOrderWrapper {
        logger.info { "Fetching CompanyOrder with ID: $orderId" }

        val grpcRequest = GetCompanyOrderByIdRequest.newBuilder()
            .setOrderId(orderId)
            .build()

        return try {
            val grpcResponse = stub.getCompanyOrderById(grpcRequest)
            logger.info { "Successfully fetched and mapped CompanyOrder with ID: $orderId" }

            val domainCompanyOrder = companyOrderGrpcToDomainMapper.map(grpcResponse.companyOrder)
            domainCompanyOrder
        } catch (e: Exception) {
            logger.error(e) { "Error fetching CompanyOrder with ID: $orderId" }
            throw e
        }
    }
}