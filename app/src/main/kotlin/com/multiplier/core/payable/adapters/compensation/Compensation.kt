package com.multiplier.core.payable.adapters.compensation

import com.multiplier.payable.types.CurrencyCode

data class Compensation(
    val contractId: Long,
    val basePay: CompensationPayComponent,
    val otherPays: List<CompensationPayComponent>,
    val source: CompensationSource,
) {
    companion object {
        const val BASE_PAY = "basePay"
        const val MONTH_13TH = "13thMonth"
        const val MONTH_14TH = "14thMonth"
        const val PROBATION_BASE_PAY = "probationBasePay"
    }
}

data class CompensationPayComponent(
    val id: String,
    val name: String,
    val amount: Double,
    val currencyCode: CurrencyCode,
    val category: CompensationCategory,
    val frequency: RateFrequency,
    val payFrequency: PayFrequency,
    val rateType: RateType,
)

enum class CompensationCategory {
    CONTRACT_BASE_PAY,
    CONTRACT_BASE_PAY_BREAKUP,
    CONTRACT_ALLOWANCE,
    DEDUCTION,
    PAY_SUPPLEMENT,
    EMPLOYER_DEDUCTION,
    EMPLOYEE_DEDUCTION,
    EMPLOYER_CONTRIBUTION,
    EMPLOYEE_CONTRIBUTION,
    CATEGORY_MONTH_PAY_13TH_14TH,
    CONTRACT_BASE_PAY_ADDITIONAL,
}

enum class RateFrequency {
    ANNUALLY,
    ONETIME,
    SEMIANNUALLY,
    QUARTERLY,
    MONTHLY,
    SEMIMONTHLY,
    BIWEEKLY,
    WEEKLY,
    DAILY,
    HOURLY,
    //To support backward compatibility the ff enum are only from old compensation model
    BI_MONTHLY,
    TRI_ANNUALLY,
    CUSTOM,
}

enum class PayFrequency {
    ANNUALLY,
    SEMI_ANNUALLY,
    QUARTERLY,
    MONTHLY,
    SEMI_MONTHLY,
    BI_WEEKLY,
    WEEKLY,
    DAILY,
    ONE_TIME,
    NOT_SET, //to handle null pay frequency fromold compensation model
}

enum class RateType {
    GROSS,
    CTC,
    NET,
    NULL,
}

enum class CompensationSource {
    COMPENSATION_SERVICE,
    CONTRACT_SERVICE,
}