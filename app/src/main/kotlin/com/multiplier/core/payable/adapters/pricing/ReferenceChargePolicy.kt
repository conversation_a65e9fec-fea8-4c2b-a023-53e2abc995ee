package com.multiplier.core.payable.adapters.pricing

data class ReferenceChargePolicy(
    val targets: List<TargetReference>
) : ChargePolicy {
    override val chargeType: ChargeType
        get() = ChargeType.REFERENCE

    fun findTarget(lineCode: String, dimensions: Map<String, String>): TargetReference? {
        return targets.find {
            it.lineCode == lineCode
                    && it.dimensions == dimensions
        }
    }
}
