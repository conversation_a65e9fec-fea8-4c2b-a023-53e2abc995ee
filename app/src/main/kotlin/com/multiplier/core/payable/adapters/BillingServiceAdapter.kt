package com.multiplier.core.payable.adapters

import com.google.protobuf.Timestamp
import com.multiplier.billing.grpc.billing.Billing
import com.multiplier.billing.grpc.billing.Billing.BilledItem
import com.multiplier.billing.grpc.billing.Billing.BillingCommand
import com.multiplier.billing.grpc.billing.BillingServiceGrpc
import com.multiplier.common.exception.toBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.core.mapper.bill.BilledItemGrpcToDomainMapper
import com.multiplier.core.payable.adapters.billing.BilledItemWrapper
import com.multiplier.core.payable.adapters.billing.BillingError
import com.multiplier.core.payable.adapters.billing.BillingResult
import com.multiplier.core.payable.mapper.GrpcDateTimeMapper
import com.multiplier.grpc.common.time.v2.DurationOuterClass.Duration
import com.multiplier.payable.engine.domain.aggregates.DateRange
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface BillingServiceAdapter {
    fun getOrCreateBills(
        companyId: Long,
        entityId: Long? = null,
        duration: DateRange,
        lineCodes: Set<String>,
    ): Map<String, BillingResult>

    fun getOrCreateOrderFormAdvanceBills(
        companyId: Long,
        entityId: Long?,
        dateRange: DateRange
    ): List<BilledItemWrapper>

    fun getBilledItems(
        companyId: Long,
        startDateSeconds: Long,
        endDateSeconds: Long,
        excludeBillingIds: List<Long> = emptyList(),
        lineItemCodes: List<String> = emptyList(),
        entityId: Long? = null
    ): List<BilledItem>

    fun generateBills(
        companyId: Long,
        startDateSeconds: Long,
        endDateSeconds: Long,
        lineItemCodes: List<String> = emptyList()
    ): List<Billing.BillingResultPerLineCode>

    fun getBillsByIds(billIds: Set<Long>): List<BilledItemWrapper>

    fun deleteBills(billIds: List<Long>): List<Long>
}

@Service
class BillingServiceAdapterImpl(
    @GrpcClient("billing-service")
    private val billingServiceClient: BillingServiceGrpc.BillingServiceBlockingStub,
    private val grpcDateTimeMapper: GrpcDateTimeMapper,
    private val billedItemGrpcToDomainMapper: BilledItemGrpcToDomainMapper,
) : BillingServiceAdapter {

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    override fun getOrCreateBills(
        companyId: Long,
        entityId: Long?,
        duration: DateRange,
        lineCodes: Set<String>
    ): Map<String, BillingResult> {
        val requestBuilder = BillingCommand.newBuilder()
            .setCompanyId(companyId)
            .addAllLineCodes(lineCodes)
            .setDuration(
                Duration.newBuilder()
                    .setStartDate(grpcDateTimeMapper.toTimestamp(duration.startDate))
                    .setEndDate(grpcDateTimeMapper.toTimestamp(duration.endDate))
                    .build()
            )
        entityId?.let { requestBuilder.setEntityId(it) }
        val resp = billingServiceClient.generateBills(requestBuilder.build())
        return resp.resultsList.associate {
            it.lineCode to BillingResult(
                generationError = if (it.hasError()) BillingError(
                    errorCode = it.error.errorCode.name,
                    errorMessage = it.error.errorMessage,
                ) else null,
                billedItems = billedItemGrpcToDomainMapper.map(it.itemsList)
            )
        }
    }

    override fun getBilledItems(
        companyId: Long,
        startDateSeconds: Long,
        endDateSeconds: Long,
        excludeBillingIds: List<Long>,
        lineItemCodes: List<String>,
        entityId: Long?
    ): List<BilledItem> {
        logger.info {
            "Fetching billed items for companyId: $companyId, entityId: $entityId, duration: $startDateSeconds to $endDateSeconds, " +
                    "excludeBillingIds: $excludeBillingIds, lineItemCodes: $lineItemCodes"
        }

        try {
            val requestBuilder = Billing.GetBilledItemsRequest.newBuilder()
                .setCompanyId(companyId)
                .setDuration(
                    Duration.newBuilder()
                        .setStartDate(Timestamp.newBuilder().setSeconds(startDateSeconds).build())
                        .setEndDate(Timestamp.newBuilder().setSeconds(endDateSeconds).build())
                        .build()
                )
                .addAllExcludeBillingIds(excludeBillingIds)
                .addAllLineItemCodes(lineItemCodes)

            entityId?.let { requestBuilder.setEntityId(it) }

            val grpcBilledItemsResponse = billingServiceClient.getBilledItems(requestBuilder.build())
            logger.info { "Successfully fetched ${grpcBilledItemsResponse.billedItemsList.size} billed items." }
            return grpcBilledItemsResponse.billedItemsList
        } catch (e: Exception) {
            logger.error(e) { "Error fetching billed items for companyId: $companyId, entityId: $entityId, duration: $startDateSeconds to $endDateSeconds." }
            throw e
        }
    }

    override fun generateBills(
        companyId: Long,
        startDateSeconds: Long,
        endDateSeconds: Long,
        lineItemCodes: List<String>
    ): List<Billing.BillingResultPerLineCode> {
        logger.info {
            "Generating bills for companyId: $companyId, duration: $startDateSeconds to $endDateSeconds, " +
                    "lineItemCodes: $lineItemCodes"
        }

        try {
            val response = billingServiceClient.generateBills(
                BillingCommand.newBuilder()
                    .setCompanyId(companyId)
                    .setDuration(
                        Duration.newBuilder()
                            .setStartDate(Timestamp.newBuilder().setSeconds(startDateSeconds).build())
                            .setEndDate(Timestamp.newBuilder().setSeconds(endDateSeconds).build())
                            .build()
                    )
                    .addAllLineCodes(lineItemCodes)
                    .build()
            )
            logger.info { "Successfully generated bills for companyId: $companyId" }
            return response.resultsList
        } catch (e: Exception) {
            logger.error(e) { "Error generating bills for companyId: $companyId, duration: $startDateSeconds to $endDateSeconds." }
            throw e
        }
    }

    override fun getBillsByIds(billIds: Set<Long>): List<BilledItemWrapper> {
        logger.info { "Fetching bills by IDs: $billIds" }

        if (billIds.isEmpty()) {
            return emptyList()
        }

        try {
            val request = Billing.GetBilledItemsByIdsRequest.newBuilder()
                .addAllBilledItemIds(billIds)
                .build()

            val response = billingServiceClient.getBilledItemsByIds(request)
            val billedItems = billedItemGrpcToDomainMapper.map(response.billedItemsList)

            validateAllBillsFetched(billIds, billedItems)

            return billedItems
        } catch (e: Exception) {
            logger.error(e) { "Error fetching bills by IDs: $billIds" }
            throw e
        }
    }

    private fun validateAllBillsFetched(billIds: Set<Long>, billedItems: List<BilledItemWrapper>) {
        val returnedIds = billedItems.map { it.billId }.toSet()
        val missingIds = billIds - returnedIds
        if (missingIds.isNotEmpty()) {
            logger.error { "Missing billing items for IDs: $missingIds. Requested: $billIds, Returned: $returnedIds" }
            throw PayableErrorCode.BILLING_ITEMS_NOT_FOUND.toBusinessException(
                "Billing items not found for IDs: $missingIds. Requested ${billIds.size} items but only ${billedItems.size} were returned."
            )
        }
    }

    override fun getOrCreateOrderFormAdvanceBills(
        companyId: Long,
        entityId: Long?,
        dateRange: DateRange
    ): List<BilledItemWrapper> {
        val perLineCodeResult = getOrCreateBills(
            companyId,
            entityId,
            dateRange,
            setOf("ORDER_FORM_ADVANCE")
        )
        val orderFormAdvanceResult =
            perLineCodeResult["ORDER_FORM_ADVANCE"]
                ?: return emptyList()

        orderFormAdvanceResult.generationError?.let { error ->
            logger.error { "Billing generation error for ORDER_FORM_ADVANCE: ${error.errorCode} - ${error.errorMessage}." }
            throw PayableErrorCode.BILLING_GENERATION_FAILED.toBusinessException("Billing generation failed for ORDER_FORM_ADVANCE for companyId=$companyId, entityId=$entityId, dateRange=${dateRange}: errorCode=${error.errorCode} - errorMessage=${error.errorMessage}.")
        }

        return orderFormAdvanceResult.billedItems
    }

    override fun deleteBills(billIds: List<Long>): List<Long> {
        logger.info { "Deleting bills by IDs: $billIds" }

        if (billIds.isEmpty()) {
            return emptyList()
        }

        try {
            val request = Billing.DeleteBilledItemsByIdsRequest.newBuilder()
                .addAllBilledItemIds(billIds)
                .build()

            val response = billingServiceClient.deleteBilledItemsByIds(request)

            if (response.hasError()) {
                logger.error { "Error deleting bills: ${response.error.errorCode} - ${response.error.errorMessage}" }
                throw PayableErrorCode.BILLING_DELETION_FAILED.toBusinessException(
                    "Failed to delete bills with IDs: $billIds. Error: ${response.error.errorCode} - ${response.error.errorMessage}"
                )
            }

            val deletedIds = response.deletedBilledItemIdsList
            val requestedIds = billIds.toSet()
            val deletedIdsSet = deletedIds.toSet()
            val notDeletedIds = requestedIds - deletedIdsSet

            if (notDeletedIds.isNotEmpty()) {
                logger.error {
                    "Not all bills were deleted. Requested: $requestedIds, " +
                    "Successfully deleted: $deletedIdsSet, " +
                    "Failed to delete: $notDeletedIds"
                }
                throw PayableErrorCode.BILLING_DELETION_FAILED.toBusinessException(
                    "Failed to delete all requested bills. Successfully deleted ${deletedIds.size} out of ${billIds.size} bills. " +
                    "Failed to delete bill IDs: $notDeletedIds"
                )
            }

            logger.info { "Successfully deleted all ${deletedIds.size} requested bills: $deletedIdsSet" }

            return deletedIds
        } catch (e: Exception) {
            logger.error(e) { "Error deleting bills by IDs: $billIds" }
            throw e
        }
    }
}