package com.multiplier.core.payable.subsidiary

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.config.featureflag.FeatureFlagService
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.ClassPathResource
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

/**
 * Temporary implementation of [InvoiceSubsidiaryMapper] that reads the mapping from feature flag and a local file.
 */
@Service
class TempInvoiceSubsidiaryMapper(
    private val featureFlagService: FeatureFlagService,
    @Value("\${spring.profiles.active}") private val activeProfile: String,
) : InvoiceSubsidiaryMapper {

    private val objectMapper: ObjectMapper = ObjectMapper()

    /**
     * Customer subsidiary mapping.
     */
    private val customerMapping: List<CustomerSubsidiaryMap>
        get() = objectMapper.readValue(
            featureFlagService.feature("invoice-subsidiary-mapping-by-json", emptyMap()).value.toString(),
            InvoiceSubsidiaryMapping::class.java
        ).customerSubsidiaryMap

    /**
     * Tax code mapping.
     */
    private val taxMapping: InvoiceSubsidiaryMapping by lazy {
        objectMapper.readValue(
            ClassPathResource("config/subsidiary/subsidiary-mapping-$activeProfile.json").inputStream,
            InvoiceSubsidiaryMapping::class.java
        )
    }

    /**
     * Returns true if the customer is under different subsidiary than the Multiplier Singapore.
     */
    override fun isUnderDifferentSubsidiary(customerId: Long): Boolean {
        return try {
            customerMapping.any { it.customerId == customerId }
        } catch (e: Exception) {
            log.warn("Error while checking if the customer is under different subsidiary for customerId = $customerId", e)
            return false
        }
    }

    /**
     * Returns the subsidiary id for the given customer id.
     */
    override fun mapSubsidiary(customerId: Long) =
        (customerMapping.find { it.customerId == customerId }?.subsidiaryId ?: "1").toString()
            .also{ log.info { "Mapping from growthbook: $customerMapping" } }

    /**
     * Returns the tax code id for the given customer id and item type.
     * If the itemType is empty, it will return the tax code for all item types.
     */
    override fun mapTaxCodeId(customerId: Long, itemType: String): String? {
        log.info { "Tax code map: $taxMapping" }
        val subsidiaryId = mapSubsidiary(customerId)
        val taxCodeEntry = taxMapping.taxCodeMap.find {
            (subsidiaryId == it.subsidiaryId.toString()) &&
                    ((it.itemType == itemType) || it.itemType.isEmpty())
        } ?: throw IllegalArgumentException("Tax code not found for customerId = $customerId, itemType = $itemType")

        val taxCodeIdEntry = taxMapping.taxCodeIdMap.find { it.taxCode == taxCodeEntry.taxCode }
            ?: throw IllegalArgumentException("Tax code id not found for customerId = $customerId, itemType = $itemType")

        return taxCodeIdEntry.taxCodeId.toString()
    }
}