package com.multiplier.core.exception

import com.multiplier.common.exception.ErrorCode

enum class PayableErrorCode(
    override val message: String,
) : ErrorCode {
    INVALID_COMPANY_PAYABLE_ITEM("Invalid company payable item"),
    UNSUPPORTED_CHARGE_POLICY("Unsupported charge policy"),
    UNSUPPORTED_OFFERING_CODE("Unsupported offering code"),
    INVOICING_NO_DELTA_ITEMS("No new items or lack of delta found for invoicing"),
    ORDER_FORM_ADVANCE_MUST_HAVE_TARGET("Order form advance must have target"),
    COMPANY_NOT_FOUND("Company not found"),
    COMPANY_PRIMARY_ENTITY_NOT_FOUND("Company primary entity not found"),
    ADVANCE_COLLECTION_BALANCE_NOT_AVAILABLE("Advance collection balance not available"),
    CURRENCY_MISMATCH_EXCEPTION("Currency mismatch"),
    INVALID_STATE_TRANSITION("Invalid state transition"),
    ADVANCE_COLLECTION_BALANCE_EXISTS("Advance collection balance exists"),
    BILLING_GENERATION_FAILED("Billing generation failed"),
    COMPANY_PRICING_NOT_FOUND("Company pricing information not found"),
    COMPANY_BILLING_CURRENCY_NOT_FOUND("Company billing currency not found"),
    COMPANY_PAYABLE_NOT_FOUND("Company payable not found"),
    MULTIPLE_COMPANY_PAYABLES_FOUND("Multiple company payables found when expecting one"),
    COMPANY_PAYABLE_ALREADY_PAID("Company payable is already paid"),
    INVOICE_SOURCE_REPORT_NOT_FOUND("Invoice source report not found"),
    FILE_UPLOAD_FAILED("File upload to storage failed"),
    FILE_RETRIEVAL_FAILED("File retrieval from storage failed"),
    FILE_METADATA_NOT_FOUND("File metadata not found in database"),
    FILE_PROCESSING_ERROR("File processing error occurred"),
    NETSUITE_INTEGRATION_FAILED("NetSuite integration service failed"),
    NETSUITE_INVOICE_CREATION_FAILED("NetSuite invoice creation failed"),
    NETSUITE_INVOICE_RETRIEVAL_FAILED("NetSuite invoice retrieval failed"),
    NETSUITE_PDF_GENERATION_FAILED("NetSuite PDF generation failed"),
    DISPUTE_PERMISSION_DENIED("User not authorized to create dispute for company"),
    DISPUTE_ALREADY_EXISTS("Dispute already exists for the given origin"),
    USER_SERVICE_UNAVAILABLE("User service unavailable"),
    NETSUITE_MAPPING_FAILED("NetSuite data mapping failed"),
    INVOICE_GENERATION_FAILED("Invoice generation failed"),
    EXTERNAL_INVOICE_DELETION_FAILED("External invoice deletion failed"),
    XERO_DATA_INTEGRITY_ERROR("Xero data integrity error"),
    UNSUPPORTED_REFERENCE_TARGET_TYPE("Unsupported reference target type"),
    ADVANCE_COLLECTION_BALANCE_NOT_FOUND("Advance collection balance not found"),
    BILLING_ITEMS_NOT_FOUND("Some billing items were not found"),
    BILLING_ITEMS_REQUIRES_CONTRACT_ID("Billing item requires contract ID"),
    BILLING_ITEMS_REQUIRES_CONTRACT_WITH_COUNTRY("Billing item requires contract with country"),
    BILLING_ITEMS_REQUIRES_UNIQUE_CONTRACT_ID("Billing item requires unique contract ID"),
    INVOICE_NOT_FOUND("Invoice not found"),
    UNSUPPORTED_OPERATION("Unsupported operation"),
    INVALID_FINANCIAL_TRANSACTION_TYPE("Invalid financial transaction type"),
    COMPANY_BINDING_NOT_FOUND("Company binding not found"),
    INVALID_EXTERNAL_CUSTOMER("Invalid external customer"),
    ORDER_FORM_ADVANCE_BALANCE_MUST_HAVE_REFERENCE_LINE("Order form advance balance must have reference line"),
    INVALID_COMPANY_PRIMARY_ENTITY("Invalid company primary entity"),
    ADJUSTMENT_MUST_HAVE_MATCHING_ADVANCE_LINE_TAX_CODE("Adjustment must have matching advance line tax code."),
    INVALID_COUNTRY_WORK_STATUS("Invalid country work status"),
    BILLING_DELETION_FAILED("Billing deletion failed"),
    ENTITY_BILLING_CURRENCY_RETRIEVAL_FAILED("Failed to retrieve billing currencies for entities"),
    MULTIPLE_ENTITIES_FOUND_IN_METER("Multiple entities found in metering data"),
    COMPANY_ENTITY_ID_MISMATCH("Company entity ID mismatch"),
    BANK_FEE_BALANCE_SAVED_SEARCH_RESPONSE_NULL("Bank fee balance saved search response is null"),
    BANK_FEE_BALANCE_SAVED_SEARCH_DATA_NULL("Bank fee balance saved search data is null"),
    BANK_FEE_BALANCE_CSV_PROCESSING_FAILED("Bank fee balance CSV processing failed"),
    BANK_FEE_BALANCE_NOT_FOUND("Bank fee balance not found"),
    BANK_FEE_BALANCE_CSV_INVALID_FORMAT("Bank fee balance CSV has invalid format"),
    BANK_FEE_BALANCE_CSV_INVALID_DATA("Bank fee balance CSV contains invalid data"),
    NOT_SUPPORTED_INVOICE_TYPE("Invoice type is not supported")
    ;
}