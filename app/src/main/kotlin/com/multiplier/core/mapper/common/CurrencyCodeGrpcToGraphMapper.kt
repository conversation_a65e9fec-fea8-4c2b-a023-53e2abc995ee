package com.multiplier.core.mapper.common

import com.multiplier.payable.types.CurrencyCode
import com.multiplier.grpc.common.currency.v2.Currency.CurrencyCode as GrpcCurrencyCode
import org.springframework.stereotype.Component

@Component
class CurrencyCodeGrpcToGraphMapper {
    fun map(currency: GrpcCurrencyCode): CurrencyCode {
        return try {
            val currencyString = currency.name.removePrefix("CURRENCY_CODE_")
            CurrencyCode.valueOf(currencyString)
        } catch (e: IllegalArgumentException) {
            throw IllegalArgumentException("Invalid currency code: ${currency.name}", e)
        }
    }
}