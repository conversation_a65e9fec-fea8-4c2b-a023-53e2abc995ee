package com.multiplier.core.mapper.pricing

import com.multiplier.core.payable.adapters.pricing.TargetReference
import org.springframework.stereotype.Component
import com.multiplier.productcatalogue.grpc.schema.pricing.policy.TargetReference as GrpcTargetReference

@Component
class TargetReferenceGrpcToDomainMapper(
    private val referenceTargetTypeGrpcToDomainMapper: ReferenceTargetTypeGrpcToDomainMapper,
) {
    fun map(target: GrpcTargetReference): TargetReference {
        return TargetReference(
            percentage = target.percentage,
            lineCode = target.lineCode,
            dimensions = target.dimensionsMap,
            referenceTargetType = referenceTargetTypeGrpcToDomainMapper.map(target.referenceTargetType),
        )
    }

    fun map(targets: List<GrpcTargetReference>): List<TargetReference> {
        return targets.map(::map)
    }
}