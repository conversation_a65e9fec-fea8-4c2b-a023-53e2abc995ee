package com.multiplier.core.mapper.bill

import com.multiplier.billing.grpc.billing.Billing.BilledItem
import com.multiplier.core.mapper.AmountGrpcToDomainMapper
import com.multiplier.core.mapper.product.CompanyProductGrpcToDomainMapper
import com.multiplier.core.payable.adapters.billing.BilledItemWrapper
import com.multiplier.core.payable.mapper.GrpcDateTimeMapper
import com.multiplier.payable.engine.domain.aggregates.Duration
import com.multiplier.payable.engine.vas.toLocalDateTime
import org.springframework.stereotype.Component
import java.time.ZoneOffset

@Component
class BilledItemGrpcToDomainMapper(
    private val companyProductGrpcToDomainMapper: CompanyProductGrpcToDomainMapper,
    private val grpcDateTimeMapper: GrpcDateTimeMapper,
    private val amountGrpcToDomainMapper: AmountGrpcToDomainMapper,
    private val billedItemUsageGrpcToDomainMapper: BilledItemUsageGrpcToDomainMapper,
) {
    fun map(billedItem: BilledItem): BilledItemWrapper {
        val billingDuration = billedItem.billingPeriod.billingDuration
        val usageDuration = billedItem.billingPeriod.usageDuration

        return BilledItemWrapper(
            billId = billedItem.id,
            transactionId = billedItem.transactionId,
            companyId = billedItem.companyId,
            entityId = billedItem.entityId,
            companyProduct = companyProductGrpcToDomainMapper.map(billedItem.companyProduct),
            billingAmount = amountGrpcToDomainMapper.map(billedItem.billingAmount),
            billingDuration = Duration(
                startDate = grpcDateTimeMapper.toLocalDateTime(billingDuration.startDate)!!.toLocalDate(),
                endDate = grpcDateTimeMapper.toLocalDateTime(billingDuration.endDate)!!.toLocalDate()
            ),
            usageDuration = Duration(
                startDate = grpcDateTimeMapper.toLocalDateTime(usageDuration.startDate)!!.toLocalDate(),
                endDate = grpcDateTimeMapper.toLocalDateTime(usageDuration.endDate)!!.toLocalDate()
            ),
            billingTime = billedItem.billingTime.toLocalDateTime().toEpochSecond(ZoneOffset.UTC),
            referenceBills = billedItem.referencedBillsList.map { referenceBill ->
                map(referenceBill)
            },
            usages = billedItemUsageGrpcToDomainMapper.map(billedItem.usageList)
        )
    }

    fun map(billedItems: List<BilledItem>): List<BilledItemWrapper> {
        return billedItems.map(::map)
    }
}