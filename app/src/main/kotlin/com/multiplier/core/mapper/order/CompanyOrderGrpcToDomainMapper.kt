package com.multiplier.core.mapper.order

import com.multiplier.core.mapper.product.CompanyProductGrpcToDomainMapper
import com.multiplier.core.payable.adapters.order.CompanyOrderWrapper
import com.multiplier.productcatalogue.grpc.schema.order.CompanyOrder
import org.springframework.stereotype.Component

@Component
class CompanyOrderGrpcToDomainMapper(
    private val companyProductGrpcToDomainMapper: CompanyProductGrpcToDomainMapper,
) {
    fun map(companyOrder: CompanyOrder): CompanyOrderWrapper {
        return CompanyOrderWrapper(
            companyId = companyOrder.companyId,
            orderId = companyOrder.id,
            orderedProducts = companyProductGrpcToDomainMapper.map(companyOrder.orderedProductsList)
        )
    }
}