package com.multiplier.core.util.dto.vendorbill

import com.multiplier.core.payable.adapters.netsuite.models.CountryCode
import com.multiplier.core.payable.expenseBill.ExpenseBillLineItemType
import com.multiplier.core.payable.service.vendorbill.itemstore.ParsedDate
import com.multiplier.payable.engine.currency.CurrencyCode
import lombok.Builder
import lombok.Data

@Data
@Builder
data class PayrollData(
    val vendorDataMap: Map<ExpenseBillLineItemType, VendorDataDto>,
    val memberPay: MemberPayDto
)

@Data
@Builder
data class VendorDataDto(
    val amount: Double
)

@Data
@Builder
data class MemberPayDto(
    val countryCode: CountryCode,
    val contractId: Long,
    val currency: CurrencyCode,
    val cycleId: Long,
    val frequency: String,
    val payDate: ParsedDate,
    val endDate: ParsedDate,
    val startDate: ParsedDate
)