package com.multiplier.dispute.origin.type

import com.multiplier.dispute.origin.api.DisputeOriginType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.InjectMocks
import org.mockito.junit.jupiter.MockitoExtension
import java.util.stream.Stream

@ExtendWith(MockitoExtension::class)
class DisputeOriginTypeToApiMapperTest {

    @InjectMocks
    private lateinit var mapper: DisputeOriginTypeToApiMapper

    @ParameterizedTest
    @MethodSource
    fun givenGraph_whenMap_thenReturnApi(
        graphType: com.multiplier.payable.types.DisputeOriginType,
        expectedType: DisputeOriginType,
    ) {
        // WHEN
        val type = mapper.map(graphType)

        // THEN
        assertThat(type).isEqualTo(expectedType)
    }

    companion object {
        /**
         * A data source for the test with the same name.
         * See https://www.baeldung.com/parameterized-tests-junit-5#6-method
         */
        @JvmStatic
        private fun givenGraph_whenMap_thenReturnApi(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    com.multiplier.payable.types.DisputeOriginType.COMPANY_PAYABLE,
                    DisputeOriginType.COMPANY_PAYABLE
                )
            )
        }
    }
}