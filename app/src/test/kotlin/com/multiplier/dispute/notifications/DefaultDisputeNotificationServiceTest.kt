package com.multiplier.dispute.notifications

import com.multiplier.dispute.notifications.factory.business.DisputeNotificationFactoryByOriginType
import com.multiplier.dispute.notifications.factory.business.DisputeNotificationHandlerByOriginType
import com.multiplier.dispute.utils.TestDataFactory
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import kotlin.test.assertFailsWith

@ExtendWith(MockitoExtension::class)
class DefaultDisputeNotificationServiceTest {

    @Mock
    private lateinit var disputeByBizFactory: DisputeNotificationFactoryByOriginType

    @Mock
    private lateinit var handler: DisputeNotificationHandlerByOriginType

    @InjectMocks
    private lateinit var service: DefaultDisputeNotificationService

    @Test
    fun `doSend should call correct handler based on originType`() {
        val disputeDto = TestDataFactory.mockSampleDispute()
        whenever(disputeByBizFactory.getHandler(any())).thenReturn(handler)

        service.doSend(disputeDto)

        verify(disputeByBizFactory).getHandler(disputeDto.disputeOrigin.type)
        verify(handler).handle(disputeDto)
    }

    @Test
    fun `doSend should throw exception when handler throws exception`() {
        val disputeDto = TestDataFactory.mockSampleDispute()
        whenever(disputeByBizFactory.getHandler(any())).thenReturn(handler)
        whenever(handler.handle(any())).thenThrow(RuntimeException::class.java)

        assertFailsWith<RuntimeException> {
            service.doSend(disputeDto)
        }
    }
}
