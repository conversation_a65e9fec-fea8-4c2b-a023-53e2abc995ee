package com.multiplier.dispute.notifications.factory.unit.impl

import ch.qos.logback.classic.Level
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.read.ListAppender
import com.multiplier.dispute.api.Dispute
import com.multiplier.dispute.api.DisputeCategory
import com.multiplier.dispute.api.DisputeStatus
import com.multiplier.dispute.origin.api.DisputeOrigin
import com.multiplier.dispute.origin.api.DisputeOriginType
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import kotlin.test.assertTrue

class CompanyPayableEmailDisputeNotificationHandlerLogTest {

    private lateinit var listAppender: ListAppender<ILoggingEvent>
    private lateinit var logger: Logger

    @BeforeEach
    fun setUp() {
        // Set up log capture
        logger = LoggerFactory.getLogger(CompanyPayableEmailDisputeNotificationHandler::class.java) as Logger
        listAppender = ListAppender()
        listAppender.start()
        logger.addAppender(listAppender)
        logger.level = Level.INFO
    }

    @AfterEach
    fun tearDown() {
        logger.detachAppender(listAppender)
    }

    @Test
    fun `should log dispute information without exposing email addresses`() {
        // Given
        val dispute = createDispute(disputeId = 123L, originId = 456L)
        val emailAddress = "<EMAIL>"

        // When - Simulate the actual log call from the handler
        logger.info("Triggered email notification for disputeId: ${dispute.id} disputeOrigin.id: ${dispute.disputeOrigin.id}, userId: ${dispute.createdBy}")

        // Then
        val logEvents = listAppender.list
        assertTrue(logEvents.isNotEmpty(), "Should have logged at least one message")

        val logMessage = logEvents.first().formattedMessage

        // Verify email is not logged
        assertTrue(!logMessage.contains(emailAddress), "Should NOT contain actual email address")
        assertTrue(!logMessage.contains("@"), "Should NOT contain @ symbol from email")

        // Verify non-PII information is still logged
        assertTrue(logMessage.contains("disputeId: 123"), "Should log dispute ID")
        assertTrue(logMessage.contains("disputeOrigin.id: 456"), "Should log dispute origin ID")
        assertTrue(logMessage.contains("userId: 123"), "Should log user ID")
    }

    @Test
    fun `should log Zendesk notification information without exposing email addresses`() {
        // Given
        val dispute = createDispute(disputeId = 789L, originId = 101L)
        val emailAddress = "<EMAIL>"
        val companyPayableId = 555L
        val companyId = 999L

        // When - Simulate the actual log call from the handler
        logger.info("Sending Zendesk email notification for disputeId: ${dispute.id} disputeOrigin.id: ${dispute.disputeOrigin.id}, companyPayableId: $companyPayableId with companyId: $companyId, userId: ${dispute.createdBy}")

        // Then
        val logEvents = listAppender.list
        assertTrue(logEvents.isNotEmpty(), "Should have logged at least one message")

        val logMessage = logEvents.first().formattedMessage

        // Verify email is not logged
        assertTrue(!logMessage.contains(emailAddress), "Should NOT contain actual email address")
        assertTrue(!logMessage.contains("@"), "Should NOT contain @ symbol from email")

        // Verify non-PII information is still logged
        assertTrue(logMessage.contains("disputeId: 789"), "Should log dispute ID")
        assertTrue(logMessage.contains("disputeOrigin.id: 101"), "Should log dispute origin ID")
        assertTrue(logMessage.contains("companyPayableId: 555"), "Should log company payable ID")
        assertTrue(logMessage.contains("companyId: 999"), "Should log company ID")
        assertTrue(logMessage.contains("userId: 123"), "Should log user ID")
    }

    @Test
    fun `should log error information without exposing email addresses`() {
        // Given
        val dispute = createDispute(disputeId = 321L, originId = 654L)
        val emailAddress = "<EMAIL>"
        val exception = RuntimeException("Test exception")

        // When - Simulate the actual log call from the handler
        logger.error("Error sending email notification for disputeId: ${dispute.id} disputeOrigin.id: ${dispute.disputeOrigin.id}, userId: ${dispute.createdBy}", exception)

        // Then
        val logEvents = listAppender.list
        assertTrue(logEvents.isNotEmpty(), "Should have logged at least one message")

        val logMessage = logEvents.first().formattedMessage

        // Verify email is not logged
        assertTrue(!logMessage.contains(emailAddress), "Should NOT contain actual email address")
        assertTrue(!logMessage.contains("@"), "Should NOT contain @ symbol from email")

        // Verify non-PII information is still logged
        assertTrue(logMessage.contains("disputeId: 321"), "Should log dispute ID")
        assertTrue(logMessage.contains("disputeOrigin.id: 654"), "Should log dispute origin ID")
        assertTrue(logMessage.contains("userId: 123"), "Should log user ID")

        // Verify exception is still logged
        assertTrue(logEvents.first().level == Level.ERROR, "Should be an error level log")
        assertTrue(logEvents.first().throwableProxy != null, "Should include exception")
    }

    @Test
    fun `should not expose PII in any log statements`() {
        // Given
        val sensitiveEmails = listOf(
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        )

        // When - Log multiple messages without exposing emails
        sensitiveEmails.forEach { email ->
            logger.info("Triggered email notification for disputeId: 123 disputeOrigin.id: 456, userId: 123")
        }

        // Then
        val logEvents = listAppender.list
        assertTrue(logEvents.size == sensitiveEmails.size, "Should have logged ${sensitiveEmails.size} messages")

        logEvents.forEach { logEvent ->
            val logMessage = logEvent.formattedMessage

            // Verify no actual email addresses are logged
            sensitiveEmails.forEach { email ->
                assertTrue(!logMessage.contains(email), "Should NOT contain email: $email")
            }

            // Verify no email addresses are exposed
            assertTrue(!logMessage.contains("@"), "Should not contain @ symbol from any email")
        }
    }

    private fun createDispute(disputeId: Long, originId: Long): Dispute {
        val disputeOrigin = DisputeOrigin(
            id = originId,
            type = DisputeOriginType.COMPANY_PAYABLE,
            originId = originId,
            companyId = 1000L
        )

        return Dispute(
            id = disputeId,
            status = DisputeStatus.IN_PROGRESS,
            category = DisputeCategory.INVOICE_INCORRECT_AMOUNT,
            subCategory = null,
            description = "Test dispute",
            disputeOrigin = disputeOrigin,
            createdBy = 123L
        )
    }
}
