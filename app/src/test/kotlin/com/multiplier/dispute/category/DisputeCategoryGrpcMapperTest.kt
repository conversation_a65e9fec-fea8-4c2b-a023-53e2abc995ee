package com.multiplier.dispute.category

import com.multiplier.dispute.api.DisputeCategory
import com.multiplier.dispute.grpc.schema.DisputeOuterClass
import org.junit.Assert.assertEquals
import org.junit.jupiter.api.Test

class DisputeCategoryGrpcMapperTest {

    private val mapper = DisputeCategoryGrpcMapper()

    @Test
    fun testMapToDisputeCategory_InvoiceIncorrectAmount() {
        val result = mapper.mapToDisputeCategory(DisputeCategory.INVOICE_INCORRECT_AMOUNT)
        assertEquals(DisputeOuterClass.DisputeCategory.CATEGORY_INVOICE_INCORRECT_AMOUNT, result)
    }

    @Test
    fun testMapToDisputeCategory_InvoiceIncorrectFormat() {
        val result = mapper.mapToDisputeCategory(DisputeCategory.INVOICE_INCORRECT_FORMAT)
        assertEquals(DisputeOuterClass.DisputeCategory.CATEGORY_INVOICE_INCORRECT_FORMAT, result)
    }

    @Test
    fun testMapToDisputeCategory_InvoiceQuery() {
        val result = mapper.mapToDisputeCategory(DisputeCategory.INVOICE_QUERY)
        assertEquals(DisputeOuterClass.DisputeCategory.CATEGORY_INVOICE_QUERY, result)
    }

    @Test
    fun testMapToDisputeCategory_Other() {
        val result = mapper.mapToDisputeCategory(DisputeCategory.OTHER)
        assertEquals(DisputeOuterClass.DisputeCategory.CATEGORY_OTHER, result)
    }

}