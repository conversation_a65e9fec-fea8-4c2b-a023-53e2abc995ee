package com.multiplier.dispute.facade.page

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.core.util.UserExperience
import com.multiplier.core.util.UserExperienceChecker
import com.multiplier.dispute.api.DisputeStatus
import com.multiplier.dispute.database.page.DisputePageRequest
import com.multiplier.dispute.origin.api.DisputeOriginType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Answers
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class UserAwareDisputePageRequestProviderTest {

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private lateinit var currentUser: CurrentUser

    @Mock
    private lateinit var userExperienceChecker: UserExperience<PERSON>hecker

    @InjectMocks
    private lateinit var userAwareDisputePageRequestProvider: UserAwareDisputePageRequestProvider

    @Test
    fun givenCustomerRequest_whenGet_thenReturnUserAwareRequest() {
        // GIVEN
        val companyUser = true
        mockUserExperienceChecker(companyUser)
        val companyIdOfCurrentUser = mockCurrentUser()

        val requestFilter = DisputePageRequest(
            originType = DisputeOriginType.COMPANY_PAYABLE,
            companyIds = listOf(companyIdOfCurrentUser),
            statuses = listOf(DisputeStatus.IN_PROGRESS),
            pageNumber = 0,
            pageSize = 10,
        )

        val expectedFilter = requestFilter.copy(
            userExperience = UserExperience.CUSTOMER
        )

        // WHEN
        val filter = userAwareDisputePageRequestProvider.get(requestFilter)

        // THEN
        assertThat(filter).isEqualTo(expectedFilter)
    }

    private fun mockUserExperienceChecker(companyUser: Boolean) {
        whenever(userExperienceChecker.isCompanyExperience(currentUser))
            .thenReturn(companyUser)
    }

    private fun mockCurrentUser(): Long {
        val companyIdOfCurrentUser = 666L
        whenever(currentUser.context!!.scopes.companyId).thenReturn(companyIdOfCurrentUser)
        return companyIdOfCurrentUser
    }

    @Test
    fun givenOpsRequest_whenGet_thenReturnUserAwareRequest() {
        // GIVEN
        val companyUser = false
        mockUserExperienceChecker(companyUser)

        val requestFilter = DisputePageRequest(
            originType = DisputeOriginType.COMPANY_PAYABLE,
            companyIds = listOf(42L),
            statuses = listOf(DisputeStatus.IN_PROGRESS),
            pageNumber = 0,
            pageSize = 10,
        )

        val expectedFilter = requestFilter.copy(
            userExperience = UserExperience.OPERATIONS,
        )

        // WHEN
        val filter = userAwareDisputePageRequestProvider.get(requestFilter)

        // THEN
        assertThat(filter).isEqualTo(expectedFilter)
    }
}