package com.multiplier.dispute.facade.validation

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.common.transport.user.UserContext
import com.multiplier.common.transport.user.UserScopes
import com.multiplier.core.util.UserExperienceChecker
import com.multiplier.dispute.database.CreateDisputeRequest
import com.multiplier.common.exception.MplBusinessException
import com.multiplier.dispute.facade.DisputeCreationException
import com.multiplier.dispute.origin.database.CreateDisputeOriginRequest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class DisputeCompanyUserAndInputCompanyMatchValidatorTest {

    @Mock
    private lateinit var userExperienceChecker: UserExperienceChecker
    @Mock
    private lateinit var currentUser: CurrentUser

    @InjectMocks
    private lateinit var disputeCompanyUserAndInputCompanyMatchValidator:
            DisputeCompanyUserAndInputCompanyMatchValidator

    @Test
    fun `throw if not allowed to create dispute`() {
        // given
        val currentUserCompanyId = 1L
        val companyIdFromInput = 2L
        val userContext = Mockito.mock(UserContext::class.java)
        val userScopes = Mockito.mock(UserScopes::class.java)
        val createDisputeRequest = Mockito.mock(CreateDisputeRequest::class.java)
        val disputeOriginRequest = Mockito.mock(CreateDisputeOriginRequest::class.java)

        // when
        whenever(userExperienceChecker.isCompanyExperience(currentUser)).thenReturn(true)
        whenever(currentUser.context).thenReturn(userContext)
        whenever(userContext!!.scopes).thenReturn(userScopes)
        whenever(userScopes.companyId).thenReturn(currentUserCompanyId)
        whenever(createDisputeRequest.disputeOrigin).thenReturn(disputeOriginRequest)
        whenever(disputeOriginRequest.companyId).thenReturn(companyIdFromInput)

        // then
        val exception = assertThrows(MplBusinessException::class.java) {
            disputeCompanyUserAndInputCompanyMatchValidator.validate(createDisputeRequest)
        }

        assertEquals(
            "User(userId = ${currentUser.context!!.id}) is not " +
                    "allowed to create dispute for company $companyIdFromInput",
            exception.message
        )
    }

}