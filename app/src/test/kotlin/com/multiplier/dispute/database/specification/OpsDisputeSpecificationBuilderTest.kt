package com.multiplier.dispute.database.specification

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class OpsDisputeSpecificationBuilderTest {

    @Mock
    private lateinit var baseDisputeSpecificationBuilder: BaseDisputeSpecificationBuilder

    @InjectMocks
    private lateinit var opsDisputeSpecificationBuilder: OpsDisputeSpecificationBuilder

    @Test
    fun givenRequest_whenBuild_thenCallBaseDisputeSpecificationBuilder() {
        // GIVEN
        val request = mock<DisputeSpecificationRequest>()

        // WHEN
        opsDisputeSpecificationBuilder.build(request)

        // THEN
        verify(baseDisputeSpecificationBuilder).build(request)
    }
}