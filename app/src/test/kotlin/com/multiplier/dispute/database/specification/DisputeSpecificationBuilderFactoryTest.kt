package com.multiplier.dispute.database.specification

import com.multiplier.core.util.UserExperience
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class DisputeSpecificationBuilderFactoryTest {

    @Mock
    private lateinit var baseDisputeSpecificationBuilder: BaseDisputeSpecificationBuilder

    @InjectMocks
    private lateinit var disputeSpecificationBuilderFactory: DisputeSpecificationBuilderFactory

    @Test
    fun givenOps_whenGet_thenReturnOpsBuilder() {
        // GIVEN
        val userExperience = UserExperience.OPERATIONS

        // WHEN
        val builder = disputeSpecificationBuilderFactory.get(userExperience)

        // THEN
        assertThat(builder).isInstanceOf(OpsDisputeSpecificationBuilder::class.java)
    }

    @Test
    fun givenCustomer_whenGet_thenReturnAppBuilder() {
        // GIVEN
        val userExperience = UserExperience.CUSTOMER

        // WHEN
        val builder = disputeSpecificationBuilderFactory.get(userExperience)

        // THEN
        assertThat(builder).isInstanceOf(CustomerDisputeSpecificationBuilder::class.java)
    }
}