package com.multiplier.utils

import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

object InternalFinancialTransactionTestDataFactory {

    fun defaultDateRange(): DateRange {
        val start = LocalDateTime.now().truncatedTo(ChronoUnit.DAYS)
        val end = start.plusDays(7) // Default to a week later
        return DateRange(start, end)
    }

    fun defaultInvoiceCommand(
        transactionType: TransactionType = TransactionType.ANNUAL_PLAN_INVOICE,
        transactionId: String = "TX123456789",
        companyId: Long = 12345L,
        dateRange: DateRange = defaultDateRange(),
        transactionDate: LocalDateTime = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS),
        cycle: InvoiceCycle = InvoiceCycle.MONTHLY,
        tracingCtx: MutableMap<String, String> = mutableMapOf("source" to "test")
    ): InvoiceCommand {
        return InvoiceCommand(
            transactionType = transactionType,
            transactionId = transactionId,
            companyId = companyId,
            dateRange = dateRange,
            transactionDate = transactionDate,
            cycle = cycle,
            depositCommand = null
        ).apply {
            tracingContext.putAll(tracingCtx)
        }
    }
}
