package com.multiplier.utils

// File: src/test/kotlin/utils/TestExtensions.kt

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.util.dto.payroll.CompanyPayrollWrapper
import com.multiplier.payable.engine.collector.billed.firstinvoice.BilledFirstInvoiceItemKey
import com.multiplier.payable.engine.collector.data.CollectorInput
import com.multiplier.payable.engine.collector.data.DefaultCollectorInput
import com.multiplier.payable.engine.collector.gp.DefaultDataKey
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.types.CurrencyCode
import org.mockito.Mockito
import java.time.LocalDate

// A reified extension function for Mockito's any() to enforce non-null usage.
inline fun <reified T : Any> anyNonNull(): T = Mockito.any(T::class.java) ?: createInstance()

// Helper function to create a dummy instance of any class. Be cautious with classes that have complex dependencies or initial states.
inline fun <reified T> createInstance(): T {
    return when (T::class) {
        String::class -> "" as T
        Int::class -> 0 as T
        Long::class -> 0L as T
        Collection::class -> arrayListOf<Any>() as T // Handling Collection
        List::class -> emptyList<Any>() as T
        Set::class -> emptySet<Any>() as T
        Map::class -> emptyMap<Any, Any>() as T
        LineItemType::class -> LineItemType.PEO_SALARY_DISBURSEMENT as T
        CollectorInput::class -> DefaultCollectorInput(
            transactionId = "123",
            requestTimeMs = 123,
        ) as T
        DefaultDataKey::class -> DefaultDataKey(
            companyId = 1,
            month = 12,
            year = 2023,
            oTime = 123,
            lineItemType = LineItemType.PEO_SALARY_DISBURSEMENT
        ) as T
        CompanyPayrollWrapper::class -> CompanyPayrollWrapper(
            memberPays = listOf()
        ) as T
        PayableItemStoreDto::class -> PayableItemStoreDto(
            amount = 0.0,
            currency = CurrencyCode.USD,
            companyId = 1,
            contractId = 2,
            transactionId = "123",
            month = 12,
            year=2023,
            itemType = LineItemType.PEO_SALARY_DISBURSEMENT,
            periodStartDate = LocalDate.now(),
            periodEndDate = LocalDate.now(),
            versionId = "qwerty",
            originalTimestamp = 1234567890L,
        ) as T
        BilledFirstInvoiceItemKey::class -> BilledFirstInvoiceItemKey(
            companyId = 1L,
            oTime = System.currentTimeMillis(),
            month = 9,
            year = 2024
        ) as T
        // Add more default types as needed
        else -> throw IllegalArgumentException("Cannot create instance for ${T::class}")
    }
}
