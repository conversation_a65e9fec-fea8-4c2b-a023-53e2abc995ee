package com.multiplier.financialsettings.billingcurrencysetting.domain

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class BillingCurrencySettingTest {

    @Test
    fun `given valid parameters, when creating BillingCurrencySetting, then create with correct values`() {
        // GIVEN
        val id = 1L
        val companyEntityContextId = 100L
        val currencyCode = "USD"
        val transactionType = "FIRST_INVOICE"

        // WHEN
        val setting = BillingCurrencySetting(
            id = id,
            companyEntityContextId = companyEntityContextId,
            currencyCode = currencyCode,
            transactionType = transactionType
        )

        // THEN
        assertThat(setting.id).isEqualTo(id)
        assertThat(setting.companyEntityContextId).isEqualTo(companyEntityContextId)
        assertThat(setting.currencyCode).isEqualTo(currencyCode)
        assertThat(setting.transactionType).isEqualTo(transactionType)
    }

    @Test
    fun `given null id, when creating BillingCurrencySetting, then create with null id`() {
        // GIVEN
        val companyEntityContextId = 100L
        val currencyCode = "EUR"
        val transactionType = "SECOND_INVOICE"

        // WHEN
        val setting = BillingCurrencySetting(
            id = null,
            companyEntityContextId = companyEntityContextId,
            currencyCode = currencyCode,
            transactionType = transactionType
        )

        // THEN
        assertThat(setting.id).isNull()
        assertThat(setting.companyEntityContextId).isEqualTo(companyEntityContextId)
        assertThat(setting.currencyCode).isEqualTo(currencyCode)
        assertThat(setting.transactionType).isEqualTo(transactionType)
    }

    @Test
    fun `given two settings with same values, when comparing, then they are equal`() {
        // GIVEN
        val setting1 = BillingCurrencySetting(
            id = 1L,
            companyEntityContextId = 100L,
            currencyCode = "USD",
            transactionType = "FIRST_INVOICE"
        )
        val setting2 = BillingCurrencySetting(
            id = 1L,
            companyEntityContextId = 100L,
            currencyCode = "USD",
            transactionType = "FIRST_INVOICE"
        )

        // WHEN & THEN
        assertThat(setting1).isEqualTo(setting2)
        assertThat(setting1.hashCode()).isEqualTo(setting2.hashCode())
    }

    @Test
    fun `given two settings with different values, when comparing, then they are not equal`() {
        // GIVEN
        val setting1 = BillingCurrencySetting(
            id = 1L,
            companyEntityContextId = 100L,
            currencyCode = "USD",
            transactionType = "FIRST_INVOICE"
        )
        val setting2 = BillingCurrencySetting(
            id = 2L,
            companyEntityContextId = 100L,
            currencyCode = "USD",
            transactionType = "FIRST_INVOICE"
        )

        // WHEN & THEN
        assertThat(setting1).isNotEqualTo(setting2)
    }

    @Test
    fun `given different currency codes, when creating settings, then create with correct currency codes`() {
        // GIVEN
        val companyEntityContextId = 100L
        val transactionType = "FIRST_INVOICE"

        // WHEN
        val usdSetting = BillingCurrencySetting(
            id = 1L,
            companyEntityContextId = companyEntityContextId,
            currencyCode = "USD",
            transactionType = transactionType
        )
        val eurSetting = BillingCurrencySetting(
            id = 2L,
            companyEntityContextId = companyEntityContextId,
            currencyCode = "EUR",
            transactionType = transactionType
        )
        val jpySetting = BillingCurrencySetting(
            id = 3L,
            companyEntityContextId = companyEntityContextId,
            currencyCode = "JPY",
            transactionType = transactionType
        )

        // THEN
        assertThat(usdSetting.currencyCode).isEqualTo("USD")
        assertThat(eurSetting.currencyCode).isEqualTo("EUR")
        assertThat(jpySetting.currencyCode).isEqualTo("JPY")
    }

    @Test
    fun `given different company entity context ids, when creating settings, then create with correct context ids`() {
        // GIVEN
        val currencyCode = "USD"
        val transactionType = "FIRST_INVOICE"

        // WHEN
        val setting1 = BillingCurrencySetting(
            id = 1L,
            companyEntityContextId = 100L,
            currencyCode = currencyCode,
            transactionType = transactionType
        )
        val setting2 = BillingCurrencySetting(
            id = 2L,
            companyEntityContextId = 200L,
            currencyCode = currencyCode,
            transactionType = transactionType
        )

        // THEN
        assertThat(setting1.companyEntityContextId).isEqualTo(100L)
        assertThat(setting2.companyEntityContextId).isEqualTo(200L)
    }

    @Test
    fun `given setting, when calling toString, then return string representation`() {
        // GIVEN
        val setting = BillingCurrencySetting(
            id = 1L,
            companyEntityContextId = 100L,
            currencyCode = "USD",
            transactionType = "FIRST_INVOICE"
        )

        // WHEN
        val result = setting.toString()

        // THEN
        assertThat(result).contains("BillingCurrencySetting")
        assertThat(result).contains("id=1")
        assertThat(result).contains("companyEntityContextId=100")
        assertThat(result).contains("currencyCode=USD")
        assertThat(result).contains("transactionType=FIRST_INVOICE")
    }

    @Test
    fun `given setting, when using copy method, then create new setting with updated values`() {
        // GIVEN
        val original = BillingCurrencySetting(
            id = 1L,
            companyEntityContextId = 100L,
            currencyCode = "USD",
            transactionType = "FIRST_INVOICE"
        )

        // WHEN
        val copied = original.copy(
            currencyCode = "EUR",
            companyEntityContextId = 200L,
            transactionType = "SECOND_INVOICE"
        )

        // THEN
        assertThat(copied.id).isEqualTo(original.id)
        assertThat(copied.companyEntityContextId).isEqualTo(200L)
        assertThat(copied.currencyCode).isEqualTo("EUR")
        assertThat(copied.transactionType).isEqualTo("SECOND_INVOICE")
    }

    @Test
    fun `given settings with same context and currency but different ids, when comparing, then they are not equal`() {
        // GIVEN
        val setting1 = BillingCurrencySetting(
            id = 1L,
            companyEntityContextId = 100L,
            currencyCode = "USD",
            transactionType = "FIRST_INVOICE"
        )
        val setting2 = BillingCurrencySetting(
            id = 2L,
            companyEntityContextId = 100L,
            currencyCode = "USD",
            transactionType = "FIRST_INVOICE"
        )

        // WHEN & THEN
        assertThat(setting1).isNotEqualTo(setting2)
        assertThat(setting1.hashCode()).isNotEqualTo(setting2.hashCode())
    }

    @Test
    fun `given settings with null and non-null ids, when comparing, then they are not equal`() {
        // GIVEN
        val setting1 = BillingCurrencySetting(
            id = null,
            companyEntityContextId = 100L,
            currencyCode = "USD",
            transactionType = "FIRST_INVOICE"
        )
        val setting2 = BillingCurrencySetting(
            id = 1L,
            companyEntityContextId = 100L,
            currencyCode = "USD",
            transactionType = "FIRST_INVOICE"
        )

        // WHEN & THEN
        assertThat(setting1).isNotEqualTo(setting2)
        assertThat(setting1.hashCode()).isNotEqualTo(setting2.hashCode())
    }

    @Test
    fun `given various currency codes, when creating settings, then create with correct values`() {
        // GIVEN
        val companyEntityContextId = 100L
        val transactionType = "FIRST_INVOICE"

        // WHEN
        val usdSetting = BillingCurrencySetting(
            id = 1L,
            companyEntityContextId = companyEntityContextId,
            currencyCode = "USD",
            transactionType = transactionType
        )
        val eurSetting = BillingCurrencySetting(
            id = 2L,
            companyEntityContextId = companyEntityContextId,
            currencyCode = "EUR",
            transactionType = transactionType
        )
        val jpySetting = BillingCurrencySetting(
            id = 3L,
            companyEntityContextId = companyEntityContextId,
            currencyCode = "JPY",
            transactionType = transactionType
        )

        // THEN
        assertThat(usdSetting.currencyCode).isEqualTo("USD")
        assertThat(eurSetting.currencyCode).isEqualTo("EUR")
        assertThat(jpySetting.currencyCode).isEqualTo("JPY")
    }
}
