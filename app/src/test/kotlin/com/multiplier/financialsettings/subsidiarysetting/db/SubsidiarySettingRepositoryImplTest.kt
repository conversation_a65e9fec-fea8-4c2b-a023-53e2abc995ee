package com.multiplier.financialsettings.subsidiarysetting.db

import com.multiplier.financialsettings.subsidiarysetting.domain.MultiplierSubsidiary
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDateTime

class SubsidiarySettingRepositoryImplTest {

    private val jpaRepository = mockk<JpaSubsidiarySettingRepository>()
    private lateinit var repository: SubsidiarySettingRepositoryImpl

    @BeforeEach
    fun setUp() {
        repository = SubsidiarySettingRepositoryImpl(jpaRepository)
    }

    @Test
    fun `given existing context and transaction type, when findByContextIdAndTransactionType, then return mapped domain entity`() {
        // GIVEN
        val contextId = 1L
        val transactionType = "FIRST_INVOICE"
        val subsidiary = MultiplierSubsidiary(country = "SGP", name = "Singapore Subsidiary")
        val jpaEntity = JpaSubsidiarySetting(
            id = 1L,
            companyEntityContextId = contextId,
            transactionType = transactionType,
            subsidiary = subsidiary,
            createdBy = 1L,
            createdOn = LocalDateTime.now(),
            updatedBy = 1L,
            updatedOn = LocalDateTime.now()
        )

        every { jpaRepository.findByContextIdAndTransactionType(contextId, transactionType) } returns jpaEntity

        // WHEN
        val result = repository.findByContextIdAndTransactionType(contextId, transactionType)

        // THEN
        assertThat(result).isNotNull
        assertThat(result!!.id).isEqualTo(1L)
        assertThat(result.companyEntityContextId).isEqualTo(contextId)
        assertThat(result.transactionType).isEqualTo(transactionType)
        assertThat(result.subsidiary).isEqualTo(subsidiary)
        verify { jpaRepository.findByContextIdAndTransactionType(contextId, transactionType) }
    }

    @Test
    fun `given non-existing context and transaction type, when findByContextIdAndTransactionType, then return null`() {
        // GIVEN
        val contextId = 999L
        val transactionType = "NON_EXISTING"

        every { jpaRepository.findByContextIdAndTransactionType(contextId, transactionType) } returns null

        // WHEN
        val result = repository.findByContextIdAndTransactionType(contextId, transactionType)

        // THEN
        assertThat(result).isNull()
        verify { jpaRepository.findByContextIdAndTransactionType(contextId, transactionType) }
    }

    @Test
    fun `given existing context with global setting, when findByContextId, then return global setting`() {
        // GIVEN
        val contextId = 1L
        val subsidiary = MultiplierSubsidiary(country = "USA", name = "Global Subsidiary")
        val jpaEntity = JpaSubsidiarySetting(
            id = 2L,
            companyEntityContextId = contextId,
            transactionType = "GLOBAL",
            subsidiary = subsidiary,
            createdBy = 1L,
            createdOn = LocalDateTime.now(),
            updatedBy = 1L,
            updatedOn = LocalDateTime.now()
        )

        every { jpaRepository.findByContextId(contextId) } returns jpaEntity

        // WHEN
        val result = repository.findByContextId(contextId)

        // THEN
        assertThat(result).isNotNull
        assertThat(result!!.id).isEqualTo(2L)
        assertThat(result.companyEntityContextId).isEqualTo(contextId)
        assertThat(result.transactionType).isEqualTo("GLOBAL")
        assertThat(result.subsidiary).isEqualTo(subsidiary)
        verify { jpaRepository.findByContextId(contextId) }
    }

    @Test
    fun `given context with no global setting, when findByContextId, then return null`() {
        // GIVEN
        val contextId = 999L

        every { jpaRepository.findByContextId(contextId) } returns null

        // WHEN
        val result = repository.findByContextId(contextId)

        // THEN
        assertThat(result).isNull()
        verify { jpaRepository.findByContextId(contextId) }
    }

    @Test
    fun `given different transaction types, when findByContextIdAndTransactionType, then return correct settings`() {
        // GIVEN
        val contextId = 1L
        val subsidiary1 = MultiplierSubsidiary(country = "SGP", name = "Singapore Subsidiary")
        val subsidiary2 = MultiplierSubsidiary(country = "USA", name = "US Subsidiary")
        
        val firstInvoiceEntity = JpaSubsidiarySetting(
            id = 1L,
            companyEntityContextId = contextId,
            transactionType = "FIRST_INVOICE",
            subsidiary = subsidiary1,
            createdBy = 1L,
            createdOn = LocalDateTime.now(),
            updatedBy = 1L,
            updatedOn = LocalDateTime.now()
        )
        
        val secondInvoiceEntity = JpaSubsidiarySetting(
            id = 2L,
            companyEntityContextId = contextId,
            transactionType = "SECOND_INVOICE",
            subsidiary = subsidiary2,
            createdBy = 1L,
            createdOn = LocalDateTime.now(),
            updatedBy = 1L,
            updatedOn = LocalDateTime.now()
        )

        every { jpaRepository.findByContextIdAndTransactionType(contextId, "FIRST_INVOICE") } returns firstInvoiceEntity
        every { jpaRepository.findByContextIdAndTransactionType(contextId, "SECOND_INVOICE") } returns secondInvoiceEntity

        // WHEN
        val firstResult = repository.findByContextIdAndTransactionType(contextId, "FIRST_INVOICE")
        val secondResult = repository.findByContextIdAndTransactionType(contextId, "SECOND_INVOICE")

        // THEN
        assertThat(firstResult!!.transactionType).isEqualTo("FIRST_INVOICE")
        assertThat(firstResult.subsidiary).isEqualTo(subsidiary1)
        
        assertThat(secondResult!!.transactionType).isEqualTo("SECOND_INVOICE")
        assertThat(secondResult.subsidiary).isEqualTo(subsidiary2)
        
        verify { jpaRepository.findByContextIdAndTransactionType(contextId, "FIRST_INVOICE") }
        verify { jpaRepository.findByContextIdAndTransactionType(contextId, "SECOND_INVOICE") }
    }

    @Test
    fun `given JPA entity with null id, when mapping to domain, then map correctly`() {
        // GIVEN
        val contextId = 1L
        val transactionType = "GLOBAL"
        val subsidiary = MultiplierSubsidiary(country = "EUR", name = "European Subsidiary")
        val jpaEntity = JpaSubsidiarySetting(
            id = null,
            companyEntityContextId = contextId,
            transactionType = transactionType,
            subsidiary = subsidiary,
            createdBy = 1L,
            createdOn = LocalDateTime.now(),
            updatedBy = 1L,
            updatedOn = LocalDateTime.now()
        )

        every { jpaRepository.findByContextIdAndTransactionType(contextId, transactionType) } returns jpaEntity

        // WHEN
        val result = repository.findByContextIdAndTransactionType(contextId, transactionType)

        // THEN
        assertThat(result).isNotNull
        assertThat(result!!.id).isNull()
        assertThat(result.companyEntityContextId).isEqualTo(contextId)
        assertThat(result.transactionType).isEqualTo(transactionType)
        assertThat(result.subsidiary).isEqualTo(subsidiary)
    }
}
