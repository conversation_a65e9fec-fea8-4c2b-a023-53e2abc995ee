package com.multiplier.financialsettings.subsidiarysetting.application

import com.multiplier.financialsettings.application.dto.BillingCurrencySettingDimensionInput
import com.multiplier.financialsettings.application.dto.SubsidiarySettingDimensionInput
import com.multiplier.financialsettings.domain.exception.NoMatchingSettingFoundException
import com.multiplier.financialsettings.domain.model.FinancialSettingDimensionValue
import com.multiplier.financialsettings.domain.model.FinancialSettingResponseStrategy
import com.multiplier.financialsettings.subsidiarysetting.domain.MultiplierSubsidiary
import com.multiplier.financialsettings.subsidiarysetting.domain.SubsidiarySetting
import com.multiplier.financialsettings.subsidiarysetting.domain.SubsidiarySettingDimensionValidator
import com.multiplier.financialsettings.subsidiarysetting.domain.SubsidiarySettingRepository
import com.multiplier.payable.engine.domain.entities.TransactionType
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class SubsidiarySettingReadUseCaseTest {

    private val subsidiarySettingRepository = mockk<SubsidiarySettingRepository>()
    private val subsidiaryDimensionValidator = mockk<SubsidiarySettingDimensionValidator>()

    private lateinit var useCase: SubsidiarySettingReadUseCase

    @BeforeEach
    fun setUp() {
        useCase = SubsidiarySettingReadUseCase(
            subsidiarySettingRepository = subsidiarySettingRepository,
            subsidiaryDimensionValidator = subsidiaryDimensionValidator
        )
    }

    @Test
    fun `given MATCH strategy with exact match found, when read, then return matched subsidiary`() {
        // GIVEN
        val contextId = 1L
        val subsidiary = MultiplierSubsidiary(country = "SGP", name = "Singapore Subsidiary")
        val setting = SubsidiarySetting(
            id = 1L,
            companyEntityContextId = contextId,
            transactionType = "FIRST_INVOICE",
            subsidiary = subsidiary
        )
        val dimensionInput = SubsidiarySettingDimensionInput(
            transactionType = FinancialSettingDimensionValue.Of(TransactionType.FIRST_INVOICE)
        )

        every { subsidiaryDimensionValidator.getTransactionTypeStringForQuery(dimensionInput) } returns "FIRST_INVOICE"
        every { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "FIRST_INVOICE") } returns setting

        // WHEN
        val result = useCase.read(contextId, dimensionInput, FinancialSettingResponseStrategy.MATCH)

        // THEN
        assertThat(result).isEqualTo(subsidiary)
        verify { subsidiaryDimensionValidator.getTransactionTypeStringForQuery(dimensionInput) }
        verify { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "FIRST_INVOICE") }
    }

    @Test
    fun `given MATCH strategy with no exact match, when read, then throw NoMatchingSettingFoundException`() {
        // GIVEN
        val contextId = 1L
        val dimensionInput = SubsidiarySettingDimensionInput(
            transactionType = FinancialSettingDimensionValue.Of(TransactionType.SECOND_INVOICE)
        )

        every { subsidiaryDimensionValidator.getTransactionTypeStringForQuery(dimensionInput) } returns "SECOND_INVOICE"
        every { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "SECOND_INVOICE") } returns null

        // WHEN & THEN
        assertThrows<NoMatchingSettingFoundException> {
            useCase.read(contextId, dimensionInput, FinancialSettingResponseStrategy.MATCH)
        }

        verify { subsidiaryDimensionValidator.getTransactionTypeStringForQuery(dimensionInput) }
        verify { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "SECOND_INVOICE") }
    }

    @Test
    fun `given FALLBACK strategy with exact match found, when read, then return matched subsidiary`() {
        // GIVEN
        val contextId = 1L
        val subsidiary = MultiplierSubsidiary(country = "USA", name = "US Subsidiary")
        val setting = SubsidiarySetting(
            id = 1L,
            companyEntityContextId = contextId,
            transactionType = "FIRST_INVOICE",
            subsidiary = subsidiary
        )
        val dimensionInput = SubsidiarySettingDimensionInput(
            transactionType = FinancialSettingDimensionValue.Of(TransactionType.FIRST_INVOICE)
        )

        every { subsidiaryDimensionValidator.getTransactionTypeStringForQuery(dimensionInput) } returns "FIRST_INVOICE"
        every { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "FIRST_INVOICE") } returns setting

        // WHEN
        val result = useCase.read(contextId, dimensionInput, FinancialSettingResponseStrategy.FALLBACK)

        // THEN
        assertThat(result).isEqualTo(subsidiary)
        verify { subsidiaryDimensionValidator.getTransactionTypeStringForQuery(dimensionInput) }
        verify { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "FIRST_INVOICE") }
    }

    @Test
    fun `given FALLBACK strategy with no exact match but global found, when read, then return global subsidiary`() {
        // GIVEN
        val contextId = 1L
        val globalSubsidiary = MultiplierSubsidiary(country = "SGP", name = "Global Subsidiary")
        val globalSetting = SubsidiarySetting(
            id = 2L,
            companyEntityContextId = contextId,
            transactionType = "GLOBAL",
            subsidiary = globalSubsidiary
        )
        val dimensionInput = SubsidiarySettingDimensionInput(
            transactionType = FinancialSettingDimensionValue.Of(TransactionType.SECOND_INVOICE)
        )

        every { subsidiaryDimensionValidator.getTransactionTypeStringForQuery(dimensionInput) } returns "SECOND_INVOICE"
        every { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "SECOND_INVOICE") } returns null
        every { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "GLOBAL") } returns globalSetting

        // WHEN
        val result = useCase.read(contextId, dimensionInput, FinancialSettingResponseStrategy.FALLBACK)

        // THEN
        assertThat(result).isEqualTo(globalSubsidiary)
        verify { subsidiaryDimensionValidator.getTransactionTypeStringForQuery(dimensionInput) }
        verify { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "SECOND_INVOICE") }
        verify { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "GLOBAL") }
    }

    @Test
    fun `given FALLBACK strategy with no exact match and no global, when read, then return global default subsidiary`() {
        // GIVEN
        val contextId = 1L
        val dimensionInput = SubsidiarySettingDimensionInput(
            transactionType = FinancialSettingDimensionValue.Of(TransactionType.GP_FUNDING_INVOICE)
        )

        every { subsidiaryDimensionValidator.getTransactionTypeStringForQuery(dimensionInput) } returns "GP_FUNDING_INVOICE"
        every { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "GP_FUNDING_INVOICE") } returns null
        every { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "GLOBAL") } returns null

        // WHEN
        val result = useCase.read(contextId, dimensionInput, FinancialSettingResponseStrategy.FALLBACK)

        // THEN
        assertThat(result.name).isEqualTo("Multiplier Technologies Pte Ltd")
        assertThat(result.country).isEqualTo("SGP")

        verify { subsidiaryDimensionValidator.getTransactionTypeStringForQuery(dimensionInput) }
        verify { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "GP_FUNDING_INVOICE") }
        verify { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "GLOBAL") }
    }

    @Test
    fun `given non-subsidiary dimension input, when read, then fallback to global and return global default subsidiary`() {
        // GIVEN
        val contextId = 1L
        val dimensionInput = BillingCurrencySettingDimensionInput(
            transactionType = FinancialSettingDimensionValue.Global()
        )

        every { subsidiarySettingRepository.findByContextIdAndTransactionType(contextId, "GLOBAL") } returns null

        // WHEN
        val result = useCase.read(contextId, dimensionInput, FinancialSettingResponseStrategy.FALLBACK)

        // THEN
        assertThat(result.name).isEqualTo("Multiplier Technologies Pte Ltd")
        assertThat(result.country).isEqualTo("SGP")
    }

    @Test
    fun `given null contextId with MATCH strategy, when read, then throw NoMatchingSettingFoundException`() {
        // GIVEN
        val dimensionInput = SubsidiarySettingDimensionInput(
            transactionType = FinancialSettingDimensionValue.Of(TransactionType.FIRST_INVOICE)
        )

        // WHEN & THEN
        assertThrows<NoMatchingSettingFoundException> {
            useCase.read(null, dimensionInput, FinancialSettingResponseStrategy.MATCH)
        }
    }

    @Test
    fun `given null contextId with FALLBACK strategy, when read, then return global default subsidiary`() {
        // GIVEN
        val dimensionInput = SubsidiarySettingDimensionInput(
            transactionType = FinancialSettingDimensionValue.Of(TransactionType.FIRST_INVOICE)
        )

        // WHEN
        val result = useCase.read(null, dimensionInput, FinancialSettingResponseStrategy.FALLBACK)

        // THEN
        assertThat(result.name).isEqualTo("Multiplier Technologies Pte Ltd")
        assertThat(result.country).isEqualTo("SGP")
    }
}
