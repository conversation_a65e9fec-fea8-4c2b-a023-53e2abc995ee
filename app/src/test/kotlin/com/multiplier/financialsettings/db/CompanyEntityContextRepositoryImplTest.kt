package com.multiplier.financialsettings.db

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import java.util.*

class CompanyEntityContextRepositoryImplTest {

    private val jpaRepository = mockk<JpaCompanyEntityContextRepository>()
    private lateinit var repository: CompanyEntityContextRepositoryImpl

    @BeforeEach
    fun setUp() {
        repository = CompanyEntityContextRepositoryImpl(jpaRepository)
    }

    @Test
    fun `given existing company and entity, when findContextId, then return context id`() {
        // GIVEN
        val companyId = 100L
        val entityId = 200L
        val expectedContextId = 1L

        every { jpaRepository.findContextIdByCompanyIdAndEntityId(companyId, entityId) } returns expectedContextId

        // WHEN
        val result = repository.findContextId(companyId, entityId)

        // THEN
        assertThat(result).isEqualTo(expectedContextId)
        verify { jpaRepository.findContextIdByCompanyIdAndEntityId(companyId, entityId) }
    }

    @Test
    fun `given non-existing company and entity, when findContextId, then return null`() {
        // GIVEN
        val companyId = 999L
        val entityId = 999L

        every { jpaRepository.findContextIdByCompanyIdAndEntityId(companyId, entityId) } returns null

        // WHEN
        val result = repository.findContextId(companyId, entityId)

        // THEN
        assertThat(result).isNull()
        verify { jpaRepository.findContextIdByCompanyIdAndEntityId(companyId, entityId) }
    }

    @Test
    fun `given existing context id, when findByContextId, then return mapped domain entity`() {
        // GIVEN
        val contextId = 1L
        val companyId = 100L
        val entityId = 200L
        val jpaEntity = JpaCompanyEntityContext(
            id = contextId,
            companyId = companyId,
            entityId = entityId,
            createdBy = 1L,
            createdOn = LocalDateTime.now(),
            updatedBy = 1L,
            updatedOn = LocalDateTime.now()
        )

        every { jpaRepository.findById(contextId) } returns Optional.of(jpaEntity)

        // WHEN
        val result = repository.findByContextId(contextId)

        // THEN
        assertThat(result).isNotNull
        assertThat(result!!.id).isEqualTo(contextId)
        assertThat(result.companyId).isEqualTo(companyId)
        assertThat(result.entityId).isEqualTo(entityId)
        verify { jpaRepository.findById(contextId) }
    }

    @Test
    fun `given non-existing context id, when findByContextId, then return null`() {
        // GIVEN
        val contextId = 999L

        every { jpaRepository.findById(contextId) } returns Optional.empty()

        // WHEN
        val result = repository.findByContextId(contextId)

        // THEN
        assertThat(result).isNull()
        verify { jpaRepository.findById(contextId) }
    }


}
