package com.multiplier.financialsettings.bankfeethresholdsetting.domain

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class BankFeeThresholdSettingTest {

    @Test
    fun `given valid parameters, when creating BankFeeThresholdSetting, then create with correct values`() {
        // GIVEN
        val id = 1L
        val companyEntityContextId = 100L
        val bankFeeThreshold = BankFeeThresholdAmount(value = 1000.0f, currencyCode = "USD")

        // WHEN
        val setting = BankFeeThresholdSetting(
            id = id,
            companyEntityContextId = companyEntityContextId,
            bankFeeThreshold = bankFeeThreshold
        )

        // THEN
        assertThat(setting.id).isEqualTo(id)
        assertThat(setting.companyEntityContextId).isEqualTo(companyEntityContextId)
        assertThat(setting.bankFeeThreshold).isEqualTo(bankFeeThreshold)
    }

    @Test
    fun `given valid parameters, when creating BankFeeThresholdAmount, then create with correct values`() {
        // GIVEN
        val value = 500.0f
        val currencyCode = "EUR"

        // WHEN
        val amount = BankFeeThresholdAmount(value = value, currencyCode = currencyCode)

        // THEN
        assertThat(amount.value).isEqualTo(value)
        assertThat(amount.currencyCode).isEqualTo(currencyCode)
    }

    @Test
    fun `given different BankFeeThresholdAmount objects with same values, when comparing, then they are equal`() {
        // GIVEN
        val amount1 = BankFeeThresholdAmount(value = 1000.0f, currencyCode = "USD")
        val amount2 = BankFeeThresholdAmount(value = 1000.0f, currencyCode = "USD")

        // WHEN & THEN
        assertThat(amount1).isEqualTo(amount2)
        assertThat(amount1.hashCode()).isEqualTo(amount2.hashCode())
    }

    @Test
    fun `given different BankFeeThresholdSetting objects with same values, when comparing, then they are equal`() {
        // GIVEN
        val bankFeeThreshold = BankFeeThresholdAmount(value = 1000.0f, currencyCode = "USD")
        val setting1 = BankFeeThresholdSetting(
            id = 1L,
            companyEntityContextId = 100L,
            bankFeeThreshold = bankFeeThreshold
        )
        val setting2 = BankFeeThresholdSetting(
            id = 1L,
            companyEntityContextId = 100L,
            bankFeeThreshold = bankFeeThreshold
        )

        // WHEN & THEN
        assertThat(setting1).isEqualTo(setting2)
        assertThat(setting1.hashCode()).isEqualTo(setting2.hashCode())
    }
}
