package com.multiplier.financialsettings.application

import com.multiplier.core.schema.currency.Currency.CurrencyCode
import com.multiplier.financialsettings.application.dto.*
import com.multiplier.financialsettings.bankfeethresholdsetting.domain.BankFeeThresholdAmount
import com.multiplier.financialsettings.domain.model.FinancialSettingDimensionValue
import com.multiplier.financialsettings.domain.model.FinancialSettingResponseStrategy
import com.multiplier.financialsettings.domain.model.FinancialSettingType
import com.multiplier.financialsettings.subsidiarysetting.domain.MultiplierSubsidiary
import com.multiplier.payable.engine.domain.entities.TransactionType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class FinancialSettingServiceTest {

    /**
     * Test implementation of FinancialSettingService for testing interface contract
     */
    private class TestFinancialSettingService : FinancialSettingService {
        override fun getFinancialSetting(request: FinancialSettingRequest): FinancialSettingResponse {
            return when (request.settingType) {
                FinancialSettingType.SUBSIDIARY_SETTING -> {
                    val subsidiary = MultiplierSubsidiary(country = "SGP", name = "Test Subsidiary")
                    SubsidiarySettingResponse(subsidiary)
                }
                FinancialSettingType.BILLING_CURRENCY_SETTING -> {
                    BillingCurrencySettingResponse(CurrencyCode.USD)
                }
                FinancialSettingType.BANK_FEE_THRESHOLD_SETTING -> {
                    val bankFeeThreshold = BankFeeThresholdAmount(value = 1000.0f, currencyCode = "USD")
                    BankFeeThresholdSettingResponse(bankFeeThreshold)
                }
            }
        }
    }

    private val service: FinancialSettingService = TestFinancialSettingService()

    @Test
    fun `given subsidiary setting request, when getFinancialSetting, then return SubsidiarySettingResponse`() {
        // GIVEN
        val request = FinancialSettingRequest(
            companyId = 100L,
            entityId = 200L,
            settingType = FinancialSettingType.SUBSIDIARY_SETTING,
            dimensions = SubsidiarySettingDimensionInput(
                transactionType = FinancialSettingDimensionValue.Of(TransactionType.FIRST_INVOICE)
            ),
            responseStrategy = FinancialSettingResponseStrategy.MATCH
        )

        // WHEN
        val response = service.getFinancialSetting(request)

        // THEN
        assertThat(response).isInstanceOf(SubsidiarySettingResponse::class.java)
        val subsidiaryResponse = response as SubsidiarySettingResponse
        assertThat(subsidiaryResponse.subsidiary.country).isEqualTo("SGP")
        assertThat(subsidiaryResponse.subsidiary.name).isEqualTo("Test Subsidiary")
    }

    @Test
    fun `given billing currency setting request, when getFinancialSetting, then return BillingCurrencySettingResponse`() {
        // GIVEN
        val request = FinancialSettingRequest(
            companyId = 100L,
            entityId = 200L,
            settingType = FinancialSettingType.BILLING_CURRENCY_SETTING,
            dimensions = BillingCurrencySettingDimensionInput(
                transactionType = FinancialSettingDimensionValue.Global()
            ),
            responseStrategy = FinancialSettingResponseStrategy.MATCH
        )

        // WHEN
        val response = service.getFinancialSetting(request)

        // THEN
        assertThat(response).isInstanceOf(BillingCurrencySettingResponse::class.java)
        val currencyResponse = response as BillingCurrencySettingResponse
        assertThat(currencyResponse.currencyCode).isEqualTo(CurrencyCode.USD)
    }

    @Test
    fun `given different request strategies, when getFinancialSetting, then handle both strategies`() {
        // GIVEN
        val matchRequest = FinancialSettingRequest(
            companyId = 100L,
            entityId = 200L,
            settingType = FinancialSettingType.SUBSIDIARY_SETTING,
            dimensions = SubsidiarySettingDimensionInput(
                transactionType = FinancialSettingDimensionValue.Of(TransactionType.FIRST_INVOICE)
            ),
            responseStrategy = FinancialSettingResponseStrategy.MATCH
        )

        val fallbackRequest = FinancialSettingRequest(
            companyId = 100L,
            entityId = 200L,
            settingType = FinancialSettingType.SUBSIDIARY_SETTING,
            dimensions = SubsidiarySettingDimensionInput(
                transactionType = FinancialSettingDimensionValue.Global()
            ),
            responseStrategy = FinancialSettingResponseStrategy.FALLBACK
        )

        // WHEN
        val matchResponse = service.getFinancialSetting(matchRequest)
        val fallbackResponse = service.getFinancialSetting(fallbackRequest)

        // THEN
        assertThat(matchResponse).isInstanceOf(SubsidiarySettingResponse::class.java)
        assertThat(fallbackResponse).isInstanceOf(SubsidiarySettingResponse::class.java)
    }

    @Test
    fun `given different setting types, when getFinancialSetting, then return appropriate response types`() {
        // GIVEN
        val subsidiaryRequest = FinancialSettingRequest(
            companyId = 100L,
            entityId = 200L,
            settingType = FinancialSettingType.SUBSIDIARY_SETTING,
            dimensions = SubsidiarySettingDimensionInput(
                transactionType = FinancialSettingDimensionValue.Of(TransactionType.FIRST_INVOICE)
            ),
            responseStrategy = FinancialSettingResponseStrategy.MATCH
        )

        val currencyRequest = FinancialSettingRequest(
            companyId = 100L,
            entityId = 200L,
            settingType = FinancialSettingType.BILLING_CURRENCY_SETTING,
            dimensions = BillingCurrencySettingDimensionInput(
                transactionType = FinancialSettingDimensionValue.Global()
            ),
            responseStrategy = FinancialSettingResponseStrategy.MATCH
        )

        // WHEN
        val subsidiaryResponse = service.getFinancialSetting(subsidiaryRequest)
        val currencyResponse = service.getFinancialSetting(currencyRequest)

        // THEN
        assertThat(subsidiaryResponse).isInstanceOf(SubsidiarySettingResponse::class.java)
        assertThat(currencyResponse).isInstanceOf(BillingCurrencySettingResponse::class.java)
        assertThat(subsidiaryResponse).isNotEqualTo(currencyResponse)
    }

    @Test
    fun `given service interface, when checking method signature, then has correct method`() {
        // GIVEN
        val serviceClass = FinancialSettingService::class.java

        // WHEN
        val methods = serviceClass.declaredMethods

        // THEN
        assertThat(methods).hasSize(1)
        val method = methods[0]
        assertThat(method.name).isEqualTo("getFinancialSetting")
        assertThat(method.parameterTypes).hasSize(1)
        assertThat(method.parameterTypes[0]).isEqualTo(FinancialSettingRequest::class.java)
        assertThat(method.returnType).isEqualTo(FinancialSettingResponse::class.java)
    }
}
