package com.multiplier.financialsettings.application.dto

import com.multiplier.core.schema.currency.Currency.CurrencyCode
import com.multiplier.financialsettings.subsidiarysetting.domain.MultiplierSubsidiary
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class FinancialSettingResponseTest {

    @Test
    fun `given MultiplierSubsidiary, when creating SubsidiarySettingResponse, then create with correct subsidiary`() {
        // GIVEN
        val subsidiary = MultiplierSubsidiary(country = "SGP", name = "Singapore Subsidiary")

        // WHEN
        val response = SubsidiarySettingResponse(subsidiary = subsidiary)

        // THEN
        assertThat(response.subsidiary).isEqualTo(subsidiary)
        assertThat(response).isInstanceOf(FinancialSettingResponse::class.java)
    }

    @Test
    fun `given CurrencyCode, when creating BillingCurrencySettingResponse, then create with correct currency code`() {
        // GIVEN
        val currencyCode = CurrencyCode.USD

        // WHEN
        val response = BillingCurrencySettingResponse(currencyCode = currencyCode)

        // THEN
        assertThat(response.currencyCode).isEqualTo(currencyCode)
        assertThat(response).isInstanceOf(FinancialSettingResponse::class.java)
    }

    @Test
    fun `given two subsidiary responses with same subsidiary, when comparing, then they are equal`() {
        // GIVEN
        val subsidiary = MultiplierSubsidiary(country = "USA", name = "US Subsidiary")
        val response1 = SubsidiarySettingResponse(subsidiary = subsidiary)
        val response2 = SubsidiarySettingResponse(subsidiary = subsidiary)

        // WHEN & THEN
        assertThat(response1).isEqualTo(response2)
        assertThat(response1.hashCode()).isEqualTo(response2.hashCode())
    }

    @Test
    fun `given two billing currency responses with same currency code, when comparing, then they are equal`() {
        // GIVEN
        val currencyCode = CurrencyCode.EUR
        val response1 = BillingCurrencySettingResponse(currencyCode = currencyCode)
        val response2 = BillingCurrencySettingResponse(currencyCode = currencyCode)

        // WHEN & THEN
        assertThat(response1).isEqualTo(response2)
        assertThat(response1.hashCode()).isEqualTo(response2.hashCode())
    }

    @Test
    fun `given two subsidiary responses with different subsidiaries, when comparing, then they are not equal`() {
        // GIVEN
        val subsidiary1 = MultiplierSubsidiary(country = "SGP", name = "Singapore Subsidiary")
        val subsidiary2 = MultiplierSubsidiary(country = "USA", name = "US Subsidiary")
        val response1 = SubsidiarySettingResponse(subsidiary = subsidiary1)
        val response2 = SubsidiarySettingResponse(subsidiary = subsidiary2)

        // WHEN & THEN
        assertThat(response1).isNotEqualTo(response2)
    }

    @Test
    fun `given two billing currency responses with different currency codes, when comparing, then they are not equal`() {
        // GIVEN
        val response1 = BillingCurrencySettingResponse(currencyCode = CurrencyCode.USD)
        val response2 = BillingCurrencySettingResponse(currencyCode = CurrencyCode.EUR)

        // WHEN & THEN
        assertThat(response1).isNotEqualTo(response2)
    }

    @Test
    fun `given subsidiary response and billing currency response, when comparing, then they are not equal`() {
        // GIVEN
        val subsidiary = MultiplierSubsidiary(country = "SGP", name = "Singapore Subsidiary")
        val subsidiaryResponse = SubsidiarySettingResponse(subsidiary = subsidiary)
        val currencyResponse = BillingCurrencySettingResponse(currencyCode = CurrencyCode.USD)

        // WHEN & THEN
        assertThat(subsidiaryResponse).isNotEqualTo(currencyResponse)
    }

    @Test
    fun `given different currency codes, when creating billing currency responses, then create with correct currency codes`() {
        // GIVEN & WHEN
        val usdResponse = BillingCurrencySettingResponse(currencyCode = CurrencyCode.USD)
        val eurResponse = BillingCurrencySettingResponse(currencyCode = CurrencyCode.EUR)
        val jpyResponse = BillingCurrencySettingResponse(currencyCode = CurrencyCode.JPY)
        val gbpResponse = BillingCurrencySettingResponse(currencyCode = CurrencyCode.GBP)

        // THEN
        assertThat(usdResponse.currencyCode).isEqualTo(CurrencyCode.USD)
        assertThat(eurResponse.currencyCode).isEqualTo(CurrencyCode.EUR)
        assertThat(jpyResponse.currencyCode).isEqualTo(CurrencyCode.JPY)
        assertThat(gbpResponse.currencyCode).isEqualTo(CurrencyCode.GBP)
    }

    @Test
    fun `given different subsidiaries, when creating subsidiary responses, then create with correct subsidiaries`() {
        // GIVEN
        val sgpSubsidiary = MultiplierSubsidiary(country = "SGP", name = "Singapore Subsidiary")
        val usaSubsidiary = MultiplierSubsidiary(country = "USA", name = "US Subsidiary")
        val eurSubsidiary = MultiplierSubsidiary(country = "EUR", name = "European Subsidiary")

        // WHEN
        val sgpResponse = SubsidiarySettingResponse(subsidiary = sgpSubsidiary)
        val usaResponse = SubsidiarySettingResponse(subsidiary = usaSubsidiary)
        val eurResponse = SubsidiarySettingResponse(subsidiary = eurSubsidiary)

        // THEN
        assertThat(sgpResponse.subsidiary).isEqualTo(sgpSubsidiary)
        assertThat(usaResponse.subsidiary).isEqualTo(usaSubsidiary)
        assertThat(eurResponse.subsidiary).isEqualTo(eurSubsidiary)
    }

    @Test
    fun `given responses, when checking interface implementation, then implement FinancialSettingResponse`() {
        // GIVEN
        val subsidiary = MultiplierSubsidiary(country = "SGP", name = "Singapore Subsidiary")
        val subsidiaryResponse = SubsidiarySettingResponse(subsidiary = subsidiary)
        val currencyResponse = BillingCurrencySettingResponse(currencyCode = CurrencyCode.USD)

        // WHEN & THEN
        assertThat(subsidiaryResponse).isInstanceOf(FinancialSettingResponse::class.java)
        assertThat(currencyResponse).isInstanceOf(FinancialSettingResponse::class.java)
    }
}
