package com.multiplier.financialsettings.domain.model

import com.multiplier.payable.engine.domain.entities.TransactionType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class FinancialSettingDimensionTest {

    @Test
    fun `given transaction type, when creating FinancialSettingTransactionTypeDimension, then return correct values`() {
        // GIVEN
        val transactionType = TransactionType.FIRST_INVOICE

        // WHEN
        val dimension = FinancialSettingTransactionTypeDimension(transactionType)

        // THEN
        assertThat(dimension.type).isEqualTo(FinancialSettingDimensionType.TRANSACTION_TYPE)
        assertThat(dimension.value).isEqualTo(transactionType)
        assertThat(dimension.transactionType).isEqualTo(transactionType)
    }

    @Test
    fun `given SECOND_INVOICE transaction type, when creating dimension, then return correct values`() {
        // GIVEN
        val transactionType = TransactionType.SECOND_INVOICE

        // WHEN
        val dimension = FinancialSettingTransactionTypeDimension(transactionType)

        // THEN
        assertThat(dimension.type).isEqualTo(FinancialSettingDimensionType.TRANSACTION_TYPE)
        assertThat(dimension.value).isEqualTo(transactionType)
        assertThat(dimension.transactionType).isEqualTo(transactionType)
    }

    @Test
    fun `given GP_FUNDING_INVOICE transaction type, when creating dimension, then return correct values`() {
        // GIVEN
        val transactionType = TransactionType.GP_FUNDING_INVOICE

        // WHEN
        val dimension = FinancialSettingTransactionTypeDimension(transactionType)

        // THEN
        assertThat(dimension.type).isEqualTo(FinancialSettingDimensionType.TRANSACTION_TYPE)
        assertThat(dimension.value).isEqualTo(transactionType)
        assertThat(dimension.transactionType).isEqualTo(transactionType)
    }

    @Test
    fun `given two dimensions with same transaction type, when comparing, then they are equal`() {
        // GIVEN
        val dimension1 = FinancialSettingTransactionTypeDimension(TransactionType.FIRST_INVOICE)
        val dimension2 = FinancialSettingTransactionTypeDimension(TransactionType.FIRST_INVOICE)

        // WHEN & THEN
        assertThat(dimension1).isEqualTo(dimension2)
        assertThat(dimension1.hashCode()).isEqualTo(dimension2.hashCode())
    }

    @Test
    fun `given two dimensions with different transaction types, when comparing, then they are not equal`() {
        // GIVEN
        val dimension1 = FinancialSettingTransactionTypeDimension(TransactionType.FIRST_INVOICE)
        val dimension2 = FinancialSettingTransactionTypeDimension(TransactionType.SECOND_INVOICE)

        // WHEN & THEN
        assertThat(dimension1).isNotEqualTo(dimension2)
    }

    @Test
    fun `given dimension, when checking interface implementation, then implements FinancialSettingDimension`() {
        // GIVEN
        val dimension = FinancialSettingTransactionTypeDimension(TransactionType.FIRST_INVOICE)

        // WHEN & THEN
        assertThat(dimension).isInstanceOf(FinancialSettingDimension::class.java)
    }
}
