package com.multiplier.payable.severance.history.service

import com.multiplier.payable.severance.history.db.JpaContractSeveranceHistory
import com.multiplier.payable.types.Amount
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.ZonedDateTime

class ContractSeveranceHistoryEntityMapperTest {

    private val mapper = ContractSeveranceHistoryEntityMapper()
    private val now = ZonedDateTime.now()

    @Test
    fun `should map JpaContractSeveranceHistory to ContractSeveranceHistoryEntity`() {
        // given
        val jpaEntity = JpaContractSeveranceHistory(
            id = 1L,
            companyPayableId = 100L,
            amount = Amount.newBuilder()
                .currency(CurrencyCode.CAD)
                .amount(1000.00)
                .build(),
            contractId = 200L,
            startDate = now,
            endDate = now.plusDays(30),
            amountInContractCurrency = Amount.newBuilder()
                .currency(CurrencyCode.CAD)
                .amount(100.00)
                .build()
        )

        // when
        val domainEntity = mapper.toDomain(jpaEntity)

        // then
        assertEquals(jpaEntity.id, domainEntity.id)
        assertEquals(jpaEntity.companyPayableId, domainEntity.companyPayableId)
        assertEquals(jpaEntity.amount, domainEntity.amount)
        assertEquals(jpaEntity.contractId, domainEntity.contractId)
        assertEquals(jpaEntity.startDate, domainEntity.startDate)
        assertEquals(jpaEntity.endDate, domainEntity.endDate)
        assertEquals(jpaEntity.amountInContractCurrency, domainEntity.amountInContractCurrency)
    }
} 