package com.multiplier.payable.severance

import com.multiplier.core.payable.currencyexchange.CurrencyExchangeV2Service
import com.multiplier.payable.engine.contract.Contract
import com.multiplier.payable.engine.severance.Severance
import com.multiplier.payable.severance.refund.SeveranceRefundCalculator
import com.multiplier.payable.severance.refund.SeveranceRefundCalculatorInput
import com.multiplier.payable.types.CurrencyCode
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.extension.ExtendWith
import java.math.BigDecimal
import java.time.LocalDate

@ExtendWith(MockKExtension::class)
class SeveranceRefundCalculatorTest {

    @InjectMockKs
    private lateinit var calculator: SeveranceRefundCalculator

    @Test
    fun `should return null when severance list is empty`() {
        val contact = mockk<Contract> {
            every { id } returns 1L
        }
        val input = SeveranceRefundCalculatorInput(
            severances = emptyList(),
            companyBillingCurrency = CurrencyCode.USD,
            contract = contact
        )

        val result = calculator.calculate(input)
        assertNull(result)
    }

    @Test
    fun `should sum severance when all are in same currency`() {
        val severance1 = testSeverance(CurrencyCode.USD, 100.0, LocalDate.of(2021, 1, 1), LocalDate.of(2021, 1, 31))
        val severance2 = testSeverance(CurrencyCode.USD, 200.0, LocalDate.of(2021, 2, 1), LocalDate.of(2021, 2, 28))

        val contact = mockk<Contract>()

        val input = SeveranceRefundCalculatorInput(
            severances = listOf(severance1, severance2),
            companyBillingCurrency = CurrencyCode.USD,
            contract = contact,
        )

        val result = calculator.calculate(input)

        assertNotNull(result)
        result?.let {
            assertEquals(300.0, result.severance)
            assertEquals(CurrencyCode.USD, result.currencyCode)
            assertEquals(LocalDate.of(2021, 1, 1), result.startDate)
            assertEquals(LocalDate.of(2021, 2, 28), result.endDate)
        }
    }

    @Test
    fun `should return 0 when multiple currencies exist`() {
        val severance1 = testSeverance(CurrencyCode.USD, 100.0, LocalDate.of(2021, 1, 1), LocalDate.of(2021, 1, 31))
        val severance2 = testSeverance(CurrencyCode.SGD, 200.0, LocalDate.of(2021, 2, 1), LocalDate.of(2021, 2, 28))

        val contact = mockk<Contract>()

        val input = SeveranceRefundCalculatorInput(
            severances = listOf(severance1, severance2),
            companyBillingCurrency = CurrencyCode.EUR,
            contract = contact,
        )

        val result = calculator.calculate(input)

        assertNotNull(result)
        result?.let {
            assertEquals(0.0, result.severance)
            assertEquals(CurrencyCode.EUR, result.currencyCode)
            assertEquals(LocalDate.of(2021, 1, 1), result.startDate)
            assertEquals(LocalDate.of(2021, 2, 28), result.endDate)
        }

    }

    private fun testSeverance(currency: CurrencyCode, amount: Double, startDate: LocalDate, endDate: LocalDate) =
        Severance(
            companyId = 1L,
            contractId = 123L,
            startDate = startDate,
            endDate = endDate,
            severance = amount,
            currencyCode = currency,
            fetchTime = 0L
        )
}