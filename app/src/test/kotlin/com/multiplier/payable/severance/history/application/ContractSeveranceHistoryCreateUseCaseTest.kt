package com.multiplier.payable.severance.history.application

import com.multiplier.payable.severance.history.domain.ContractSeveranceHistoryEntity
import com.multiplier.payable.severance.history.domain.ContractSeveranceHistoryRepository
import com.multiplier.payable.severance.history.domain.create.ContractSeveranceHistoryCreateInput
import com.multiplier.payable.severance.history.domain.create.filter.ContractSeveranceHistoryCreationFilter
import com.multiplier.payable.severance.history.domain.create.validation.ContractSeveranceHistoryCreationValidation
import com.multiplier.payable.types.Amount
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.never
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.ZonedDateTime

@ExtendWith(MockitoExtension::class)
class ContractSeveranceHistoryCreateUseCaseTest {

    @Mock
    private lateinit var contractSeveranceHistoryRepository: ContractSeveranceHistoryRepository

    @Mock
    private lateinit var contractSeveranceHistoryCreationValidation: ContractSeveranceHistoryCreationValidation

    @Mock
    private lateinit var contractSeveranceHistoryCreationFilter: ContractSeveranceHistoryCreationFilter

    private lateinit var contractSeveranceHistoryCreateUseCase: ContractSeveranceHistoryCreateUseCase

    private val testCompanyPayableId = 1L
    private val testContractId = 2L
    private val testAmount = Amount.newBuilder()
        .currency(CurrencyCode.USD)
        .amount(1000.00)
        .build()
    
    private val testContractAmount = Amount.newBuilder()
        .currency(CurrencyCode.USD)
        .amount(1000.00)
        .build()

    private val testStartDate = ZonedDateTime.now()
    private val testEndDate = testStartDate.plusDays(30)

    @BeforeEach
    fun setup() {
        MockitoAnnotations.openMocks(this)
        contractSeveranceHistoryCreateUseCase = ContractSeveranceHistoryCreateUseCase(
            contractSeveranceHistoryRepository,
            contractSeveranceHistoryCreationValidation,
            contractSeveranceHistoryCreationFilter
        )
    }

    @Test
    fun `should create contract severance history when filter allows and validation passes`() {
        // Arrange
        val input = ContractSeveranceHistoryCreateInput(
            companyPayableId = testCompanyPayableId,
            contractId = testContractId,
            amount = testAmount,
            amountInContractCurrency = testContractAmount,
            startDate = testStartDate,
            endDate = testEndDate
        )

        val expectedEntity = ContractSeveranceHistoryEntity(
            id = 1L,
            companyPayableId = testCompanyPayableId,
            contractId = testContractId,
            amount = testAmount,
            amountInContractCurrency = testContractAmount,
            startDate = testStartDate,
            endDate = testEndDate
        )

        whenever(contractSeveranceHistoryCreationFilter.filter(input)).thenReturn(false) // Filter allows processing
        whenever(contractSeveranceHistoryRepository.upsertContractSeveranceHistoryForContractIdAndTimePeriodAndCompanyPayableId(input))
            .thenReturn(expectedEntity)

        // Act
        val result = contractSeveranceHistoryCreateUseCase.create(input)

        // Assert
        assertNotNull(result)
        assertEquals(expectedEntity, result.contractSeveranceHistoryEntity)
        verify(contractSeveranceHistoryCreationFilter).filter(input)
        verify(contractSeveranceHistoryCreationValidation).validate(input)
        verify(contractSeveranceHistoryRepository).upsertContractSeveranceHistoryForContractIdAndTimePeriodAndCompanyPayableId(input)
    }

    @Test
    fun `should skip processing when filter blocks`() {
        // Arrange
        val input = ContractSeveranceHistoryCreateInput(
            companyPayableId = testCompanyPayableId,
            contractId = testContractId,
            amount = testAmount,
            startDate = testStartDate,
            endDate = testEndDate,
            amountInContractCurrency = testContractAmount
        )

        whenever(contractSeveranceHistoryCreationFilter.filter(input)).thenReturn(true) // Filter blocks processing

        // Act
        val result = contractSeveranceHistoryCreateUseCase.create(input)

        // Assert
        assertNotNull(result)
        assertNull(result.contractSeveranceHistoryEntity) // No entity should be created
        verify(contractSeveranceHistoryCreationFilter).filter(input)
        verify(contractSeveranceHistoryCreationValidation, never()).validate(any()) // Validation should not be called
        verify(contractSeveranceHistoryRepository, never()).upsertContractSeveranceHistoryForContractIdAndTimePeriodAndCompanyPayableId(any()) // Repository should not be called
    }

    @Test
    fun `should propagate validation exception when validation fails`() {
        // Arrange
        val input = ContractSeveranceHistoryCreateInput(
            companyPayableId = testCompanyPayableId,
            contractId = testContractId,
            amount = testAmount,
            startDate = testStartDate,
            endDate = testEndDate,
            amountInContractCurrency = testContractAmount
        )

        val expectedException = IllegalArgumentException("Validation failed")
        whenever(contractSeveranceHistoryCreationFilter.filter(input)).thenReturn(false) // Filter allows processing
        whenever(contractSeveranceHistoryCreationValidation.validate(input)).thenThrow(expectedException)

        // Act & Assert
        val exception = assertThrows(IllegalArgumentException::class.java) {
            contractSeveranceHistoryCreateUseCase.create(input)
        }

        assertEquals(expectedException, exception)
        verify(contractSeveranceHistoryCreationFilter).filter(input)
        verify(contractSeveranceHistoryCreationValidation).validate(input)
        verify(contractSeveranceHistoryRepository, never()).upsertContractSeveranceHistoryForContractIdAndTimePeriodAndCompanyPayableId(any())
    }

    @Test
    fun `should propagate repository exception when creation fails`() {
        // Arrange
        val input = ContractSeveranceHistoryCreateInput(
            companyPayableId = testCompanyPayableId,
            contractId = testContractId,
            amount = testAmount,
            startDate = testStartDate,
            endDate = testEndDate,
            amountInContractCurrency = testContractAmount
        )

        val expectedException = RuntimeException("Repository error")
        whenever(contractSeveranceHistoryCreationFilter.filter(input)).thenReturn(false) // Filter allows processing
        whenever(contractSeveranceHistoryRepository.upsertContractSeveranceHistoryForContractIdAndTimePeriodAndCompanyPayableId(input))
            .thenThrow(expectedException)

        // Act & Assert
        val exception = assertThrows(RuntimeException::class.java) {
            contractSeveranceHistoryCreateUseCase.create(input)
        }

        assertEquals(expectedException, exception)
        verify(contractSeveranceHistoryCreationFilter).filter(input)
        verify(contractSeveranceHistoryCreationValidation).validate(input)
        verify(contractSeveranceHistoryRepository).upsertContractSeveranceHistoryForContractIdAndTimePeriodAndCompanyPayableId(input)
    }
} 
