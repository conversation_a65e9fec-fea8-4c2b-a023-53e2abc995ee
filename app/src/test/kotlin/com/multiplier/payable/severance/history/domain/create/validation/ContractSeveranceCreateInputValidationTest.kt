package com.multiplier.payable.severance.history.domain.create.validation

import com.multiplier.payable.severance.history.domain.create.ContractSeveranceHistoryCreateInput
import com.multiplier.payable.types.Amount
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import java.time.ZonedDateTime

@ExtendWith(MockitoExtension::class)
class ContractSeveranceCreateInputValidationTest {

    private lateinit var validation: ContractSeveranceCreateInputValidation

    @BeforeEach
    fun setUp() {
        validation = ContractSeveranceCreateInputValidation()
    }

    @Test
    fun `should pass validation when input is valid`() {
        // given
        val validInput = ContractSeveranceHistoryCreateInput(
            companyPayableId = 1L,
            startDate = ZonedDateTime.now().minusDays(30),
            endDate = ZonedDateTime.now(),
            contractId = 2L,
            amount = Amount.newBuilder()
                .amount(100.0)
                .currency(CurrencyCode.USD)
                .build(),
            amountInContractCurrency = Amount.newBuilder()
                .amount(80.0)
                .currency(CurrencyCode.CAD)
                .build()
        )

        // when/then - no exception should be thrown
        validation.validate(validInput)
    }

    @Test
    fun `should throw IllegalArgumentException when billing amount is null`() {
        // given
        val invalidInput = ContractSeveranceHistoryCreateInput(
            companyPayableId = 1L,
            startDate = ZonedDateTime.now().minusDays(30),
            endDate = ZonedDateTime.now(),
            contractId = 2L,
            amount = Amount.newBuilder()
                .currency(CurrencyCode.USD)
                .build(), // amount is null
            amountInContractCurrency = Amount.newBuilder()
                .amount(80.0)
                .currency(CurrencyCode.CAD)
                .build()
        )

        // when/then
        assertThrows<IllegalArgumentException> {
            validation.validate(invalidInput)
        }
    }

    @Test
    fun `should throw IllegalArgumentException when billing currency is null`() {
        // given
        val invalidInput = ContractSeveranceHistoryCreateInput(
            companyPayableId = 1L,
            startDate = ZonedDateTime.now().minusDays(30),
            endDate = ZonedDateTime.now(),
            contractId = 2L,
            amount = Amount.newBuilder()
                .amount(100.0)
                .build(), // currency is null
            amountInContractCurrency = Amount.newBuilder()
                .amount(80.0)
                .currency(CurrencyCode.CAD)
                .build()
        )

        // when/then
        assertThrows<IllegalArgumentException> {
            validation.validate(invalidInput)
        }
    }

    @Test
    fun `should throw IllegalArgumentException when contract currency amount is null`() {
        // given
        val invalidInput = ContractSeveranceHistoryCreateInput(
            companyPayableId = 1L,
            startDate = ZonedDateTime.now().minusDays(30),
            endDate = ZonedDateTime.now(),
            contractId = 2L,
            amount = Amount.newBuilder()
                .amount(100.0)
                .currency(CurrencyCode.USD)
                .build(),
            amountInContractCurrency = Amount.newBuilder()
                .currency(CurrencyCode.CAD)
                .build() // amount is null
        )

        // when/then
        assertThrows<IllegalArgumentException> {
            validation.validate(invalidInput)
        }
    }

    @Test
    fun `should throw IllegalArgumentException when contract currency is null`() {
        // given
        val invalidInput = ContractSeveranceHistoryCreateInput(
            companyPayableId = 1L,
            startDate = ZonedDateTime.now().minusDays(30),
            endDate = ZonedDateTime.now(),
            contractId = 2L,
            amount = Amount.newBuilder()
                .amount(100.0)
                .currency(CurrencyCode.USD)
                .build(),
            amountInContractCurrency = Amount.newBuilder()
                .amount(80.0)
                .build() // currency is null
        )

        // when/then
        assertThrows<IllegalArgumentException> {
            validation.validate(invalidInput)
        }
    }
} 