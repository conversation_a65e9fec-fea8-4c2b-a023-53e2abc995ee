package com.multiplier.payable.severance.history.service

import com.multiplier.payable.severance.history.db.JpaContractSeveranceHistory
import com.multiplier.payable.severance.history.db.JpaContractSeveranceHistoryRepository
import com.multiplier.payable.severance.history.domain.ContractSeveranceHistoryEntity
import com.multiplier.payable.severance.history.domain.create.ContractSeveranceHistoryCreateInput
import com.multiplier.payable.types.Amount
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.never
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.ZonedDateTime

@ExtendWith(MockitoExtension::class)
class ContractSeveranceHistoryRepositoryImplTest {

    @Mock
    private lateinit var jpaContractSeveranceHistoryRepository: JpaContractSeveranceHistoryRepository

    @Mock
    private lateinit var contractSeveranceHistoryEntityMapper: ContractSeveranceHistoryEntityMapper

    private lateinit var contractSeveranceHistoryRepositoryImpl: ContractSeveranceHistoryRepositoryImpl

    private val now = ZonedDateTime.now()
    private val contractId = 100L
    private val companyPayableId = 200L
    private val amount = Amount.newBuilder()
        .currency(CurrencyCode.CAD)
        .amount(1000.00)
        .build()
    private val amountInContractCurrency = Amount.newBuilder()
        .currency(CurrencyCode.USD)
        .amount(750.00)
        .build()

    @BeforeEach
    fun setUp() {
        contractSeveranceHistoryRepositoryImpl = ContractSeveranceHistoryRepositoryImpl(
            jpaContractSeveranceHistoryRepository, 
            contractSeveranceHistoryEntityMapper
        )
    }

    @Test
    fun `should check if contract severance history exists for contract id and time period`() {
        // given
        val startDate = now
        val endDate = now.plusDays(30)

        whenever(jpaContractSeveranceHistoryRepository.existsByContractIdAndStartDateGreaterThanEqualAndEndDateLessThanEqualAndCompanyPayableId(
            contractId = contractId,
            startDate = startDate,
            endDate = endDate,
            companyPayableId = companyPayableId
        )).thenReturn(true)

        // when
        val result = contractSeveranceHistoryRepositoryImpl.isContractSeveranceHistoryExistsForContractIdAndTimePeriodAndCompanyPayableId(
            contractId, startDate, endDate, companyPayableId
        )

        // then
        assertTrue(result)
        verify(jpaContractSeveranceHistoryRepository).existsByContractIdAndStartDateGreaterThanEqualAndEndDateLessThanEqualAndCompanyPayableId(
            contractId = contractId,
            startDate = startDate,
            endDate = endDate,
            companyPayableId = companyPayableId
        )
    }

    @Test
    fun `should create contract severance history for contract id and time period`() {
        // given
        val startDate = now
        val endDate = now.plusDays(30)
        
        val createInput = ContractSeveranceHistoryCreateInput(
            companyPayableId = companyPayableId,
            startDate = startDate,
            endDate = endDate,
            contractId = contractId,
            amount = amount,
            amountInContractCurrency = amountInContractCurrency
        )
        
        val jpaEntity = JpaContractSeveranceHistory(
            companyPayableId = createInput.companyPayableId,
            contractId = createInput.contractId,
            amount = createInput.amount,
            amountInContractCurrency = createInput.amountInContractCurrency,
            startDate = createInput.startDate,
            endDate = createInput.endDate
        )
        
        val savedJpaEntity = jpaEntity.copy(id = 1L)
        val domainEntity = ContractSeveranceHistoryEntity(
            id = savedJpaEntity.id,
            companyPayableId = savedJpaEntity.companyPayableId,
            amount = savedJpaEntity.amount,
            amountInContractCurrency = savedJpaEntity.amountInContractCurrency,
            contractId = savedJpaEntity.contractId,
            startDate = savedJpaEntity.startDate,
            endDate = savedJpaEntity.endDate
        )
        
        whenever(jpaContractSeveranceHistoryRepository.save(any())).thenReturn(savedJpaEntity)
        whenever(contractSeveranceHistoryEntityMapper.toDomain(savedJpaEntity)).thenReturn(domainEntity)

        // when
        val result = contractSeveranceHistoryRepositoryImpl.createContractSeveranceHistoryForContractIdAndTimePeriod(createInput)

        // then
        assertEquals(domainEntity, result)
        assertEquals(amount, result.amount)
        assertEquals(amountInContractCurrency, result.amountInContractCurrency)
        assertEquals(contractId, result.contractId)
        assertEquals(companyPayableId, result.companyPayableId)
        assertEquals(startDate, result.startDate)
        assertEquals(endDate, result.endDate)
        assertEquals(1L, result.id)
        
        verify(jpaContractSeveranceHistoryRepository).save(any())
        verify(contractSeveranceHistoryEntityMapper).toDomain(savedJpaEntity)
    }

    @Test
    fun `should find contract severance history for contract id and time period`() {
        // given
        val startDate = now
        val endDate = now.plusDays(30)

        val jpaEntity = JpaContractSeveranceHistory(
            id = 1L,
            companyPayableId = companyPayableId,
            contractId = contractId,
            amount = amount,
            amountInContractCurrency = amountInContractCurrency,
            startDate = startDate,
            endDate = endDate
        )

        val domainEntity = ContractSeveranceHistoryEntity(
            id = jpaEntity.id,
            companyPayableId = jpaEntity.companyPayableId,
            amount = jpaEntity.amount,
            amountInContractCurrency = jpaEntity.amountInContractCurrency,
            contractId = jpaEntity.contractId,
            startDate = jpaEntity.startDate,
            endDate = jpaEntity.endDate
        )

        whenever(jpaContractSeveranceHistoryRepository.findByContractIdAndStartDateGreaterThanEqualAndEndDateLessThanEqualAndCompanyPayableId(
            contractId = contractId,
            startDate = startDate,
            endDate = endDate,
            companyPayableId = companyPayableId
        )).thenReturn(jpaEntity)

        whenever(contractSeveranceHistoryEntityMapper.toDomain(jpaEntity)).thenReturn(domainEntity)

        // when
        val result = contractSeveranceHistoryRepositoryImpl.findContractSeveranceHistoryForContractIdAndTimePeriodAndCompanyPayableId(
            contractId, startDate, endDate, companyPayableId
        )

        // then
        assertNotNull(result)
        assertEquals(domainEntity, result)
        assertEquals(amount, result!!.amount)
        assertEquals(amountInContractCurrency, result.amountInContractCurrency)
        assertEquals(contractId, result.contractId)
        assertEquals(companyPayableId, result.companyPayableId)
        assertEquals(startDate, result.startDate)
        assertEquals(endDate, result.endDate)
        assertEquals(1L, result.id)

        verify(jpaContractSeveranceHistoryRepository).findByContractIdAndStartDateGreaterThanEqualAndEndDateLessThanEqualAndCompanyPayableId(
            contractId = contractId,
            startDate = startDate,
            endDate = endDate,
            companyPayableId = companyPayableId
        )
        verify(contractSeveranceHistoryEntityMapper).toDomain(jpaEntity)
    }
    
    @Test
    fun `should return null when no contract severance history found for contract id and time period`() {
        // given
        val startDate = now
        val endDate = now.plusDays(30)

        whenever(jpaContractSeveranceHistoryRepository.findByContractIdAndStartDateGreaterThanEqualAndEndDateLessThanEqualAndCompanyPayableId(
            contractId = contractId,
            startDate = startDate,
            endDate = endDate,
            companyPayableId = companyPayableId
        )).thenReturn(null)

        // when
        val result = contractSeveranceHistoryRepositoryImpl.findContractSeveranceHistoryForContractIdAndTimePeriodAndCompanyPayableId(
            contractId, startDate, endDate, companyPayableId
        )

        // then
        assertNull(result)
        verify(jpaContractSeveranceHistoryRepository).findByContractIdAndStartDateGreaterThanEqualAndEndDateLessThanEqualAndCompanyPayableId(
            contractId = contractId,
            startDate = startDate,
            endDate = endDate,
            companyPayableId = companyPayableId
        )
        verify(contractSeveranceHistoryEntityMapper, never()).toDomain(any())
    }

    @Test
    fun `should get all contract severance histories for contract id`() {
        // given
        val jpaEntity1 = JpaContractSeveranceHistory(
            id = 1L,
            companyPayableId = companyPayableId,
            contractId = contractId,
            amount = amount,
            amountInContractCurrency = amountInContractCurrency,
            startDate = now,
            endDate = now.plusDays(30)
        )
        
        val jpaEntity2 = JpaContractSeveranceHistory(
            id = 2L,
            companyPayableId = companyPayableId,
            contractId = contractId,
            amount = Amount.newBuilder()
                .currency(CurrencyCode.CAD)
                .amount(1500.00)
                .build(),
            amountInContractCurrency = Amount.newBuilder()
                .currency(CurrencyCode.USD)
                .amount(1125.00)
                .build(),
            startDate = now.plusDays(31),
            endDate = now.plusDays(60)
        )
        
        val domainEntity1 = ContractSeveranceHistoryEntity(
            id = jpaEntity1.id,
            companyPayableId = jpaEntity1.companyPayableId,
            amount = jpaEntity1.amount,
            amountInContractCurrency = jpaEntity1.amountInContractCurrency,
            contractId = jpaEntity1.contractId,
            startDate = jpaEntity1.startDate,
            endDate = jpaEntity1.endDate
        )
        
        val domainEntity2 = ContractSeveranceHistoryEntity(
            id = jpaEntity2.id,
            companyPayableId = jpaEntity2.companyPayableId,
            amount = jpaEntity2.amount,
            amountInContractCurrency = jpaEntity2.amountInContractCurrency,
            contractId = jpaEntity2.contractId,
            startDate = jpaEntity2.startDate,
            endDate = jpaEntity2.endDate
        )
        
        val jpaEntities = listOf(jpaEntity1, jpaEntity2)
        val domainEntities = listOf(domainEntity1, domainEntity2)
        
        whenever(jpaContractSeveranceHistoryRepository.findAllByContractId(contractId)).thenReturn(jpaEntities)
        whenever(contractSeveranceHistoryEntityMapper.toDomain(jpaEntity1)).thenReturn(domainEntity1)
        whenever(contractSeveranceHistoryEntityMapper.toDomain(jpaEntity2)).thenReturn(domainEntity2)

        // when
        val result = contractSeveranceHistoryRepositoryImpl.getContractSeveranceHistoryForContractId(contractId)

        // then
        assertEquals(domainEntities, result)
        assertEquals(2, result.size)
        
        // Verify first entity
        assertEquals(amount, result[0].amount)
        assertEquals(amountInContractCurrency, result[0].amountInContractCurrency)
        assertEquals(contractId, result[0].contractId)
        assertEquals(companyPayableId, result[0].companyPayableId)
        assertEquals(now, result[0].startDate)
        assertEquals(now.plusDays(30), result[0].endDate)
        assertEquals(1L, result[0].id)
        
        // Verify second entity
        assertEquals(1500.00, result[1].amount.amount)
        assertEquals(CurrencyCode.CAD, result[1].amount.currency)
        assertEquals(1125.00, result[1].amountInContractCurrency.amount)
        assertEquals(CurrencyCode.USD, result[1].amountInContractCurrency.currency)
        assertEquals(contractId, result[1].contractId)
        assertEquals(companyPayableId, result[1].companyPayableId)
        assertEquals(now.plusDays(31), result[1].startDate)
        assertEquals(now.plusDays(60), result[1].endDate)
        assertEquals(2L, result[1].id)
        
        verify(jpaContractSeveranceHistoryRepository).findAllByContractId(contractId)
        verify(contractSeveranceHistoryEntityMapper).toDomain(jpaEntity1)
        verify(contractSeveranceHistoryEntityMapper).toDomain(jpaEntity2)
    }

    @Test
    fun `should create new record when upserting and no existing record found`() {
        // given
        val createInput = ContractSeveranceHistoryCreateInput(
            companyPayableId = companyPayableId,
            contractId = contractId,
            amount = amount,
            amountInContractCurrency = amountInContractCurrency,
            startDate = now,
            endDate = now.plusDays(30)
        )

        val savedJpaEntity = JpaContractSeveranceHistory(
            id = 1L,
            companyPayableId = companyPayableId,
            contractId = contractId,
            amount = amount,
            amountInContractCurrency = amountInContractCurrency,
            startDate = now,
            endDate = now.plusDays(30)
        )

        val expectedDomainEntity = ContractSeveranceHistoryEntity(
            id = 1L,
            companyPayableId = companyPayableId,
            contractId = contractId,
            amount = amount,
            amountInContractCurrency = amountInContractCurrency,
            startDate = now,
            endDate = now.plusDays(30)
        )

        whenever(jpaContractSeveranceHistoryRepository.findByContractIdAndStartDateGreaterThanEqualAndEndDateLessThanEqualAndCompanyPayableId(
            contractId = contractId,
            startDate = now,
            endDate = now.plusDays(30),
            companyPayableId = companyPayableId
        )).thenReturn(null)

        whenever(jpaContractSeveranceHistoryRepository.save(any<JpaContractSeveranceHistory>())).thenReturn(savedJpaEntity)
        whenever(contractSeveranceHistoryEntityMapper.toDomain(savedJpaEntity)).thenReturn(expectedDomainEntity)

        // when
        val result = contractSeveranceHistoryRepositoryImpl.upsertContractSeveranceHistoryForContractIdAndTimePeriodAndCompanyPayableId(createInput)

        // then
        assertEquals(expectedDomainEntity, result)
        verify(jpaContractSeveranceHistoryRepository).findByContractIdAndStartDateGreaterThanEqualAndEndDateLessThanEqualAndCompanyPayableId(
            contractId = contractId,
            startDate = now,
            endDate = now.plusDays(30),
            companyPayableId = companyPayableId
        )
        verify(jpaContractSeveranceHistoryRepository).save(any<JpaContractSeveranceHistory>())
        verify(contractSeveranceHistoryEntityMapper).toDomain(savedJpaEntity)
    }

    @Test
    fun `should update existing record when upserting and existing record found`() {
        // given
        val updatedAmount = Amount.newBuilder()
            .currency(CurrencyCode.CAD)
            .amount(2000.00)
            .build()

        val updatedAmountInContractCurrency = Amount.newBuilder()
            .currency(CurrencyCode.USD)
            .amount(1500.00)
            .build()

        val createInput = ContractSeveranceHistoryCreateInput(
            companyPayableId = companyPayableId,
            contractId = contractId,
            amount = updatedAmount,
            amountInContractCurrency = updatedAmountInContractCurrency,
            startDate = now,
            endDate = now.plusDays(30)
        )

        val existingJpaEntity = JpaContractSeveranceHistory(
            id = 1L,
            companyPayableId = companyPayableId,
            contractId = contractId,
            amount = amount,
            amountInContractCurrency = amountInContractCurrency,
            startDate = now,
            endDate = now.plusDays(30),
            createdOn = now.toLocalDateTime()
        )

        val updatedJpaEntity = JpaContractSeveranceHistory(
            id = 1L,
            companyPayableId = companyPayableId,
            contractId = contractId,
            amount = updatedAmount,
            amountInContractCurrency = updatedAmountInContractCurrency,
            startDate = now,
            endDate = now.plusDays(30),
            createdOn = now.toLocalDateTime()
        )

        val expectedDomainEntity = ContractSeveranceHistoryEntity(
            id = 1L,
            companyPayableId = companyPayableId,
            contractId = contractId,
            amount = updatedAmount,
            amountInContractCurrency = updatedAmountInContractCurrency,
            startDate = now,
            endDate = now.plusDays(30)
        )

        whenever(jpaContractSeveranceHistoryRepository.findByContractIdAndStartDateGreaterThanEqualAndEndDateLessThanEqualAndCompanyPayableId(
            contractId = contractId,
            startDate = now,
            endDate = now.plusDays(30),
            companyPayableId = companyPayableId
        )).thenReturn(existingJpaEntity)

        whenever(jpaContractSeveranceHistoryRepository.save(any<JpaContractSeveranceHistory>())).thenReturn(updatedJpaEntity)
        whenever(contractSeveranceHistoryEntityMapper.toDomain(updatedJpaEntity)).thenReturn(expectedDomainEntity)

        // when
        val result = contractSeveranceHistoryRepositoryImpl.upsertContractSeveranceHistoryForContractIdAndTimePeriodAndCompanyPayableId(createInput)

        // then
        assertEquals(expectedDomainEntity, result)
        assertEquals(updatedAmount, result.amount)
        assertEquals(updatedAmountInContractCurrency, result.amountInContractCurrency)
        verify(jpaContractSeveranceHistoryRepository).findByContractIdAndStartDateGreaterThanEqualAndEndDateLessThanEqualAndCompanyPayableId(
            contractId = contractId,
            startDate = now,
            endDate = now.plusDays(30),
            companyPayableId = companyPayableId
        )
        verify(jpaContractSeveranceHistoryRepository).save(any<JpaContractSeveranceHistory>())
        verify(contractSeveranceHistoryEntityMapper).toDomain(updatedJpaEntity)
    }
}