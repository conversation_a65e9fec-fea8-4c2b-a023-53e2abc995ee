package com.multiplier.payable.severance.history.payable.creditnote

import com.multiplier.core.payable.creditnote.database.CreditNoteDto
import com.multiplier.core.payable.creditnote.database.CreditNoteQueryDto
import com.multiplier.core.payable.creditnote.database.CreditNoteQueryService
import com.multiplier.payable.severance.history.payable.CompanyPayableSeveranceCreateService
import com.multiplier.payable.severance.history.payable.CompanyPayableSeveranceHistoryInput
import com.multiplier.payable.severance.history.payable.ContractPayableSeveranceLineItem
import com.multiplier.payable.severance.history.port.ContractSeveranceHistory
import com.multiplier.payable.types.Amount
import com.multiplier.payable.types.CreditNoteStatus
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.*
import java.time.LocalDate
import java.time.ZoneId
import kotlin.test.assertEquals

@ExtendWith(MockitoExtension::class)
class CreditNoteSeveranceHistoryServiceTest {

    @Mock
    private lateinit var creditNoteQueryService: CreditNoteQueryService

    @Mock
    private lateinit var creditNoteSeveranceLineItemFinder: CreditNoteSeveranceLineItemFinder

    @Mock
    private lateinit var companyPayableSeveranceCreateService: CompanyPayableSeveranceCreateService

    private lateinit var service: CreditNoteSeveranceHistoryService

    private val currentDate = LocalDate.now()
    private val startDate = currentDate.atStartOfDay(ZoneId.of("UTC"))
    private val endDate = currentDate.plusMonths(1).atStartOfDay(ZoneId.of("UTC"))
    private val batchSize = 2
    private val creditNoteId1 = 1001L
    private val creditNoteId2 = 1002L
    private val creditNoteId3 = 1003L
    private val companyPayableId = 5000L
    private val contractId1 = 2001L
    private val contractId2 = 2002L

    @BeforeEach
    fun setUp() {
        service = CreditNoteSeveranceHistoryService(
            creditNoteQueryService,
            creditNoteSeveranceLineItemFinder,
            companyPayableSeveranceCreateService
        )
    }

    @Test
    fun `should process credit notes in batches and create severance history`() {
        // given
        val input = CompanyPayableSeveranceHistoryInput(
            startDate = startDate,
            endDate = endDate,
            batchSize = batchSize
        )

        val creditNoteIds = listOf(creditNoteId1, creditNoteId2, creditNoteId3)
        whenever(creditNoteQueryService.getCreditNotesIds(any())).thenReturn(creditNoteIds)

        val creditNote1 = mock<CreditNoteDto>()
        val creditNote2 = mock<CreditNoteDto>()
        val creditNote3 = mock<CreditNoteDto>()

        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(eq(listOf(creditNoteId1, creditNoteId2))))
            .thenReturn(mapOf(creditNoteId1 to creditNote1, creditNoteId2 to creditNote2))
        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(eq(listOf(creditNoteId3))))
            .thenReturn(mapOf(creditNoteId3 to creditNote3))

        val lineItems1 = createLineItems(contractId1)
        val lineItems2 = createLineItems(contractId2)
        val lineItems3 = createLineItems(contractId1)

        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote1)).thenReturn(lineItems1)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote2)).thenReturn(lineItems2)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote3)).thenReturn(lineItems3)

        // when
        service.getContractSeveranceHistoryRecords(input)

        // then
        verifyQueryParameters(input)
        verifyBatchProcessing(creditNoteIds)

        // Verify the batched line items are processed together
        val expectedBatch1 = lineItems1 + lineItems2  // First batch combines items from first two credit notes
        val expectedBatch2 = lineItems3               // Second batch contains items from third credit note

        val createCaptor = argumentCaptor<List<ContractPayableSeveranceLineItem>>()
        verify(companyPayableSeveranceCreateService, times(2)).create(createCaptor.capture())

        val capturedBatches = createCaptor.allValues
        assertEquals(expectedBatch1, capturedBatches[0])
        assertEquals(expectedBatch2, capturedBatches[1])
    }

    @Test
    fun `should handle exceptions from line item finder and skip entire batch`() {
        // given
        val input = CompanyPayableSeveranceHistoryInput(
            startDate = startDate,
            endDate = endDate,
            batchSize = batchSize
        )

        // Mock 3 credit notes to create 2 batches
        val creditNoteIds = listOf(creditNoteId1, creditNoteId2, creditNoteId3)
        whenever(creditNoteQueryService.getCreditNotesIds(any())).thenReturn(creditNoteIds)

        // Mock first batch (will fail due to exception)
        val creditNote1 = mock<CreditNoteDto>()
        val creditNote2 = mock<CreditNoteDto>()
        val firstBatchMap = mapOf(creditNoteId1 to creditNote1, creditNoteId2 to creditNote2)
        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(eq(listOf(creditNoteId1, creditNoteId2)))).thenReturn(firstBatchMap)

        // Mock second batch (will succeed)
        val creditNote3 = mock<CreditNoteDto>()
        val secondBatchMap = mapOf(creditNoteId3 to creditNote3)
        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(eq(listOf(creditNoteId3)))).thenReturn(secondBatchMap)

        // Mock line items - first batch throws exception, second batch succeeds
        val successfulLineItems = createLineItems(contractId1)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote1)).thenReturn(successfulLineItems)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote2))
            .thenThrow(CreditNoteSeveranceLineItemMappingException("Test exception"))
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote3)).thenReturn(successfulLineItems)

        // when
        service.getContractSeveranceHistoryRecords(input)

        // then
        // Only the second batch should be processed (first batch is skipped due to exception)
        val createCaptor = argumentCaptor<List<ContractPayableSeveranceLineItem>>()
        verify(companyPayableSeveranceCreateService, times(2)).create(createCaptor.capture())

        val capturedLineItems = createCaptor.firstValue
        assertEquals(1, capturedLineItems.size)
        assertEquals(successfulLineItems.first().contractId, capturedLineItems.first().contractId)

        // Verify that both credit notes in the first batch were attempted to be processed
        verify(creditNoteSeveranceLineItemFinder).getSeveranceLineItems(creditNote1)
        verify(creditNoteSeveranceLineItemFinder).getSeveranceLineItems(creditNote2)
        verify(creditNoteSeveranceLineItemFinder).getSeveranceLineItems(creditNote3)
    }

    @Test
    fun `should handle empty credit note list`() {
        // given
        val input = CompanyPayableSeveranceHistoryInput(
            startDate = startDate,
            endDate = endDate,
            batchSize = batchSize
        )

        whenever(creditNoteQueryService.getCreditNotesIds(any())).thenReturn(emptyList())

        // when
        val result = service.getContractSeveranceHistoryRecords(input)

        // then
        verify(creditNoteQueryService, never()).findCreditNotesByIdsWithErrorHandling(any())
        verify(creditNoteSeveranceLineItemFinder, never()).getSeveranceLineItems(any())
        verify(companyPayableSeveranceCreateService, never()).create(any())
        assertEquals(emptyList(), result)
    }

    @Test
    fun `should handle batch-level exception and skip entire batch`() {
        // given
        val input = CompanyPayableSeveranceHistoryInput(
            startDate = startDate,
            endDate = endDate,
            batchSize = batchSize
        )
        val creditNoteIds = listOf(creditNoteId1, creditNoteId2, creditNoteId3)
        whenever(creditNoteQueryService.getCreditNotesIds(any())).thenReturn(creditNoteIds)

        // Mock first batch to throw exception at findCreditNotesByIdsWithErrorHandling level
        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(eq(listOf(creditNoteId1, creditNoteId2))))
            .thenThrow(RuntimeException("Database connection error"))

        // Mock second batch to succeed
        val creditNote3 = mock<CreditNoteDto>()
        val secondBatchMap = mapOf(creditNoteId3 to creditNote3)
        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(eq(listOf(creditNoteId3)))).thenReturn(secondBatchMap)

        val lineItem = createLineItems(contractId1)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote3)).thenReturn(lineItem)

        val contractSeveranceHistory = mock<ContractSeveranceHistory>()
        whenever(companyPayableSeveranceCreateService.create(any())).thenReturn(listOf(contractSeveranceHistory))

        // when
        val result = service.getContractSeveranceHistoryRecords(input)

        // then
        // First batch should be skipped due to exception, only second batch processed
        verify(companyPayableSeveranceCreateService, times(1)).create(any())
        assertEquals(1, result.size)
        assertEquals(contractSeveranceHistory, result[0])
    }

    @Test
    fun `should handle empty credit note map from findCreditNotesByIdsWithErrorHandling`() {
        // given
        val input = CompanyPayableSeveranceHistoryInput(
            startDate = startDate,
            endDate = endDate,
            batchSize = batchSize
        )
        val creditNoteIds = listOf(creditNoteId1, creditNoteId2)
        whenever(creditNoteQueryService.getCreditNotesIds(any())).thenReturn(creditNoteIds)

        // Mock empty map return (all credit notes failed to load)
        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(any())).thenReturn(emptyMap())

        // when
        val result = service.getContractSeveranceHistoryRecords(input)

        // then
        verify(creditNoteSeveranceLineItemFinder, never()).getSeveranceLineItems(any())
        verify(companyPayableSeveranceCreateService).create(emptyList())
        assertEquals(emptyList(), result)
    }

    @Test
    fun `should handle mixed success and failure within batch`() {
        // given
        val input = CompanyPayableSeveranceHistoryInput(
            startDate = startDate,
            endDate = endDate,
            batchSize = batchSize
        )
        val creditNoteIds = listOf(creditNoteId1, creditNoteId2)
        whenever(creditNoteQueryService.getCreditNotesIds(any())).thenReturn(creditNoteIds)

        // Mock both credit notes loaded successfully
        val creditNote1 = mock<CreditNoteDto>()
        val creditNote2 = mock<CreditNoteDto>()
        val batchMap = mapOf(
            creditNoteId1 to creditNote1,
            creditNoteId2 to creditNote2
        )
        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(any())).thenReturn(batchMap)

        // Mock first credit note succeeds, second fails with CreditNoteSeveranceLineItemMappingException
        val lineItem = createLineItems(contractId1)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote1)).thenReturn(lineItem)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote2))
            .thenThrow(CreditNoteSeveranceLineItemMappingException("Mapping failed"))

        val contractSeveranceHistory = mock<ContractSeveranceHistory>()
        whenever(companyPayableSeveranceCreateService.create(any())).thenReturn(listOf(contractSeveranceHistory))

        // when
        val result = service.getContractSeveranceHistoryRecords(input)

        // then
        // Only successful line items should be passed to create service
        val createCaptor = argumentCaptor<List<ContractPayableSeveranceLineItem>>()
        verify(companyPayableSeveranceCreateService).create(createCaptor.capture())

        val capturedLineItems = createCaptor.firstValue
        assertEquals(1, capturedLineItems.size)
        assertEquals(lineItem[0].contractId, capturedLineItems[0].contractId)

        assertEquals(1, result.size)
        assertEquals(contractSeveranceHistory, result[0])
    }

    @Test
    fun `should handle create service failure gracefully`() {
        // given
        val input = CompanyPayableSeveranceHistoryInput(
            startDate = startDate,
            endDate = endDate,
            batchSize = batchSize
        )
        val creditNoteIds = listOf(creditNoteId1)
        whenever(creditNoteQueryService.getCreditNotesIds(any())).thenReturn(creditNoteIds)

        val creditNote1 = mock<CreditNoteDto>()
        val batchMap = mapOf(creditNoteId1 to creditNote1)
        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(any())).thenReturn(batchMap)

        val lineItem = createLineItems(contractId1)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote1)).thenReturn(lineItem)

        // Mock create service to throw exception
        whenever(companyPayableSeveranceCreateService.create(any())).thenThrow(RuntimeException("Create service failed"))

        // when
        val result = service.getContractSeveranceHistoryRecords(input)

        // then
        // Exception should be caught and empty list returned for this batch
        assertEquals(emptyList(), result)
        verify(companyPayableSeveranceCreateService).create(any())
    }

    @Test
    fun `should return flattened results from multiple batches`() {
        // given
        val input = CompanyPayableSeveranceHistoryInput(
            startDate = startDate,
            endDate = endDate,
            batchSize = batchSize
        )
        val creditNoteIds = listOf(creditNoteId1, creditNoteId2, creditNoteId3)
        whenever(creditNoteQueryService.getCreditNotesIds(any())).thenReturn(creditNoteIds)

        // Mock first batch
        val creditNote1 = mock<CreditNoteDto>()
        val creditNote2 = mock<CreditNoteDto>()
        val firstBatchMap = mapOf(creditNoteId1 to creditNote1, creditNoteId2 to creditNote2)
        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(eq(listOf(creditNoteId1, creditNoteId2)))).thenReturn(firstBatchMap)

        // Mock second batch
        val creditNote3 = mock<CreditNoteDto>()
        val secondBatchMap = mapOf(creditNoteId3 to creditNote3)
        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(eq(listOf(creditNoteId3)))).thenReturn(secondBatchMap)

        // Mock line items
        val lineItem1 = createLineItems(contractId1)
        val lineItem2 = createLineItems(contractId2)
        val lineItem3 = createLineItems(contractId1)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote1)).thenReturn(lineItem1)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote2)).thenReturn(lineItem2)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote3)).thenReturn(lineItem3)

        // Mock create service to return different results for each batch
        val contractHistory1 = mock<ContractSeveranceHistory>()
        val contractHistory2 = mock<ContractSeveranceHistory>()
        val contractHistory3 = mock<ContractSeveranceHistory>()

        whenever(companyPayableSeveranceCreateService.create(eq(lineItem1 + lineItem2))).thenReturn(listOf(contractHistory1, contractHistory2))
        whenever(companyPayableSeveranceCreateService.create(eq(lineItem3))).thenReturn(listOf(contractHistory3))

        // when
        val result = service.getContractSeveranceHistoryRecords(input)

        // then
        assertEquals(3, result.size)
        assertEquals(contractHistory1, result[0])
        assertEquals(contractHistory2, result[1])
        assertEquals(contractHistory3, result[2])
    }

    @Test
    fun `should handle empty line items from all credit notes in batch`() {
        // given
        val input = CompanyPayableSeveranceHistoryInput(
            startDate = startDate,
            endDate = endDate,
            batchSize = batchSize
        )
        val creditNoteIds = listOf(creditNoteId1, creditNoteId2)
        whenever(creditNoteQueryService.getCreditNotesIds(any())).thenReturn(creditNoteIds)

        val creditNote1 = mock<CreditNoteDto>()
        val creditNote2 = mock<CreditNoteDto>()
        val batchMap = mapOf(creditNoteId1 to creditNote1, creditNoteId2 to creditNote2)
        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(any())).thenReturn(batchMap)

        // Mock both credit notes to return empty line items
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote1)).thenReturn(emptyList())
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote2)).thenReturn(emptyList())

        // when
        val result = service.getContractSeveranceHistoryRecords(input)

        // then
        // Create service should be called with empty list
        verify(companyPayableSeveranceCreateService).create(emptyList())
        assertEquals(emptyList(), result)
    }

    @Test
    fun `should handle non-CreditNoteSeveranceLineItemMappingException from line item finder`() {
        // given
        val input = CompanyPayableSeveranceHistoryInput(
            startDate = startDate,
            endDate = endDate,
            batchSize = batchSize
        )
        val creditNoteIds = listOf(creditNoteId1, creditNoteId2)
        whenever(creditNoteQueryService.getCreditNotesIds(any())).thenReturn(creditNoteIds)

        val creditNote1 = mock<CreditNoteDto>()
        val creditNote2 = mock<CreditNoteDto>()
        val batchMap = mapOf(creditNoteId1 to creditNote1, creditNoteId2 to creditNote2)
        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(any())).thenReturn(batchMap)

        // Mock first credit note to succeed, second to throw non-CreditNoteSeveranceLineItemMappingException
        val lineItem = createLineItems(contractId1)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote1)).thenReturn(lineItem)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote2))
            .thenThrow(RuntimeException("Unexpected error"))

        // when
        val result = service.getContractSeveranceHistoryRecords(input)

        // then
        // The RuntimeException should bubble up and cause the entire batch to be skipped
        assertEquals(emptyList(), result)
        verify(companyPayableSeveranceCreateService, never()).create(any())
    }

    @Test
    fun `should process single batch when credit note count equals batch size`() {
        // given
        val input = CompanyPayableSeveranceHistoryInput(
            startDate = startDate,
            endDate = endDate,
            batchSize = batchSize
        )
        val creditNoteIds = listOf(creditNoteId1, creditNoteId2) // Exactly batch size
        whenever(creditNoteQueryService.getCreditNotesIds(any())).thenReturn(creditNoteIds)

        val creditNote1 = mock<CreditNoteDto>()
        val creditNote2 = mock<CreditNoteDto>()
        val batchMap = mapOf(creditNoteId1 to creditNote1, creditNoteId2 to creditNote2)
        whenever(creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(any())).thenReturn(batchMap)

        val lineItem1 = createLineItems(contractId1)
        val lineItem2 = createLineItems(contractId2)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote1)).thenReturn(lineItem1)
        whenever(creditNoteSeveranceLineItemFinder.getSeveranceLineItems(creditNote2)).thenReturn(lineItem2)

        val contractHistory = mock<ContractSeveranceHistory>()
        whenever(companyPayableSeveranceCreateService.create(any())).thenReturn(listOf(contractHistory))

        // when
        val result = service.getContractSeveranceHistoryRecords(input)

        // then
        verify(companyPayableSeveranceCreateService, times(1)).create(any()) // Only one batch
        assertEquals(1, result.size)
    }

    private fun createLineItems(contractId: Long): List<ContractPayableSeveranceLineItem> {
        return listOf(
            ContractPayableSeveranceLineItem(
                contractId = contractId,
                companyPayableId = companyPayableId,
                startTime = startDate,
                endTime = endDate,
                amount = Amount.newBuilder()
                    .amount(1000.00)
                    .currency(CurrencyCode.USD)
                    .build(),
                amountInContractCurrency = Amount.newBuilder()
                    .amount(800.00)
                    .currency(CurrencyCode.CAD)
                    .build()
            )
        )
    }

    @Test
    fun `should create correct credit note query without company IDs`() {
        // given
        val input = CompanyPayableSeveranceHistoryInput(startDate, endDate, batchSize)
        whenever(creditNoteQueryService.getCreditNotesIds(any())).thenReturn(emptyList())

        // when
        service.getContractSeveranceHistoryRecords(input)

        // then
        val queryCaptor = argumentCaptor<CreditNoteQueryDto>()
        verify(creditNoteQueryService).getCreditNotesIds(queryCaptor.capture())

        val capturedQuery = queryCaptor.firstValue
        assertEquals(listOf(CreditNoteStatus.FULLY_APPLIED, CreditNoteStatus.DRAFT), capturedQuery.statuses)
        assertEquals(null, capturedQuery.reason)
        assertEquals(input.startDate.toLocalDateTime(), capturedQuery.createdDateRange.startDate)
        assertEquals(input.endDate.toLocalDateTime(), capturedQuery.createdDateRange.endDate)
        assertEquals(null, capturedQuery.companyIds) // Should be null when empty
    }

    @Test
    fun `should create correct credit note query with company IDs`() {
        // given
        val companyIds = listOf(1001L, 1002L)
        val input = CompanyPayableSeveranceHistoryInput(startDate, endDate, batchSize, companyIds)
        whenever(creditNoteQueryService.getCreditNotesIds(any())).thenReturn(emptyList())

        // when
        service.getContractSeveranceHistoryRecords(input)

        // then
        val queryCaptor = argumentCaptor<CreditNoteQueryDto>()
        verify(creditNoteQueryService).getCreditNotesIds(queryCaptor.capture())

        val capturedQuery = queryCaptor.firstValue
        assertEquals(listOf(CreditNoteStatus.FULLY_APPLIED, CreditNoteStatus.DRAFT), capturedQuery.statuses)
        assertEquals(null, capturedQuery.reason)
        assertEquals(input.startDate.toLocalDateTime(), capturedQuery.createdDateRange.startDate)
        assertEquals(input.endDate.toLocalDateTime(), capturedQuery.createdDateRange.endDate)
        assertEquals(companyIds, capturedQuery.companyIds)
    }

    private fun verifyQueryParameters(input: CompanyPayableSeveranceHistoryInput) {
        val queryCaptor = argumentCaptor<CreditNoteQueryDto>()
        verify(creditNoteQueryService).getCreditNotesIds(queryCaptor.capture())

        val capturedQuery = queryCaptor.firstValue
        assertEquals(listOf(CreditNoteStatus.FULLY_APPLIED, CreditNoteStatus.DRAFT) , capturedQuery.statuses)
        assertEquals(null, capturedQuery.reason)
        assertEquals(input.startDate.toLocalDateTime(), capturedQuery.createdDateRange.startDate)
        assertEquals(input.endDate.toLocalDateTime(), capturedQuery.createdDateRange.endDate)
    }

    private fun verifyBatchProcessing(creditNoteIds: List<Long>) {
        val firstBatch = creditNoteIds.take(2)
        val secondBatch = creditNoteIds.drop(2)

        verify(creditNoteQueryService).findCreditNotesByIdsWithErrorHandling(eq(firstBatch))
        if (secondBatch.isNotEmpty()) {
            verify(creditNoteQueryService).findCreditNotesByIdsWithErrorHandling(eq(secondBatch))
        }
    }

}