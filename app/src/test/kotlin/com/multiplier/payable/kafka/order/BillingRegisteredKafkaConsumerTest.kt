package com.multiplier.payable.kafka.order

import com.multiplier.billing.kafka.schema.BillingRegistered
import com.multiplier.core.payable.adapters.order.CompanyOrderAdapter
import com.multiplier.core.payable.adapters.order.CompanyOrderWrapper
import com.multiplier.core.payable.adapters.pricing.ReferenceChargePolicy
import com.multiplier.core.payable.adapters.product.CompanyProductWrapper
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.orchestrator.TransactionTriggeringType
import com.multiplier.payable.engine.transaction.GenerateFinancialTransactionCommand
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import java.time.LocalDateTime

@ExtendWith(MockitoExtension::class)
class BillingRegisteredKafkaConsumerTest {

    @Mock
    private lateinit var blockingGenerateTransactionCommand: GenerateFinancialTransactionCommand

    @Mock
    private lateinit var companyOrderAdapter: CompanyOrderAdapter

    @InjectMocks
    private lateinit var consumer: BillingRegisteredKafkaConsumer

    private lateinit var billingRegisteredEvent: BillingRegistered
    private lateinit var companyOrderWrapper: CompanyOrderWrapper
    private lateinit var advanceProduct: CompanyProductWrapper
    private lateinit var startDate: LocalDateTime
    private lateinit var endDate: LocalDateTime

    @BeforeEach
    fun setup() {
        startDate = LocalDateTime.now()
        endDate = startDate.plusMonths(1)

        billingRegisteredEvent = BillingRegistered.newBuilder()
            .setCompanyId(123L)
            .setOrderId(456L)
            .build()

        advanceProduct = CompanyProductWrapper(
            startDate = startDate,
            endDate = endDate,
            lineCode = "ORDER_FORM_ADVANCE",
            dimensions = mapOf("key1" to "value1"),
            chargePolicy = ReferenceChargePolicy(emptyList())
        )

        companyOrderWrapper = CompanyOrderWrapper(
            companyId = 456L,
            orderId = 789L,
            orderedProducts = listOf(advanceProduct)
        )
    }

    @Test
    fun `should generate transaction command when order contains ORDER_FORM_ADVANCE`() {
        // Given
        `when`(companyOrderAdapter.getCompanyOrder(456L)).thenReturn(companyOrderWrapper)

        val expectedCommands = listOf(
            InvoiceCommand(
                transactionId = "test-id",
                companyId = 123L,
                dateRange = DateRange(startDate, endDate),
                transactionDate = LocalDateTime.now(),
                cycle = InvoiceCycle.YEARLY,
                transactionType = TransactionType.ORDER_FORM_ADVANCE
            )
        )

        `when`(
            blockingGenerateTransactionCommand.generate(
                eq(TransactionType.ORDER_FORM_ADVANCE),
                eq(listOf(123L)),
                any(),
                any(),
                eq(InvoiceCycle.YEARLY),
                eq(emptyMap()),
                eq(false),
                eq(null),
                eq(emptyMap()),
                eq(null),
                eq(null),
                eq(null),
                eq(TransactionTriggeringType.MANUAL),
                eq(emptyMap()),
            )
        ).thenReturn(expectedCommands)

        // When
        consumer.consume(billingRegisteredEvent)

        // Then
        verify(companyOrderAdapter).getCompanyOrder(456L)
        verify(blockingGenerateTransactionCommand).generate(
            eq(TransactionType.ORDER_FORM_ADVANCE),
            eq(listOf(123L)),
            any(),
            any(),
            eq(InvoiceCycle.YEARLY),
            eq(emptyMap()),
            eq(false),
            eq(null),
            eq(emptyMap()),
            eq(null),
            eq(null),
            eq(null),
            eq(TransactionTriggeringType.MANUAL),
            eq(emptyMap()),
        )
    }

    @Test
    fun `should not generate transaction command when order does not contain ORDER_FORM_ADVANCE`() {
        // Given
        val regularProduct = CompanyProductWrapper(
            startDate = startDate,
            endDate = endDate,
            lineCode = "REGULAR_PRODUCT",
            dimensions = mapOf("key2" to "value2"),
            chargePolicy = ReferenceChargePolicy(emptyList())
        )

        val orderWithoutAdvance = CompanyOrderWrapper(
            companyId = 456L,
            orderId = 789L,
            orderedProducts = listOf(regularProduct)
        )

        `when`(companyOrderAdapter.getCompanyOrder(456L)).thenReturn(orderWithoutAdvance)

        // When
        consumer.consume(billingRegisteredEvent)

        // Then
        verify(companyOrderAdapter).getCompanyOrder(456L)
        verifyNoInteractions(blockingGenerateTransactionCommand)
    }

    @Test
    fun `should propagate exception when error occurs`() {
        // Given
        val exception = RuntimeException("Test exception")
        `when`(companyOrderAdapter.getCompanyOrder(456L)).thenThrow(exception)

        // When/Then
        try {
            consumer.consume(billingRegisteredEvent)
        } catch (e: Exception) {
            // Exception is expected to be propagated
        }

        verify(companyOrderAdapter).getCompanyOrder(456L)
        verifyNoInteractions(blockingGenerateTransactionCommand)
    }

    @Test
    fun `should handle multiple products with different start dates but same end date`() {
        // Given
        val earlierStartDate = startDate.minusDays(5)
        val commonEndDate = endDate

        val product1 = CompanyProductWrapper(
            startDate = earlierStartDate,
            endDate = commonEndDate,
            lineCode = "ORDER_FORM_ADVANCE",
            dimensions = mapOf("key1" to "value1"),
            chargePolicy = ReferenceChargePolicy(emptyList())
        )

        val product2 = CompanyProductWrapper(
            startDate = startDate,
            endDate = commonEndDate,
            lineCode = "ORDER_FORM_ADVANCE",
            dimensions = mapOf("key2" to "value2"),
            chargePolicy = ReferenceChargePolicy(emptyList())
        )

        val orderWithMultipleProducts = CompanyOrderWrapper(
            companyId = 456L,
            orderId = 789L,
            orderedProducts = listOf(product1, product2)
        )

        `when`(companyOrderAdapter.getCompanyOrder(456L)).thenReturn(orderWithMultipleProducts)

        // When
        consumer.consume(billingRegisteredEvent)

        // Then
        verify(companyOrderAdapter).getCompanyOrder(456L)
        verify(blockingGenerateTransactionCommand).generate(
            eq(TransactionType.ORDER_FORM_ADVANCE),
            eq(listOf(123L)),
            eq(DateRange(earlierStartDate, commonEndDate)),
            any(),
            eq(InvoiceCycle.YEARLY),
            eq(emptyMap()),
            eq(false),
            eq(null),
            eq(emptyMap()),
            eq(null),
            eq(null),
            eq(null),
            eq(TransactionTriggeringType.MANUAL),
            eq(emptyMap()),
        )
    }

    @Test
    fun `should handle multiple products with same start date but different end dates`() {
        // Given
        val commonStartDate = startDate
        val laterEndDate = endDate.plusDays(5)

        val product1 = CompanyProductWrapper(
            startDate = commonStartDate,
            endDate = endDate,
            lineCode = "ORDER_FORM_ADVANCE",
            dimensions = mapOf("key1" to "value1"),
            chargePolicy = ReferenceChargePolicy(emptyList())
        )

        val product2 = CompanyProductWrapper(
            startDate = commonStartDate,
            endDate = laterEndDate,
            lineCode = "ORDER_FORM_ADVANCE",
            dimensions = mapOf("key2" to "value2"),
            chargePolicy = ReferenceChargePolicy(emptyList())
        )

        val orderWithMultipleProducts = CompanyOrderWrapper(
            companyId = 456L,
            orderId = 789L,
            orderedProducts = listOf(product1, product2)
        )

        `when`(companyOrderAdapter.getCompanyOrder(456L)).thenReturn(orderWithMultipleProducts)

        // When
        consumer.consume(billingRegisteredEvent)

        // Then
        verify(companyOrderAdapter).getCompanyOrder(456L)
        verify(blockingGenerateTransactionCommand).generate(
            eq(TransactionType.ORDER_FORM_ADVANCE),
            eq(listOf(123L)),
            eq(DateRange(commonStartDate, laterEndDate)),
            any(),
            eq(InvoiceCycle.YEARLY),
            eq(emptyMap()),
            eq(false),
            eq(null),
            eq(emptyMap()),
            eq(null),
            eq(null),
            eq(null),
            eq(TransactionTriggeringType.MANUAL),
            eq(emptyMap()),
        )
    }

    @Test
    fun `should handle multiple products with different start and end dates`() {
        // Given
        val earlierStartDate = startDate.minusDays(5)
        val laterEndDate = endDate.plusDays(5)

        val product1 = CompanyProductWrapper(
            startDate = earlierStartDate,
            endDate = endDate,
            lineCode = "ORDER_FORM_ADVANCE",
            dimensions = mapOf("key1" to "value1"),
            chargePolicy = ReferenceChargePolicy(emptyList())
        )

        val product2 = CompanyProductWrapper(
            startDate = startDate,
            endDate = laterEndDate,
            lineCode = "ORDER_FORM_ADVANCE",
            dimensions = mapOf("key2" to "value2"),
            chargePolicy = ReferenceChargePolicy(emptyList())
        )

        val orderWithMultipleProducts = CompanyOrderWrapper(
            companyId = 456L,
            orderId = 789L,
            orderedProducts = listOf(product1, product2)
        )

        `when`(companyOrderAdapter.getCompanyOrder(456L)).thenReturn(orderWithMultipleProducts)

        // When
        consumer.consume(billingRegisteredEvent)

        // Then
        verify(companyOrderAdapter).getCompanyOrder(456L)
        verify(blockingGenerateTransactionCommand).generate(
            eq(TransactionType.ORDER_FORM_ADVANCE),
            eq(listOf(123L)),
            eq(DateRange(earlierStartDate, laterEndDate)),
            any(),
            eq(InvoiceCycle.YEARLY),
            eq(emptyMap()),
            eq(false),
            eq(null),
            eq(emptyMap()),
            eq(null),
            eq(null),
            eq(null),
            eq(TransactionTriggeringType.MANUAL),
            eq(emptyMap()),
        )
    }

    @Test
    fun `should handle multiple products with identical start and end dates`() {
        // Given
        val product1 = CompanyProductWrapper(
            startDate = startDate,
            endDate = endDate,
            lineCode = "ORDER_FORM_ADVANCE",
            dimensions = mapOf("key1" to "value1"),
            chargePolicy = ReferenceChargePolicy(emptyList())
        )

        val product2 = CompanyProductWrapper(
            startDate = startDate,
            endDate = endDate,
            lineCode = "ORDER_FORM_ADVANCE",
            dimensions = mapOf("key2" to "value2"),
            chargePolicy = ReferenceChargePolicy(emptyList())
        )

        val orderWithMultipleProducts = CompanyOrderWrapper(
            companyId = 456L,
            orderId = 789L,
            orderedProducts = listOf(product1, product2)
        )

        `when`(companyOrderAdapter.getCompanyOrder(456L)).thenReturn(orderWithMultipleProducts)

        // When
        consumer.consume(billingRegisteredEvent)

        // Then
        verify(companyOrderAdapter).getCompanyOrder(456L)
        verify(blockingGenerateTransactionCommand).generate(
            eq(TransactionType.ORDER_FORM_ADVANCE),
            eq(listOf(123L)),
            eq(DateRange(startDate, endDate)),
            any(),
            eq(InvoiceCycle.YEARLY),
            eq(emptyMap()),
            eq(false),
            eq(null),
            eq(emptyMap()),
            eq(null),
            eq(null),
            eq(null),
            eq(TransactionTriggeringType.MANUAL),
            eq(emptyMap()),
        )
    }
}
