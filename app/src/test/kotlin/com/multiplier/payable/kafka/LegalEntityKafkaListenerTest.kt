package com.multiplier.payable.kafka

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.doThrow
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.springframework.kafka.support.Acknowledgment

@ExtendWith(MockitoExtension::class)
class LegalEntityKafkaListenerTest {

    @Mock
    private lateinit var processor: LegalEntityKafkaMessageProcessor

    @Mock
    private lateinit var acknowledgment: Acknowledgment

    @InjectMocks
    private lateinit var listener: LegalEntityKafkaListener

    @Test
    fun shouldAck_whenSuccessfullyProcessMessages() {
        listener.consumeLegalEntityEvent(emptyList(), acknowledgment)
        verify(acknowledgment, times(1)).acknowledge()
        verify(processor, times(1)).handle(anyOrNull())
    }

    @Test
    fun shouldAck_whenIssueHappened() {
        Mockito.`when`<Any?>(processor.handle(anyOrNull())).doThrow(RuntimeException("test exception"))
        listener.consumeLegalEntityEvent(emptyList(), acknowledgment)
        verify(acknowledgment).acknowledge()
        verify(processor, times(1)).handle(anyOrNull())
    }
}