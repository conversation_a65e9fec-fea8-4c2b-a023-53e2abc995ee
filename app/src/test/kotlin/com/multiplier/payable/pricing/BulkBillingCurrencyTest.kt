package com.multiplier.payable.pricing

import com.multiplier.payable.engine.domain.entities.TransactionType
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import com.multiplier.payable.engine.currency.CurrencyCode

class BulkBillingCurrencyTest {

    @Test
    fun `BulkEntityBillingCurrencyRequest should require non-empty entity requests`() {
        // When/Then
        assertThrows<IllegalArgumentException> {
            BulkEntityBillingCurrencyRequest(entityRequests = emptyList())
        }
    }

    @Test
    fun `BulkEntityBillingCurrencyRequest should accept valid entity requests`() {
        // Given
        val entityRequests = listOf(
            EntityBillingCurrencyRequest(
                companyId = 123L,
                entityId = 456L,
                transactionType = null
            ),
            EntityBillingCurrencyRequest(
                companyId = 789L,
                entityId = 101L,
                transactionType = TransactionType.GP_FUNDING_INVOICE
            )
        )

        // When
        val request = BulkEntityBillingCurrencyRequest(entityRequests = entityRequests)

        // Then
        assertEquals(2, request.entityRequests.size)
        assertEquals(123L, request.entityRequests[0].companyId)
        assertEquals(456L, request.entityRequests[0].entityId)
        assertEquals(null, request.entityRequests[0].transactionType)
        assertEquals(789L, request.entityRequests[1].companyId)
        assertEquals(101L, request.entityRequests[1].entityId)
        assertEquals(TransactionType.GP_FUNDING_INVOICE, request.entityRequests[1].transactionType)
    }

    @Test
    fun `EntityBillingCurrencyRequest should store company and entity IDs correctly`() {
        // Given
        val companyId = 123L
        val entityId = 456L
        val transactionType = TransactionType.GP_SERVICE_INVOICE

        // When
        val request = EntityBillingCurrencyRequest(
            companyId = companyId,
            entityId = entityId,
            transactionType = transactionType
        )

        // Then
        assertEquals(companyId, request.companyId)
        assertEquals(entityId, request.entityId)
        assertEquals(transactionType, request.transactionType)
    }

    @Test
    fun `EntityBillingCurrencyRequest should handle null transactionType correctly`() {
        // Given
        val companyId = 123L
        val entityId = 456L

        // When
        val request = EntityBillingCurrencyRequest(
            companyId = companyId,
            entityId = entityId,
            transactionType = null
        )

        // Then
        assertEquals(companyId, request.companyId)
        assertEquals(entityId, request.entityId)
        assertEquals(null, request.transactionType)
    }

    @Test
    fun `BulkEntityBillingCurrencyResponse should identify complete success correctly`() {
        // Given
        val successful = listOf(
            EntityBillingCurrencyResult(
                companyId = 123L,
                entityId = 456L,
                currencyCode = CurrencyCode.USD
            )
        )
        val failed = emptyList<EntityBillingCurrencyError>()

        // When
        val response = BulkEntityBillingCurrencyResponse(successful = successful, failed = failed)

        // Then
        assertTrue(response.isCompleteSuccess())
        assertFalse(response.isPartialSuccess())
        assertFalse(response.isCompleteFailure())
    }

    @Test
    fun `BulkEntityBillingCurrencyResponse should identify complete failure correctly`() {
        // Given
        val successful = emptyList<EntityBillingCurrencyResult>()
        val failed = listOf(
            EntityBillingCurrencyError(
                companyId = 123L,
                entityId = 456L,
                errorMessage = "No financial setting found"
            )
        )

        // When
        val response = BulkEntityBillingCurrencyResponse(successful = successful, failed = failed)

        // Then
        assertFalse(response.isCompleteSuccess())
        assertFalse(response.isPartialSuccess())
        assertTrue(response.isCompleteFailure())
    }

    @Test
    fun `BulkEntityBillingCurrencyResponse should identify partial success correctly`() {
        // Given
        val successful = listOf(
            EntityBillingCurrencyResult(
                companyId = 123L,
                entityId = 456L,
                currencyCode = CurrencyCode.USD
            )
        )
        val failed = listOf(
            EntityBillingCurrencyError(
                companyId = 123L,
                entityId = 789L,
                errorMessage = "No financial setting found"
            )
        )

        // When
        val response = BulkEntityBillingCurrencyResponse(successful = successful, failed = failed)

        // Then
        assertFalse(response.isCompleteSuccess())
        assertTrue(response.isPartialSuccess())
        assertFalse(response.isCompleteFailure())
    }

    @Test
    fun `BulkEntityBillingCurrencyResponse success factory method should create correct response`() {
        // Given
        val results = listOf(
            EntityBillingCurrencyResult(
                companyId = 123L,
                entityId = 456L,
                currencyCode = CurrencyCode.EUR
            ),
            EntityBillingCurrencyResult(
                companyId = 789L,
                entityId = 101L,
                currencyCode = CurrencyCode.GBP
            )
        )

        // When
        val response = BulkEntityBillingCurrencyResponse.success(results)

        // Then
        assertTrue(response.isCompleteSuccess())
        assertEquals(2, response.successful.size)
        assertEquals(0, response.failed.size)
        assertEquals(results, response.successful)
    }

    @Test
    fun `BulkEntityBillingCurrencyResponse failure factory method should create correct response`() {
        // Given
        val errors = listOf(
            EntityBillingCurrencyError(
                companyId = 123L,
                entityId = 456L,
                errorMessage = "Error 1"
            ),
            EntityBillingCurrencyError(
                companyId = 789L,
                entityId = 101L,
                errorMessage = "Error 2"
            )
        )

        // When
        val response = BulkEntityBillingCurrencyResponse.failure(errors)

        // Then
        assertTrue(response.isCompleteFailure())
        assertEquals(0, response.successful.size)
        assertEquals(2, response.failed.size)
        assertEquals(errors, response.failed)
    }

    @Test
    fun `BulkEntityBillingCurrencyResponse partial factory method should create correct response`() {
        // Given
        val successful = listOf(
            EntityBillingCurrencyResult(
                companyId = 123L,
                entityId = 456L,
                currencyCode = CurrencyCode.USD
            )
        )
        val failed = listOf(
            EntityBillingCurrencyError(
                companyId = 789L,
                entityId = 101L,
                errorMessage = "Error message"
            )
        )

        // When
        val response = BulkEntityBillingCurrencyResponse.partial(successful, failed)

        // Then
        assertTrue(response.isPartialSuccess())
        assertEquals(1, response.successful.size)
        assertEquals(1, response.failed.size)
        assertEquals(successful, response.successful)
        assertEquals(failed, response.failed)
    }

    @Test
    fun `EntityBillingCurrencyResult should store all fields correctly`() {
        // Given
        val companyId = 123L
        val entityId = 456L
        val currencyCode = CurrencyCode.EUR

        // When
        val result = EntityBillingCurrencyResult(
            companyId = companyId,
            entityId = entityId,
            currencyCode = currencyCode
        )

        // Then
        assertEquals(companyId, result.companyId)
        assertEquals(entityId, result.entityId)
        assertEquals(currencyCode, result.currencyCode)
    }

    @Test
    fun `EntityBillingCurrencyError should store all fields correctly`() {
        // Given
        val companyId = 123L
        val entityId = 456L
        val errorMessage = "Test error message"

        // When
        val error = EntityBillingCurrencyError(
            companyId = companyId,
            entityId = entityId,
            errorMessage = errorMessage
        )

        // Then
        assertEquals(companyId, error.companyId)
        assertEquals(entityId, error.entityId)
        assertEquals(errorMessage, error.errorMessage)
    }

    @Test
    fun `should handle empty responses correctly`() {
        // Given
        val emptySuccessful = emptyList<EntityBillingCurrencyResult>()
        val emptyFailed = emptyList<EntityBillingCurrencyError>()

        // When
        val response = BulkEntityBillingCurrencyResponse(
            successful = emptySuccessful,
            failed = emptyFailed
        )

        // Then
        assertFalse(response.isCompleteSuccess()) // Empty response is neither success nor failure
        assertFalse(response.isPartialSuccess())
        assertFalse(response.isCompleteFailure())
    }
}
