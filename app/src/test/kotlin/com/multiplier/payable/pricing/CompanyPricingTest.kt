package com.multiplier.payable.pricing

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.core.payable.pricing.adapter.PricingServiceAdapter
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.types.Pricing
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class CompanyPricingTest {

    private lateinit var companyPricing: CompanyPricing
    private lateinit var pricingServiceAdapter: PricingServiceAdapter

    @BeforeEach
    fun setup() {
        pricingServiceAdapter = mockk()
        companyPricing = CompanyPricing(pricingServiceAdapter)
    }

    @Test
    fun `should get pricing from pricing service adapter`() {
        // Given
        val companyId = 123L
        val entityId = 456L
        val transactionType = TransactionType.FIRST_INVOICE
        val expectedPricing = Pricing.newBuilder()
            .billingCurrencyCode(CurrencyCode.USD)
            .build()

        every { pricingServiceAdapter.getPricing(companyId) } returns expectedPricing

        // When
        val result = companyPricing.getPricing(transactionType, companyId, entityId)

        // Then
        assertNotNull(result)
        assertEquals(expectedPricing, result)
        verify { pricingServiceAdapter.getPricing(companyId) }
    }

    @Test
    fun `should throw exception when getBillingCurrencies is called`() {
        // Given
        val request = BulkEntityBillingCurrencyRequest(
            entityRequests = listOf(
                EntityBillingCurrencyRequest(
                    companyId = 123L,
                    entityId = 456L,
                    transactionType = null
                )
            )
        )

        // When/Then
        val exception = assertThrows<MplBusinessException> {
            companyPricing.getBillingCurrencies(request)
        }

        assertEquals(PayableErrorCode.UNSUPPORTED_OPERATION, exception.errorCode)
        assertEquals("This is not supported. $request", exception.message)
    }

    @Test
    fun `should handle different transaction types`() {
        // Given
        val companyId = 789L
        val entityId = 101L
        val expectedPricing = Pricing.newBuilder()
            .billingCurrencyCode(CurrencyCode.EUR)
            .build()

        every { pricingServiceAdapter.getPricing(companyId) } returns expectedPricing

        // Test different transaction types
        val transactionTypes = listOf(
            TransactionType.FIRST_INVOICE,
            TransactionType.SECOND_INVOICE,
            TransactionType.ANNUAL_PLAN_INVOICE,
            TransactionType.VAS_INCIDENT_INVOICE
        )

        transactionTypes.forEach { transactionType ->
            // When
            val result = companyPricing.getPricing(transactionType, companyId, entityId)

            // Then
            assertNotNull(result)
            assertEquals(expectedPricing, result)
        }

        verify(exactly = transactionTypes.size) { pricingServiceAdapter.getPricing(companyId) }
    }
}
