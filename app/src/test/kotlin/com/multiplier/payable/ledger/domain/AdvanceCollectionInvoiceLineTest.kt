package com.multiplier.payable.ledger.domain

import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertNull

class AdvanceCollectionInvoiceLineTest {

    @Test
    fun `should create AdvanceCollectionInvoiceLine with all properties`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("15.00")
        )
        val payableItemIds = setOf("item1", "item2", "item3")

        val invoiceLine = AdvanceCollectionInvoiceLine(
            itemType = "SERVICE",
            taxReference = taxReference,
            unitPrice = BigDecimal("150.00"),
            currency = "SGD",
            description = "Professional services",
            payableItemIds = payableItemIds
        )

        assertEquals("SERVICE", invoiceLine.itemType)
        assertEquals(taxReference, invoiceLine.taxReference)
        assertEquals(BigDecimal("150.00"), invoiceLine.unitPrice)
        assertEquals("SGD", invoiceLine.currency)
        assertEquals("Professional services", invoiceLine.description)
        assertEquals(payableItemIds, invoiceLine.payableItemIds)
    }

    @Test
    fun `should create AdvanceCollectionInvoiceLine with empty payableItemIds`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "VAT",
            taxRate = "20.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("30.00")
        )

        val invoiceLine = AdvanceCollectionInvoiceLine(
            itemType = "PRODUCT",
            taxReference = taxReference,
            unitPrice = BigDecimal("100.00"),
            currency = "EUR",
            description = "Product sale"
        )

        assertEquals("PRODUCT", invoiceLine.itemType)
        assertEquals(taxReference, invoiceLine.taxReference)
        assertEquals(BigDecimal("100.00"), invoiceLine.unitPrice)
        assertEquals("EUR", invoiceLine.currency)
        assertEquals("Product sale", invoiceLine.description)
        assertEquals(emptySet<String>(), invoiceLine.payableItemIds)
    }

    @Test
    fun `should create AdvanceCollectionInvoiceLine with null payableItemIds`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "NONE",
            taxRate = "0.0",
            taxType = "EXEMPT",
            taxAmount = BigDecimal.ZERO
        )

        val invoiceLine = AdvanceCollectionInvoiceLine(
            itemType = "EXEMPT_SERVICE",
            taxReference = taxReference,
            unitPrice = BigDecimal("50.00"),
            currency = "USD",
            description = "Tax exempt service",
            payableItemIds = null
        )

        assertEquals("EXEMPT_SERVICE", invoiceLine.itemType)
        assertEquals(taxReference, invoiceLine.taxReference)
        assertEquals(BigDecimal("50.00"), invoiceLine.unitPrice)
        assertEquals("USD", invoiceLine.currency)
        assertEquals("Tax exempt service", invoiceLine.description)
        assertNull(invoiceLine.payableItemIds)
    }

    @Test
    fun `should handle zero unit price`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "FREE",
            taxRate = "0.0",
            taxType = "NONE",
            taxAmount = BigDecimal.ZERO
        )

        val invoiceLine = AdvanceCollectionInvoiceLine(
            itemType = "FREE_SERVICE",
            taxReference = taxReference,
            unitPrice = BigDecimal.ZERO,
            currency = "USD",
            description = "Free service"
        )

        assertEquals("FREE_SERVICE", invoiceLine.itemType)
        assertEquals(BigDecimal.ZERO, invoiceLine.unitPrice)
        assertEquals("USD", invoiceLine.currency)
        assertEquals("Free service", invoiceLine.description)
    }

    @Test
    fun `should handle negative unit price`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "REFUND",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("-5.00")
        )

        val invoiceLine = AdvanceCollectionInvoiceLine(
            itemType = "REFUND",
            taxReference = taxReference,
            unitPrice = BigDecimal("-50.00"),
            currency = "GBP",
            description = "Refund for overpayment"
        )

        assertEquals("REFUND", invoiceLine.itemType)
        assertEquals(BigDecimal("-50.00"), invoiceLine.unitPrice)
        assertEquals("GBP", invoiceLine.currency)
        assertEquals("Refund for overpayment", invoiceLine.description)
    }

    @Test
    fun `should handle large unit price with precision`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "LUXURY",
            taxRate = "25.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("2500.00")
        )

        val invoiceLine = AdvanceCollectionInvoiceLine(
            itemType = "LUXURY_SERVICE",
            taxReference = taxReference,
            unitPrice = BigDecimal("10000.123456"),
            currency = "CHF",
            description = "Premium luxury service"
        )

        assertEquals("LUXURY_SERVICE", invoiceLine.itemType)
        assertEquals(BigDecimal("10000.123456"), invoiceLine.unitPrice)
        assertEquals("CHF", invoiceLine.currency)
        assertEquals("Premium luxury service", invoiceLine.description)
    }

    @Test
    fun `should handle different currency codes`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "LOCAL",
            taxRate = "15.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("15.00")
        )

        val currencies = listOf("USD", "EUR", "GBP", "JPY", "SGD", "AUD", "CAD")

        currencies.forEach { currency ->
            val invoiceLine = AdvanceCollectionInvoiceLine(
                itemType = "MULTI_CURRENCY_SERVICE",
                taxReference = taxReference,
                unitPrice = BigDecimal("100.00"),
                currency = currency,
                description = "Service in $currency"
            )

            assertEquals(currency, invoiceLine.currency)
            assertEquals("Service in $currency", invoiceLine.description)
        }
    }

    @Test
    fun `should handle empty and whitespace descriptions`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "STANDARD",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("10.00")
        )

        val emptyDescriptionLine = AdvanceCollectionInvoiceLine(
            itemType = "EMPTY_DESC",
            taxReference = taxReference,
            unitPrice = BigDecimal("100.00"),
            currency = "USD",
            description = ""
        )

        val whitespaceDescriptionLine = AdvanceCollectionInvoiceLine(
            itemType = "WHITESPACE_DESC",
            taxReference = taxReference,
            unitPrice = BigDecimal("100.00"),
            currency = "USD",
            description = "   "
        )

        assertEquals("", emptyDescriptionLine.description)
        assertEquals("   ", whitespaceDescriptionLine.description)
    }

    @Test
    fun `should handle single payable item id`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "SINGLE",
            taxRate = "5.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("5.00")
        )

        val invoiceLine = AdvanceCollectionInvoiceLine(
            itemType = "SINGLE_ITEM",
            taxReference = taxReference,
            unitPrice = BigDecimal("100.00"),
            currency = "USD",
            description = "Single item service",
            payableItemIds = setOf("single-item-123")
        )

        assertEquals(1, invoiceLine.payableItemIds?.size)
        assertEquals(setOf("single-item-123"), invoiceLine.payableItemIds)
    }

    @Test
    fun `should handle multiple payable item ids`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "MULTI",
            taxRate = "12.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("12.00")
        )

        val payableItemIds = setOf(
            "item-001",
            "item-002",
            "item-003",
            "item-004",
            "item-005"
        )

        val invoiceLine = AdvanceCollectionInvoiceLine(
            itemType = "MULTI_ITEM",
            taxReference = taxReference,
            unitPrice = BigDecimal("500.00"),
            currency = "EUR",
            description = "Multi-item service bundle",
            payableItemIds = payableItemIds
        )

        assertEquals(5, invoiceLine.payableItemIds?.size)
        assertEquals(payableItemIds, invoiceLine.payableItemIds)
    }

    @Test
    fun `should handle different tax reference types`() {
        val fixedTaxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "FIXED_TAX",
            taxRate = "0.0",
            taxType = "FIXED",
            taxAmount = BigDecimal("25.00")
        )

        val invoiceLine = AdvanceCollectionInvoiceLine(
            itemType = "LICENSE",
            taxReference = fixedTaxReference,
            unitPrice = BigDecimal("500.00"),
            currency = "USD",
            description = "Software license",
            payableItemIds = setOf("license_001")
        )

        assertEquals("LICENSE", invoiceLine.itemType)
        assertEquals(fixedTaxReference, invoiceLine.taxReference)
        assertEquals("FIXED", invoiceLine.taxReference.taxType)
        assertEquals(BigDecimal("25.00"), invoiceLine.taxReference.taxAmount)
    }

    @Test
    fun `should handle large payableItemIds set`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("10.00")
        )
        
        val largePayableItemIds = (1..100).map { "item_$it" }.toSet()

        val invoiceLine = AdvanceCollectionInvoiceLine(
            itemType = "BULK_SERVICE",
            taxReference = taxReference,
            unitPrice = BigDecimal("1000.00"),
            currency = "SGD",
            description = "Bulk services",
            payableItemIds = largePayableItemIds
        )

        assertEquals(100, invoiceLine.payableItemIds?.size)
        assertEquals(largePayableItemIds, invoiceLine.payableItemIds)
    }
}
