package com.multiplier.payable.ledger.domain.invoice

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.creditnote.database.CreditNoteDto
import com.multiplier.core.payable.creditnote.database.CreditNoteItemDto
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.types.PayableStatus
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class ManualAdjustedSecondInvoiceCreditNoteTest {

    @Test
    fun `getAdjustedLineItems should return only advance collection adjustment line items`() {
        // Given
        val payable = createCompanyPayable()
        val creditNoteItems = listOf(
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR),
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GLOBAL_PAYROLL),
            createCreditNoteItemDto(LineItemType.MANAGEMENT_FEE_EOR), // Not an adjustment line
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR),
            createCreditNoteItemDto(LineItemType.EOR_SALARY_DISBURSEMENT) // Not an adjustment line
        )
        val creditNote = createCreditNoteDto(creditNoteItems)
        val manualCreditNote = ManualAdjustedSecondInvoiceCreditNote(payable, creditNote)

        // When
        val result = manualCreditNote.getAdjustedLineItems()

        // Then
        assertEquals(3, result.size)
        assertTrue(result.all { it.itemType in LineItemType.getAdvanceCollectionAdjustmentLineItemTypes() })
        
        val itemTypes = result.map { it.itemType }
        assertTrue(itemTypes.contains(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR))
        assertTrue(itemTypes.contains(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GLOBAL_PAYROLL))
        assertTrue(itemTypes.contains(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR))
    }

    @Test
    fun `getAdjustedLineItems should return empty list when no adjustment line items`() {
        // Given
        val payable = createCompanyPayable()
        val creditNoteItems = listOf(
            createCreditNoteItemDto(LineItemType.MANAGEMENT_FEE_EOR),
            createCreditNoteItemDto(LineItemType.EOR_SALARY_DISBURSEMENT),
            createCreditNoteItemDto(LineItemType.VAT_PAYROLL_COST)
        )
        val creditNote = createCreditNoteDto(creditNoteItems)
        val manualCreditNote = ManualAdjustedSecondInvoiceCreditNote(payable, creditNote)

        // When
        val result = manualCreditNote.getAdjustedLineItems()

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `getAdjustedLineItems should return empty list when credit note has no items`() {
        // Given
        val payable = createCompanyPayable()
        val creditNote = createCreditNoteDto(emptyList())
        val manualCreditNote = ManualAdjustedSecondInvoiceCreditNote(payable, creditNote)

        // When
        val result = manualCreditNote.getAdjustedLineItems()

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `getAdjustedLineItems should handle all advance collection adjustment line item types`() {
        // Given
        val payable = createCompanyPayable()
        val allAdjustmentTypes = LineItemType.getAdvanceCollectionAdjustmentLineItemTypes()
        val creditNoteItems = allAdjustmentTypes.map { createCreditNoteItemDto(it) }
        val creditNote = createCreditNoteDto(creditNoteItems)
        val manualCreditNote = ManualAdjustedSecondInvoiceCreditNote(payable, creditNote)

        // When
        val result = manualCreditNote.getAdjustedLineItems()

        // Then
        assertEquals(allAdjustmentTypes.size, result.size)
        val resultItemTypes = result.map { it.itemType }.toSet()
        assertEquals(allAdjustmentTypes, resultItemTypes)
    }

    @Test
    fun `constructor should set payable and creditNote correctly`() {
        // Given
        val payable = createCompanyPayable()
        val creditNote = createCreditNoteDto(emptyList())

        // When
        val manualCreditNote = ManualAdjustedSecondInvoiceCreditNote(payable, creditNote)

        // Then
        assertEquals(payable, manualCreditNote.payable)
        assertEquals(creditNote, manualCreditNote.creditNote)
    }

    @Test
    fun `should implement AdjustedSecondInvoice interface`() {
        // Given
        val payable = createCompanyPayable()
        val creditNote = createCreditNoteDto(emptyList())
        val manualCreditNote = ManualAdjustedSecondInvoiceCreditNote(payable, creditNote)

        // Then
        assertTrue(manualCreditNote is AdjustedSecondInvoice)
        assertEquals(payable, manualCreditNote.payable)
    }

    @Test
    fun `getAdjustedLineItems should filter correctly with mixed line item types`() {
        // Given
        val payable = createCompanyPayable()
        val creditNoteItems = listOf(
            // Adjustment types
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR),
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_ONE_TIME_SETUP_FEE),
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PAYSLIP_MINIMUM_COMMITMENT),
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PER_PAYSLIP_FEE),
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_TOTAL_PAYMENTS),
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_STAT_FILING),
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_YEAR_END_DOCUMENTATION),
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR_CONTRACTOR),
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR_FREELANCER),
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR),
            // Non-adjustment types
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_EOR),
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_GLOBAL_PAYROLL),
            createCreditNoteItemDto(LineItemType.MANAGEMENT_FEE_EOR)
        )
        val creditNote = createCreditNoteDto(creditNoteItems)
        val manualCreditNote = ManualAdjustedSecondInvoiceCreditNote(payable, creditNote)

        // When
        val result = manualCreditNote.getAdjustedLineItems()

        // Then
        assertEquals(10, result.size) // Only the adjustment types
        assertTrue(result.all { it.itemType in LineItemType.getAdvanceCollectionAdjustmentLineItemTypes() })
        assertTrue(result.none { 
            it.itemType == LineItemType.ORDER_FORM_ADVANCE_EOR ||
            it.itemType == LineItemType.ORDER_FORM_ADVANCE_GLOBAL_PAYROLL ||
            it.itemType == LineItemType.MANAGEMENT_FEE_EOR
        })
    }

    @Test
    fun `getAdjustedLineItems should handle duplicate adjustment line item types`() {
        // Given
        val payable = createCompanyPayable()
        val creditNoteItems = listOf(
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR),
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR), // Duplicate
            createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GLOBAL_PAYROLL),
            createCreditNoteItemDto(LineItemType.MANAGEMENT_FEE_EOR) // Non-adjustment
        )
        val creditNote = createCreditNoteDto(creditNoteItems)
        val manualCreditNote = ManualAdjustedSecondInvoiceCreditNote(payable, creditNote)

        // When
        val result = manualCreditNote.getAdjustedLineItems()

        // Then
        assertEquals(3, result.size) // Should include both duplicates
        assertEquals(2, result.count { it.itemType == LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR })
        assertEquals(1, result.count { it.itemType == LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GLOBAL_PAYROLL })
    }

    private fun createCompanyPayable(
        id: Long = 1L,
        items: List<PayableItem> = emptyList()
    ): CompanyPayable {
        return CompanyPayable(
            id = id,
            companyId = 100L,
            transactionId = "TXN-001",
            items = items,
            itemType = TransactionType.SECOND_INVOICE,
            status = PayableStatus.DRAFT
        )
    }

    private fun createCreditNoteDto(items: List<CreditNoteItemDto>): CreditNoteDto {
        val creditNote = mockk<CreditNoteDto>()
        every { creditNote.items } returns items
        every { creditNote.creditNoteNo } returns "CN-001"
        every { creditNote.companyId } returns 100L
        return creditNote
    }

    private fun createCreditNoteItemDto(itemType: LineItemType): CreditNoteItemDto {
        val item = mockk<CreditNoteItemDto>()
        every { item.itemType } returns itemType
        every { item.description } returns "Test credit note item for ${itemType.name}"
        every { item.quantity } returns 1.0
        every { item.unitAmount } returns 100.0
        return item
    }
}
