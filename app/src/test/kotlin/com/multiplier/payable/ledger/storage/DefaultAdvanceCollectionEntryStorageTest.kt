package com.multiplier.payable.ledger.storage

import com.multiplier.payable.ledger.domain.AdvanceCollectionEntry
import com.multiplier.payable.ledger.domain.AdvanceCollectionEntryStatus
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionEntry
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionEntryRepository
import com.multiplier.payable.ledger.mapper.AdvanceCollectionEntryJpaToModelMapper
import com.multiplier.payable.ledger.mapper.AdvanceCollectionEntryModelToJpaMapper
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.util.*
import kotlin.test.assertEquals

class DefaultAdvanceCollectionEntryStorageTest {

    private lateinit var repository: JpaAdvanceCollectionEntryRepository
    private lateinit var modelToJpaMapper: AdvanceCollectionEntryModelToJpaMapper
    private lateinit var jpaToModelMapper: AdvanceCollectionEntryJpaToModelMapper
    private lateinit var storage: DefaultAdvanceCollectionEntryStorage

    @BeforeEach
    fun setUp() {
        repository = mockk()
        modelToJpaMapper = mockk()
        jpaToModelMapper = mockk()
        storage = DefaultAdvanceCollectionEntryStorage(repository, modelToJpaMapper, jpaToModelMapper)
    }

    @Test
    fun `store should create new entry when id is null`() {
        // Given
        val domainEntry = createDomainEntry(id = null)
        val jpaEntry = createJpaEntry(id = null)
        val savedJpaEntry = createJpaEntry(id = 1L)
        val expectedResult = createDomainEntry(id = 1L)

        every { modelToJpaMapper.map(domainEntry) } returns jpaEntry
        every { repository.save(jpaEntry) } returns savedJpaEntry
        every { jpaToModelMapper.map(savedJpaEntry) } returns expectedResult

        // When
        val result = storage.store(domainEntry)

        // Then
        assertEquals(expectedResult, result)
        verify { modelToJpaMapper.map(domainEntry) }
        verify { repository.save(jpaEntry) }
        verify { jpaToModelMapper.map(savedJpaEntry) }
    }

    @Test
    fun `store should update existing entry when id is not null`() {
        // Given
        val entryId = 1L
        val domainEntry = createDomainEntry(
            id = entryId,
            status = AdvanceCollectionEntryStatus.COMMITED,
            note = "Updated note"
        )
        val existingJpaEntry = createJpaEntry(
            id = entryId,
            status = AdvanceCollectionEntryStatus.RESERVED,
            note = "Original note"
        )
        val mergedJpaEntry = createJpaEntry(
            id = entryId,
            status = AdvanceCollectionEntryStatus.COMMITED,
            note = "Updated note"
        )
        val expectedResult = createDomainEntry(id = entryId)

        every { repository.findById(entryId) } returns Optional.of(existingJpaEntry)
        every { repository.save(any<JpaAdvanceCollectionEntry>()) } returns mergedJpaEntry
        every { jpaToModelMapper.map(mergedJpaEntry) } returns expectedResult

        // When
        val result = storage.store(domainEntry)

        // Then
        assertEquals(expectedResult, result)
        verify { repository.findById(entryId) }
        verify { repository.save(any<JpaAdvanceCollectionEntry>()) }
        verify { jpaToModelMapper.map(mergedJpaEntry) }
    }

    @Test
    fun `store should throw exception when updating non-existent entry`() {
        // Given
        val entryId = 999L
        val domainEntry = createDomainEntry(id = entryId)

        every { repository.findById(entryId) } returns Optional.empty()

        // When & Then
        try {
            storage.store(domainEntry)
        } catch (e: IllegalArgumentException) {
            assertEquals("Entry with id $entryId not found", e.message)
        }

        verify { repository.findById(entryId) }
    }

    @Test
    fun `append should always create new entry with null id`() {
        // Given
        val domainEntry = createDomainEntry(id = 5L) // Even with existing ID
        val appendOnlyEntry = domainEntry.copy(id = null)
        val jpaEntry = createJpaEntry(id = null)
        val savedJpaEntry = createJpaEntry(id = 1L)
        val expectedResult = createDomainEntry(id = 1L)

        every { modelToJpaMapper.map(appendOnlyEntry) } returns jpaEntry
        every { repository.save(jpaEntry) } returns savedJpaEntry
        every { jpaToModelMapper.map(savedJpaEntry) } returns expectedResult

        // When
        val result = storage.append(domainEntry)

        // Then
        assertEquals(expectedResult, result)
        verify { modelToJpaMapper.map(appendOnlyEntry) }
        verify { repository.save(jpaEntry) }
        verify { jpaToModelMapper.map(savedJpaEntry) }
    }

    @Test
    fun `append should handle different entry configurations`() {
        // Given
        val testCases = listOf(
            createDomainEntry(
                transactionId = "TXN-001",
                balanceId = 100L,
                amount = BigDecimal("500.00"),
                status = AdvanceCollectionEntryStatus.RESERVED
            ),
            createDomainEntry(
                transactionId = "TXN-002",
                balanceId = 101L,
                amount = BigDecimal("-250.00"),
                status = AdvanceCollectionEntryStatus.COMMITED
            ),
            createDomainEntry(
                transactionId = "TXN-003",
                balanceId = 102L,
                amount = BigDecimal.ZERO,
                status = AdvanceCollectionEntryStatus.ROLLED_BACK
            )
        )

        testCases.forEachIndexed { index, domainEntry ->
            val appendOnlyEntry = domainEntry.copy(id = null)
            val jpaEntry = createJpaEntry(id = null)
            val savedJpaEntry = createJpaEntry(id = index.toLong() + 1)
            val expectedResult = createDomainEntry(id = index.toLong() + 1)

            every { modelToJpaMapper.map(appendOnlyEntry) } returns jpaEntry
            every { repository.save(jpaEntry) } returns savedJpaEntry
            every { jpaToModelMapper.map(savedJpaEntry) } returns expectedResult

            // When
            val result = storage.append(domainEntry)

            // Then
            assertEquals(expectedResult, result)
        }
    }

    @Test
    fun `store should merge entry fields correctly during update`() {
        // Given
        val entryId = 1L
        val originalTransactionId = "TXN-001"
        val updatedTransactionId = "TXN-002"
        val originalNote = "Original note"
        val updatedNote = "Updated note"
        val originalReferences = mapOf("key1" to "value1")
        val updatedReferences = mapOf("key2" to "value2")
        val originalStatus = AdvanceCollectionEntryStatus.RESERVED
        val updatedStatus = AdvanceCollectionEntryStatus.COMMITED
        val originalAmount = BigDecimal("100.00")
        val updatedAmount = BigDecimal("200.00")

        val domainEntry = createDomainEntry(
            id = entryId,
            transactionId = updatedTransactionId,
            note = updatedNote,
            references = updatedReferences,
            status = updatedStatus,
            amount = updatedAmount
        )
        val existingJpaEntry = createJpaEntry(
            id = entryId,
            transactionId = originalTransactionId,
            note = originalNote,
            references = originalReferences,
            status = originalStatus,
            amount = originalAmount
        )
        val expectedMergedEntry = existingJpaEntry.copy(
            transactionId = updatedTransactionId,
            note = updatedNote,
            references = updatedReferences,
            status = updatedStatus,
            amount = updatedAmount
        )
        val expectedResult = createDomainEntry(id = entryId)

        every { repository.findById(entryId) } returns Optional.of(existingJpaEntry)
        every { repository.save(expectedMergedEntry) } returns expectedMergedEntry
        every { jpaToModelMapper.map(expectedMergedEntry) } returns expectedResult

        // When
        val result = storage.store(domainEntry)

        // Then
        assertEquals(expectedResult, result)
        verify { repository.findById(entryId) }
        verify { repository.save(expectedMergedEntry) }
        verify { jpaToModelMapper.map(expectedMergedEntry) }
    }

    @Test
    fun `store should preserve existing references when domain entry has null references`() {
        // Given
        val entryId = 1L
        val existingReferences = mapOf("key1" to "value1", "key2" to "value2")
        
        val domainEntry = createDomainEntry(
            id = entryId,
            references = null
        )
        val existingJpaEntry = createJpaEntry(
            id = entryId,
            references = existingReferences
        )
        val expectedMergedEntry = existingJpaEntry.copy(
            transactionId = domainEntry.transactionId,
            note = domainEntry.note,
            references = existingReferences, // Should preserve existing
            status = domainEntry.status,
            amount = domainEntry.amount
        )
        val expectedResult = createDomainEntry(id = entryId)

        every { repository.findById(entryId) } returns Optional.of(existingJpaEntry)
        every { repository.save(expectedMergedEntry) } returns expectedMergedEntry
        every { jpaToModelMapper.map(expectedMergedEntry) } returns expectedResult

        // When
        val result = storage.store(domainEntry)

        // Then
        assertEquals(expectedResult, result)
        verify { repository.findById(entryId) }
        verify { repository.save(expectedMergedEntry) }
        verify { jpaToModelMapper.map(expectedMergedEntry) }
    }

    @Test
    fun `append should handle entry with zero amount`() {
        // Given
        val domainEntry = createDomainEntry(amount = BigDecimal.ZERO)
        val appendOnlyEntry = domainEntry.copy(id = null)
        val jpaEntry = createJpaEntry(id = null)
        val savedJpaEntry = createJpaEntry(id = 1L)
        val expectedResult = createDomainEntry(id = 1L)

        every { modelToJpaMapper.map(appendOnlyEntry) } returns jpaEntry
        every { repository.save(jpaEntry) } returns savedJpaEntry
        every { jpaToModelMapper.map(savedJpaEntry) } returns expectedResult

        // When
        val result = storage.append(domainEntry)

        // Then
        assertEquals(expectedResult, result)
        verify { modelToJpaMapper.map(appendOnlyEntry) }
        verify { repository.save(jpaEntry) }
        verify { jpaToModelMapper.map(savedJpaEntry) }
    }

    @Test
    fun `append should handle entry with negative amount`() {
        // Given
        val domainEntry = createDomainEntry(amount = BigDecimal("-750.00"))
        val appendOnlyEntry = domainEntry.copy(id = null)
        val jpaEntry = createJpaEntry(id = null)
        val savedJpaEntry = createJpaEntry(id = 1L)
        val expectedResult = createDomainEntry(id = 1L)

        every { modelToJpaMapper.map(appendOnlyEntry) } returns jpaEntry
        every { repository.save(jpaEntry) } returns savedJpaEntry
        every { jpaToModelMapper.map(savedJpaEntry) } returns expectedResult

        // When
        val result = storage.append(domainEntry)

        // Then
        assertEquals(expectedResult, result)
        verify { modelToJpaMapper.map(appendOnlyEntry) }
        verify { repository.save(jpaEntry) }
        verify { jpaToModelMapper.map(savedJpaEntry) }
    }

    @Test
    fun `store should handle all entry statuses`() {
        // Given
        val statuses = listOf(
            AdvanceCollectionEntryStatus.RESERVED,
            AdvanceCollectionEntryStatus.COMMITED,
            AdvanceCollectionEntryStatus.ROLLED_BACK
        )

        statuses.forEachIndexed { index, status ->
            val domainEntry = createDomainEntry(id = null, status = status)
            val jpaEntry = createJpaEntry(id = null, status = status)
            val savedJpaEntry = createJpaEntry(id = index.toLong() + 1, status = status)
            val expectedResult = createDomainEntry(id = index.toLong() + 1, status = status)

            every { modelToJpaMapper.map(domainEntry) } returns jpaEntry
            every { repository.save(jpaEntry) } returns savedJpaEntry
            every { jpaToModelMapper.map(savedJpaEntry) } returns expectedResult

            // When
            val result = storage.store(domainEntry)

            // Then
            assertEquals(expectedResult, result)
        }
    }

    @Test
    fun `store should handle repository throwing exception during create`() {
        // Given
        val domainEntry = createDomainEntry(id = null)
        val jpaEntry = createJpaEntry(id = null)

        every { modelToJpaMapper.map(domainEntry) } returns jpaEntry
        every { repository.save(jpaEntry) } throws RuntimeException("Database error")

        // When & Then
        try {
            storage.store(domainEntry)
        } catch (e: RuntimeException) {
            assertEquals("Database error", e.message)
        }

        verify { modelToJpaMapper.map(domainEntry) }
        verify { repository.save(jpaEntry) }
    }

    @Test
    fun `append should handle mapper throwing exception`() {
        // Given
        val domainEntry = createDomainEntry()
        val appendOnlyEntry = domainEntry.copy(id = null)

        every { modelToJpaMapper.map(appendOnlyEntry) } throws RuntimeException("Mapping error")

        // When & Then
        try {
            storage.append(domainEntry)
        } catch (e: RuntimeException) {
            assertEquals("Mapping error", e.message)
        }

        verify { modelToJpaMapper.map(appendOnlyEntry) }
    }

    private fun createDomainEntry(
        id: Long? = null,
        transactionId: String = "TXN-001",
        balanceId: Long = 100L,
        amount: BigDecimal = BigDecimal("500.00"),
        note: String? = "Test entry",
        references: Map<String, String>? = mapOf("test" to "value"),
        status: AdvanceCollectionEntryStatus = AdvanceCollectionEntryStatus.RESERVED
    ): AdvanceCollectionEntry {
        return AdvanceCollectionEntry(
            id = id,
            transactionId = transactionId,
            balanceId = balanceId,
            amount = amount,
            note = note,
            references = references,
            status = status
        )
    }

    private fun createJpaEntry(
        id: Long? = null,
        transactionId: String = "TXN-001",
        balanceId: Long = 100L,
        amount: BigDecimal = BigDecimal("500.00"),
        note: String? = "Test entry",
        references: Map<String, String> = mapOf("test" to "value"),
        status: AdvanceCollectionEntryStatus = AdvanceCollectionEntryStatus.RESERVED
    ): JpaAdvanceCollectionEntry {
        return JpaAdvanceCollectionEntry(
            id = id,
            transactionId = transactionId,
            balanceId = balanceId,
            amount = amount,
            note = note,
            references = references,
            status = status
        )
    }
}
