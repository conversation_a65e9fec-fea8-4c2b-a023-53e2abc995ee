package com.multiplier.payable.ledger

import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertNull

class AdvanceCollectionBalanceMetadataTest {

    @Test
    fun `should create metadata with all properties`() {
        // Given
        val transactionId = "TXN-001"
        val invoiceNo = "INV-001"
        val referenceLine = AdvanceCollectionInvoiceLine(
            itemType = "SERVICE",
            taxReference = AdvanceCollectionInvoiceLineTaxReference(
                taxCode = "GST",
                taxRate = "10.0",
                taxType = "PERCENTAGE",
                taxAmount = BigDecimal("15.00")
            ),
            unitPrice = BigDecimal("150.00"),
            currency = "SGD",
            description = "Professional services",
            payableItemIds = setOf("item1", "item2")
        )

        // When
        val metadata = AdvanceCollectionBalanceMetadata(
            transactionId = transactionId,
            invoiceNo = invoiceNo,
            referenceLine = referenceLine
        )

        // Then
        assertEquals(transactionId, metadata.transactionId)
        assertEquals(invoiceNo, metadata.invoiceNo)
        assertEquals(referenceLine, metadata.referenceLine)
    }

    @Test
    fun `should create metadata with null transactionId`() {
        // Given
        val invoiceNo = "INV-002"
        val referenceLine = AdvanceCollectionInvoiceLine(
            itemType = "PRODUCT",
            taxReference = AdvanceCollectionInvoiceLineTaxReference(
                taxCode = "VAT",
                taxRate = "20.0",
                taxType = "PERCENTAGE",
                taxAmount = BigDecimal("20.00")
            ),
            unitPrice = BigDecimal("100.00"),
            currency = "EUR",
            description = "Product sale",
            payableItemIds = setOf("item3")
        )

        // When
        val metadata = AdvanceCollectionBalanceMetadata(
            transactionId = null,
            invoiceNo = invoiceNo,
            referenceLine = referenceLine
        )

        // Then
        assertNull(metadata.transactionId)
        assertEquals(invoiceNo, metadata.invoiceNo)
        assertEquals(referenceLine, metadata.referenceLine)
    }

    @Test
    fun `should create metadata with null referenceLine`() {
        // Given
        val transactionId = "TXN-003"
        val invoiceNo = "INV-003"

        // When
        val metadata = AdvanceCollectionBalanceMetadata(
            transactionId = transactionId,
            invoiceNo = invoiceNo,
            referenceLine = null
        )

        // Then
        assertEquals(transactionId, metadata.transactionId)
        assertEquals(invoiceNo, metadata.invoiceNo)
        assertNull(metadata.referenceLine)
    }

    @Test
    fun `should create metadata with default transactionId`() {
        // Given
        val invoiceNo = "INV-004"

        // When
        val metadata = AdvanceCollectionBalanceMetadata(
            invoiceNo = invoiceNo
        )

        // Then
        assertNull(metadata.transactionId) // default value is null
        assertEquals(invoiceNo, metadata.invoiceNo)
        assertNull(metadata.referenceLine) // default value is null
    }

    @Test
    fun `should support equality comparison`() {
        // Given
        val transactionId = "TXN-005"
        val invoiceNo = "INV-005"
        val referenceLine = AdvanceCollectionInvoiceLine(
            itemType = "SERVICE",
            taxReference = AdvanceCollectionInvoiceLineTaxReference(
                taxCode = "GST",
                taxRate = "5.0",
                taxType = "PERCENTAGE",
                taxAmount = BigDecimal("5.00")
            ),
            unitPrice = BigDecimal("100.00"),
            currency = "USD",
            description = "Consulting",
            payableItemIds = setOf("item4")
        )

        val metadata1 = AdvanceCollectionBalanceMetadata(
            transactionId = transactionId,
            invoiceNo = invoiceNo,
            referenceLine = referenceLine
        )

        val metadata2 = AdvanceCollectionBalanceMetadata(
            transactionId = transactionId,
            invoiceNo = invoiceNo,
            referenceLine = referenceLine
        )

        // When & Then
        assertEquals(metadata1, metadata2)
        assertEquals(metadata1.hashCode(), metadata2.hashCode())
    }

    @Test
    fun `should support copy functionality`() {
        // Given
        val originalMetadata = AdvanceCollectionBalanceMetadata(
            transactionId = "TXN-006",
            invoiceNo = "INV-006",
            referenceLine = null
        )

        // When
        val copiedMetadata = originalMetadata.copy(
            transactionId = "TXN-007",
            invoiceNo = "INV-007"
        )

        // Then
        assertEquals("TXN-007", copiedMetadata.transactionId)
        assertEquals("INV-007", copiedMetadata.invoiceNo)
        assertNull(copiedMetadata.referenceLine)
        
        // Original should remain unchanged
        assertEquals("TXN-006", originalMetadata.transactionId)
        assertEquals("INV-006", originalMetadata.invoiceNo)
    }

    @Test
    fun `should handle different invoice number formats`() {
        // Given
        val invoiceNumbers = listOf(
            "INV-001",
            "INVOICE-2023-001",
            "ADV-COL-INV-12345",
            "2023-12-001-INV",
            "INV_001_FINAL",
            "DRAFT-INV-001"
        )

        invoiceNumbers.forEach { invoiceNo ->
            // When
            val metadata = AdvanceCollectionBalanceMetadata(
                transactionId = "TXN-001",
                invoiceNo = invoiceNo
            )

            // Then
            assertEquals(invoiceNo, metadata.invoiceNo)
        }
    }

    @Test
    fun `should handle special characters in invoice number`() {
        // Given
        val specialInvoiceNumbers = listOf(
            "INV@001",
            "INV#001",
            "INV$001",
            "INV%001",
            "INV&001",
            "INV*001",
            "INV(001)",
            "INV-001+ADDON",
            "INV.001.FINAL"
        )

        specialInvoiceNumbers.forEach { invoiceNo ->
            // When
            val metadata = AdvanceCollectionBalanceMetadata(
                transactionId = "TXN-001",
                invoiceNo = invoiceNo
            )

            // Then
            assertEquals(invoiceNo, metadata.invoiceNo)
        }
    }

    @Test
    fun `should handle unicode characters in invoice number`() {
        // Given
        val unicodeInvoiceNumbers = listOf(
            "INV-001-测试",
            "INV-001-テスト",
            "INV-001-тест",
            "INV-001-δοκιμή",
            "INV-001-परीक्षण"
        )

        unicodeInvoiceNumbers.forEach { invoiceNo ->
            // When
            val metadata = AdvanceCollectionBalanceMetadata(
                transactionId = "TXN-001",
                invoiceNo = invoiceNo
            )

            // Then
            assertEquals(invoiceNo, metadata.invoiceNo)
        }
    }

    @Test
    fun `should handle long invoice numbers`() {
        // Given
        val longInvoiceNo = "INV-" + "0".repeat(100) + "-VERY-LONG-INVOICE-NUMBER-FOR-TESTING-PURPOSES"

        // When
        val metadata = AdvanceCollectionBalanceMetadata(
            transactionId = "TXN-001",
            invoiceNo = longInvoiceNo
        )

        // Then
        assertEquals(longInvoiceNo, metadata.invoiceNo)
    }

    @Test
    fun `should handle different transaction ID formats`() {
        // Given
        val transactionIds = listOf(
            "TXN-001",
            "TRANSACTION-2023-001",
            "ADV-COL-TXN-12345",
            "2023-12-001-TXN",
            "TXN_001_FINAL",
            "DRAFT-TXN-001",
            java.util.UUID.randomUUID().toString()
        )

        transactionIds.forEach { transactionId ->
            // When
            val metadata = AdvanceCollectionBalanceMetadata(
                transactionId = transactionId,
                invoiceNo = "INV-001"
            )

            // Then
            assertEquals(transactionId, metadata.transactionId)
        }
    }
}
