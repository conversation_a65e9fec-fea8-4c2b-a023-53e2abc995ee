package com.multiplier.payable.ledger.listener.invoice

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.ledger.listener.LedgerInvoiceListener
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertSame
import kotlin.test.assertTrue

class LedgerInvoiceListenerFactoryTest {

    private lateinit var factory: LedgerInvoiceListenerFactory
    private lateinit var mockListener1: LedgerInvoiceListener
    private lateinit var mockListener2: LedgerInvoiceListener
    private lateinit var mockListener3: LedgerInvoiceListener

    @BeforeEach
    fun setUp() {
        mockListener1 = mockk()
        mockListener2 = mockk()
        mockListener3 = mockk()

        every { mockListener1.transactionType() } returns TransactionType.ORDER_FORM_ADVANCE
        every { mockListener2.transactionType() } returns TransactionType.SECOND_INVOICE
        every { mockListener3.transactionType() } returns TransactionType.ANNUAL_PLAN_INVOICE

        val listeners = listOf(mockListener1, mockListener2, mockListener3)
        factory = LedgerInvoiceListenerFactory(listeners)
    }

    @Test
    fun `factory should initialize listener map correctly`() {
        // When & Then - factory initialization should complete without errors
        // The init block should populate the listenerMap
        
        // Verify we can get listeners for the registered transaction types
        val listener1 = factory.get(TransactionType.ORDER_FORM_ADVANCE)
        val listener2 = factory.get(TransactionType.SECOND_INVOICE)
        val listener3 = factory.get(TransactionType.ANNUAL_PLAN_INVOICE)

        assertSame(mockListener1, listener1)
        assertSame(mockListener2, listener2)
        assertSame(mockListener3, listener3)
    }

    @Test
    fun `get should return correct listener for ORDER_FORM_ADVANCE`() {
        // When
        val result = factory.get(TransactionType.ORDER_FORM_ADVANCE)

        // Then
        assertSame(mockListener1, result)
    }

    @Test
    fun `get should return correct listener for SECOND_INVOICE`() {
        // When
        val result = factory.get(TransactionType.SECOND_INVOICE)

        // Then
        assertSame(mockListener2, result)
    }

    @Test
    fun `get should return correct listener for ANNUAL_PLAN_INVOICE`() {
        // When
        val result = factory.get(TransactionType.ANNUAL_PLAN_INVOICE)

        // Then
        assertSame(mockListener3, result)
    }

    @Test
    fun `get should throw exception for unsupported transaction type`() {
        // When & Then
        val exception = assertThrows<MplBusinessException> {
            factory.get(TransactionType.FIRST_INVOICE) // Not registered
        }

        assertTrue(exception.message!!.contains("No ledger invoice listener found for transaction type: FIRST_INVOICE"))
    }

    @Test
    fun `get should throw exception for null transaction type mapping`() {
        // Given - create factory with listener that returns a different transaction type
        val conflictingListener = mockk<LedgerInvoiceListener>()
        every { conflictingListener.transactionType() } returns TransactionType.FIRST_INVOICE

        val factoryWithConflict = LedgerInvoiceListenerFactory(listOf(conflictingListener))

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            factoryWithConflict.get(TransactionType.SECOND_INVOICE)
        }

        assertTrue(exception.message!!.contains("No ledger invoice listener found for transaction type: SECOND_INVOICE"))
    }

    @Test
    fun `factory should handle empty listener list`() {
        // Given
        val emptyFactory = LedgerInvoiceListenerFactory(emptyList())

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            emptyFactory.get(TransactionType.ORDER_FORM_ADVANCE)
        }

        assertTrue(exception.message!!.contains("No ledger invoice listener found for transaction type: ORDER_FORM_ADVANCE"))
    }

    @Test
    fun `factory should handle duplicate transaction types by using last registered`() {
        // Given
        val duplicateListener = mockk<LedgerInvoiceListener>()
        every { duplicateListener.transactionType() } returns TransactionType.ORDER_FORM_ADVANCE // Same as mockListener1

        val factoryWithDuplicate = LedgerInvoiceListenerFactory(listOf(mockListener1, duplicateListener))

        // When
        val result = factoryWithDuplicate.get(TransactionType.ORDER_FORM_ADVANCE)

        // Then - should return the last registered listener
        assertSame(duplicateListener, result)
    }

    @Test
    fun `SUPPORTED_TRANSACTION_TYPES should contain expected transaction types`() {
        // When
        val supportedTypes = LedgerInvoiceListenerFactory.SUPPORTED_TRANSACTION_TYPES

        // Then
        assertEquals(5, supportedTypes.size)
        assertTrue(supportedTypes.contains(TransactionType.ORDER_FORM_ADVANCE))
        assertTrue(supportedTypes.contains(TransactionType.SECOND_INVOICE))
        assertTrue(supportedTypes.contains(TransactionType.ANNUAL_PLAN_AOR_INVOICE))
        assertTrue(supportedTypes.contains(TransactionType.ANNUAL_PLAN_INVOICE))
        assertTrue(supportedTypes.contains(TransactionType.GP_SERVICE_INVOICE))
    }

    @Test
    fun `factory should work with all supported transaction types`() {
        // Given - create listeners for all supported types
        val allListeners = mutableListOf<LedgerInvoiceListener>()
        LedgerInvoiceListenerFactory.SUPPORTED_TRANSACTION_TYPES.forEach { transactionType ->
            val listener = mockk<LedgerInvoiceListener>()
            every { listener.transactionType() } returns transactionType
            allListeners.add(listener)
        }

        val completeFactory = LedgerInvoiceListenerFactory(allListeners)

        // When & Then - should be able to get listener for each supported type
        LedgerInvoiceListenerFactory.SUPPORTED_TRANSACTION_TYPES.forEach { transactionType ->
            val listener = completeFactory.get(transactionType)
            assertEquals(transactionType, listener.transactionType())
        }
    }
}
