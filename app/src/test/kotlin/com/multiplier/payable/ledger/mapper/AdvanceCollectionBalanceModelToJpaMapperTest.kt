package com.multiplier.payable.ledger.mapper

import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.ledger.AdvanceCollectionBalanceMetadata
import com.multiplier.payable.ledger.domain.AdvanceCollectionBalance
import com.multiplier.payable.ledger.domain.AdvanceCollectionProduct
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionInvoiceLineTaxReference
import com.multiplier.payable.types.CurrencyCode
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertNull

class AdvanceCollectionBalanceModelToJpaMapperTest {

    private lateinit var hashBuilder: AdvanceCollectionBalanceHashBuilder
    private lateinit var referenceLineModelToJpaMapper: AdvanceCollectionInvoiceLineModelToJpaMapper
    private lateinit var mapper: AdvanceCollectionBalanceModelToJpaMapper

    @BeforeEach
    fun setUp() {
        hashBuilder = mockk()
        referenceLineModelToJpaMapper = mockk()
        mapper = AdvanceCollectionBalanceModelToJpaMapper(hashBuilder, referenceLineModelToJpaMapper)
    }

    @Test
    fun `should map domain model to JPA entity successfully with all properties`() {
        // Given
        val domainInvoiceLine = AdvanceCollectionInvoiceLine(
            itemType = "SERVICE",
            taxReference = AdvanceCollectionInvoiceLineTaxReference(
                taxCode = "GST",
                taxRate = "10.0",
                taxType = "PERCENTAGE",
                taxAmount = BigDecimal("15.00")
            ),
            unitPrice = BigDecimal("150.00"),
            currency = "SGD",
            description = "Professional services",
            payableItemIds = setOf("item1", "item2")
        )

        val jpaInvoiceLine = JpaAdvanceCollectionInvoiceLine(
            itemType = "SERVICE",
            taxReference = JpaAdvanceCollectionInvoiceLineTaxReference(
                taxCode = "GST",
                taxRate = "10.0",
                taxType = "PERCENTAGE",
                taxAmount = BigDecimal("15.00")
            ),
            unitPrice = BigDecimal("150.00"),
            currency = "SGD",
            description = "Professional services",
            payableItemIds = setOf("item1", "item2")
        )

        val domainModel = AdvanceCollectionBalance(
            id = 1L,
            companyId = 100L,
            entityId = 200L,
            advanceCollectionProduct = AdvanceCollectionProduct(
                lineCode = "EOR_PREMIUM",
                targetType = "COMPANY_PRODUCT",
                dimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC")
            ),
            balance = Amount(BigDecimal("1000.00"), CurrencyCode.SGD),
            metadata = AdvanceCollectionBalanceMetadata(
                invoiceNo = "INV-001",
                referenceLine = domainInvoiceLine
            )
        )

        every { hashBuilder.hash(domainModel) } returns "generated-hash"
        every { referenceLineModelToJpaMapper.map(domainInvoiceLine) } returns jpaInvoiceLine

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals(1L, result.id)
        assertEquals(100L, result.companyId)
        assertEquals(200L, result.entityId)
        assertEquals("EOR_PREMIUM", result.targetProductLineCode)
        assertEquals(mapOf("OFFERING" to "EOR", "REGION" to "APAC"), result.targetProductDimensions)
        assertEquals("COMPANY_PRODUCT", result.targetType)
        assertEquals("generated-hash", result.hash)
        assertEquals(BigDecimal("1000.00"), result.balance)
        assertEquals("SGD", result.currency)
        assertEquals("INV-001", result.invoiceNo)
        assertEquals(jpaInvoiceLine, result.invoiceLineReference)

        verify { hashBuilder.hash(domainModel) }
        verify { referenceLineModelToJpaMapper.map(domainInvoiceLine) }
    }

    @Test
    fun `should map domain model with null referenceLine`() {
        // Given
        val domainModel = AdvanceCollectionBalance(
            id = 2L,
            companyId = 101L,
            entityId = 201L,
            advanceCollectionProduct = AdvanceCollectionProduct(
                lineCode = "GP_BASIC",
                targetType = "ENTITY_PRODUCT",
                dimensions = mapOf("OFFERING" to "GP", "REGION" to "EMEA")
            ),
            balance = Amount(BigDecimal("500.00"), CurrencyCode.EUR),
            metadata = AdvanceCollectionBalanceMetadata(
                invoiceNo = "INV-002",
                referenceLine = null
            )
        )

        every { hashBuilder.hash(domainModel) } returns "generated-hash-2"
        every { referenceLineModelToJpaMapper.map(null) } returns null

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals(2L, result.id)
        assertEquals(101L, result.companyId)
        assertEquals(201L, result.entityId)
        assertEquals("GP_BASIC", result.targetProductLineCode)
        assertEquals(mapOf("OFFERING" to "GP", "REGION" to "EMEA"), result.targetProductDimensions)
        assertEquals("ENTITY_PRODUCT", result.targetType)
        assertEquals("generated-hash-2", result.hash)
        assertEquals(BigDecimal("500.00"), result.balance)
        assertEquals("EUR", result.currency)
        assertEquals("INV-002", result.invoiceNo)
        assertNull(result.invoiceLineReference)

        verify { hashBuilder.hash(domainModel) }
        verify { referenceLineModelToJpaMapper.map(null) }
    }

    @Test
    fun `should map domain model with different currency codes`() {
        // Given
        val domainModel = AdvanceCollectionBalance(
            id = 3L,
            companyId = 102L,
            entityId = 202L,
            advanceCollectionProduct = AdvanceCollectionProduct(
                lineCode = "PAYROLL_STANDARD",
                targetType = "COMPANY_PRODUCT",
                dimensions = mapOf("OFFERING" to "PAYROLL", "REGION" to "AMERICAS")
            ),
            balance = Amount(BigDecimal("750.00"), CurrencyCode.USD),
            metadata = AdvanceCollectionBalanceMetadata(
                invoiceNo = "INV-003",
                referenceLine = null
            )
        )

        every { hashBuilder.hash(domainModel) } returns "generated-hash-3"
        every { referenceLineModelToJpaMapper.map(null) } returns null

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("USD", result.currency)
        assertEquals(BigDecimal("750.00"), result.balance)
    }

    @Test
    fun `should map domain model with empty dimensions`() {
        // Given
        val domainModel = AdvanceCollectionBalance(
            id = 4L,
            companyId = 103L,
            entityId = 203L,
            advanceCollectionProduct = AdvanceCollectionProduct(
                lineCode = "BASIC_SERVICE",
                targetType = "BASIC_PRODUCT",
                dimensions = emptyMap()
            ),
            balance = Amount(BigDecimal("100.00"), CurrencyCode.GBP),
            metadata = AdvanceCollectionBalanceMetadata(
                invoiceNo = "INV-004",
                referenceLine = null
            )
        )

        every { hashBuilder.hash(domainModel) } returns "generated-hash-4"
        every { referenceLineModelToJpaMapper.map(null) } returns null

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals(emptyMap<String, String>(), result.targetProductDimensions)
        assertEquals("BASIC_SERVICE", result.targetProductLineCode)
        assertEquals("BASIC_PRODUCT", result.targetType)
    }

    @Test
    fun `should map domain model with complex dimensions`() {
        // Given
        val complexDimensions = mapOf(
            "OFFERING" to "EOR_PREMIUM",
            "REGION" to "APAC-SOUTH",
            "BILLING_CYCLE" to "MONTHLY",
            "SERVICE_TIER" to "ENTERPRISE",
            "CURRENCY_GROUP" to "SGD_GROUP"
        )

        val domainModel = AdvanceCollectionBalance(
            id = 5L,
            companyId = 104L,
            entityId = 204L,
            advanceCollectionProduct = AdvanceCollectionProduct(
                lineCode = "ENTERPRISE_EOR",
                targetType = "PREMIUM_PRODUCT",
                dimensions = complexDimensions
            ),
            balance = Amount(BigDecimal("2500.00"), CurrencyCode.SGD),
            metadata = AdvanceCollectionBalanceMetadata(
                invoiceNo = "INV-005",
                referenceLine = null
            )
        )

        every { hashBuilder.hash(domainModel) } returns "generated-hash-5"
        every { referenceLineModelToJpaMapper.map(null) } returns null

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals(complexDimensions, result.targetProductDimensions)
        assertEquals("ENTERPRISE_EOR", result.targetProductLineCode)
        assertEquals("PREMIUM_PRODUCT", result.targetType)
        assertEquals(BigDecimal("2500.00"), result.balance)
    }

    @Test
    fun `should map domain model with zero balance`() {
        // Given
        val domainModel = AdvanceCollectionBalance(
            id = 6L,
            companyId = 105L,
            entityId = 205L,
            advanceCollectionProduct = AdvanceCollectionProduct(
                lineCode = "FREE_TIER",
                targetType = "FREE_PRODUCT",
                dimensions = mapOf("OFFERING" to "FREE")
            ),
            balance = Amount(BigDecimal.ZERO, CurrencyCode.USD),
            metadata = AdvanceCollectionBalanceMetadata(
                invoiceNo = "INV-006",
                referenceLine = null
            )
        )

        every { hashBuilder.hash(domainModel) } returns "generated-hash-6"
        every { referenceLineModelToJpaMapper.map(null) } returns null

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals(BigDecimal.ZERO, result.balance)
        assertEquals("USD", result.currency)
    }

    @Test
    fun `should map domain model with null id`() {
        // Given
        val domainModel = AdvanceCollectionBalance(
            id = null,
            companyId = 107L,
            entityId = 207L,
            advanceCollectionProduct = AdvanceCollectionProduct(
                lineCode = "NEW_PRODUCT",
                targetType = "NEW_TYPE",
                dimensions = mapOf("OFFERING" to "NEW")
            ),
            balance = Amount(BigDecimal("300.00"), CurrencyCode.CAD),
            metadata = AdvanceCollectionBalanceMetadata(
                invoiceNo = "INV-007",
                referenceLine = null
            )
        )

        every { hashBuilder.hash(domainModel) } returns "generated-hash-7"
        every { referenceLineModelToJpaMapper.map(null) } returns null

        // When
        val result = mapper.map(domainModel)

        // Then
        assertNull(result.id)
        assertEquals(107L, result.companyId)
        assertEquals(207L, result.entityId)
    }
}
