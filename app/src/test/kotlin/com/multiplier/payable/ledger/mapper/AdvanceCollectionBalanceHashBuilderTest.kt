package com.multiplier.payable.ledger.mapper

import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.ledger.AdvanceCollectionBalanceMetadata
import com.multiplier.payable.ledger.domain.AdvanceCollectionBalance
import com.multiplier.payable.ledger.domain.AdvanceCollectionProduct
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals

class AdvanceCollectionBalanceHashBuilderTest {

    private lateinit var hashBuilder: AdvanceCollectionBalanceHashBuilder

    @BeforeEach
    fun setUp() {
        hashBuilder = AdvanceCollectionBalanceHashBuilder()
    }

    @Test
    fun `should generate consistent hash for same input`() {
        // Given
        val balance = createTestBalance(
            companyId = 100L,
            entityId = 200L,
            lineCode = "EOR_PREMIUM",
            dimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC"),
            targetType = "COMPANY_PRODUCT"
        )

        // When
        val hash1 = hashBuilder.hash(balance)
        val hash2 = hashBuilder.hash(balance)

        // Then
        assertEquals(hash1, hash2)
    }

    @Test
    fun `should generate different hashes for different company IDs`() {
        // Given
        val balance1 = createTestBalance(companyId = 100L)
        val balance2 = createTestBalance(companyId = 101L)

        // When
        val hash1 = hashBuilder.hash(balance1)
        val hash2 = hashBuilder.hash(balance2)

        // Then
        assertNotEquals(hash1, hash2)
    }

    @Test
    fun `should generate different hashes for different entity IDs`() {
        // Given
        val balance1 = createTestBalance(entityId = 200L)
        val balance2 = createTestBalance(entityId = 201L)

        // When
        val hash1 = hashBuilder.hash(balance1)
        val hash2 = hashBuilder.hash(balance2)

        // Then
        assertNotEquals(hash1, hash2)
    }

    @Test
    fun `should generate different hashes for different line codes`() {
        // Given
        val balance1 = createTestBalance(lineCode = "EOR_PREMIUM")
        val balance2 = createTestBalance(lineCode = "GP_BASIC")

        // When
        val hash1 = hashBuilder.hash(balance1)
        val hash2 = hashBuilder.hash(balance2)

        // Then
        assertNotEquals(hash1, hash2)
    }

    @Test
    fun `should generate different hashes for different target types`() {
        // Given
        val balance1 = createTestBalance(targetType = "COMPANY_PRODUCT")
        val balance2 = createTestBalance(targetType = "ENTITY_PRODUCT")

        // When
        val hash1 = hashBuilder.hash(balance1)
        val hash2 = hashBuilder.hash(balance2)

        // Then
        assertNotEquals(hash1, hash2)
    }

    @Test
    fun `should generate different hashes for different dimensions`() {
        // Given
        val balance1 = createTestBalance(dimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC"))
        val balance2 = createTestBalance(dimensions = mapOf("OFFERING" to "GP", "REGION" to "EMEA"))

        // When
        val hash1 = hashBuilder.hash(balance1)
        val hash2 = hashBuilder.hash(balance2)

        // Then
        assertNotEquals(hash1, hash2)
    }

    @Test
    fun `should generate same hash for dimensions in different order`() {
        // Given
        val balance1 = createTestBalance(dimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC"))
        val balance2 = createTestBalance(dimensions = mapOf("REGION" to "APAC", "OFFERING" to "EOR"))

        // When
        val hash1 = hashBuilder.hash(balance1)
        val hash2 = hashBuilder.hash(balance2)

        // Then
        assertEquals(hash1, hash2)
    }

    @Test
    fun `should generate hash for empty dimensions`() {
        // Given
        val balance = createTestBalance(dimensions = emptyMap())

        // When
        val hash = hashBuilder.hash(balance)

        // Then
        assertEquals(64, hash.length) // SHA-256 produces 64 character hex string
    }

    @Test
    fun `should generate hash for single dimension`() {
        // Given
        val balance = createTestBalance(dimensions = mapOf("OFFERING" to "EOR"))

        // When
        val hash = hashBuilder.hash(balance)

        // Then
        assertEquals(64, hash.length)
    }

    @Test
    fun `should generate hash for multiple dimensions`() {
        // Given
        val complexDimensions = mapOf(
            "OFFERING" to "EOR_PREMIUM",
            "REGION" to "APAC-SOUTH",
            "BILLING_CYCLE" to "MONTHLY",
            "SERVICE_TIER" to "ENTERPRISE",
            "CURRENCY_GROUP" to "SGD_GROUP"
        )
        val balance = createTestBalance(dimensions = complexDimensions)

        // When
        val hash = hashBuilder.hash(balance)

        // Then
        assertEquals(64, hash.length)
    }

    @Test
    fun `should handle special characters in dimensions`() {
        // Given
        val specialDimensions = mapOf(
            "OFFERING-TYPE" to "EOR_PREMIUM",
            "REGION.CODE" to "APAC-SOUTH",
            "BILLING_CYCLE" to "MONTHLY-ADVANCE",
            "SERVICE@TIER" to "ENTERPRISE#1"
        )
        val balance = createTestBalance(dimensions = specialDimensions)

        // When
        val hash = hashBuilder.hash(balance)

        // Then
        assertEquals(64, hash.length)
    }

    @Test
    fun `should generate different hashes for dimensions with different values`() {
        // Given
        val balance1 = createTestBalance(dimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC"))
        val balance2 = createTestBalance(dimensions = mapOf("OFFERING" to "EOR", "REGION" to "EMEA"))

        // When
        val hash1 = hashBuilder.hash(balance1)
        val hash2 = hashBuilder.hash(balance2)

        // Then
        assertNotEquals(hash1, hash2)
    }

    @Test
    fun `should generate different hashes for dimensions with different keys`() {
        // Given
        val balance1 = createTestBalance(dimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC"))
        val balance2 = createTestBalance(dimensions = mapOf("OFFERING" to "EOR", "LOCATION" to "APAC"))

        // When
        val hash1 = hashBuilder.hash(balance1)
        val hash2 = hashBuilder.hash(balance2)

        // Then
        assertNotEquals(hash1, hash2)
    }

    @Test
    fun `should generate hash with only hex characters`() {
        // Given
        val balance = createTestBalance()

        // When
        val hash = hashBuilder.hash(balance)

        // Then
        assertEquals(64, hash.length)
        // Verify all characters are valid hex (0-9, a-f)
        val hexPattern = Regex("^[0-9a-f]+$")
        assert(hexPattern.matches(hash)) { "Hash should contain only hex characters: $hash" }
    }

    @Test
    fun `should not include balance amount or currency in hash`() {
        // Given - Two balances with different amounts/currencies but same other properties
        val balance1 = createTestBalance(amount = BigDecimal("1000.00"), currency = CurrencyCode.SGD)
        val balance2 = createTestBalance(amount = BigDecimal("2000.00"), currency = CurrencyCode.USD)

        // When
        val hash1 = hashBuilder.hash(balance1)
        val hash2 = hashBuilder.hash(balance2)

        // Then - Hashes should be the same since balance amount/currency are not part of hash
        assertEquals(hash1, hash2)
    }

    @Test
    fun `should not include metadata in hash`() {
        // Given - Two balances with different metadata but same other properties
        val balance1 = createTestBalance(invoiceNo = "INV-001")
        val balance2 = createTestBalance(invoiceNo = "INV-002")

        // When
        val hash1 = hashBuilder.hash(balance1)
        val hash2 = hashBuilder.hash(balance2)

        // Then - Hashes should be the same since metadata is not part of hash
        assertEquals(hash1, hash2)
    }

    private fun createTestBalance(
        companyId: Long = 100L,
        entityId: Long = 200L,
        lineCode: String = "EOR_PREMIUM",
        dimensions: Map<String, String> = mapOf("OFFERING" to "EOR", "REGION" to "APAC"),
        targetType: String = "COMPANY_PRODUCT",
        amount: BigDecimal = BigDecimal("1000.00"),
        currency: CurrencyCode = CurrencyCode.SGD,
        invoiceNo: String = "INV-001"
    ): AdvanceCollectionBalance {
        return AdvanceCollectionBalance(
            id = 1L,
            companyId = companyId,
            entityId = entityId,
            advanceCollectionProduct = AdvanceCollectionProduct(
                lineCode = lineCode,
                targetType = targetType,
                dimensions = dimensions
            ),
            balance = Amount(amount, currency),
            metadata = AdvanceCollectionBalanceMetadata(
                invoiceNo = invoiceNo,
                referenceLine = null
            )
        )
    }
}
