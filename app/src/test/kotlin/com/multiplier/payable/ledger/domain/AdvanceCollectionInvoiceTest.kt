package com.multiplier.payable.ledger.domain

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.core.payable.adapters.billing.BilledItemWrapper
import com.multiplier.core.payable.adapters.pricing.ReferenceChargePolicy
import com.multiplier.core.payable.adapters.product.CompanyProductWrapper
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.engine.domain.aggregates.Duration
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoice
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class AdvanceCollectionInvoiceTest {

    @Test
    fun `hasCollectionLines should return true when lines are not empty`() {
        val invoice = AdvanceCollectionInvoice(
            transactionId = "txn123",
            invoiceNo = "INV001",
            lines = listOf(createSampleInvoiceLine()),
            collectionItems = emptyList()
        )

        assertTrue(invoice.hasCollectionLines())
    }

    @Test
    fun `hasCollectionLines should return false when lines are empty`() {
        val invoice = AdvanceCollectionInvoice(
            transactionId = "txn123",
            invoiceNo = "INV001",
            lines = emptyList(),
            collectionItems = emptyList()
        )

        assertFalse(invoice.hasCollectionLines())
    }

    @Test
    fun `findLine should return line when payableLineId exists`() {
        val payableItemId = "item123"
        val line = createSampleInvoiceLine(payableItemIds = setOf(payableItemId))
        val invoice = AdvanceCollectionInvoice(
            transactionId = "txn123",
            invoiceNo = "INV001",
            lines = listOf(line),
            collectionItems = emptyList()
        )

        val foundLine = invoice.findLine(payableItemId)

        assertNotNull(foundLine)
        assertEquals(line, foundLine)
    }

    @Test
    fun `findLine should return null when payableLineId does not exist`() {
        val line = createSampleInvoiceLine(payableItemIds = setOf("item456"))
        val invoice = AdvanceCollectionInvoice(
            transactionId = "txn123",
            invoiceNo = "INV001",
            lines = listOf(line),
            collectionItems = emptyList()
        )

        val foundLine = invoice.findLine("item123")

        assertNull(foundLine)
    }

    @Test
    fun `findLine should return null when payableItemIds is null`() {
        val line = createSampleInvoiceLine(payableItemIds = null)
        val invoice = AdvanceCollectionInvoice(
            transactionId = "txn123",
            invoiceNo = "INV001",
            lines = listOf(line),
            collectionItems = emptyList()
        )

        val foundLine = invoice.findLine("item123")

        assertNull(foundLine)
    }

    @Test
    fun `findLine should return null when payableItemIds is empty`() {
        val line = createSampleInvoiceLine(payableItemIds = emptySet())
        val invoice = AdvanceCollectionInvoice(
            transactionId = "txn123",
            invoiceNo = "INV001",
            lines = listOf(line),
            collectionItems = emptyList()
        )

        val foundLine = invoice.findLine("item123")

        assertNull(foundLine)
    }

    @Test
    fun `findLine should find line in multiple lines`() {
        val targetPayableItemId = "item456"
        val line1 = createSampleInvoiceLine(payableItemIds = setOf("item123"))
        val line2 = createSampleInvoiceLine(payableItemIds = setOf(targetPayableItemId, "item789"))
        val line3 = createSampleInvoiceLine(payableItemIds = setOf("item999"))
        
        val invoice = AdvanceCollectionInvoice(
            transactionId = "txn123",
            invoiceNo = "INV001",
            lines = listOf(line1, line2, line3),
            collectionItems = emptyList()
        )

        val foundLine = invoice.findLine(targetPayableItemId)

        assertNotNull(foundLine)
        assertEquals(line2, foundLine)
    }

    @Test
    fun `metadataOf should return metadata when matching collection item and line exist`() {
        val billId = 123L
        val payableLineItemId = UUID.randomUUID()
        val payableItem = createPayableItem(billId = billId.toString(), companyPayableLineItemIds = listOf(payableLineItemId))
        val line = createSampleInvoiceLine(payableItemIds = setOf(payableLineItemId.toString()))
        val bill = createBilledItemWrapper(billId = billId)

        val invoice = AdvanceCollectionInvoice(
            transactionId = "txn123",
            invoiceNo = "INV001",
            lines = listOf(line),
            collectionItems = listOf(payableItem)
        )

        val metadata = invoice.metadataOf(bill)

        assertNotNull(metadata)
        assertEquals("txn123", metadata.transactionId)
        assertEquals("INV001", metadata.invoiceNo)
        assertEquals(line, metadata.referenceLine)
    }

    @Test
    fun `metadataOf should return null when no matching collection item exists`() {
        val bill = createBilledItemWrapper(billId = 123L)
        val invoice = AdvanceCollectionInvoice(
            transactionId = "txn123",
            invoiceNo = "INV001",
            lines = listOf(createSampleInvoiceLine()),
            collectionItems = emptyList()
        )

        val metadata = invoice.metadataOf(bill)

        assertNull(metadata)
    }

    @Test
    fun `metadataOf should return null when no matching invoice line exists`() {
        val billId = 123L
        val payableLineItemId = UUID.randomUUID()
        val payableItem = createPayableItem(billId = billId.toString(), companyPayableLineItemIds = listOf(payableLineItemId))
        val line = createSampleInvoiceLine(payableItemIds = setOf("different_id"))
        val bill = createBilledItemWrapper(billId = billId)

        val invoice = AdvanceCollectionInvoice(
            transactionId = "txn123",
            invoiceNo = "INV001",
            lines = listOf(line),
            collectionItems = listOf(payableItem)
        )

        val metadata = invoice.metadataOf(bill)

        assertNull(metadata)
    }

    @Test
    fun `metadataOf should throw exception when payable item has multiple line item ids`() {
        val billId = 123L
        val payableItem = createPayableItem(
            billId = billId.toString(),
            companyPayableLineItemIds = listOf(UUID.randomUUID(), UUID.randomUUID()) // Multiple IDs
        )
        val bill = createBilledItemWrapper(billId = billId)

        val invoice = AdvanceCollectionInvoice(
            transactionId = "txn123",
            invoiceNo = "INV001",
            lines = listOf(createSampleInvoiceLine()),
            collectionItems = listOf(payableItem)
        )

        val exception = assertThrows<MplBusinessException> {
            invoice.metadataOf(bill)
        }

        assertTrue(exception.message.contains("Company payable item must have exactly one line item id"))
    }

    private fun createSampleInvoiceLine(payableItemIds: Set<String>? = setOf("item123")): AdvanceCollectionInvoiceLine {
        return AdvanceCollectionInvoiceLine(
            itemType = "SERVICE",
            taxReference = AdvanceCollectionInvoiceLineTaxReference(
                taxCode = "GST",
                taxRate = "10.0",
                taxType = "PERCENTAGE",
                taxAmount = BigDecimal("10.00")
            ),
            unitPrice = BigDecimal("100.00"),
            currency = "SGD",
            description = "Test service",
            payableItemIds = payableItemIds
        )
    }

    private fun createPayableItem(
        billId: String,
        companyPayableLineItemIds: List<UUID>
    ): PayableItem {
        return PayableItem(
            companyPayableLineItemIds = companyPayableLineItemIds,
            month = 1,
            year = 2024,
            lineItemType = "SERVICE",
            contractId = 100L,
            companyId = 123L,
            description = "Test service",
            amountInBaseCurrency = 100.0,
            baseCurrency = "SGD",
            billableCost = 100.0,
            originalTimestamp = System.currentTimeMillis(),
            cycle = InvoiceCycle.MONTHLY,
            countryCode = "SGP",
            billId = billId
        )
    }

    private fun createBilledItemWrapper(billId: Long): BilledItemWrapper {
        return BilledItemWrapper(
            billId = billId,
            transactionId = "txn-$billId",
            companyId = 123L,
            entityId = 456L,
            companyProduct = CompanyProductWrapper(
                startDate = LocalDateTime.of(2023, 1, 1, 0, 0),
                endDate = LocalDateTime.of(2023, 2, 1, 0, 0),
                lineCode = "TEST_LINE_CODE",
                dimensions = mapOf("key1" to "value1"),
                chargePolicy = ReferenceChargePolicy(emptyList())
            ),
            billingAmount = Amount(BigDecimal.valueOf(100.0), CurrencyCode.SGD),
            billingDuration = Duration(
                startDate = LocalDate.of(2023, 1, 1),
                endDate = LocalDate.of(2023, 2, 1)
            ),
            usageDuration = Duration(
                startDate = LocalDate.of(2023, 1, 1),
                endDate = LocalDate.of(2023, 2, 1)
            ),
            billingTime = System.currentTimeMillis(),
            referenceBills = emptyList(),
            usages = emptyList()
        )
    }
}
