package com.multiplier.payable.ledger.listener

import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.data.CompanyPayableDataProvider
import com.multiplier.payable.ledger.listener.invoice.LedgerInvoiceListenerFactory
import com.multiplier.payable.types.PayableStatus
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifyOrder
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class DefaultLedgerBalanceEventListenerTest {

    private lateinit var companyPayableDataProvider: CompanyPayableDataProvider
    private lateinit var ledgerInvoiceListenerFactory: LedgerInvoiceListenerFactory
    private lateinit var listener: DefaultLedgerBalanceEventListener
    private lateinit var mockLedgerInvoiceListener: LedgerInvoiceListener

    @BeforeEach
    fun setUp() {
        companyPayableDataProvider = mockk()
        ledgerInvoiceListenerFactory = mockk()
        mockLedgerInvoiceListener = mockk()
        listener = DefaultLedgerBalanceEventListener(
            companyPayableDataProvider,
            ledgerInvoiceListenerFactory
        )
    }

    @Test
    fun `onEvent should return early when no payables found`() {
        // Given
        val event = LedgerBalanceEvent(payableId = 1L)
        every { companyPayableDataProvider.findByPayableIds(setOf(1L), any()) } returns emptyList()

        // When
        listener.onEvent(event)

        // Then
        verify { companyPayableDataProvider.findByPayableIds(setOf(1L), any()) }
        verify(exactly = 0) { ledgerInvoiceListenerFactory.get(any()) }
    }

    @Test
    fun `onEvent should return early when transaction type not supported`() {
        // Given
        val event = LedgerBalanceEvent(payableId = 1L)
        val payable = createCompanyPayable(
            id = 1L,
            itemType = TransactionType.FIRST_INVOICE, // Not supported
            status = PayableStatus.PAID
        )
        every { companyPayableDataProvider.findByPayableIds(setOf(1L), any()) } returns listOf(payable)

        // When
        listener.onEvent(event)

        // Then
        verify { companyPayableDataProvider.findByPayableIds(setOf(1L), any()) }
        verify(exactly = 0) { ledgerInvoiceListenerFactory.get(any()) }
    }

    @Test
    fun `onEvent should call checkAndCommit for PAID status`() {
        // Given
        val event = LedgerBalanceEvent(payableId = 1L)
        val payable = createCompanyPayable(
            id = 1L,
            itemType = TransactionType.ORDER_FORM_ADVANCE,
            status = PayableStatus.PAID
        )
        every { companyPayableDataProvider.findByPayableIds(setOf(1L), any()) } returns listOf(payable)
        every { ledgerInvoiceListenerFactory.get(TransactionType.ORDER_FORM_ADVANCE) } returns mockLedgerInvoiceListener
        every { mockLedgerInvoiceListener.checkAndCommit(payable) } returns Unit

        // When
        listener.onEvent(event)

        // Then
        verifyOrder {
            companyPayableDataProvider.findByPayableIds(setOf(1L), any())
            ledgerInvoiceListenerFactory.get(TransactionType.ORDER_FORM_ADVANCE)
            mockLedgerInvoiceListener.checkAndCommit(payable)
        }
    }

    @Test
    fun `onEvent should call checkAndRollback for DELETED status`() {
        // Given
        val event = LedgerBalanceEvent(payableId = 1L)
        val payable = createCompanyPayable(
            id = 1L,
            itemType = TransactionType.SECOND_INVOICE,
            status = PayableStatus.DELETED
        )
        every { companyPayableDataProvider.findByPayableIds(setOf(1L), any()) } returns listOf(payable)
        every { ledgerInvoiceListenerFactory.get(TransactionType.SECOND_INVOICE) } returns mockLedgerInvoiceListener
        every { mockLedgerInvoiceListener.checkAndRollback(payable) } returns Unit

        // When
        listener.onEvent(event)

        // Then
        verifyOrder {
            companyPayableDataProvider.findByPayableIds(setOf(1L), any())
            ledgerInvoiceListenerFactory.get(TransactionType.SECOND_INVOICE)
            mockLedgerInvoiceListener.checkAndRollback(payable)
        }
    }

    @Test
    fun `onEvent should call checkAndRollback for VOIDED status`() {
        // Given
        val event = LedgerBalanceEvent(payableId = 1L)
        val payable = createCompanyPayable(
            id = 1L,
            itemType = TransactionType.ANNUAL_PLAN_INVOICE,
            status = PayableStatus.VOIDED
        )
        every { companyPayableDataProvider.findByPayableIds(setOf(1L), any()) } returns listOf(payable)
        every { ledgerInvoiceListenerFactory.get(TransactionType.ANNUAL_PLAN_INVOICE) } returns mockLedgerInvoiceListener
        every { mockLedgerInvoiceListener.checkAndRollback(payable) } returns Unit

        // When
        listener.onEvent(event)

        // Then
        verifyOrder {
            companyPayableDataProvider.findByPayableIds(setOf(1L), any())
            ledgerInvoiceListenerFactory.get(TransactionType.ANNUAL_PLAN_INVOICE)
            mockLedgerInvoiceListener.checkAndRollback(payable)
        }
    }

    @Test
    fun `onEvent should skip processing for unsupported status`() {
        // Given
        val event = LedgerBalanceEvent(payableId = 1L)
        val payable = createCompanyPayable(
            id = 1L,
            itemType = TransactionType.ORDER_FORM_ADVANCE,
            status = PayableStatus.DRAFT // Not in commit or rollback statuses
        )
        every { companyPayableDataProvider.findByPayableIds(setOf(1L), any()) } returns listOf(payable)

        // When
        listener.onEvent(event)

        // Then
        verify { companyPayableDataProvider.findByPayableIds(setOf(1L), any()) }
        verify(exactly = 0) { ledgerInvoiceListenerFactory.get(any()) }
    }

    @Test
    fun `onEvent should handle all supported transaction types`() {
        // Given
        val supportedTypes = LedgerInvoiceListenerFactory.SUPPORTED_TRANSACTION_TYPES
        supportedTypes.forEach { transactionType ->
            val event = LedgerBalanceEvent(payableId = 1L)
            val payable = createCompanyPayable(
                id = 1L,
                itemType = transactionType,
                status = PayableStatus.PAID
            )
            every { companyPayableDataProvider.findByPayableIds(setOf(1L), any()) } returns listOf(payable)
            every { ledgerInvoiceListenerFactory.get(transactionType) } returns mockLedgerInvoiceListener
            every { mockLedgerInvoiceListener.checkAndCommit(payable) } returns Unit

            // When
            listener.onEvent(event)

            // Then
            verify { ledgerInvoiceListenerFactory.get(transactionType) }
            verify { mockLedgerInvoiceListener.checkAndCommit(payable) }
        }
    }

    @Test
    fun `isNotSkippedPayable should return false for manual transactions`() {
        // Given
        val jpaPayable = mockk<JpaCompanyPayable>()
        every { jpaPayable.isManualTransaction } returns true
        every { jpaPayable.isLegacyTransaction } returns false

        // When
        val result = listener.javaClass.getDeclaredMethod("isNotSkippedPayable", JpaCompanyPayable::class.java)
            .apply { isAccessible = true }
            .invoke(listener, jpaPayable) as Boolean

        // Then
        assertFalse(result)
    }

    @Test
    fun `isNotSkippedPayable should return false for legacy transactions`() {
        // Given
        val jpaPayable = mockk<JpaCompanyPayable>()
        every { jpaPayable.isManualTransaction } returns false
        every { jpaPayable.isLegacyTransaction } returns true

        // When
        val result = listener.javaClass.getDeclaredMethod("isNotSkippedPayable", JpaCompanyPayable::class.java)
            .apply { isAccessible = true }
            .invoke(listener, jpaPayable) as Boolean

        // Then
        assertFalse(result)
    }

    @Test
    fun `isNotSkippedPayable should return false for both manual and legacy transactions`() {
        // Given
        val jpaPayable = mockk<JpaCompanyPayable>()
        every { jpaPayable.isManualTransaction } returns true
        every { jpaPayable.isLegacyTransaction } returns true

        // When
        val result = listener.javaClass.getDeclaredMethod("isNotSkippedPayable", JpaCompanyPayable::class.java)
            .apply { isAccessible = true }
            .invoke(listener, jpaPayable) as Boolean

        // Then
        assertFalse(result)
    }

    @Test
    fun `isNotSkippedPayable should return true for regular transactions`() {
        // Given
        val jpaPayable = mockk<JpaCompanyPayable>()
        every { jpaPayable.isManualTransaction } returns false
        every { jpaPayable.isLegacyTransaction } returns false

        // When
        val result = listener.javaClass.getDeclaredMethod("isNotSkippedPayable", JpaCompanyPayable::class.java)
            .apply { isAccessible = true }
            .invoke(listener, jpaPayable) as Boolean

        // Then
        assertTrue(result)
    }

    @Test
    fun `onEvent should use filter function when finding payables`() {
        // Given
        val event = LedgerBalanceEvent(payableId = 1L)
        every { companyPayableDataProvider.findByPayableIds(setOf(1L), any()) } returns emptyList()

        // When
        listener.onEvent(event)

        // Then
        verify { companyPayableDataProvider.findByPayableIds(setOf(1L), any<(JpaCompanyPayable) -> Boolean>()) }
    }

    private fun createCompanyPayable(
        id: Long,
        itemType: TransactionType,
        status: PayableStatus
    ): CompanyPayable {
        return CompanyPayable(
            id = id,
            companyId = 100L,
            transactionId = "TXN-001",
            items = emptyList(),
            itemType = itemType,
            status = status
        )
    }
}
