package com.multiplier.payable.ledger.mapper

import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionBalance
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionInvoiceLineTaxReference
import com.multiplier.payable.types.CurrencyCode
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertNull

class AdvanceCollectionBalanceJpaToModelMapperTest {

    private lateinit var referenceLineJpaToModelMapper: AdvanceCollectionInvoiceLineJpaToModelMapper
    private lateinit var mapper: AdvanceCollectionBalanceJpaToModelMapper

    @BeforeEach
    fun setUp() {
        referenceLineJpaToModelMapper = mockk()
        mapper = AdvanceCollectionBalanceJpaToModelMapper(referenceLineJpaToModelMapper)
    }

    @Test
    fun `should map JPA entity to domain model successfully with all properties`() {
        // Given
        val jpaTaxReference = JpaAdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("15.00")
        )

        val jpaInvoiceLine = JpaAdvanceCollectionInvoiceLine(
            itemType = "SERVICE",
            taxReference = jpaTaxReference,
            unitPrice = BigDecimal("150.00"),
            currency = "SGD",
            description = "Professional services",
            payableItemIds = setOf("item1", "item2")
        )

        val jpaEntity = JpaAdvanceCollectionBalance(
            id = 1L,
            companyId = 100L,
            entityId = 200L,
            targetProductLineCode = "EOR_PREMIUM",
            targetProductDimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC"),
            targetType = "COMPANY_PRODUCT",
            hash = "test-hash",
            balance = BigDecimal("1000.00"),
            currency = "SGD",
            invoiceNo = "INV-001",
            invoiceLineReference = jpaInvoiceLine
        )

        val domainInvoiceLine = AdvanceCollectionInvoiceLine(
            itemType = "SERVICE",
            taxReference = AdvanceCollectionInvoiceLineTaxReference(
                taxCode = "GST",
                taxRate = "10.0",
                taxType = "PERCENTAGE",
                taxAmount = BigDecimal("15.00")
            ),
            unitPrice = BigDecimal("150.00"),
            currency = "SGD",
            description = "Professional services",
            payableItemIds = setOf("item1", "item2")
        )

        every { referenceLineJpaToModelMapper.map(jpaInvoiceLine) } returns domainInvoiceLine

        // When
        val result = mapper.map(jpaEntity)

        // Then
        assertEquals(1L, result.id)
        assertEquals(100L, result.companyId)
        assertEquals(200L, result.entityId)
        
        assertEquals("EOR_PREMIUM", result.advanceCollectionProduct.lineCode)
        assertEquals("COMPANY_PRODUCT", result.advanceCollectionProduct.targetType)
        assertEquals(mapOf("OFFERING" to "EOR", "REGION" to "APAC"), result.advanceCollectionProduct.dimensions)
        
        assertEquals(BigDecimal("1000.00"), result.balance.value)
        assertEquals(CurrencyCode.SGD, result.balance.currency)
        
        assertEquals("INV-001", result.metadata.invoiceNo)
        assertEquals(domainInvoiceLine, result.metadata.referenceLine)

        verify { referenceLineJpaToModelMapper.map(jpaInvoiceLine) }
    }

    @Test
    fun `should map JPA entity with null invoiceLineReference`() {
        // Given
        val jpaEntity = JpaAdvanceCollectionBalance(
            id = 2L,
            companyId = 101L,
            entityId = 201L,
            targetProductLineCode = "GP_BASIC",
            targetProductDimensions = mapOf("OFFERING" to "GP", "REGION" to "EMEA"),
            targetType = "ENTITY_PRODUCT",
            hash = "test-hash-2",
            balance = BigDecimal("500.00"),
            currency = "EUR",
            invoiceNo = "INV-002",
            invoiceLineReference = null
        )

        // When
        val result = mapper.map(jpaEntity)

        // Then
        assertEquals(2L, result.id)
        assertEquals(101L, result.companyId)
        assertEquals(201L, result.entityId)
        
        assertEquals("GP_BASIC", result.advanceCollectionProduct.lineCode)
        assertEquals("ENTITY_PRODUCT", result.advanceCollectionProduct.targetType)
        assertEquals(mapOf("OFFERING" to "GP", "REGION" to "EMEA"), result.advanceCollectionProduct.dimensions)
        
        assertEquals(BigDecimal("500.00"), result.balance.value)
        assertEquals(CurrencyCode.EUR, result.balance.currency)
        
        assertEquals("INV-002", result.metadata.invoiceNo)
        assertNull(result.metadata.referenceLine)
    }

    @Test
    fun `should map JPA entity with different currency codes`() {
        // Given
        val jpaEntity = JpaAdvanceCollectionBalance(
            id = 3L,
            companyId = 102L,
            entityId = 202L,
            targetProductLineCode = "PAYROLL_STANDARD",
            targetProductDimensions = mapOf("OFFERING" to "PAYROLL", "REGION" to "AMERICAS"),
            targetType = "COMPANY_PRODUCT",
            hash = "test-hash-3",
            balance = BigDecimal("750.00"),
            currency = "USD",
            invoiceNo = "INV-003",
            invoiceLineReference = null
        )

        // When
        val result = mapper.map(jpaEntity)

        // Then
        assertEquals(CurrencyCode.USD, result.balance.currency)
        assertEquals(BigDecimal("750.00"), result.balance.value)
    }

    @Test
    fun `should map JPA entity with empty dimensions`() {
        // Given
        val jpaEntity = JpaAdvanceCollectionBalance(
            id = 4L,
            companyId = 103L,
            entityId = 203L,
            targetProductLineCode = "BASIC_SERVICE",
            targetProductDimensions = emptyMap(),
            targetType = "BASIC_PRODUCT",
            hash = "test-hash-4",
            balance = BigDecimal("100.00"),
            currency = "GBP",
            invoiceNo = "INV-004",
            invoiceLineReference = null
        )

        // When
        val result = mapper.map(jpaEntity)

        // Then
        assertEquals(emptyMap<String, String>(), result.advanceCollectionProduct.dimensions)
        assertEquals("BASIC_SERVICE", result.advanceCollectionProduct.lineCode)
        assertEquals("BASIC_PRODUCT", result.advanceCollectionProduct.targetType)
    }

    @Test
    fun `should map JPA entity with complex dimensions`() {
        // Given
        val complexDimensions = mapOf(
            "OFFERING" to "EOR_PREMIUM",
            "REGION" to "APAC-SOUTH",
            "BILLING_CYCLE" to "MONTHLY",
            "SERVICE_TIER" to "ENTERPRISE",
            "CURRENCY_GROUP" to "SGD_GROUP"
        )

        val jpaEntity = JpaAdvanceCollectionBalance(
            id = 5L,
            companyId = 104L,
            entityId = 204L,
            targetProductLineCode = "ENTERPRISE_EOR",
            targetProductDimensions = complexDimensions,
            targetType = "PREMIUM_PRODUCT",
            hash = "test-hash-5",
            balance = BigDecimal("2500.00"),
            currency = "SGD",
            invoiceNo = "INV-005",
            invoiceLineReference = null
        )

        // When
        val result = mapper.map(jpaEntity)

        // Then
        assertEquals(complexDimensions, result.advanceCollectionProduct.dimensions)
        assertEquals("ENTERPRISE_EOR", result.advanceCollectionProduct.lineCode)
        assertEquals("PREMIUM_PRODUCT", result.advanceCollectionProduct.targetType)
        assertEquals(BigDecimal("2500.00"), result.balance.value)
    }

    @Test
    fun `should map JPA entity with zero balance`() {
        // Given
        val jpaEntity = JpaAdvanceCollectionBalance(
            id = 6L,
            companyId = 105L,
            entityId = 205L,
            targetProductLineCode = "FREE_TIER",
            targetProductDimensions = mapOf("OFFERING" to "FREE"),
            targetType = "FREE_PRODUCT",
            hash = "test-hash-6",
            balance = BigDecimal.ZERO,
            currency = "USD",
            invoiceNo = "INV-006",
            invoiceLineReference = null
        )

        // When
        val result = mapper.map(jpaEntity)

        // Then
        assertEquals(BigDecimal.ZERO, result.balance.value)
        assertEquals(CurrencyCode.USD, result.balance.currency)
    }

    @Test
    fun `should map JPA entity with large balance amount`() {
        // Given
        val jpaEntity = JpaAdvanceCollectionBalance(
            id = 7L,
            companyId = 106L,
            entityId = 206L,
            targetProductLineCode = "ENTERPRISE_BULK",
            targetProductDimensions = mapOf("OFFERING" to "BULK", "REGION" to "GLOBAL"),
            targetType = "ENTERPRISE_PRODUCT",
            hash = "test-hash-7",
            balance = BigDecimal("999999.99"),
            currency = "EUR",
            invoiceNo = "INV-007",
            invoiceLineReference = null
        )

        // When
        val result = mapper.map(jpaEntity)

        // Then
        assertEquals(BigDecimal("999999.99"), result.balance.value)
        assertEquals(CurrencyCode.EUR, result.balance.currency)
    }
}
