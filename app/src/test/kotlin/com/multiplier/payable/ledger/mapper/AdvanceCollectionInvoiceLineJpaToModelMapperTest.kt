package com.multiplier.payable.ledger.mapper

import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionInvoiceLineTaxReference
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals

class AdvanceCollectionInvoiceLineJpaToModelMapperTest {

    private lateinit var taxReferenceJpaToModelMapper: AdvanceCollectionInvoiceLineTaxReferenceJpaToModelMapper
    private lateinit var mapper: AdvanceCollectionInvoiceLineJpaToModelMapper

    @BeforeEach
    fun setUp() {
        taxReferenceJpaToModelMapper = mockk()
        mapper = AdvanceCollectionInvoiceLineJpaToModelMapper(taxReferenceJpaToModelMapper)
    }

    @Test
    fun `should map JPA entity to domain model successfully`() {
        // Given
        val jpaTaxReference = JpaAdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("15.00")
        )

        val domainTaxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("15.00")
        )

        val jpaEntity = JpaAdvanceCollectionInvoiceLine(
            itemType = "SERVICE",
            taxReference = jpaTaxReference,
            unitPrice = BigDecimal("150.00"),
            currency = "SGD",
            description = "Professional services",
            payableItemIds = setOf("item1", "item2")
        )

        every { taxReferenceJpaToModelMapper.map(jpaTaxReference) } returns domainTaxReference

        // When
        val result = mapper.map(jpaEntity)

        // Then
        assertEquals("SERVICE", result.itemType)
        assertEquals(domainTaxReference, result.taxReference)
        assertEquals(BigDecimal("150.00"), result.unitPrice)
        assertEquals("SGD", result.currency)
        assertEquals("Professional services", result.description)
        assertEquals(setOf("item1", "item2"), result.payableItemIds)

        verify { taxReferenceJpaToModelMapper.map(jpaTaxReference) }
    }

    @Test
    fun `should map JPA entity with null payableItemIds`() {
        // Given
        val jpaTaxReference = JpaAdvanceCollectionInvoiceLineTaxReference(
            taxCode = "VAT",
            taxRate = "20.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("20.00")
        )

        val domainTaxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "VAT",
            taxRate = "20.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("20.00")
        )

        val jpaEntity = JpaAdvanceCollectionInvoiceLine(
            itemType = "PRODUCT",
            taxReference = jpaTaxReference,
            unitPrice = BigDecimal("100.00"),
            currency = "EUR",
            description = "Product sale",
            payableItemIds = null
        )

        every { taxReferenceJpaToModelMapper.map(jpaTaxReference) } returns domainTaxReference

        // When
        val result = mapper.map(jpaEntity)

        // Then
        assertEquals("PRODUCT", result.itemType)
        assertEquals(domainTaxReference, result.taxReference)
        assertEquals(BigDecimal("100.00"), result.unitPrice)
        assertEquals("EUR", result.currency)
        assertEquals("Product sale", result.description)
        assertEquals(null, result.payableItemIds)

        verify { taxReferenceJpaToModelMapper.map(jpaTaxReference) }
    }

    @Test
    fun `should map JPA entity with empty payableItemIds`() {
        // Given
        val jpaTaxReference = JpaAdvanceCollectionInvoiceLineTaxReference(
            taxCode = "HST",
            taxRate = "13.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("13.00")
        )

        val domainTaxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "HST",
            taxRate = "13.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("13.00")
        )

        val jpaEntity = JpaAdvanceCollectionInvoiceLine(
            itemType = "CONSULTATION",
            taxReference = jpaTaxReference,
            unitPrice = BigDecimal("200.00"),
            currency = "CAD",
            description = "Consultation services",
            payableItemIds = emptySet()
        )

        every { taxReferenceJpaToModelMapper.map(jpaTaxReference) } returns domainTaxReference

        // When
        val result = mapper.map(jpaEntity)

        // Then
        assertEquals("CONSULTATION", result.itemType)
        assertEquals(domainTaxReference, result.taxReference)
        assertEquals(BigDecimal("200.00"), result.unitPrice)
        assertEquals("CAD", result.currency)
        assertEquals("Consultation services", result.description)
        assertEquals(emptySet<String>(), result.payableItemIds)

        verify { taxReferenceJpaToModelMapper.map(jpaTaxReference) }
    }

    @Test
    fun `should map JPA entity with zero unit price`() {
        // Given
        val jpaTaxReference = JpaAdvanceCollectionInvoiceLineTaxReference(
            taxCode = "EXEMPT",
            taxRate = "0.0",
            taxType = "EXEMPT",
            taxAmount = BigDecimal.ZERO
        )

        val domainTaxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "EXEMPT",
            taxRate = "0.0",
            taxType = "EXEMPT",
            taxAmount = BigDecimal.ZERO
        )

        val jpaEntity = JpaAdvanceCollectionInvoiceLine(
            itemType = "FREE_SERVICE",
            taxReference = jpaTaxReference,
            unitPrice = BigDecimal.ZERO,
            currency = "USD",
            description = "Free consultation",
            payableItemIds = setOf("free_001")
        )

        every { taxReferenceJpaToModelMapper.map(jpaTaxReference) } returns domainTaxReference

        // When
        val result = mapper.map(jpaEntity)

        // Then
        assertEquals("FREE_SERVICE", result.itemType)
        assertEquals(domainTaxReference, result.taxReference)
        assertEquals(BigDecimal.ZERO, result.unitPrice)
        assertEquals("USD", result.currency)
        assertEquals("Free consultation", result.description)
        assertEquals(setOf("free_001"), result.payableItemIds)

        verify { taxReferenceJpaToModelMapper.map(jpaTaxReference) }
    }

    @Test
    fun `should map JPA entity with large payableItemIds set`() {
        // Given
        val jpaTaxReference = JpaAdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("100.00")
        )

        val domainTaxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("100.00")
        )

        val largePayableItemIds = (1..50).map { "item_$it" }.toSet()

        val jpaEntity = JpaAdvanceCollectionInvoiceLine(
            itemType = "BULK_SERVICE",
            taxReference = jpaTaxReference,
            unitPrice = BigDecimal("1000.00"),
            currency = "SGD",
            description = "Bulk services",
            payableItemIds = largePayableItemIds
        )

        every { taxReferenceJpaToModelMapper.map(jpaTaxReference) } returns domainTaxReference

        // When
        val result = mapper.map(jpaEntity)

        // Then
        assertEquals("BULK_SERVICE", result.itemType)
        assertEquals(domainTaxReference, result.taxReference)
        assertEquals(BigDecimal("1000.00"), result.unitPrice)
        assertEquals("SGD", result.currency)
        assertEquals("Bulk services", result.description)
        assertEquals(largePayableItemIds, result.payableItemIds)
        assertEquals(50, result.payableItemIds?.size)

        verify { taxReferenceJpaToModelMapper.map(jpaTaxReference) }
    }
}
