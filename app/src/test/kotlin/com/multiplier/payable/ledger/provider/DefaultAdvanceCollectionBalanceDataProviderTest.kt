package com.multiplier.payable.ledger.provider

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.ledger.AdvanceCollectionBalanceMetadata
import com.multiplier.payable.ledger.domain.AdvanceCollectionBalance
import com.multiplier.payable.ledger.domain.AdvanceCollectionProduct
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionBalance
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionBalanceRepository
import com.multiplier.payable.ledger.mapper.AdvanceCollectionBalanceJpaToModelMapper
import com.multiplier.payable.types.CurrencyCode
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class DefaultAdvanceCollectionBalanceDataProviderTest {

    private lateinit var repository: JpaAdvanceCollectionBalanceRepository
    private lateinit var mapper: AdvanceCollectionBalanceJpaToModelMapper
    private lateinit var companyPrimaryEntityProvider: CompanyPrimaryEntityProvider
    private lateinit var dataProvider: DefaultAdvanceCollectionBalanceDataProvider

    @BeforeEach
    fun setUp() {
        repository = mockk()
        mapper = mockk()
        companyPrimaryEntityProvider = mockk()
        dataProvider = DefaultAdvanceCollectionBalanceDataProvider(
            repository,
            mapper,
            companyPrimaryEntityProvider
        )
    }

    @Test
    fun `query should return mapped balances when entityId is provided`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = AdvanceCollectionProduct(
            lineCode = "EOR_PREMIUM",
            targetType = "COMPANY_PRODUCT",
            dimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC")
        )

        val jpaBalance = createJpaBalance(
            companyId = companyId,
            entityId = entityId,
            targetProductLineCode = product.lineCode,
            targetType = product.targetType,
            targetProductDimensions = product.dimensions
        )

        val domainBalance = createDomainBalance(
            companyId = companyId,
            entityId = entityId,
            advanceCollectionProduct = product
        )

        every {
            repository.query(companyId, entityId, product.lineCode, product.targetType)
        } returns listOf(jpaBalance)

        every { mapper.map(jpaBalance) } returns domainBalance

        // When
        val result = dataProvider.query(companyId, entityId, product)

        // Then
        assertEquals(1, result.size)
        assertEquals(domainBalance, result.first())
        verify { repository.query(companyId, entityId, product.lineCode, product.targetType) }
        verify { mapper.map(jpaBalance) }
    }

    @Test
    fun `query should use primary entity when entityId is null`() {
        // Given
        val companyId = 100L
        val primaryEntityId = 300L
        val product = AdvanceCollectionProduct(
            lineCode = "GP_BASIC",
            targetType = "ENTITY_PRODUCT",
            dimensions = mapOf("OFFERING" to "GP", "REGION" to "EMEA")
        )

        val jpaBalance = createJpaBalance(
            companyId = companyId,
            entityId = primaryEntityId,
            targetProductLineCode = product.lineCode,
            targetType = product.targetType,
            targetProductDimensions = product.dimensions
        )

        val domainBalance = createDomainBalance(
            companyId = companyId,
            entityId = primaryEntityId,
            advanceCollectionProduct = product
        )

        every { companyPrimaryEntityProvider.get(companyId) } returns primaryEntityId
        every {
            repository.query(companyId, primaryEntityId, product.lineCode, product.targetType)
        } returns listOf(jpaBalance)
        every { mapper.map(jpaBalance) } returns domainBalance

        // When
        val result = dataProvider.query(companyId, null, product)

        // Then
        assertEquals(1, result.size)
        assertEquals(domainBalance, result.first())
        verify { companyPrimaryEntityProvider.get(companyId) }
        verify { repository.query(companyId, primaryEntityId, product.lineCode, product.targetType) }
        verify { mapper.map(jpaBalance) }
    }

    @Test
    fun `query should filter balances by dimensions`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = AdvanceCollectionProduct(
            lineCode = "EOR_PREMIUM",
            targetType = "COMPANY_PRODUCT",
            dimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC")
        )

        val matchingJpaBalance = createJpaBalance(
            targetProductDimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC", "EXTRA" to "VALUE")
        )
        val nonMatchingJpaBalance = createJpaBalance(
            targetProductDimensions = mapOf("OFFERING" to "GP", "REGION" to "EMEA")
        )

        val matchingDomainBalance = createDomainBalance()

        every {
            repository.query(companyId, entityId, product.lineCode, product.targetType)
        } returns listOf(matchingJpaBalance, nonMatchingJpaBalance)

        every { mapper.map(matchingJpaBalance) } returns matchingDomainBalance

        // When
        val result = dataProvider.query(companyId, entityId, product)

        // Then
        assertEquals(1, result.size)
        assertEquals(matchingDomainBalance, result.first())
        verify { mapper.map(matchingJpaBalance) }
    }

    @Test
    fun `query should return empty list when no balances found`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = AdvanceCollectionProduct(
            lineCode = "EOR_PREMIUM",
            targetType = "COMPANY_PRODUCT",
            dimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC")
        )

        every {
            repository.query(companyId, entityId, product.lineCode, product.targetType)
        } returns emptyList()

        // When
        val result = dataProvider.query(companyId, entityId, product)

        // Then
        assertTrue(result.isEmpty())
        verify { repository.query(companyId, entityId, product.lineCode, product.targetType) }
    }

    @Test
    fun `query should return empty list when no balances match dimensions`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = AdvanceCollectionProduct(
            lineCode = "EOR_PREMIUM",
            targetType = "COMPANY_PRODUCT",
            dimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC")
        )

        val nonMatchingJpaBalance = createJpaBalance(
            targetProductDimensions = mapOf("OFFERING" to "GP", "REGION" to "EMEA")
        )

        every {
            repository.query(companyId, entityId, product.lineCode, product.targetType)
        } returns listOf(nonMatchingJpaBalance)

        // When
        val result = dataProvider.query(companyId, entityId, product)

        // Then
        assertTrue(result.isEmpty())
        verify { repository.query(companyId, entityId, product.lineCode, product.targetType) }
    }

    @Test
    fun `findExisting should return mapped balance when found`() {
        // Given
        val balanceId = 1L
        val jpaBalance = createJpaBalance(id = balanceId)
        val domainBalance = createDomainBalance(id = balanceId)

        every { repository.findById(balanceId) } returns Optional.of(jpaBalance)
        every { mapper.map(jpaBalance) } returns domainBalance

        // When
        val result = dataProvider.findExisting(balanceId)

        // Then
        assertEquals(domainBalance, result)
        verify { repository.findById(balanceId) }
        verify { mapper.map(jpaBalance) }
    }

    @Test
    fun `findExisting should throw exception when balance not found`() {
        // Given
        val balanceId = 999L

        every { repository.findById(balanceId) } returns Optional.empty()

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            dataProvider.findExisting(balanceId)
        }

        assertTrue(exception.message!!.contains("Balance $balanceId not found"))
        verify { repository.findById(balanceId) }
    }

    @Test
    fun `query should handle empty dimensions`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = AdvanceCollectionProduct(
            lineCode = "BASIC_SERVICE",
            targetType = "BASIC_PRODUCT",
            dimensions = emptyMap()
        )

        val jpaBalance = createJpaBalance(
            targetProductDimensions = emptyMap()
        )
        val domainBalance = createDomainBalance()

        every {
            repository.query(companyId, entityId, product.lineCode, product.targetType)
        } returns listOf(jpaBalance)
        every { mapper.map(jpaBalance) } returns domainBalance

        // When
        val result = dataProvider.query(companyId, entityId, product)

        // Then
        assertEquals(1, result.size)
        assertEquals(domainBalance, result.first())
    }

    @Test
    fun `query should handle complex dimension matching`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = AdvanceCollectionProduct(
            lineCode = "ENTERPRISE_EOR",
            targetType = "PREMIUM_PRODUCT",
            dimensions = mapOf(
                "OFFERING" to "EOR_PREMIUM",
                "REGION" to "APAC-SOUTH",
                "BILLING_CYCLE" to "MONTHLY"
            )
        )

        val exactMatchJpaBalance = createJpaBalance(
            targetProductDimensions = mapOf(
                "OFFERING" to "EOR_PREMIUM",
                "REGION" to "APAC-SOUTH",
                "BILLING_CYCLE" to "MONTHLY"
            )
        )

        val supersetJpaBalance = createJpaBalance(
            targetProductDimensions = mapOf(
                "OFFERING" to "EOR_PREMIUM",
                "REGION" to "APAC-SOUTH",
                "BILLING_CYCLE" to "MONTHLY",
                "EXTRA_DIMENSION" to "EXTRA_VALUE"
            )
        )

        val partialMatchJpaBalance = createJpaBalance(
            targetProductDimensions = mapOf(
                "OFFERING" to "EOR_PREMIUM",
                "REGION" to "APAC-SOUTH"
                // Missing BILLING_CYCLE
            )
        )

        val exactMatchDomainBalance = createDomainBalance()
        val supersetDomainBalance = createDomainBalance()

        every {
            repository.query(companyId, entityId, product.lineCode, product.targetType)
        } returns listOf(exactMatchJpaBalance, supersetJpaBalance, partialMatchJpaBalance)

        every { mapper.map(exactMatchJpaBalance) } returns exactMatchDomainBalance
        every { mapper.map(supersetJpaBalance) } returns supersetDomainBalance

        // When
        val result = dataProvider.query(companyId, entityId, product)

        // Then
        assertEquals(2, result.size) // Only exact match and superset should be included
        assertTrue(result.contains(exactMatchDomainBalance))
        assertTrue(result.contains(supersetDomainBalance))
    }

    private fun createJpaBalance(
        id: Long = 1L,
        companyId: Long = 100L,
        entityId: Long = 200L,
        targetProductLineCode: String = "EOR_PREMIUM",
        targetProductDimensions: Map<String, String> = mapOf("OFFERING" to "EOR", "REGION" to "APAC"),
        targetType: String = "COMPANY_PRODUCT"
    ): JpaAdvanceCollectionBalance {
        return JpaAdvanceCollectionBalance(
            id = id,
            companyId = companyId,
            entityId = entityId,
            targetProductLineCode = targetProductLineCode,
            targetProductDimensions = targetProductDimensions,
            targetType = targetType,
            hash = "test-hash",
            balance = BigDecimal("1000.00"),
            currency = "SGD",
            invoiceNo = "INV-001",
            invoiceLineReference = null
        )
    }

    private fun createDomainBalance(
        id: Long = 1L,
        companyId: Long = 100L,
        entityId: Long = 200L,
        advanceCollectionProduct: AdvanceCollectionProduct = AdvanceCollectionProduct(
            lineCode = "EOR_PREMIUM",
            targetType = "COMPANY_PRODUCT",
            dimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC")
        )
    ): AdvanceCollectionBalance {
        return AdvanceCollectionBalance(
            id = id,
            companyId = companyId,
            entityId = entityId,
            advanceCollectionProduct = advanceCollectionProduct,
            balance = Amount(BigDecimal("1000.00"), CurrencyCode.SGD),
            metadata = AdvanceCollectionBalanceMetadata(
                transactionId = "TXN-001",
                invoiceNo = "INV-001"
            )
        )
    }
}
