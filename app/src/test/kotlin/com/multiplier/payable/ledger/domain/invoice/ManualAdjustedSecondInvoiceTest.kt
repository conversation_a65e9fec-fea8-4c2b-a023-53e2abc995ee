package com.multiplier.payable.ledger.domain.invoice

import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.types.PayableStatus
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class ManualAdjustedSecondInvoiceTest {

    @Test
    fun `getAdjustedLineItems should return only advance collection adjustment line items`() {
        // Given
        val payable = createCompanyPayable()
        val lineItems = listOf(
            createLineItemDTO(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR),
            createLineItemDTO(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GLOBAL_PAYROLL),
            createLineItemDTO(LineItemType.MANAGEMENT_FEE_EOR), // Not an adjustment line
            createLineItemDTO(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR)
        )
        val invoice = createInvoiceDTO(lineItems)
        val manualInvoice = ManualAdjustedSecondInvoice(payable, invoice)

        // When
        val result = manualInvoice.getAdjustedLineItems()

        // Then
        assertEquals(3, result.size)
        assertTrue(result.all { it.itemType in LineItemType.getAdvanceCollectionAdjustmentLineItemTypes() })
        
        val itemTypes = result.map { it.itemType }
        assertTrue(itemTypes.contains(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR))
        assertTrue(itemTypes.contains(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GLOBAL_PAYROLL))
        assertTrue(itemTypes.contains(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR))
    }

    @Test
    fun `getAdjustedLineItems should return empty list when no adjustment line items`() {
        // Given
        val payable = createCompanyPayable()
        val lineItems = listOf(
            createLineItemDTO(LineItemType.MANAGEMENT_FEE_EOR),
            createLineItemDTO(LineItemType.EOR_SALARY_DISBURSEMENT)
        )
        val invoice = createInvoiceDTO(lineItems)
        val manualInvoice = ManualAdjustedSecondInvoice(payable, invoice)

        // When
        val result = manualInvoice.getAdjustedLineItems()

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `getAdjustedLineItems should return empty list when invoice has no line items`() {
        // Given
        val payable = createCompanyPayable()
        val invoice = createInvoiceDTO(emptyList())
        val manualInvoice = ManualAdjustedSecondInvoice(payable, invoice)

        // When
        val result = manualInvoice.getAdjustedLineItems()

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `constructor should set payable and invoice correctly`() {
        // Given
        val payable = createCompanyPayable()
        val invoice = createInvoiceDTO(emptyList())

        // When
        val manualInvoice = ManualAdjustedSecondInvoice(payable, invoice)

        // Then
        assertEquals(payable, manualInvoice.payable)
        assertEquals(invoice, manualInvoice.invoice)
    }

    @Test
    fun `should implement AdjustedSecondInvoice interface`() {
        // Given
        val payable = createCompanyPayable()
        val invoice = createInvoiceDTO(emptyList())
        val manualInvoice = ManualAdjustedSecondInvoice(payable, invoice)

        // Then
        assertTrue(manualInvoice is AdjustedSecondInvoice)
        assertEquals(payable, manualInvoice.payable)
    }

    private fun createCompanyPayable(
        id: Long = 1L,
        items: List<PayableItem> = emptyList()
    ): CompanyPayable {
        return CompanyPayable(
            id = id,
            companyId = 100L,
            transactionId = "TXN-001",
            items = items,
            itemType = TransactionType.SECOND_INVOICE,
            status = PayableStatus.DRAFT
        )
    }

    private fun createInvoiceDTO(lineItems: List<LineItemDTO>): InvoiceDTO {
        val invoice = mockk<InvoiceDTO>()
        every { invoice.lineItems } returns lineItems
        every { invoice.invoiceNo } returns "INV-001"
        every { invoice.companyId } returns 100L
        return invoice
    }

    private fun createLineItemDTO(itemType: LineItemType): LineItemDTO {
        val lineItem = mockk<LineItemDTO>()
        every { lineItem.itemType } returns itemType
        every { lineItem.description } returns "Test line item for ${itemType.name}"
        every { lineItem.quantity } returns BigDecimal.ONE
        every { lineItem.unitAmount } returns BigDecimal("100.00")
        return lineItem
    }
}
