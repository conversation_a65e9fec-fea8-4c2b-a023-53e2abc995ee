package com.multiplier.payable.ledger.mapper

import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import com.multiplier.payable.ledger.infrastructure.JpaAdvanceCollectionInvoiceLineTaxReference
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertNull

class AdvanceCollectionInvoiceLineModelToJpaMapperTest {

    private lateinit var taxReferenceModelToJpaMapper: AdvanceCollectionInvoiceLineTaxReferenceModelToJpaMapper
    private lateinit var mapper: AdvanceCollectionInvoiceLineModelToJpaMapper

    @BeforeEach
    fun setUp() {
        taxReferenceModelToJpaMapper = mockk()
        mapper = AdvanceCollectionInvoiceLineModelToJpaMapper(taxReferenceModelToJpaMapper)
    }

    @Test
    fun `should map domain model to JPA entity successfully`() {
        // Given
        val domainTaxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("15.00")
        )

        val jpaTaxReference = JpaAdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("15.00")
        )

        val domainModel = AdvanceCollectionInvoiceLine(
            itemType = "SERVICE",
            taxReference = domainTaxReference,
            unitPrice = BigDecimal("150.00"),
            currency = "SGD",
            description = "Professional services",
            payableItemIds = setOf("item1", "item2")
        )

        every { taxReferenceModelToJpaMapper.map(domainTaxReference) } returns jpaTaxReference

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("SERVICE", result!!.itemType)
        assertEquals(jpaTaxReference, result.taxReference)
        assertEquals(BigDecimal("150.00"), result.unitPrice)
        assertEquals("SGD", result.currency)
        assertEquals("Professional services", result.description)
        assertEquals(setOf("item1", "item2"), result.payableItemIds)

        verify { taxReferenceModelToJpaMapper.map(domainTaxReference) }
    }

    @Test
    fun `should return null when domain model is null`() {
        // When
        val result = mapper.map(null)

        // Then
        assertNull(result)
    }

    @Test
    fun `should map domain model with null payableItemIds`() {
        // Given
        val domainTaxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "VAT",
            taxRate = "20.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("20.00")
        )

        val jpaTaxReference = JpaAdvanceCollectionInvoiceLineTaxReference(
            taxCode = "VAT",
            taxRate = "20.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("20.00")
        )

        val domainModel = AdvanceCollectionInvoiceLine(
            itemType = "PRODUCT",
            taxReference = domainTaxReference,
            unitPrice = BigDecimal("100.00"),
            currency = "EUR",
            description = "Product sale",
            payableItemIds = null
        )

        every { taxReferenceModelToJpaMapper.map(domainTaxReference) } returns jpaTaxReference

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("PRODUCT", result!!.itemType)
        assertEquals(jpaTaxReference, result.taxReference)
        assertEquals(BigDecimal("100.00"), result.unitPrice)
        assertEquals("EUR", result.currency)
        assertEquals("Product sale", result.description)
        assertNull(result.payableItemIds)

        verify { taxReferenceModelToJpaMapper.map(domainTaxReference) }
    }

    @Test
    fun `should map domain model with empty payableItemIds`() {
        // Given
        val domainTaxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "HST",
            taxRate = "13.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("13.00")
        )

        val jpaTaxReference = JpaAdvanceCollectionInvoiceLineTaxReference(
            taxCode = "HST",
            taxRate = "13.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("13.00")
        )

        val domainModel = AdvanceCollectionInvoiceLine(
            itemType = "CONSULTATION",
            taxReference = domainTaxReference,
            unitPrice = BigDecimal("200.00"),
            currency = "CAD",
            description = "Consultation services",
            payableItemIds = emptySet()
        )

        every { taxReferenceModelToJpaMapper.map(domainTaxReference) } returns jpaTaxReference

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("CONSULTATION", result!!.itemType)
        assertEquals(jpaTaxReference, result.taxReference)
        assertEquals(BigDecimal("200.00"), result.unitPrice)
        assertEquals("CAD", result.currency)
        assertEquals("Consultation services", result.description)
        assertEquals(emptySet<String>(), result.payableItemIds)

        verify { taxReferenceModelToJpaMapper.map(domainTaxReference) }
    }

    @Test
    fun `should map domain model with zero unit price`() {
        // Given
        val domainTaxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "EXEMPT",
            taxRate = "0.0",
            taxType = "EXEMPT",
            taxAmount = BigDecimal.ZERO
        )

        val jpaTaxReference = JpaAdvanceCollectionInvoiceLineTaxReference(
            taxCode = "EXEMPT",
            taxRate = "0.0",
            taxType = "EXEMPT",
            taxAmount = BigDecimal.ZERO
        )

        val domainModel = AdvanceCollectionInvoiceLine(
            itemType = "FREE_SERVICE",
            taxReference = domainTaxReference,
            unitPrice = BigDecimal.ZERO,
            currency = "USD",
            description = "Free consultation",
            payableItemIds = setOf("free_001")
        )

        every { taxReferenceModelToJpaMapper.map(domainTaxReference) } returns jpaTaxReference

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("FREE_SERVICE", result!!.itemType)
        assertEquals(jpaTaxReference, result.taxReference)
        assertEquals(BigDecimal.ZERO, result.unitPrice)
        assertEquals("USD", result.currency)
        assertEquals("Free consultation", result.description)
        assertEquals(setOf("free_001"), result.payableItemIds)

        verify { taxReferenceModelToJpaMapper.map(domainTaxReference) }
    }

    @Test
    fun `should map domain model with large payableItemIds set`() {
        // Given
        val domainTaxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("100.00")
        )

        val jpaTaxReference = JpaAdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("100.00")
        )

        val largePayableItemIds = (1..50).map { "item_$it" }.toSet()

        val domainModel = AdvanceCollectionInvoiceLine(
            itemType = "BULK_SERVICE",
            taxReference = domainTaxReference,
            unitPrice = BigDecimal("1000.00"),
            currency = "SGD",
            description = "Bulk services",
            payableItemIds = largePayableItemIds
        )

        every { taxReferenceModelToJpaMapper.map(domainTaxReference) } returns jpaTaxReference

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("BULK_SERVICE", result!!.itemType)
        assertEquals(jpaTaxReference, result.taxReference)
        assertEquals(BigDecimal("1000.00"), result.unitPrice)
        assertEquals("SGD", result.currency)
        assertEquals("Bulk services", result.description)
        assertEquals(largePayableItemIds, result.payableItemIds)
        assertEquals(50, result.payableItemIds?.size)

        verify { taxReferenceModelToJpaMapper.map(domainTaxReference) }
    }
}
