package com.multiplier.payable.ledger.domain.invoice

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.core.payable.adapters.billing.BilledItemWrapper
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.ledger.AdvanceCollectionBalanceMetadata
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class AdvanceCollectionInvoiceTest {

    @Test
    fun `hasCollectionLines should return true when lines are not empty`() {
        // Given
        val line = mockk<AdvanceCollectionInvoiceLine>()
        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = listOf(line),
            collectionItems = emptyList()
        )

        // When
        val result = invoice.hasCollectionLines()

        // Then
        assertTrue(result)
    }

    @Test
    fun `hasCollectionLines should return false when lines are empty`() {
        // Given
        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = emptyList(),
            collectionItems = emptyList()
        )

        // When
        val result = invoice.hasCollectionLines()

        // Then
        assertFalse(result)
    }

    @Test
    fun `findLine should return line when payable line id is found`() {
        // Given
        val line1 = mockk<AdvanceCollectionInvoiceLine>()
        val line2 = mockk<AdvanceCollectionInvoiceLine>()
        every { line1.payableItemIds } returns setOf("item-1", "item-2")
        every { line2.payableItemIds } returns setOf("item-3", "item-4")

        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = listOf(line1, line2),
            collectionItems = emptyList()
        )

        // When
        val result = invoice.findLine("item-3")

        // Then
        assertEquals(line2, result)
    }

    @Test
    fun `findLine should return null when payable line id is not found`() {
        // Given
        val line1 = mockk<AdvanceCollectionInvoiceLine>()
        val line2 = mockk<AdvanceCollectionInvoiceLine>()
        every { line1.payableItemIds } returns setOf("item-1", "item-2")
        every { line2.payableItemIds } returns setOf("item-3", "item-4")

        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = listOf(line1, line2),
            collectionItems = emptyList()
        )

        // When
        val result = invoice.findLine("item-5")

        // Then
        assertNull(result)
    }

    @Test
    fun `findLine should return null when line has null payableItemIds`() {
        // Given
        val line = mockk<AdvanceCollectionInvoiceLine>()
        every { line.payableItemIds } returns null

        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = listOf(line),
            collectionItems = emptyList()
        )

        // When
        val result = invoice.findLine("item-1")

        // Then
        assertNull(result)
    }

    @Test
    fun `metadataOf should return metadata when bill and reference line are found`() {
        // Given
        val bill = mockk<BilledItemWrapper>()
        val payableItem = mockk<PayableItem>()
        val line = mockk<AdvanceCollectionInvoiceLine>()

        every { bill.billId } returns 123L
        every { payableItem.billId } returns "123"
        every { payableItem.companyPayableLineItemIds } returns listOf(java.util.UUID.fromString("00000000-0000-0000-0000-000000000456"))
        every { line.payableItemIds } returns setOf("00000000-0000-0000-0000-000000000456")

        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = listOf(line),
            collectionItems = listOf(payableItem)
        )

        // When
        val result = invoice.metadataOf(bill)

        // Then
        assertNotNull(result)
        assertEquals("TXN-001", result.transactionId)
        assertEquals("INV-001", result.invoiceNo)
        assertEquals(line, result.referenceLine)
    }

    @Test
    fun `metadataOf should return null when no matching payable item found`() {
        // Given
        val bill = mockk<BilledItemWrapper>()
        val payableItem = mockk<PayableItem>()

        every { bill.billId } returns 123L
        every { payableItem.billId } returns "456" // Different bill id

        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = emptyList(),
            collectionItems = listOf(payableItem)
        )

        // When
        val result = invoice.metadataOf(bill)

        // Then
        assertNull(result)
    }

    @Test
    fun `metadataOf should throw exception when payable item has multiple line item ids`() {
        // Given
        val bill = mockk<BilledItemWrapper>()
        val payableItem = mockk<PayableItem>()

        every { bill.billId } returns 123L
        every { payableItem.billId } returns "123"
        every { payableItem.companyPayableLineItemIds } returns listOf(java.util.UUID.fromString("00000000-0000-0000-0000-000000000456"), java.util.UUID.fromString("00000000-0000-0000-0000-000000000789")) // Multiple ids

        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = emptyList(),
            collectionItems = listOf(payableItem)
        )

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            invoice.metadataOf(bill)
        }
        assertTrue(exception.message!!.contains("Company payable item must have exactly one line item id"))
    }

    @Test
    fun `metadataOf should throw exception when payable item has no line item ids`() {
        // Given
        val bill = mockk<BilledItemWrapper>()
        val payableItem = mockk<PayableItem>()

        every { bill.billId } returns 123L
        every { payableItem.billId } returns "123"
        every { payableItem.companyPayableLineItemIds } returns emptyList() // No ids

        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = emptyList(),
            collectionItems = listOf(payableItem)
        )

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            invoice.metadataOf(bill)
        }
        assertTrue(exception.message!!.contains("Company payable item must have exactly one line item id"))
    }

    @Test
    fun `metadataOf should return null when reference line is not found`() {
        // Given
        val bill = mockk<BilledItemWrapper>()
        val payableItem = mockk<PayableItem>()
        val line = mockk<AdvanceCollectionInvoiceLine>()

        every { bill.billId } returns 123L
        every { payableItem.billId } returns "123"
        every { payableItem.companyPayableLineItemIds } returns listOf(java.util.UUID.fromString("00000000-0000-0000-0000-000000000456"))
        every { line.payableItemIds } returns setOf("00000000-0000-0000-0000-000000000789") // Different id

        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = listOf(line),
            collectionItems = listOf(payableItem)
        )

        // When
        val result = invoice.metadataOf(bill)

        // Then
        assertNull(result)
    }

    @Test
    fun `billIds should return set of bill ids from collection items`() {
        // Given
        val payableItem1 = mockk<PayableItem>()
        val payableItem2 = mockk<PayableItem>()
        val payableItem3 = mockk<PayableItem>()

        every { payableItem1.billId } returns "123"
        every { payableItem2.billId } returns "456"
        every { payableItem3.billId } returns null

        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = emptyList(),
            collectionItems = listOf(payableItem1, payableItem2, payableItem3)
        )

        // When
        val result = invoice.billIds()

        // Then
        assertEquals(setOf(123L, 456L), result)
    }

    @Test
    fun `billIds should return empty set when no collection items have bill ids`() {
        // Given
        val payableItem1 = mockk<PayableItem>()
        val payableItem2 = mockk<PayableItem>()

        every { payableItem1.billId } returns null
        every { payableItem2.billId } returns null

        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = emptyList(),
            collectionItems = listOf(payableItem1, payableItem2)
        )

        // When
        val result = invoice.billIds()

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `billIds should return empty set when collection items is empty`() {
        // Given
        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = emptyList(),
            collectionItems = emptyList()
        )

        // When
        val result = invoice.billIds()

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `data class should have correct properties`() {
        // Given
        val transactionId = "TXN-001"
        val invoiceNo = "INV-001"
        val lines = listOf(mockk<AdvanceCollectionInvoiceLine>())
        val collectionItems = listOf(mockk<PayableItem>())

        // When
        val invoice = AdvanceCollectionInvoice(
            transactionId = transactionId,
            invoiceNo = invoiceNo,
            lines = lines,
            collectionItems = collectionItems
        )

        // Then
        assertEquals(transactionId, invoice.transactionId)
        assertEquals(invoiceNo, invoice.invoiceNo)
        assertEquals(lines, invoice.lines)
        assertEquals(collectionItems, invoice.collectionItems)
    }

    @Test
    fun `metadataOf should handle payable item with null bill id`() {
        // Given
        val bill = mockk<BilledItemWrapper>()
        val payableItem = mockk<PayableItem>()

        every { bill.billId } returns 123L
        every { payableItem.billId } returns null

        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = emptyList(),
            collectionItems = listOf(payableItem)
        )

        // When
        val result = invoice.metadataOf(bill)

        // Then
        assertNull(result)
    }

    @Test
    fun `metadataOf should handle multiple collection items correctly`() {
        // Given
        val bill = mockk<BilledItemWrapper>()
        val payableItem1 = mockk<PayableItem>()
        val payableItem2 = mockk<PayableItem>()
        val line = mockk<AdvanceCollectionInvoiceLine>()

        every { bill.billId } returns 456L
        every { payableItem1.billId } returns "123"
        every { payableItem2.billId } returns "456"
        every { payableItem2.companyPayableLineItemIds } returns listOf(java.util.UUID.fromString("00000000-0000-0000-0000-000000000789"))
        every { line.payableItemIds } returns setOf("00000000-0000-0000-0000-000000000789")

        val invoice = AdvanceCollectionInvoice(
            transactionId = "TXN-001",
            invoiceNo = "INV-001",
            lines = listOf(line),
            collectionItems = listOf(payableItem1, payableItem2)
        )

        // When
        val result = invoice.metadataOf(bill)

        // Then
        assertNotNull(result)
        assertEquals("TXN-001", result.transactionId)
        assertEquals("INV-001", result.invoiceNo)
        assertEquals(line, result.referenceLine)
    }
}
