package com.multiplier.payable.ledger.domain

import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals

class AdvanceCollectionInvoiceLineTaxReferenceTest {

    @Test
    fun `should create AdvanceCollectionInvoiceLineTaxReference with all properties`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("15.00")
        )

        assertEquals("GST", taxReference.taxCode)
        assertEquals("10.0", taxReference.taxRate)
        assertEquals("PERCENTAGE", taxReference.taxType)
        assertEquals(BigDecimal("15.00"), taxReference.taxAmount)
    }

    @Test
    fun `should create tax reference with zero tax amount`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "EXEMPT",
            taxRate = "0.0",
            taxType = "EXEMPT",
            taxAmount = BigDecimal.ZERO
        )

        assertEquals("EXEMPT", taxReference.taxCode)
        assertEquals("0.0", taxReference.taxRate)
        assertEquals("EXEMPT", taxReference.taxType)
        assertEquals(BigDecimal.ZERO, taxReference.taxAmount)
    }

    @Test
    fun `should create tax reference with fixed tax type`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "FIXED_TAX",
            taxRate = "0.0",
            taxType = "FIXED",
            taxAmount = BigDecimal("25.00")
        )

        assertEquals("FIXED_TAX", taxReference.taxCode)
        assertEquals("0.0", taxReference.taxRate)
        assertEquals("FIXED", taxReference.taxType)
        assertEquals(BigDecimal("25.00"), taxReference.taxAmount)
    }

    @Test
    fun `should create tax reference with VAT`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "VAT",
            taxRate = "20.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("40.00")
        )

        assertEquals("VAT", taxReference.taxCode)
        assertEquals("20.0", taxReference.taxRate)
        assertEquals("PERCENTAGE", taxReference.taxType)
        assertEquals(BigDecimal("40.00"), taxReference.taxAmount)
    }

    @Test
    fun `should create tax reference with HST`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "HST",
            taxRate = "13.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("26.00")
        )

        assertEquals("HST", taxReference.taxCode)
        assertEquals("13.0", taxReference.taxRate)
        assertEquals("PERCENTAGE", taxReference.taxType)
        assertEquals(BigDecimal("26.00"), taxReference.taxAmount)
    }

    @Test
    fun `should handle high precision tax amounts`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "7.5",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("12.375")
        )

        assertEquals("GST", taxReference.taxCode)
        assertEquals("7.5", taxReference.taxRate)
        assertEquals("PERCENTAGE", taxReference.taxType)
        assertEquals(BigDecimal("12.375"), taxReference.taxAmount)
    }

    @Test
    fun `should handle fractional tax rates`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "SPECIAL_TAX",
            taxRate = "2.5",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("5.25")
        )

        assertEquals("SPECIAL_TAX", taxReference.taxCode)
        assertEquals("2.5", taxReference.taxRate)
        assertEquals("PERCENTAGE", taxReference.taxType)
        assertEquals(BigDecimal("5.25"), taxReference.taxAmount)
    }

    @Test
    fun `should support equality comparison`() {
        val taxReference1 = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("15.00")
        )

        val taxReference2 = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("15.00")
        )

        val taxReference3 = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "VAT",
            taxRate = "20.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("30.00")
        )

        assertEquals(taxReference1, taxReference2)
        assertNotEquals(taxReference1, taxReference3)
        assertEquals(taxReference1.hashCode(), taxReference2.hashCode())
    }

    @Test
    fun `should handle empty tax code`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "",
            taxRate = "0.0",
            taxType = "NONE",
            taxAmount = BigDecimal.ZERO
        )

        assertEquals("", taxReference.taxCode)
        assertEquals("0.0", taxReference.taxRate)
        assertEquals("NONE", taxReference.taxType)
        assertEquals(BigDecimal.ZERO, taxReference.taxAmount)
    }

    @Test
    fun `should handle negative tax amount for refunds`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST_REFUND",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("-5.00")
        )

        assertEquals("GST_REFUND", taxReference.taxCode)
        assertEquals("10.0", taxReference.taxRate)
        assertEquals("PERCENTAGE", taxReference.taxType)
        assertEquals(BigDecimal("-5.00"), taxReference.taxAmount)
    }

    @Test
    fun `should handle large tax amounts`() {
        val taxReference = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "LUXURY_TAX",
            taxRate = "50.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("999999.99")
        )

        assertEquals("LUXURY_TAX", taxReference.taxCode)
        assertEquals("50.0", taxReference.taxRate)
        assertEquals("PERCENTAGE", taxReference.taxType)
        assertEquals(BigDecimal("999999.99"), taxReference.taxAmount)
    }
}
