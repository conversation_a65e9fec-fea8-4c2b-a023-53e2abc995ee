package com.multiplier.payable.ledger.provider

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.creditnote.database.CreditNoteDto
import com.multiplier.core.payable.creditnote.database.CreditNoteService
import com.multiplier.core.payable.invoice.database.InvoiceService
import com.multiplier.core.payable.repository.model.PayableSource
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.data.CompanyPayableDataProvider
import com.multiplier.payable.ledger.domain.invoice.ManualAdjustedSecondInvoice
import com.multiplier.payable.ledger.domain.invoice.ManualAdjustedSecondInvoiceCreditNote
import com.multiplier.payable.ledger.domain.invoice.PlatformAdjustedSecondInvoice
import com.multiplier.payable.ledger.domain.invoice.PlatformAdjustedSecondInvoiceCreditNote
import com.multiplier.payable.types.PayableStatus
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class DefaultAdjustedSecondInvoiceDataProviderTest {

    private lateinit var creditNoteService: CreditNoteService
    private lateinit var invoiceService: InvoiceService
    private lateinit var companyPayableDataProvider: CompanyPayableDataProvider
    private lateinit var dataProvider: DefaultAdjustedSecondInvoiceDataProvider

    @BeforeEach
    fun setUp() {
        creditNoteService = mockk()
        invoiceService = mockk()
        companyPayableDataProvider = mockk()
        dataProvider = DefaultAdjustedSecondInvoiceDataProvider(
            creditNoteService,
            invoiceService,
            companyPayableDataProvider
        )
    }

    @Test
    fun `findExisting should throw exception when payable not found`() {
        // Given
        val payableId = 1L
        every { companyPayableDataProvider.findByPayableIds(setOf(payableId)) } returns emptyList()

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            dataProvider.findExisting(payableId)
        }
        assertTrue(exception.message!!.contains("Company payable $payableId not found"))
    }

    @Test
    fun `findExisting should return PlatformAdjustedSecondInvoice when system source and invoice exists`() {
        // Given
        val payableId = 1L
        val payable = createCompanyPayable(id = payableId, source = PayableSource.SYSTEM)
        val invoice = mockk<InvoiceDTO>()

        every { companyPayableDataProvider.findByPayableIds(setOf(payableId)) } returns listOf(payable)
        every { invoiceService.getByCompanyPayableIds(setOf(payableId)) } returns listOf(invoice)

        // When
        val result = dataProvider.findExisting(payableId)

        // Then
        assertTrue(result is PlatformAdjustedSecondInvoice)
        assertEquals(payable, result.payable)
    }

    @Test
    fun `findExisting should return ManualAdjustedSecondInvoice when netsuite source and invoice exists`() {
        // Given
        val payableId = 1L
        val payable = createCompanyPayable(id = payableId, source = PayableSource.NETSUITE)
        val invoice = mockk<InvoiceDTO>()

        every { companyPayableDataProvider.findByPayableIds(setOf(payableId)) } returns listOf(payable)
        every { invoiceService.getByCompanyPayableIds(setOf(payableId)) } returns listOf(invoice)

        // When
        val result = dataProvider.findExisting(payableId)

        // Then
        assertTrue(result is ManualAdjustedSecondInvoice)
        assertEquals(payable, result.payable)
        assertEquals(invoice, (result as ManualAdjustedSecondInvoice).invoice)
    }

    @Test
    fun `findExisting should return PlatformAdjustedSecondInvoiceCreditNote when system source and credit note exists`() {
        // Given
        val payableId = 1L
        val payable = createCompanyPayable(id = payableId, source = PayableSource.SYSTEM)
        val creditNote = mockk<CreditNoteDto>()

        every { companyPayableDataProvider.findByPayableIds(setOf(payableId)) } returns listOf(payable)
        every { invoiceService.getByCompanyPayableIds(setOf(payableId)) } returns emptyList()
        every { creditNoteService.getByCompanyPayableIds(setOf(payableId)) } returns listOf(creditNote)

        // When
        val result = dataProvider.findExisting(payableId)

        // Then
        assertTrue(result is PlatformAdjustedSecondInvoiceCreditNote)
        assertEquals(payable, result.payable)
    }

    @Test
    fun `findExisting should return ManualAdjustedSecondInvoiceCreditNote when netsuite source and credit note exists`() {
        // Given
        val payableId = 1L
        val payable = createCompanyPayable(id = payableId, source = PayableSource.NETSUITE)
        val creditNote = mockk<CreditNoteDto>()

        every { companyPayableDataProvider.findByPayableIds(setOf(payableId)) } returns listOf(payable)
        every { invoiceService.getByCompanyPayableIds(setOf(payableId)) } returns emptyList()
        every { creditNoteService.getByCompanyPayableIds(setOf(payableId)) } returns listOf(creditNote)

        // When
        val result = dataProvider.findExisting(payableId)

        // Then
        assertTrue(result is ManualAdjustedSecondInvoiceCreditNote)
        assertEquals(payable, result.payable)
        assertEquals(creditNote, (result as ManualAdjustedSecondInvoiceCreditNote).creditNote)
    }

    @Test
    fun `findExisting should throw exception when neither invoice nor credit note exists`() {
        // Given
        val payableId = 1L
        val payable = createCompanyPayable(id = payableId, source = PayableSource.SYSTEM)

        every { companyPayableDataProvider.findByPayableIds(setOf(payableId)) } returns listOf(payable)
        every { invoiceService.getByCompanyPayableIds(setOf(payableId)) } returns emptyList()
        every { creditNoteService.getByCompanyPayableIds(setOf(payableId)) } returns emptyList()

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            dataProvider.findExisting(payableId)
        }
        assertTrue(exception.message!!.contains("Invoice not found for company payable $payableId"))
    }

    @Test
    fun `findExisting should verify correct service calls`() {
        // Given
        val payableId = 1L
        val payable = createCompanyPayable(id = payableId, source = PayableSource.SYSTEM)
        val invoice = mockk<InvoiceDTO>()

        every { companyPayableDataProvider.findByPayableIds(setOf(payableId)) } returns listOf(payable)
        every { invoiceService.getByCompanyPayableIds(setOf(payableId)) } returns listOf(invoice)

        // When
        dataProvider.findExisting(payableId)

        // Then
        verify { companyPayableDataProvider.findByPayableIds(setOf(payableId)) }
        verify { invoiceService.getByCompanyPayableIds(setOf(payableId)) }
    }

    private fun createCompanyPayable(
        id: Long,
        source: PayableSource = PayableSource.SYSTEM
    ): CompanyPayable {
        return CompanyPayable(
            id = id,
            companyId = 100L,
            transactionId = "TXN-001",
            items = emptyList(),
            itemType = TransactionType.SECOND_INVOICE,
            status = PayableStatus.DRAFT,
            source = source
        )
    }
}
