package com.multiplier.payable.ledger

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.core.payable.currencyexchange.CurrencyExchangeV2Service
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.ledger.domain.AdvanceCollectionBalance
import com.multiplier.payable.ledger.domain.AdvanceCollectionEntry
import com.multiplier.payable.ledger.domain.AdvanceCollectionEntryStateMachine
import com.multiplier.payable.ledger.domain.AdvanceCollectionEntryStatus
import com.multiplier.payable.ledger.domain.AdvanceCollectionEntryTransitionEventType
import com.multiplier.payable.ledger.domain.AdvanceCollectionProduct
import com.multiplier.payable.ledger.provider.DefaultAdvanceCollectionBalanceDataProvider
import com.multiplier.payable.ledger.storage.AdvanceCollectionBalanceStorage
import com.multiplier.payable.ledger.storage.AdvanceCollectionEntryStorage
import com.multiplier.payable.lock.distributed.DistributedLock
import com.multiplier.payable.lock.distributed.DistributedLockProvider
import com.multiplier.payable.types.CurrencyCode
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.dao.DataIntegrityViolationException
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class DefaultAdvanceCollectionLedgerTest {

    private lateinit var balanceStorage: AdvanceCollectionBalanceStorage
    private lateinit var entryStorage: AdvanceCollectionEntryStorage
    private lateinit var balanceProvider: DefaultAdvanceCollectionBalanceDataProvider
    private lateinit var currencyExchangeV2Service: CurrencyExchangeV2Service
    private lateinit var stateMachine: AdvanceCollectionEntryStateMachine
    private lateinit var lockProvider: DistributedLockProvider
    private lateinit var ledger: DefaultAdvanceCollectionLedger

    @BeforeEach
    fun setUp() {
        balanceStorage = mockk<AdvanceCollectionBalanceStorage>()
        entryStorage = mockk<AdvanceCollectionEntryStorage>()
        balanceProvider = mockk<DefaultAdvanceCollectionBalanceDataProvider>()
        currencyExchangeV2Service = mockk<CurrencyExchangeV2Service>()
        stateMachine = mockk<AdvanceCollectionEntryStateMachine>()
        lockProvider = mockk<DistributedLockProvider>()
        ledger = DefaultAdvanceCollectionLedger(
            balanceStorage,
            entryStorage,
            balanceProvider,
            currencyExchangeV2Service,
            stateMachine,
            lockProvider
        )
    }

    @Test
    fun `hasBalance should return true when balances exist`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = createAdvanceCollectionProduct()
        val existingBalances = listOf(createAdvanceCollectionBalance())

        every { balanceProvider.query(companyId, entityId, product) } returns existingBalances

        // When
        val result = ledger.hasBalance(companyId, entityId, product)

        // Then
        assertTrue(result)
        verify { balanceProvider.query(companyId, entityId, product) }
    }

    @Test
    fun `hasBalance should return false when no balances exist`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = createAdvanceCollectionProduct()

        every { balanceProvider.query(companyId, entityId, product) } returns emptyList()

        // When
        val result = ledger.hasBalance(companyId, entityId, product)

        // Then
        assertFalse(result)
        verify { balanceProvider.query(companyId, entityId, product) }
    }

    @Test
    fun `hasBalance should handle null entityId`() {
        // Given
        val companyId = 100L
        val entityId: Long? = null
        val product = createAdvanceCollectionProduct()
        val existingBalances = listOf(createAdvanceCollectionBalance())

        every { balanceProvider.query(companyId, entityId, product) } returns existingBalances

        // When
        val result = ledger.hasBalance(companyId, entityId, product)

        // Then
        assertTrue(result)
        verify { balanceProvider.query(companyId, entityId, product) }
    }

    @Test
    fun `registerBalance should create balance and initial entry when no existing balance`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = createAdvanceCollectionProduct()
        val amount = Amount(BigDecimal("1000.00"), CurrencyCode.SGD)
        val metadata = AdvanceCollectionBalanceMetadata(
            transactionId = "TXN-001",
            invoiceNo = "INV-001"
        )

        val savedBalance = createAdvanceCollectionBalance(id = 1L)
        val savedEntry = createAdvanceCollectionEntry(id = 1L)

        every { balanceProvider.query(companyId, entityId, product) } returns emptyList()
        every { balanceStorage.store(any()) } returns savedBalance
        every { entryStorage.append(any()) } returns savedEntry

        // When
        ledger.registerBalance(companyId, entityId, product, amount, metadata)

        // Then
        val balanceSlot = slot<AdvanceCollectionBalance>()
        val entrySlot = slot<AdvanceCollectionEntry>()
        
        verify { balanceProvider.query(companyId, entityId, product) }
        verify { balanceStorage.store(capture(balanceSlot)) }
        verify { entryStorage.append(capture(entrySlot)) }

        // Verify balance properties
        val capturedBalance = balanceSlot.captured
        assertEquals(companyId, capturedBalance.companyId)
        assertEquals(entityId, capturedBalance.entityId)
        assertEquals(product, capturedBalance.advanceCollectionProduct)
        assertEquals(amount, capturedBalance.balance)
        assertEquals(metadata, capturedBalance.metadata)

        // Verify entry properties
        val capturedEntry = entrySlot.captured
        assertEquals(1L, capturedEntry.balanceId)
        assertEquals(amount.value, capturedEntry.amount)
        assertEquals("Initial balance", capturedEntry.note)
        assertEquals(AdvanceCollectionEntryStatus.COMMITED, capturedEntry.status)
        assertEquals("TXN-001", capturedEntry.transactionId)
    }

    @Test
    fun `registerBalance should throw exception when balance already exists`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = createAdvanceCollectionProduct()
        val amount = Amount(BigDecimal("1000.00"), CurrencyCode.SGD)
        val metadata = AdvanceCollectionBalanceMetadata(
            transactionId = "TXN-001",
            invoiceNo = "INV-001"
        )

        val existingBalances = listOf(createAdvanceCollectionBalance())

        every { balanceProvider.query(companyId, entityId, product) } returns existingBalances

        // When & Then
        try {
            ledger.registerBalance(companyId, entityId, product, amount, metadata)
        } catch (e: Exception) {
            assertTrue(e.message!!.contains("Advance collection balance exists already"))
        }

        verify { balanceProvider.query(companyId, entityId, product) }
    }

    @Test
    fun `registerBalance should handle different currencies`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = createAdvanceCollectionProduct()
        val currencies = listOf(CurrencyCode.SGD, CurrencyCode.USD, CurrencyCode.EUR)

        currencies.forEach { currency ->
            val amount = Amount(BigDecimal("1000.00"), currency)
            val metadata = AdvanceCollectionBalanceMetadata(
                transactionId = "TXN-001",
                invoiceNo = "INV-001"
            )

            val savedBalance = createAdvanceCollectionBalance(id = 1L)
            val savedEntry = createAdvanceCollectionEntry(id = 1L)

            every { balanceProvider.query(companyId, entityId, product) } returns emptyList()
            every { balanceStorage.store(any()) } returns savedBalance
            every { entryStorage.append(any()) } returns savedEntry

            // When
            ledger.registerBalance(companyId, entityId, product, amount, metadata)

            // Then
            verify { balanceStorage.store(any()) }
            verify { entryStorage.append(any()) }
        }
    }

    @Test
    fun `registerBalance should handle zero amount`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = createAdvanceCollectionProduct()
        val amount = Amount(BigDecimal.ZERO, CurrencyCode.SGD)
        val metadata = AdvanceCollectionBalanceMetadata(
            transactionId = "TXN-001",
            invoiceNo = "INV-001"
        )

        val savedBalance = createAdvanceCollectionBalance(id = 1L)
        val savedEntry = createAdvanceCollectionEntry(id = 1L)

        every { balanceProvider.query(companyId, entityId, product) } returns emptyList()
        every { balanceStorage.store(any()) } returns savedBalance
        every { entryStorage.append(any()) } returns savedEntry

        // When
        ledger.registerBalance(companyId, entityId, product, amount, metadata)

        // Then
        val entrySlot = slot<AdvanceCollectionEntry>()
        verify { entryStorage.append(capture(entrySlot)) }
        
        assertEquals(BigDecimal.ZERO, entrySlot.captured.amount)
    }

    @Test
    fun `registerBalance should handle negative amount`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = createAdvanceCollectionProduct()
        val amount = Amount(BigDecimal("-500.00"), CurrencyCode.SGD)
        val metadata = AdvanceCollectionBalanceMetadata(
            transactionId = "TXN-001",
            invoiceNo = "INV-001"
        )

        val savedBalance = createAdvanceCollectionBalance(id = 1L)
        val savedEntry = createAdvanceCollectionEntry(id = 1L)

        every { balanceProvider.query(companyId, entityId, product) } returns emptyList()
        every { balanceStorage.store(any()) } returns savedBalance
        every { entryStorage.append(any()) } returns savedEntry

        // When
        ledger.registerBalance(companyId, entityId, product, amount, metadata)

        // Then
        val entrySlot = slot<AdvanceCollectionEntry>()
        verify { entryStorage.append(capture(entrySlot)) }
        
        assertEquals(BigDecimal("-500.00"), entrySlot.captured.amount)
    }

    @Test
    fun `registerBalance should handle complex product dimensions`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val complexProduct = AdvanceCollectionProduct(
            lineCode = "ENTERPRISE_EOR",
            targetType = "PREMIUM_PRODUCT",
            dimensions = mapOf(
                "OFFERING" to "EOR_PREMIUM",
                "REGION" to "APAC-SOUTH",
                "BILLING_CYCLE" to "MONTHLY",
                "SERVICE_TIER" to "ENTERPRISE"
            )
        )
        val amount = Amount(BigDecimal("5000.00"), CurrencyCode.SGD)
        val metadata = AdvanceCollectionBalanceMetadata(
            transactionId = "TXN-001",
            invoiceNo = "INV-001"
        )

        val savedBalance = createAdvanceCollectionBalance(id = 1L)
        val savedEntry = createAdvanceCollectionEntry(id = 1L)

        every { balanceProvider.query(companyId, entityId, complexProduct) } returns emptyList()
        every { balanceStorage.store(any()) } returns savedBalance
        every { entryStorage.append(any()) } returns savedEntry

        // When
        ledger.registerBalance(companyId, entityId, complexProduct, amount, metadata)

        // Then
        val balanceSlot = slot<AdvanceCollectionBalance>()
        verify { balanceStorage.store(capture(balanceSlot)) }
        
        assertEquals(complexProduct, balanceSlot.captured.advanceCollectionProduct)
    }

    @Test
    fun `tryReserve should successfully reserve amount when balance is available`() {
        // Given
        val transactionId = "TXN-001"
        val companyId = 100L
        val entityId = 200L
        val product = createAdvanceCollectionProduct()
        val amount = Amount(BigDecimal("500.00"), CurrencyCode.SGD)

        val balance = createAdvanceCollectionBalance(id = 1L)
        val lock = mockk<DistributedLock>()
        val reservedAmount = AdvanceCollectionReservedAmount(
            reservedEntry = createAdvanceCollectionEntry(id = 1L),
            currentBalance = balance
        )

        every { balanceProvider.query(companyId, entityId, product) } returns listOf(balance)
        every { lockProvider.getLock("advance-collection-balance", "1") } returns lock
        every { lock.tryLock(any()) } returns true
        every { lock.unlock() } returns Unit
        every { balance.reserve(transactionId, amount) } returns reservedAmount
        every { balanceStorage.store(any()) } returns balance
        every { entryStorage.append(any()) } returns createAdvanceCollectionEntry(id = 1L)

        // When
        val result = ledger.tryReserve(transactionId, companyId, entityId, product, amount)

        // Then
        assertTrue(result.isReserved())
        verify { balanceProvider.query(companyId, entityId, product) }
        verify { lockProvider.getLock("advance-collection-balance", "1") }
        verify { lock.tryLock(any()) }
        verify { balance.reserve(transactionId, amount) }
        verify { balanceStorage.store(any()) }
        verify { entryStorage.append(any()) }
        verify { lock.unlock() }
    }

    @Test
    fun `tryReserve should throw exception when no balance found`() {
        // Given
        val transactionId = "TXN-001"
        val companyId = 100L
        val entityId = 200L
        val product = createAdvanceCollectionProduct()
        val amount = Amount(BigDecimal("500.00"), CurrencyCode.SGD)

        every { balanceProvider.query(companyId, entityId, product) } returns emptyList()

        // When & Then
        assertThrows<MplBusinessException> {
            ledger.tryReserve(transactionId, companyId, entityId, product, amount)
        }

        verify { balanceProvider.query(companyId, entityId, product) }
    }

    @Test
    fun `tryReserve should throw exception when multiple balances found`() {
        // Given
        val transactionId = "TXN-001"
        val companyId = 100L
        val entityId = 200L
        val product = createAdvanceCollectionProduct()
        val amount = Amount(BigDecimal("500.00"), CurrencyCode.SGD)

        val balances = listOf(
            createAdvanceCollectionBalance(id = 1L),
            createAdvanceCollectionBalance(id = 2L)
        )

        every { balanceProvider.query(companyId, entityId, product) } returns balances

        // When & Then
        assertThrows<MplBusinessException> {
            ledger.tryReserve(transactionId, companyId, entityId, product, amount)
        }

        verify { balanceProvider.query(companyId, entityId, product) }
    }

    @Test
    fun `tryReserve should throw exception when lock cannot be acquired`() {
        // Given
        val transactionId = "TXN-001"
        val companyId = 100L
        val entityId = 200L
        val product = createAdvanceCollectionProduct()
        val amount = Amount(BigDecimal("500.00"), CurrencyCode.SGD)

        val balance = createAdvanceCollectionBalance(id = 1L)
        val lock = mockk<DistributedLock>()

        every { balanceProvider.query(companyId, entityId, product) } returns listOf(balance)
        every { lockProvider.getLock("advance-collection-balance", "1") } returns lock
        every { lock.tryLock(any()) } returns false

        // When & Then
        assertThrows<MplBusinessException> {
            ledger.tryReserve(transactionId, companyId, entityId, product, amount)
        }

        verify { balanceProvider.query(companyId, entityId, product) }
        verify { lockProvider.getLock("advance-collection-balance", "1") }
        verify { lock.tryLock(any()) }
    }

    @Test
    fun `tryReserve should handle currency conversion when currencies differ`() {
        // Given
        val transactionId = "TXN-001"
        val companyId = 100L
        val entityId = 200L
        val product = createAdvanceCollectionProduct()
        val amount = Amount(BigDecimal("500.00"), CurrencyCode.USD)

        val balance = createAdvanceCollectionBalance(
            id = 1L,
            amount = BigDecimal("1000.00"),
            currency = CurrencyCode.SGD
        )
        val lock = mockk<DistributedLock>()
        val convertedAmount = Amount(BigDecimal("675.00"), CurrencyCode.SGD)
        val reservedAmount = AdvanceCollectionReservedAmount(
            reservedEntry = createAdvanceCollectionEntry(id = 1L),
            currentBalance = balance
        )

        every { balanceProvider.query(companyId, entityId, product) } returns listOf(balance)
        every { lockProvider.getLock("advance-collection-balance", "1") } returns lock
        every { lock.tryLock(any()) } returns true
        every { lock.unlock() } returns Unit
        every { currencyExchangeV2Service.exchange(
            BigDecimal("500.00"),
            CurrencyCode.USD,
            CurrencyCode.SGD,
            companyId
        ) } returns BigDecimal("675.00")
        every { balance.reserve(transactionId, convertedAmount) } returns reservedAmount
        every { balanceStorage.store(any()) } returns balance
        every { entryStorage.append(any()) } returns createAdvanceCollectionEntry(id = 1L)

        // When
        val result = ledger.tryReserve(transactionId, companyId, entityId, product, amount)

        // Then
        assertTrue(result.isReserved())
        verify { currencyExchangeV2Service.exchange(
            BigDecimal("500.00"),
            CurrencyCode.USD,
            CurrencyCode.SGD,
            companyId
        ) }
        verify { balance.reserve(transactionId, convertedAmount) }
        verify { lock.unlock() }
    }

    private fun createAdvanceCollectionProduct(): AdvanceCollectionProduct {
        return AdvanceCollectionProduct(
            lineCode = "EOR_PREMIUM",
            targetType = "COMPANY_PRODUCT",
            dimensions = mapOf("OFFERING" to "EOR", "REGION" to "APAC")
        )
    }

    @Test
    fun `commit should update entry status when valid transition`() {
        // Given
        val entry = createAdvanceCollectionEntry(
            id = 1L,
            transactionId = "TXN-001",
            balanceId = 100L,
            status = AdvanceCollectionEntryStatus.RESERVED
        )
        val updatedEntry = entry.copy(status = AdvanceCollectionEntryStatus.COMMITED)

        every { stateMachine.isValidTransition(
            AdvanceCollectionEntryStatus.RESERVED,
            AdvanceCollectionEntryTransitionEventType.COMMIT
        ) } returns true
        every { stateMachine.isValidTransition(
            AdvanceCollectionEntryStatus.COMMITED,
            AdvanceCollectionEntryTransitionEventType.COMMIT
        ) } returns false
        every { stateMachine.next(
            AdvanceCollectionEntryStatus.RESERVED,
            AdvanceCollectionEntryTransitionEventType.COMMIT
        ) } returns AdvanceCollectionEntryStatus.COMMITED
        every { entryStorage.store(updatedEntry) } returns updatedEntry

        // When
        ledger.commit(entry)

        // Then
        verify { stateMachine.isValidTransition(
            AdvanceCollectionEntryStatus.RESERVED,
            AdvanceCollectionEntryTransitionEventType.COMMIT
        ) }
        verify { stateMachine.next(
            AdvanceCollectionEntryStatus.RESERVED,
            AdvanceCollectionEntryTransitionEventType.COMMIT
        ) }
        verify { entryStorage.store(updatedEntry) }
    }

    @Test
    fun `commit should skip when invalid transition`() {
        // Given
        val entry = createAdvanceCollectionEntry(
            id = 1L,
            transactionId = "TXN-001",
            balanceId = 100L
        )

        every { stateMachine.isValidTransition(
            AdvanceCollectionEntryStatus.COMMITED,
            AdvanceCollectionEntryTransitionEventType.COMMIT
        ) } returns false

        // When
        ledger.commit(entry)

        // Then
        verify { stateMachine.isValidTransition(
            AdvanceCollectionEntryStatus.COMMITED,
            AdvanceCollectionEntryTransitionEventType.COMMIT
        ) }
        verify(exactly = 0) { entryStorage.store(any()) }
    }

    @Test
    fun `rollBack should reverse entry and update balance`() {
        // Given
        val entry = createAdvanceCollectionEntry(
            id = 1L,
            balanceId = 100L,
            amount = BigDecimal("500.00"),
            status = AdvanceCollectionEntryStatus.RESERVED
        )
        val balance = createAdvanceCollectionBalance(id = 100L)
        val lock = mockk<DistributedLock>()
        val updatedBalance = balance.copy(
            balance = Amount(BigDecimal("1500.00"), CurrencyCode.SGD)
        )
        val rolledBackEntry = entry.copy(status = AdvanceCollectionEntryStatus.ROLLED_BACK)
        val reversalEntry = createAdvanceCollectionEntry(
            balanceId = 100L,
            amount = BigDecimal("-500.00"),
            status = AdvanceCollectionEntryStatus.COMMITED
        )

        every { lockProvider.getLock("advance-collection-balance", "100") } returns lock
        every { lock.tryLock(any()) } returns true
        every { lock.unlock() } returns Unit
        every { balanceProvider.findExisting(100L) } returns balance
        every { balance.add(BigDecimal("-500.00")) } returns updatedBalance
        every { balanceStorage.store(updatedBalance) } returns updatedBalance
        every { stateMachine.next(
            AdvanceCollectionEntryStatus.RESERVED,
            AdvanceCollectionEntryTransitionEventType.ROLLBACK
        ) } returns AdvanceCollectionEntryStatus.ROLLED_BACK
        every { entryStorage.store(rolledBackEntry) } returns rolledBackEntry
        every { entryStorage.append(any()) } returns reversalEntry

        // When
        ledger.rollBack(entry)

        // Then
        verify { lockProvider.getLock("advance-collection-balance", "100") }
        verify { lock.tryLock(any()) }
        verify { balanceProvider.findExisting(100L) }
        verify { balance.add(BigDecimal("-500.00")) }
        verify { balanceStorage.store(updatedBalance) }
        verify { entryStorage.store(rolledBackEntry) }
        verify { entryStorage.append(any()) }
        verify { lock.unlock() }
    }

    @Test
    fun `rollBack should throw exception when lock cannot be acquired`() {
        // Given
        val entry = createAdvanceCollectionEntry(
            id = 1L,
            balanceId = 100L
        )
        val lock = mockk<DistributedLock>()

        every { lockProvider.getLock("advance-collection-balance", "100") } returns lock
        every { lock.tryLock(any()) } returns false

        // When & Then
        assertThrows<MplBusinessException> {
            ledger.rollBack(entry)
        }

        verify { lockProvider.getLock("advance-collection-balance", "100") }
        verify { lock.tryLock(any()) }
    }

    @Test
    fun `registerBalance should handle DataIntegrityViolationException gracefully`() {
        // Given
        val companyId = 100L
        val entityId = 200L
        val product = createAdvanceCollectionProduct()
        val amount = Amount(BigDecimal("1000.00"), CurrencyCode.SGD)
        val metadata = AdvanceCollectionBalanceMetadata(
            transactionId = "TXN-001",
            invoiceNo = "INV-001"
        )

        every { balanceProvider.query(companyId, entityId, product) } returns emptyList()
        every { balanceStorage.store(any()) } throws DataIntegrityViolationException("Duplicate key")

        // When
        ledger.registerBalance(companyId, entityId, product, amount, metadata)

        // Then
        verify { balanceProvider.query(companyId, entityId, product) }
        verify { balanceStorage.store(any()) }
        // Should not throw exception, just log warning
    }

    private fun createAdvanceCollectionBalance(
        id: Long? = null,
        companyId: Long = 100L,
        entityId: Long = 200L,
        amount: BigDecimal = BigDecimal("1000.00"),
        currency: CurrencyCode = CurrencyCode.SGD
    ): AdvanceCollectionBalance {
        return mockk<AdvanceCollectionBalance> {
            every { <EMAIL> } returns id
            every { <EMAIL> } returns companyId
            every { <EMAIL> } returns entityId
            every { <EMAIL> } returns createAdvanceCollectionProduct()
            every { <EMAIL> } returns Amount(amount, currency)
            every { <EMAIL> } returns AdvanceCollectionBalanceMetadata(
                transactionId = "TXN-001",
                invoiceNo = "INV-001"
            )
            every { <EMAIL>(any(), any()) } returns AdvanceCollectionReservedAmount(
                reservedEntry = createAdvanceCollectionEntry(),
                currentBalance = this@mockk
            )
            every { <EMAIL>(any()) } returns this@mockk
            every { <EMAIL>(any(), any(), any(), any(), any()) } returns this@mockk
        }
    }

    private fun createAdvanceCollectionEntry(
        id: Long? = null,
        balanceId: Long = 1L,
        transactionId: String = "TXN-001",
        amount: BigDecimal = BigDecimal("1000.00"),
        status: AdvanceCollectionEntryStatus = AdvanceCollectionEntryStatus.COMMITED
    ): AdvanceCollectionEntry {
        return AdvanceCollectionEntry(
            id = id,
            transactionId = transactionId,
            balanceId = balanceId,
            amount = amount,
            note = "Test entry",
            references = mapOf("test" to "value"),
            status = status
        )
    }
}
