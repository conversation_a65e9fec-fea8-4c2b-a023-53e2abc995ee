package com.multiplier.payable.ledger.mapper

import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals

class AdvanceCollectionInvoiceLineTaxReferenceModelToJpaMapperTest {

    private lateinit var mapper: AdvanceCollectionInvoiceLineTaxReferenceModelToJpaMapper

    @BeforeEach
    fun setUp() {
        mapper = AdvanceCollectionInvoiceLineTaxReferenceModelToJpaMapper()
    }

    @Test
    fun `should map domain tax reference to JPA entity successfully`() {
        // Given
        val domainModel = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("15.00")
        )

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("GST", result.taxCode)
        assertEquals("10.0", result.taxRate)
        assertEquals("PERCENTAGE", result.taxType)
        assertEquals(BigDecimal("15.00"), result.taxAmount)
    }

    @Test
    fun `should map domain tax reference with zero tax amount`() {
        // Given
        val domainModel = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "EXEMPT",
            taxRate = "0.0",
            taxType = "EXEMPT",
            taxAmount = BigDecimal.ZERO
        )

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("EXEMPT", result.taxCode)
        assertEquals("0.0", result.taxRate)
        assertEquals("EXEMPT", result.taxType)
        assertEquals(BigDecimal.ZERO, result.taxAmount)
    }

    @Test
    fun `should map domain tax reference with fixed tax type`() {
        // Given
        val domainModel = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "FIXED_TAX",
            taxRate = "0.0",
            taxType = "FIXED",
            taxAmount = BigDecimal("25.00")
        )

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("FIXED_TAX", result.taxCode)
        assertEquals("0.0", result.taxRate)
        assertEquals("FIXED", result.taxType)
        assertEquals(BigDecimal("25.00"), result.taxAmount)
    }

    @Test
    fun `should map domain tax reference with VAT`() {
        // Given
        val domainModel = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "VAT",
            taxRate = "20.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("40.00")
        )

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("VAT", result.taxCode)
        assertEquals("20.0", result.taxRate)
        assertEquals("PERCENTAGE", result.taxType)
        assertEquals(BigDecimal("40.00"), result.taxAmount)
    }

    @Test
    fun `should map domain tax reference with HST`() {
        // Given
        val domainModel = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "HST",
            taxRate = "13.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("26.00")
        )

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("HST", result.taxCode)
        assertEquals("13.0", result.taxRate)
        assertEquals("PERCENTAGE", result.taxType)
        assertEquals(BigDecimal("26.00"), result.taxAmount)
    }

    @Test
    fun `should map domain tax reference with high precision tax amounts`() {
        // Given
        val domainModel = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST",
            taxRate = "7.5",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("12.375")
        )

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("GST", result.taxCode)
        assertEquals("7.5", result.taxRate)
        assertEquals("PERCENTAGE", result.taxType)
        assertEquals(BigDecimal("12.375"), result.taxAmount)
    }

    @Test
    fun `should map domain tax reference with fractional tax rates`() {
        // Given
        val domainModel = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "SPECIAL_TAX",
            taxRate = "2.5",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("5.25")
        )

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("SPECIAL_TAX", result.taxCode)
        assertEquals("2.5", result.taxRate)
        assertEquals("PERCENTAGE", result.taxType)
        assertEquals(BigDecimal("5.25"), result.taxAmount)
    }

    @Test
    fun `should map domain tax reference with empty tax code`() {
        // Given
        val domainModel = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "",
            taxRate = "0.0",
            taxType = "NONE",
            taxAmount = BigDecimal.ZERO
        )

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("", result.taxCode)
        assertEquals("0.0", result.taxRate)
        assertEquals("NONE", result.taxType)
        assertEquals(BigDecimal.ZERO, result.taxAmount)
    }

    @Test
    fun `should map domain tax reference with negative tax amount for refunds`() {
        // Given
        val domainModel = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "GST_REFUND",
            taxRate = "10.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("-5.00")
        )

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("GST_REFUND", result.taxCode)
        assertEquals("10.0", result.taxRate)
        assertEquals("PERCENTAGE", result.taxType)
        assertEquals(BigDecimal("-5.00"), result.taxAmount)
    }

    @Test
    fun `should map domain tax reference with large tax amounts`() {
        // Given
        val domainModel = AdvanceCollectionInvoiceLineTaxReference(
            taxCode = "LUXURY_TAX",
            taxRate = "50.0",
            taxType = "PERCENTAGE",
            taxAmount = BigDecimal("999999.99")
        )

        // When
        val result = mapper.map(domainModel)

        // Then
        assertEquals("LUXURY_TAX", result.taxCode)
        assertEquals("50.0", result.taxRate)
        assertEquals("PERCENTAGE", result.taxType)
        assertEquals(BigDecimal("999999.99"), result.taxAmount)
    }
}
