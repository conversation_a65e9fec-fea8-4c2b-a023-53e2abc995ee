package com.multiplier.payable.ledger.listener.invoice

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.ledger.AdvanceCollectionLedger
import com.multiplier.payable.ledger.provider.AdvanceCollectionEntryDataProvider
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class LedgerStandaloneAnnualPlanAORInvoiceListenerTest {

    private lateinit var ledger: AdvanceCollectionLedger
    private lateinit var advanceCollectionEntryDataProvider: AdvanceCollectionEntryDataProvider
    private lateinit var entryDataProvider: AdvanceCollectionEntryDataProvider
    private lateinit var listener: LedgerStandaloneAnnualPlanAORInvoiceListener

    @BeforeEach
    fun setUp() {
        ledger = mockk()
        advanceCollectionEntryDataProvider = mockk()
        entryDataProvider = mockk()
        listener = LedgerStandaloneAnnualPlanAORInvoiceListener(
            ledger,
            advanceCollectionEntryDataProvider,
            entryDataProvider
        )
    }

    @Test
    fun `transactionType should return ANNUAL_PLAN_AOR_INVOICE`() {
        // When
        val result = listener.transactionType()

        // Then
        assertEquals(TransactionType.ANNUAL_PLAN_AOR_INVOICE, result)
    }

    @Test
    fun `listener should implement LedgerStandaloneInvoiceListener`() {
        // Then
        assert(listener is LedgerStandaloneInvoiceListener)
    }

    @Test
    fun `listener should have correct dependencies injected`() {
        // Then
        assertEquals(ledger, listener.ledger)
        assertEquals(advanceCollectionEntryDataProvider, listener.advanceCollectionEntryDataProvider)
        assertEquals(entryDataProvider, listener.entryDataProvider)
    }
}
