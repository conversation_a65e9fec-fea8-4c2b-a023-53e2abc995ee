package com.multiplier.payable.ledger.listener.invoice

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.core.payable.adapters.BillingServiceAdapter
import com.multiplier.core.payable.adapters.billing.BilledItemWrapper
import com.multiplier.core.payable.adapters.pricing.ReferenceChargePolicy
import com.multiplier.core.payable.adapters.pricing.TargetReference
import com.multiplier.core.payable.adapters.pricing.ReferenceTargetType
import com.multiplier.core.payable.adapters.product.CompanyProductWrapper
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.ledger.AdvanceCollectionBalanceMetadata
import com.multiplier.payable.ledger.AdvanceCollectionLedger
import com.multiplier.payable.ledger.domain.AdvanceCollectionProduct
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoice
import com.multiplier.payable.ledger.provider.AdvanceCollectionInvoiceDataProvider
import com.multiplier.payable.ledger.provider.CompanyPrimaryEntityProvider
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.types.CurrencyCode
import java.time.LocalDateTime
import com.multiplier.payable.types.PayableStatus
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifyOrder
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import java.math.BigDecimal

class LedgerAdvanceCollectionInvoiceListenerTest {

    private lateinit var ledger: AdvanceCollectionLedger
    private lateinit var billingServiceAdapter: BillingServiceAdapter
    private lateinit var advanceCollectionInvoiceDataProvider: AdvanceCollectionInvoiceDataProvider
    private lateinit var companyPrimaryEntityProvider: CompanyPrimaryEntityProvider
    private lateinit var listener: LedgerAdvanceCollectionInvoiceListener

    @BeforeEach
    fun setUp() {
        ledger = mockk()
        billingServiceAdapter = mockk()
        advanceCollectionInvoiceDataProvider = mockk()
        companyPrimaryEntityProvider = mockk()
        listener = LedgerAdvanceCollectionInvoiceListener(
            ledger,
            billingServiceAdapter,
            advanceCollectionInvoiceDataProvider,
            companyPrimaryEntityProvider
        )
    }

    @Test
    fun `onCommit should return early when invoice has no collection lines`() {
        // Given
        val payable = createCompanyPayable()
        val invoice = mockk<AdvanceCollectionInvoice>()
        every { advanceCollectionInvoiceDataProvider.findExistingInvoice(payable) } returns invoice
        every { invoice.hasCollectionLines() } returns false

        // When
        listener.onCommit(payable)

        // Then
        verify { advanceCollectionInvoiceDataProvider.findExistingInvoice(payable) }
        verify { invoice.hasCollectionLines() }
        verify(exactly = 0) { billingServiceAdapter.getBillsByIds(any()) }
    }

    @Test
    fun `onCommit should process bills and register balances successfully`() {
        // Given
        val payable = createCompanyPayable()
        val invoice = mockk<AdvanceCollectionInvoice>()
        val billIds = setOf(1L, 2L)
        val billedItem1 = createBilledItemWrapper(1L, 100L, "EOR_SALARY")
        val billedItem2 = createBilledItemWrapper(2L, 200L, "MANAGEMENT_FEE")
        val billedItems = listOf(billedItem1, billedItem2)
        val balanceMetadata1 = createBalanceMetadata("TXN-001", "INV-001")
        val balanceMetadata2 = createBalanceMetadata("TXN-002", "INV-002")

        every { advanceCollectionInvoiceDataProvider.findExistingInvoice(payable) } returns invoice
        every { invoice.hasCollectionLines() } returns true
        every { invoice.billIds() } returns billIds
        every { billingServiceAdapter.getBillsByIds(billIds) } returns billedItems
        every { companyPrimaryEntityProvider.get(100L) } returns 1001L
        every { companyPrimaryEntityProvider.get(200L) } returns 2001L
        every { invoice.metadataOf(billedItem1) } returns balanceMetadata1
        every { invoice.metadataOf(billedItem2) } returns balanceMetadata2
        every { ledger.registerBalance(any(), any(), any(), any(), any()) } returns Unit

        // When
        listener.onCommit(payable)

        // Then
        verifyOrder {
            advanceCollectionInvoiceDataProvider.findExistingInvoice(payable)
            invoice.hasCollectionLines()
            invoice.billIds()
            billingServiceAdapter.getBillsByIds(billIds)
            companyPrimaryEntityProvider.get(100L)
            companyPrimaryEntityProvider.get(200L)
            invoice.metadataOf(billedItem1)
            invoice.metadataOf(billedItem2)
        }
        verify(exactly = 2) { ledger.registerBalance(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `onCommit should skip bills with null metadata`() {
        // Given
        val payable = createCompanyPayable()
        val invoice = mockk<AdvanceCollectionInvoice>()
        val billIds = setOf(1L)
        val billedItem = createBilledItemWrapper(1L, 100L, "EOR_SALARY")
        val billedItems = listOf(billedItem)

        every { advanceCollectionInvoiceDataProvider.findExistingInvoice(payable) } returns invoice
        every { invoice.hasCollectionLines() } returns true
        every { invoice.billIds() } returns billIds
        every { billingServiceAdapter.getBillsByIds(billIds) } returns billedItems
        every { companyPrimaryEntityProvider.get(100L) } returns 1001L
        every { invoice.metadataOf(billedItem) } returns null

        // When
        listener.onCommit(payable)

        // Then
        verify { invoice.metadataOf(billedItem) }
        verify(exactly = 0) { ledger.registerBalance(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `onCommit should throw exception when charge policy is not ReferenceChargePolicy`() {
        // Given
        val payable = createCompanyPayable()
        val invoice = mockk<AdvanceCollectionInvoice>()
        val billIds = setOf(1L)
        val billedItem = createBilledItemWrapperWithInvalidChargePolicy(1L, 100L)
        val billedItems = listOf(billedItem)
        val balanceMetadata = createBalanceMetadata("TXN-001", "INV-001")

        every { advanceCollectionInvoiceDataProvider.findExistingInvoice(payable) } returns invoice
        every { invoice.hasCollectionLines() } returns true
        every { invoice.billIds() } returns billIds
        every { billingServiceAdapter.getBillsByIds(billIds) } returns billedItems
        every { companyPrimaryEntityProvider.get(100L) } returns 1001L
        every { invoice.metadataOf(billedItem) } returns balanceMetadata

        // When & Then
        assertThrows<IllegalArgumentException> {
            listener.onCommit(payable)
        }
    }

    @Test
    fun `onCommit should skip reference bills with null target`() {
        // Given
        val payable = createCompanyPayable()
        val invoice = mockk<AdvanceCollectionInvoice>()
        val billIds = setOf(1L)
        val billedItem = createBilledItemWrapperWithNullTarget(1L, 100L, "EOR_SALARY")
        val billedItems = listOf(billedItem)
        val balanceMetadata = createBalanceMetadata("TXN-001", "INV-001")

        every { advanceCollectionInvoiceDataProvider.findExistingInvoice(payable) } returns invoice
        every { invoice.hasCollectionLines() } returns true
        every { invoice.billIds() } returns billIds
        every { billingServiceAdapter.getBillsByIds(billIds) } returns billedItems
        every { companyPrimaryEntityProvider.get(100L) } returns 1001L
        every { invoice.metadataOf(billedItem) } returns balanceMetadata

        // When
        listener.onCommit(payable)

        // Then
        verify(exactly = 0) { ledger.registerBalance(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `onCommit should throw exception when primary entity is null`() {
        // Given
        val payable = createCompanyPayable()
        val invoice = mockk<AdvanceCollectionInvoice>()
        val billIds = setOf(1L)
        val billedItem = createBilledItemWrapper(1L, 100L, "EOR_SALARY")
        val billedItems = listOf(billedItem)
        val balanceMetadata = createBalanceMetadata("TXN-001", "INV-001")

        every { advanceCollectionInvoiceDataProvider.findExistingInvoice(payable) } returns invoice
        every { invoice.hasCollectionLines() } returns true
        every { invoice.billIds() } returns billIds
        every { billingServiceAdapter.getBillsByIds(billIds) } returns billedItems
        every { companyPrimaryEntityProvider.get(100L) } returns null
        every { invoice.metadataOf(billedItem) } returns balanceMetadata

        // When & Then
        assertThrows<IllegalArgumentException> {
            listener.onCommit(payable)
        }
    }

    @Test
    fun `onRollback should throw MplBusinessException`() {
        // Given
        val payable = createCompanyPayable()

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            listener.onRollback(payable)
        }
        assertEquals(
            "Rollback not supported for advance collection balance, payable id = 1",
            exception.message
        )
    }

    @Test
    fun `transactionType should return ORDER_FORM_ADVANCE`() {
        // When
        val result = listener.transactionType()

        // Then
        assertEquals(TransactionType.ORDER_FORM_ADVANCE, result)
    }

    @Test
    fun `onCommit should handle multiple reference bills correctly`() {
        // Given
        val payable = createCompanyPayable()
        val invoice = mockk<AdvanceCollectionInvoice>()
        val billIds = setOf(1L)
        val billedItem = createBilledItemWrapperWithMultipleReferenceBills(1L, 100L)
        val billedItems = listOf(billedItem)
        val balanceMetadata = createBalanceMetadata("TXN-001", "INV-001")

        every { advanceCollectionInvoiceDataProvider.findExistingInvoice(payable) } returns invoice
        every { invoice.hasCollectionLines() } returns true
        every { invoice.billIds() } returns billIds
        every { billingServiceAdapter.getBillsByIds(billIds) } returns billedItems
        every { companyPrimaryEntityProvider.get(100L) } returns 1001L
        every { companyPrimaryEntityProvider.get(200L) } returns 2001L
        every { invoice.metadataOf(billedItem) } returns balanceMetadata
        every { ledger.registerBalance(any(), any(), any(), any(), any()) } returns Unit

        // When
        listener.onCommit(payable)

        // Then
        verify(exactly = 2) { ledger.registerBalance(any(), any(), any(), any(), any()) }
    }

    private fun createCompanyPayable(): CompanyPayable {
        return CompanyPayable(
            id = 1L,
            companyId = 100L,
            transactionId = "TXN-001",
            items = emptyList(),
            itemType = TransactionType.ORDER_FORM_ADVANCE,
            status = PayableStatus.PAID
        )
    }

    private fun createBilledItemWrapper(billId: Long, companyId: Long, lineCode: String): BilledItemWrapper {
        val billedItem = mockk<BilledItemWrapper>()
        val companyProduct = mockk<CompanyProductWrapper>()
        val chargePolicy = mockk<ReferenceChargePolicy>()
        val referenceBill = mockk<BilledItemWrapper>()
        val referenceCompanyProduct = mockk<CompanyProductWrapper>()
        val referenceTarget = mockk<TargetReference>()

        every { billedItem.billId } returns billId
        every { billedItem.companyId } returns companyId
        every { billedItem.companyProduct } returns companyProduct
        every { billedItem.referenceBills } returns listOf(referenceBill)
        every { billedItem.billingAmount } returns Amount(BigDecimal("100.0"), CurrencyCode.USD)
        every { companyProduct.chargePolicy } returns chargePolicy
        every { referenceBill.companyId } returns companyId
        every { referenceBill.companyProduct } returns referenceCompanyProduct
        every { referenceBill.billingAmount } returns Amount(BigDecimal("50.0"), CurrencyCode.USD)
        every { referenceCompanyProduct.lineCode } returns lineCode
        every { referenceCompanyProduct.dimensions } returns emptyMap()
        every { chargePolicy.findTarget(lineCode, emptyMap()) } returns referenceTarget
        every { referenceTarget.referenceTargetType } returns ReferenceTargetType.COMPANY_PRODUCT

        return billedItem
    }

    private fun createBilledItemWrapperWithInvalidChargePolicy(billId: Long, companyId: Long): BilledItemWrapper {
        val billedItem = mockk<BilledItemWrapper>()
        val companyProduct = mockk<CompanyProductWrapper>()
        val invalidChargePolicy = mockk<com.multiplier.core.payable.adapters.pricing.ChargePolicy>()

        every { billedItem.billId } returns billId
        every { billedItem.companyId } returns companyId
        every { billedItem.companyProduct } returns companyProduct
        every { companyProduct.chargePolicy } returns invalidChargePolicy

        return billedItem
    }

    private fun createBilledItemWrapperWithNullTarget(billId: Long, companyId: Long, lineCode: String): BilledItemWrapper {
        val billedItem = mockk<BilledItemWrapper>()
        val companyProduct = mockk<CompanyProductWrapper>()
        val chargePolicy = mockk<ReferenceChargePolicy>()
        val referenceBill = mockk<BilledItemWrapper>()
        val referenceCompanyProduct = mockk<CompanyProductWrapper>()

        every { billedItem.billId } returns billId
        every { billedItem.companyId } returns companyId
        every { billedItem.companyProduct } returns companyProduct
        every { billedItem.referenceBills } returns listOf(referenceBill)
        every { companyProduct.chargePolicy } returns chargePolicy
        every { referenceBill.companyId } returns companyId
        every { referenceBill.companyProduct } returns referenceCompanyProduct
        every { referenceCompanyProduct.lineCode } returns lineCode
        every { referenceCompanyProduct.dimensions } returns emptyMap()
        every { chargePolicy.findTarget(lineCode, emptyMap()) } returns null

        return billedItem
    }

    private fun createBilledItemWrapperWithMultipleReferenceBills(billId: Long, companyId: Long): BilledItemWrapper {
        val billedItem = mockk<BilledItemWrapper>()
        val companyProduct = mockk<CompanyProductWrapper>()
        val chargePolicy = mockk<ReferenceChargePolicy>()
        val referenceBill1 = createReferenceBill(100L, "EOR_SALARY")
        val referenceBill2 = createReferenceBill(200L, "MANAGEMENT_FEE")

        every { billedItem.billId } returns billId
        every { billedItem.companyId } returns companyId
        every { billedItem.companyProduct } returns companyProduct
        every { billedItem.referenceBills } returns listOf(referenceBill1, referenceBill2)
        every { billedItem.billingAmount } returns Amount(BigDecimal("100.0"), CurrencyCode.USD)
        every { companyProduct.chargePolicy } returns chargePolicy

        return billedItem
    }

    private fun createReferenceBill(companyId: Long, lineCode: String): BilledItemWrapper {
        val referenceBill = mockk<BilledItemWrapper>()
        val referenceCompanyProduct = mockk<CompanyProductWrapper>()
        val referenceTarget = mockk<TargetReference>()
        val chargePolicy = mockk<ReferenceChargePolicy>()

        every { referenceBill.companyId } returns companyId
        every { referenceBill.companyProduct } returns referenceCompanyProduct
        every { referenceBill.billingAmount } returns Amount(BigDecimal("50.0"), CurrencyCode.USD)
        every { referenceCompanyProduct.lineCode } returns lineCode
        every { referenceCompanyProduct.dimensions } returns emptyMap()
        every { referenceTarget.referenceTargetType } returns ReferenceTargetType.COMPANY_PRODUCT

        // Mock the charge policy to return the reference target
        every { chargePolicy.findTarget(lineCode, emptyMap()) } returns referenceTarget

        return referenceBill
    }

    private fun createBalanceMetadata(transactionId: String, invoiceNo: String): AdvanceCollectionBalanceMetadata {
        return mockk<AdvanceCollectionBalanceMetadata>()
    }
}
