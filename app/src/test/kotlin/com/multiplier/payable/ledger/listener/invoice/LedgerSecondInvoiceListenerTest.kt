package com.multiplier.payable.ledger.listener.invoice

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.creditnote.database.CreditNoteItemDto
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.ledger.AdvanceCollectionLedger
import com.multiplier.payable.ledger.domain.invoice.ManualAdjustedSecondInvoice
import com.multiplier.payable.ledger.domain.invoice.ManualAdjustedSecondInvoiceCreditNote
import com.multiplier.payable.ledger.domain.invoice.PlatformAdjustedSecondInvoice
import com.multiplier.payable.ledger.domain.invoice.PlatformAdjustedSecondInvoiceCreditNote
import com.multiplier.payable.ledger.provider.AdjustedSecondInvoiceDataProvider
import com.multiplier.payable.ledger.provider.AdvanceCollectionEntryDataProvider
import com.multiplier.payable.types.PayableStatus
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals

class LedgerSecondInvoiceListenerTest {

    private lateinit var ledger: AdvanceCollectionLedger
    private lateinit var advanceCollectionEntryDataProvider: AdvanceCollectionEntryDataProvider
    private lateinit var entryDataProvider: AdvanceCollectionEntryDataProvider
    private lateinit var secondInvoiceDataProvider: AdjustedSecondInvoiceDataProvider
    private lateinit var listener: LedgerSecondInvoiceListener

    @BeforeEach
    fun setUp() {
        ledger = mockk()
        advanceCollectionEntryDataProvider = mockk()
        entryDataProvider = mockk()
        secondInvoiceDataProvider = mockk()
        listener = LedgerSecondInvoiceListener(
            ledger,
            advanceCollectionEntryDataProvider,
            entryDataProvider,
            secondInvoiceDataProvider
        )
    }

    @Test
    fun `onCommit should call super onCommit for PlatformAdjustedSecondInvoice`() {
        // Given
        val payable = createCompanyPayable()
        val platformInvoice = mockk<PlatformAdjustedSecondInvoice>()
        every { secondInvoiceDataProvider.findExisting(1L) } returns platformInvoice
        every { advanceCollectionEntryDataProvider.findByTransactionId("TXN-001") } returns emptyList()

        // When
        listener.onCommit(payable)

        // Then
        verify { secondInvoiceDataProvider.findExisting(1L) }
        verify { advanceCollectionEntryDataProvider.findByTransactionId("TXN-001") }
    }

    @Test
    fun `onCommit should call super onCommit for PlatformAdjustedSecondInvoiceCreditNote`() {
        // Given
        val payable = createCompanyPayable()
        val platformCreditNote = mockk<PlatformAdjustedSecondInvoiceCreditNote>()
        every { secondInvoiceDataProvider.findExisting(1L) } returns platformCreditNote
        every { advanceCollectionEntryDataProvider.findByTransactionId("TXN-001") } returns emptyList()

        // When
        listener.onCommit(payable)

        // Then
        verify { secondInvoiceDataProvider.findExisting(1L) }
        verify { advanceCollectionEntryDataProvider.findByTransactionId("TXN-001") }
    }

    @Test
    fun `onCommit should return early for ManualAdjustedSecondInvoice with no adjusted line items`() {
        // Given
        val payable = createCompanyPayable()
        val manualInvoice = mockk<ManualAdjustedSecondInvoice>()
        every { secondInvoiceDataProvider.findExisting(1L) } returns manualInvoice
        every { manualInvoice.getAdjustedLineItems() } returns emptyList()

        // When
        listener.onCommit(payable)

        // Then
        verify { secondInvoiceDataProvider.findExisting(1L) }
        verify { manualInvoice.getAdjustedLineItems() }
        verify(exactly = 0) { advanceCollectionEntryDataProvider.findByTransactionId(any()) }
    }

    @Test
    fun `onCommit should throw exception for ManualAdjustedSecondInvoice with adjusted line items`() {
        // Given
        val payable = createCompanyPayable()
        val manualInvoice = mockk<ManualAdjustedSecondInvoice>()
        val adjustedLineItem = createLineItemDTO(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR)
        every { secondInvoiceDataProvider.findExisting(1L) } returns manualInvoice
        every { manualInvoice.getAdjustedLineItems() } returns listOf(adjustedLineItem)

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            listener.onCommit(payable)
        }
        assertEquals(
            "Adjustment not supported for manual second invoice, payable id = 1",
            exception.message
        )
    }

    @Test
    fun `onCommit should return early for ManualAdjustedSecondInvoiceCreditNote with no adjusted line items`() {
        // Given
        val payable = createCompanyPayable()
        val manualCreditNote = mockk<ManualAdjustedSecondInvoiceCreditNote>()
        every { secondInvoiceDataProvider.findExisting(1L) } returns manualCreditNote
        every { manualCreditNote.getAdjustedLineItems() } returns emptyList()

        // When
        listener.onCommit(payable)

        // Then
        verify { secondInvoiceDataProvider.findExisting(1L) }
        verify { manualCreditNote.getAdjustedLineItems() }
        verify(exactly = 0) { advanceCollectionEntryDataProvider.findByTransactionId(any()) }
    }

    @Test
    fun `onCommit should throw exception for ManualAdjustedSecondInvoiceCreditNote with adjusted line items`() {
        // Given
        val payable = createCompanyPayable()
        val manualCreditNote = mockk<ManualAdjustedSecondInvoiceCreditNote>()
        val adjustedLineItem = createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GLOBAL_PAYROLL)
        every { secondInvoiceDataProvider.findExisting(1L) } returns manualCreditNote
        every { manualCreditNote.getAdjustedLineItems() } returns listOf(adjustedLineItem)

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            listener.onCommit(payable)
        }
        assertEquals(
            "Adjustment not supported for manual credit note, payable id = 1",
            exception.message
        )
    }

    @Test
    fun `onRollback should call super onRollback for PlatformAdjustedSecondInvoice`() {
        // Given
        val payable = createCompanyPayable()
        val platformInvoice = mockk<PlatformAdjustedSecondInvoice>()
        every { secondInvoiceDataProvider.findExisting(1L) } returns platformInvoice
        every { entryDataProvider.findByTransactionId("TXN-001") } returns emptyList()
        every { ledger.rollBack(any()) } returns Unit

        // When
        listener.onRollback(payable)

        // Then
        verify { secondInvoiceDataProvider.findExisting(1L) }
        verify { entryDataProvider.findByTransactionId("TXN-001") }
    }

    @Test
    fun `onRollback should call super onRollback for PlatformAdjustedSecondInvoiceCreditNote`() {
        // Given
        val payable = createCompanyPayable()
        val platformCreditNote = mockk<PlatformAdjustedSecondInvoiceCreditNote>()
        every { secondInvoiceDataProvider.findExisting(1L) } returns platformCreditNote
        every { entryDataProvider.findByTransactionId("TXN-001") } returns emptyList()
        every { ledger.rollBack(any()) } returns Unit

        // When
        listener.onRollback(payable)

        // Then
        verify { secondInvoiceDataProvider.findExisting(1L) }
        verify { entryDataProvider.findByTransactionId("TXN-001") }
    }

    @Test
    fun `onRollback should return early for ManualAdjustedSecondInvoice with no adjusted line items`() {
        // Given
        val payable = createCompanyPayable()
        val manualInvoice = mockk<ManualAdjustedSecondInvoice>()
        every { secondInvoiceDataProvider.findExisting(1L) } returns manualInvoice
        every { manualInvoice.getAdjustedLineItems() } returns emptyList()

        // When
        listener.onRollback(payable)

        // Then
        verify { secondInvoiceDataProvider.findExisting(1L) }
        verify { manualInvoice.getAdjustedLineItems() }
        verify(exactly = 0) { entryDataProvider.findByTransactionId(any()) }
    }

    @Test
    fun `onRollback should throw exception for ManualAdjustedSecondInvoice with adjusted line items`() {
        // Given
        val payable = createCompanyPayable()
        val manualInvoice = mockk<ManualAdjustedSecondInvoice>()
        val adjustedLineItem = createLineItemDTO(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR)
        every { secondInvoiceDataProvider.findExisting(1L) } returns manualInvoice
        every { manualInvoice.getAdjustedLineItems() } returns listOf(adjustedLineItem)

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            listener.onRollback(payable)
        }
        assertEquals(
            "Rollback adjustment not supported for manual second invoice, payable id = 1",
            exception.message
        )
    }

    @Test
    fun `onRollback should return early for ManualAdjustedSecondInvoiceCreditNote with no adjusted line items`() {
        // Given
        val payable = createCompanyPayable()
        val manualCreditNote = mockk<ManualAdjustedSecondInvoiceCreditNote>()
        every { secondInvoiceDataProvider.findExisting(1L) } returns manualCreditNote
        every { manualCreditNote.getAdjustedLineItems() } returns emptyList()

        // When
        listener.onRollback(payable)

        // Then
        verify { secondInvoiceDataProvider.findExisting(1L) }
        verify { manualCreditNote.getAdjustedLineItems() }
        verify(exactly = 0) { entryDataProvider.findByTransactionId(any()) }
    }

    @Test
    fun `onRollback should throw exception for ManualAdjustedSecondInvoiceCreditNote with adjusted line items`() {
        // Given
        val payable = createCompanyPayable()
        val manualCreditNote = mockk<ManualAdjustedSecondInvoiceCreditNote>()
        val adjustedLineItem = createCreditNoteItemDto(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_ONE_TIME_SETUP_FEE)
        every { secondInvoiceDataProvider.findExisting(1L) } returns manualCreditNote
        every { manualCreditNote.getAdjustedLineItems() } returns listOf(adjustedLineItem)

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            listener.onRollback(payable)
        }
        assertEquals(
            "Rollback adjustment not supported for manual credit note, payable id = 1",
            exception.message
        )
    }

    @Test
    fun `transactionType should return SECOND_INVOICE`() {
        // When
        val result = listener.transactionType()

        // Then
        assertEquals(TransactionType.SECOND_INVOICE, result)
    }

    @Test
    fun `listener should implement LedgerStandaloneInvoiceListener`() {
        // Then
        assert(listener is LedgerStandaloneInvoiceListener)
    }

    @Test
    fun `listener should have correct dependencies injected`() {
        // Then
        assertEquals(ledger, listener.ledger)
        assertEquals(advanceCollectionEntryDataProvider, listener.advanceCollectionEntryDataProvider)
        assertEquals(entryDataProvider, listener.entryDataProvider)
    }

    private fun createCompanyPayable(): CompanyPayable {
        return CompanyPayable(
            id = 1L,
            companyId = 100L,
            transactionId = "TXN-001",
            items = emptyList(),
            itemType = TransactionType.SECOND_INVOICE,
            status = PayableStatus.PAID
        )
    }

    private fun createPayableItem(lineItemType: LineItemType): PayableItem {
        return mockk<PayableItem>().apply {
            every { <EMAIL> } returns lineItemType.name
        }
    }

    private fun createLineItemDTO(lineItemType: LineItemType): LineItemDTO {
        return mockk<LineItemDTO>().apply {
            every { <EMAIL> } returns lineItemType
        }
    }

    private fun createCreditNoteItemDto(lineItemType: LineItemType): CreditNoteItemDto {
        return mockk<CreditNoteItemDto>().apply {
            every { <EMAIL> } returns lineItemType
        }
    }
}
