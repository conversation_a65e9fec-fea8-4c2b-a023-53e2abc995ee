package com.multiplier.payable.ledger.listener.invoice

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.ledger.domain.AdvanceCollectionEntry
import com.multiplier.payable.ledger.AdvanceCollectionLedger
import com.multiplier.payable.ledger.provider.AdvanceCollectionEntryDataProvider
import com.multiplier.payable.types.PayableStatus
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifyOrder
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class LedgerStandaloneInvoiceListenerTest {

    private lateinit var ledger: AdvanceCollectionLedger
    private lateinit var advanceCollectionEntryDataProvider: AdvanceCollectionEntryDataProvider
    private lateinit var entryDataProvider: AdvanceCollectionEntryDataProvider
    private lateinit var listener: TestLedgerStandaloneInvoiceListener

    @BeforeEach
    fun setUp() {
        ledger = mockk()
        advanceCollectionEntryDataProvider = mockk()
        entryDataProvider = mockk()
        listener = TestLedgerStandaloneInvoiceListener(
            ledger,
            advanceCollectionEntryDataProvider,
            entryDataProvider
        )
    }

    @Test
    fun `onCommit should return early when no adjustment items found`() {
        // Given
        val payable = createCompanyPayableWithoutAdjustmentItems()

        // When
        listener.onCommit(payable)

        // Then
        verify(exactly = 0) { advanceCollectionEntryDataProvider.findByTransactionId(any()) }
    }

    @Test
    fun `onCommit should return early when transaction id is null`() {
        // Given
        val payable = createCompanyPayableWithNullTransactionId()

        // When & Then
        try {
            listener.onCommit(payable)
        } catch (e: IllegalArgumentException) {
            // Expected exception for null transaction id
        }
    }

    @Test
    fun `onCommit should process adjustment items successfully`() {
        // Given
        val adjustmentItem1 = createPayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR)
        val adjustmentItem2 = createPayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR)
        val payable = createCompanyPayableWithAdjustmentItems(listOf(adjustmentItem1, adjustmentItem2))
        val entry1 = mockk<AdvanceCollectionEntry>()
        val entry2 = mockk<AdvanceCollectionEntry>()
        val entries = listOf(entry1, entry2)

        every { advanceCollectionEntryDataProvider.findByTransactionId("TXN-001") } returns entries
        every { ledger.commit(entry1) } returns Unit
        every { ledger.commit(entry2) } returns Unit

        // When
        listener.onCommit(payable)

        // Then
        verifyOrder {
            advanceCollectionEntryDataProvider.findByTransactionId("TXN-001")
            ledger.commit(entry1)
            ledger.commit(entry2)
        }
    }

    @Test
    fun `onCommit should return early when entries size does not match adjustment items size`() {
        // Given
        val adjustmentItem1 = createPayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR)
        val adjustmentItem2 = createPayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR)
        val payable = createCompanyPayableWithAdjustmentItems(listOf(adjustmentItem1, adjustmentItem2))
        val entry1 = mockk<AdvanceCollectionEntry>()
        val entries = listOf(entry1) // Only one entry for two adjustment items

        every { advanceCollectionEntryDataProvider.findByTransactionId("TXN-001") } returns entries

        // When
        listener.onCommit(payable)

        // Then
        verify { advanceCollectionEntryDataProvider.findByTransactionId("TXN-001") }
        verify(exactly = 0) { ledger.commit(any()) }
    }

    @Test
    fun `onCommit should handle exceptions during commit gracefully`() {
        // Given
        val adjustmentItem = createPayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR)
        val payable = createCompanyPayableWithAdjustmentItems(listOf(adjustmentItem))
        val entry = mockk<AdvanceCollectionEntry>()
        val entries = listOf(entry)

        every { advanceCollectionEntryDataProvider.findByTransactionId("TXN-001") } returns entries
        every { ledger.commit(entry) } throws RuntimeException("Commit failed")

        // When
        listener.onCommit(payable)

        // Then
        verify { advanceCollectionEntryDataProvider.findByTransactionId("TXN-001") }
        verify { ledger.commit(entry) }
    }

    @Test
    fun `onCommit should only process advance collection adjustment line item types`() {
        // Given
        val adjustmentItem = createPayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR)
        val nonAdjustmentItem = createPayableItem(LineItemType.EOR_SALARY)
        val payable = createCompanyPayableWithItems(listOf(adjustmentItem, nonAdjustmentItem))
        val entry = mockk<AdvanceCollectionEntry>()
        val entries = listOf(entry)

        every { advanceCollectionEntryDataProvider.findByTransactionId("TXN-001") } returns entries
        every { ledger.commit(entry) } returns Unit

        // When
        listener.onCommit(payable)

        // Then
        verify { advanceCollectionEntryDataProvider.findByTransactionId("TXN-001") }
        verify { ledger.commit(entry) }
    }

    @Test
    fun `onRollback should process all entries for transaction id`() {
        // Given
        val payable = createCompanyPayableWithAdjustmentItems(emptyList())
        val entry1 = mockk<AdvanceCollectionEntry>()
        val entry2 = mockk<AdvanceCollectionEntry>()
        val entries = listOf(entry1, entry2)

        every { entryDataProvider.findByTransactionId("TXN-001") } returns entries
        every { ledger.rollBack(entry1) } returns Unit
        every { ledger.rollBack(entry2) } returns Unit

        // When
        listener.onRollback(payable)

        // Then
        verifyOrder {
            entryDataProvider.findByTransactionId("TXN-001")
            ledger.rollBack(entry1)
            ledger.rollBack(entry2)
        }
    }

    @Test
    fun `onRollback should handle null transaction id`() {
        // Given
        val payable = createCompanyPayableWithNullTransactionId()

        // When & Then
        try {
            listener.onRollback(payable)
        } catch (e: IllegalArgumentException) {
            // Expected exception for null transaction id
        }
    }

    @Test
    fun `onRollback should handle empty entries list`() {
        // Given
        val payable = createCompanyPayableWithAdjustmentItems(emptyList())
        every { entryDataProvider.findByTransactionId("TXN-001") } returns emptyList()

        // When
        listener.onRollback(payable)

        // Then
        verify { entryDataProvider.findByTransactionId("TXN-001") }
        verify(exactly = 0) { ledger.rollBack(any()) }
    }

    @Test
    fun `listener should have correct properties`() {
        // Then
        assertEquals(ledger, listener.ledger)
        assertEquals(advanceCollectionEntryDataProvider, listener.advanceCollectionEntryDataProvider)
        assertEquals(entryDataProvider, listener.entryDataProvider)
    }

    @Test
    fun `onCommit should handle all advance collection adjustment line item types`() {
        // Given
        val adjustmentTypes = LineItemType.getAdvanceCollectionAdjustmentLineItemTypes()
        adjustmentTypes.forEach { lineItemType ->
            val adjustmentItem = createPayableItem(lineItemType)
            val payable = createCompanyPayableWithAdjustmentItems(listOf(adjustmentItem))
            val entry = mockk<AdvanceCollectionEntry>()
            val entries = listOf(entry)

            every { advanceCollectionEntryDataProvider.findByTransactionId("TXN-001") } returns entries
            every { ledger.commit(entry) } returns Unit

            // When
            listener.onCommit(payable)

            // Then
            verify { ledger.commit(entry) }
        }
    }

    private fun createCompanyPayableWithoutAdjustmentItems(): CompanyPayable {
        val nonAdjustmentItem = createPayableItem(LineItemType.EOR_SALARY)
        return CompanyPayable(
            id = 1L,
            companyId = 100L,
            transactionId = "TXN-001",
            items = listOf(nonAdjustmentItem),
            itemType = TransactionType.ANNUAL_PLAN_INVOICE,
            status = PayableStatus.PAID
        )
    }

    private fun createCompanyPayableWithAdjustmentItems(adjustmentItems: List<PayableItem>): CompanyPayable {
        return CompanyPayable(
            id = 1L,
            companyId = 100L,
            transactionId = "TXN-001",
            items = adjustmentItems,
            itemType = TransactionType.ANNUAL_PLAN_INVOICE,
            status = PayableStatus.PAID
        )
    }

    private fun createCompanyPayableWithItems(items: List<PayableItem>): CompanyPayable {
        return CompanyPayable(
            id = 1L,
            companyId = 100L,
            transactionId = "TXN-001",
            items = items,
            itemType = TransactionType.ANNUAL_PLAN_INVOICE,
            status = PayableStatus.PAID
        )
    }

    private fun createCompanyPayableWithNullTransactionId(): CompanyPayable {
        return CompanyPayable(
            id = 1L,
            companyId = 100L,
            transactionId = null,
            items = emptyList(),
            itemType = TransactionType.ANNUAL_PLAN_INVOICE,
            status = PayableStatus.PAID
        )
    }

    private fun createPayableItem(lineItemType: LineItemType): PayableItem {
        return mockk<PayableItem>().apply {
            every { <EMAIL> } returns lineItemType.name
        }
    }

    // Test implementation of the interface
    private class TestLedgerStandaloneInvoiceListener(
        override val ledger: AdvanceCollectionLedger,
        override val advanceCollectionEntryDataProvider: AdvanceCollectionEntryDataProvider,
        override val entryDataProvider: AdvanceCollectionEntryDataProvider
    ) : LedgerStandaloneInvoiceListener {

        override fun transactionType(): TransactionType {
            return TransactionType.ANNUAL_PLAN_INVOICE
        }
    }
}
