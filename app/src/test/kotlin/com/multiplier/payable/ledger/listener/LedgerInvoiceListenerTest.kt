package com.multiplier.payable.ledger.listener

import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.types.PayableStatus
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class LedgerInvoiceListenerTest {

    @Test
    fun `ROLLBACK_PAYABLE_STATUSES should contain correct statuses`() {
        // Then
        val expectedStatuses = setOf(PayableStatus.DELETED, PayableStatus.VOIDED)
        assertEquals(expectedStatuses, LedgerInvoiceListener.ROLLBACK_PAYABLE_STATUSES)
    }

    @Test
    fun `COMMIT_PAYABLE_STATUSES should contain correct statuses`() {
        // Then
        val expectedStatuses = setOf(PayableStatus.PAID)
        assertEquals(expectedStatuses, LedgerInvoiceListener.COMMIT_PAYABLE_STATUSES)
    }

    @Test
    fun `checkAndCommit should call onCommit when payable is valid and status is PAID`() {
        // Given
        val listener = createTestListener(TransactionType.ORDER_FORM_ADVANCE)
        val payable = createCompanyPayable(
            itemType = TransactionType.ORDER_FORM_ADVANCE,
            status = PayableStatus.PAID
        )

        // When
        listener.checkAndCommit(payable)

        // Then
        verify { listener.onCommit(payable) }
    }

    @Test
    fun `checkAndCommit should not call onCommit when payable is invalid`() {
        // Given
        val listener = createTestListener(TransactionType.ORDER_FORM_ADVANCE)
        val payable = createCompanyPayable(
            itemType = TransactionType.SECOND_INVOICE, // Different from listener's transaction type
            status = PayableStatus.PAID
        )

        // When
        listener.checkAndCommit(payable)

        // Then
        verify(exactly = 0) { listener.onCommit(any()) }
    }

    @Test
    fun `checkAndCommit should not call onCommit when status is not in COMMIT_PAYABLE_STATUSES`() {
        // Given
        val listener = createTestListener(TransactionType.ORDER_FORM_ADVANCE)
        val payable = createCompanyPayable(
            itemType = TransactionType.ORDER_FORM_ADVANCE,
            status = PayableStatus.DRAFT // Not in commit statuses
        )

        // When
        listener.checkAndCommit(payable)

        // Then
        verify(exactly = 0) { listener.onCommit(any()) }
    }

    @Test
    fun `checkAndRollback should call onRollback when payable is valid and status is DELETED`() {
        // Given
        val listener = createTestListener(TransactionType.SECOND_INVOICE)
        val payable = createCompanyPayable(
            itemType = TransactionType.SECOND_INVOICE,
            status = PayableStatus.DELETED
        )

        // When
        listener.checkAndRollback(payable)

        // Then
        verify { listener.onRollback(payable) }
    }

    @Test
    fun `checkAndRollback should call onRollback when payable is valid and status is VOIDED`() {
        // Given
        val listener = createTestListener(TransactionType.ANNUAL_PLAN_INVOICE)
        val payable = createCompanyPayable(
            itemType = TransactionType.ANNUAL_PLAN_INVOICE,
            status = PayableStatus.VOIDED
        )

        // When
        listener.checkAndRollback(payable)

        // Then
        verify { listener.onRollback(payable) }
    }

    @Test
    fun `checkAndRollback should not call onRollback when payable is invalid`() {
        // Given
        val listener = createTestListener(TransactionType.ORDER_FORM_ADVANCE)
        val payable = createCompanyPayable(
            itemType = TransactionType.SECOND_INVOICE, // Different from listener's transaction type
            status = PayableStatus.DELETED
        )

        // When
        listener.checkAndRollback(payable)

        // Then
        verify(exactly = 0) { listener.onRollback(any()) }
    }

    @Test
    fun `checkAndRollback should not call onRollback when status is not in ROLLBACK_PAYABLE_STATUSES`() {
        // Given
        val listener = createTestListener(TransactionType.ORDER_FORM_ADVANCE)
        val payable = createCompanyPayable(
            itemType = TransactionType.ORDER_FORM_ADVANCE,
            status = PayableStatus.PAID // Not in rollback statuses
        )

        // When
        listener.checkAndRollback(payable)

        // Then
        verify(exactly = 0) { listener.onRollback(any()) }
    }

    @Test
    fun `isValid should return true when transaction types match`() {
        // Given
        val listener = createTestListener(TransactionType.ORDER_FORM_ADVANCE)
        val payable = createCompanyPayable(
            itemType = TransactionType.ORDER_FORM_ADVANCE,
            status = PayableStatus.PAID
        )

        // When
        val result = listener.isValid(payable)

        // Then
        assertTrue(result)
    }

    @Test
    fun `isValid should return false when transaction types do not match`() {
        // Given
        val listener = createTestListener(TransactionType.ORDER_FORM_ADVANCE)
        val payable = createCompanyPayable(
            itemType = TransactionType.SECOND_INVOICE,
            status = PayableStatus.PAID
        )

        // When
        val result = listener.isValid(payable)

        // Then
        assertFalse(result)
    }

    @Test
    fun `transactionType should return the correct transaction type`() {
        // Given
        val expectedType = TransactionType.ANNUAL_PLAN_INVOICE
        val listener = createTestListener(expectedType)

        // When
        val result = listener.transactionType()

        // Then
        assertEquals(expectedType, result)
    }

    @Test
    fun `checkAndCommit should handle all commit statuses`() {
        // Given
        val listener = createTestListener(TransactionType.ORDER_FORM_ADVANCE)
        
        LedgerInvoiceListener.COMMIT_PAYABLE_STATUSES.forEach { status ->
            val payable = createCompanyPayable(
                itemType = TransactionType.ORDER_FORM_ADVANCE,
                status = status
            )

            // When
            listener.checkAndCommit(payable)

            // Then
            verify { listener.onCommit(payable) }
        }
    }

    @Test
    fun `checkAndRollback should handle all rollback statuses`() {
        // Given
        val listener = createTestListener(TransactionType.ORDER_FORM_ADVANCE)
        
        LedgerInvoiceListener.ROLLBACK_PAYABLE_STATUSES.forEach { status ->
            val payable = createCompanyPayable(
                itemType = TransactionType.ORDER_FORM_ADVANCE,
                status = status
            )

            // When
            listener.checkAndRollback(payable)

            // Then
            verify { listener.onRollback(payable) }
        }
    }

    private fun createTestListener(transactionType: TransactionType): LedgerInvoiceListener {
        val listener = mockk<LedgerInvoiceListener>(relaxed = true)
        every { listener.transactionType() } returns transactionType
        every { listener.isValid(any()) } answers { callOriginal() }
        every { listener.checkAndCommit(any()) } answers { callOriginal() }
        every { listener.checkAndRollback(any()) } answers { callOriginal() }
        return listener
    }

    private fun createCompanyPayable(
        itemType: TransactionType,
        status: PayableStatus
    ): CompanyPayable {
        return CompanyPayable(
            id = 1L,
            companyId = 100L,
            transactionId = "TXN-001",
            items = emptyList(),
            itemType = itemType,
            status = status
        )
    }
}
