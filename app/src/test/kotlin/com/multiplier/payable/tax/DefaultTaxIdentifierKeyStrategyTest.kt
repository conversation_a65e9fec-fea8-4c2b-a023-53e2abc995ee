package com.multiplier.payable.tax

import com.multiplier.core.payable.adapters.CompanyServiceAdapterWithCache
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.company.Company
import com.multiplier.core.payable.companypayable.database.PayableItemTypeMapper
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.types.Address
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.LegalEntity
import com.multiplier.payable.types.PayableItemType
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class DefaultTaxIdentifierKeyStrategyTest {

    @MockK
    private lateinit var companyServiceAdapterWithCache: CompanyServiceAdapterWithCache

    @MockK
    private lateinit var payableItemTypeMapper: PayableItemTypeMapper

    @InjectMockKs
    private lateinit var defaultTaxIdentifierKeyStrategy: DefaultTaxIdentifierKeyStrategy

    @Test
    fun `should create tax identifier key with company country code`() {
        // given
        val context = createTaxIdentifierKeyContext(
            transactionType = TransactionType.ANNUAL_PLAN_INVOICE,
            companyId = 1L,
            lineItemType = "ANNUAL_MANAGEMENT_FEE_EOR"
        )
        val company = createCompanyWithCountry(CountryCode.SGP)
        
        every { companyServiceAdapterWithCache.getCompanyById(1L) } returns company
        every { payableItemTypeMapper.map(LineItemType.ANNUAL_MANAGEMENT_FEE_EOR) } returns PayableItemType.ANNUAL_MEMBER_MANAGEMENT_FEE

        // when
        val result = defaultTaxIdentifierKeyStrategy.getIdentifier(context)

        // then
        assertThat(result.countryCode).isEqualTo(CountryCode.SGP)
        assertThat(result.payableItemType).isEqualTo(PayableItemType.ANNUAL_MEMBER_MANAGEMENT_FEE)
        verify { companyServiceAdapterWithCache.getCompanyById(1L) }
        verify { payableItemTypeMapper.map(LineItemType.ANNUAL_MANAGEMENT_FEE_EOR) }
    }

    @Test
    fun `should handle different country codes correctly`() {
        // given
        val context = createTaxIdentifierKeyContext(
            transactionType = TransactionType.FIRST_INVOICE,
            companyId = 2L,
            lineItemType = "GROSS_SALARY"
        )
        val company = createCompanyWithCountry(CountryCode.USA)
        
        every { companyServiceAdapterWithCache.getCompanyById(2L) } returns company
        every { payableItemTypeMapper.map(LineItemType.GROSS_SALARY) } returns PayableItemType.MEMBER_PAYROLL_COST

        // when
        val result = defaultTaxIdentifierKeyStrategy.getIdentifier(context)

        // then
        assertThat(result.countryCode).isEqualTo(CountryCode.USA)
        assertThat(result.payableItemType).isEqualTo(PayableItemType.MEMBER_PAYROLL_COST)
    }

    @Test
    fun `should handle different payable item types correctly`() {
        // given
        val context = createTaxIdentifierKeyContext(
            transactionType = TransactionType.FIRST_INVOICE,
            companyId = 1L,
            lineItemType = "MANAGEMENT_FEE_EOR"
        )
        val company = createCompanyWithCountry(CountryCode.SGP)
        
        every { companyServiceAdapterWithCache.getCompanyById(1L) } returns company
        every { payableItemTypeMapper.map(LineItemType.MANAGEMENT_FEE_EOR) } returns PayableItemType.MEMBER_MANAGEMENT_FEE

        // when
        val result = defaultTaxIdentifierKeyStrategy.getIdentifier(context)

        // then
        assertThat(result.countryCode).isEqualTo(CountryCode.SGP)
        assertThat(result.payableItemType).isEqualTo(PayableItemType.MEMBER_MANAGEMENT_FEE)
    }

    @Test
    fun `should throw exception when company primary entity is null`() {
        // given
        val context = createTaxIdentifierKeyContext(
            transactionType = TransactionType.ANNUAL_PLAN_INVOICE,
            companyId = 1L,
            lineItemType = "ANNUAL_MANAGEMENT_FEE_EOR"
        )
        val company = Company.builder().id(1L).primaryEntity(null).build()
        
        every { companyServiceAdapterWithCache.getCompanyById(1L) } returns company

        // when & then
        val exception = assertThrows<IllegalArgumentException> {
            defaultTaxIdentifierKeyStrategy.getIdentifier(context)
        }
        assertThat(exception.message).contains("Primary entity must not be null for companyId: 1")
    }

    @Test
    fun `should throw exception when company address is null`() {
        // given
        val context = createTaxIdentifierKeyContext(
            transactionType = TransactionType.ANNUAL_PLAN_INVOICE,
            companyId = 1L,
            lineItemType = "ANNUAL_MANAGEMENT_FEE_EOR"
        )
        val primaryEntity = LegalEntity()
        primaryEntity.address = null
        val company = Company.builder().id(1L).primaryEntity(primaryEntity).build()
        
        every { companyServiceAdapterWithCache.getCompanyById(1L) } returns company

        // when & then
        val exception = assertThrows<IllegalArgumentException> {
            defaultTaxIdentifierKeyStrategy.getIdentifier(context)
        }
        assertThat(exception.message).contains("Address must not be null for companyId: 1")
    }

    @Test
    fun `should throw exception when company country is null`() {
        // given
        val context = createTaxIdentifierKeyContext(
            transactionType = TransactionType.ANNUAL_PLAN_INVOICE,
            companyId = 1L,
            lineItemType = "ANNUAL_MANAGEMENT_FEE_EOR"
        )
        val address = Address()
        address.country = null
        val primaryEntity = LegalEntity()
        primaryEntity.address = address
        val company = Company.builder().id(1L).primaryEntity(primaryEntity).build()
        
        every { companyServiceAdapterWithCache.getCompanyById(1L) } returns company

        // when & then
        val exception = assertThrows<IllegalArgumentException> {
            defaultTaxIdentifierKeyStrategy.getIdentifier(context)
        }
        assertThat(exception.message).contains("Country must not be null for companyId: 1")
    }

    private fun createTaxIdentifierKeyContext(
        transactionType: TransactionType,
        companyId: Long,
        lineItemType: String
    ): TaxIdentifierKeyContext {
        val payableItem = PayableItem(
            month = 4,
            year = 2024,
            lineItemType = lineItemType,
            amountInBaseCurrency = 100.0,
            baseCurrency = "USD",
            originalTimestamp = 123L,
            cycle = InvoiceCycle.MONTHLY,
            companyId = companyId,
            contractId = 1000L,
            countryCode = "SGP"
        )
        return TaxIdentifierKeyContext(transactionType, companyId, payableItem)
    }

    private fun createCompanyWithCountry(countryCode: CountryCode): Company {
        val address = Address()
        address.country = countryCode
        val primaryEntity = LegalEntity()
        primaryEntity.address = address
        return Company.builder().id(1L).primaryEntity(primaryEntity).build()
    }
}
