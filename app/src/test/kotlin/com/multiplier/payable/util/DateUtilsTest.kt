package com.multiplier.payable.util

import com.multiplier.payable.engine.domain.aggregates.MonthYear
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class DateUtilsTest {

    @Test
    fun `generateMonthsBetween returns correct list for same year range`() {
        // GIVEN
        val start = MonthYear(1, 2024)
        val end = MonthYear(3, 2024)
        
        // WHEN
        val result = DateUtils.generateMonthsBetween(start, end)
        
        // THEN
        val expected = listOf(MonthYear(1, 2024), MonthYear(2, 2024), MonthYear(3, 2024))
        assertEquals(expected, result)
    }

    @Test
    fun `generateMonthsBetween returns correct list for consecutive years`() {
        // GIVEN
        val start = MonthYear(12, 2023)
        val end = MonthYear(2, 2024)
        
        // WHEN
        val result = DateUtils.generateMonthsBetween(start, end)
        
        // THEN
        val expected = listOf(MonthYear(12, 2023), MonthYear(1, 2024), MonthYear(2, 2024))
        assertEquals(expected, result)
    }

    @Test
    fun `generateMonthsBetween returns empty list for end before start`() {
        // GIVEN
        val start = MonthYear(3, 2024)
        val end = MonthYear(1, 2024)
        
        // WHEN
        val result = DateUtils.generateMonthsBetween(start, end)
        
        // THEN
        assertEquals(emptyList<MonthYear>(), result)
    }

}
