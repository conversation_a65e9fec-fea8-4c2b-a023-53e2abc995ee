package com.multiplier.payable.netsuite.transaction.kafka.producer

import com.multiplier.payable.kafka.schema.NetsuiteTransaction
import com.multiplier.payable.netsuite.transaction.kafka.topic.TransactionKafkaTopicProperties
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.kafka.core.KafkaTemplate

@ExtendWith(MockitoExtension::class)
class TransactionKafkaProducerTest {

    @Mock
    private lateinit var topicProperties: TransactionKafkaTopicProperties

    @Mock
    private lateinit var kafkaTemplate: KafkaTemplate<String, NetsuiteTransaction>

    @InjectMocks
    private lateinit var transactionKafkaProducer: TransactionKafkaProducer

    @Test
    fun givenContext_whenProduce_thenSendMessage() {
        // GIVEN
        val topic = "awesomeTopic"
        whenever(topicProperties.name).thenReturn(topic)

        val context = TransactionKafkaProducerContext(
            companyPayableId = 42L,
            companyId = 42L,
            month = 42,
            year = 42,
        )
        val expectedMessage = NetsuiteTransaction.newBuilder()
            .setCompanyPayableId(context.companyPayableId!!)
            .setCompanyId(context.companyId)
            .setMonth(context.month)
            .setYear(context.year)
            .build()

        // WHEN
        transactionKafkaProducer.produce(context)

        // THEN
        verify(kafkaTemplate).send(topic, context.companyPayableId.toString(), expectedMessage)
    }
}
