package com.multiplier.payable.netsuite.transaction.kafka.consumer

import com.multiplier.payable.kafka.schema.NetsuiteTransaction
import com.multiplier.payable.ledger.listener.LedgerBalanceEvent
import com.multiplier.payable.ledger.listener.LedgerBalanceEventListener
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class AdvanceCollectionBalanceHandlerTest {

    @Mock
    private lateinit var ledgerBalanceEventListener: LedgerBalanceEventListener

    @InjectMocks
    private lateinit var handler: AdvanceCollectionBalanceHandler

    private fun createNetsuiteTransaction(companyPayableId: Long): NetsuiteTransaction {
        return NetsuiteTransaction.newBuilder()
            .setCompanyPayableId(companyPayableId)
            .setCompanyId(123L)
            .setMonth(1)
            .setYear(2024)
            .build()
    }

    @Test
    fun `consume should handle empty message list`() {
        // Given
        val messages = emptyList<NetsuiteTransaction>()

        // When
        handler.consume(messages)

        // Then
        verify(ledgerBalanceEventListener, never()).onEvent(any())
    }

    @Test
    fun `consume should process single message`() {
        // Given
        val companyPayableId = 123L
        val message = createNetsuiteTransaction(companyPayableId)
        val messages = listOf(message)

        // When
        handler.consume(messages)

        // Then
        verify(ledgerBalanceEventListener).onEvent(LedgerBalanceEvent(companyPayableId))
    }

    @Test
    fun `consume should process multiple messages with same payable id only once`() {
        // Given
        val companyPayableId = 123L
        val message1 = createNetsuiteTransaction(companyPayableId)
        val message2 = createNetsuiteTransaction(companyPayableId)
        val messages = listOf(message1, message2)

        // When
        handler.consume(messages)

        // Then
        verify(ledgerBalanceEventListener, times(1)).onEvent(LedgerBalanceEvent(companyPayableId))
    }

    @Test
    fun `consume should process multiple messages with different payable ids`() {
        // Given
        val companyPayableId1 = 123L
        val companyPayableId2 = 456L
        val message1 = createNetsuiteTransaction(companyPayableId1)
        val message2 = createNetsuiteTransaction(companyPayableId2)
        val messages = listOf(message1, message2)

        // When
        handler.consume(messages)

        // Then
        verify(ledgerBalanceEventListener).onEvent(LedgerBalanceEvent(companyPayableId1))
        verify(ledgerBalanceEventListener).onEvent(LedgerBalanceEvent(companyPayableId2))
    }

    @Test
    fun `consume should continue processing other payables when one fails`() {
        // Given
        val companyPayableId1 = 123L
        val companyPayableId2 = 456L
        val message1 = createNetsuiteTransaction(companyPayableId1)
        val message2 = createNetsuiteTransaction(companyPayableId2)
        val messages = listOf(message1, message2)

        whenever(ledgerBalanceEventListener.onEvent(LedgerBalanceEvent(companyPayableId1)))
            .thenThrow(RuntimeException("Processing failed"))

        // When
        handler.consume(messages)

        // Then
        verify(ledgerBalanceEventListener).onEvent(LedgerBalanceEvent(companyPayableId1))
        verify(ledgerBalanceEventListener).onEvent(LedgerBalanceEvent(companyPayableId2))
    }

    @Test
    fun `consume should handle zero payable id`() {
        // Given
        val companyPayableId = 0L
        val message = createNetsuiteTransaction(companyPayableId)
        val messages = listOf(message)

        // When
        handler.consume(messages)

        // Then
        verify(ledgerBalanceEventListener).onEvent(LedgerBalanceEvent(companyPayableId))
    }

    @Test
    fun `consume should deduplicate payable ids correctly`() {
        // Given
        val uniquePayableIds = (1L..5L).toList()
        val messages = mutableListOf<NetsuiteTransaction>()
        
        // Add each payable id multiple times
        uniquePayableIds.forEach { payableId ->
            repeat(3) {
                messages.add(createNetsuiteTransaction(payableId))
            }
        }

        // When
        handler.consume(messages)

        // Then
        verify(ledgerBalanceEventListener, times(5)).onEvent(any())
        uniquePayableIds.forEach { payableId ->
            verify(ledgerBalanceEventListener).onEvent(LedgerBalanceEvent(payableId))
        }
    }
}
