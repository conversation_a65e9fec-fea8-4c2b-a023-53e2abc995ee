package com.multiplier.payable.netsuite.transaction.appevent.publisher.invoice

import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.repository.model.InvoiceType
import com.multiplier.payable.netsuite.transaction.appevent.publisher.TransactionAppEventPublisher
import com.multiplier.payable.netsuite.transaction.appevent.publisher.TransactionAppEventPublisherContext
import com.multiplier.payable.types.InvoiceStatus
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoInteractions
import java.time.LocalDate

@ExtendWith(MockitoExtension::class)
class InvoiceAppEventPublisherTest {

    @Mock
    private lateinit var transactionAppEventPublisher: TransactionAppEventPublisher

    @InjectMocks
    private lateinit var invoiceAppEventPublisher: InvoiceAppEventPublisher

    @Test
    fun givenCompanyPayableIdIsNull_whenPublish_thenEventIsNotPublished() {
        // GIVEN
        val invoice =
            InvoiceDTO
                .builder()
                .companyPayableId(null)
                .companyId(42L)
                .externalId("awesomeExternalId")
                .date(LocalDate.of(2023, 5, 15))
                .build()

        // WHEN
        invoiceAppEventPublisher.publish(invoice)

        // THEN
        verifyNoInteractions(transactionAppEventPublisher)
    }

    @Test
    fun givenValidInvoice_whenPublish_thenEventIsPublished() {
        // GIVEN
        val invoice =
            InvoiceDTO
                .builder()
                .companyPayableId(1L)
                .companyId(42L)
                .externalId("awesomeExternalId")
                .date(LocalDate.of(2023, 5, 15))
                .type(InvoiceType.SALARY)
                .status(InvoiceStatus.DRAFT)
                .build()

        val expectedContext =
            TransactionAppEventPublisherContext(
                companyPayableId = invoice.companyPayableId,
                companyId = invoice.companyId,
                month = invoice.date.monthValue,
                year = invoice.date.year,
            )

        // WHEN
        invoiceAppEventPublisher.publish(invoice)

        // THEN
        verify(transactionAppEventPublisher).publish(expectedContext)
    }

    @Test
    fun givenInvoiceWithAnnualPlanType_whenPublish_thenEventIsPublished() {
        // GIVEN
        val invoice =
            InvoiceDTO
                .builder()
                .externalId("awesomeExternalId")
                .companyPayableId(42L)
                .companyId(42L)
                .date(LocalDate.of(2023, 5, 15))
                .type(InvoiceType.ANNUAL_PLAN)
                .status(InvoiceStatus.DRAFT)
                .build()

        val expectedContext =
            TransactionAppEventPublisherContext(
                companyPayableId = invoice.companyPayableId,
                companyId = invoice.companyId,
                month = invoice.date.monthValue,
                year = invoice.date.year,
            )

        // WHEN
        invoiceAppEventPublisher.publish(invoice)

        // THEN
        verify(transactionAppEventPublisher).publish(expectedContext)
    }

    @Test
    fun givenIsrGenerationEnabledAndInvoiceTypeSalary_whenPublish_thenEventIsPublished() {
        // GIVEN
        val invoice =
            InvoiceDTO
                .builder()
                .externalId("awesomeExternalId")
                .companyPayableId(42L)
                .companyId(42L)
                .date(LocalDate.of(2023, 5, 15))
                .type(InvoiceType.SALARY)
                .status(InvoiceStatus.DRAFT)
                .build()

        val expectedContext =
            TransactionAppEventPublisherContext(
                companyPayableId = invoice.companyPayableId,
                companyId = invoice.companyId,
                month = invoice.date.monthValue,
                year = invoice.date.year,
            )

        // WHEN
        invoiceAppEventPublisher.publish(invoice)

        // THEN
        verify(transactionAppEventPublisher).publish(expectedContext)
    }

    @Test
    fun givenCompanyPayableIsNull_whenPublish_thenEventIsNotPublished() {
        // GIVEN
        val invoice =
            InvoiceDTO
                .builder()
                .companyPayableId(null)
                .companyId(42L)
                .externalId("awesomeExternalId")
                .date(LocalDate.of(2023, 5, 15))
                .type(InvoiceType.SALARY)
                .status(InvoiceStatus.DRAFT)
                .build()

        // WHEN
        invoiceAppEventPublisher.publish(invoice)

        // THEN
        verifyNoInteractions(transactionAppEventPublisher)
    }

    @Test
    fun givenStatusIsPaid_whenPublish_thenEventIsPublished() {
        // GIVEN
        val invoice =
            InvoiceDTO
                .builder()
                .externalId("awesomeExternalId")
                .companyPayableId(42L)
                .companyId(42L)
                .date(LocalDate.of(2023, 5, 15))
                .type(InvoiceType.SALARY)
                .status(InvoiceStatus.PAID)
                .build()

        val expectedContext =
            TransactionAppEventPublisherContext(
                companyPayableId = invoice.companyPayableId,
                companyId = invoice.companyId,
                month = invoice.date.monthValue,
                year = invoice.date.year,
            )

        // WHEN
        invoiceAppEventPublisher.publish(invoice)

        // THEN
        verify(transactionAppEventPublisher).publish(expectedContext)
    }

    @ParameterizedTest
    @ValueSource(strings = ["DRAFT", "AUTHORIZED", "PENDING"])
    fun givenStatusIsAuthorized_whenPublish_thenEventIsPublished(status: InvoiceStatus) {
        // GIVEN
        val invoice =
            InvoiceDTO
                .builder()
                .externalId("awesomeExternalId")
                .companyPayableId(42L)
                .companyId(42L)
                .date(LocalDate.of(2023, 5, 15))
                .type(InvoiceType.SALARY)
                .status(status)
                .build()
        val expectedContext =
            TransactionAppEventPublisherContext(
                companyPayableId = invoice.companyPayableId,
                companyId = invoice.companyId,
                month = invoice.date.monthValue,
                year = invoice.date.year,
            )

        // WHEN
        invoiceAppEventPublisher.publish(invoice)

        // THEN
        verify(transactionAppEventPublisher).publish(expectedContext)
    }
}
