package com.multiplier.payable.netsuite.transaction.kafka.consumer

import com.multiplier.core.payable.creditnote.database.CreditNoteService
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.InvoiceType
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaInvoice
import com.multiplier.payable.types.InvoiceStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito.lenient
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class IsrGenerationSkippedCompanyPayableIdProviderTest {

    private val jpaCompanyPayableRepository: JpaCompanyPayableRepository = mock()

    private val creditNoteService: CreditNoteService = mock()

    private val provider = IsrGenerationSkippedCompanyPayableIdProvider(
        jpaCompanyPayableRepository,
        creditNoteService
    )

    @Test
    fun `get should return empty set when companyPayableIds is empty`() {
        // When
        val result = provider.get(emptyList())

        // Then
        assertEquals(emptySet<Long>(), result)
    }

    @Test
    fun `get should return filtered IDs based on manual and legacy transactions`() {
        // Given
        val mockInvoice1 = mock<com.multiplier.core.payable.repository.model.JpaInvoice>()
        lenient().`when`(mockInvoice1.type).thenReturn(com.multiplier.core.payable.repository.model.InvoiceType.SALARY)
        lenient().`when`(mockInvoice1.status).thenReturn(com.multiplier.payable.types.InvoiceStatus.DRAFT)

        val mockInvoice2 = mock<com.multiplier.core.payable.repository.model.JpaInvoice>()
        lenient().`when`(mockInvoice2.type).thenReturn(com.multiplier.core.payable.repository.model.InvoiceType.SALARY)
        lenient().`when`(mockInvoice2.status).thenReturn(com.multiplier.payable.types.InvoiceStatus.DRAFT)

        val mockInvoice4 = mock<com.multiplier.core.payable.repository.model.JpaInvoice>()
        lenient().`when`(mockInvoice4.type).thenReturn(com.multiplier.core.payable.repository.model.InvoiceType.SALARY)
        lenient().`when`(mockInvoice4.status).thenReturn(com.multiplier.payable.types.InvoiceStatus.DRAFT)

        val mockInvoice5 = mock<com.multiplier.core.payable.repository.model.JpaInvoice>()
        lenient().`when`(mockInvoice5.type).thenReturn(com.multiplier.core.payable.repository.model.InvoiceType.SALARY)
        lenient().`when`(mockInvoice5.status).thenReturn(com.multiplier.payable.types.InvoiceStatus.DRAFT)

        val companyPayable1 = mock<JpaCompanyPayable> {
            on { id } doReturn 1L
            on { isManualTransaction } doReturn true  // Manual transaction
            on { invoice } doReturn mockInvoice1
        }

        val companyPayable2 = mock<JpaCompanyPayable> {
            on { id } doReturn 2L
            on { isLegacyTransaction } doReturn true  // Legacy transaction
            on { invoice } doReturn mockInvoice2
        }

        val companyPayable3 = mock<JpaCompanyPayable>()
        lenient().`when`(companyPayable3.id).thenReturn(3L)
        lenient().`when`(companyPayable3.isManualTransaction).thenReturn(false)
        lenient().`when`(companyPayable3.isLegacyTransaction).thenReturn(false)
        lenient().`when`(companyPayable3.skipIsrGeneration).thenReturn(false)
        whenever(companyPayable3.invoice).thenReturn(null)  // No invoice, so won't be processed

        val companyPayable4 = mock<JpaCompanyPayable> {
            on { id } doReturn 4L
            on { isManualTransaction } doReturn true  // Manual transaction
            on { invoice } doReturn mockInvoice4
        }

        val companyPayable5 = mock<JpaCompanyPayable> {
            on { id } doReturn 5L
            on { skipIsrGeneration } doReturn true  // Skip ISR generation
            on { invoice } doReturn mockInvoice5
        }

        whenever(jpaCompanyPayableRepository.findByIdIn(any()))
            .thenReturn(listOf(companyPayable1, companyPayable2, companyPayable3, companyPayable4, companyPayable5))
        whenever(creditNoteService.getAllCreditNotes(any()))
            .thenReturn(emptyList())

        // When
        val result = provider.get(listOf(1L, 2L, 3L, 4L, 5L))

        // Then
        assertEquals(setOf(1L, 2L, 4L, 5L), result)  // Only 1, 2, 4, and 5 should be returned (skipped payables)
    }

    @Test
    fun `get should return empty set when no manual or legacy transactions are found`() {
        // Given
        val companyPayable1 = mock<JpaCompanyPayable>()
        lenient().`when`(companyPayable1.id).thenReturn(1L)
        lenient().`when`(companyPayable1.isManualTransaction).thenReturn(false)
        lenient().`when`(companyPayable1.isLegacyTransaction).thenReturn(false)
        lenient().`when`(companyPayable1.skipIsrGeneration).thenReturn(false)
        whenever(companyPayable1.invoice).thenReturn(null)  // No invoice, so won't be processed

        val companyPayable2 = mock<JpaCompanyPayable>()
        lenient().`when`(companyPayable2.id).thenReturn(2L)
        lenient().`when`(companyPayable2.isManualTransaction).thenReturn(false)
        lenient().`when`(companyPayable2.isLegacyTransaction).thenReturn(false)
        lenient().`when`(companyPayable2.skipIsrGeneration).thenReturn(false)
        whenever(companyPayable2.invoice).thenReturn(null)  // No invoice, so won't be processed

        whenever(jpaCompanyPayableRepository.findByIdIn(any()))
            .thenReturn(listOf(companyPayable1, companyPayable2))
        whenever(creditNoteService.getAllCreditNotes(any()))
            .thenReturn(emptyList())

        // When
        val result = provider.get(listOf(1L, 2L))

        // Then
        assertEquals(emptySet<Long>(), result)  // No transactions should match since no invoices
    }

    @Test
    fun `get should return empty set when input list is empty`() {
        // Given
        val emptyList = emptyList<Long>()

        // When
        val result = provider.get(emptyList)

        // Then
        assertEquals(emptySet<Long>(), result)
        // Verify that repository methods are not called when input is empty
        verify(jpaCompanyPayableRepository, never()).findByIdIn(any())
        verify(creditNoteService, never()).getAllCreditNotes(any())
    }

    @Test
    fun `get should return empty set when input list is null`() {
        // Given - null list should be handled by CollectionUtils.isEmpty

        // When
        val result = provider.get(emptyList())

        // Then
        assertEquals(emptySet<Long>(), result)
    }

    @Test
    fun `get should handle invoice with non-salary type`() {
        // Given
        val companyPayable = mock<JpaCompanyPayable>()
        val invoice = mock<JpaInvoice>()

        lenient().`when`(companyPayable.id).thenReturn(1L)
        lenient().`when`(companyPayable.isManualTransaction).thenReturn(false)
        lenient().`when`(companyPayable.isLegacyTransaction).thenReturn(false)
        lenient().`when`(companyPayable.skipIsrGeneration).thenReturn(false)
        whenever(companyPayable.invoice).thenReturn(invoice)
        whenever(invoice.type).thenReturn(InvoiceType.GROSS) // Not SALARY
        lenient().`when`(invoice.status).thenReturn(InvoiceStatus.DRAFT)

        whenever(jpaCompanyPayableRepository.findByIdIn(any()))
            .thenReturn(listOf(companyPayable))
        whenever(creditNoteService.getAllCreditNotes(any()))
            .thenReturn(emptyList())

        // When
        val result = provider.get(listOf(1L))

        // Then
        assertEquals(setOf(1L), result) // Should be skipped due to non-salary type
    }

    @Test
    fun `get should handle invoice with non-considered status`() {
        // Given
        val companyPayable = mock<JpaCompanyPayable>()
        val invoice = mock<JpaInvoice>()

        lenient().`when`(companyPayable.id).thenReturn(1L)
        lenient().`when`(companyPayable.isManualTransaction).thenReturn(false)
        lenient().`when`(companyPayable.isLegacyTransaction).thenReturn(false)
        lenient().`when`(companyPayable.skipIsrGeneration).thenReturn(false)
        whenever(companyPayable.invoice).thenReturn(invoice)
        whenever(invoice.type).thenReturn(InvoiceType.SALARY)
        whenever(invoice.status).thenReturn(InvoiceStatus.PAID) // Not in ISR_UPDATABLE_INVOICE_STATUSES

        whenever(jpaCompanyPayableRepository.findByIdIn(any()))
            .thenReturn(listOf(companyPayable))
        whenever(creditNoteService.getAllCreditNotes(any()))
            .thenReturn(emptyList())

        // When
        val result = provider.get(listOf(1L))

        // Then
        assertEquals(setOf(1L), result) // Should be skipped due to non-considered status
    }

    @Test
    fun `get should not skip invoice with salary type and considered status`() {
        // Given
        val companyPayable = mock<JpaCompanyPayable>()
        val invoice = mock<JpaInvoice>()

        lenient().`when`(companyPayable.id).thenReturn(1L)
        lenient().`when`(companyPayable.isManualTransaction).thenReturn(false)
        lenient().`when`(companyPayable.isLegacyTransaction).thenReturn(false)
        lenient().`when`(companyPayable.skipIsrGeneration).thenReturn(false)
        whenever(companyPayable.invoice).thenReturn(invoice)
        whenever(invoice.type).thenReturn(InvoiceType.SALARY)
        whenever(invoice.status).thenReturn(InvoiceStatus.DRAFT) // In ISR_UPDATABLE_INVOICE_STATUSES

        whenever(jpaCompanyPayableRepository.findByIdIn(any()))
            .thenReturn(listOf(companyPayable))
        whenever(creditNoteService.getAllCreditNotes(any()))
            .thenReturn(emptyList())

        // When
        val result = provider.get(listOf(1L))

        // Then
        assertEquals(emptySet<Long>(), result) // Should not be skipped
    }

    @Test
    fun `get should test companion object constants coverage`() {
        // Given - Test that companion object constants are accessible
        val companyPayable = mock<JpaCompanyPayable>()
        val invoice = mock<JpaInvoice>()

        lenient().`when`(companyPayable.id).thenReturn(1L)
        lenient().`when`(companyPayable.isManualTransaction).thenReturn(false)
        lenient().`when`(companyPayable.isLegacyTransaction).thenReturn(false)
        lenient().`when`(companyPayable.skipIsrGeneration).thenReturn(false)
        whenever(companyPayable.invoice).thenReturn(invoice)
        whenever(invoice.type).thenReturn(InvoiceType.SALARY)

        // Test all ISR_UPDATABLE_INVOICE_STATUSES
        whenever(invoice.status).thenReturn(InvoiceStatus.PENDING) // One of the updatable statuses

        whenever(jpaCompanyPayableRepository.findByIdIn(any()))
            .thenReturn(listOf(companyPayable))
        whenever(creditNoteService.getAllCreditNotes(any()))
            .thenReturn(emptyList())

        // When
        val result = provider.get(listOf(1L))

        // Then
        assertEquals(emptySet<Long>(), result) // Should not be skipped for PENDING status
    }
}
