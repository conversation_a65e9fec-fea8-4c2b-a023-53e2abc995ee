package com.multiplier.payable.engine.payableitem.deposit.description.builder

import com.multiplier.core.payable.adapters.DepositServiceAdapter
import com.multiplier.payable.engine.common.TimePeriod
import com.multiplier.payable.engine.common.TimeUnit
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.deposit.Deposit
import com.multiplier.payable.engine.deposit.DepositPayableItemDescriptionBuilderContext
import com.multiplier.payable.engine.deposit.DepositType
import com.multiplier.payable.engine.deposit.SalaryPeriodPolicyFeed
import com.multiplier.payable.engine.payableitem.deposit.description.label.builder.SalaryPayableItemDescriptionLabelBuilder
import com.multiplier.payable.engine.reconciler.descriptionbuilder.AmountFormatter
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import com.multiplier.payable.types.CurrencyCode as PayableCurrencyCode

@ExtendWith(MockitoExtension::class)
class SalaryPayableItemDescriptionBuilderTest {
    @Mock
    private lateinit var labelBuilder: SalaryPayableItemDescriptionLabelBuilder

    @Mock
    private lateinit var depositServiceAdapter: DepositServiceAdapter

    @Mock
    private lateinit var amountFormatter: AmountFormatter

    @InjectMocks
    private lateinit var builder: SalaryPayableItemDescriptionBuilder

    @Test
    fun whenGetType_thenReturnType() {
        // WHEN
        val depositType = builder.getType()

        // THEN
        assertThat(depositType).isEqualTo(DepositType.SALARY)
    }

    @Test
    fun givenDepositContext_whenBuild_thenReturnDescription() {
        // GIVEN
        val amount = 42.0
        val currencyCode = CurrencyCode.USD
        val contractType = ContractType.CONTRACTOR
        val deposit =
            Deposit(
                type = DepositType.SALARY,
                amount = amount,
                currencyCode = currencyCode,
                contractType = contractType,
            )
        val companyId = 1L
        val depositPayableItemDescriptionBuilderContext =
            DepositPayableItemDescriptionBuilderContext(
                companyId = companyId,
                deposit = deposit,
                enhancedDescriptionEnabled = false,
            )

        val salaryLabel = "awesomeSalaryLabel"
        whenever(labelBuilder.build(contractType, companyId)).thenReturn(salaryLabel)
        whenever(amountFormatter.format(amount)).thenReturn("awesomeAmount")

        val expectedDescription = "awesomeSalaryLabel: USD awesomeAmount"

        // WHEN
        val description = builder.build(depositPayableItemDescriptionBuilderContext)

        // THEN
        assertThat(description).isEqualTo(expectedDescription)
    }

    @Test
    fun givenEmployeeDepositContext_whenBuild_thenReturnEnhancedDescription() {
        // GIVEN
        val amount = 42.0
        val currencyCode = CurrencyCode.USD
        val contractType = ContractType.EMPLOYEE
        val deposit =
            Deposit(
                type = DepositType.SALARY,
                amount = amount,
                currencyCode = currencyCode,
                contractType = contractType,
                depositId = 123L,
                noticePeriod = 1.0,
            )
        val companyId = 1L
        val depositPayableItemDescriptionBuilderContext =
            DepositPayableItemDescriptionBuilderContext(
                companyId = companyId,
                deposit = deposit,
                enhancedDescriptionEnabled = true,
            )

        val salaryPeriodPolicyFeed = SalaryPeriodPolicyFeed(
            grossAmount = com.multiplier.payable.engine.common.Amount(java.math.BigDecimal("32.0"), PayableCurrencyCode.USD),
            contributionAmount = com.multiplier.payable.engine.common.Amount(java.math.BigDecimal("10.0"), PayableCurrencyCode.USD),
            period = TimePeriod(1.0, TimeUnit.MONTHS)
        )

        whenever(depositServiceAdapter.getDepositFeed(any(), any(), any())).thenReturn(salaryPeriodPolicyFeed)

        // WHEN
        val description = builder.build(depositPayableItemDescriptionBuilderContext)

        // THEN
        val expectedDescription = "Deposit - Salary \nTotal: USD 42.00 \nSalary: USD 32.00 \n Contributions: USD 10.00 \nPeriod Covered: 1 Month"
        assertThat(description).isEqualTo(expectedDescription)
    }

    @Test
    fun givenEmployeeDepositContextWithGreaterThanOneNumberOfMonths_whenBuild_thenReturnEnhancedDescription() {
        // GIVEN
        val amount = 42.0
        val currencyCode = CurrencyCode.USD
        val contractType = ContractType.EMPLOYEE
        val deposit =
            Deposit(
                type = DepositType.SALARY,
                amount = amount,
                currencyCode = currencyCode,
                contractType = contractType,
                depositId = 123L,
                noticePeriod = 2.0,
            )
        val companyId = 1L
        val depositPayableItemDescriptionBuilderContext =
            DepositPayableItemDescriptionBuilderContext(
                companyId = companyId,
                deposit = deposit,
                enhancedDescriptionEnabled = true,
            )

        val salaryPeriodPolicyFeed = SalaryPeriodPolicyFeed(
            grossAmount = com.multiplier.payable.engine.common.Amount(java.math.BigDecimal("32.0"), PayableCurrencyCode.USD),
            contributionAmount = com.multiplier.payable.engine.common.Amount(java.math.BigDecimal("10.0"), PayableCurrencyCode.USD),
            period = TimePeriod(2.0, TimeUnit.MONTHS)
        )

        whenever(depositServiceAdapter.getDepositFeed(any(), any(), any())).thenReturn(salaryPeriodPolicyFeed)

        // WHEN
        val description = builder.build(depositPayableItemDescriptionBuilderContext)

        // THEN
        val expectedDescription = "Deposit - Salary \nTotal: USD 42.00 \nSalary: USD 32.00 \n Contributions: USD 10.00 \nPeriod Covered: 2 Months"
        assertThat(description).isEqualTo(expectedDescription)
    }
}
