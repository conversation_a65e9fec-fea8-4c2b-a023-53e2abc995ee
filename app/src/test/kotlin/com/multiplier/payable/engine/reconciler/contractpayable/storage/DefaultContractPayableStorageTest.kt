package com.multiplier.payable.engine.reconciler.contractpayable.storage

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.JpaContractDepositPayableRepository
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.any
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever
import java.util.*

@ExtendWith(MockitoExtension::class)
class DefaultContractPayableStorageTest {

    @Mock
    private lateinit var jpaCompanyPayableRepository: JpaCompanyPayableRepository

    @Mock
    private lateinit var jpaContractDepositPayableRepository: JpaContractDepositPayableRepository

    @InjectMocks
    private lateinit var defaultContractPayableStorage: DefaultContractPayableStorage

    @Test
    fun givenContext_whenStore_thenThrowException() {
        // GIVEN
        val context = mock<ContractPayableStorageContext>()

        whenever(jpaCompanyPayableRepository.findById(context.companyPayableId))
            .thenThrow(IllegalArgumentException())

        // WHEN and THEN
        assertThrows<IllegalArgumentException> {
            defaultContractPayableStorage.store(context)
        }
    }

    @Test
    fun givenContext_whenStore_thenDelegate() {
        // GIVEN
        val context = mock<ContractPayableStorageContext>()

        val jpaCompanyPayable = mock<JpaCompanyPayable>()
        whenever(jpaCompanyPayableRepository.findById(context.companyPayableId))
            .thenReturn(Optional.of(jpaCompanyPayable))

        // WHEN
        defaultContractPayableStorage.store(context)

        // THEN
        verify(jpaContractDepositPayableRepository).save(any())
    }
}