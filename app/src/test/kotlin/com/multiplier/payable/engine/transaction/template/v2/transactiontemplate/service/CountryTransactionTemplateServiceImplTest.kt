package com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.service


import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.domain.CountryTransactionTemplateFilter
import com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.domain.JpaCountryTransactionTemplate
import com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.domain.JpaCountryTransactionTemplateRepository
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import java.util.Optional

@ExtendWith(MockitoExtension::class)
class CountryTransactionTemplateServiceImplTest {

    private val repository = Mockito.mock(JpaCountryTransactionTemplateRepository::class.java)
    private val service = CountryTransactionTemplateServiceImpl(repository)

    @Test
    fun `test findTemplateByFilter with valid filter`() {
        val filter = CountryTransactionTemplateFilter(TransactionType.VENDOR_BILL, "USA")
        val template = Mockito.mock(JpaCountryTransactionTemplate::class.java)

        whenever(repository.findOne(any())).thenReturn(Optional.of(template))

        val result = service.findTemplateByFilter(filter)

        assertTrue(result.isPresent)
    }

    @Test
    fun `test findTemplateByFilter with no matching template`() {
        val filter = CountryTransactionTemplateFilter(TransactionType.VENDOR_BILL, "USA")

        whenever(repository.findOne(any())).thenReturn(Optional.empty())

        val result = service.findTemplateByFilter(filter)

        assertTrue(result.isEmpty)
    }
}