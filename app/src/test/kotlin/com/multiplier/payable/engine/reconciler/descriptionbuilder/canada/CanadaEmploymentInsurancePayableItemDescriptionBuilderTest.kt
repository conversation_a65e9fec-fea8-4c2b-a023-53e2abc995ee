package com.multiplier.payable.engine.reconciler.descriptionbuilder.canada

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.AmountFormatter
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.assertj.core.api.Assertions.assertThat
import org.mockito.Mockito.mock

@ExtendWith(MockitoExtension::class)
class CanadaEmploymentInsurancePayableItemDescriptionBuilderTest {

    @Mock
    private lateinit var amountFormatter: AmountFormatter

    @InjectMocks
    private lateinit var canadaEmploymentInsurancePayableItemDescriptionBuilder: CanadaEmploymentInsurancePayableItemDescriptionBuilder

    @Test
    fun `getLineItemType should return CANADA_EMPLOYMENT_INSURANCE`() {
        // WHEN
        val result = canadaEmploymentInsurancePayableItemDescriptionBuilder.lineItemType

        // THEN
        assertThat(result).isEqualTo(LineItemType.CANADA_EMPLOYMENT_INSURANCE)
    }

    @Test
    fun `getPrefix should return 'Canada Employment Insurance'`() {
        // WHEN
        val result = canadaEmploymentInsurancePayableItemDescriptionBuilder.getPrefix()

        // THEN
        assertThat(result).isEqualTo("Employment Insurance")
    }

    @Test
    fun `build should format description correctly`() {
        // GIVEN
        val context = PayableItemDescriptionBuilderContext(
            contractId = 123L,
            currencyCode = "CAD",
            amountInBaseCurrency = 1000.0,
            payrollCycleId = 456L,
            monthYear = mock()
        )

        // WHEN
        val result = canadaEmploymentInsurancePayableItemDescriptionBuilder.build(context)

        // THEN
        val expected = "Employment Insurance"
        assertThat(result).isEqualTo(expected)
    }
}
