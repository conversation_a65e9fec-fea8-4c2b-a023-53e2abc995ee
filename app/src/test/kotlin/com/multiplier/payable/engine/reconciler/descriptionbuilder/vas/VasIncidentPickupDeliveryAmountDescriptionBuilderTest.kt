package com.multiplier.payable.engine.reconciler.descriptionbuilder.vas

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import com.multiplier.payable.engine.vas.IncidentType
import com.multiplier.payable.engine.vas.IncidentWrapper
import com.multiplier.payable.engine.vas.LogisticsIncident
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.assertEquals

class VasIncidentPickupDeliveryAmountDescriptionBuilderTest {

    private val builder = VasIncidentPickupDeliveryAmountDescriptionBuilder()

    @Test
    fun `should return correct line item type`() {
        // When
        val result = builder.lineItemType

        // Then
        assertEquals(LineItemType.VAS_INCIDENT_PICKUP_DELIVERY_AMOUNT, result)
    }

    @Test
    fun `should build description from incident description`() {
        // Given
        val incident = LogisticsIncident(
            id = 1L,
            amount = Amount(BigDecimal.valueOf(100.0), CurrencyCode.USD),
            description = "Test Pickup Delivery Description",
            incidentTime = LocalDateTime.now(),
            type = IncidentType.PICKUP_DELIVERY,
            countryCode = CountryCode.IND,
            chargePolicy = null
        )

        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(1, 2024),
            currencyCode = "USD",
            amountInBaseCurrency = 100.0,
            incidentWrapper = IncidentWrapper(
                incident = incident,
                transactionId = "test-transaction-id"
            )
        )

        // When
        val result = builder.build(context)

        // Then
        assertEquals("Test Pickup Delivery Description", result)
    }

    @Test
    fun `should throw exception when incident wrapper is null`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(1, 2024),
            currencyCode = "USD",
            amountInBaseCurrency = 100.0
        )

        // When/Then
        val exception = assertThrows<IllegalArgumentException> {
            builder.build(context)
        }

        assertEquals("incidentWrapper must not be null", exception.message)
    }

    @Test
    fun `should throw exception when incident is null`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(1, 2024),
            currencyCode = "USD",
            amountInBaseCurrency = 100.0,
            incidentWrapper = IncidentWrapper(
                incident = null,
                transactionId = "test-transaction-id"
            )
        )

        // When/Then
        val exception = assertThrows<IllegalArgumentException> {
            builder.build(context)
        }

        assertEquals("incident must not be null", exception.message)
    }
}
