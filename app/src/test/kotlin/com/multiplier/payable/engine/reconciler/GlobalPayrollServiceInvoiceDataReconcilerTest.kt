package com.multiplier.payable.engine.reconciler

import com.multiplier.core.payable.adapters.BillingServiceAdapter
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.adapters.billing.BilledItemWrapper
import com.multiplier.core.payable.adapters.pricing.ReferenceChargePolicy
import com.multiplier.core.payable.adapters.product.CompanyProductWrapper
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.Duration
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorage
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorageContext
import com.multiplier.payable.engine.reconciler.data.invoice.InvoiceDataProvider
import com.multiplier.payable.engine.reconciler.data.invoice.InvoiceDataProviderFactory
import com.multiplier.payable.engine.reconciler.data.item.ItemStoreDataProvider
import com.multiplier.payable.engine.reconciler.data.item.ItemStoreDataProviderFactory
import com.multiplier.payable.engine.reconciler.diff.reconciler.InvoiceDiffReconciler
import com.multiplier.payable.engine.reconciler.diff.reconciler.InvoiceDiffReconcilerFactory
import com.multiplier.payable.engine.testutils.InvoiceEngineTestDataFactory
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import com.multiplier.payable.ledger.AdvanceCollectionBalanceMetadata
import com.multiplier.payable.ledger.AdvanceCollectionLedger
import com.multiplier.payable.ledger.AdvanceCollectionReservedAmount
import com.multiplier.payable.ledger.domain.AdvanceCollectionBalance
import com.multiplier.payable.ledger.domain.AdvanceCollectionEntry
import com.multiplier.payable.ledger.domain.AdvanceCollectionProduct
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import org.mockito.kotlin.isNull
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.threeten.bp.Instant
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

class GlobalPayrollServiceInvoiceDataReconcilerTest {

    private val transactionTemplateProvider = mock<TransactionTemplateProvider>()
    private val itemStoreDataProviderFactory = mock<ItemStoreDataProviderFactory>()
    private val invoiceDataProviderFactory = mock<InvoiceDataProviderFactory>()
    private val invoiceDiffReconcilerFactory = mock<InvoiceDiffReconcilerFactory>()
    private val globalPayrollServiceInvoiceCompanyPayableStorage = mock<CompanyPayableStorage>()
    private val advanceCollectionLedger = mock<AdvanceCollectionLedger>()
    private val billingServiceAdapter = mock<BillingServiceAdapter>()

    private val reconciler = GlobalPayrollServiceInvoiceDataReconciler(
        transactionTemplateProvider = transactionTemplateProvider,
        itemStoreDataProviderFactory = itemStoreDataProviderFactory,
        invoiceDataProviderFactory = invoiceDataProviderFactory,
        invoiceDiffReconcilerFactory = invoiceDiffReconcilerFactory,
        globalPayrollServiceInvoiceCompanyPayableStorage = globalPayrollServiceInvoiceCompanyPayableStorage,
        advanceCollectionLedger = advanceCollectionLedger,
        billingServiceAdapter = billingServiceAdapter,
    )

    @Test
    fun `transactionType should return GP_SERVICE_INVOICE`() {
        // When
        val result = reconciler.transactionType

        // Then
        assertEquals(TransactionType.GP_SERVICE_INVOICE, result)
    }

    @Test
    fun `handle should process successfully with normal items only`() {
        // Given
        val command = createTestCommand()
        val template = createTestTemplate()
        val latestItems = listOf(createTestPayableItem(LineItemType.TOTAL_PAYMENTS))
        val invoicedItems = emptyList<PayableItem>()
        val diffItems = listOf(createTestPayableItem(LineItemType.TOTAL_PAYMENTS))

        setupMocks(command, template, latestItems, invoicedItems, diffItems)

        val storageContextCaptor = argumentCaptor<CompanyPayableStorageContext>()
        whenever(globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(storageContextCaptor.capture())).thenReturn(1L)

        // When
        reconciler.handle(command)

        // Then
        verify(globalPayrollServiceInvoiceCompanyPayableStorage).exchangeAndStore(any())
        assertEquals(1, storageContextCaptor.firstValue.items.size)
        assertEquals("TOTAL_PAYMENTS", storageContextCaptor.firstValue.items[0].lineItemType)
    }

    @Test
    fun `handle should process adjustment items with ledger balance`() {
        // Given
        val billId = 123L
        val command = createTestCommand()
        val template = createTestTemplate()
        val latestItems = listOf(createTestPayableItem(LineItemType.TOTAL_PAYMENTS))
        val invoicedItems = emptyList<PayableItem>()
        val adjustmentItem = createTestPayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_TOTAL_PAYMENTS, billId)
        val normalItem = createTestPayableItem(LineItemType.TOTAL_PAYMENTS)
        val diffItems = listOf(adjustmentItem, normalItem)

        setupMocks(command, template, latestItems, invoicedItems, diffItems)

        val billedItem = createTestBilledItemWrapper(billId)
        val reservedAmount = AdvanceCollectionReservedAmount(
            reservedEntry = AdvanceCollectionEntry(
                transactionId = command.transactionId,
                balanceId = 123L,
                amount = BigDecimal("50.0"),
            ),
            currentBalance = createTestAdvanceCollectionBalance(),
        )

        whenever(billingServiceAdapter.getBillsByIds(setOf(billId))).thenReturn(listOf(billedItem))
        whenever(advanceCollectionLedger.hasBalance(eq(command.companyId), eq(command.entityId), any())).thenReturn(true)
        whenever(advanceCollectionLedger.tryReserve(eq(command.transactionId), eq(command.companyId), eq(456L), any(), any())).thenReturn(reservedAmount)

        val storageContextCaptor = argumentCaptor<CompanyPayableStorageContext>()
        whenever(globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(storageContextCaptor.capture())).thenReturn(1L)

        // When
        reconciler.handle(command)

        // Then
        verify(billingServiceAdapter).getBillsByIds(setOf(123L))
        verify(advanceCollectionLedger).hasBalance(eq(command.companyId), isNull(), any())
        verify(advanceCollectionLedger).tryReserve(eq(command.transactionId), eq(command.companyId), eq(456L), any(), any())
        verify(globalPayrollServiceInvoiceCompanyPayableStorage).exchangeAndStore(any())
        assertEquals(2, storageContextCaptor.firstValue.items.size)
    }

    @Test
    fun `handle should filter out adjustment items without ledger balance`() {
        // Given
        val command = createTestCommand()
        val template = createTestTemplate()
        val latestItems = listOf(createTestPayableItem(LineItemType.TOTAL_PAYMENTS))
        val invoicedItems = emptyList<PayableItem>()
        val adjustmentItem = createTestPayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_TOTAL_PAYMENTS, billId = 123L)
        val normalItem = createTestPayableItem(LineItemType.TOTAL_PAYMENTS)
        val diffItems = listOf(adjustmentItem, normalItem)

        setupMocks(command, template, latestItems, invoicedItems, diffItems)

        // Setup ledger to return no balance
        val billedItem = createTestBilledItemWrapper(123L)
        whenever(billingServiceAdapter.getBillsByIds(setOf(123L))).thenReturn(listOf(billedItem))
        whenever(advanceCollectionLedger.hasBalance(eq(command.companyId), eq(command.entityId), any())).thenReturn(false)

        val storageContextCaptor = argumentCaptor<CompanyPayableStorageContext>()
        whenever(globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(storageContextCaptor.capture())).thenReturn(1L)

        // When
        reconciler.handle(command)

        // Then
        verify(billingServiceAdapter).getBillsByIds(setOf(123L))
        verify(advanceCollectionLedger).hasBalance(eq(command.companyId), isNull(), any())
        verify(globalPayrollServiceInvoiceCompanyPayableStorage).exchangeAndStore(any())
        verify(advanceCollectionLedger, never()).tryReserve(any(), any(), any(), any(), any())
        assertEquals(1, storageContextCaptor.firstValue.items.size)
        assertEquals("TOTAL_PAYMENTS", storageContextCaptor.firstValue.items[0].lineItemType)
    }

    @Test
    fun `handle should throw exception when error occurs`() {
        // Given
        val command = createTestCommand()
        whenever(transactionTemplateProvider.findTemplateFor(any(), any())).thenThrow(RuntimeException("Test error"))

        // When & Then
        assertThrows(RuntimeException::class.java) {
            reconciler.handle(command)
        }
    }

    @Test
    fun `rollback should complete without errors`() {
        // Given
        val command = createTestCommand()

        // When & Then (should not throw)
        reconciler.rollback(command)
    }

    private fun createTestCommand(): InvoiceCommand {
        return InvoiceEngineTestDataFactory.createInvoiceCommand(
            transactionType = TransactionType.GP_SERVICE_INVOICE,
            companyId = 123L,
            transactionId = "test-transaction-123"
        )
    }

    private fun createTestTemplate(): TransactionTemplate {
        return mock<TransactionTemplate>().apply {
            whenever(lineItemTypes).thenReturn(
                listOf(
                    LineItemType.ONE_TIME_SETUP_FEE,
                    LineItemType.PAYSLIP_MINIMUM_COMMITMENT,
                    LineItemType.PER_PAYSLIP_FEE,
                    LineItemType.TOTAL_PAYMENTS,
                    LineItemType.STAT_FILING,
                    LineItemType.YEAR_END_DOCUMENTATION,
                    LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_ONE_TIME_SETUP_FEE,
                    LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PAYSLIP_MINIMUM_COMMITMENT,
                    LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PER_PAYSLIP_FEE,
                    LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_TOTAL_PAYMENTS,
                    LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_STAT_FILING,
                    LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_YEAR_END_DOCUMENTATION
                )
            )
            whenever(skipIsrGeneration).thenReturn(false)
        }
    }

    private fun createTestPayableItem(
        lineItemType: LineItemType,
        billId: Long? = null,
        billingDuration: DateRange? = null
    ): PayableItem {
        return PayableItem(
            month = 1,
            year = 2024,
            lineItemType = lineItemType.name,
            contractId = 100L,
            companyId = 123L,
            description = "Test description",
            amountInBaseCurrency = 100.0,
            baseCurrency = "USD",
            billableCost = 100.0,
            versionId = "v1.0",
            originalTimestamp = Instant.now().epochSecond,
            cycle = InvoiceCycle.MONTHLY,
            countryCode = "US",
            periodStartDate = LocalDate.of(2024, 1, 1),
            periodEndDate = LocalDate.of(2024, 1, 31),
            billId = billId?.toString(),
            billingDuration = billingDuration,
            entityId = 456L
        )
    }

    private fun createTestBilledItemWrapper(billId: Long): BilledItemWrapper {
        return BilledItemWrapper(
            billId = billId,
            transactionId = "test-transaction-id",
            companyId = 123L,
            entityId = 456L,
            companyProduct = CompanyProductWrapper(
                startDate = LocalDateTime.now(),
                endDate = LocalDateTime.now().plusMonths(1),
                lineCode = "ORDER_FORM_ADVANCE",
                dimensions = mapOf("OFFERING" to "EOR"),
                chargePolicy = ReferenceChargePolicy(emptyList())
            ),
            billingAmount = Amount(BigDecimal("100.00"), CurrencyCode.USD),
            billingDuration = Duration(
                startDate = LocalDate.now(),
                endDate = LocalDate.now().plusMonths(1)
            ),
            usageDuration = Duration(
                startDate = LocalDate.now(),
                endDate = LocalDate.now().plusMonths(1)
            ),
            billingTime = System.currentTimeMillis(),
            referenceBills = emptyList(),
            usages = emptyList()
        )
    }

    private fun setupMocks(
        command: InvoiceCommand,
        template: TransactionTemplate,
        latestItems: List<PayableItem>,
        invoicedItems: List<PayableItem>,
        diffItems: List<PayableItem>
    ) {
        val itemStoreDataProvider = mock<ItemStoreDataProvider>()
        val invoiceDataProvider = mock<InvoiceDataProvider>()
        val invoiceDiffReconciler = mock<InvoiceDiffReconciler>()

        whenever(transactionTemplateProvider.findTemplateFor(eq(command.transactionType), eq(command.companyId))).thenReturn(template)
        whenever(itemStoreDataProviderFactory.get(eq(TransactionType.GP_SERVICE_INVOICE))).thenReturn(itemStoreDataProvider)
        whenever(itemStoreDataProvider.fetchLatest(eq(command), any())).thenReturn(latestItems)
        whenever(invoiceDataProviderFactory.get(eq(TransactionType.GP_SERVICE_INVOICE))).thenReturn(invoiceDataProvider)
        whenever(invoiceDataProvider.fetchAndAggregateInvoiceItems(eq(command), isNull())).thenReturn(invoicedItems)
        whenever(invoiceDiffReconcilerFactory.get(eq(TransactionType.GP_SERVICE_INVOICE))).thenReturn(invoiceDiffReconciler)
        whenever(invoiceDiffReconciler.reconcileAndValidate(eq(command), eq(template), eq(invoicedItems), eq(latestItems))).thenReturn(diffItems)
    }

    @Test
    fun `handle should process empty latest items list`() {
        // Given
        val command = createTestCommand()
        val template = createTestTemplate()
        val latestItems = emptyList<PayableItem>()
        val invoicedItems = emptyList<PayableItem>()
        val diffItems = emptyList<PayableItem>()

        setupMocks(command, template, latestItems, invoicedItems, diffItems)

        val storageContextCaptor = argumentCaptor<CompanyPayableStorageContext>()
        whenever(globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(storageContextCaptor.capture())).thenReturn(1L)

        // When
        reconciler.handle(command)

        // Then
        verify(globalPayrollServiceInvoiceCompanyPayableStorage).exchangeAndStore(any())
        assertEquals(0, storageContextCaptor.firstValue.items.size)
    }

    @Test
    fun `handle should handle null billing date range`() {
        // Given
        val command = createTestCommand()
        val template = createTestTemplate()
        val latestItems = listOf(createTestPayableItem(LineItemType.TOTAL_PAYMENTS, billingDuration = null))
        val invoicedItems = emptyList<PayableItem>()
        val diffItems = listOf(createTestPayableItem(LineItemType.TOTAL_PAYMENTS))

        setupMocks(command, template, latestItems, invoicedItems, diffItems)

        val storageContextCaptor = argumentCaptor<CompanyPayableStorageContext>()
        whenever(globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(storageContextCaptor.capture())).thenReturn(1L)

        // When
        reconciler.handle(command)

        // Then
        verify(globalPayrollServiceInvoiceCompanyPayableStorage).exchangeAndStore(any())
        assertEquals(1, storageContextCaptor.firstValue.items.size)
    }

    @Test
    fun `handle should process adjustment items with zero reserved amount`() {
        // Given
        val command = createTestCommand()
        val template = createTestTemplate()
        val latestItems = listOf(createTestPayableItem(LineItemType.TOTAL_PAYMENTS))
        val invoicedItems = emptyList<PayableItem>()
        val adjustmentItem = createTestPayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_TOTAL_PAYMENTS, billId = 123L)
        val normalItem = createTestPayableItem(LineItemType.TOTAL_PAYMENTS)
        val diffItems = listOf(adjustmentItem, normalItem)

        setupMocks(command, template, latestItems, invoicedItems, diffItems)

        // Setup ledger to return zero reserved amount
        val billedItem = createTestBilledItemWrapper(123L)
        val reservedAmount = AdvanceCollectionReservedAmount(
            reservedEntry = AdvanceCollectionEntry(
                transactionId = command.transactionId,
                balanceId = 123L,
                amount = BigDecimal.ZERO,
            ),
            currentBalance = createTestAdvanceCollectionBalance(),
        )

        whenever(billingServiceAdapter.getBillsByIds(setOf(123L))).thenReturn(listOf(billedItem))
        whenever(advanceCollectionLedger.hasBalance(eq(command.companyId), eq(command.entityId), any())).thenReturn(true)
        whenever(advanceCollectionLedger.tryReserve(eq(command.transactionId), eq(command.companyId), eq(456L), any(), any())).thenReturn(reservedAmount)

        val storageContextCaptor = argumentCaptor<CompanyPayableStorageContext>()
        whenever(globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(storageContextCaptor.capture())).thenReturn(1L)

        // When
        reconciler.handle(command)

        // Then
        verify(billingServiceAdapter).getBillsByIds(setOf(123L))
        verify(advanceCollectionLedger).hasBalance(eq(command.companyId), isNull(), any())
        verify(advanceCollectionLedger).tryReserve(eq(command.transactionId), eq(command.companyId), eq(456L), any(), any())
        verify(globalPayrollServiceInvoiceCompanyPayableStorage).exchangeAndStore(any())
        // Should only have the normal item since adjustment item has zero reserved amounts
        assertEquals(1, storageContextCaptor.firstValue.items.size)
        assertEquals("TOTAL_PAYMENTS", storageContextCaptor.firstValue.items[0].lineItemType)
    }

    @Test
    fun `handle should process adjustment items without billId`() {
        // Given
        val command = createTestCommand()
        val template = createTestTemplate()
        val latestItems = listOf(createTestPayableItem(LineItemType.TOTAL_PAYMENTS))
        val invoicedItems = emptyList<PayableItem>()
        val adjustmentItem = createTestPayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_TOTAL_PAYMENTS, billId = null)
        val normalItem = createTestPayableItem(LineItemType.TOTAL_PAYMENTS)
        val diffItems = listOf(adjustmentItem, normalItem)

        setupMocks(command, template, latestItems, invoicedItems, diffItems)

        whenever(billingServiceAdapter.getBillsByIds(emptySet())).thenReturn(emptyList())

        val storageContextCaptor = argumentCaptor<CompanyPayableStorageContext>()
        whenever(globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(storageContextCaptor.capture())).thenReturn(1L)

        // When
        reconciler.handle(command)

        // Then
        verify(billingServiceAdapter).getBillsByIds(emptySet())
        verify(globalPayrollServiceInvoiceCompanyPayableStorage).exchangeAndStore(any())
        verify(advanceCollectionLedger, never()).hasBalance(any(), any(), any())
        verify(advanceCollectionLedger, never()).tryReserve(any(), any(), any(), any(), any())
        // Should only have the normal item since adjustment item has no billId
        assertEquals(1, storageContextCaptor.firstValue.items.size)
        assertEquals("TOTAL_PAYMENTS", storageContextCaptor.firstValue.items[0].lineItemType)
    }

    @Test
    fun `handle should process multiple adjustment item types`() {
        // Given
        val command = createTestCommand()
        val template = createTestTemplate()
        val latestItems = listOf(createTestPayableItem(LineItemType.TOTAL_PAYMENTS))
        val invoicedItems = emptyList<PayableItem>()
        val adjustmentItem1 = createTestPayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_TOTAL_PAYMENTS, billId = 123L)
        val adjustmentItem2 = createTestPayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_STAT_FILING, billId = 124L)
        val normalItem = createTestPayableItem(LineItemType.TOTAL_PAYMENTS)
        val diffItems = listOf(adjustmentItem1, adjustmentItem2, normalItem)

        setupMocks(command, template, latestItems, invoicedItems, diffItems)

        // Setup ledger for both adjustment items
        val billedItem1 = createTestBilledItemWrapper(123L)
        val billedItem2 = createTestBilledItemWrapper(124L)
        val reservedAmount = AdvanceCollectionReservedAmount(
            reservedEntry = AdvanceCollectionEntry(
                transactionId = command.transactionId,
                balanceId = 123L,
                amount = BigDecimal("25.0"),
            ),
            currentBalance = createTestAdvanceCollectionBalance(),
        )

        whenever(billingServiceAdapter.getBillsByIds(setOf(123L, 124L))).thenReturn(listOf(billedItem1, billedItem2))
        whenever(advanceCollectionLedger.hasBalance(eq(command.companyId), eq(command.entityId), any())).thenReturn(true)
        whenever(advanceCollectionLedger.tryReserve(eq(command.transactionId), eq(command.companyId), eq(456L), any(), any())).thenReturn(reservedAmount)

        val storageContextCaptor = argumentCaptor<CompanyPayableStorageContext>()
        whenever(globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(storageContextCaptor.capture())).thenReturn(1L)

        // When
        reconciler.handle(command)

        // Then
        verify(billingServiceAdapter).getBillsByIds(setOf(123L, 124L))
        verify(globalPayrollServiceInvoiceCompanyPayableStorage).exchangeAndStore(any())
        verify(advanceCollectionLedger, times(2)).hasBalance(eq(command.companyId), isNull(), any())
        verify(advanceCollectionLedger, times(2)).tryReserve(eq(command.transactionId), eq(command.companyId), eq(456L), any(), any())
        // Should have all three items (2 adjustment + 1 normal)
        assertEquals(3, storageContextCaptor.firstValue.items.size)
    }

    @Test
    fun `buildAdvanceCollectionProduct should return null when billId not found`() {
        // Given
        val command = createTestCommand()
        val template = createTestTemplate()
        val latestItems = listOf(createTestPayableItem(LineItemType.TOTAL_PAYMENTS))
        val invoicedItems = emptyList<PayableItem>()
        val adjustmentItem = createTestPayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_TOTAL_PAYMENTS, billId = 999L)
        val normalItem = createTestPayableItem(LineItemType.TOTAL_PAYMENTS)
        val diffItems = listOf(adjustmentItem, normalItem)

        setupMocks(command, template, latestItems, invoicedItems, diffItems)

        // Return an empty list for getBillsByIds to simulate billId not found
        whenever(billingServiceAdapter.getBillsByIds(setOf(999L))).thenReturn(emptyList())

        val storageContextCaptor = argumentCaptor<CompanyPayableStorageContext>()
        whenever(globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(storageContextCaptor.capture())).thenReturn(1L)

        // When
        reconciler.handle(command)

        // Then
        verify(billingServiceAdapter).getBillsByIds(setOf(999L))
        verify(globalPayrollServiceInvoiceCompanyPayableStorage).exchangeAndStore(any())
        verify(advanceCollectionLedger, never()).hasBalance(any(), any(), any())
        verify(advanceCollectionLedger, never()).tryReserve(any(), any(), any(), any(), any())
        // Should only have the normal item since adjustment item's billId was not found
        assertEquals(1, storageContextCaptor.firstValue.items.size)
        assertEquals("TOTAL_PAYMENTS", storageContextCaptor.firstValue.items[0].lineItemType)
    }

    @Test
    fun `handle should calculate bounding date range correctly`() {
        // Given
        val command = createTestCommand()
        val template = createTestTemplate()
        val startDate = LocalDateTime.of(2024, 1, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 1, 31, 23, 59)
        val item1 = createTestPayableItem(LineItemType.TOTAL_PAYMENTS, billingDuration = DateRange(startDate, endDate.minusDays(5)))
        val item2 = createTestPayableItem(LineItemType.TOTAL_PAYMENTS, billingDuration = DateRange(startDate.plusDays(5), endDate))
        val latestItems = listOf(item1, item2)
        val invoicedItems = emptyList<PayableItem>()
        val diffItems = listOf(item1, item2)

        // Custom setup for this test to handle the calculated bounding date range
        val itemStoreDataProvider = mock<ItemStoreDataProvider>()
        val mockInvoiceDataProvider = mock<InvoiceDataProvider>()
        val invoiceDiffReconciler = mock<InvoiceDiffReconciler>()
        val expectedBoundingRange = DateRange(startDate, endDate)

        whenever(transactionTemplateProvider.findTemplateFor(eq(command.transactionType), eq(command.companyId))).thenReturn(template)
        whenever(itemStoreDataProviderFactory.get(eq(TransactionType.GP_SERVICE_INVOICE))).thenReturn(itemStoreDataProvider)
        whenever(itemStoreDataProvider.fetchLatest(eq(command), any())).thenReturn(latestItems)
        whenever(invoiceDataProviderFactory.get(eq(TransactionType.GP_SERVICE_INVOICE))).thenReturn(mockInvoiceDataProvider)
        whenever(mockInvoiceDataProvider.fetchAndAggregateInvoiceItems(eq(command), eq(expectedBoundingRange))).thenReturn(invoicedItems)
        whenever(invoiceDiffReconcilerFactory.get(eq(TransactionType.GP_SERVICE_INVOICE))).thenReturn(invoiceDiffReconciler)
        whenever(invoiceDiffReconciler.reconcileAndValidate(eq(command), eq(template), eq(invoicedItems), eq(latestItems))).thenReturn(diffItems)

        val storageContextCaptor = argumentCaptor<CompanyPayableStorageContext>()
        whenever(globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(storageContextCaptor.capture())).thenReturn(1L)

        // When
        reconciler.handle(command)

        // Then
        verify(invoiceDataProviderFactory).get(TransactionType.GP_SERVICE_INVOICE)
        assertEquals(2, storageContextCaptor.firstValue.items.size)
    }

    private fun createTestAdvanceCollectionBalance(): AdvanceCollectionBalance {
        val metadata = AdvanceCollectionBalanceMetadata(
            transactionId = "test-transaction-123",
            invoiceNo = "INV-123",
            referenceLine = AdvanceCollectionInvoiceLine(
                itemType = "ORDER_FORM_ADVANCE",
                taxReference = AdvanceCollectionInvoiceLineTaxReference(
                    taxCode = "ZR-SG",
                    taxRate = "0.0",
                    taxType = "PERCENTAGE",
                    taxAmount = BigDecimal.ZERO
                ),
                unitPrice = BigDecimal("100.00"),
                currency = "USD",
                description = "Test advance collection",
                payableItemIds = setOf("test-item-1")
            )
        )

        return AdvanceCollectionBalance(
            id = 123L,
            companyId = 123L,
            entityId = 456L,
            advanceCollectionProduct = AdvanceCollectionProduct(
                lineCode = "ORDER_FORM_ADVANCE",
                dimensions = mapOf("OFFERING" to "EOR"),
                targetType = "COMPANY_PRODUCT"
            ),
            balance = Amount(BigDecimal("100.00"), CurrencyCode.USD),
            metadata = metadata
        )
    }
}
