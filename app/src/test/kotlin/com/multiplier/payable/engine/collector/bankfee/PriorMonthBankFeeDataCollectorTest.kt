package com.multiplier.payable.engine.collector.bankfee

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.DefaultProcessedCollectorInput
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.domain.aggregates.MonthYearDuration
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CurrencyCode
import io.mockk.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class PriorMonthBankFeeDataCollectorTest {

    private val dataCollectorInputProcessor: DataCollectorInputProcessor<DefaultProcessedCollectorInput> = mockk()
    private val bankFeeCalculatorService: BankFeeCalculatorService = mockk()
    private val payableItemStoreService: PayableItemStoreService = mockk()
    private val objectMapper: ObjectMapper = mockk()

    private lateinit var priorMonthBankFeeDataCollector: PriorMonthBankFeeDataCollector

    private val transactionId = "test-transaction-123"
    private val companyId = 1001L
    private val entityId = 2001L
    private val amount = BigDecimal("150.00")
    private val currency = CurrencyCode.USD

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        priorMonthBankFeeDataCollector = PriorMonthBankFeeDataCollector(
            dataCollectorInputProcessor,
            bankFeeCalculatorService,
            payableItemStoreService,
            objectMapper
        )
    }

    @Test
    fun `getSupportedType should return BANK_FEE`() {
        // When
        val result = priorMonthBankFeeDataCollector.getSupportedType()

        // Then
        assertEquals(LineItemType.BANK_FEE, result)
    }

    @Test
    fun `handle should process bank fee when shouldAddToInvoice is true`() {
        // Given
        val command = createInvoiceCommand()
        val processedInput = createProcessedInput()
        val calculatorResponse = createBankFeeCalculatorResponse(shouldAddToInvoice = true)
        val payableItemJson = """{"companyId":1001,"amount":{"value":150.00,"currency":"USD"}}"""

        every { dataCollectorInputProcessor.process(command) } returns processedInput
        every { bankFeeCalculatorService.calculateBankFeeRecovery(any()) } returns calculatorResponse
        every { objectMapper.writeValueAsString(calculatorResponse) } returns payableItemJson
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } returns listOf()

        // When
        priorMonthBankFeeDataCollector.handle(command)

        // Then
        verify { dataCollectorInputProcessor.process(command) }
        verify { bankFeeCalculatorService.calculateBankFeeRecovery(any()) }
        verify { payableItemStoreService.saveAndIgnoreDuplication(any()) }
        verify { objectMapper.writeValueAsString(calculatorResponse) }
    }

    @Test
    fun `handle should not save payable item when shouldAddToInvoice is false`() {
        // Given
        val command = createInvoiceCommand()
        val processedInput = createProcessedInput()
        val calculatorResponse = createBankFeeCalculatorResponse(shouldAddToInvoice = false)

        every { dataCollectorInputProcessor.process(command) } returns processedInput
        every { bankFeeCalculatorService.calculateBankFeeRecovery(any()) } returns calculatorResponse

        // When
        priorMonthBankFeeDataCollector.handle(command)

        // Then
        verify { dataCollectorInputProcessor.process(command) }
        verify { bankFeeCalculatorService.calculateBankFeeRecovery(any()) }
        verify(exactly = 0) { payableItemStoreService.saveAndIgnoreDuplication(any()) }
        verify(exactly = 0) { objectMapper.writeValueAsString(any()) }
    }

    @Test
    fun `handle should process multiple companies`() {
        // Given
        val command = createInvoiceCommand()
        val processedInput = createProcessedInputWithMultipleCompanies()
        val calculatorResponse = createBankFeeCalculatorResponse(shouldAddToInvoice = true)
        val payableItemJson = """{"companyId":1001,"amount":{"value":150.00,"currency":"USD"}}"""

        every { dataCollectorInputProcessor.process(command) } returns processedInput
        every { bankFeeCalculatorService.calculateBankFeeRecovery(any()) } returns calculatorResponse
        every { objectMapper.writeValueAsString(calculatorResponse) } returns payableItemJson
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } returns listOf()

        // When
        priorMonthBankFeeDataCollector.handle(command)

        // Then
        verify { dataCollectorInputProcessor.process(command) }
        verify(exactly = 2) { bankFeeCalculatorService.calculateBankFeeRecovery(any()) }
        verify { payableItemStoreService.saveAndIgnoreDuplication(any()) }
    }

    @Test
    fun `handle should continue processing other companies when one fails`() {
        // Given
        val command = createInvoiceCommand()
        val processedInput = createProcessedInputWithMultipleCompanies()
        val calculatorResponse = createBankFeeCalculatorResponse(shouldAddToInvoice = true)
        val payableItemJson = """{"companyId":1001,"amount":{"value":150.00,"currency":"USD"}}"""

        every { dataCollectorInputProcessor.process(command) } returns processedInput
        every { bankFeeCalculatorService.calculateBankFeeRecovery(match { it.companyId == 1001L }) } throws RuntimeException("Service error")
        every { bankFeeCalculatorService.calculateBankFeeRecovery(match { it.companyId == 1002L }) } returns calculatorResponse
        every { objectMapper.writeValueAsString(calculatorResponse) } returns payableItemJson
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } returns listOf()

        // When
        priorMonthBankFeeDataCollector.handle(command)

        // Then
        verify { dataCollectorInputProcessor.process(command) }
        verify(exactly = 2) { bankFeeCalculatorService.calculateBankFeeRecovery(any()) }
        verify { payableItemStoreService.saveAndIgnoreDuplication(any()) }
    }

    @Test
    fun `handle should not save when no payable items are created`() {
        // Given
        val command = createInvoiceCommand()
        val processedInput = createProcessedInput()
        val calculatorResponse = createBankFeeCalculatorResponse(shouldAddToInvoice = false)

        every { dataCollectorInputProcessor.process(command) } returns processedInput
        every { bankFeeCalculatorService.calculateBankFeeRecovery(any()) } returns calculatorResponse

        // When
        priorMonthBankFeeDataCollector.handle(command)

        // Then
        verify { dataCollectorInputProcessor.process(command) }
        verify { bankFeeCalculatorService.calculateBankFeeRecovery(any()) }
        verify(exactly = 0) { payableItemStoreService.saveAndIgnoreDuplication(any()) }
    }
    @Test
    fun `createBankFeePayableItemStoreDto should create correct PayableItemStoreDto`() {
        // Given
        val command = createInvoiceCommand()
        val processedInput = createProcessedInput()
        val calculatorResponse = createBankFeeCalculatorResponse(shouldAddToInvoice = true)
        val payableItemJson = """{"companyId":1001,"amount":{"value":150.00,"currency":"USD"}}"""

        every { dataCollectorInputProcessor.process(command) } returns processedInput
        every { bankFeeCalculatorService.calculateBankFeeRecovery(any()) } returns calculatorResponse
        every { objectMapper.writeValueAsString(calculatorResponse) } returns payableItemJson
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } answers {
            val items = firstArg<List<PayableItemStoreDto>>()
            // Verify the PayableItemStoreDto properties
            val item = items.first()
            assertEquals(amount.toDouble(), item.amount)
            assertEquals(currency, item.currency)
            assertEquals(companyId, item.companyId)
            assertEquals(-1L, item.contractId)
            assertEquals(transactionId, item.transactionId)
            assertEquals(LineItemType.BANK_FEE, item.itemType)
            assertEquals(payableItemJson, item.itemData)
            assertTrue(item.versionId.isNotEmpty())
            assertTrue(item.originalTimestamp > 0)
            listOf()
        }

        // When
        priorMonthBankFeeDataCollector.handle(command)

        // Then
        verify { payableItemStoreService.saveAndIgnoreDuplication(any()) }
    }

    @Test
    fun `handle should work with different currency codes`() {
        // Given
        val command = createInvoiceCommand()
        val processedInput = createProcessedInput()
        val calculatorResponse = BankFeeCalculatorResponse(
            companyId = companyId,
            amount = Amount(BigDecimal("25.75"), CurrencyCode.EUR),
            startDate = LocalDate.of(2024, 5, 1),
            endDate = LocalDate.of(2024, 5, 31),
            shouldAddToInvoice = true,
            threshold = 20.0,
            entityId = 456L
        )
        val payableItemJson = """{"currency":"EUR"}"""

        every { dataCollectorInputProcessor.process(command) } returns processedInput
        every { bankFeeCalculatorService.calculateBankFeeRecovery(any()) } returns calculatorResponse
        every { objectMapper.writeValueAsString(calculatorResponse) } returns payableItemJson
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } answers {
            val items = firstArg<List<PayableItemStoreDto>>()
            val item = items.first()
            assertEquals(25.75, item.amount)
            assertEquals(CurrencyCode.EUR, item.currency)
            listOf()
        }

        // When
        priorMonthBankFeeDataCollector.handle(command)

        // Then
        verify { payableItemStoreService.saveAndIgnoreDuplication(any()) }
    }

    @Test
    fun `handle should work with zero amount when shouldAddToInvoice is true`() {
        // Given
        val command = createInvoiceCommand()
        val processedInput = createProcessedInput()
        val calculatorResponse = BankFeeCalculatorResponse(
            companyId = companyId,
            amount = Amount(BigDecimal.ZERO, CurrencyCode.USD),
            startDate = LocalDate.of(2024, 5, 1),
            endDate = LocalDate.of(2024, 5, 31),
            shouldAddToInvoice = true,
            threshold = 0.0,
            entityId = 456L
        )
        val payableItemJson = """{"amount":0}"""

        every { dataCollectorInputProcessor.process(command) } returns processedInput
        every { bankFeeCalculatorService.calculateBankFeeRecovery(any()) } returns calculatorResponse
        every { objectMapper.writeValueAsString(calculatorResponse) } returns payableItemJson
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } answers {
            val items = firstArg<List<PayableItemStoreDto>>()
            val item = items.first()
            assertEquals(0.0, item.amount)
            listOf()
        }

        // When
        priorMonthBankFeeDataCollector.handle(command)

        // Then
        verify { payableItemStoreService.saveAndIgnoreDuplication(any()) }
    }

    @Test
    fun `handle should work when entityId is null in command`() {
        // Given
        val command = mockk<InvoiceCommand> {
            every { transactionId } returns <EMAIL>
            every { entityId } returns null
            every { companyId } returns <EMAIL>
            every { dateRange } returns DateRange(
                LocalDateTime.of(2024, 5, 1, 0, 0),
                LocalDateTime.of(2024, 5, 31, 23, 59, 59)
            )
        }
        val processedInput = createProcessedInput()
        val calculatorResponse = createBankFeeCalculatorResponse(shouldAddToInvoice = true)
        val payableItemJson = """{"companyId":1001}"""

        every { dataCollectorInputProcessor.process(command) } returns processedInput
        every { bankFeeCalculatorService.calculateBankFeeRecovery(any()) } returns calculatorResponse
        every { objectMapper.writeValueAsString(calculatorResponse) } returns payableItemJson
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } returns listOf()

        // When
        priorMonthBankFeeDataCollector.handle(command)

        // Then
        val calculatorInputSlot = slot<BankFeeCalculatorInput>()
        verify { bankFeeCalculatorService.calculateBankFeeRecovery(capture(calculatorInputSlot)) }

        val capturedInput = calculatorInputSlot.captured
        assertEquals(null, capturedInput.entityId)
    }

    @Test
    fun `handle should verify calculator input parameters are correct`() {
        // Given
        val command = createInvoiceCommand()
        val processedInput = createProcessedInput()
        val calculatorResponse = createBankFeeCalculatorResponse(shouldAddToInvoice = true)
        val payableItemJson = """{"companyId":1001}"""

        every { dataCollectorInputProcessor.process(command) } returns processedInput
        every { bankFeeCalculatorService.calculateBankFeeRecovery(any()) } returns calculatorResponse
        every { objectMapper.writeValueAsString(calculatorResponse) } returns payableItemJson
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } returns listOf()

        // When
        priorMonthBankFeeDataCollector.handle(command)

        // Then
        val calculatorInputSlot = slot<BankFeeCalculatorInput>()
        verify { bankFeeCalculatorService.calculateBankFeeRecovery(capture(calculatorInputSlot)) }

        val capturedInput = calculatorInputSlot.captured
        assertEquals(companyId, capturedInput.companyId)
        assertEquals(LocalDate.of(2024, 5, 1), capturedInput.startDate)
        assertEquals(LocalDate.of(2024, 5, 31), capturedInput.endDate)
        assertEquals(entityId, capturedInput.entityId)
    }

    private fun createInvoiceCommand(): InvoiceCommand = mockk {
        every { transactionId } returns <EMAIL>
        every { companyId } returns <EMAIL>
        every { entityId } returns <EMAIL>
        every { dateRange } returns DateRange(
            LocalDateTime.of(2024, 5, 1, 0, 0),
            LocalDateTime.of(2024, 5, 31, 23, 59, 59)
        )
    }

    private fun createProcessedInput(): DefaultProcessedCollectorInput {
        val monthYearDuration = MonthYearDuration(
            from = MonthYear(5, 2024),
            to = MonthYear(5, 2024)
        )
        return DefaultProcessedCollectorInput(
            transactionId = transactionId,
            transactionDate = LocalDateTime.of(2024, 5, 15, 10, 0),
            timeQueryDuration = monthYearDuration,
            companyIds = setOf(companyId)
        )
    }

    private fun createProcessedInputWithMultipleCompanies(): DefaultProcessedCollectorInput {
        val monthYearDuration = MonthYearDuration(
            from = MonthYear(5, 2024),
            to = MonthYear(5, 2024)
        )
        return DefaultProcessedCollectorInput(
            transactionId = transactionId,
            transactionDate = LocalDateTime.of(2024, 5, 15, 10, 0),
            timeQueryDuration = monthYearDuration,
            companyIds = setOf(1001L, 1002L)
        )
    }

    private fun createBankFeeCalculatorResponse(shouldAddToInvoice: Boolean): BankFeeCalculatorResponse {
        return BankFeeCalculatorResponse(
            companyId = companyId,
            amount = Amount(amount, currency),
            startDate = LocalDate.of(2024, 5, 1),
            endDate = LocalDate.of(2024, 5, 31),
            shouldAddToInvoice = shouldAddToInvoice,
            threshold = 10.0,
            entityId = 456L
        )
    }
}
