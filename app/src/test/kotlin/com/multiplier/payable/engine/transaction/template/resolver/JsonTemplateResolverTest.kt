package com.multiplier.payable.engine.transaction.template.resolver

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.formatter.DataFormatter
import com.multiplier.payable.engine.formatter.template.resolver.DataFormatterTemplateResolver
import com.multiplier.payable.engine.formatter.template.resolver.TextPlaceholderResolver
import com.multiplier.payable.engine.splitter.ItemSplitter
import com.multiplier.payable.engine.splitter.template.resolver.SelectorSplitterTemplateResolver
import com.multiplier.payable.engine.transaction.template.FinancialTransactionFormatterContext
import com.multiplier.payable.engine.transaction.template.FxConfig
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.fail
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import java.time.LocalDateTime

@ExtendWith(MockitoExtension::class)
class JsonTemplateResolverTest {
    @Mock
    private lateinit var dataFormatter: DataFormatter

    @Mock lateinit var itemSplitter: ItemSplitter

    @Mock
    private lateinit var dataFormatterTemplateResolver: DataFormatterTemplateResolver

    @Mock
    private lateinit var selectorSplitterTemplateResolver: SelectorSplitterTemplateResolver

    @Mock
    private lateinit var placeholdersResolver: TextPlaceholderResolver

    @InjectMocks
    private lateinit var jsonTemplateResolver: JsonTemplateResolver

    private val period =
        DateRange(
            startDate = LocalDateTime.of(2023, 1, 1, 0, 0),
            endDate = LocalDateTime.of(2023, 1, 31, 0, 0),
        )

    @Test
    fun `should set the line items to the template object from the json`() {
        val json =
            JsonTemplateResolverTest::class.java
                .getResource("/transaction-template/gp-transaction-template.json")
                ?.readText()
                ?: fail("Please check the gp-transaction-template.json exists in the resource folder")

        val context =
            FinancialTransactionFormatterContext(
                referenceDate = period.endDate,
                payableItems = listOf(),
                period = period,
            )
        whenever(dataFormatterTemplateResolver.resolve(any())).thenReturn(dataFormatter)
        whenever(selectorSplitterTemplateResolver.resolve(any())).thenReturn(itemSplitter)
        whenever(
            placeholdersResolver.resolve(
                "getMonthInThreeLetters() + '''' + getYearInTwoDigits() + ' Payroll - ' + country",
                context,
            ),
        ).thenReturn("Jan'2023 Payroll - IND")
        val template = jsonTemplateResolver.resolve(json)

        assertThat(template.lineItemTypes).contains(LineItemType.PEO_SALARY_DISBURSEMENT)
        assertThat(template.getReference(context)).isEqualTo("Jan'2023 Payroll - IND")
        assertThat(template.dataFormatter).isEqualTo(dataFormatter)
        assertThat(template.mergedTransactionTypes).isNotEmpty()
        assertThat(template.mergedTransactionTypes).contains(TransactionType.FIRST_INVOICE)
        assertThat(template.lookUpTransactionTypes).isNotEmpty()
        assertThat(template.lookUpTransactionTypes).contains(TransactionType.SECOND_INVOICE)
        assertThat(template.fxConfig).isNotNull
        assertThat(template.fxConfig).isEqualTo(
            FxConfig("INTERNALLY_FIXED", "RAW_RATE", 1.0, 2, "HALF_UP"),
        )
    }

    @Test
    fun `when no splitters defined should still resolve the template`() {
        val json =
            JsonTemplateResolverTest::class.java
                .getResource("/transaction-template/gp-transaction-template-no-splitters.json")
                ?.readText()
                ?: fail("Please check the gp-transaction-template.json exists in the resource folder")

        val context =
            FinancialTransactionFormatterContext(
                referenceDate = period.endDate,
                payableItems = listOf(),
                period = period,
            )
        whenever(dataFormatterTemplateResolver.resolve(any())).thenReturn(dataFormatter)
        whenever(selectorSplitterTemplateResolver.resolve(null)).thenReturn(itemSplitter)
        whenever(
            placeholdersResolver.resolve(
                "getMonthInThreeLetters() + '''' + getYearInTwoDigits() + ' Payroll - ' + country",
                context,
            ),
        ).thenReturn("Jan'2023 Payroll - IND")
        val template = jsonTemplateResolver.resolve(json)

        assertThat(template.lineItemTypes).contains(LineItemType.PEO_SALARY_DISBURSEMENT)
        assertThat(template.getReference(context)).isEqualTo("Jan'2023 Payroll - IND")
        assertThat(template.dataFormatter).isEqualTo(dataFormatter)
        assertThat(template.itemSplitter).isEqualTo(itemSplitter)
    }
}
