package com.multiplier.payable.engine.invoice.strategy.status

import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.InvoiceStatus
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

import org.junit.jupiter.api.Assertions.*

class DefaultStatusStrategyTest {
    private val defaultStatusStrategy = DefaultStatusStrategy()

    @Test
    fun `resolveStatus default case`() {
        val command = mockk<InvoiceCommand>(){
            every { <EMAIL> } returns com.multiplier.payable.engine.domain.entities.TransactionType.FIRST_INVOICE
        }
        assertEquals(InvoiceStatus.DRAFT, defaultStatusStrategy.resolveStatus(command))
    }

    @Test
    fun `resolveStatus freelancer invoice`() {
        val command = mockk<InvoiceCommand>{
            every { <EMAIL> } returns com.multiplier.payable.engine.domain.entities.TransactionType.FREELANCER_INVOICE
        }
        assertEquals(InvoiceStatus.AUTHORIZED, defaultStatusStrategy.resolveStatus(command))
    }
}