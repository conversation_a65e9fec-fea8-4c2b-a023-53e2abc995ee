package com.multiplier.payable.engine.collector.offcycle

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.OffCycleDataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.ProcessedOffCycleCollectorInput
import com.multiplier.payable.engine.collector.payroll.ContractPayroll
import com.multiplier.payable.engine.collector.payroll.ContractPayrollService
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.domain.aggregates.MonthYearDuration
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.engine.testutils.InvoiceEngineTestDataFactory
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CurrencyCode
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Instant
import java.time.LocalDateTime

@ExtendWith(MockKExtension::class)
class OffCyclePayrollCostDataCollectorTest {

    @MockK
    private lateinit var offCycleDataCollectorInputProcessor: OffCycleDataCollectorInputProcessor

    @MockK
    private lateinit var contractPayrollService: ContractPayrollService

    @MockK
    private lateinit var payableItemStoreService: PayableItemStoreService

    @MockK
    private lateinit var offCyclePayrollPaymentPayableItemStoreNormalizer: OffCyclePayrollPaymentPayableItemStoreNormalizer

    @InjectMockKs
    private lateinit var dataCollector: OffCyclePayrollCostDataCollector

    @Test
    fun `getSupportedType should return PAYROLL_OFFCYCLE_COST`() {
        // WHEN
        val result = dataCollector.getSupportedType()

        // THEN
        assertEquals(LineItemType.PAYROLL_OFFCYCLE_COST, result)
    }

    @Test
    fun `given valid command when handle then processes input and collects off-cycle payrolls`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            companyId = 123L,
            transactionId = "TX123456789"
        )

        val processedInput = ProcessedOffCycleCollectorInput(
            transactionId = command.transactionId,
            transactionDate = command.transactionDate,
            timeQueryDuration = MonthYearDuration(MonthYear(1, 2024), MonthYear(1, 2024)),
            companyIds = setOf(command.companyId)
        )

        val contractPayroll1 = mockk<ContractPayroll> {
            every { type } returns ContractType.EMPLOYEE
            every { isOffCycle } returns true
            every { hasBillingException } returns false
        }
        val contractPayroll2 = mockk<ContractPayroll> {
            every { type } returns ContractType.EMPLOYEE
            every { isOffCycle } returns true
            every { hasBillingException } returns null
        }
        val contractPayroll3 = mockk<ContractPayroll> {
            every { type } returns ContractType.EMPLOYEE
            every { isOffCycle } returns false
            every { hasBillingException } returns false
        }
        val contractPayroll4 = mockk<ContractPayroll> {
            every { type } returns ContractType.EMPLOYEE
            every { isOffCycle } returns true
            every { hasBillingException } returns true
        }

        val allPayrolls = listOf(contractPayroll1, contractPayroll2, contractPayroll3, contractPayroll4)
        val expectedFilteredPayrolls = listOf(contractPayroll1, contractPayroll2)

        val payableItemStoreDto1 = mockk<PayableItemStoreDto> {
            every { amount } returns 100.0
        }
        val payableItemStoreDto2 = mockk<PayableItemStoreDto> {
            every { amount } returns 0.0
        }
        val payableItemStoreDto3 = mockk<PayableItemStoreDto> {
            every { amount } returns 50.0
        }

        every { offCycleDataCollectorInputProcessor.process(command) } returns processedInput
        every { contractPayrollService.getContractPayrolls(
            companyIds = listOf(command.companyId),
            month = 1,
            year = 2024
        ) } returns allPayrolls
        every { offCyclePayrollPaymentPayableItemStoreNormalizer.normalize(contractPayroll1, any()) } returns payableItemStoreDto1
        every { offCyclePayrollPaymentPayableItemStoreNormalizer.normalize(contractPayroll2, any()) } returns payableItemStoreDto2
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } returns emptyList()

        // WHEN
        dataCollector.handle(command)

        // THEN
        verify { offCycleDataCollectorInputProcessor.process(command) }
        verify { contractPayrollService.getContractPayrolls(
            companyIds = listOf(command.companyId),
            month = 1,
            year = 2024
        ) }

        // Verify that only off-cycle payrolls without billing exceptions are processed
        verify { offCyclePayrollPaymentPayableItemStoreNormalizer.normalize(contractPayroll1, any()) }
        verify { offCyclePayrollPaymentPayableItemStoreNormalizer.normalize(contractPayroll2, any()) }
        verify(exactly = 0) { offCyclePayrollPaymentPayableItemStoreNormalizer.normalize(contractPayroll3, any()) }
        verify(exactly = 0) { offCyclePayrollPaymentPayableItemStoreNormalizer.normalize(contractPayroll4, any()) }

        // Verify that all normalized items are saved (including zero amounts)
        val savedDtosSlot = slot<List<PayableItemStoreDto>>()
        verify { payableItemStoreService.saveAndIgnoreDuplication(capture(savedDtosSlot)) }
        assertEquals(2, savedDtosSlot.captured.size)
        assertEquals(100.0, savedDtosSlot.captured[0].amount)
        assertEquals(0.0, savedDtosSlot.captured[1].amount)

        // Verify that processedInput is properly set
        assertEquals(LineItemType.PAYROLL_OFFCYCLE_COST, processedInput.lineItemType)
        assert(processedInput.originalTimestamp.isAfter(Instant.EPOCH))
    }

    @Test
    fun `given command with multiple company ids when handle then processes all companies`() {
        // GIVEN
        val companyIds = setOf(123L, 456L, 789L)
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            companyId = 123L,
            transactionId = "TX123456789"
        )

        val processedInput = ProcessedOffCycleCollectorInput(
            transactionId = command.transactionId,
            transactionDate = command.transactionDate,
            timeQueryDuration = MonthYearDuration(MonthYear(1, 2024), MonthYear(1, 2024)),
            companyIds = companyIds
        )

        every { offCycleDataCollectorInputProcessor.process(command) } returns processedInput
        every { contractPayrollService.getContractPayrolls(
            companyIds = companyIds.toList(),
            month = 1,
            year = 2024
        ) } returns emptyList()
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } returns emptyList()

        // WHEN
        dataCollector.handle(command)

        // THEN
        verify { contractPayrollService.getContractPayrolls(
            companyIds = companyIds.toList(),
            month = 1,
            year = 2024
        ) }
    }

    @Test
    fun `given no off-cycle payrolls when handle then saves empty list`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

        val processedInput = ProcessedOffCycleCollectorInput(
            transactionId = command.transactionId,
            transactionDate = command.transactionDate,
            timeQueryDuration = MonthYearDuration(MonthYear(1, 2024), MonthYear(1, 2024)),
            companyIds = setOf(command.companyId)
        )

        every { offCycleDataCollectorInputProcessor.process(command) } returns processedInput
        every { contractPayrollService.getContractPayrolls(any(), any(), any()) } returns emptyList()
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } returns emptyList()

        // WHEN
        dataCollector.handle(command)

        // THEN
        val savedDtosSlot = slot<List<PayableItemStoreDto>>()
        verify { payableItemStoreService.saveAndIgnoreDuplication(capture(savedDtosSlot)) }
        assertEquals(0, savedDtosSlot.captured.size)
    }

    @Test
    fun `given all payrolls have zero amounts when handle then saves zero amount items`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

        val processedInput = ProcessedOffCycleCollectorInput(
            transactionId = command.transactionId,
            transactionDate = command.transactionDate,
            timeQueryDuration = MonthYearDuration(MonthYear(1, 2024), MonthYear(1, 2024)),
            companyIds = setOf(command.companyId)
        )

        val contractPayroll = mockk<ContractPayroll> {
            every { type } returns ContractType.EMPLOYEE
            every { isOffCycle } returns true
            every { hasBillingException } returns false
        }

        val payableItemStoreDto = mockk<PayableItemStoreDto> {
            every { amount } returns 0.0
        }

        every { offCycleDataCollectorInputProcessor.process(command) } returns processedInput
        every { contractPayrollService.getContractPayrolls(any(), any(), any()) } returns listOf(contractPayroll)
        every { offCyclePayrollPaymentPayableItemStoreNormalizer.normalize(contractPayroll, any()) } returns payableItemStoreDto
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } returns emptyList()

        // WHEN
        dataCollector.handle(command)

        // THEN
        val savedDtosSlot = slot<List<PayableItemStoreDto>>()
        verify { payableItemStoreService.saveAndIgnoreDuplication(capture(savedDtosSlot)) }
        assertEquals(1, savedDtosSlot.captured.size)
        assertEquals(0.0, savedDtosSlot.captured[0].amount)
    }

    @Test
    fun `given cross-month timeQueryDuration when handle then uses correct month and year`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

        val processedInput = ProcessedOffCycleCollectorInput(
            transactionId = command.transactionId,
            transactionDate = command.transactionDate,
            timeQueryDuration = MonthYearDuration(MonthYear(6, 2024), MonthYear(8, 2024)),
            companyIds = setOf(command.companyId)
        )

        every { offCycleDataCollectorInputProcessor.process(command) } returns processedInput
        every { contractPayrollService.getContractPayrolls(any(), any(), any()) } returns emptyList()
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } returns emptyList()

        // WHEN
        dataCollector.handle(command)

        // THEN
        verify { contractPayrollService.getContractPayrolls(
            companyIds = listOf(command.companyId),
            month = 6, // Should use the 'from' month
            year = 2024
        ) }
    }

    @Test
    fun `given payrolls with non-EMPLOYEE contract types when handle then filters them out`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

        val processedInput = ProcessedOffCycleCollectorInput(
            transactionId = command.transactionId,
            transactionDate = command.transactionDate,
            timeQueryDuration = MonthYearDuration(MonthYear(1, 2024), MonthYear(1, 2024)),
            companyIds = setOf(command.companyId)
        )

        val employeePayroll = mockk<ContractPayroll> {
            every { type } returns ContractType.EMPLOYEE
            every { isOffCycle } returns true
            every { hasBillingException } returns false
        }
        val freelancerPayroll = mockk<ContractPayroll> {
            every { type } returns ContractType.FREELANCER
            every { isOffCycle } returns true
            every { hasBillingException } returns false
        }
        val contractorPayroll = mockk<ContractPayroll> {
            every { type } returns ContractType.CONTRACTOR
            every { isOffCycle } returns true
            every { hasBillingException } returns false
        }
        val hrMemberPayroll = mockk<ContractPayroll> {
            every { type } returns ContractType.HR_MEMBER
            every { isOffCycle } returns true
            every { hasBillingException } returns false
        }

        val payableItemStoreDto = mockk<PayableItemStoreDto> {
            every { amount } returns 100.0
        }

        every { offCycleDataCollectorInputProcessor.process(command) } returns processedInput
        every { contractPayrollService.getContractPayrolls(any(), any(), any()) } returns listOf(
            employeePayroll, freelancerPayroll, contractorPayroll, hrMemberPayroll
        )
        every { offCyclePayrollPaymentPayableItemStoreNormalizer.normalize(employeePayroll, any()) } returns payableItemStoreDto
        every { payableItemStoreService.saveAndIgnoreDuplication(any()) } returns emptyList()

        // WHEN
        dataCollector.handle(command)

        // THEN
        // Only EMPLOYEE contract type should be processed
        verify { offCyclePayrollPaymentPayableItemStoreNormalizer.normalize(employeePayroll, any()) }
        verify(exactly = 0) { offCyclePayrollPaymentPayableItemStoreNormalizer.normalize(freelancerPayroll, any()) }
        verify(exactly = 0) { offCyclePayrollPaymentPayableItemStoreNormalizer.normalize(contractorPayroll, any()) }
        verify(exactly = 0) { offCyclePayrollPaymentPayableItemStoreNormalizer.normalize(hrMemberPayroll, any()) }

        val savedDtosSlot = slot<List<PayableItemStoreDto>>()
        verify { payableItemStoreService.saveAndIgnoreDuplication(capture(savedDtosSlot)) }
        assertEquals(1, savedDtosSlot.captured.size) // Only the EMPLOYEE payroll should be saved
    }

    @Test
    fun `when rollback then logs rollback message`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

        // WHEN
        dataCollector.rollback(command)

        // THEN
        // This test verifies that rollback doesn't throw an exception
        // The actual logging verification would require a logging framework mock
    }
}
