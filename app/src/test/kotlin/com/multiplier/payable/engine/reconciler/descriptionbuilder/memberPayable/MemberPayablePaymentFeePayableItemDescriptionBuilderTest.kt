package com.multiplier.payable.engine.reconciler.descriptionbuilder.memberPayable

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.memberpayable.MemberPayable
import com.multiplier.payable.engine.memberpayable.MemberPayableWrapper
import com.multiplier.payable.engine.memberpayable.PaymentMethod
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import org.junit.jupiter.api.Test

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

@ExtendWith(MockKExtension::class)
class MemberPayablePaymentFeePayableItemDescriptionBuilderTest {

    @InjectMockKs
    private lateinit var memberPayablePaymentFeePayableItemDescriptionBuilder: MemberPayablePaymentFeePayableItemDescriptionBuilder

    @Test
    fun getLineItemType() {
        assertEquals(LineItemType.PAYMENT_FEE, memberPayablePaymentFeePayableItemDescriptionBuilder.lineItemType)
    }

    @ParameterizedTest
    @CsvSource(
        "BANK_TRANSFER, Bank transfer fee",
        "CARD, Credit card fee",
        "ACH_DIRECT_DEBIT, Direct debit fee",
        "SEPA_DIRECT_DEBIT, Direct debit fee",
        "EGIRO_DIRECT_DEBIT, Direct debit fee",
        "CRYPTO, Crypto payout fee"
    )
    fun should_return_correctFeeDescription(paymentMethod: PaymentMethod, expectedDescription: String) {
        val context = mockk<PayableItemDescriptionBuilderContext>(){
            every { memberPayableWrapper } returns MemberPayableWrapper(
                memberPayable = mockk<MemberPayable>(),
                paymentMethod = paymentMethod
            )
        }
        val result = memberPayablePaymentFeePayableItemDescriptionBuilder.build(context)
        assertEquals(expectedDescription, result)
    }

    @Test
    fun should_throw_exception_if_unknownPaymentMethod(){
        val context = mockk<PayableItemDescriptionBuilderContext>(){
            every { memberPayableWrapper } returns null
        }
        val exception = org.junit.jupiter.api.assertThrows<IllegalArgumentException> {
            memberPayablePaymentFeePayableItemDescriptionBuilder.build(context)
        }
        assertEquals("No description available for given payment method null", exception.message)
    }
}