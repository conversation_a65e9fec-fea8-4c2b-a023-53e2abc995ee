package com.multiplier.payable.engine.transaction

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.transaction.mapper.JpaInvoiceLineItemMapper
import com.multiplier.payable.engine.transaction.mapper.JpaInvoiceLineItemMapperImpl
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mapstruct.factory.Mappers
import java.time.LocalDate

class JpaInvoiceLineItemMapperTest {
    private val mapper = Mappers.getMapper(JpaInvoiceLineItemMapper::class.java)

    @Test
    fun `should map correct`() {
        // GIVEN
        val item =
            PayableItem(
                month = 3,
                year = 2024,
                lineItemType = "ANNUAL_MANAGEMENT_FEE_EOR",
                companyId = 1L,
                description = "Line Item",
                amountInBaseCurrency = 120.0,
                baseCurrency = "SGD",
                billableCost = 100.0,
                originalTimestamp = 123456789L,
                cycle = InvoiceCycle.YEARLY,
                countryCode = "SGP",
                countryName = "Singapore",
                periodStartDate = LocalDate.of(2024, 3, 1),
                periodEndDate = LocalDate.of(2025, 2, 28),
                itemCount = 2,
            )

        // WHEN
        val result = mapper.map(item, "Singapore")

        // THEN
        assertEquals(100.0, result.unitPrice)
        assertEquals(LineItemType.ANNUAL_MANAGEMENT_FEE_EOR, result.itemType)
        assertEquals(2.0, result.quantity)
        assertEquals("Singapore", result.countryName)
        assertEquals(120.0, result.amountInBaseCurrency)
        assertEquals("SGD", result.baseCurrency)
        assertEquals("Line Item", result.description)
    }
}
