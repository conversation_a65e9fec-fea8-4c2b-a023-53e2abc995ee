package com.multiplier.payable.engine.reconciler.multiplierPayable.service

import com.multiplier.core.payable.expenseBill.ExpenseBillLineItemType
import com.multiplier.core.payable.repository.JpaMultiplierPayableRepository
import com.multiplier.core.payable.repository.filter.MultiplierPayableFilter
import com.multiplier.core.payable.repository.model.JpaMultiplierPayable
import com.multiplier.core.payable.repository.model.MultiplierPayableStatus
import com.multiplier.payable.engine.reconciler.multiplierPayable.MultiplierPayableMapper
import com.multiplier.payable.engine.reconciler.multiplierPayable.dto.MultiplierPayableDTO
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.*
import org.springframework.data.jpa.domain.Specification
import java.util.*

class MultiplierPayableServiceImplTest {

    private lateinit var repository: JpaMultiplierPayableRepository
    private lateinit var mapper: MultiplierPayableMapper
    private lateinit var service: MultiplierPayableServiceImpl

    private val dummyDto = MultiplierPayableDTO(
        status = MultiplierPayableStatus.APPROVED,
        lineItemType = ExpenseBillLineItemType.DEFAULT,
        transactionId = "txn-1",
        payrollCycleId = 123L,
        items = emptySet()
    )

    private val dummyEntity = JpaMultiplierPayable.builder()
        .id(1L)
        .status(dummyDto.status)
        .lineItemType(dummyDto.lineItemType)
        .transactionId(dummyDto.transactionId)
        .payrollCycleId(dummyDto.payrollCycleId)
        .items(emptySet())
        .createdOn(null)
        .updatedOn(null)
        .createdBy(0L)
        .updatedBy(null)
        .build()

    @BeforeEach
    fun setUp() {
        repository = mock(JpaMultiplierPayableRepository::class.java)
        mapper = mock(MultiplierPayableMapper::class.java)
        service = MultiplierPayableServiceImpl(repository, mapper)
    }

    @Test
    fun `saveAll should map DTO to entity, save and return mapped DTOs`() {
        val dtoList = listOf(dummyDto)
        val entityList = listOf(dummyEntity)

        `when`(mapper.toJpaMultiplierPayable(dummyDto)).thenReturn(dummyEntity)
        `when`(repository.saveAll(entityList)).thenReturn(entityList)
        `when`(mapper.toMultiplierPayableDTO(dummyEntity)).thenReturn(dummyDto)

        val result = service.saveAll(dtoList)

        assertThat(result).hasSize(1)
        assertThat(result[0]).isEqualTo(dummyDto)

        verify(mapper).toJpaMultiplierPayable(dummyDto)
        verify(repository).saveAll(entityList)
        verify(mapper).toMultiplierPayableDTO(dummyEntity)
    }

    @Test
    fun `getMultiplierPayablesByTransactionId should return mapped DTOs`() {
        `when`(repository.findByTransactionId("txn-1")).thenReturn(listOf(dummyEntity))
        `when`(mapper.toMultiplierPayableDTO(dummyEntity)).thenReturn(dummyDto)

        val result = service.getMultiplierPayablesByTransactionId("txn-1")

        assertThat(result).hasSize(1)
        assertThat(result[0].transactionId).isEqualTo("txn-1")

        verify(repository).findByTransactionId("txn-1")
        verify(mapper).toMultiplierPayableDTO(dummyEntity)
    }

    @Test
    fun `getMultiplierPayablesByFilter should return matching DTOs`() {
        // Given
        val filter = MultiplierPayableFilter.builder()
            .payrollCycleId(123L)
            .lineItemType(ExpenseBillLineItemType.DEFAULT)
            .status(MultiplierPayableStatus.APPROVED)
            .build()

        `when`(repository.findAll(any<Specification<JpaMultiplierPayable>>())).thenReturn(listOf(dummyEntity))
        `when`(mapper.toMultiplierPayableDTO(dummyEntity)).thenReturn(dummyDto)

        // When
        val result = service.getMultiplierPayablesByFilter(filter)

        // Then
        assertThat(result).hasSize(1)
        assertThat(result[0].transactionId).isEqualTo("txn-1")

        verify(repository).findAll(any<Specification<JpaMultiplierPayable>>())
        verify(mapper).toMultiplierPayableDTO(dummyEntity)
    }

    @Test
    fun `getMultiplierPayableByPayrollCycleIdAndItemType should return null if not found`() {
        `when`(repository.findByPayrollCycleIdAndLineItemType(123L, ExpenseBillLineItemType.DEFAULT))
            .thenReturn(Optional.empty())

        val result = service.getMultiplierPayableByPayrollCycleIdAndItemType(123L, ExpenseBillLineItemType.DEFAULT)

        assertThat(result).isNull()

        verify(repository).findByPayrollCycleIdAndLineItemType(123L, ExpenseBillLineItemType.DEFAULT)
        verifyNoInteractions(mapper)
    }
}
