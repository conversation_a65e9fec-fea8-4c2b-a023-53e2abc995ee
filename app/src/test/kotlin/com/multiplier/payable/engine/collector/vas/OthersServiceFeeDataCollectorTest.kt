package com.multiplier.payable.engine.collector.vas

import com.multiplier.core.payable.adapters.VasIncidentServiceAdapter
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.ProcessedIncidentCollectorInput
import com.multiplier.payable.engine.collector.vas.payment.OthersServiceFeeDataCollector
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.vas.IncidentType
import com.multiplier.payable.engine.vas.OthersIncident
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class OthersServiceFeeDataCollectorTest {
    @RelaxedMockK
    private lateinit var vasIncidentalService: VasIncidentServiceAdapter

    @RelaxedMockK
    private lateinit var dataCollectorInputProcessor: DataCollectorInputProcessor<ProcessedIncidentCollectorInput>

    @RelaxedMockK
    private lateinit var payableItemStoreService: PayableItemStoreService

    @RelaxedMockK
    private lateinit var incidentPayableItemStoreNormalizer: IncidentPayableItemStoreNormalizer

    @InjectMockKs
    private lateinit var othersServiceFeeDataCollector: OthersServiceFeeDataCollector

    @Test
    fun `should collect data for others service fee`() {
        // Arrange
        val mockTransactionId = "some-transactionId"
        val command = mockk<InvoiceCommand>(relaxed = true)

        val processedCollectorInput =
            mockk<ProcessedIncidentCollectorInput>(relaxed = true) {
                every { transactionId } returns mockTransactionId
            }

        val incident1 =
            mockk<OthersIncident>(relaxed = true) {
                every { type } returns IncidentType.OTHERS_SERVICE_FEE
            }

        val incident2 =
            mockk<OthersIncident>(relaxed = true) {
                every { type } returns IncidentType.OTHERS_SERVICE_FEE
            }

        val incident3 =
            mockk<OthersIncident>(relaxed = true) {
                every { type } returns IncidentType.LAPTOP
            }

        every { dataCollectorInputProcessor.process(command) } returns processedCollectorInput
        every { vasIncidentalService.getIncidents(mockTransactionId) } returns listOf(incident1, incident2, incident3)

        // Act
        othersServiceFeeDataCollector.handle(command)

        // Assert
        verify(exactly = 2) { incidentPayableItemStoreNormalizer.normalize(any(), any()) }
        verify { payableItemStoreService.saveAndIgnoreDuplication(any()) }
    }
}
