package com.multiplier.payable.engine.reconciler.descriptionbuilder.adjustment.gp

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class TotalPaymentsAdjustmentDescriptionBuilderTest {

    @InjectMocks
    private lateinit var builder: TotalPaymentsAdjustmentDescriptionBuilder

    @Test
    fun `lineItemType should return ORDER_FORM_ADVANCE_ADJUSTMENT_TOTAL_PAYMENTS`() {
        // When
        val result = builder.lineItemType

        // Then
        assertThat(result).isEqualTo(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_TOTAL_PAYMENTS)
    }

    @Test
    fun `build should return correct description with all context values`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(1, 2024),
            countryCode = "US",
            currencyCode = "USD",
            amountInBaseCurrency = 800.0,
            noOfMembers = 25
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Payment Fee Advance Adjustment (for 25 Members) US: USD 800.0")
    }

    @Test
    fun `build should handle null noOfMembers`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(2, 2024),
            countryCode = "SG",
            currencyCode = "SGD",
            amountInBaseCurrency = 600.0,
            noOfMembers = null
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Payment Fee Advance Adjustment (for Unknown Members) SG: SGD 600.0")
    }

    @Test
    fun `build should handle null countryCode`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(3, 2024),
            countryCode = null,
            currencyCode = "EUR",
            amountInBaseCurrency = 1200.0,
            noOfMembers = 50
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Payment Fee Advance Adjustment (for 50 Members) N/A: EUR 1200.0")
    }

    @Test
    fun `build should handle both null noOfMembers and countryCode`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(4, 2024),
            countryCode = null,
            currencyCode = "GBP",
            amountInBaseCurrency = 400.0,
            noOfMembers = null
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Payment Fee Advance Adjustment (for Unknown Members) N/A: GBP 400.0")
    }

    @Test
    fun `build should handle zero members`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(5, 2024),
            countryCode = "CA",
            currencyCode = "CAD",
            amountInBaseCurrency = 0.0,
            noOfMembers = 0
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Payment Fee Advance Adjustment (for 0 Members) CA: CAD 0.0")
    }

    @Test
    fun `build should handle single member`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(6, 2024),
            countryCode = "AU",
            currencyCode = "AUD",
            amountInBaseCurrency = 100.0,
            noOfMembers = 1
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Payment Fee Advance Adjustment (for 1 Members) AU: AUD 100.0")
    }

    @Test
    fun `build should handle large number of members`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(7, 2024),
            countryCode = "IN",
            currencyCode = "INR",
            amountInBaseCurrency = 50000.0,
            noOfMembers = 1000
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Payment Fee Advance Adjustment (for 1000 Members) IN: INR 50000.0")
    }

    @Test
    fun `build should handle negative amount`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(8, 2024),
            countryCode = "JP",
            currencyCode = "JPY",
            amountInBaseCurrency = -5000.0,
            noOfMembers = 15
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Payment Fee Advance Adjustment (for 15 Members) JP: JPY -5000.0")
    }

    @Test
    fun `build should handle empty countryCode`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(9, 2024),
            countryCode = "",
            currencyCode = "CHF",
            amountInBaseCurrency = 750.0,
            noOfMembers = 30
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Payment Fee Advance Adjustment (for 30 Members) : CHF 750.0")
    }

    @Test
    fun `build should handle decimal amounts`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(10, 2024),
            countryCode = "NZ",
            currencyCode = "NZD",
            amountInBaseCurrency = 123.45,
            noOfMembers = 7
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Payment Fee Advance Adjustment (for 7 Members) NZ: NZD 123.45")
    }
}
