package com.multiplier.payable.engine.reconciler.data.item

import com.multiplier.payable.engine.InvoicingException
import com.multiplier.payable.engine.domain.entities.TransactionType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`

class ItemStoreDataProviderFactoryTest {

    @Test
    fun `given valid TransactionType, when get, then return correct ItemStoreDataProvider`() {
        // GIVEN
        val mockProvider1 = mock(ItemStoreDataProvider::class.java)
        val mockProvider2 = mock(ItemStoreDataProvider::class.java)
        `when`(mockProvider1.transactionType()).thenReturn(TransactionType.DEPOSIT_INVOICE)
        `when`(mockProvider2.transactionType()).thenReturn(TransactionType.GP_FUNDING_INVOICE)

        val factory = ItemStoreDataProviderFactory(listOf(mockProvider1, mockProvider2))

        // WHEN
        val provider1 = factory.get(TransactionType.DEPOSIT_INVOICE)
        val provider2 = factory.get(TransactionType.GP_FUNDING_INVOICE)

        // THEN
        assertSame(mockProvider1, provider1)
        assertSame(mockProvider2, provider2)
    }

    @Test
    fun `given invalid TransactionType, when get, then throw InvoicingException`() {
        // GIVEN
        val mockProvider = mock(ItemStoreDataProvider::class.java)
        `when`(mockProvider.transactionType()).thenReturn(TransactionType.DEPOSIT_INVOICE)

        val factory = ItemStoreDataProviderFactory(listOf(mockProvider))

        // WHEN & THEN
        val exception = assertThrows<InvoicingException> {
            factory.get(TransactionType.ANNUAL_PLAN_INVOICE)
        }
        assertEquals(
            "The ItemStoreDataProvider for transaction type 'ANNUAL_PLAN_INVOICE' not found",
            exception.message
        )
    }
}
