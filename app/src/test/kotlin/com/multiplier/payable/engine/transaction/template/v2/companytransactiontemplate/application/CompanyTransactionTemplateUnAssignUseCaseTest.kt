package com.multiplier.payable.engine.transaction.template.v2.companytransactiontemplate.application

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateConfigurationException
import com.multiplier.payable.engine.transaction.template.v2.companytransactiontemplate.domain.CompanyTransactionTemplateEntity
import com.multiplier.payable.engine.transaction.template.v2.companytransactiontemplate.domain.CompanyTransactionTemplateRepository
import com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.domain.TransactionTemplateId
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import org.mockito.Mockito.*
import java.util.*

class CompanyTransactionTemplateUnAssignUseCaseTest {

    private val companyTransactionTemplateRepository = mock(CompanyTransactionTemplateRepository::class.java)
    private val useCase = CompanyTransactionTemplateUnAssignUseCase(companyTransactionTemplateRepository)

    @Test
    fun executeSuccessfullyUnassignsTemplate() {
        val transactionTemplateId = TransactionTemplateId(UUID.randomUUID())
        val transactionType = TransactionType.FIRST_INVOICE
        val companyId = 123L
        val template = CompanyTransactionTemplateEntity(transactionTemplateId, companyId, transactionType, companyTransactionTemplateRepository)

        `when`(companyTransactionTemplateRepository.findByCompanyIdAndTransactionType(companyId, transactionType))
            .thenReturn(template)

        useCase.execute(transactionTemplateId, transactionType, companyId)

        verify(companyTransactionTemplateRepository).deleteById(template.id)
    }

    @Test
    fun throwsExceptionWhenTemplateDoesNotExist() {
        val transactionTemplateId = TransactionTemplateId(UUID.randomUUID())
        val transactionType = TransactionType.FIRST_INVOICE
        val companyId = 123L

        `when`(companyTransactionTemplateRepository.findByCompanyIdAndTransactionType(companyId, transactionType))
            .thenReturn(null)

        assertThrows(TransactionTemplateConfigurationException::class.java) {
            useCase.execute(transactionTemplateId, transactionType, companyId)
        }
    }
}