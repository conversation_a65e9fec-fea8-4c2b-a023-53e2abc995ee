package com.multiplier.payable.engine.collector.vas

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.data.ProcessedIncidentCollectorInput
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.transaction.IncidentsInvoiceCommand
import com.multiplier.payable.engine.vas.IncidentQuantityChargePolicy
import com.multiplier.payable.engine.vas.IncidentQuantityUnit
import com.multiplier.payable.engine.vas.IncidentType
import com.multiplier.payable.engine.vas.LegalIncident
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDateTime
import java.time.Month
import java.util.*

class LegalIncidentPayableItemStoreNormalizerTest {

    private lateinit var objectMapper: ObjectMapper
    private lateinit var normalizer: LegalIncidentPayableItemStoreNormalizer

    @BeforeEach
    fun setUp() {
        objectMapper = ObjectMapper().apply {
            registerModule(JavaTimeModule())
            disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        }
        normalizer = LegalIncidentPayableItemStoreNormalizer(objectMapper)
    }

    @Test
    fun `should create normalizer successfully`() {
        // Assert
        assertThat(normalizer).isNotNull()
    }

    @Test
    fun `should normalize LegalIncident to PayableItemStoreDto correctly`() {
        // Arrange
        val chargePolicy = IncidentQuantityChargePolicy(8.5, IncidentQuantityUnit.HOURS)
        val legalIncident = LegalIncident(
            id = 123L,
            amount = Amount(BigDecimal("500.75"), CurrencyCode.USD),
            description = "Legal consultation for contract review",
            incidentTime = LocalDateTime.of(2024, 5, 15, 10, 30),
            type = IncidentType.LEGAL_CONSULTATION,
            countryCode = CountryCode.USA,
            chargePolicy = chargePolicy
        )

        val startDate = LocalDateTime.of(2024, 5, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 5, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 5, 15, 10, 30)
        val originalTimestamp = Instant.parse("2024-05-15T12:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "legal-txn-123",
            transactionDate = transactionDate,
            companyId = 789L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(legalIncident, processedInput)

        // Assert
        assertThat(result.amount).isEqualTo(500.75)
        assertThat(result.currency).isEqualTo(CurrencyCode.USD)
        assertThat(result.companyId).isEqualTo(789L)
        assertThat(result.contractId).isEqualTo(-1L) // Always -1 for LegalIncident
        assertThat(result.transactionId).isEqualTo("legal-txn-123")
        assertThat(result.month).isEqualTo(Month.MAY.value)
        assertThat(result.year).isEqualTo(2024)
        assertThat(result.itemData).isNotNull()
        assertThat(result.itemData).contains("\"id\":123")
        assertThat(result.itemData).contains("\"description\":\"Legal consultation for contract review\"")
        assertThat(result.itemType).isEqualTo(LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE)
        assertThat(result.periodStartDate).isEqualTo(legalIncident.incidentTime.toLocalDate())
        assertThat(result.periodEndDate).isEqualTo(legalIncident.incidentTime.toLocalDate())
        assertThat(result.countryCode).isEqualTo("USA")
        assertThat(result.originalTimestamp).isEqualTo(originalTimestamp.toEpochMilli())
        assertThat(result.versionId).isNotNull()
    }

    @Test
    fun `should handle LegalIncident with null charge policy`() {
        // Arrange
        val legalIncident = LegalIncident(
            id = 456L,
            amount = Amount(BigDecimal("300.00"), CurrencyCode.EUR),
            description = "Legal advice on employment law",
            incidentTime = LocalDateTime.of(2024, 3, 10, 15, 0),
            type = IncidentType.LEGAL_CONSULTATION,
            countryCode = CountryCode.DEU,
            chargePolicy = null
        )

        val startDate = LocalDateTime.of(2024, 3, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 3, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 3, 10, 15, 0)
        val originalTimestamp = Instant.parse("2024-03-10T15:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "legal-txn-456",
            transactionDate = transactionDate,
            companyId = 999L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(legalIncident, processedInput)

        // Assert
        assertThat(result.amount).isEqualTo(300.00)
        assertThat(result.currency).isEqualTo(CurrencyCode.EUR)
        assertThat(result.contractId).isEqualTo(-1L)
        assertThat(result.countryCode).isEqualTo("DEU")
        assertThat(result.itemType).isEqualTo(LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE)
        assertThat(result.month).isEqualTo(Month.MARCH.value)
        assertThat(result.year).isEqualTo(2024)
        assertThat(result.itemData).contains("\"chargePolicy\":null")
    }

    @ParameterizedTest
    @EnumSource(IncidentType::class)
    fun `should handle different incident types correctly`(incidentType: IncidentType) {
        // Arrange
        val chargePolicy = IncidentQuantityChargePolicy(5.0, IncidentQuantityUnit.HOURS)
        val legalIncident = LegalIncident(
            id = 789L,
            amount = Amount(BigDecimal("250.50"), CurrencyCode.GBP),
            description = "Legal incident for type $incidentType",
            incidentTime = LocalDateTime.of(2024, 12, 25, 10, 0),
            type = incidentType,
            countryCode = CountryCode.GBR,
            chargePolicy = chargePolicy
        )

        val startDate = LocalDateTime.of(2024, 12, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 12, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 12, 25, 10, 0)
        val originalTimestamp = Instant.parse("2024-12-25T10:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "legal-txn-789",
            transactionDate = transactionDate,
            companyId = 555L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(legalIncident, processedInput)

        // Assert
        assertThat(result.amount).isEqualTo(250.50)
        assertThat(result.currency).isEqualTo(CurrencyCode.GBP)
        assertThat(result.contractId).isEqualTo(-1L)
        assertThat(result.countryCode).isEqualTo("GBR")
        assertThat(result.itemType).isEqualTo(LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE)
        assertThat(result.month).isEqualTo(Month.DECEMBER.value)
        assertThat(result.year).isEqualTo(2024)
        assertThat(result.itemData).contains("\"type\":\"${incidentType.name}\"")
    }

    @Test
    fun `should handle zero amount correctly`() {
        // Arrange
        val legalIncident = LegalIncident(
            id = 999L,
            amount = Amount(BigDecimal.ZERO, CurrencyCode.USD),
            description = "Zero amount legal incident",
            incidentTime = LocalDateTime.of(2024, 1, 15, 12, 0),
            type = IncidentType.LEGAL_CONSULTATION,
            countryCode = CountryCode.USA,
            chargePolicy = null
        )

        val startDate = LocalDateTime.of(2024, 1, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 1, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 1, 15, 12, 0)
        val originalTimestamp = Instant.parse("2024-01-15T12:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "legal-txn-zero",
            transactionDate = transactionDate,
            companyId = 100L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(legalIncident, processedInput)

        // Assert
        assertThat(result.amount).isEqualTo(0.0)
        assertThat(result.currency).isEqualTo(CurrencyCode.USD)
        assertThat(result.countryCode).isEqualTo("USA")
        assertThat(result.itemData).contains("\"amount\":{\"value\":0")
    }

    @Test
    fun `should handle negative amount correctly`() {
        // Arrange
        val legalIncident = LegalIncident(
            id = 888L,
            amount = Amount(BigDecimal("-150.25"), CurrencyCode.EUR),
            description = "Refund for legal services",
            incidentTime = LocalDateTime.of(2024, 6, 15, 14, 30),
            type = IncidentType.LEGAL_CONSULTATION,
            countryCode = CountryCode.FRA,
            chargePolicy = null
        )

        val startDate = LocalDateTime.of(2024, 6, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 6, 30, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 6, 15, 14, 30)
        val originalTimestamp = Instant.parse("2024-06-15T14:30:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "legal-txn-negative",
            transactionDate = transactionDate,
            companyId = 200L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(legalIncident, processedInput)

        // Assert
        assertThat(result.amount).isEqualTo(-150.25)
        assertThat(result.currency).isEqualTo(CurrencyCode.EUR)
        assertThat(result.countryCode).isEqualTo("FRA")
        assertThat(result.month).isEqualTo(Month.JUNE.value)
        assertThat(result.year).isEqualTo(2024)
    }

    @Test
    fun `should handle very large amounts correctly`() {
        // Arrange
        val largeAmount = BigDecimal("*********.99")
        val chargePolicy = IncidentQuantityChargePolicy(100.0, IncidentQuantityUnit.HOURS)
        val legalIncident = LegalIncident(
            id = 777L,
            amount = Amount(largeAmount, CurrencyCode.JPY),
            description = "Large legal consultation fee",
            incidentTime = LocalDateTime.of(2024, 9, 15, 9, 0),
            type = IncidentType.LEGAL_CONSULTATION,
            countryCode = CountryCode.JPN,
            chargePolicy = chargePolicy
        )

        val startDate = LocalDateTime.of(2024, 9, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 9, 30, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 9, 15, 9, 0)
        val originalTimestamp = Instant.parse("2024-09-15T09:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "legal-txn-large",
            transactionDate = transactionDate,
            companyId = 300L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(legalIncident, processedInput)

        // Assert
        assertThat(result.amount).isEqualTo(*********.99)
        assertThat(result.currency).isEqualTo(CurrencyCode.JPY)
        assertThat(result.countryCode).isEqualTo("JPN")
    }

    @Test
    fun `should handle cross-year incident time correctly`() {
        // Arrange
        val chargePolicy = IncidentQuantityChargePolicy(12.0, IncidentQuantityUnit.HOURS)
        val legalIncident = LegalIncident(
            id = 666L,
            amount = Amount(BigDecimal("1200.50"), CurrencyCode.CAD),
            description = "Year-end legal consultation",
            incidentTime = LocalDateTime.of(2023, 12, 31, 23, 30), // Different year from transaction
            type = IncidentType.LEGAL_CONSULTATION,
            countryCode = CountryCode.CAN,
            chargePolicy = chargePolicy
        )

        val startDate = LocalDateTime.of(2024, 1, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 1, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 1, 15, 10, 0)
        val originalTimestamp = Instant.parse("2024-01-15T10:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "legal-txn-cross-year",
            transactionDate = transactionDate,
            companyId = 400L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(legalIncident, processedInput)

        // Assert
        assertThat(result.month).isEqualTo(Month.DECEMBER.value) // From incidentTime
        assertThat(result.year).isEqualTo(2023) // From incidentTime
        assertThat(result.periodStartDate).isEqualTo(legalIncident.incidentTime.toLocalDate())
        assertThat(result.periodEndDate).isEqualTo(legalIncident.incidentTime.toLocalDate())
    }

    @Test
    fun `should handle ObjectMapper serialization correctly`() {
        // Arrange
        val chargePolicy = IncidentQuantityChargePolicy(6.5, IncidentQuantityUnit.HOURS)
        val legalIncident = LegalIncident(
            id = 555L,
            amount = Amount(BigDecimal("750.45"), CurrencyCode.USD),
            description = "Contract review and legal advice",
            incidentTime = LocalDateTime.of(2024, 4, 15, 11, 0),
            type = IncidentType.LEGAL_CONSULTATION,
            countryCode = CountryCode.USA,
            chargePolicy = chargePolicy
        )

        val startDate = LocalDateTime.of(2024, 4, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 4, 30, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 4, 15, 11, 0)
        val originalTimestamp = Instant.parse("2024-04-15T11:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "legal-txn-json",
            transactionDate = transactionDate,
            companyId = 500L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(legalIncident, processedInput)

        // Assert
        assertThat(result.itemData).isNotNull()
        assertThat(result.itemData).isNotEmpty()

        // Verify JSON contains all expected fields
        assertThat(result.itemData).contains("\"id\":555")
        assertThat(result.itemData).contains("\"amount\":")
        assertThat(result.itemData).contains("\"value\":750.45")
        assertThat(result.itemData).contains("\"currency\":\"USD\"")
        assertThat(result.itemData).contains("\"description\":\"Contract review and legal advice\"")
        assertThat(result.itemData).contains("\"countryCode\":\"USA\"")
        assertThat(result.itemData).contains("\"type\":\"LEGAL_CONSULTATION\"")
        assertThat(result.itemData).contains("\"chargePolicy\":")
        assertThat(result.itemData).contains("\"value\":6.5")
        assertThat(result.itemData).contains("\"unit\":\"HOURS\"")
    }

    @Test
    fun `should handle ObjectMapper serialization failure gracefully`() {
        // Arrange
        val mockObjectMapper = mockk<ObjectMapper>()
        val normalizerWithMockMapper = LegalIncidentPayableItemStoreNormalizer(mockObjectMapper)

        val legalIncident = LegalIncident(
            id = 444L,
            amount = Amount(BigDecimal("100.00"), CurrencyCode.USD),
            description = "Legal consultation",
            incidentTime = LocalDateTime.of(2024, 7, 15, 13, 0),
            type = IncidentType.LEGAL_CONSULTATION,
            countryCode = CountryCode.USA,
            chargePolicy = null
        )

        val startDate = LocalDateTime.of(2024, 7, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 7, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 7, 15, 13, 0)
        val originalTimestamp = Instant.parse("2024-07-15T13:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "legal-txn-error",
            transactionDate = transactionDate,
            companyId = 600L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
        processedInput.originalTimestamp = originalTimestamp

        every { mockObjectMapper.writeValueAsString(any()) } throws RuntimeException("Serialization failed")

        // Act & Assert
        assertThrows<RuntimeException> {
            normalizerWithMockMapper.normalize(legalIncident, processedInput)
        }

        verify { mockObjectMapper.writeValueAsString(legalIncident) }
    }

    @Test
    fun `should generate consistent versionId for same input`() {
        // Arrange
        val chargePolicy = IncidentQuantityChargePolicy(4.0, IncidentQuantityUnit.HOURS)
        val legalIncident = LegalIncident(
            id = 333L,
            amount = Amount(BigDecimal("400.00"), CurrencyCode.USD),
            description = "Legal consultation",
            incidentTime = LocalDateTime.of(2024, 8, 15, 16, 0),
            type = IncidentType.LEGAL_CONSULTATION,
            countryCode = CountryCode.USA,
            chargePolicy = chargePolicy
        )

        val startDate = LocalDateTime.of(2024, 8, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 8, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 8, 15, 16, 0)
        val originalTimestamp = Instant.parse("2024-08-15T16:00:00Z")

        val processedInput1 = ProcessedIncidentCollectorInput(
            transactionId = "legal-txn-version1",
            transactionDate = transactionDate,
            companyId = 700L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput1.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
        processedInput1.originalTimestamp = originalTimestamp

        val processedInput2 = ProcessedIncidentCollectorInput(
            transactionId = "legal-txn-version2",
            transactionDate = transactionDate,
            companyId = 700L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput2.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
        processedInput2.originalTimestamp = originalTimestamp

        // Act
        val result1 = normalizer.normalize(legalIncident, processedInput1)
        val result2 = normalizer.normalize(legalIncident, processedInput2)

        // Assert
        assertThat(result1.versionId).isEqualTo(result2.versionId)
        assertThat(result1.originalTimestamp).isEqualTo(result2.originalTimestamp)
    }

    @Test
    fun `should handle different currencies correctly`() {
        // Arrange
        val currencies = listOf(
            CurrencyCode.USD to "USD",
            CurrencyCode.EUR to "EUR",
            CurrencyCode.GBP to "GBP",
            CurrencyCode.JPY to "JPY",
            CurrencyCode.CAD to "CAD"
        )

        currencies.forEach { (currencyCode, expectedString) ->
            val chargePolicy = IncidentQuantityChargePolicy(2.0, IncidentQuantityUnit.HOURS)
            val legalIncident = LegalIncident(
                id = 222L,
                amount = Amount(BigDecimal("100.00"), currencyCode),
                description = "Legal consultation in $expectedString",
                incidentTime = LocalDateTime.of(2024, 10, 15, 18, 0),
                type = IncidentType.LEGAL_CONSULTATION,
                countryCode = CountryCode.USA,
                chargePolicy = chargePolicy
            )

            val startDate = LocalDateTime.of(2024, 10, 1, 0, 0)
            val endDate = LocalDateTime.of(2024, 10, 31, 23, 59)
            val transactionDate = LocalDateTime.of(2024, 10, 15, 18, 0)
            val originalTimestamp = Instant.parse("2024-10-15T18:00:00Z")

            val processedInput = ProcessedIncidentCollectorInput(
                transactionId = "legal-txn-currency-$expectedString",
                transactionDate = transactionDate,
                companyId = 800L,
                dateRange = DateRange(startDate, endDate),
                incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
            )
            processedInput.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
            processedInput.originalTimestamp = originalTimestamp

            // Act
            val result = normalizer.normalize(legalIncident, processedInput)

            // Assert
            assertThat(result.currency).isEqualTo(currencyCode)
            assertThat(result.itemData).contains("\"currency\":\"$expectedString\"")
        }
    }

    @Test
    fun `should handle different country codes correctly`() {
        // Arrange
        val countries = listOf(
            CountryCode.USA to "USA",
            CountryCode.GBR to "GBR",
            CountryCode.DEU to "DEU",
            CountryCode.FRA to "FRA",
            CountryCode.JPN to "JPN",
            CountryCode.CAN to "CAN",
            CountryCode.AUS to "AUS",
            CountryCode.SGP to "SGP"
        )

        countries.forEach { (countryCode, expectedString) ->
            val legalIncident = LegalIncident(
                id = 111L,
                amount = Amount(BigDecimal("200.00"), CurrencyCode.USD),
                description = "Legal consultation in $expectedString",
                incidentTime = LocalDateTime.of(2024, 11, 10, 14, 0),
                type = IncidentType.LEGAL_CONSULTATION,
                countryCode = countryCode,
                chargePolicy = null
            )

            val startDate = LocalDateTime.of(2024, 11, 1, 0, 0)
            val endDate = LocalDateTime.of(2024, 11, 30, 23, 59)
            val transactionDate = LocalDateTime.of(2024, 11, 10, 14, 0)
            val originalTimestamp = Instant.parse("2024-11-10T14:00:00Z")

            val processedInput = ProcessedIncidentCollectorInput(
                transactionId = "legal-txn-country-$expectedString",
                transactionDate = transactionDate,
                companyId = 900L,
                dateRange = DateRange(startDate, endDate),
                incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
            )
            processedInput.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
            processedInput.originalTimestamp = originalTimestamp

            // Act
            val result = normalizer.normalize(legalIncident, processedInput)

            // Assert
            assertThat(result.countryCode).isEqualTo(expectedString)
            assertThat(result.itemData).contains("\"countryCode\":\"$expectedString\"")
        }
    }

    @Test
    fun `should handle different quantity units correctly`() {
        // Arrange - Only HOURS is available in IncidentQuantityUnit
        val chargePolicy = IncidentQuantityChargePolicy(3.5, IncidentQuantityUnit.HOURS)
        val legalIncident = LegalIncident(
            id = 101L,
            amount = Amount(BigDecimal("350.00"), CurrencyCode.USD),
            description = "Legal consultation charged by HOURS",
            incidentTime = LocalDateTime.of(2024, 2, 20, 9, 30),
            type = IncidentType.LEGAL_CONSULTATION,
            countryCode = CountryCode.USA,
            chargePolicy = chargePolicy
        )

        val startDate = LocalDateTime.of(2024, 2, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 2, 29, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 2, 20, 9, 30)
        val originalTimestamp = Instant.parse("2024-02-20T09:30:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "legal-txn-unit-HOURS",
            transactionDate = transactionDate,
            companyId = 1000L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(legalIncident, processedInput)

        // Assert
        assertThat(result.itemData).contains("\"unit\":\"HOURS\"")
        assertThat(result.itemData).contains("\"value\":3.5")
    }

    @Test
    fun `should handle null fields correctly`() {
        // Arrange
        val legalIncident = LegalIncident(
            id = 0L,
            amount = Amount(BigDecimal("0.01"), CurrencyCode.USD),
            description = "",
            incidentTime = LocalDateTime.of(2024, 1, 1, 0, 0),
            type = IncidentType.LEGAL_CONSULTATION,
            countryCode = CountryCode.USA,
            chargePolicy = null
        )

        val startDate = LocalDateTime.of(2024, 1, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 1, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 1, 1, 0, 0)
        val originalTimestamp = Instant.parse("2024-01-01T00:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "legal-txn-null-fields",
            transactionDate = transactionDate,
            companyId = 1L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LEGAL_CONSULTATION_FEE
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(legalIncident, processedInput)

        // Assert
        assertThat(result.amount).isEqualTo(0.01)
        assertThat(result.contractId).isEqualTo(-1L)
        assertThat(result.itemData).contains("\"id\":0")
        assertThat(result.itemData).contains("\"description\":\"\"")
        assertThat(result.itemData).contains("\"chargePolicy\":null")
    }
}
