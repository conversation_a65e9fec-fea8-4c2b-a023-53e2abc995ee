package com.multiplier.payable.engine.reconciler.diff.handler

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.payableitem.PayableItemKey
import com.multiplier.payable.engine.reconciler.diff.Diff
import org.junit.jupiter.api.Test
import org.mockito.Mockito.`when`
import org.mockito.kotlin.mock
import kotlin.random.Random
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class DeleteHandlerTest {

    private val deleteHandler: DeleteHandler = DeleteHandler()

    @Test
    fun `given delete handler when handling to be deleted items then items are reverted with new versionId`() {
        //given
        val context: DiffHandlerContext<List<PayableItem>> = mock()
        val newVersion = "1"
        val diff = initDiff()
        `when`(context.diff).thenReturn(diff)
        `when`(context.newVersionId()).thenReturn(newVersion)

        //when
        val items = deleteHandler.handle(context)

        //then
        val keyToItemMap = diff.toBeDeleted.associateBy { key(it) }
        for (actualItem in items) {
            val key = key(actualItem)
            assertTrue { keyToItemMap.containsKey(key) }
            val expectedItem = keyToItemMap[key]!!.revertAmountAndMigrateToNewVersion(newVersion)
            assertEquals(expectedItem.amountInBaseCurrency, actualItem.amountInBaseCurrency)
            assertEquals(expectedItem.billableCost, actualItem.billableCost)
            assertEquals(expectedItem.versionId, actualItem.versionId)
        }
    }

    private fun initDiff(): Diff<List<PayableItem>> {
        return Diff(
            toBeDeleted = mutableListOf(
                PayableItem(
                    month = 1,
                    year = 2024,
                    companyId = 1,
                    contractId = 1,
                    lineItemType = LineItemType.PEO_SALARY_DISBURSEMENT.name,
                    amountInBaseCurrency = Random.nextDouble(),
                    baseCurrency = "USD",
                    billableCost = Random.nextDouble(),
                    versionId = "1",
                    cycle = InvoiceCycle.MONTHLY,
                    originalTimestamp = 1,
                    countryCode = "SGP",
                )
            ),
            toBeUpdated = mutableListOf(),
            toBeCreated = mutableListOf()
        )
    }

    fun key(item: PayableItem): PayableItemKey {
        return PayableItemKey(
            month = item.month,
            year = item.year,
            itemType = item.lineItemType,
            contractId = item.contractId,
            annualSeatPaymentTerm = item.annualSeatPaymentTerm,
            companyId = item.companyId,
            periodStartDate = item.periodStartDate,
            periodEndDate = item.periodEndDate,
        )
    }
}