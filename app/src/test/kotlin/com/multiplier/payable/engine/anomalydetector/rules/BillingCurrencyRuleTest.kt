package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.service.PricingService
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class BillingCurrencyRuleTest {
    @Mock
    private lateinit var pricingService: PricingService

    @Mock
    private lateinit var featureFlagService: FeatureFlagService

    @Mock
    private lateinit var jpaAnomalyReportRepository: JpaAnomalyReportRepository

    @Mock
    private lateinit var mockCommand: InvoiceCommand

    private lateinit var billingCurrencyRule: BillingCurrencyRule

    @BeforeEach
    fun setUp() {
        billingCurrencyRule = BillingCurrencyRule(pricingService, featureFlagService)
    }

    @Test
    fun `should return success when feature flag is disabled`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(false)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        // Use a simple empty request - no need to mock the DTO and payable for this test
        val request = InvoiceAnomalyDetectorRequest.builder().build()

        // When
        val result = billingCurrencyRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Rule 'BillingCurrency' is disabled by feature flag"))
    }

    @Test
    fun `should return success when request is invalid`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = InvoiceAnomalyDetectorRequest.builder().build() // Empty request

        // When
        val result = billingCurrencyRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice data or company payable is null - validation skipped"))
    }

    @Test
    fun `should return success when billing currencies match`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val companyId = 1L
        val billingCurrency = CurrencyCode.USD
        val request = createMockRequest(billingCurrency, companyId)

        Mockito.`when`(pricingService.getCompanyBillingCurrency(companyId)).thenReturn(billingCurrency)

        // When
        val result = billingCurrencyRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Billing currencies match"))
    }

    @Test
    fun `should return failure when billing currencies don't match`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val companyId = 1L
        val invoiceBillingCurrency = CurrencyCode.USD
        val companyBillingCurrency = CurrencyCode.EUR
        val request = createMockRequest(invoiceBillingCurrency, companyId)

        Mockito.`when`(pricingService.getCompanyBillingCurrency(companyId)).thenReturn(companyBillingCurrency)

        // When
        val result = billingCurrencyRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertEquals("Invoice Billing currency is expected to be EUR but is USD", result.messages[0])
    }

    @Test
    fun `should handle exceptions gracefully`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val companyId = 1L
        val request = createMockRequest(CurrencyCode.USD, companyId)

        Mockito.`when`(pricingService.getCompanyBillingCurrency(companyId)).thenThrow(RuntimeException("Test exception"))

        // When
        val result = billingCurrencyRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Exception occurred during BillingCurrency anomaly detection"))
    }

    @Test
    fun `should return correct rule type`() {
        // When
        val ruleType = billingCurrencyRule.type

        // Then
        assertEquals(DetectionRuleType.BILLING_CURRENCY, ruleType)
    }

    @Test
    fun `should return correct rule name`() {
        // When
        val ruleName = billingCurrencyRule.ruleName

        // Then
        assertEquals("BillingCurrency", ruleName)
    }

    @Test
    fun `should return correct feature flag name`() {
        // When
        val featureFlagName = billingCurrencyRule.featureFlagName

        // Then
        assertEquals("ENABLE_BILLING_CURRENCY_CHECK", featureFlagName)
    }

    private fun createMockRequest(
        billingCurrency: CurrencyCode,
        companyId: Long,
    ): InvoiceAnomalyDetectorRequest {
        val mockInvoiceDTO = Mockito.mock(InvoiceDTO::class.java)
        Mockito.`when`(mockInvoiceDTO.billingCurrencyCode).thenReturn(billingCurrency)

        val mockCompanyPayable = Mockito.mock(JpaCompanyPayable::class.java)
        Mockito.`when`(mockCompanyPayable.companyId).thenReturn(companyId)

        return InvoiceAnomalyDetectorRequest
            .builder()
            .invoiceDTO(mockInvoiceDTO)
            .payable(mockCompanyPayable)
            .build()
    }
}
