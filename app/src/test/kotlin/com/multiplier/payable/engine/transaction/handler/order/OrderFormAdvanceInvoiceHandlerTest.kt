package com.multiplier.payable.engine.transaction.handler.order

import com.multiplier.core.payable.pricing.adapter.PricingServiceAdapter
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.entities.Invoice
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.generator.FinancialTransactionGenerator
import com.multiplier.payable.engine.transaction.handler.InvoiceTransactionBuilder
import com.multiplier.payable.types.Pricing
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import java.time.LocalDateTime
import com.multiplier.payable.types.CurrencyCode as GraphCurrencyCode

@ExtendWith(MockitoExtension::class)
class OrderFormAdvanceInvoiceHandlerTest {

    @Mock
    private lateinit var pricingServiceAdapter: PricingServiceAdapter

    @Mock
    private lateinit var invoiceTransactionBuilder: InvoiceTransactionBuilder

    @Mock
    private lateinit var invoiceGenerator: FinancialTransactionGenerator

    @InjectMocks
    private lateinit var handler: OrderFormAdvanceInvoiceHandler

    @Test
    fun `test transactionType`() {
        Assertions.assertEquals(TransactionType.ORDER_FORM_ADVANCE, handler.transactionType)
    }

    @Test
    fun `test handle`() {
        val command = InvoiceCommand(
            transactionId = "txn1",
            transactionType = TransactionType.ORDER_FORM_ADVANCE,
            companyId = 1,
            dateRange = DateRange(LocalDateTime.MIN, LocalDateTime.MAX),
            transactionDate = LocalDateTime.MIN,
            cycle = mock(),
        )
        val pricing = mock<Pricing> {
            on { billingCurrencyCode } doReturn GraphCurrencyCode.USD
        }
        doReturn(pricing).`when`(pricingServiceAdapter).getCompanyPricing(1)
        val invoice1 = mock<Invoice>()
        val invoice2 = mock<Invoice>()
        doReturn(listOf(invoice1, invoice2)).`when`(invoiceTransactionBuilder).build(
            command.copy(
                billingCurrencyCode = CurrencyCode.USD,
                dueDate = LocalDateTime.MIN.plusDays(7),
            )
        )

        handler.handle(command)

        Mockito.verify(invoiceGenerator).generate(invoice1)
        Mockito.verify(invoiceGenerator).generate(invoice2)
    }
}
