package com.multiplier.payable.engine.collector.payroll

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.util.dto.payroll.CompanyMemberPayWrapper
import com.multiplier.core.util.dto.payroll.CompanyPayrollWrapper
import com.multiplier.payable.engine.collector.payroll.enricher.CanadaPensionPlanPayrollDataEnricher
import com.multiplier.payable.types.CountryCode
import com.multiplier.payroll.schema.Payroll.PayComponent
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class CanadaPensionPlanPayrollDataEnricherTest {
    private val canadaPensionPlanPayrollDataEnricher = CanadaPensionPlanPayrollDataEnricher()

    @Test
    fun `should enrich with pension plan component when attributes match`() {
        // Given
        val contract = Contract.newBuilder()
            .setId(1)
            .setCountry("CAN")
            .build()
        
        val contributions = listOf(
            PayComponent.newBuilder().setName("Co Portion CPP").setValue(50.0).build(),
            PayComponent.newBuilder().setName("Co Portion QPP").setValue(25.0).build(),
            PayComponent.newBuilder().setName("Other Contribution").setValue(25.0).build()
        )
        
        val memberPay = CompanyMemberPayWrapper(
            contract = contract,
            amountGross = 1000.0,
            contributions = contributions
        )
        
        val nonEligibleContract = Contract.newBuilder()
            .setId(2)
            .setCountry("USA")
            .build()
            
        val nonEligibleMemberPay = CompanyMemberPayWrapper(
            contract = nonEligibleContract,
            amountGross = 1100.0,
            contributions = contributions
        )
        
        val memberPays = listOf(memberPay, nonEligibleMemberPay)
        val companyPayrollWrapper = CompanyPayrollWrapper(
            memberPays = memberPays,
        )

        // When
        val enrichedCompanyPayrollWrapper = canadaPensionPlanPayrollDataEnricher.enrich(
            companyPayrollWrapper = companyPayrollWrapper,
            attributes = listOf(PayrollAttributeKey.CANADA_PENSION_PLAN)
        )

        // Then
        assertThat(enrichedCompanyPayrollWrapper.memberPays).hasSize(2)
        
        val enrichedMemberPay =
            enrichedCompanyPayrollWrapper.memberPays.first { CountryCode.valueOf(it.contract.country) == CountryCode.CAN }
        assertThat(enrichedMemberPay).hasFieldOrPropertyWithValue(
            "payComponents",
            mapOf(PayrollAttributeKey.CANADA_PENSION_PLAN.toComponentName() to 50.0) // CPP only
        )
        
        val nonEnrichedMemberPay =
            enrichedCompanyPayrollWrapper.memberPays.first { CountryCode.valueOf(it.contract.country) != CountryCode.CAN }
        assertThat(nonEnrichedMemberPay.payComponents).isEmpty()
    }

    @Test
    fun `should not enrich when attributes do not match`() {
        // Given
        val contract = Contract.newBuilder()
            .setId(1)
            .setCountry("CAN")
            .build()
        
        val contributions = listOf(
            PayComponent.newBuilder().setName("Co Portion CPP").setValue(50.0).build(),
            PayComponent.newBuilder().setName("Co Portion QPP").setValue(25.0).build()
        )
        
        val memberPay = CompanyMemberPayWrapper(
            contract = contract,
            amountGross = 1000.0,
            contributions = contributions
        )
        
        val memberPays = listOf(memberPay)
        val companyPayrollWrapper = CompanyPayrollWrapper(
            memberPays = memberPays,
        )

        // When
        val enrichedCompanyPayrollWrapper = canadaPensionPlanPayrollDataEnricher.enrich(
            companyPayrollWrapper = companyPayrollWrapper,
            attributes = listOf(PayrollAttributeKey.CANADA_EMPLOYMENT_INSURANCE) // Different attribute
        )

        // Then
        assertThat(enrichedCompanyPayrollWrapper.memberPays).hasSize(1)
        assertThat(enrichedCompanyPayrollWrapper.memberPays[0].payComponents).isEmpty()
    }

    @Test
    fun `should handle empty attributes list`() {
        // Given
        val contract = Contract.newBuilder()
            .setId(1)
            .setCountry("CAN")
            .build()
        
        val contributions = listOf(
            PayComponent.newBuilder().setName("Co Portion CPP").setValue(50.0).build(),
            PayComponent.newBuilder().setName("Co Portion QPP").setValue(25.0).build()
        )
        
        val memberPay = CompanyMemberPayWrapper(
            contract = contract,
            amountGross = 1000.0,
            contributions = contributions
        )
        
        val memberPays = listOf(memberPay)
        val companyPayrollWrapper = CompanyPayrollWrapper(
            memberPays = memberPays,
        )

        // When
        val enrichedCompanyPayrollWrapper = canadaPensionPlanPayrollDataEnricher.enrich(
            companyPayrollWrapper = companyPayrollWrapper,
            attributes = emptyList()
        )

        // Then
        assertThat(enrichedCompanyPayrollWrapper.memberPays).hasSize(1)
        assertThat(enrichedCompanyPayrollWrapper.memberPays[0].payComponents).isEmpty()
    }

    @Test
    fun `should handle null contributions`() {
        // Given
        val contract = Contract.newBuilder()
            .setId(1)
            .setCountry("CAN")
            .build()
        
        val memberPay = CompanyMemberPayWrapper(
            contract = contract,
            amountGross = 1000.0,
            contributions = null
        )
        
        val memberPays = listOf(memberPay)
        val companyPayrollWrapper = CompanyPayrollWrapper(
            memberPays = memberPays,
        )

        // When
        val enrichedCompanyPayrollWrapper = canadaPensionPlanPayrollDataEnricher.enrich(
            companyPayrollWrapper = companyPayrollWrapper,
            attributes = listOf(PayrollAttributeKey.CANADA_PENSION_PLAN)
        )

        // Then
        assertThat(enrichedCompanyPayrollWrapper.memberPays).hasSize(1)
        assertThat(enrichedCompanyPayrollWrapper.memberPays[0].payComponents)
            .containsEntry(PayrollAttributeKey.CANADA_PENSION_PLAN.toComponentName(), 0.0)
    }
}
