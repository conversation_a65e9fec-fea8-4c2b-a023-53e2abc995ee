package com.multiplier.payable.engine.payableitem.key.provider

import com.multiplier.payable.engine.domain.entities.TransactionType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class AnnualPlanPayableItemKeyProviderFactoryTest {

    @Mock
    private lateinit var annualPlanPayableItemKeyProvider: AnnualPlanPayableItemKeyProvider

    @Mock
    private lateinit var annualPlanAorPayableItemKeyProvider: AnnualPlanAorPayableItemKeyProvider

    private lateinit var factory: AnnualPlanPayableItemKeyProviderFactory

    @BeforeEach
    fun setUp() {
        factory = AnnualPlanPayableItemKeyProviderFactory(
            annualPlanPayableItemKeyProvider,
            annualPlanAorPayableItemKeyProvider
        )
    }

    @Test
    fun `should return annual plan provider for ANNUAL_PLAN_INVOICE transaction type`() {
        // When
        val result = factory.get(TransactionType.ANNUAL_PLAN_INVOICE)

        // Then
        assertThat(result).isEqualTo(annualPlanPayableItemKeyProvider)
    }

    @Test
    fun `should return annual plan AOR provider for ANNUAL_PLAN_AOR_INVOICE transaction type`() {
        // When
        val result = factory.get(TransactionType.ANNUAL_PLAN_AOR_INVOICE)

        // Then
        assertThat(result).isEqualTo(annualPlanAorPayableItemKeyProvider)
    }

    @Test
    fun `should throw IllegalArgumentException for unsupported transaction type`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            factory.get(TransactionType.FIRST_INVOICE)
        }

        assertThat(exception.message).isEqualTo("No key provider found for transaction type: FIRST_INVOICE")
    }

    @Test
    fun `should throw IllegalArgumentException for SECOND_INVOICE transaction type`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            factory.get(TransactionType.SECOND_INVOICE)
        }

        assertThat(exception.message).isEqualTo("No key provider found for transaction type: SECOND_INVOICE")
    }

    @Test
    fun `should throw IllegalArgumentException for DEPOSIT_INVOICE transaction type`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            factory.get(TransactionType.DEPOSIT_INVOICE)
        }

        assertThat(exception.message).isEqualTo("No key provider found for transaction type: DEPOSIT_INVOICE")
    }

    @Test
    fun `should throw IllegalArgumentException for FREELANCER_INVOICE transaction type`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            factory.get(TransactionType.FREELANCER_INVOICE)
        }

        assertThat(exception.message).isEqualTo("No key provider found for transaction type: FREELANCER_INVOICE")
    }

    @Test
    fun `should throw IllegalArgumentException for INSURANCE_INVOICE transaction type`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            factory.get(TransactionType.INSURANCE_INVOICE)
        }

        assertThat(exception.message).isEqualTo("No key provider found for transaction type: INSURANCE_INVOICE")
    }

    @Test
    fun `should throw IllegalArgumentException for VAS_INCIDENT_INVOICE transaction type`() {
        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            factory.get(TransactionType.VAS_INCIDENT_INVOICE)
        }

        assertThat(exception.message).isEqualTo("No key provider found for transaction type: VAS_INCIDENT_INVOICE")
    }
}
