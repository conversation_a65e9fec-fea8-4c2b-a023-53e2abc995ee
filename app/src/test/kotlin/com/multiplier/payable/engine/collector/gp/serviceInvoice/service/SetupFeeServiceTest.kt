package com.multiplier.payable.engine.collector.gp.serviceInvoice.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.billing.grpc.billing.Billing
import com.multiplier.core.payable.adapters.BillingServiceAdapter
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.gp.serviceInvoice.mapper.BillingToServiceItemTypesMapper
import com.multiplier.payable.engine.collector.gp.serviceInvoice.model.OneTimeSetupFee
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import java.time.LocalDateTime
import java.time.ZoneOffset
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@ExtendWith(MockitoExtension::class)
class SetupFeeServiceTest {

    private lateinit var payableItemStoreService: PayableItemStoreService
    private lateinit var billingServiceAdapter: BillingServiceAdapter
    private lateinit var billingToServiceItemTypesMapper: BillingToServiceItemTypesMapper
    private lateinit var objectMapper: ObjectMapper
    private lateinit var setupFeeService: SetupFeeService

    private lateinit var invoiceCommand: InvoiceCommand
    private val transactionId = "setup-fee-txn-123"

    @BeforeEach
    fun setUp() {
        payableItemStoreService = mockk(relaxed = true)
        billingServiceAdapter = mockk(relaxed = true)
        billingToServiceItemTypesMapper = mockk(relaxed = true)
        objectMapper = ObjectMapper()
        
        setupFeeService = SetupFeeService(
            payableItemStoreService,
            billingServiceAdapter,
            billingToServiceItemTypesMapper,
            objectMapper
        )

        invoiceCommand = InvoiceCommand(
            transactionId = transactionId,
            transactionType = TransactionType.GP_SERVICE_INVOICE,
            companyId = 456L,
            entityId = 789L,
            transactionDate = LocalDateTime.of(2024, 3, 15, 0, 0),
            cycle = InvoiceCycle.MONTHLY,
            dateRange = DateRange(
                startDate = LocalDateTime.of(2024, 3, 1, 0, 0),
                endDate = LocalDateTime.of(2024, 3, 31, 23, 59)
            )
        )
    }

    @Test
    fun `getSetupFees should return mapped setup fees when billed items exist`() {
        // GIVEN
        val billedItem = Billing.BilledItem.newBuilder().build()
        
        val expectedSetupFee = OneTimeSetupFee(
            transactionId = transactionId,
            billId = "setup-bill-123",
            companyId = 456L,
            amount = 500.0,
            currencyCode = CurrencyCode.USD,
            calculatedTime = System.currentTimeMillis(),
            countryCode = CountryCode.USA,
            month = 3,
            year = 2024,
            entityId = 789L
        )

        every {
            billingServiceAdapter.getBilledItems(
                companyId = 456L,
                startDateSeconds = invoiceCommand.dateRange.startDate.toEpochSecond(ZoneOffset.UTC),
                endDateSeconds = invoiceCommand.dateRange.endDate.toEpochSecond(ZoneOffset.UTC),
                excludeBillingIds = any(),
                lineItemCodes = any(),
                entityId = 789L
            )
        } returns listOf(billedItem)

        every { 
            billingToServiceItemTypesMapper.mapToOneTimeSetupFee(invoiceCommand, billedItem)
        } returns expectedSetupFee

        // WHEN
        val result = setupFeeService.getSetupFees(invoiceCommand)

        // THEN
        assertEquals(1, result.size)
        assertEquals(expectedSetupFee, result[0])
        verify { billingServiceAdapter.getBilledItems(any(), any(), any(), any(), any(), any()) }
        verify { billingToServiceItemTypesMapper.mapToOneTimeSetupFee(invoiceCommand, billedItem) }
    }

    @Test
    fun `normalizeAndSave should return empty list when no setup fees provided`() {
        // WHEN
        val result = setupFeeService.normalizeAndSave(transactionId, emptyList(), invoiceCommand)

        // THEN
        assertTrue(result.isEmpty())
    }

    @Test
    fun `normalizeAndSave should create PayableItemStoreDto with transactionId from setup fee`() {
        // GIVEN
        val setupFee = OneTimeSetupFee(
            transactionId = transactionId,
            billId = "setup-bill-456",
            companyId = 456L,
            amount = 750.0,
            currencyCode = CurrencyCode.EUR,
            calculatedTime = 1640995200000L,
            countryCode = CountryCode.DEU,
            month = 3,
            year = 2024,
            entityId = 789L
        )

        val expectedPayableItemStoreDto = PayableItemStoreDto(
            month = 3,
            year = 2024,
            itemType = LineItemType.ONE_TIME_SETUP_FEE,
            itemData = objectMapper.writeValueAsString(setupFee),
            companyId = 456L,
            contractId = -1,
            amount = 750.0,
            currency = CurrencyCode.EUR,
            versionId = "test-version",
            originalTimestamp = 1640995200000L,
            countryCode = CountryCode.DEU.toString(),
            periodStartDate = invoiceCommand.dateRange.startDate.toLocalDate(),
            periodEndDate = invoiceCommand.dateRange.endDate.toLocalDate(),
            entityId = 789L,
            transactionId = transactionId
        )

        every {
            payableItemStoreService.saveAndIgnoreDuplication(any())
        } returns listOf(expectedPayableItemStoreDto)

        // WHEN
        val result = setupFeeService.normalizeAndSave(
            transactionId,
            listOf(setupFee),
            invoiceCommand
        )

        // THEN
        assertEquals(1, result.size)
        val payableItemStoreDto = result[0]

        // Key assertion: Verify transactionId is correctly set from setupFee.transactionId
        assertEquals(transactionId, payableItemStoreDto.transactionId)

        // Verify other essential fields
        assertEquals(LineItemType.ONE_TIME_SETUP_FEE, payableItemStoreDto.itemType)
        assertEquals(456L, payableItemStoreDto.companyId)
        assertEquals(-1L, payableItemStoreDto.contractId)
        assertEquals(750.0, payableItemStoreDto.amount)
        assertEquals(CurrencyCode.EUR, payableItemStoreDto.currency)
        assertEquals(789L, payableItemStoreDto.entityId)

        // Verify itemData contains the serialized setup fee with transactionId
        assertTrue(payableItemStoreDto.itemData?.contains("\"transactionId\":\"$transactionId\"") == true)
    }

    @Test
    fun `normalizeAndSave should use transactionId from OneTimeSetupFee object`() {
        // GIVEN
        val customTransactionId = "custom-setup-txn-999"
        val setupFee = OneTimeSetupFee(
            transactionId = customTransactionId, // Different from method parameter
            billId = "setup-bill-custom",
            companyId = 999L,
            amount = 1000.0,
            currencyCode = CurrencyCode.GBP,
            calculatedTime = 1640995200000L,
            countryCode = CountryCode.GBR,
            month = 3,
            year = 2024,
            entityId = 789L
        )

        val expectedPayableItemStoreDto = PayableItemStoreDto(
            month = 3,
            year = 2024,
            itemType = LineItemType.ONE_TIME_SETUP_FEE,
            itemData = objectMapper.writeValueAsString(setupFee),
            companyId = 999L,
            contractId = -1,
            amount = 1000.0,
            currency = CurrencyCode.GBP,
            versionId = "test-version",
            originalTimestamp = 1640995200000L,
            countryCode = CountryCode.GBR.toString(),
            periodStartDate = invoiceCommand.dateRange.startDate.toLocalDate(),
            periodEndDate = invoiceCommand.dateRange.endDate.toLocalDate(),
            entityId = 789L,
            transactionId = customTransactionId
        )

        every {
            payableItemStoreService.saveAndIgnoreDuplication(any())
        } returns listOf(expectedPayableItemStoreDto)

        // WHEN - Pass different transactionId as parameter vs setupFee.transactionId
        val result = setupFeeService.normalizeAndSave(
            "method-param-txn", // This should be ignored
            listOf(setupFee),
            invoiceCommand
        )

        // THEN
        assertEquals(1, result.size)
        val payableItemStoreDto = result[0]

        // Should use setupFee.transactionId, not the method parameter
        assertEquals(customTransactionId, payableItemStoreDto.transactionId)
        assertEquals(1000.0, payableItemStoreDto.amount)
        assertEquals(999L, payableItemStoreDto.companyId)
    }

    @Test
    fun `normalizeAndSave should preserve transactionId for multiple setup fees`() {
        // GIVEN
        val setupFee1 = OneTimeSetupFee(
            transactionId = transactionId,
            billId = "setup-bill-1",
            companyId = 456L,
            amount = 300.0,
            currencyCode = CurrencyCode.USD,
            calculatedTime = 1640995200000L,
            countryCode = CountryCode.USA,
            month = 3,
            year = 2024,
            entityId = 789L
        )

        val setupFee2 = OneTimeSetupFee(
            transactionId = transactionId,
            billId = "setup-bill-2",
            companyId = 456L,
            amount = 400.0,
            currencyCode = CurrencyCode.USD,
            calculatedTime = 1640995200000L, // Same timestamp to group together
            countryCode = CountryCode.USA,
            month = 3,
            year = 2024,
            entityId = 789L
        )

        val expectedDto1 = PayableItemStoreDto(
            month = 3, year = 2024, itemType = LineItemType.ONE_TIME_SETUP_FEE,
            itemData = objectMapper.writeValueAsString(setupFee1), companyId = 456L, contractId = -1,
            amount = 300.0, currency = CurrencyCode.USD, versionId = "test-version", originalTimestamp = 1640995200000L,
            countryCode = CountryCode.USA.toString(), periodStartDate = invoiceCommand.dateRange.startDate.toLocalDate(),
            periodEndDate = invoiceCommand.dateRange.endDate.toLocalDate(), entityId = 789L, transactionId = transactionId
        )

        val expectedDto2 = PayableItemStoreDto(
            month = 3, year = 2024, itemType = LineItemType.ONE_TIME_SETUP_FEE,
            itemData = objectMapper.writeValueAsString(setupFee2), companyId = 456L, contractId = -1,
            amount = 400.0, currency = CurrencyCode.USD, versionId = "test-version", originalTimestamp = 1640995200000L,
            countryCode = CountryCode.USA.toString(), periodStartDate = invoiceCommand.dateRange.startDate.toLocalDate(),
            periodEndDate = invoiceCommand.dateRange.endDate.toLocalDate(), entityId = 789L, transactionId = transactionId
        )

        every {
            payableItemStoreService.saveAndIgnoreDuplication(any())
        } returns listOf(expectedDto1, expectedDto2)

        // WHEN
        val result = setupFeeService.normalizeAndSave(
            transactionId,
            listOf(setupFee1, setupFee2),
            invoiceCommand
        )

        // THEN
        assertEquals(2, result.size)

        // Key assertion: Both PayableItemStoreDto should have the correct transactionId
        result.forEach { dto ->
            assertEquals(transactionId, dto.transactionId)
            assertEquals(LineItemType.ONE_TIME_SETUP_FEE, dto.itemType)
        }

        // Verify amounts are preserved and transactionId is consistent
        val transactionIds = result.map { it.transactionId }.distinct()
        assertEquals(1, transactionIds.size) // Should all have the same transactionId
        assertEquals(transactionId, transactionIds[0])

        val amounts = result.mapNotNull { it.amount }.sorted()
        assertEquals(listOf(300.0, 400.0), amounts)
    }

    @Test
    fun `normalizeAndSave should handle exception during save and continue processing`() {
        // GIVEN
        val setupFee = OneTimeSetupFee(
            transactionId = transactionId,
            billId = "setup-bill-error",
            companyId = 456L,
            amount = 200.0,
            currencyCode = CurrencyCode.CAD,
            calculatedTime = 1640995200000L,
            countryCode = CountryCode.CAN,
            month = 3,
            year = 2024,
            entityId = 789L
        )

        every { 
            payableItemStoreService.saveAndIgnoreDuplication(any())
        } throws RuntimeException("Save error")

        // WHEN
        val result = setupFeeService.normalizeAndSave(
            transactionId, 
            listOf(setupFee), 
            invoiceCommand
        )

        // THEN
        assertTrue(result.isEmpty()) // Should return empty list when save fails
        verify { payableItemStoreService.saveAndIgnoreDuplication(any()) }
    }
}
