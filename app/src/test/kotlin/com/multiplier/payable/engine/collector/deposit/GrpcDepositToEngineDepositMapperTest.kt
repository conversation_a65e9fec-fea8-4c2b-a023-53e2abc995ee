package com.multiplier.payable.engine.collector.deposit

import com.multiplier.core.payable.adapters.DepositCalculationBreakdown
import com.multiplier.payable.engine.contract.Contract
import com.multiplier.payable.engine.contract.ContractStatus
import com.multiplier.payable.engine.contract.ContractTerm
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.contract.CountryWorkStatus
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.deposit.DepositType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.time.LocalDate
import kotlin.test.assertNotNull

class GrpcDepositToEngineDepositMapperTest {
    private val grpcDepositToEngineDepositMapper = GrpcDepositToEngineDepositMapper()

    @Test
    fun `should return null for zero total amount`() {
        grpcDepositToEngineDepositMapper.map(
            depositCalculationBreakdown =
                DepositCalculationBreakdown(
                    total = 0.0,
                    salaryCost = 0.0,
                    additionalLeaveCost = 0.0,
                    fixedAdditionalPayCost = 0.0,
                ),
            contract = createTestContract(),
        ).also {
            assertNull(it)
        }
    }

    @Test
    fun `should return map for exist deposit`() {
        grpcDepositToEngineDepositMapper.map(
            depositCalculationBreakdown =
                DepositCalculationBreakdown(
                    total = 10.0,
                    salaryCost = 10.0,
                ),
            contract = createTestContract(),
        ).also {
            assertNotNull(it)
            assertNotNull(it.deposits)
            assertThat(it.deposits.size).isEqualTo(1)
            assertThat(it.deposits.first()).isNotNull
            assertThat(it.deposits.first().type).isEqualTo(DepositType.SALARY)
            assertThat(it.deposits.first().amount).isEqualTo(10.0)
        }
    }

    private fun createTestContract(): Contract =
        Contract(
            id = 1L,
            companyId = 1L,
            memberId = 1L,
            country = "USA",
            type = ContractType.EMPLOYEE,
            status = ContractStatus.ACTIVE,
            term = ContractTerm.FIXED,
            workStatus = CountryWorkStatus.RESIDENT,
            currencyCode = CurrencyCode.USD,
            startOn = LocalDate.of(2024, 4, 14),
            endedOn = LocalDate.of(2024, 4, 20),
        )
}
