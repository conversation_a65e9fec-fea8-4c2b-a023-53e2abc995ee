package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.core.payable.adapters.PayrollServiceAdapter
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.util.dto.payroll.CompanyMemberPayWrapper
import com.multiplier.core.util.dto.payroll.CompanyPayrollWrapper
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@ExtendWith(MockitoExtension::class)
class BaseCurrencyCheckRuleTest {

    @Mock
    private lateinit var payrollServiceAdapter: PayrollServiceAdapter

    @Mock
    private lateinit var featureFlagService: FeatureFlagService

    @Mock
    private lateinit var jpaAnomalyReportRepository: JpaAnomalyReportRepository

    private lateinit var baseCurrencyCheckRule: BaseCurrencyCheckRule

    @BeforeEach
    fun setUp() {
        baseCurrencyCheckRule = BaseCurrencyCheckRule(
            payrollServiceAdapter,
            featureFlagService
        )
    }

    @Test
    fun `should pass when payroll currency matches FX base currency from line items`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        `when`(mockGbFeatureResult.on).thenReturn(true)
        `when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = createRequest(
            payrollCurrency = CurrencyCode.USD,
            lineItemBaseCurrency = "USD",
            billingCurrency = CurrencyCode.SGD
        )

        // Mock payroll service
        mockPayrollService(command, CurrencyCode.USD)

        // When
        val result = baseCurrencyCheckRule.detect(command, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Base currency validation passed - payroll output currency USD matches FX conversion base currency"))
    }

    @Test
    fun `should fail when payroll currency does not match FX base currency from line items`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        `when`(mockGbFeatureResult.on).thenReturn(true)
        `when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = createRequest(
            payrollCurrency = CurrencyCode.USD,
            lineItemBaseCurrency = "SGD",
            billingCurrency = CurrencyCode.SGD
        )

        // Mock payroll service
        mockPayrollService(command, CurrencyCode.USD)

        // When
        val result = baseCurrencyCheckRule.detect(command, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Payroll output currency USD does not match FX conversion base currency SGD"))
    }

    @Test
    fun `should use billing currency as fallback when no base currency in line items`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        `when`(mockGbFeatureResult.on).thenReturn(true)
        `when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = createRequest(
            payrollCurrency = CurrencyCode.USD,
            lineItemBaseCurrency = null, // No base currency in line items
            billingCurrency = CurrencyCode.USD
        )

        // Mock payroll service
        mockPayrollService(command, CurrencyCode.USD)

        // When
        val result = baseCurrencyCheckRule.detect(command, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Base currency validation passed - payroll output currency USD matches FX conversion base currency"))
    }

    @Test
    fun `should fail when billing currency fallback does not match payroll currency`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        `when`(mockGbFeatureResult.on).thenReturn(true)
        `when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = createRequest(
            payrollCurrency = CurrencyCode.USD,
            lineItemBaseCurrency = null, // No base currency in line items
            billingCurrency = CurrencyCode.SGD
        )

        // Mock payroll service
        mockPayrollService(command, CurrencyCode.USD)

        // When
        val result = baseCurrencyCheckRule.detect(command, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Payroll output currency USD does not match FX conversion base currency SGD"))
    }

    @Test
    fun `should fail when payroll currency cannot be determined`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        `when`(mockGbFeatureResult.on).thenReturn(true)
        `when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = createRequest(
            payrollCurrency = CurrencyCode.USD,
            lineItemBaseCurrency = "USD",
            billingCurrency = CurrencyCode.USD
        )

        // Mock payroll service to return empty list
        val monthYear = command.getMonthYear()
        `when`(payrollServiceAdapter.getCompaniesPayroll(
            companyIds = listOf(command.companyId),
            month = monthYear.month,
            year = monthYear.year
        )).thenReturn(emptyList())

        // When
        val result = baseCurrencyCheckRule.detect(command, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Unable to determine payroll output currency"))
    }

    @Test
    fun `should fail when FX conversion base currency cannot be determined`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        `when`(mockGbFeatureResult.on).thenReturn(true)
        `when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = createRequest(
            payrollCurrency = CurrencyCode.USD,
            lineItemBaseCurrency = null, // No base currency
            billingCurrency = null // No billing currency either
        )

        // Mock payroll service
        mockPayrollService(command, CurrencyCode.USD)

        // When
        val result = baseCurrencyCheckRule.detect(command, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Unable to determine FX conversion base currency"))
    }

    @Test
    fun `should handle payroll service exception gracefully`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        `when`(mockGbFeatureResult.on).thenReturn(true)
        `when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = createRequest(
            payrollCurrency = CurrencyCode.USD,
            lineItemBaseCurrency = "USD",
            billingCurrency = CurrencyCode.USD
        )

        // Mock payroll service to throw exception
        val monthYear = command.getMonthYear()
        `when`(payrollServiceAdapter.getCompaniesPayroll(
            companyIds = listOf(command.companyId),
            month = monthYear.month,
            year = monthYear.year
        )).thenThrow(RuntimeException("Payroll service error"))

        // When
        val result = baseCurrencyCheckRule.detect(command, request)

        // Then
        assertFalse(result.success) // Should fail when exception occurs
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Unable to determine payroll output currency"))
    }

    @Test
    fun `should skip check when feature flag is disabled`() {
        // Given
        val command = createInvoiceCommand()
        val request = createRequest(
            payrollCurrency = CurrencyCode.USD,
            lineItemBaseCurrency = "SGD",
            billingCurrency = CurrencyCode.SGD
        )

        // Mock feature flag to be disabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        `when`(mockGbFeatureResult.on).thenReturn(false)
        `when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        // When
        val result = baseCurrencyCheckRule.detect(command, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Rule 'BaseCurrencyCheck' is disabled by feature flag"))
    }

    @Test
    fun `should skip check when invoice data is null`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        `when`(mockGbFeatureResult.on).thenReturn(true)
        `when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(null) // Null invoice
            .payable(createCompanyPayable())
            .build()

        // When
        val result = baseCurrencyCheckRule.detect(command, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice data or company payable is null - validation skipped"))
    }

    @Test
    fun `should skip check when company payable is null`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        `when`(mockGbFeatureResult.on).thenReturn(true)
        `when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(createInvoiceDTO("USD", CurrencyCode.USD))
            .payable(null) // Null payable
            .build()

        // When
        val result = baseCurrencyCheckRule.detect(command, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice data or company payable is null - validation skipped"))
    }

    private fun createInvoiceCommand(): InvoiceCommand {
        // Use a fixed date that matches the company payable (November 2024)
        val transactionDate = LocalDateTime.of(2024, 11, 15, 10, 0, 0)
        return InvoiceCommand(
            transactionId = "test-transaction-id",
            transactionType = TransactionType.SECOND_INVOICE,
            companyId = 123L,
            dateRange = DateRange(transactionDate, transactionDate),
            transactionDate = transactionDate,
            cycle = InvoiceCycle.MONTHLY
        )
    }

    private fun createRequest(
        payrollCurrency: CurrencyCode,
        lineItemBaseCurrency: String?,
        billingCurrency: CurrencyCode?
    ): InvoiceAnomalyDetectorRequest {
        return InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(createInvoiceDTO(lineItemBaseCurrency, billingCurrency))
            .payable(createCompanyPayable())
            .build()
    }

    private fun createInvoiceDTO(
        lineItemBaseCurrency: String?,
        billingCurrency: CurrencyCode?
    ): InvoiceDTO {
        val lineItem = LineItemDTO.builder()
            .description("Test line item")
            .unitAmount(1000.0)
            .quantity(1.0)
            .itemType(LineItemType.GROSS_SALARY)
            .baseCurrency(lineItemBaseCurrency)
            .build()

        return InvoiceDTO.builder()
            .invoiceNo("INV-001")
            .date(LocalDate.now())
            .billingCurrencyCode(billingCurrency)
            .lineItems(listOf(lineItem))
            .totalAmount(1000.0)
            .companyId(123L)
            .companyPayableId(456L)
            .build()
    }

    private fun createCompanyPayable(): JpaCompanyPayable {
        return JpaCompanyPayable().apply {
            id = 456L // Set the ID to avoid NullPointerException
            companyId = 123L
            month = 11
            year = 2024
            totalAmount = 1000.0
            currency = CurrencyCode.USD
        }
    }

    private fun mockPayrollService(command: InvoiceCommand, currency: CurrencyCode) {
        val contract = ContractOuterClass.Contract.newBuilder()
            .setId(789L)
            .setCompanyId(command.companyId)
            .setCountry("USA")
            .build()

        val memberPay = CompanyMemberPayWrapper(
            id = 1L,
            contract = contract,
            currency = currency,
            amountTotalCost = 1000.0
        )

        val companyPayroll = CompanyPayrollWrapper(
            id = 1L,
            memberPays = listOf(memberPay),
            companyId = command.companyId
        )

        val monthYear = command.getMonthYear()
        `when`(payrollServiceAdapter.getCompaniesPayroll(
            companyIds = listOf(command.companyId),
            month = monthYear.month,
            year = monthYear.year
        )).thenReturn(listOf(companyPayroll))
    }
}
