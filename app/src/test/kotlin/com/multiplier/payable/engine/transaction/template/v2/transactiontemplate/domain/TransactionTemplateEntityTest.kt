package com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.domain

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.payable.engine.domain.entities.TransactionType
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.doReturn
import kotlin.test.assertEquals

@ExtendWith(MockitoExtension::class)
class TransactionTemplateEntityTest {

    private val objectMapper = ObjectMapper()

    @Mock
    private lateinit var transactionTemplateRepository: TransactionTemplateRepository

    @Test
    fun `when default is true and default template is already present, should throw error`() {
        val transactionType = TransactionType.FIRST_INVOICE
        val isDefault = true
        val config = ""
        val description = ""

        doReturn(true).`when`(transactionTemplateRepository).isDefaultTransactionTemplatePresent(transactionType)

        assertThrows<IllegalArgumentException> {
            TransactionTemplateEntity(
                transactionType,
                isDefault,
                config,
                description,
                transactionTemplateRepository,
                objectMapper
            )
        }
    }

    @Test
    fun `when default is true and default template is not present, store the values`() {
        val transactionType = TransactionType.FIRST_INVOICE
        val isDefault = true
        val config = ""
        val description = ""

        doReturn(false).`when`(transactionTemplateRepository).isDefaultTransactionTemplatePresent(transactionType)

        val transactionTemplateEntity = TransactionTemplateEntity(
            transactionType,
            isDefault,
            config,
            description,
            transactionTemplateRepository,
            objectMapper
        )

        assertEquals(transactionType, transactionTemplateEntity.transactionType)
        assertEquals(isDefault, transactionTemplateEntity.isDefault)
        assertEquals(config, transactionTemplateEntity.json.toString())
        assertEquals(description, transactionTemplateEntity.description)
    }

    @Test
    fun `when default is not present, should set properties`() {
        val transactionType = TransactionType.FIRST_INVOICE
        val isDefault = false
        val config = ""
        val description = ""

        val transactionTemplateEntity = TransactionTemplateEntity(
            transactionType,
            isDefault,
            config,
            description,
            transactionTemplateRepository,
            objectMapper
        )

        assertEquals(transactionType, transactionTemplateEntity.transactionType)
        assertEquals(isDefault, transactionTemplateEntity.isDefault)
        assertEquals(config, transactionTemplateEntity.json.toString())
        assertEquals(description, transactionTemplateEntity.description)
    }

}