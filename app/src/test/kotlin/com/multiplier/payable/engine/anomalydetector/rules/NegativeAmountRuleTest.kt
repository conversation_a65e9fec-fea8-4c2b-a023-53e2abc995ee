package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class NegativeAmountRuleTest {

    @Mock
    private lateinit var featureFlagService: FeatureFlagService

    @Mock
    private lateinit var jpaAnomalyReportRepository: JpaAnomalyReportRepository

    @Mock
    private lateinit var mockCommand: InvoiceCommand

    private lateinit var negativeAmountRule: NegativeAmountRule

    @BeforeEach
    fun setUp() {
        negativeAmountRule = NegativeAmountRule(featureFlagService)
    }

    @Test
    fun `should return success when feature flag is disabled`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(false)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        // Use a simple empty request
        val request = InvoiceAnomalyDetectorRequest.builder().build()

        // When
        val result = negativeAmountRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Rule 'NegativeAmount' is disabled by feature flag"))
    }

    @Test
    fun `should return success when request is invalid`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = InvoiceAnomalyDetectorRequest.builder().build() // Empty request

        // When
        val result = negativeAmountRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice data or company payable is null - validation skipped"))
    }

    @Test
    fun `should return success when invoice amount is positive`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = createMockRequest(100.0, 100.0)

        // When
        val result = negativeAmountRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice amount is positive - no negative amount anomalies detected"))
    }

    @Test
    fun `should return failure when invoice amount is negative`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = createMockRequest(-100.0, -100.0)

        // When
        val result = negativeAmountRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertEquals("Invoice amount is negative. Credit notes need manual intervention", result.messages[0])
    }

    @Test
    fun `should return success when only payable amount is negative but invoice amount is positive`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = createMockRequest(-100.0, 100.0)

        // When
        val result = negativeAmountRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice amount is positive - no negative amount anomalies detected"))
    }

    @Test
    fun `should return success when only invoice amount is negative but payable amount is positive`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = createMockRequest(100.0, -100.0)

        // When
        val result = negativeAmountRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice amount is positive - no negative amount anomalies detected"))
    }

    @Test
    fun `should return correct rule type`() {
        // When
        val ruleType = negativeAmountRule.type

        // Then
        assertEquals(DetectionRuleType.NEGATIVE_AMOUNT, ruleType)
    }

    @Test
    fun `should return correct rule name`() {
        // When
        val ruleName = negativeAmountRule.ruleName

        // Then
        assertEquals("NegativeAmount", ruleName)
    }

    @Test
    fun `should return correct feature flag name`() {
        // When
        val featureFlagName = negativeAmountRule.featureFlagName

        // Then
        assertEquals("ENABLE_CREDIT_NOTE_CHECK", featureFlagName)
    }

    private fun createMockRequest(payableAmount: Double, invoiceAmount: Double): InvoiceAnomalyDetectorRequest {
        val mockInvoiceDTO = Mockito.mock(InvoiceDTO::class.java)
        Mockito.`when`(mockInvoiceDTO.totalAmount).thenReturn(invoiceAmount)

        val mockCompanyPayable = Mockito.mock(JpaCompanyPayable::class.java)
        Mockito.`when`(mockCompanyPayable.totalAmount).thenReturn(payableAmount)

        return InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(mockInvoiceDTO)
            .payable(mockCompanyPayable)
            .build()
    }
}
