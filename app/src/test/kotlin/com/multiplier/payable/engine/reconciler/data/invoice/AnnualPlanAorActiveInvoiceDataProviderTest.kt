package com.multiplier.payable.engine.reconciler.data.invoice

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.testutils.CompanyPayableTestDataFactory
import com.multiplier.payable.engine.testutils.InvoiceEngineTestDataFactory
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.mapper.CompanyPayableMapper
import com.multiplier.payable.types.CompanyPayableType
import com.multiplier.payable.types.InvoiceStatus
import com.multiplier.payable.types.PayableStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.doReturn
import org.mockito.Mockito.spy
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.eq
import org.mockito.kotlin.isNull
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDateTime
import java.util.stream.Stream

@ExtendWith(MockitoExtension::class)
class AnnualPlanAorActiveInvoiceDataProviderTest {

    @Mock
    private lateinit var jpaCompanyPayableRepository: JpaCompanyPayableRepository

    @Mock
    private lateinit var mapper: CompanyPayableMapper

    @InjectMocks
    private lateinit var provider: AnnualPlanAorActiveInvoiceDataProvider

    private lateinit var command: InvoiceCommand
    private val companyId = 123L
    private val startDate = LocalDateTime.of(2024, 1, 1, 0, 0)
    private val endDate = LocalDateTime.of(2024, 12, 31, 0, 0)

    @BeforeEach
    fun setup() {
        command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            companyId = companyId,
            transactionType = TransactionType.ANNUAL_PLAN_AOR_INVOICE,
            dateRange = DateRange(
                startDate = startDate,
                endDate = endDate
            )
        )
    }

    @Test
    fun `when transactionType is called then return ANNUAL_PLAN_AOR_INVOICE`() {
        // WHEN
        val transactionType = provider.transactionType()

        // THEN
        assertThat(transactionType).isEqualTo(TransactionType.ANNUAL_PLAN_AOR_INVOICE)
    }

    @Test
    fun `given active invoices when fetchActiveInvoices then return mapped company payables`() {
        // GIVEN
        val jpaCompanyPayable = CompanyPayableTestDataFactory.createJpaCompanyPayable(
            status = PayableStatus.DRAFT,
            invoice = CompanyPayableTestDataFactory.createJpaInvoice(
                status = InvoiceStatus.DRAFT
            ),
            type = CompanyPayableType.ANNUAL_PLAN_AOR
        )
        val jpaCompanyPayables = listOf(jpaCompanyPayable)

        whenever(
            jpaCompanyPayableRepository.findByCompanyIdAndTypeAndDateRangeOverlap(
                companyId,
                CompanyPayableType.ANNUAL_PLAN_AOR,
                startDate.toLocalDate(),
                endDate.toLocalDate()
            )
        ).thenReturn(jpaCompanyPayables)

        val expectedCompanyPayables = listOf(
            CompanyPayableTestDataFactory.createCompanyPayable(
                itemType = TransactionType.ANNUAL_PLAN_AOR_INVOICE
            )
        )
        whenever(mapper.mapCompanyPayables(jpaCompanyPayables)).thenReturn(expectedCompanyPayables)

        // WHEN
        val result = provider.fetchActiveInvoices(command)

        // THEN
        assertThat(result).isEqualTo(expectedCompanyPayables)
        verify(jpaCompanyPayableRepository).findByCompanyIdAndTypeAndDateRangeOverlap(
            companyId,
            CompanyPayableType.ANNUAL_PLAN_AOR,
            startDate.toLocalDate(),
            endDate.toLocalDate()
        )
        verify(mapper).mapCompanyPayables(jpaCompanyPayables)
    }

    @ParameterizedTest
    @MethodSource("statusCombinations")
    fun `given various status combinations when fetchActiveInvoices then filter correctly`(
        payableStatus: PayableStatus,
        invoiceStatus: InvoiceStatus?,
        expectedSize: Int
    ) {
        // GIVEN
        val invoice = if (invoiceStatus != null) {
            CompanyPayableTestDataFactory.createJpaInvoice(status = invoiceStatus)
        } else {
            null
        }

        val jpaCompanyPayable = CompanyPayableTestDataFactory.createJpaCompanyPayable(
            status = payableStatus,
            invoice = invoice,
            type = CompanyPayableType.ANNUAL_PLAN_AOR
        )
        val jpaCompanyPayables = listOf(jpaCompanyPayable)

        whenever(
            jpaCompanyPayableRepository.findByCompanyIdAndTypeAndDateRangeOverlap(
                companyId,
                CompanyPayableType.ANNUAL_PLAN_AOR,
                startDate.toLocalDate(),
                endDate.toLocalDate()
            )
        ).thenReturn(jpaCompanyPayables)

        // For inactive statuses, the filtering will result in an empty list
        val filteredList = if (expectedSize > 0) jpaCompanyPayables else emptyList()

        if (expectedSize > 0) {
            val expectedCompanyPayables = listOf(
                CompanyPayableTestDataFactory.createCompanyPayable(
                    itemType = TransactionType.ANNUAL_PLAN_AOR_INVOICE
                )
            )
            whenever(mapper.mapCompanyPayables(filteredList)).thenReturn(expectedCompanyPayables)
        }

        // WHEN
        val result = provider.fetchActiveInvoices(command)

        // THEN
        assertThat(result.size).isEqualTo(expectedSize)
        verify(jpaCompanyPayableRepository).findByCompanyIdAndTypeAndDateRangeOverlap(
            companyId,
            CompanyPayableType.ANNUAL_PLAN_AOR,
            startDate.toLocalDate(),
            endDate.toLocalDate()
        )

        if (expectedSize > 0) {
            verify(mapper).mapCompanyPayables(filteredList)
        } else {
            verify(mapper).mapCompanyPayables(emptyList())
        }
    }

    @Test
    fun `given no invoices when fetchActiveInvoices then return empty list`() {
        // GIVEN
        whenever(
            jpaCompanyPayableRepository.findByCompanyIdAndTypeAndDateRangeOverlap(
                companyId,
                CompanyPayableType.ANNUAL_PLAN_AOR,
                startDate.toLocalDate(),
                endDate.toLocalDate()
            )
        ).thenReturn(emptyList())

        // The implementation will call mapper with empty list
        whenever(mapper.mapCompanyPayables(emptyList())).thenReturn(emptyList())

        // WHEN
        val result = provider.fetchActiveInvoices(command)

        // THEN
        assertThat(result).isEmpty()
        verify(jpaCompanyPayableRepository).findByCompanyIdAndTypeAndDateRangeOverlap(
            companyId,
            CompanyPayableType.ANNUAL_PLAN_AOR,
            startDate.toLocalDate(),
            endDate.toLocalDate()
        )
        verify(mapper).mapCompanyPayables(emptyList())
    }

    @Test
    fun `given multiple invoices with different statuses when fetchActiveInvoices then filter correctly`() {
        // GIVEN
        val activePayable = CompanyPayableTestDataFactory.createJpaCompanyPayable(
            id = 1L,
            status = PayableStatus.DRAFT,
            invoice = CompanyPayableTestDataFactory.createJpaInvoice(status = InvoiceStatus.DRAFT),
            type = CompanyPayableType.ANNUAL_PLAN_AOR
        )

        val inactivePayableStatus = CompanyPayableTestDataFactory.createJpaCompanyPayable(
            id = 2L,
            status = PayableStatus.VOIDED,
            invoice = CompanyPayableTestDataFactory.createJpaInvoice(status = InvoiceStatus.DRAFT),
            type = CompanyPayableType.ANNUAL_PLAN_AOR
        )

        val inactiveInvoiceStatus = CompanyPayableTestDataFactory.createJpaCompanyPayable(
            id = 3L,
            status = PayableStatus.DRAFT,
            invoice = CompanyPayableTestDataFactory.createJpaInvoice(status = InvoiceStatus.VOIDED),
            type = CompanyPayableType.ANNUAL_PLAN_AOR
        )

        val jpaCompanyPayables = listOf(activePayable, inactivePayableStatus, inactiveInvoiceStatus)

        whenever(
            jpaCompanyPayableRepository.findByCompanyIdAndTypeAndDateRangeOverlap(
                companyId,
                CompanyPayableType.ANNUAL_PLAN_AOR,
                startDate.toLocalDate(),
                endDate.toLocalDate()
            )
        ).thenReturn(jpaCompanyPayables)

        val expectedCompanyPayables = listOf(
            CompanyPayableTestDataFactory.createCompanyPayable(
                id = 1L,
                itemType = TransactionType.ANNUAL_PLAN_AOR_INVOICE
            )
        )

        whenever(mapper.mapCompanyPayables(listOf(activePayable))).thenReturn(expectedCompanyPayables)

        // WHEN
        val result = provider.fetchActiveInvoices(command)

        // THEN
        assertThat(result).isEqualTo(expectedCompanyPayables)
        verify(jpaCompanyPayableRepository).findByCompanyIdAndTypeAndDateRangeOverlap(
            companyId,
            CompanyPayableType.ANNUAL_PLAN_AOR,
            startDate.toLocalDate(),
            endDate.toLocalDate()
        )
        verify(mapper).mapCompanyPayables(listOf(activePayable))
    }

    @Test
    fun `when fetchAndAggregateInvoiceItems then call fetchActiveInvoices and aggregateInvoiceItems`() {
        // GIVEN
        val activeInvoices = listOf(
            CompanyPayableTestDataFactory.createCompanyPayable(
                itemType = TransactionType.ANNUAL_PLAN_AOR_INVOICE,
                items = listOf(
                    CompanyPayableTestDataFactory.createPayableItem()
                )
            )
        )

        // Mock fetchActiveInvoices to return our test data
        val spyProvider = spy(provider)
        doReturn(activeInvoices).`when`(spyProvider).fetchActiveInvoices(eq(command), isNull())

        // WHEN
        val result = spyProvider.fetchAndAggregateInvoiceItems(command)

        // THEN
        verify(spyProvider).fetchActiveInvoices(eq(command), isNull())
        assertThat(result).isEqualTo(activeInvoices[0].items)
    }

    @Test
    fun `when fetchActiveInvoices with dateRange then call fetchActiveInvoices without dateRange`() {
        // GIVEN
        val dateRange = DateRange(
            startDate = LocalDateTime.of(2024, 2, 1, 0, 0),
            endDate = LocalDateTime.of(2024, 2, 29, 0, 0)
        )

        val activeInvoices = listOf(
            CompanyPayableTestDataFactory.createCompanyPayable(
                itemType = TransactionType.ANNUAL_PLAN_AOR_INVOICE
            )
        )

        // Mock fetchActiveInvoices to return our test data
        val spyProvider = spy(provider)
        doReturn(activeInvoices).`when`(spyProvider).fetchActiveInvoices(eq(command))

        // WHEN
        val result = spyProvider.fetchActiveInvoices(command, dateRange)

        // THEN
        verify(spyProvider).fetchActiveInvoices(eq(command))
        assertThat(result).isEqualTo(activeInvoices)
    }

    @Test
    fun `when aggregateInvoiceItems then return same items`() {
        // GIVEN
        val payableItems = listOf(
            CompanyPayableTestDataFactory.createPayableItem()
        )

        // WHEN
        val result = provider.aggregateInvoiceItems(payableItems)

        // THEN
        assertThat(result).isEqualTo(payableItems)
    }

    companion object {
        @JvmStatic
        fun statusCombinations(): Stream<Arguments> {
            return Stream.of(
                // Active combinations
                Arguments.of(PayableStatus.DRAFT, InvoiceStatus.DRAFT, 1),
                Arguments.of(PayableStatus.SUBMITTED, InvoiceStatus.AUTHORIZED, 1),
                Arguments.of(PayableStatus.AUTHORIZED, InvoiceStatus.PAID, 1),

                // Inactive payable status
                Arguments.of(PayableStatus.VOIDED, InvoiceStatus.DRAFT, 0),
                Arguments.of(PayableStatus.DELETED, InvoiceStatus.AUTHORIZED, 0),

                // Inactive invoice status
                Arguments.of(PayableStatus.DRAFT, InvoiceStatus.VOIDED, 0),
                Arguments.of(PayableStatus.SUBMITTED, InvoiceStatus.DELETED, 0),

                // Null invoice (should be treated as deleted)
                Arguments.of(PayableStatus.DRAFT, null, 0)
            )
        }
    }
}
