package com.multiplier.payable.engine.collector.adjustment.gp

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.gp.serviceInvoice.model.YearEndDocumentationFee
import com.multiplier.payable.engine.collector.gp.serviceInvoice.service.YearEndDocumentationService
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDateTime

class YearEndDocumentationAdjustmentCollectorTest {

    private lateinit var yearEndDocumentationService: YearEndDocumentationService
    private lateinit var collector: YearEndDocumentationAdjustmentCollector

    @BeforeEach
    fun setUp() {
        yearEndDocumentationService = mock()
        collector = YearEndDocumentationAdjustmentCollector(yearEndDocumentationService)
    }

    @Test
    fun `getSupportedType should return correct LineItemType`() {
        // When
        val result = collector.getSupportedType()

        // Then
        assertEquals(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_YEAR_END_DOCUMENTATION, result)
    }

    @Test
    fun `handle should process successfully when documentation fees exist`() {
        // Given
        val command = createInvoiceCommand()
        val documentationFees = listOf(
            createYearEndDocumentationFee(amount = 400.0),
            createYearEndDocumentationFee(amount = 300.0)
        )
        val adjustmentFees = documentationFees.map { it.copy(amount = it.amount * -1) }

        whenever(yearEndDocumentationService.getYearEndDocumentationFees(command))
            .thenReturn(documentationFees)

        // When
        collector.handle(command)

        // Then
        verify(yearEndDocumentationService).getYearEndDocumentationFees(command)
        verify(yearEndDocumentationService).normalizeAndSave(
            transactionId = command.transactionId,
            documentationFees = adjustmentFees,
            command = command,
            lineItemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_YEAR_END_DOCUMENTATION
        )
    }

    @Test
    fun `handle should convert positive amounts to negative for adjustments`() {
        // Given
        val command = createInvoiceCommand()
        val documentationFees = listOf(
            createYearEndDocumentationFee(amount = 400.0),
            createYearEndDocumentationFee(amount = 225.75)
        )
        val expectedAdjustmentFees = listOf(
            createYearEndDocumentationFee(amount = -400.0),
            createYearEndDocumentationFee(amount = -225.75)
        )

        whenever(yearEndDocumentationService.getYearEndDocumentationFees(command))
            .thenReturn(documentationFees)

        // When
        collector.handle(command)

        // Then
        verify(yearEndDocumentationService).normalizeAndSave(
            transactionId = command.transactionId,
            documentationFees = expectedAdjustmentFees,
            command = command,
            lineItemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_YEAR_END_DOCUMENTATION
        )
    }

    @Test
    fun `handle should not call normalizeAndSave when no documentation fees exist`() {
        // Given
        val command = createInvoiceCommand()
        whenever(yearEndDocumentationService.getYearEndDocumentationFees(command))
            .thenReturn(emptyList())

        // When
        collector.handle(command)

        // Then
        verify(yearEndDocumentationService).getYearEndDocumentationFees(command)
        verify(yearEndDocumentationService).normalizeAndSave(
            transactionId = command.transactionId,
            documentationFees = emptyList(),
            command = command,
            lineItemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_YEAR_END_DOCUMENTATION
        )
    }

    @Test
    fun `handle should propagate exception when getYearEndDocumentationFees throws exception`() {
        // Given
        val command = createInvoiceCommand()
        val exception = RuntimeException("Service error")
        whenever(yearEndDocumentationService.getYearEndDocumentationFees(command))
            .thenThrow(exception)

        // When & Then
        val thrownException = assertThrows(RuntimeException::class.java) {
            collector.handle(command)
        }
        assertEquals("Service error", thrownException.message)
        verify(yearEndDocumentationService).getYearEndDocumentationFees(command)
        verify(yearEndDocumentationService, never()).normalizeAndSave(any(), any(), any(), any())
    }

    @Test
    fun `handle should propagate exception when normalizeAndSave throws exception`() {
        // Given
        val command = createInvoiceCommand()
        val documentationFees = listOf(createYearEndDocumentationFee(amount = 400.0))
        val adjustmentFees = documentationFees.map { it.copy(amount = it.amount * -1) }
        val exception = RuntimeException("Save error")

        whenever(yearEndDocumentationService.getYearEndDocumentationFees(command))
            .thenReturn(documentationFees)
        whenever(yearEndDocumentationService.normalizeAndSave(any(), any(), any(), any()))
            .thenThrow(exception)

        // When & Then
        val thrownException = assertThrows(RuntimeException::class.java) {
            collector.handle(command)
        }
        assertEquals("Save error", thrownException.message)
        verify(yearEndDocumentationService).getYearEndDocumentationFees(command)
        verify(yearEndDocumentationService).normalizeAndSave(
            transactionId = command.transactionId,
            documentationFees = adjustmentFees,
            command = command,
            lineItemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_YEAR_END_DOCUMENTATION
        )
    }

    private fun createInvoiceCommand(): InvoiceCommand {
        return InvoiceCommand(
            transactionId = "test-transaction-123",
            transactionType = TransactionType.GP_SERVICE_INVOICE,
            companyId = 123L,
            dateRange = DateRange(
                startDate = LocalDateTime.of(2024, 1, 1, 0, 0),
                endDate = LocalDateTime.of(2024, 1, 31, 23, 59, 59)
            ),
            transactionDate = LocalDateTime.of(2024, 1, 15, 10, 0),
            cycle = InvoiceCycle.MONTHLY
        )
    }

    private fun createYearEndDocumentationFee(amount: Double): YearEndDocumentationFee {
        return YearEndDocumentationFee(
            transactionId = "test-transaction-123",
            billId = "bill-123",
            companyId = 123L,
            amount = amount,
            noOfMembers = 20,
            currencyCode = CurrencyCode.USD,
            calculatedTime = 123456789L,
            countryCode = CountryCode.USA,
            month = 1,
            year = 2024,
            entityId = 456L
        )
    }
}
