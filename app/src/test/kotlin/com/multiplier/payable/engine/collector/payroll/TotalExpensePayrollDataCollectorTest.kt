package com.multiplier.payable.engine.collector.payroll

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.DefaultProcessedCollectorInput
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.domain.aggregates.MonthYearDuration
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentCaptor
import org.mockito.Captor
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.doReturn
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.capture
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class TotalExpensePayrollDataCollectorTest {

    @Mock
    private lateinit var dataCollectorInputProcessor: DataCollectorInputProcessor<DefaultProcessedCollectorInput>

    @Mock
    private lateinit var contractPayrollService: ContractPayrollService

    @Mock
    private lateinit var contractPayrollItemStoreService: ContractPayrollItemStoreService

    @InjectMocks
    private lateinit var totalExpensePayrollDataCollector: TotalExpensePayrollDataCollector

    @Captor
    private lateinit var memberPayableItemStoreHelperInputCaptor: ArgumentCaptor<ContractPayrollsItemStoreInput>

    @Nested
    inner class GetSupportedType {

        @Test
        fun `should return EOR_EXPENSE_DISBURSEMENT`() {
            // WHEN
            val result = totalExpensePayrollDataCollector.getSupportedType()

            // THEN
            assertEquals(LineItemType.EOR_EXPENSE_DISBURSEMENT, result)
        }
    }

    @Nested
    inner class Handle {

        @Test
        fun `should call processInput and handle`() {
            // GIVEN
            val companyId = 1L
            val transactionId = "transactionId"
            val timeQueryDuration = MonthYearDuration(
                from = MonthYear(1, 2021),
                to = MonthYear(1, 2021)
            )
            val companyIds = setOf(companyId)

            val command = Mockito.mock(InvoiceCommand::class.java)
            val defaultProcessedCollectorInput = Mockito.mock(DefaultProcessedCollectorInput::class.java)
            val contractPayroll = Mockito.mock(ContractPayroll::class.java)

            val expectedMemberPayableItemStoreHelperInput = ContractPayrollsItemStoreInput(
                transactionId = transactionId,
                contractPayrolls = listOf(contractPayroll),
                monthYearDuration = timeQueryDuration,
                itemType = LineItemType.EOR_EXPENSE_DISBURSEMENT,
                isBillable = { it.totalExpenseAmount != null },
                amountCost = { it.totalExpenseAmount ?: 0.0 }
            )

            // WHEN
            doReturn(defaultProcessedCollectorInput).whenever(dataCollectorInputProcessor).process(command)
            doReturn(timeQueryDuration).whenever(defaultProcessedCollectorInput).timeQueryDuration
            doReturn(companyIds).whenever(defaultProcessedCollectorInput).companyIds
            doReturn(transactionId).whenever(defaultProcessedCollectorInput).transactionId
            doReturn(listOf(contractPayroll)).whenever(contractPayrollService).getContractPayrolls(
                companyIds.toList(), 1, 2021)
            doReturn(ContractType.EMPLOYEE).whenever(contractPayroll).type
            doReturn(false).whenever(contractPayroll).isOffCycle

            totalExpensePayrollDataCollector.handle(command)

            // THEN
            verify(contractPayrollItemStoreService)
                .normalizeAndSaveForCompanyPayrolls(capture(memberPayableItemStoreHelperInputCaptor))

            assertThat(memberPayableItemStoreHelperInputCaptor.value)
                .usingRecursiveComparison()
                .isEqualTo(expectedMemberPayableItemStoreHelperInput)
        }

    }
}