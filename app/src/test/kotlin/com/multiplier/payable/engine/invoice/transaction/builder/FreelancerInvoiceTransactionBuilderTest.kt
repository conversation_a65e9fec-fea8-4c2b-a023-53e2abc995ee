package com.multiplier.payable.engine.invoice.transaction.builder

import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.data.CompanyPayableDataProvider
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class FreelancerInvoiceTransactionBuilderTest {

    @MockK
    private lateinit var companyPayableDataProvider: CompanyPayableDataProvider

    @MockK
    private lateinit var builderService: InvoiceTransactionBuilderService

    @InjectMockKs
    private lateinit var builder: FreelancerInvoiceTransactionBuilder


    @Test
    fun `should build invoices for given transaction`() {
        val command = mockk<InvoiceCommand> {
            every { transactionId } returns "id"
        }
        val companyPayables = listOf(mockk<CompanyPayable>())

        every { companyPayableDataProvider.findByTransactionId(any()) } returns companyPayables
        every {builderService.createInvoices(any(), any(), any()) } returns emptyList()

        builder.build(command)

        verify { companyPayableDataProvider.findByTransactionId(any()) }
        verify { builderService.createInvoices(any(), any(), any()) }
    }
}
