package com.multiplier.payable.engine.domain.aggregates

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDateTime
import java.util.stream.Stream

class DateRangeTest {
    @Test
    fun `should return correct number of invoicing days inclusive`() {
        val dateRange =
            DateRange(
                startDate = LocalDateTime.of(2021, 1, 1, 0, 0),
                endDate = LocalDateTime.of(2021, 1, 31, 23, 59),
            )

        assertThat(dateRange.numberOfInvoicingDaysInclusive()).isEqualTo(30)
    }

    @ParameterizedTest
    @MethodSource
    fun `should compare two date ranges correctly`(
        dateRange1: DateRange,
        dateRange2: DateRange,
        expectedResult: Int,
    ) {
        assertThat(dateRange1.compareTo(dateRange2)).isEqualTo(expectedResult)
        assertThat(dateRange2.compareTo(dateRange1)).isEqualTo(-expectedResult)
    }

    @ParameterizedTest
    @MethodSource
    fun `should return overlapping date range`(
        dateRange1: DateRange,
        dateRange2: DateRange,
        expectedOverlap: Boolean,
    ) {
        assertThat(dateRange1.isOverlapping(dateRange2)).isEqualTo(expectedOverlap)
        assertThat(dateRange2.isOverlapping(dateRange1)).isEqualTo(expectedOverlap)
    }

    companion object {
        @JvmStatic
        fun `should return overlapping date range`(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    DateRange(
                        startDate = LocalDateTime.of(2021, 1, 1, 0, 0),
                        endDate = LocalDateTime.of(2021, 1, 31, 23, 59),
                    ),
                    DateRange(
                        startDate = LocalDateTime.of(2021, 1, 1, 0, 0),
                        endDate = LocalDateTime.of(2021, 1, 31, 23, 59),
                    ),
                    true,
                ),
                Arguments.of(
                    DateRange(
                        startDate = LocalDateTime.of(2021, 1, 1, 0, 0),
                        endDate = LocalDateTime.of(2021, 1, 31, 23, 59),
                    ),
                    DateRange(
                        startDate = LocalDateTime.of(2021, 2, 1, 0, 0),
                        endDate = LocalDateTime.of(2021, 12, 15, 23, 59),
                    ),
                    false,
                ),
                Arguments.of(
                    DateRange(
                        startDate = LocalDateTime.of(2021, 1, 1, 0, 0),
                        endDate = LocalDateTime.of(2021, 1, 31, 23, 59),
                    ),
                    DateRange(
                        startDate = LocalDateTime.of(2021, 1, 15, 0, 0),
                        endDate = LocalDateTime.of(2021, 2, 2, 23, 59),
                    ),
                    true,
                ),
            )
        }

        @JvmStatic
        fun `should compare two date ranges correctly`(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    DateRange(
                        startDate = LocalDateTime.of(2021, 1, 1, 0, 0),
                        endDate = LocalDateTime.of(2021, 1, 31, 23, 59),
                    ),
                    DateRange(
                        startDate = LocalDateTime.of(2021, 1, 1, 0, 0),
                        endDate = LocalDateTime.of(2021, 1, 31, 23, 59),
                    ),
                    0,
                ),
                Arguments.of(
                    DateRange(
                        startDate = LocalDateTime.of(2021, 1, 1, 0, 0),
                        endDate = LocalDateTime.of(2021, 1, 31, 23, 59),
                    ),
                    DateRange(
                        startDate = LocalDateTime.of(2021, 1, 2, 0, 0),
                        endDate = LocalDateTime.of(2021, 1, 31, 23, 59),
                    ),
                    -1,
                ),
                Arguments.of(
                    DateRange(
                        startDate = LocalDateTime.of(2021, 1, 1, 0, 0),
                        endDate = LocalDateTime.of(2021, 1, 31, 23, 59),
                    ),
                    DateRange(
                        startDate = LocalDateTime.of(2021, 1, 1, 0, 0),
                        endDate = LocalDateTime.of(2021, 2, 2, 23, 59),
                    ),
                    -1,
                )
            )
        }
    }
}
