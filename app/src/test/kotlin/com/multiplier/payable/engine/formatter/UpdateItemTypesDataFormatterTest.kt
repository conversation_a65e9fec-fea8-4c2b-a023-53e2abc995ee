package com.multiplier.payable.engine.formatter

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.formatter.accessor.PropertyAccessor
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.doReturn
import java.util.*

class UpdateItemTypesDataFormatterTest {

    @Mock
    private lateinit var propertyAccessor: PropertyAccessor<PayableItem>

    @Mock
    private lateinit var dataFormatterParam: DataFormatterParam

    private lateinit var formatter: UpdateItemTypesDataFormatter

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        formatter = UpdateItemTypesDataFormatter(propertyAccessor)
        formatter.dataFormatterParam = dataFormatterParam
    }

    @Test
    fun `newInstance should return a new instance of UpdateItemTypesDataFormatter`() {
        val newInstance = formatter.newInstance()

        assert(newInstance is UpdateItemTypesDataFormatter)
        assert(newInstance !== formatter)
    }

    @Test
    fun `getOrder should return the order from dataFormatterParam`() {
        val expectedOrder = 10L
        doReturn(expectedOrder).`when`(dataFormatterParam).order

        val result = formatter.getOrder()

        assertEquals(expectedOrder, result)
    }

    @Nested
    inner class FormatTests {

        @Test
        fun `format should throw IllegalArgumentException when supportedItemTypes is null or empty`() {
            // Given
            doReturn(null).`when`(dataFormatterParam).supportedItemTypes

            // When/Then
            assertThrows<IllegalArgumentException> {
                formatter.format(emptyList())
            }

            // Given
            doReturn(emptyList<String>()).`when`(dataFormatterParam).supportedItemTypes

            // When/Then
            assertThrows<IllegalArgumentException> {
                formatter.format(emptyList())
            }
        }

        @Test
        fun `format should throw IllegalArgumentException when targetLineItemType is null`() {
            // Given
            val supportedItemTypes = listOf(LineItemType.MANAGEMENT_FEE_PEO.name)
            doReturn(supportedItemTypes).`when`(dataFormatterParam).supportedItemTypes

            val additionalParam = DataFormatterParam.DataFormatterAdditionalParam(
                targetLineItemType = null
            )
            doReturn(additionalParam).`when`(dataFormatterParam).additionalParam

            // When/Then
            assertThrows<IllegalArgumentException> {
                formatter.format(emptyList())
            }
        }

        @Test
        fun `format should update line item type and currency fields correctly`() {
            // Given
            val supportedItemTypes = listOf(LineItemType.MANAGEMENT_FEE_PEO.name)
            doReturn(supportedItemTypes).`when`(dataFormatterParam).supportedItemTypes

            val targetLineItemType = LineItemType.ADMIN_FEES.name
            val additionalParam = DataFormatterParam.DataFormatterAdditionalParam(
                targetLineItemType = targetLineItemType
            )
            doReturn(additionalParam).`when`(dataFormatterParam).additionalParam

            val originalItem = createPayableItem(
                lineItemType = LineItemType.MANAGEMENT_FEE_PEO.name,
                baseCurrency = CurrencyCode.INR.name,
                amountInBaseCurrency = 500.0,
                billingCurrency = CurrencyCode.USD.name,
                billableCost = 1000.0
            )

            // When
            val result = formatter.format(listOf(originalItem))

            // Then
            assertEquals(1, result.size)
            val updatedItem = result[0]
            assertEquals(targetLineItemType, updatedItem.lineItemType)
            assertEquals(CurrencyCode.USD.name, updatedItem.baseCurrency)
            assertEquals(1000.0, updatedItem.amountInBaseCurrency)
        }

        @Test
        fun `format should filter items based on supportedItemTypes`() {
            // Given
            val supportedItemTypes = listOf(LineItemType.MANAGEMENT_FEE_PEO.name)
            doReturn(supportedItemTypes).`when`(dataFormatterParam).supportedItemTypes

            val targetLineItemType = LineItemType.ADMIN_FEES.name
            val additionalParam = DataFormatterParam.DataFormatterAdditionalParam(
                targetLineItemType = targetLineItemType
            )
            doReturn(additionalParam).`when`(dataFormatterParam).additionalParam

            val supportedItem = createPayableItem(lineItemType = LineItemType.MANAGEMENT_FEE_PEO.name)
            val unsupportedItem = createPayableItem(lineItemType = LineItemType.GROSS_SALARY.name)

            // When
            val result = formatter.format(listOf(supportedItem, unsupportedItem))

            // Then
            assertEquals(2, result.size)
            val updatedSupportedItem = result.find { it.lineItemType == targetLineItemType }!!
            val unchangedUnsupportedItem = result.find { it.lineItemType == LineItemType.GROSS_SALARY.name }!!

            assertEquals(targetLineItemType, updatedSupportedItem.lineItemType)
            assertEquals(LineItemType.GROSS_SALARY.name, unchangedUnsupportedItem.lineItemType)
        }
    }

    private fun createPayableItem(
        lineItemType: String = LineItemType.MANAGEMENT_FEE_PEO.name,
        baseCurrency: String = CurrencyCode.USD.name,
        amountInBaseCurrency: Double = 1000.0,
        billingCurrency: String = CurrencyCode.USD.name,
        billableCost: Double = 1000.0
    ): PayableItem {
        return PayableItem(
            companyPayableLineItemIds = listOf(UUID.randomUUID()),
            month = 12,
            year = 2024,
            lineItemType = lineItemType,
            contractId = 1L,
            employmentType = "FULL_TIME",
            companyId = 100L,
            description = "Test Description",
            amountInBaseCurrency = amountInBaseCurrency,
            baseCurrency = baseCurrency,
            billableCost = billableCost,
            billingCurrency = billingCurrency,
            originalTimestamp = System.currentTimeMillis(),
            cycle = InvoiceCycle.MONTHLY,
            countryCode = CountryCode.USA.name
        )
    }
}
