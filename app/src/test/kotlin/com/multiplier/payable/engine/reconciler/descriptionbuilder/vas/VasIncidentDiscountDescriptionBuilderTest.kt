package com.multiplier.payable.engine.reconciler.descriptionbuilder.vas

import com.multiplier.core.payable.adapters.VasIncidentServiceAdapter
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import com.multiplier.payable.engine.vas.Incident
import com.multiplier.payable.engine.vas.IncidentFeeDiscount
import com.multiplier.payable.engine.vas.IncidentType
import com.multiplier.payable.engine.vas.IncidentWrapper
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class VasIncidentDiscountDescriptionBuilderTest {
    @RelaxedMockK
    private lateinit var vasIncidentService: VasIncidentServiceAdapter

    @InjectMockKs
    private lateinit var builder: VasIncidentDiscountDescriptionBuilder

    @Test
    fun getLineItemType() {
        assertEquals(LineItemType.VAS_INCIDENT_DISCOUNT, builder.lineItemType)
    }

    @Test
    fun `should build incident discount description`() {
        val incident =
            mockk<Incident> {
                every { id } returns 1L
                every { type } returns IncidentType.LAPTOP
            }

        val incidentDiscount =
            mockk<IncidentFeeDiscount> {
                every { appliedIncidentIds } returns listOf(1L)
            }

        val incidentWrapper =
            mockk<IncidentWrapper> {
                every { transactionId } returns "transactionId"
                every { incidentFeeDiscount } returns incidentDiscount
            }
        val context =
            mockk<PayableItemDescriptionBuilderContext> mock@{
                every { <EMAIL> } returns incidentWrapper
            }

        every { vasIncidentService.getIncidents(any()) } returns listOf(incident)
        val description = builder.build(context)

        assertEquals("Discount on Management Fee for: LAPTOP", description)
    }

    @Test
    fun `when incident wrapper is null then should throw error`() {
        val context =
            mockk<PayableItemDescriptionBuilderContext> {
                every { incidentWrapper } returns null
            }

        assertThrows<IllegalStateException> { builder.build(context) }
    }

    @Test
    fun `when incident discount on wrapper is null then should throw error`() {
        val incidentWrapper =
            mockk<IncidentWrapper> {
                every { transactionId } returns "transactionId"
                every { incidentFeeDiscount } returns null
            }

        val context =
            mockk<PayableItemDescriptionBuilderContext> mock@{
                every { <EMAIL> } returns incidentWrapper
            }

        assertThrows<IllegalStateException> { builder.build(context) }
    }
}
