package com.multiplier.payable.engine.testutils

import com.multiplier.core.payable.repository.model.JpaTransactionCommandLog
import com.multiplier.payable.engine.TransactionStatus
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.DefaultFinancialTransaction
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.orchestrator.TransactionAction
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.testutils.CompanyPayableTestDataFactory.createPayableItem
import com.multiplier.payable.engine.testutils.CompanyPayableTestDataFactory.createTransactionPayableItem
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.DateRangeOutput
import com.multiplier.payable.types.FinancialTransactionType
import com.multiplier.payable.types.TracingContextEntry
import com.multiplier.payable.types.TransactionCommand
import com.multiplier.payable.types.TransactionPayableItem
import java.time.LocalDateTime

object InvoiceEngineTestDataFactory {

    fun createInvoiceCommand(
        transactionId: String = "TX123456789",
        transactionType: TransactionType = TransactionType.GP_FUNDING_INVOICE,
        companyId: Long = 123L,
        dateRange: DateRange = DateRange(
            startDate = LocalDateTime.of(2024, 1, 1, 0, 0),
            endDate = LocalDateTime.of(2024, 1, 31, 0, 0)
        ),
        transactionDate: LocalDateTime = LocalDateTime.of(2024, 1, 1, 0, 0),
        cycle: InvoiceCycle = InvoiceCycle.MONTHLY,
        tracingCtx: MutableMap<String, String> = mutableMapOf(
            "error" to "error message",
            "status" to "status1"
        ),
        status: TransactionStatus = TransactionStatus.INIT,
        action: TransactionAction = TransactionAction.COMMIT,
        transactionCount: Int? = null,
        forcedContractIds: Set<Long> = emptySet(),
    ) = InvoiceCommand(
        transactionId = transactionId,
        transactionType = transactionType,
        companyId = companyId,
        dateRange = dateRange,
        transactionDate = transactionDate,
        cycle = cycle,
        status = status,
        action = action,
        transactionCount = transactionCount,
        forceContractIds = forcedContractIds,
    ).apply {
        tracingContext.putAll(tracingCtx)
    }

    fun createTransactionCommand(
        transactionId: String = "TX123456789",
        transactionType: FinancialTransactionType = FinancialTransactionType.GLOBAL_PAYROLL_FUNDING_INVOICE,
        companyId: Long = 123L,
        dateRange: DateRangeOutput = DateRangeOutput.newBuilder()
            .startDate(LocalDateTime.of(2024, 1, 1, 0, 0))
            .endDate(LocalDateTime.of(2024, 1, 31, 0, 0))
            .build(),
        transactionDate: LocalDateTime = LocalDateTime.of(2024, 1, 1, 0, 0),
        cycle: String = "MONTHLY",
        tracingContext: List<TracingContextEntry> = listOf(
            TracingContextEntry.newBuilder()
                .key("error")
                .value("error message")
                .build(),
            TracingContextEntry.newBuilder()
                .key("status")
                .value("status1")
                .build()
        ),
        status: String = "INIT",
        action: String = "COMMIT",
    ): TransactionCommand {
        val command = TransactionCommand()
        command.transactionId = transactionId
        command.transactionType = transactionType
        command.companyId = companyId
        command.dateRange = dateRange
        command.transactionDate = transactionDate
        command.cycle = cycle
        command.tracingContext = tracingContext
        command.transactionStatus = status
        command.transactionAction = action
        return command
    }

    fun createTransactionCommandLog(
        id: Long? = null,
        transactionId: String = "TX123456789",
        transactionType: String = "GP_FUNDING_INVOICE",
        transactionStatus: String = "INIT",
        command: String = "{}",
        note: String? = null,
        createdOn: LocalDateTime = LocalDateTime.now(),
        updatedOn: LocalDateTime = LocalDateTime.now(),
    ) = JpaTransactionCommandLog().apply {
        this.id = id
        this.transactionId = transactionId
        this.transactionType = transactionType
        this.transactionStatus = transactionStatus
        this.command = command
        this.note = note
        this.createdOn = createdOn
        this.updatedOn = updatedOn
    }

    fun createInternalFinancialTransaction(
        transactionId: String = "TX123456789",
        companyId: Long = 123L,
        dueDate: LocalDateTime = LocalDateTime.of(2024, 1, 31, 0, 0),
        date: LocalDateTime = LocalDateTime.of(2024, 1, 1, 0, 0),
        reference: String = "REF-123",
        year: Int = 2024,
        month: Int = 1,
        currency: CurrencyCode = CurrencyCode.USD,
        companyPayableId: Long = 1000L,
        items: List<PayableItem> = listOf(createPayableItem()),
        invoiceId: String = "e453d445-0eda-44fb-9f99-fd5b165530f2",
    ) = DefaultFinancialTransaction().apply {
        this.transactionId = transactionId
        this.companyId = companyId
        this.dueDate = dueDate
        this.date = date
        this.reference = reference
        this.year = year
        this.month = month
        this.currency = currency
        this.companyPayableId = companyPayableId
        this.items = items
        this.invoiceId = invoiceId
    }

    fun createGraphFinancialTransaction(
        transactionId: String = "TX123456789",
        companyId: Long = 123L,
        dueDate: LocalDateTime = LocalDateTime.of(2024, 1, 31, 0, 0),
        date: LocalDateTime = LocalDateTime.of(2024, 1, 1, 0, 0),
        reference: String = "REF-123",
        year: Int = 2024,
        month: Int = 1,
        currency: com.multiplier.payable.types.CurrencyCode = com.multiplier.payable.types.CurrencyCode.USD,
        items: List<TransactionPayableItem> = listOf(createTransactionPayableItem()),
    ) = com.multiplier.payable.types.DefaultFinancialTransaction().apply {
        this.transactionId = transactionId
        this.company = createGraphCompany(companyId)
        this.dueDate = dueDate
        this.date = date
        this.reference = reference
        this.year = year
        this.month = month
        this.currency = currency
        this.items = items
    }

    fun createGraphCompany(
        id: Long = 123L,
    ) = com.multiplier.payable.types.Company.newBuilder()
        .id(id)
        .build()
}
