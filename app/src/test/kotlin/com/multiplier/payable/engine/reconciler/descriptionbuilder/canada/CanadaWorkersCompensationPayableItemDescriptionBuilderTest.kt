package com.multiplier.payable.engine.reconciler.descriptionbuilder.canada

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.AmountFormatter
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.mock
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class CanadaWorkersCompensationPayableItemDescriptionBuilderTest {

    @Mock
    private lateinit var amountFormatter: AmountFormatter

    @InjectMocks
    private lateinit var canadaWorkersCompensationPayableItemDescriptionBuilder: CanadaWorkersCompensationPayableItemDescriptionBuilder

    @Test
    fun `getLineItemType should return CANADA_WORKERS_COMPENSATION`() {
        // WHEN
        val result = canadaWorkersCompensationPayableItemDescriptionBuilder.lineItemType

        // THEN
        assertThat(result).isEqualTo(LineItemType.CANADA_WORKERS_COMPENSATION)
    }

    @Test
    fun `getPrefix should return 'Canada Workers Compensation'`() {
        // WHEN
        val result = canadaWorkersCompensationPayableItemDescriptionBuilder.getPrefix()

        // THEN
        assertThat(result).isEqualTo("Worker's Compensation")
    }

    @Test
    fun `build should format description correctly`() {
        // GIVEN
        val context = PayableItemDescriptionBuilderContext(
            contractId = 123L,
            currencyCode = "CAD",
            amountInBaseCurrency = 1000.0,
            payrollCycleId = 456L,
            monthYear = mock()
        )

        // WHEN
        val result = canadaWorkersCompensationPayableItemDescriptionBuilder.build(context)

        // THEN
        val expected = "Worker's Compensation"
        assertThat(result).isEqualTo(expected)
    }
}
