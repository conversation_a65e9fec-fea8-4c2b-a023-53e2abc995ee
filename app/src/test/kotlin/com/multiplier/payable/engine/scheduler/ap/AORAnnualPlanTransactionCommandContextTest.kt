package com.multiplier.payable.engine.scheduler.ap

import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.scheduler.TransactionCommandContext
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AORAnnualPlanTransactionCommandContextTest {

    @Test
    fun `should create context with correct properties`() {
        // Given
        val companyId = 123L
        val transactionId = "txn-456"
        val dateRange = DateRange(
            startDate = LocalDate.of(2024, 1, 1).atStartOfDay(),
            endDate = LocalDate.of(2024, 1, 31).atTime(LocalTime.MAX)
        )
        val planId = 789L

        // When
        val context = AORAnnualPlanTransactionCommandContext(
            companyId = companyId,
            transactionId = transactionId,
            dateRange = dateRange,
            planId = planId
        )

        // Then
        assertEquals(companyId, context.companyId)
        assertEquals(transactionId, context.transactionId)
        assertEquals(dateRange, context.dateRange)
        assertEquals(planId, context.planId)
    }

    @Test
    fun `should implement TransactionCommandContext interface`() {
        // Given
        val context = createTestContext()

        // Then
        assertTrue(context is TransactionCommandContext)
    }

    @Test
    fun `should have correct traces map with planId`() {
        // Given
        val planId = 999L
        val context = createTestContext(planId = planId)

        // When
        val traces = context.traces

        // Then
        assertEquals(1, traces.size)
        assertTrue(traces.containsKey(AORAnnualPlanTransactionCommandContext.PLAN_ID_TRACE_KEY))
        assertEquals(planId.toString(), traces[AORAnnualPlanTransactionCommandContext.PLAN_ID_TRACE_KEY])
    }

    @Test
    fun `should have correct PLAN_ID_TRACE_KEY constant`() {
        // Then
        assertEquals("planId", AORAnnualPlanTransactionCommandContext.PLAN_ID_TRACE_KEY)
    }

    @Test
    fun `should convert planId to string in traces`() {
        // Given
        val planId = 12345L
        val context = createTestContext(planId = planId)

        // When
        val traceValue = context.traces[AORAnnualPlanTransactionCommandContext.PLAN_ID_TRACE_KEY]

        // Then
        assertEquals("12345", traceValue)
    }

    @Test
    fun `should handle zero planId correctly`() {
        // Given
        val planId = 0L
        val context = createTestContext(planId = planId)

        // When
        val traceValue = context.traces[AORAnnualPlanTransactionCommandContext.PLAN_ID_TRACE_KEY]

        // Then
        assertEquals("0", traceValue)
        assertEquals(0L, context.planId)
    }

    @Test
    fun `should handle negative planId correctly`() {
        // Given
        val planId = -123L
        val context = createTestContext(planId = planId)

        // When
        val traceValue = context.traces[AORAnnualPlanTransactionCommandContext.PLAN_ID_TRACE_KEY]

        // Then
        assertEquals("-123", traceValue)
        assertEquals(-123L, context.planId)
    }

    @Test
    fun `should handle large planId correctly`() {
        // Given
        val planId = Long.MAX_VALUE
        val context = createTestContext(planId = planId)

        // When
        val traceValue = context.traces[AORAnnualPlanTransactionCommandContext.PLAN_ID_TRACE_KEY]

        // Then
        assertEquals(Long.MAX_VALUE.toString(), traceValue)
        assertEquals(Long.MAX_VALUE, context.planId)
    }

    @Test
    fun `should be data class with correct equals and hashCode`() {
        // Given
        val context1 = createTestContext(
            companyId = 123L,
            transactionId = "txn-1",
            planId = 456L
        )
        val context2 = createTestContext(
            companyId = 123L,
            transactionId = "txn-1",
            planId = 456L
        )
        val context3 = createTestContext(
            companyId = 123L,
            transactionId = "txn-1",
            planId = 789L // Different planId
        )

        // Then
        assertEquals(context1, context2)
        assertEquals(context1.hashCode(), context2.hashCode())
        assertTrue(context1 != context3)
        assertTrue(context1.hashCode() != context3.hashCode())
    }

    @Test
    fun `should have correct toString representation`() {
        // Given
        val context = createTestContext(
            companyId = 123L,
            transactionId = "txn-456",
            planId = 789L
        )

        // When
        val stringRepresentation = context.toString()

        // Then
        assertTrue(stringRepresentation.contains("companyId=123"))
        assertTrue(stringRepresentation.contains("transactionId=txn-456"))
        assertTrue(stringRepresentation.contains("planId=789"))
        assertTrue(stringRepresentation.contains("AORAnnualPlanTransactionCommandContext"))
    }

    @Test
    fun `should support copy with different values`() {
        // Given
        val originalContext = createTestContext(
            companyId = 123L,
            transactionId = "txn-1",
            planId = 456L
        )

        // When
        val copiedContext = originalContext.copy(
            companyId = 999L,
            planId = 888L
        )

        // Then
        assertEquals(999L, copiedContext.companyId)
        assertEquals("txn-1", copiedContext.transactionId) // Should remain the same
        assertEquals(888L, copiedContext.planId)
        assertEquals(originalContext.dateRange, copiedContext.dateRange) // Should remain the same
        
        // Traces should be updated with new planId
        assertEquals("888", copiedContext.traces[AORAnnualPlanTransactionCommandContext.PLAN_ID_TRACE_KEY])
    }

    @Test
    fun `should handle empty transactionId correctly`() {
        // Given
        val context = createTestContext(transactionId = "")

        // Then
        assertEquals("", context.transactionId)
        assertTrue(context.traces.isNotEmpty()) // Should still have planId trace
    }

    @Test
    fun `should handle very long transactionId correctly`() {
        // Given
        val longTransactionId = "a".repeat(1000)
        val context = createTestContext(transactionId = longTransactionId)

        // Then
        assertEquals(longTransactionId, context.transactionId)
        assertEquals(1000, context.transactionId.length)
    }

    private fun createTestContext(
        companyId: Long = 123L,
        transactionId: String = "test-txn-id",
        planId: Long = 456L
    ): AORAnnualPlanTransactionCommandContext {
        val dateRange = DateRange(
            startDate = LocalDate.of(2024, 1, 1).atStartOfDay(),
            endDate = LocalDate.of(2024, 1, 31).atTime(LocalTime.MAX)
        )
        return AORAnnualPlanTransactionCommandContext(
            companyId = companyId,
            transactionId = transactionId,
            dateRange = dateRange,
            planId = planId
        )
    }
}
