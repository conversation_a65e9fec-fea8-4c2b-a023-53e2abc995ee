package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.fetcher.IADCompanyPayableFetcher
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaInvoice
import com.multiplier.core.payable.repository.model.JpaInvoiceLineItem
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.PayableStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class HighValueItemRuleTest {

    @Mock
    private lateinit var iadCompanyPayableFetcher: IADCompanyPayableFetcher

    @Mock
    private lateinit var featureFlagService: FeatureFlagService

    @Mock
    private lateinit var jpaAnomalyReportRepository: JpaAnomalyReportRepository

    @Mock
    private lateinit var mockCommand: InvoiceCommand

    private lateinit var highValueItemRule: HighValueItemRule

    @BeforeEach
    fun setUp() {
        highValueItemRule = HighValueItemRule(iadCompanyPayableFetcher, featureFlagService, jpaAnomalyReportRepository)
    }

    @Test
    fun `should return success when feature flag is disabled`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(false)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = InvoiceAnomalyDetectorRequest.builder().build()

        // When
        val result = highValueItemRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Rule 'HighValueItem' is disabled by feature flag"))
    }

    @Test
    fun `should return success when request is invalid`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = InvoiceAnomalyDetectorRequest.builder().build() // Empty request

        // When
        val result = highValueItemRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice data or company payable is null - validation skipped"))
    }

    @Test
    fun `should return success when no line items present`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = createMockRequest(emptyList(), 123L, 3, 2024)

        // When
        val result = highValueItemRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("No line items found in invoice - validation skipped"))
    }

    @Test
    fun `should return success when insufficient historical data`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val lineItems = listOf(
            createLineItem("Gross Salary", 1000.0, 1.0, LineItemType.GROSS_SALARY, "USD")
        )
        val request = createMockRequest(lineItems, 123L, 3, 2024)

        // Mock insufficient historical data (less than 3 months)
        mockHistoricalData(request.payable!!, emptyList()) // No historical data

        // When
        val result = highValueItemRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Insufficient historical data - validation skipped"))
    }

    @Test
    fun `should return success when line item amounts are within historical thresholds`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val lineItems = listOf(
            createLineItem("Gross Salary", 1000.0, 1.0, LineItemType.GROSS_SALARY, "USD")
        )
        val request = createMockRequest(lineItems, 123L, 3, 2024)

        // Mock historical data with higher amounts (current amount is within threshold)
        val historicalLineItems = listOf(
            createHistoricalLineItem("Historical Gross Salary", 1200.0, 1.0, LineItemType.GROSS_SALARY),
            createHistoricalLineItem("Historical Gross Salary", 1100.0, 1.0, LineItemType.GROSS_SALARY),
            createHistoricalLineItem("Historical Gross Salary", 1300.0, 1.0, LineItemType.GROSS_SALARY)
        )
        mockHistoricalData(request.payable!!, historicalLineItems)

        // When
        val result = highValueItemRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("All line item amounts are within acceptable per-unit thresholds"))
    }

    @Test
    fun `should return warning when line item per-unit amount exceeds historical threshold`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val lineItems = listOf(
            createLineItem("Gross Salary", 2000.0, 1.0, LineItemType.GROSS_SALARY, "USD") // High per-unit amount
        )
        val request = createMockRequest(lineItems, 123L, 3, 2024)

        // Mock historical data with lower per-unit amounts (current per-unit amount exceeds threshold)
        val historicalLineItems = listOf(
            createHistoricalLineItem("Historical Gross Salary", 800.0, 1.0, LineItemType.GROSS_SALARY),
            createHistoricalLineItem("Historical Gross Salary", 900.0, 1.0, LineItemType.GROSS_SALARY),
            createHistoricalLineItem("Historical Gross Salary", 1000.0, 1.0, LineItemType.GROSS_SALARY) // Max per-unit: 1000, threshold: 1500
        )
        mockHistoricalData(request.payable!!, historicalLineItems)

        // When
        val result = highValueItemRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success) // Should be success (WARNING, not FAIL)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Gross Salary"))
        assertTrue(result.messages[0].contains("exceeds historical per-unit threshold"))
        assertTrue(result.messages[0].contains("2000.00"))
        assertTrue(result.messages[0].contains("1500.00"))
    }

    @Test
    fun `should handle multiple line items with different thresholds`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val lineItems = listOf(
            createLineItem("Gross Salary", 1000.0, 1.0, LineItemType.GROSS_SALARY, "USD"), // Within threshold
            createLineItem("Management Fee", 500.0, 1.0, LineItemType.MANAGEMENT_FEE_EOR, "USD") // Exceeds threshold
        )
        val request = createMockRequest(lineItems, 123L, 3, 2024)

        // Mock historical data
        val historicalLineItems = listOf(
            // Gross salary historical data (max: 1200, threshold: 1800)
            createHistoricalLineItem("Historical Gross Salary", 1200.0, 1.0, LineItemType.GROSS_SALARY),
            createHistoricalLineItem("Historical Gross Salary", 1100.0, 1.0, LineItemType.GROSS_SALARY),
            // Management fee historical data (max: 200, threshold: 300)
            createHistoricalLineItem("Historical Management Fee", 200.0, 1.0, LineItemType.MANAGEMENT_FEE_EOR),
            createHistoricalLineItem("Historical Management Fee", 150.0, 1.0, LineItemType.MANAGEMENT_FEE_EOR)
        )
        mockHistoricalData(request.payable!!, historicalLineItems)

        // When
        val result = highValueItemRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success) // Should be success (WARNING, not FAIL)
        assertEquals(1, result.messages.size) // Only management fee should exceed threshold
        assertTrue(result.messages[0].contains("Management Fee"))
        assertTrue(result.messages[0].contains("exceeds historical per-unit threshold"))
    }

    @Test
    fun `should handle line items with quantity greater than 1 using per-unit comparison`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val lineItems = listOf(
            createLineItem("Gross Salary", 500.0, 3.0, LineItemType.GROSS_SALARY, "USD") // Per-unit: 500, Total: 1500
        )
        val request = createMockRequest(lineItems, 123L, 3, 2024)

        // Mock historical data (max per-unit: 1000, threshold: 1500)
        val historicalLineItems = listOf(
            createHistoricalLineItem("Historical Gross Salary", 1000.0, 1.0, LineItemType.GROSS_SALARY),
            createHistoricalLineItem("Historical Gross Salary", 800.0, 1.0, LineItemType.GROSS_SALARY)
        )
        mockHistoricalData(request.payable!!, historicalLineItems)

        // When
        val result = highValueItemRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success) // Should be success (per-unit amount 500 < threshold 1500)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("All line item amounts are within acceptable per-unit thresholds"))
    }

    @Test
    fun `should avoid false positives with per-unit comparison for high quantity orders`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val lineItems = listOf(
            // Large quantity but reasonable per-unit cost - should NOT trigger anomaly
            createLineItem("Gross Salary", 800.0, 10.0, LineItemType.GROSS_SALARY, "USD") // Per-unit: 800, Total: 8000
        )
        val request = createMockRequest(lineItems, 123L, 3, 2024)

        // Mock historical data with similar per-unit amounts
        val historicalLineItems = listOf(
            createHistoricalLineItem("Historical Gross Salary", 900.0, 1.0, LineItemType.GROSS_SALARY),
            createHistoricalLineItem("Historical Gross Salary", 850.0, 2.0, LineItemType.GROSS_SALARY),
            createHistoricalLineItem("Historical Gross Salary", 1000.0, 1.0, LineItemType.GROSS_SALARY) // Max per-unit: 1000, threshold: 1500
        )
        mockHistoricalData(request.payable!!, historicalLineItems)

        // When
        val result = highValueItemRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success) // Should be success - per-unit amount 800 < threshold 1500
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("All line item amounts are within acceptable per-unit thresholds")) // No anomaly detected despite high total amount
    }

    @Test
    fun `should detect anomaly when per-unit amount genuinely exceeds threshold regardless of quantity`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val lineItems = listOf(
            // High per-unit cost should trigger anomaly regardless of quantity
            createLineItem("Gross Salary", 2000.0, 1.0, LineItemType.GROSS_SALARY, "USD") // Per-unit: 2000, Total: 2000
        )
        val request = createMockRequest(lineItems, 123L, 3, 2024)

        // Mock historical data with lower per-unit amounts
        val historicalLineItems = listOf(
            createHistoricalLineItem("Historical Gross Salary", 800.0, 5.0, LineItemType.GROSS_SALARY),
            createHistoricalLineItem("Historical Gross Salary", 900.0, 3.0, LineItemType.GROSS_SALARY),
            createHistoricalLineItem("Historical Gross Salary", 1000.0, 2.0, LineItemType.GROSS_SALARY) // Max per-unit: 1000, threshold: 1500
        )
        mockHistoricalData(request.payable!!, historicalLineItems)

        // When
        val result = highValueItemRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success) // Should be success (WARNING, not FAIL)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("per-unit amount"))
        assertTrue(result.messages[0].contains("exceeds historical per-unit threshold"))
        assertTrue(result.messages[0].contains("2000.00"))
        assertTrue(result.messages[0].contains("1500.00"))
    }

    @Test
    fun `should only include authorized paid and partially paid historical invoices`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val lineItems = listOf(
            createLineItem("Gross Salary", 2000.0, 1.0, LineItemType.GROSS_SALARY, "USD")
        )
        val request = createMockRequest(lineItems, 123L, 3, 2024)

        // Create historical payables with different statuses
        val authorizedPayable = Mockito.mock(JpaCompanyPayable::class.java)
        val paidPayable = Mockito.mock(JpaCompanyPayable::class.java)
        val draftPayable = Mockito.mock(JpaCompanyPayable::class.java)
        val voidedPayable = Mockito.mock(JpaCompanyPayable::class.java)

        val mockInvoice = Mockito.mock(JpaInvoice::class.java)
        val historicalLineItems = listOf(
            createHistoricalLineItem("Historical Gross Salary", 1000.0, 1.0, LineItemType.GROSS_SALARY)
        )

        // Set up statuses
        Mockito.`when`(authorizedPayable.status).thenReturn(PayableStatus.AUTHORIZED)
        Mockito.`when`(paidPayable.status).thenReturn(PayableStatus.PAID)
        Mockito.`when`(draftPayable.status).thenReturn(PayableStatus.DRAFT)
        Mockito.`when`(voidedPayable.status).thenReturn(PayableStatus.VOIDED)

        // Set up invoices (only AUTHORIZED and PAID should be included)
        Mockito.`when`(authorizedPayable.invoice).thenReturn(mockInvoice)
        Mockito.`when`(paidPayable.invoice).thenReturn(mockInvoice)
        Mockito.`when`(draftPayable.invoice).thenReturn(mockInvoice)
        Mockito.`when`(voidedPayable.invoice).thenReturn(mockInvoice)
        Mockito.`when`(mockInvoice.lineItems).thenReturn(historicalLineItems)

        // Mock fetcher to return all statuses, but rule should filter to only valid ones
        Mockito.`when`(iadCompanyPayableFetcher.getInvoicesForReqTime(Mockito.any(), Mockito.eq(false)))
            .thenReturn(listOf(authorizedPayable, paidPayable, draftPayable, voidedPayable))

        // When
        val result = highValueItemRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success) // Should be success (per-unit amount 2000 > threshold 1500 from 2 valid payables)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("exceeds historical per-unit threshold"))
        // Threshold should be calculated from only AUTHORIZED and PAID payables (2 line items)
        // Max per-unit: 1000, threshold: 1500, current: 2000 -> should trigger anomaly
    }

    @Test
    fun `should return correct rule type`() {
        // When
        val ruleType = highValueItemRule.type

        // Then
        assertEquals(DetectionRuleType.HIGH_VALUE_ITEM, ruleType)
    }

    @Test
    fun `should return correct rule name`() {
        // When
        val ruleName = highValueItemRule.ruleName

        // Then
        assertEquals("HighValueItem", ruleName)
    }

    @Test
    fun `should return correct feature flag name`() {
        // When
        val featureFlagName = highValueItemRule.featureFlagName

        // Then
        assertEquals("ENABLE_HIGH_VALUE_ITEM_CHECK", featureFlagName)
    }

    private fun createMockRequest(
        lineItems: List<LineItemDTO>,
        companyId: Long,
        month: Int,
        year: Int
    ): InvoiceAnomalyDetectorRequest {
        val mockInvoiceDTO = Mockito.mock(InvoiceDTO::class.java)
        Mockito.`when`(mockInvoiceDTO.lineItems).thenReturn(lineItems)

        val mockCompanyPayable = Mockito.mock(JpaCompanyPayable::class.java)
        Mockito.`when`(mockCompanyPayable.companyId).thenReturn(companyId)
        Mockito.`when`(mockCompanyPayable.month).thenReturn(month)
        Mockito.`when`(mockCompanyPayable.year).thenReturn(year)

        return InvoiceAnomalyDetectorRequest
            .builder()
            .invoiceDTO(mockInvoiceDTO)
            .payable(mockCompanyPayable)
            .build()
    }

    private fun createLineItem(
        description: String,
        unitAmount: Double,
        quantity: Double,
        itemType: LineItemType,
        currency: String
    ): LineItemDTO {
        return LineItemDTO.builder()
            .description(description)
            .unitAmount(unitAmount)
            .quantity(quantity)
            .itemType(itemType)
            .baseCurrency(currency)
            .build()
    }

    private fun createHistoricalLineItem(
        description: String,
        unitPrice: Double,
        quantity: Double,
        itemType: LineItemType
    ): JpaInvoiceLineItem {
        return JpaInvoiceLineItem.builder()
            .description(description)
            .unitPrice(unitPrice)
            .quantity(quantity)
            .itemType(itemType)
            .build()
    }

    private fun mockHistoricalData(
        companyPayable: JpaCompanyPayable,
        historicalLineItems: List<JpaInvoiceLineItem>
    ) {
        // Create mock historical payables with invoices containing line items
        val mockHistoricalPayables = mutableListOf<JpaCompanyPayable>()

        // Group line items by month (simulate 3+ months of data)
        val itemsPerMonth = if (historicalLineItems.isNotEmpty()) {
            historicalLineItems.chunked((historicalLineItems.size / 3).coerceAtLeast(1))
        } else {
            emptyList()
        }

        itemsPerMonth.forEachIndexed { index, monthLineItems ->
            val mockPayable = Mockito.mock(JpaCompanyPayable::class.java)
            val mockInvoice = Mockito.mock(JpaInvoice::class.java)

            Mockito.`when`(mockPayable.status).thenReturn(PayableStatus.AUTHORIZED)
            Mockito.`when`(mockPayable.invoice).thenReturn(mockInvoice)
            Mockito.`when`(mockInvoice.lineItems).thenReturn(monthLineItems)

            mockHistoricalPayables.add(mockPayable)
        }

        // Mock the fetcher to return historical data for any month
        Mockito.`when`(iadCompanyPayableFetcher.getInvoicesForReqTime(Mockito.any(), Mockito.eq(false)))
            .thenReturn(mockHistoricalPayables)
    }
}
