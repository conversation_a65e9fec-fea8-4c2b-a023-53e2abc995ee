package com.multiplier.payable.engine.reconciler.data.item.deposit

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.JpaPayableItemStore
import com.multiplier.payable.engine.payableitem.JpaPayableItemStoreRepository
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.payableitem.PayableItemMapper
import com.multiplier.payable.engine.reconciler.data.item.DepositInvoiceItemStoreDataProvider
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.LocalDateTime
import java.util.*

@ExtendWith(MockitoExtension::class)
class DepositInvoiceItemStoreDataProviderTest {

    @Mock
    private lateinit var jpaPayableItemStoreRepository: JpaPayableItemStoreRepository

    @Mock
    private lateinit var payableItemMapper: PayableItemMapper

    @InjectMocks
    private lateinit var depositInvoiceItemStoreDataProvider: DepositInvoiceItemStoreDataProvider

    @Test
    fun givenEmptyLineItemTypes_whenFetch_thenThrowException() {
        // GIVEN
        val command = mock<InvoiceCommand>()
        val lineItemTypes = emptyList<LineItemType>()

        // WHEN and THEN
        assertThrows<IllegalArgumentException> {
            depositInvoiceItemStoreDataProvider.fetchLatest(command, lineItemTypes)
        }
    }

    @Test
    fun givenEmptyForcedContractIds_whenFetch_thenThrowException() {
        // GIVEN
        val command = mock<InvoiceCommand>()

        whenever(command.forceContractIds)
            .thenReturn(emptySet())

        val lineItemTypes = mock<List<LineItemType>>()

        // WHEN and THEN
        assertThrows<IllegalArgumentException> {
            depositInvoiceItemStoreDataProvider.fetchLatest(command, lineItemTypes)
        }
    }

    @Test
    fun givenParams_whenFetch_thenReturnItems() {
        // GIVEN
        val command = InvoiceCommand(
            transactionId = UUID.randomUUID().toString(),
            transactionType = TransactionType.GP_FUNDING_INVOICE,
            companyId = 42L,
            dateRange = DateRange(
                startDate = mock(),
                endDate = mock()
            ),
            transactionDate = LocalDateTime.now(),
            cycle = InvoiceCycle.MONTHLY,
            forceContractIds = setOf(42L)
        )

        val contractId = command.forceContractIds.first()
        val lineItemTypes = mock<MutableCollection<LineItemType>>()
        val jpaPayableItemStores = mock<List<JpaPayableItemStore>>()
        whenever(
            jpaPayableItemStoreRepository.findByContractIdAndPayableItemTypesWithinDateRange(
                contractId,
                lineItemTypes,
                command.dateRange.startDate.toLocalDate(),
                command.dateRange.endDate.toLocalDate(),
            )
        )
            .thenReturn(jpaPayableItemStores)

        val mappedPayableItems = listOf(
            PayableItem(
                month = 42,
                year = 42,
                lineItemType = "awesomeItemType",
                amountInBaseCurrency = 42.0,
                baseCurrency = "awesomeBaseCurrency",
                cycle = InvoiceCycle.MONTHLY,
                originalTimestamp = 42L,
                countryCode = "awesomeCountryCode",
            )
        )
        whenever(payableItemMapper.mapList(jpaPayableItemStores, command.cycle))
            .thenReturn(mappedPayableItems)

        // WHEN
        val latestPayableItems = depositInvoiceItemStoreDataProvider.fetchLatest(command, lineItemTypes)

        // THEN
        assertThat(latestPayableItems).isEqualTo(mappedPayableItems)
    }
}