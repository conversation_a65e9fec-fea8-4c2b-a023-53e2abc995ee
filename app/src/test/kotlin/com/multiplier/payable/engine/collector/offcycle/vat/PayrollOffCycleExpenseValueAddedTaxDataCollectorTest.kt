package com.multiplier.payable.engine.collector.offcycle.vat

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.payroll.ContractPayroll
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.verify
import java.time.LocalDate
import org.junit.jupiter.api.Assertions.assertEquals

@ExtendWith(MockitoExtension::class)
class PayrollOffCycleExpenseValueAddedTaxDataCollectorTest {

    @Mock
    private lateinit var offCycleVatDataCollectionService: OffCycleVatDataCollectionService

    @InjectMocks
    private lateinit var payrollOffCycleExpenseValueAddedTaxDataCollector: PayrollOffCycleExpenseValueAddedTaxDataCollector

    private lateinit var invoiceCommand: InvoiceCommand

    @BeforeEach
    fun setUp() {
        invoiceCommand = InvoiceCommand(
            transactionId = "test-transaction-123",
            companyId = 1L,
            transactionType = com.multiplier.payable.engine.domain.entities.TransactionType.PAYROLL_OFFCYCLE_INVOICE,
            dateRange = com.multiplier.payable.engine.domain.aggregates.DateRange(
                startDate = java.time.LocalDateTime.of(2024, 6, 1, 0, 0),
                endDate = java.time.LocalDateTime.of(2024, 6, 30, 23, 59)
            ),
            transactionDate = java.time.LocalDateTime.of(2024, 6, 15, 12, 0),
            cycle = com.multiplier.payable.engine.domain.aggregates.InvoiceCycle.MONTHLY
        )
    }

    @Nested
    inner class GetSupportedType {

        @Test
        fun `should return VAT_PAYROLL_OFFCYCLE_EXPENSE line item type`() {
            // WHEN
            val result = payrollOffCycleExpenseValueAddedTaxDataCollector.getSupportedType()

            // THEN
            assertEquals(LineItemType.VAT_PAYROLL_OFFCYCLE_EXPENSE, result)
        }
    }

    @Nested
    inner class GetPayrollCostAmount {

        @Test
        fun `should return total cost minus expense amount when expense amount is not null`() {
            // GIVEN
            val contractPayroll = createContractPayroll(
                amountTotalCost = 1000.0,
                totalExpenseAmount = 200.0
            )

            // WHEN
            val result = payrollOffCycleExpenseValueAddedTaxDataCollector.getPayrollCostAmount(contractPayroll)

            // THEN
            assertEquals(200.0, result)
        }

        @Test
        fun `should return total cost when expense amount is null`() {
            // GIVEN
            val contractPayroll = createContractPayroll(
                amountTotalCost = 1000.0,
                totalExpenseAmount = null
            )

            // WHEN
            val result = payrollOffCycleExpenseValueAddedTaxDataCollector.getPayrollCostAmount(contractPayroll)

            // THEN
            assertEquals(0.0, result)
        }

        @Test
        fun `should return total cost when expense amount is zero`() {
            // GIVEN
            val contractPayroll = createContractPayroll(
                amountTotalCost = 1000.0,
                totalExpenseAmount = 0.0
            )

            // WHEN
            val result = payrollOffCycleExpenseValueAddedTaxDataCollector.getPayrollCostAmount(contractPayroll)

            // THEN
            assertEquals(0.0, result)
        }

        @Test
        fun `should handle negative expense amount`() {
            // GIVEN
            val contractPayroll = createContractPayroll(
                amountTotalCost = 1000.0,
                totalExpenseAmount = -100.0
            )

            // WHEN
            val result = payrollOffCycleExpenseValueAddedTaxDataCollector.getPayrollCostAmount(contractPayroll)

            // THEN
            assertEquals(-100.0, result)
        }

        @Test
        fun `should handle expense amount greater than total cost`() {
            // GIVEN
            val contractPayroll = createContractPayroll(
                amountTotalCost = 500.0,
                totalExpenseAmount = 800.0
            )

            // WHEN
            val result = payrollOffCycleExpenseValueAddedTaxDataCollector.getPayrollCostAmount(contractPayroll)

            // THEN
            assertEquals(800.0, result)
        }
    }

    @Nested
    inner class Handle {

        @Test
        fun `should delegate to off cycle VAT data collection service`() {
            // WHEN
            payrollOffCycleExpenseValueAddedTaxDataCollector.handle(invoiceCommand)

            // THEN
            verify(offCycleVatDataCollectionService).handleOffCycleVatCollection(
                invoiceCommand,
                payrollOffCycleExpenseValueAddedTaxDataCollector
            )
        }
    }

    @Nested
    inner class RollbackMethod {

        @Test
        fun `should have rollback method from DataCollector interface`() {
            // WHEN & THEN
            // This test ensures the rollback method exists and can be called without error
            payrollOffCycleExpenseValueAddedTaxDataCollector.rollback(invoiceCommand)
            // No verification needed as default implementation does nothing
        }
    }

    private fun createContractPayroll(
        contractId: Long = 123L,
        companyId: Long = 1L,
        amountTotalCost: Double = 1000.0,
        totalExpenseAmount: Double? = 100.0
    ): ContractPayroll {
        return ContractPayroll(
            contractId = contractId,
            companyId = companyId,
            type = com.multiplier.payable.engine.contract.ContractType.EMPLOYEE,
            isOffCycle = true,
            hasBillingException = false,
            countryCode = "USA",
            startDate = LocalDate.of(2024, 6, 1),
            endDate = LocalDate.of(2024, 6, 30),
            payrollCycleId = 456L,
            amountTotalCost = amountTotalCost,
            totalExpenseAmount = totalExpenseAmount,
            currencyCode = com.multiplier.payable.types.CurrencyCode.USD,
            payCycleCount = 1,
            fetchedTime = System.currentTimeMillis(),
            totalSeveranceAccruals = 0.0
        )
    }
}
