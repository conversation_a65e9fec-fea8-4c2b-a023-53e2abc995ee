package com.multiplier.payable.engine.splitter.selector

import com.multiplier.payable.engine.payableitem.ContractDepartment
import com.multiplier.payable.engine.splitter.context.DepartmentEnricher
import com.multiplier.payable.engine.splitter.context.SplitterContext
import com.multiplier.payable.engine.testutils.CompanyPayableTestDataFactory
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class DepartmentSelectorTest {
    @Mock
    private lateinit var deparmentEnricher: DepartmentEnricher

    @InjectMocks
    private lateinit var departmentSelector: DepartmentSelector

    @Test
    fun givenItemWithNullContractId_whenSelectAndTranslate_thenReturnNull() {
        // GIVEN
        val payableItem = CompanyPayableTestDataFactory.createPayableItem(
            contractId = null,
        )

        // WHEN
        val result = departmentSelector.selectAndTranslate()(payableItem, Mockito.mock(SplitterContext::class.java))

        // THEN
        assertEquals(null, result)
    }

    @Test
    fun givenValidItemAndContext_whenSelectAndTranslate_thenReturnCorrectDepartmentName() {
        // GIVEN
        val payableItem = CompanyPayableTestDataFactory.createPayableItem(
            contractId = 123L,
        )

        val splitterContext = SplitterContext(
            transactionId = "123L",
            payableItems = listOf(payableItem),
            contractDepartments = mapOf(123L to ContractDepartment(13L, "Engineering")),
        )

        // WHEN
        val result = departmentSelector.selectAndTranslate()(payableItem, splitterContext)

        // THEN
        assertEquals("Engineering", result)
    }

}
