package com.multiplier.payable.engine.collector.adjustment.gp

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.gp.serviceInvoice.model.PerPayslipFee
import com.multiplier.payable.engine.collector.gp.serviceInvoice.service.PayslipFeeService
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.time.LocalDateTime

class PerPayslipFeeAdjustmentCollectorTest {

    private lateinit var payslipFeeService: PayslipFeeService
    private lateinit var collector: PerPayslipFeeAdjustmentCollector

    @BeforeEach
    fun setUp() {
        payslipFeeService = mock()
        collector = PerPayslipFeeAdjustmentCollector(payslipFeeService)
    }

    @Test
    fun `getSupportedType should return correct LineItemType`() {
        // When
        val result = collector.getSupportedType()

        // Then
        assertEquals(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PER_PAYSLIP_FEE, result)
    }

    @Test
    fun `handle should process successfully when payslip fees exist`() {
        // Given
        val command = createInvoiceCommand()
        val payslipFees = listOf(
            createPerPayslipFee(amount = 100.0),
            createPerPayslipFee(amount = 50.0)
        )
        val adjustmentFees = payslipFees.map { it.copy(amount = it.amount * -1) }

        whenever(payslipFeeService.getPayslipFees(command)).thenReturn(payslipFees)

        // When
        collector.handle(command)

        // Then
        verify(payslipFeeService).getPayslipFees(command)
        verify(payslipFeeService).normalizeAndSave(
            transactionId = command.transactionId,
            payslipFees = adjustmentFees,
            command = command,
            lineItemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PER_PAYSLIP_FEE
        )
    }

    @Test
    fun `handle should validate negative amounts for adjustments`() {
        // Given
        val command = createInvoiceCommand()
        val payslipFees = listOf(
            createPerPayslipFee(amount = 100.0),
            createPerPayslipFee(amount = 75.5)
        )
        val adjustmentFees = payslipFees.map { it.copy(amount = it.amount * -1) }

        whenever(payslipFeeService.getPayslipFees(command)).thenReturn(payslipFees)

        // When
        collector.handle(command)

        // Then
        // Verify that all adjustment amounts are negative
        adjustmentFees.forEach { fee ->
            assertTrue(fee.amount < 1, "Payslip fee adjustment amount should be negative but found ${fee.amount}")
        }
        
        verify(payslipFeeService).normalizeAndSave(
            transactionId = command.transactionId,
            payslipFees = adjustmentFees,
            command = command,
            lineItemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PER_PAYSLIP_FEE
        )
    }

    @Test
    fun `handle should not call normalizeAndSave when no payslip fees exist`() {
        // Given
        val command = createInvoiceCommand()
        whenever(payslipFeeService.getPayslipFees(command)).thenReturn(emptyList())

        // When
        collector.handle(command)

        // Then
        verify(payslipFeeService).getPayslipFees(command)
        verify(payslipFeeService).normalizeAndSave(
            transactionId = command.transactionId,
            payslipFees = emptyList(),
            command = command,
            lineItemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PER_PAYSLIP_FEE
        )
    }

    @Test
    fun `handle should propagate exception when getPayslipFees throws exception`() {
        // Given
        val command = createInvoiceCommand()
        val exception = RuntimeException("Service error")
        whenever(payslipFeeService.getPayslipFees(command)).thenThrow(exception)

        // When & Then
        val thrownException = assertThrows(RuntimeException::class.java) {
            collector.handle(command)
        }
        assertEquals("Service error", thrownException.message)
        verify(payslipFeeService).getPayslipFees(command)
        verify(payslipFeeService, never()).normalizeAndSave(any(), any(), any(), any())
    }

    @Test
    fun `handle should propagate exception when normalizeAndSave throws exception`() {
        // Given
        val command = createInvoiceCommand()
        val payslipFees = listOf(createPerPayslipFee(amount = 100.0))
        val adjustmentFees = payslipFees.map { it.copy(amount = it.amount * -1) }
        val exception = RuntimeException("Save error")

        whenever(payslipFeeService.getPayslipFees(command)).thenReturn(payslipFees)
        whenever(payslipFeeService.normalizeAndSave(any(), any(), any(), any())).thenThrow(exception)

        // When & Then
        val thrownException = assertThrows(RuntimeException::class.java) {
            collector.handle(command)
        }
        assertEquals("Save error", thrownException.message)
        verify(payslipFeeService).getPayslipFees(command)
        verify(payslipFeeService).normalizeAndSave(
            transactionId = command.transactionId,
            payslipFees = adjustmentFees,
            command = command,
            lineItemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PER_PAYSLIP_FEE
        )
    }

    private fun createInvoiceCommand(): InvoiceCommand {
        return InvoiceCommand(
            transactionId = "test-transaction-123",
            transactionType = TransactionType.GP_SERVICE_INVOICE,
            companyId = 123L,
            dateRange = DateRange(
                startDate = LocalDateTime.of(2024, 1, 1, 0, 0),
                endDate = LocalDateTime.of(2024, 1, 31, 23, 59, 59)
            ),
            transactionDate = LocalDateTime.of(2024, 1, 15, 10, 0),
            cycle = InvoiceCycle.MONTHLY
        )
    }

    private fun createPerPayslipFee(amount: Double): PerPayslipFee {
        return PerPayslipFee(
            transactionId = "test-transaction-123",
            billId = "bill-123",
            companyId = 123L,
            amount = amount,
            payslipCount = 10,
            currencyCode = CurrencyCode.USD,
            calculatedTime = 123456789L,
            countryCode = CountryCode.USA,
            month = 1,
            year = 2024,
            entityId = 456L
        )
    }
}
