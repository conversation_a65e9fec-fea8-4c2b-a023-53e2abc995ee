package com.multiplier.payable.engine.reconciler

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.formatter.DataFormatter
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorage
import com.multiplier.payable.engine.reconciler.data.invoice.InvoiceDataProvider
import com.multiplier.payable.engine.reconciler.data.invoice.InvoiceDataProviderFactory
import com.multiplier.payable.engine.reconciler.data.item.ItemStoreDataProvider
import com.multiplier.payable.engine.reconciler.data.item.ItemStoreDataProviderFactory
import com.multiplier.payable.engine.reconciler.diff.reconciler.InvoiceDiffReconciler
import com.multiplier.payable.engine.reconciler.diff.reconciler.InvoiceDiffReconcilerFactory
import com.multiplier.payable.engine.testutils.InvoiceEngineTestDataFactory
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.kotlin.any
import org.mockito.kotlin.argThat
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class SecondInvoiceDataReconcilerTest {

    @Mock
    private lateinit var transactionTemplateProvider: TransactionTemplateProvider

    @Mock
    private lateinit var invoiceDataProviderFactory: InvoiceDataProviderFactory

    @Mock
    private lateinit var itemStoreDataProviderFactory: ItemStoreDataProviderFactory

    @Mock
    private lateinit var invoiceDiffReconcilerFactory: InvoiceDiffReconcilerFactory

    @Mock
    private lateinit var duplicatedSecondInvoiceValidator: DuplicatedSecondInvoiceValidator

    @Mock
    @Qualifier("second-invoice")
    private lateinit var companyPayableStorage: CompanyPayableStorage

    @InjectMocks
    private lateinit var secondInvoiceDataReconciler: SecondInvoiceDataReconciler

    @Test
    fun `handle should call both annual and second diff reconcilers with correct arguments`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            transactionType = TransactionType.SECOND_INVOICE
        )
        val mainTemplate: TransactionTemplate = mock {
            on { lineItemTypes } doReturn listOf(LineItemType.EOR_SALARY_DISBURSEMENT)
            on { mergedTransactionTypes } doReturn listOf(TransactionType.ANNUAL_PLAN_INVOICE)
            on { itemSplitter } doReturn mock()
        }
        val mergedTemplate: TransactionTemplate = mock {
            on { lineItemTypes } doReturn listOf(LineItemType.ANNUAL_MANAGEMENT_FEE_EOR)
        }

        val mainDiff = mock<PayableItem>()
        val mergedDiff = mock<PayableItem>()
        // to be verify for passed args later
        val mainReconciler = mock<InvoiceDiffReconciler> {
            on { transactionType() } doReturn TransactionType.SECOND_INVOICE
            on { reconcile(any(), any(), any(), any()) } doReturn listOf(mainDiff)
        }
        // to be verify for passed args later
        val mergedReconciler = mock<InvoiceDiffReconciler> {
            on { transactionType() } doReturn TransactionType.ANNUAL_PLAN_INVOICE
            on { reconcile(any(), any(), any(), any()) } doReturn listOf(mergedDiff)
        }

        whenever(duplicatedSecondInvoiceValidator.validate(command)).then {}
        whenever(transactionTemplateProvider.findTemplateFor(TransactionType.SECOND_INVOICE, command.companyId))
            .thenReturn(mainTemplate)
        whenever(transactionTemplateProvider.findTemplateFor(TransactionType.ANNUAL_PLAN_INVOICE, command.companyId))
            .thenReturn(mergedTemplate)
        whenever(invoiceDiffReconcilerFactory.get(TransactionType.SECOND_INVOICE)).thenReturn(mainReconciler)
        whenever(invoiceDiffReconcilerFactory.get(TransactionType.ANNUAL_PLAN_INVOICE)).thenReturn(mergedReconciler)

        // Mock invoiced and latest items for merged
        val mergedItemStoreProvider = mock<ItemStoreDataProvider>()
        whenever(itemStoreDataProviderFactory.get(TransactionType.ANNUAL_PLAN_INVOICE)).thenReturn(
            mergedItemStoreProvider
        )
        val mockMergedLatestItem = listOf(mock<PayableItem>())
        whenever(mergedItemStoreProvider.fetchLatest(command, mergedTemplate.lineItemTypes)).thenReturn(
            mockMergedLatestItem
        )

        val mergedInvoiceProvider = mock<InvoiceDataProvider>()
        whenever(invoiceDataProviderFactory.get(any<Collection<TransactionType>>())).thenReturn(mapOf(TransactionType.ANNUAL_PLAN_INVOICE to mergedInvoiceProvider))
        val mockMergedInvoicedItems = listOf(mock<PayableItem>())
        whenever(mergedInvoiceProvider.fetchAndAggregateInvoiceItems(command)).thenReturn(mockMergedInvoicedItems)

        // Mock invoiced and latest items for main
        val mainItemStoreProvider = mock<ItemStoreDataProvider>()
        whenever(itemStoreDataProviderFactory.get(TransactionType.SECOND_INVOICE)).thenReturn(mainItemStoreProvider)
        val mockMainLatestItem = listOf(mock<PayableItem>())
        whenever(mainItemStoreProvider.fetchLatest(command, mainTemplate.lineItemTypes)).thenReturn(mockMainLatestItem)

        val mainInvoiceProvider = mock<InvoiceDataProvider>()
        whenever(invoiceDataProviderFactory.get(TransactionType.SECOND_INVOICE)).thenReturn(mainInvoiceProvider)
        val mockMainInvoicedItems = listOf(mock<PayableItem>())
        whenever(mainInvoiceProvider.fetchAndAggregateInvoiceItems(command)).thenReturn(mockMainInvoicedItems)

        // WHEN
        secondInvoiceDataReconciler.handle(command)

        // THEN
        // Verify that both diff reconcilers are called with the correct arguments
        verify(mainReconciler).reconcile(
            eq(command),
            eq(mainTemplate),
            argThat { containsAll(mockMainInvoicedItems + mockMergedInvoicedItems) },
            argThat { containsAll(mockMainLatestItem + mockMergedLatestItem) }
        )
        verify(mergedReconciler).reconcile(
            eq(command),
            eq(mergedTemplate),
            argThat { containsAll(mockMainInvoicedItems + mockMergedInvoicedItems) },
            argThat { containsAll(mockMainLatestItem + mockMergedLatestItem) }
        )
    }

    @Test
    fun `test payable formatter called`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            transactionType = TransactionType.SECOND_INVOICE
        )
        val dataFormatter = mock<DataFormatter>()
        val formattedItems = listOf(mock<PayableItem>())
        whenever(dataFormatter.format(any())).thenReturn(formattedItems)

        val mainTemplate: TransactionTemplate = mock {
            on { lineItemTypes } doReturn listOf(LineItemType.EOR_SALARY_DISBURSEMENT)
            on { mergedTransactionTypes } doReturn emptyList<TransactionType>()
            on { itemSplitter } doReturn mock()
            on { payableFormatter } doReturn dataFormatter
        }

        val mainDiff = mock<PayableItem>()
        val mainReconciler = mock<InvoiceDiffReconciler> {
            on { transactionType() } doReturn TransactionType.SECOND_INVOICE
            on { reconcile(any(), any(), any(), any()) } doReturn listOf(mainDiff)
        }

        whenever(duplicatedSecondInvoiceValidator.validate(command)).then {}
        whenever(transactionTemplateProvider.findTemplateFor(TransactionType.SECOND_INVOICE, command.companyId))
            .thenReturn(mainTemplate)
        whenever(invoiceDiffReconcilerFactory.get(TransactionType.SECOND_INVOICE)).thenReturn(mainReconciler)

        // Mock invoiced and latest items for main
        val mainItemStoreProvider = mock<ItemStoreDataProvider>()
        whenever(itemStoreDataProviderFactory.get(TransactionType.SECOND_INVOICE)).thenReturn(mainItemStoreProvider)

        val mainInvoiceProvider = mock<InvoiceDataProvider>()
        whenever(invoiceDataProviderFactory.get(TransactionType.SECOND_INVOICE)).thenReturn(mainInvoiceProvider)
        whenever(mainInvoiceProvider.fetchAndAggregateInvoiceItems(command)).thenReturn(emptyList())

        // Mock empty collections for merged transaction types since there are none
        whenever(invoiceDataProviderFactory.get(emptySet<TransactionType>())).thenReturn(emptyMap())

        // Mock the fetchLatest to return empty list to simplify the test
        whenever(mainItemStoreProvider.fetchLatest(command, mainTemplate.lineItemTypes))
            .thenReturn(emptyList())

        // WHEN
        secondInvoiceDataReconciler.handle(command)

        // THEN
        // Verify that the payable formatter was called
        verify(dataFormatter).format(any())
    }
}
