package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.payable.engine.anomalydetector.GrowthBookFlagConstants
import com.multiplier.payable.engine.anomalydetector.provider.AnnualManagementFeeProvider
import com.multiplier.payable.engine.anomalydetector.request.AnnualManagementFeeData
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoInteractions
import org.mockito.kotlin.whenever
import java.time.LocalDateTime

@ExtendWith(MockitoExtension::class)
class AnnualPlanDateRangeDuplicateRuleTest {
    private val transactionTemplateProvider: TransactionTemplateProvider = mock()
    private val annualManagementFeeProvider: AnnualManagementFeeProvider = mock()
    private val featureFlagService: FeatureFlagService = mock()

    private fun createValidFetchedData(): InvoiceAnomalyDetectorRequest {
        val mockPayable = mock<JpaCompanyPayable>()
        val mockInvoiceDTO = mock<InvoiceDTO>()
        return InvoiceAnomalyDetectorRequest.builder()
            .payable(mockPayable)
            .invoiceDTO(mockInvoiceDTO)
            .build()
    }

    @Mock
    private lateinit var jpaAnomalyReportRepository: JpaAnomalyReportRepository

    private val secondInvoiceAnomalyDetectorRule by lazy {
        AnnualPlanDateRangeDuplicateRule(
            transactionTemplateProvider = transactionTemplateProvider,
            annualManagementFeeProvider = annualManagementFeeProvider,
            featureFlagService = featureFlagService
        )
    }

    @Test
    fun `should do nothing for not supported template`() {
        // given
        val command = mock<InvoiceCommand>()
        whenever(command.transactionType).thenReturn(TransactionType.SECOND_INVOICE)
        whenever(command.companyId).thenReturn(1L)

        val fetchedData = createValidFetchedData()

        val mockedFeature = mock<GBFeatureResult>()
        whenever(mockedFeature.on).thenReturn(true)
        whenever(
            featureFlagService
                .feature(GrowthBookFlagConstants.ENABLE_IAD_ANNUAL_FEE_FLAG_NAME, mapOf()),
        ).thenReturn(mockedFeature)

        val mockedTemplate = mock<TransactionTemplate>()
        whenever(mockedTemplate.mergedTransactionTypes).thenReturn(emptyList())
        whenever(transactionTemplateProvider.findTemplateFor(TransactionType.SECOND_INVOICE, 1L))
            .thenReturn(mockedTemplate)

        // when
        secondInvoiceAnomalyDetectorRule.detect(command, fetchedData)

        // then
        verify(transactionTemplateProvider).findTemplateFor(TransactionType.SECOND_INVOICE, 1L)
        verifyNoInteractions(annualManagementFeeProvider)
    }

    @Test
    fun `should do nothing for off feature flag`() {
        // given
        val command = mock<InvoiceCommand>()
        val fetchedData = createValidFetchedData()

        val mockedFeature = mock<GBFeatureResult>()
        whenever(mockedFeature.on).thenReturn(false)
        whenever(
            featureFlagService
                .feature(GrowthBookFlagConstants.ENABLE_IAD_ANNUAL_FEE_FLAG_NAME, mapOf()),
        ).thenReturn(mockedFeature)

        // when
        secondInvoiceAnomalyDetectorRule.detect(command, fetchedData)

        // then
        verifyNoInteractions(transactionTemplateProvider)
    }

    @Test
    fun `should just log error for any unexpected exception`() {
        // given
        val command = mock<InvoiceCommand>()
        val fetchedData = createValidFetchedData()

        whenever(command.transactionType).thenReturn(TransactionType.SECOND_INVOICE)
        whenever(command.companyId).thenReturn(1L)

        val mockedFeature = mock<GBFeatureResult>()
        whenever(mockedFeature.on).thenReturn(true)
        whenever(
            featureFlagService
                .feature(GrowthBookFlagConstants.ENABLE_IAD_ANNUAL_FEE_FLAG_NAME, mapOf()),
        ).thenReturn(mockedFeature)

        val mockedTemplate = mock<TransactionTemplate>()
        whenever(mockedTemplate.mergedTransactionTypes).thenReturn(listOf(TransactionType.ANNUAL_PLAN_INVOICE))
        whenever(transactionTemplateProvider.findTemplateFor(TransactionType.SECOND_INVOICE, 1L))
            .thenReturn(mockedTemplate)

        whenever(annualManagementFeeProvider.get(command)).thenThrow(RuntimeException("test"))

        // then
        assertDoesNotThrow { secondInvoiceAnomalyDetectorRule.detect(command, fetchedData) }
    }

    @Test
    fun `should return success when feature flag is disabled`() {
        // given
        val command = mock<InvoiceCommand>()
        val fetchedData = createValidFetchedData()

        val mockedFeature = mock<GBFeatureResult>()
        whenever(mockedFeature.on).thenReturn(false)
        whenever(
            featureFlagService.feature(GrowthBookFlagConstants.ENABLE_IAD_ANNUAL_FEE_FLAG_NAME, mapOf()),
        ).thenReturn(mockedFeature)

        // when
        val result = secondInvoiceAnomalyDetectorRule.detect(command, fetchedData)

        // then
        assertThat(result.success).isTrue()
        assertThat(result.messages).hasSize(1)
        assertThat(result.messages[0]).contains("Rule 'AnnualPlanDateRangeDuplicateRule' is disabled by feature flag")
        verifyNoInteractions(transactionTemplateProvider)
        verifyNoInteractions(annualManagementFeeProvider)
    }

    @Test
    fun `should return success when not merged with annual plan template`() {
        // given
        val command = mock<InvoiceCommand>()
        val fetchedData = createValidFetchedData()

        whenever(command.transactionType).thenReturn(TransactionType.SECOND_INVOICE)
        whenever(command.companyId).thenReturn(1L)

        val mockedFeature = mock<GBFeatureResult>()
        whenever(mockedFeature.on).thenReturn(true)
        whenever(
            featureFlagService.feature(GrowthBookFlagConstants.ENABLE_IAD_ANNUAL_FEE_FLAG_NAME, mapOf()),
        ).thenReturn(mockedFeature)

        val mockedTemplate = mock<TransactionTemplate>()
        whenever(mockedTemplate.mergedTransactionTypes).thenReturn(emptyList())
        whenever(transactionTemplateProvider.findTemplateFor(TransactionType.SECOND_INVOICE, 1L))
            .thenReturn(mockedTemplate)

        // when
        val result = secondInvoiceAnomalyDetectorRule.detect(command, fetchedData)

        // then
        assertThat(result.success).isTrue()
        assertThat(result.messages).hasSize(1)
        assertThat(result.messages[0]).contains("No annual plan template merging detected")
        verify(transactionTemplateProvider).findTemplateFor(TransactionType.SECOND_INVOICE, 1L)
        verifyNoInteractions(annualManagementFeeProvider)
    }

    @Test
    fun `should return success when no overlaps detected`() {
        // given
        val command = mock<InvoiceCommand>()
        whenever(command.transactionType).thenReturn(TransactionType.SECOND_INVOICE)
        whenever(command.companyId).thenReturn(1L)

        val mockedFeature = mock<GBFeatureResult>()
        whenever(mockedFeature.on).thenReturn(true)
        whenever(
            featureFlagService.feature(GrowthBookFlagConstants.ENABLE_IAD_ANNUAL_FEE_FLAG_NAME, mapOf()),
        ).thenReturn(mockedFeature)

        val mockedTemplate = mock<TransactionTemplate>()
        whenever(mockedTemplate.mergedTransactionTypes).thenReturn(listOf(TransactionType.ANNUAL_PLAN_INVOICE))
        whenever(transactionTemplateProvider.findTemplateFor(TransactionType.SECOND_INVOICE, 1L))
            .thenReturn(mockedTemplate)

        val annualFeeData =
            listOf(
                AnnualManagementFeeData(
                    contractId = 1L,
                    dateRange =
                        DateRange(
                            startDate = LocalDateTime.of(2024, 1, 1, 0, 0),
                            endDate = LocalDateTime.of(2024, 12, 31, 23, 59),
                        ),
                    payableId = 1L,
                ),
            )
        whenever(annualManagementFeeProvider.get(command)).thenReturn(annualFeeData)

        val fetchedData = createValidFetchedData()

        // when
        val result = secondInvoiceAnomalyDetectorRule.detect(command, fetchedData)

        // then
        assertThat(result.success).isTrue()
        assertThat(result.messages).hasSize(1)
        assertThat(result.messages[0]).contains("No overlapping date ranges detected")
        verify(annualManagementFeeProvider).get(command)
    }

    @Test
    fun `should return failure when overlaps detected`() {
        // given
        val command = mock<InvoiceCommand>()
        whenever(command.transactionType).thenReturn(TransactionType.SECOND_INVOICE)
        whenever(command.companyId).thenReturn(1L)

        val mockedFeature = mock<GBFeatureResult>()
        whenever(mockedFeature.on).thenReturn(true)
        whenever(
            featureFlagService.feature(GrowthBookFlagConstants.ENABLE_IAD_ANNUAL_FEE_FLAG_NAME, mapOf()),
        ).thenReturn(mockedFeature)

        val mockedTemplate = mock<TransactionTemplate>()
        whenever(mockedTemplate.mergedTransactionTypes).thenReturn(listOf(TransactionType.ANNUAL_PLAN_INVOICE))
        whenever(transactionTemplateProvider.findTemplateFor(TransactionType.SECOND_INVOICE, 1L))
            .thenReturn(mockedTemplate)

        val annualFeeData =
            listOf(
                AnnualManagementFeeData(
                    contractId = 1L,
                    dateRange =
                        DateRange(
                            startDate = LocalDateTime.of(2024, 1, 1, 0, 0),
                            endDate = LocalDateTime.of(2024, 12, 31, 23, 59),
                        ),
                    payableId = 1L,
                ),
                AnnualManagementFeeData(
                    contractId = 1L,
                    dateRange =
                        DateRange(
                            startDate = LocalDateTime.of(2024, 6, 1, 0, 0),
                            endDate = LocalDateTime.of(2025, 5, 31, 23, 59),
                        ),
                    payableId = 2L,
                ),
            )
        whenever(annualManagementFeeProvider.get(command)).thenReturn(annualFeeData)

        val fetchedData = createValidFetchedData()

        // when
        val result = secondInvoiceAnomalyDetectorRule.detect(command, fetchedData)

        // then
        assertThat(result.success).isFalse()
        assertThat(result.messages).hasSize(1)
        assertThat(result.messages[0]).contains("contract ID 1")
        assertThat(result.messages[0]).contains("payable IDs [1, 2]")
        verify(annualManagementFeeProvider).get(command)
    }

    @Test
    fun `should handle exceptions gracefully`() {
        // given
        val command = mock<InvoiceCommand>()
        whenever(command.transactionType).thenReturn(TransactionType.SECOND_INVOICE)
        whenever(command.companyId).thenReturn(1L)

        val mockedFeature = mock<GBFeatureResult>()
        whenever(mockedFeature.on).thenReturn(true)
        whenever(
            featureFlagService.feature(GrowthBookFlagConstants.ENABLE_IAD_ANNUAL_FEE_FLAG_NAME, mapOf()),
        ).thenReturn(mockedFeature)

        val mockedTemplate = mock<TransactionTemplate>()
        whenever(mockedTemplate.mergedTransactionTypes).thenReturn(listOf(TransactionType.ANNUAL_PLAN_INVOICE))
        whenever(transactionTemplateProvider.findTemplateFor(TransactionType.SECOND_INVOICE, 1L))
            .thenReturn(mockedTemplate)

        whenever(annualManagementFeeProvider.get(command)).thenThrow(RuntimeException("Test error"))

        val fetchedData = createValidFetchedData()

        // when
        val result = secondInvoiceAnomalyDetectorRule.detect(command, fetchedData)

        // then
        assertThat(result.success).isFalse()
        assertThat(result.messages).hasSize(1)
        assertThat(result.messages[0]).contains("Exception occurred during anomaly detection")
        assertThat(result.messages[0]).contains("Test error")
        verify(annualManagementFeeProvider).get(command)
    }
}
