package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.service.PricingService
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.Pricing
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import java.time.LocalDate

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class PaymentTermsRuleTest {

    @Mock
    private lateinit var pricingService: PricingService

    @Mock
    private lateinit var featureFlagService: FeatureFlagService

    @Mock
    private lateinit var jpaAnomalyReportRepository: JpaAnomalyReportRepository

    @Mock
    private lateinit var mockCommand: InvoiceCommand

    private lateinit var paymentTermsRule: PaymentTermsRule

    @BeforeEach
    fun setUp() {
        paymentTermsRule = PaymentTermsRule(pricingService, featureFlagService)
    }

    @Test
    fun `should return success when feature flag is disabled`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(false)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = InvoiceAnomalyDetectorRequest.builder().build()

        // When
        val result = paymentTermsRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Rule 'PaymentTerms' is disabled by feature flag"))
    }

    @Test
    fun `should return success when request is invalid`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = InvoiceAnomalyDetectorRequest.builder().build() // Empty request

        // When
        val result = paymentTermsRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice data or company payable is null - validation skipped"))
    }

    @Test
    fun `should return success when due date matches expected date with default payment terms`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val invoiceDate = LocalDate.of(2024, 1, 15)
        val expectedDueDate = invoiceDate.plusDays(7) // Default 7 days
        val companyId = 123L

        val request = createMockRequest(invoiceDate, expectedDueDate, companyId)
        
        // Mock pricing service to return null (will use default 7 days)
        Mockito.`when`(pricingService.getCompanyPricing(companyId)).thenReturn(null)

        // When
        val result = paymentTermsRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Payment terms validation passed"))
    }

    @Test
    fun `should return success when due date matches expected date with custom payment terms`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val invoiceDate = LocalDate.of(2024, 2, 10)
        val paymentTerms = 14 // 14 days
        val expectedDueDate = invoiceDate.plusDays(paymentTerms.toLong())
        val companyId = 456L

        val request = createMockRequest(invoiceDate, expectedDueDate, companyId)
        
        // Mock pricing service to return custom payment terms
        val mockPricing = Mockito.mock(Pricing::class.java)
        Mockito.`when`(mockPricing.paymentTermInDays).thenReturn(paymentTerms)
        Mockito.`when`(pricingService.getCompanyPricing(companyId)).thenReturn(mockPricing)

        // When
        val result = paymentTermsRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Payment terms validation passed"))
    }

    @Test
    fun `should return failure when due date does not match expected date`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val invoiceDate = LocalDate.of(2024, 3, 15)
        val paymentTerms = 10 // 10 days
        val wrongDueDate = invoiceDate.plusDays(5) // Wrong due date (5 days instead of 10)
        val companyId = 789L

        val request = createMockRequest(invoiceDate, wrongDueDate, companyId)
        
        // Mock pricing service to return custom payment terms
        val mockPricing = Mockito.mock(Pricing::class.java)
        Mockito.`when`(mockPricing.paymentTermInDays).thenReturn(paymentTerms)
        Mockito.`when`(pricingService.getCompanyPricing(companyId)).thenReturn(mockPricing)

        // When
        val result = paymentTermsRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice due date '${wrongDueDate}' does not match expected due date '${invoiceDate.plusDays(10)}' based on payment terms (10 days)"))
    }

    @Test
    fun `should return failure when invoice date is missing`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val dueDate = LocalDate.of(2024, 4, 20)
        val companyId = 111L

        val request = createMockRequest(null, dueDate, companyId) // Missing invoice date

        // When
        val result = paymentTermsRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice date is missing"))
    }

    @Test
    fun `should return failure when due date is missing`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val invoiceDate = LocalDate.of(2024, 5, 10)
        val companyId = 222L

        val request = createMockRequest(invoiceDate, null, companyId) // Missing due date

        // When
        val result = paymentTermsRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice due date is missing"))
    }

    @Test
    fun `should use default payment terms when pricing service throws exception`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val invoiceDate = LocalDate.of(2024, 6, 15)
        val expectedDueDate = invoiceDate.plusDays(7) // Default 7 days
        val companyId = 333L

        val request = createMockRequest(invoiceDate, expectedDueDate, companyId)
        
        // Mock pricing service to throw exception
        Mockito.`when`(pricingService.getCompanyPricing(companyId)).thenThrow(RuntimeException("Service unavailable"))

        // When
        val result = paymentTermsRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Payment terms validation passed"))
    }

    @Test
    fun `should handle different payment terms correctly`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val testCases = listOf(
            Triple(7, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 1, 8)),
            Triple(14, LocalDate.of(2024, 2, 15), LocalDate.of(2024, 2, 29)),
            Triple(30, LocalDate.of(2024, 3, 1), LocalDate.of(2024, 3, 31)),
            Triple(1, LocalDate.of(2024, 4, 10), LocalDate.of(2024, 4, 11))
        )

        testCases.forEach { (paymentTerms, invoiceDate, expectedDueDate) ->
            val companyId = 444L
            val request = createMockRequest(invoiceDate, expectedDueDate, companyId)
            
            // Mock pricing service to return custom payment terms
            val mockPricing = Mockito.mock(Pricing::class.java)
            Mockito.`when`(mockPricing.paymentTermInDays).thenReturn(paymentTerms)
            Mockito.`when`(pricingService.getCompanyPricing(companyId)).thenReturn(mockPricing)

            // When
            val result = paymentTermsRule.detect(mockCommand, request)

            // Then
            assertTrue(result.success, "Failed for payment terms $paymentTerms days")
            assertEquals(1, result.messages.size)
            assertTrue(result.messages[0].contains("Payment terms validation passed"))
        }
    }

    @Test
    fun `should return correct rule type`() {
        // When
        val ruleType = paymentTermsRule.type

        // Then
        assertEquals(DetectionRuleType.PAYMENT_TERMS, ruleType)
    }

    @Test
    fun `should return correct rule name`() {
        // When
        val ruleName = paymentTermsRule.ruleName

        // Then
        assertEquals("PaymentTerms", ruleName)
    }

    @Test
    fun `should return correct feature flag name`() {
        // When
        val featureFlagName = paymentTermsRule.featureFlagName

        // Then
        assertEquals("ENABLE_PAYMENT_TERMS_CHECK", featureFlagName)
    }

    private fun createMockRequest(
        invoiceDate: LocalDate?,
        dueDate: LocalDate?,
        companyId: Long
    ): InvoiceAnomalyDetectorRequest {
        val mockInvoiceDTO = Mockito.mock(InvoiceDTO::class.java)
        Mockito.`when`(mockInvoiceDTO.date).thenReturn(invoiceDate)
        Mockito.`when`(mockInvoiceDTO.dueDate).thenReturn(dueDate)

        val mockCompanyPayable = Mockito.mock(JpaCompanyPayable::class.java)
        Mockito.`when`(mockCompanyPayable.companyId).thenReturn(companyId)

        return InvoiceAnomalyDetectorRequest
            .builder()
            .invoiceDTO(mockInvoiceDTO)
            .payable(mockCompanyPayable)
            .build()
    }
}
