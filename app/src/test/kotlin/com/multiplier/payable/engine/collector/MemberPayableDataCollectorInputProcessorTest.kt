package com.multiplier.payable.engine.collector

import com.multiplier.payable.engine.collector.memberpayable.MemberPayableInvoiceCommandValidator
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.MemberPayableInvoiceCommand
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.doThrow
import org.mockito.Mockito.mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.doReturn
import java.time.LocalDateTime

@ExtendWith(MockitoExtension::class)
class MemberPayableDataCollectorInputProcessorTest {

    @Mock
    private lateinit var memberPayableInvoiceCommandValidator: MemberPayableInvoiceCommandValidator

    @InjectMocks
    private lateinit var memberPayableDataCollectorInputProcessor: MemberPayableDataCollectorInputProcessor

    @Test
    fun `when validator throws exception, throw exception`() {
        val command = mock<InvoiceCommand>()
        doThrow(IllegalArgumentException::class.java).`when`(memberPayableInvoiceCommandValidator).validate(command)
        assertThrows(IllegalArgumentException::class.java) {
            memberPayableDataCollectorInputProcessor.process(command)
        }
    }

    @Test
    fun `when validation is fine, return the input object`() {
        val now = LocalDateTime.now()
        val dateRange = DateRange(
            now,
            now
        )
        val transactionId = ""
        val companyId = 1234L
        val memberPayableCommand = mock<MemberPayableInvoiceCommand>()

        val invoiceCommand = mock<InvoiceCommand>()

        doReturn(now).`when`(invoiceCommand).transactionDate
        doReturn(companyId).`when`(invoiceCommand).companyId
        doReturn(transactionId).`when`(invoiceCommand).transactionId
        doReturn(dateRange).`when`(invoiceCommand).dateRange
        doReturn(memberPayableCommand).`when`(invoiceCommand).memberPayableInvoiceCommand

        val processedMemberPayableCollectorInput = memberPayableDataCollectorInputProcessor.process(invoiceCommand)

        assertEquals(now, processedMemberPayableCollectorInput.transactionDate)
        assertEquals(transactionId, processedMemberPayableCollectorInput.transactionId)
        assertEquals(companyId, processedMemberPayableCollectorInput.companyId)
        assertEquals(memberPayableCommand, processedMemberPayableCollectorInput.memberPayableInvoiceCommand)
    }

}