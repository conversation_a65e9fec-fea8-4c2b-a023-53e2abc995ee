package com.multiplier.payable.engine.reconciler.descriptionbuilder.vas

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import com.multiplier.payable.engine.vas.Incident
import com.multiplier.payable.engine.vas.IncidentWrapper
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test

import org.junit.jupiter.api.Assertions.*

class VasIncidentMonitorPaymentDescriptionBuilderTest {

    private val builder = VasIncidentMonitorPaymentDescriptionBuilder()

    @Test
    fun getLineItemType() {
        assertEquals(LineItemType.VAS_INCIDENT_MONITOR_PAYMENT, builder.lineItemType)
    }

    @Test
    fun build() {
        val incident = mockk<Incident>() {
            every { description } returns "monitor payment"
        }
        val incidentWrapper = mockk<IncidentWrapper>() {
            every { <EMAIL>} returns incident
        }
        val context = mockk<PayableItemDescriptionBuilderContext>() {
            every { <EMAIL> } returns incidentWrapper
        }

        val description = builder.build(context)

        assertEquals("monitor payment", description)
    }
}