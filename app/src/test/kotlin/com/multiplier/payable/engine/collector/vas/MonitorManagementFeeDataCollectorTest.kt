package com.multiplier.payable.engine.collector.vas

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.data.ProcessedIncidentCollectorInput
import com.multiplier.payable.engine.collector.vas.fee.MonitorManagementFeeDataCollector
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.vas.IncidentType
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.Instant
import kotlin.test.assertEquals

class MonitorManagementFeeDataCollectorTest {
    @MockK
    private lateinit var vasIncidentManagementFeeCollector: VasIncidentManagementFeeCollector

    @InjectMockKs
    private lateinit var dataCollector: MonitorManagementFeeDataCollector

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `getSupportedType should return VAS_INCIDENT_MONITOR_MANAGEMENT_FEE`() {
        assertEquals(
            LineItemType.VAS_INCIDENT_MONITOR_MANAGEMENT_FEE,
            dataCollector.getSupportedType(),
        )
    }

    @Test
    fun `handle should process monitor management fee data successfully`() {
        // Given
        val transactionId = "tx-123"
        val command =
            mockk<InvoiceCommand> {
                every { <EMAIL> } returns transactionId
            }

        val processedInput = mockk<ProcessedIncidentCollectorInput>(relaxed = true)
        every { vasIncidentManagementFeeCollector.processInput(command) } returns processedInput
        every {
            vasIncidentManagementFeeCollector.collectAndSaveIncidents(
                processedInput,
                IncidentType.MONITOR,
            )
        } just runs

        // When
        dataCollector.handle(command)

        // Then
        verifySequence {
            vasIncidentManagementFeeCollector.processInput(command)
            processedInput.lineItemType = LineItemType.VAS_INCIDENT_MONITOR_MANAGEMENT_FEE
            processedInput.originalTimestamp = any()
            vasIncidentManagementFeeCollector.collectAndSaveIncidents(
                processedInput,
                IncidentType.MONITOR,
            )
        }
    }

    @Test
    fun `handle should throw exception when processInput fails`() {
        // Given
        val command = mockk<InvoiceCommand>()
        val errorMessage = "Failed to process input"
        every {
            vasIncidentManagementFeeCollector.processInput(command)
        } throws RuntimeException(errorMessage)

        // When/Then
        val exception =
            assertThrows<RuntimeException> {
                dataCollector.handle(command)
            }
        assertEquals(errorMessage, exception.message)
    }

    @Test
    fun `handle should throw exception when collectAndSaveIncidents fails`() {
        // Given
        val command = mockk<InvoiceCommand>()
        val processedInput = mockk<ProcessedIncidentCollectorInput>(relaxed = true)
        val errorMessage = "Failed to collect and save incidents"

        every { vasIncidentManagementFeeCollector.processInput(command) } returns processedInput
        every {
            vasIncidentManagementFeeCollector.collectAndSaveIncidents(any(), any())
        } throws RuntimeException(errorMessage)

        // When/Then
        val exception =
            assertThrows<RuntimeException> {
                dataCollector.handle(command)
            }
        assertEquals(errorMessage, exception.message)
    }

    @Test
    fun `handle should set correct timestamp`() {
        // Given
        val command = mockk<InvoiceCommand>()
        val processedInput = mockk<ProcessedIncidentCollectorInput>(relaxed = true)
        val beforeExecution = Instant.now()

        every { vasIncidentManagementFeeCollector.processInput(command) } returns processedInput
        every {
            vasIncidentManagementFeeCollector.collectAndSaveIncidents(any(), any())
        } just runs

        // When
        dataCollector.handle(command)
        val afterExecution = Instant.now()

        // Then
        verify {
            processedInput.originalTimestamp =
                withArg { timestamp ->
                    assert(timestamp in beforeExecution..afterExecution) {
                        "Timestamp should be between $beforeExecution and $afterExecution but was $timestamp"
                    }
                }
        }
    }

    @Test
    fun `handle should use correct incident type`() {
        // Given
        val command = mockk<InvoiceCommand>()
        val processedInput = mockk<ProcessedIncidentCollectorInput>(relaxed = true)

        every { vasIncidentManagementFeeCollector.processInput(command) } returns processedInput
        every {
            vasIncidentManagementFeeCollector.collectAndSaveIncidents(any(), any())
        } just runs

        // When
        dataCollector.handle(command)

        // Then
        verify(exactly = 1) {
            vasIncidentManagementFeeCollector.collectAndSaveIncidents(
                processedInput,
                IncidentType.MONITOR,
            )
        }
    }
}
