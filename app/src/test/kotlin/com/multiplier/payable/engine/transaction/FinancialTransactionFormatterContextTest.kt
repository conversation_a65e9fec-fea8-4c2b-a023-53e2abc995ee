package com.multiplier.payable.engine.transaction

import com.multiplier.payable.engine.domain.aggregates.AnnualSeatPaymentTerm
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.payableitem.ContractDepartment
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.transaction.template.FinancialTransactionFormatterContext
import com.multiplier.payable.types.CountryCode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.Mockito.mock
import org.mockito.kotlin.whenever
import java.time.LocalDateTime
import java.util.stream.Stream

class FinancialTransactionFormatterContextTest {
    private val period =
        DateRange(
            startDate = LocalDateTime.of(2024, 1, 1, 0, 0),
            endDate = LocalDateTime.of(2024, 1, 31, 0, 0),
        )

    @Test
    fun `country property should return empty string when payableItems list is empty`() {
        // Arrange
        val context = FinancialTransactionFormatterContext(
            referenceDate = LocalDateTime.now(),
            payableItems = mutableListOf(),
            period = period
        )

        // Act
        val result = context.country

        // Assert
        assertEquals("", result)
    }

    @Test
    fun `country property should concatenate unique country codes when payableItems list is not empty`() {
        // Arrange
        val payableItem1 =
            PayableItem(
                month = 1,
                year = 2024,
                lineItemType = "",
                contractId = 123,
                companyId = 234,
                description = "",
                amountInBaseCurrency = 1000.0,
                baseCurrency = "USD",
                billableCost = 1000.0,
                versionId = "1",
                originalTimestamp = 123L,
                cycle = InvoiceCycle.BIWEEKLY,
                countryCode = CountryCode.USA.name,
            )
        val payableItem2 =
            PayableItem(
                month = 1,
                year = 2024,
                lineItemType = "",
                contractId = 234,
                companyId = 234,
                description = "",
                amountInBaseCurrency = 1000.0,
                baseCurrency = "USD",
                billableCost = 1000.0,
                versionId = "1",
                originalTimestamp = 123L,
                cycle = InvoiceCycle.BIWEEKLY,
                countryCode = CountryCode.IND.name,
            )
        val payableItem3 =
            PayableItem(
                month = 1,
                year = 2024,
                lineItemType = "",
                contractId = 345,
                companyId = 234,
                description = "",
                amountInBaseCurrency = 1000.0,
                baseCurrency = "USD",
                billableCost = 1000.0,
                versionId = "1",
                originalTimestamp = 123L,
                cycle = InvoiceCycle.BIWEEKLY,
                countryCode = CountryCode.USA.name,
            )
        val payableItems = listOf(payableItem1, payableItem2, payableItem3)
        val context =
            FinancialTransactionFormatterContext(
                referenceDate = LocalDateTime.now(),
                payableItems = payableItems.toMutableList(),
                period = period
            )
        val result = context.country

        assertEquals("USA, IND", result)
    }

    @Test
    fun `getMonthInThreeLetters should return the correct three-letter month abbreviation`() {
        // Arrange
        val transactionDate = LocalDateTime.of(2022, 5, 15, 0, 0)
        val context =
            FinancialTransactionFormatterContext(
                referenceDate = transactionDate,
                payableItems = mutableListOf(),
                period = period,
            )

        // Act
        val result = context.getMonthInThreeLetters()

        // Assert
        assertEquals("May", result)
    }

    @Test
    fun `getYearInTwoDigits should return the correct two-digit year`() {
        // Arrange
        val transactionDate = LocalDateTime.of(2022, 5, 15, 0, 0)
        val context =
            FinancialTransactionFormatterContext(
                referenceDate = transactionDate,
                payableItems = mutableListOf(),
                period = period,
            )

        // Act
        val result = context.getYearInTwoDigits()

        // Assert
        assertEquals("22", result)
    }

    @ParameterizedTest
    @MethodSource
    fun givenContext_whenGetFirstCountry_thenReturnResult(
        payableItems: List<PayableItem>,
        expectedResult: String,
    ) {
        // GIVEN
        val context =
            FinancialTransactionFormatterContext(
                referenceDate = mock(),
                payableItems = payableItems,
                period = mock(),
            )

        // WHEN
        val result = context.getFirstCountry()

        // THEN
        assertThat(result).isEqualTo(expectedResult)
    }

    @ParameterizedTest
    @MethodSource
    fun givenContext_whenGetFirstDepartmentName_thenReturnResult(
        payableItems: List<PayableItem>,
        expectedResult: String,
    ) {
        // GIVEN
        val context =
            FinancialTransactionFormatterContext(
                referenceDate = mock(),
                payableItems = payableItems,
                period = mock()
            )

        // WHEN
        val result = context.getFirstDepartmentName()

        // THEN
        assertThat(result).isEqualTo(expectedResult)
    }

    @ParameterizedTest
    @MethodSource
    fun givenContext_whenGetPaymentTermDescription_thenReturnResult(
        payableItems: List<PayableItem>,
        expectedResult: String,
    ) {
        // GIVEN
        val context =
            FinancialTransactionFormatterContext(
                referenceDate = mock(),
                payableItems = payableItems,
                period = mock(),
            )

        // WHEN
        val result = context.getPaymentTermDescription()

        // THEN
        assertThat(result).isEqualTo(expectedResult)
    }

    companion object {
        /**
         * A data source for the test with the same name.
         * See https://www.baeldung.com/parameterized-tests-junit-5#6-method
         */
        @JvmStatic
        private fun givenContext_whenGetFirstCountry_thenReturnResult(): Stream<Arguments> {
            val payableItemWithoutCountryName = mock<PayableItem>()

            val payableItemWithEmptyCountryName = mock<PayableItem>()
            val emptyCountryName = ""
            whenever(payableItemWithEmptyCountryName.countryName)
                .thenReturn(emptyCountryName)

            val payableItemWithCountryName = mock<PayableItem>()
            val countryName = "awesomeCountryName"
            whenever(payableItemWithCountryName.countryName)
                .thenReturn(countryName)

            return Stream.of(
                Arguments.of(
                    emptyList<PayableItem>(),
                    "",
                ),
                Arguments.of(
                    listOf(payableItemWithoutCountryName),
                    "",
                ),
                Arguments.of(
                    listOf(payableItemWithEmptyCountryName),
                    "",
                ),
                Arguments.of(
                    listOf(payableItemWithCountryName),
                    " ($countryName)",
                ),
            )
        }

        @JvmStatic
        private fun givenContext_whenGetFirstDepartmentName_thenReturnResult(): Stream<Arguments> {
            val payableItemWithoutDepartment = mock<PayableItem>()

            val payableItemWithDepartmentWithEmptyName = mock<PayableItem>()
            val contractDepartmentWithEmptyName = ContractDepartment(id = 1, name = "")
            whenever(payableItemWithDepartmentWithEmptyName.contractDepartment)
                .thenReturn(contractDepartmentWithEmptyName)

            val payableItemWithDepartment = mock<PayableItem>()
            val contractDepartment = ContractDepartment(id = 1, name = "awesomeDepartmentName")
            whenever(payableItemWithDepartment.contractDepartment)
                .thenReturn(contractDepartment)

            return Stream.of(
                Arguments.of(
                    emptyList<PayableItem>(),
                    "",
                ),
                Arguments.of(
                    listOf(payableItemWithoutDepartment),
                    "",
                ),
                Arguments.of(
                    listOf(payableItemWithDepartmentWithEmptyName),
                    "",
                ),
                Arguments.of(
                    listOf(payableItemWithDepartment),
                    " (awesomeDepartmentName)",
                ),
            )
        }

        @JvmStatic
        private fun givenContext_whenGetPaymentTermDescription_thenReturnResult(): Stream<Arguments> {
            val payableItemWithTermEveryMonth = mock<PayableItem>()
            val payableItemWithTermEveryTwoMonths = mock<PayableItem>()
            val payableItemWithTermWholeYear = mock<PayableItem>()
            val monthlyAnnualSeatPaymentTerm = AnnualSeatPaymentTerm(
                planPeriod = DateRange(
                    startDate = LocalDateTime.of(2024, 1, 1, 0, 0),
                    endDate = LocalDateTime.of(2024, 12, 31, 0, 0),
                ),
                seatId = 1L,
                interval = 1,
                timeUnit = "MONTH",
                periodNumber = 1,
                periodCount = 12,
                periodDateRange = DateRange(
                    startDate = LocalDateTime.of(2024, 1, 1, 0, 0),
                    endDate = LocalDateTime.of(2024, 12, 31, 0, 0),
                ),
            )
            val everyTwoMonthAnnualSeatPaymentTerm = AnnualSeatPaymentTerm(
                planPeriod = DateRange(
                    startDate = LocalDateTime.of(2024, 1, 1, 0, 0),
                    endDate = LocalDateTime.of(2024, 12, 31, 0, 0),
                ),
                seatId = 1L,
                interval = 2,
                timeUnit = "MONTH",
                periodNumber = 1,
                periodCount = 6,
                periodDateRange = DateRange(
                    startDate = LocalDateTime.of(2024, 1, 1, 0, 0),
                    endDate = LocalDateTime.of(2024, 12, 31, 0, 0),
                ),
            )

            val wholeYearAnnualSeatPaymentTerm = AnnualSeatPaymentTerm(
                planPeriod = DateRange(
                    startDate = LocalDateTime.of(2024, 1, 1, 0, 0),
                    endDate = LocalDateTime.of(2024, 12, 31, 0, 0),
                ),
                seatId = 1L,
                interval = 1,
                timeUnit = "YEAR",
                periodNumber = 1,
                periodCount = 1,
                periodDateRange = DateRange(
                    startDate = LocalDateTime.of(2024, 1, 1, 0, 0),
                    endDate = LocalDateTime.of(2024, 12, 31, 0, 0),
                ),
            )

            whenever(payableItemWithTermEveryMonth.annualSeatPaymentTerm)
                .thenReturn(monthlyAnnualSeatPaymentTerm)
            whenever(payableItemWithTermWholeYear.annualSeatPaymentTerm)
                .thenReturn(wholeYearAnnualSeatPaymentTerm)
            whenever(payableItemWithTermEveryTwoMonths.annualSeatPaymentTerm)
                .thenReturn(everyTwoMonthAnnualSeatPaymentTerm)

            return Stream.of(
                Arguments.of(
                    emptyList<PayableItem>(),
                    "",
                ),
                Arguments.of(
                    listOf(payableItemWithTermEveryMonth),
                    " (Billed every 1 month)",
                ),
                Arguments.of(
                    listOf(payableItemWithTermWholeYear),
                    "",
                ),
                Arguments.of(
                    listOf(payableItemWithTermEveryTwoMonths),
                    " (Billed every 2 months)",
                )
            )
        }
    }

    @Test
    fun `getMemberName should return empty string when payableItems list is empty`() {
        // GIVEN
        val context = FinancialTransactionFormatterContext(
            referenceDate = LocalDateTime.now(),
            payableItems = mutableListOf(),
            period = period
        )

        // WHEN
        val result = context.getMemberName()

        // THEN
        assertThat(result).isEmpty()
    }

    @Test
    fun `getMemberName should return member name enclosed in parentheses when a member name is present`() {
        // GIVEN
        val payableItem = mock(PayableItem::class.java)
        whenever(payableItem.memberName).thenReturn("John Doe")

        val context = FinancialTransactionFormatterContext(
            referenceDate = LocalDateTime.now(),
            payableItems = listOf(payableItem),
            period = period
        )

        // WHEN
        val result = context.getMemberName()

        // THEN
        assertThat(result).isEqualTo(" John Doe")
    }

    @Test
    fun `getMemberName should return empty string when member name is empty`() {
        // GIVEN
        val payableItem = mock(PayableItem::class.java)
        whenever(payableItem.memberName).thenReturn("")

        val context = FinancialTransactionFormatterContext(
            referenceDate = LocalDateTime.now(),
            payableItems = listOf(payableItem),
            period = period
        )

        // WHEN
        val result = context.getMemberName()

        // THEN
        assertThat(result).isEmpty()
    }

    @Test
    fun `getMemberName should return empty string when member name is null`() {
        // GIVEN
        val payableItem = mock(PayableItem::class.java)
        whenever(payableItem.memberName).thenReturn(null)
        val context = FinancialTransactionFormatterContext(
            referenceDate = LocalDateTime.now(),
            payableItems = listOf(payableItem),
            period = period
        )

        // WHEN
        val result = context.getMemberName()

        // THEN
        assertThat(result).isEmpty()
    }

    @Test
    fun `getContractId should return empty string when payableItems list is empty`() {
        // GIVEN
        val context = FinancialTransactionFormatterContext(
            referenceDate = LocalDateTime.now(),
            payableItems = mutableListOf(),
            period = period
        )

        // WHEN
        val result = context.getContractId()

        // THEN
        assertThat(result).isEmpty()
    }

    @Test
    fun `getContractId should return contract id enclosed in parentheses when a contract id is present`() {
        // GIVEN
        val payableItem = mock(PayableItem::class.java)
        whenever(payableItem.contractId).thenReturn(123L)

        val context = FinancialTransactionFormatterContext(
            referenceDate = LocalDateTime.now(),
            payableItems = listOf(payableItem),
            period = period
        )

        // WHEN
        val result = context.getContractId()

        // WHEN
        assertThat(result).isEqualTo(" 123")
    }

    @Test
    fun `getContractId should return empty string when contract id is null`() {
        // GIVEN
        val payableItem = mock(PayableItem::class.java)
        whenever(payableItem.contractId).thenReturn(null)

        val context = FinancialTransactionFormatterContext(
            referenceDate = LocalDateTime.now(),
            payableItems = listOf(payableItem),
            period = period
        )

        // WHEN
        val result = context.getContractId()

        // THEN
        assertThat(result).isEmpty()
    }
}
