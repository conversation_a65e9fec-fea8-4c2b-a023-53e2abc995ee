package com.multiplier.payable.engine.memberpayable.managmentfee

import com.google.protobuf.Timestamp
import com.multiplier.billing.grpc.billing.Billing
import com.multiplier.grpc.common.currency.v2.Currency
import com.multiplier.grpc.common.number.v2.BigDecimalOuterClass
import com.multiplier.grpc.common.pricing.v2.AmountOuterClass
import com.multiplier.grpc.common.time.v2.DurationOuterClass
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import java.time.Instant

class MemberPayableManagementFeeBillingMapperTest {

    private val memberPayableManagementFeeBillingMapper = MemberPayableManagementFeeBillingMapper()

    @Test
    fun mapBilling() {
        //given
        val billedItemMock = mock(Billing.BilledItem::class.java)
        val memberPayableId = 1L
        val amountValue = 1234.0
        val currencyCode = CurrencyCode.AED
        val billId = 123L
        val startDate = Instant.now()
        val endDate = Instant.MAX

        //mock
        `when`(billedItemMock.id).thenReturn(billId)

        val billingAmountMock = mock(AmountOuterClass.Amount::class.java)
        `when`(billedItemMock.billingAmount).thenReturn(billingAmountMock)

        val billingPeriodMock = mock(Billing.BillingPeriod::class.java)
        `when`(billedItemMock.billingPeriod).thenReturn(billingPeriodMock)
        val billingDurationMock = mock(DurationOuterClass.Duration::class.java)
        `when`(billingPeriodMock.billingDuration).thenReturn(billingDurationMock)
        val startDateMock = mock(Timestamp::class.java)
        `when`(billingDurationMock.startDate).thenReturn(startDateMock)
        val endDateMock = mock(Timestamp::class.java)
        `when`(billingDurationMock.endDate).thenReturn(endDateMock)
        `when`(startDateMock.seconds).thenReturn(startDate.epochSecond)
        `when`(startDateMock.nanos).thenReturn(startDate.nano)
        `when`(endDateMock.seconds).thenReturn(endDate.epochSecond)
        `when`(endDateMock.nanos).thenReturn(endDate.nano)

        val bigDecimalMock = mock(BigDecimalOuterClass.BigDecimal::class.java)
        `when`(billingAmountMock.value).thenReturn(bigDecimalMock)
        `when`(bigDecimalMock.value).thenReturn(amountValue.toString())
        `when`(billingAmountMock.currency).thenReturn(Currency.CurrencyCode.CURRENCY_CODE_AED)

        //then
        val result = memberPayableManagementFeeBillingMapper.mapBilling(
            billedItemMock,
            memberPayableId,
        )

        //assert
        assertNotNull(result)
        assertEquals(123L, result.billId)
        assertEquals(amountValue, result.amount.amount)
        assertEquals(currencyCode, result.amount.currency)
        assertEquals(billId, result.billId)
        assertEquals(startDate, result.startDate)
        assertEquals(endDate, result.endDate)
    }
}