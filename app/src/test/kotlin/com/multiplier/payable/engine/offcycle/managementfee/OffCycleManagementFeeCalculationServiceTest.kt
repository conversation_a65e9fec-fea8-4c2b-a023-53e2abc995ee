package com.multiplier.payable.engine.offcycle.managementfee

import com.google.protobuf.Timestamp
import com.multiplier.billing.grpc.billing.Billing
import com.multiplier.core.payable.adapters.BillingServiceAdapter
import com.multiplier.grpc.common.currency.v2.Currency
import com.multiplier.grpc.common.number.v2.BigDecimalOuterClass
import com.multiplier.grpc.common.pricing.v2.AmountOuterClass
import com.multiplier.grpc.common.time.v2.DurationOuterClass
import com.multiplier.payable.engine.collector.payroll.ContractPayroll
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.types.CurrencyCode
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

class OffCycleManagementFeeCalculationServiceTest {

    private val billingServiceAdapter = mockk<BillingServiceAdapter>()

    private val service = OffCycleManagementFeeCalculationService(
        billingServiceAdapter
    )

    @Test
    fun `should calculate amount for off-cycle payroll`() {
        // Given
        val contractPayroll = ContractPayroll(
            companyId = 123L,
            contractId = 456L,
            amountTotalCost = 1000.0,
            totalExpenseAmount = 100.0,
            totalSeveranceAccruals = 0.0,
            currencyCode = CurrencyCode.USD,
            countryCode = "USA",
            startDate = LocalDate.now(),
            endDate = LocalDate.now().plusDays(30),
            type = ContractType.EMPLOYEE,
            isOffCycle = true,
            payCycleCount = 1,
            fetchedTime = System.currentTimeMillis(),
            payrollCycleId = 789L,
            hasBillingException = false
        )

        val input = OffCycleManagementFeeCalculateInput(
            companyId = 123L,
            contractPayroll = contractPayroll,
            startDate = LocalDate.now(),
            endDate = LocalDate.now().plusDays(30)
        )

        val billedItem = createTestBilledItem(789L)
        val billingResult = Billing.BillingResultPerLineCode.newBuilder()
            .setLineCode("OFFCYCLE_PAYROLL_RUNS_FEE")
            .addItems(billedItem)
            .build()

        every {
            billingServiceAdapter.generateBills(
                companyId = 123L,
                startDateSeconds = any(),
                endDateSeconds = any(),
                lineItemCodes = listOf("OFFCYCLE_PAYROLL_RUNS_FEE")
            )
        } returns listOf(billingResult)

        // When
        val result = service.calculateAmount(input)

        // Then
        assertEquals(50.0, result.amount)
        assertEquals(CurrencyCode.USD, result.currency)

        verify {
            billingServiceAdapter.generateBills(
                companyId = 123L,
                startDateSeconds = any(),
                endDateSeconds = any(),
                lineItemCodes = listOf("OFFCYCLE_PAYROLL_RUNS_FEE")
            )
        }
    }

    private fun createTestBilledItem(payrollCycleId: Long): Billing.BilledItem {
        val usage = com.multiplier.metering.grpc.schema.Metering.Metric
            .newBuilder()
            .addLabels(
                com.multiplier.metering.grpc.schema.Metering.MetricLabel
                    .newBuilder()
                    .setKey("payroll_cycle_id")
                    .setValue(payrollCycleId.toString())
                    .build()
            )
            .addLabels(
                com.multiplier.metering.grpc.schema.Metering.MetricLabel
                    .newBuilder()
                    .setKey("contract_id")
                    .setValue("456") // This should match the contractId in the test
                    .build()
            )
            .build()

        val amount = 50.0
        val startDate = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val endDate = startDate.plus(1, ChronoUnit.DAYS)

        return Billing.BilledItem.newBuilder().setId(12345L).setBillingAmount(
            AmountOuterClass.Amount.newBuilder().setValue(
                BigDecimalOuterClass.BigDecimal.newBuilder().setValue(amount.toString()).build()
            ).setCurrency(Currency.CurrencyCode.CURRENCY_CODE_USD).build()
        ).setBillingPeriod(
            Billing.BillingPeriod.newBuilder().setBillingDuration(
                DurationOuterClass.Duration.newBuilder().setStartDate(
                    Timestamp.newBuilder().setSeconds(startDate.epochSecond).build()
                ).setEndDate(
                    Timestamp.newBuilder().setSeconds(endDate.epochSecond).build()
                ).build()
            ).build()
        ).addUsage(usage).build()
    }
}
