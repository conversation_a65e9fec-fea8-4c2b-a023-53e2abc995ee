package com.multiplier.payable.engine.reconciler.data.invoice

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.service.InvoiceFetcher
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.testutils.CompanyPayableTestDataFactory
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.mapper.CompanyPayableMapper
import com.multiplier.payable.types.PayableStatus
import org.apache.commons.lang3.tuple.Pair
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import java.time.LocalDateTime

@ExtendWith(MockitoExtension::class)
class FirstInvoiceDataProviderTest {
    private val invoiceFetcher: InvoiceFetcher = mock()
    private val jpaCompanyPayableRepository: JpaCompanyPayableRepository = mock()
    private val mapper: CompanyPayableMapper = mock()

    val provider: FirstInvoiceDataProvider =
        FirstInvoiceDataProvider(
            invoiceFetcher = invoiceFetcher,
            jpaCompanyPayableRepository = jpaCompanyPayableRepository,
            mapper = mapper,
        )

    @Test
    fun givenValidCommand_whenFetchActiveInvoices_shouldReturnCorrectly() {
        // GIVEN
        val command =
            InvoiceCommand(
                transactionId = "random",
                transactionDate = LocalDateTime.of(2024, 2, 2, 0, 0),
                transactionType = TransactionType.FIRST_INVOICE,
                companyId = 1L,
                dateRange = DateRange(LocalDateTime.now(), LocalDateTime.now()),
                cycle = InvoiceCycle.YEARLY,
            )

        `when`(invoiceFetcher.getFirstInvoices(eq(1L), any(), any())).thenReturn(listOf(Pair.of(1L, 1L)))

        val inactiveStatuses = listOf(PayableStatus.VOIDED, PayableStatus.DELETED)
        `when`(jpaCompanyPayableRepository.findByIdInAndStatusNotIn(eq(listOf(1L)), eq(inactiveStatuses)))
            .thenReturn(listOf(JpaCompanyPayable()))

        `when`(mapper.mapCompanyPayables(any())).thenReturn(
            listOf(
                CompanyPayableTestDataFactory.createCompanyPayable(
                    id = 10L,
                    companyId = 1L,
                    items = listOf(),
                    itemType = TransactionType.FIRST_INVOICE,
                    transactionId = "random",
                ),
            ),
        )

        // WHEN
        val result = provider.fetchActiveInvoices(command)

        // THEN
        assertEquals(1, result.size)
        assertEquals(1L, result[0].companyId)
        assertEquals(10L, result[0].id)
        assertEquals(TransactionType.FIRST_INVOICE, result[0].itemType)
    }

    @Test
    fun whenTransactionType_thenReturnResponse() {
        // WHEN
        var transactionType = provider.transactionType()

        // THEN
        assertThat(transactionType).isEqualTo(TransactionType.FIRST_INVOICE);
    }
}