package com.multiplier.payable.engine.reconciler.order

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.payableitem.PayableItem
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock

class OrderFormAdvanceCollectionDiffCalculatorTest {

    private val calculator = OrderFormAdvanceCollectionDiffCalculator()

    @Test
    fun `test calculate`() {

        val invoicedItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.ORDER_FORM_ADVANCE_EOR.name
            on { billId } doReturn "1"
        }

        val latestItem1 = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.ORDER_FORM_ADVANCE_EOR.name
            on { billId } doReturn "1"
        }
        val latestItem2 = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.ORDER_FORM_ADVANCE_EOR.name
            on { billId } doReturn "2"
        }

        val result = calculator.calculate(
            latestItems = listOf(latestItem1, latestItem2),
            invoicedItems = listOf(invoicedItem),
        )

        Assertions.assertEquals(listOf(latestItem2), result.toBeCreated)
    }
}
