package com.multiplier.payable.engine.transaction.generator

import com.multiplier.core.payable.adapters.CountryServiceAdapter
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.invoice.database.JpaInvoiceRepository
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.JpaInvoice
import com.multiplier.core.payable.service.exception.InvoiceGenerationErrorCode
import com.multiplier.core.payable.service.exception.InvoiceGenerationException
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.domain.entities.DefaultFinancialTransaction
import com.multiplier.payable.engine.domain.entities.FinancialTransaction
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.testutils.CompanyPayableTestDataFactory
import com.multiplier.payable.engine.transaction.mapper.JpaInvoiceMapper
import com.multiplier.payable.types.CompanyPayableType
import com.multiplier.payable.types.CountryCode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.verify
import java.time.LocalDateTime
import java.util.*

@ExtendWith(MockitoExtension::class)
class InternalFinancialTransactionGeneratorTest {

    @Mock
    lateinit var companyPayableRepository: JpaCompanyPayableRepository

    @Mock
    lateinit var invoiceRepository: JpaInvoiceRepository

    @Mock
    lateinit var countryServiceAdapter: CountryServiceAdapter

    @Mock
    lateinit var invoiceMapper: JpaInvoiceMapper

    @InjectMocks
    private lateinit var internalGenerator: InternalFinancialTransactionGenerator

    @Test
    fun `should generate debit note successfully`() {
        // GIVEN
        val payableItem = CompanyPayableTestDataFactory.createPayableItem(
            itemType = LineItemType.PEO_SALARY_DISBURSEMENT.name,
            countryCode = CountryCode.SGP.name
        )
        val input = createFinancialTransaction(listOf(payableItem))
        val jpaCompanyPayable = CompanyPayableTestDataFactory.createJpaCompanyPayable()
        val savedJpaInvoice = CompanyPayableTestDataFactory.createJpaInvoice(id = 1234L)

        `when`(companyPayableRepository.findById(input.companyPayableId))
            .thenReturn(Optional.of(jpaCompanyPayable))
        `when`(invoiceMapper.mapFromContextToJpaInvoice(jpaCompanyPayable, input))
            .thenReturn(savedJpaInvoice)
        `when`(invoiceRepository.save(Mockito.any(JpaInvoice::class.java)))
            .thenReturn(savedJpaInvoice)

        // WHEN
        val result = internalGenerator.generate(financialTransaction = input)

        // THEN
        assertThat(result).isNotNull()
        assertEquals("1234", result)
        verify(companyPayableRepository).saveAndFlush(jpaCompanyPayable)
    }

    @Test
    fun `should generate invoice successfully`() {
        // GIVEN
        val payableItem = CompanyPayableTestDataFactory.createPayableItem(
            itemType = LineItemType.ANNUAL_MANAGEMENT_FEE_EOR.name,
            countryCode = CountryCode.SGP.name
        )
        val input = createFinancialTransaction(listOf(payableItem))
        val jpaCompanyPayable =
            CompanyPayableTestDataFactory.createJpaCompanyPayable(type = CompanyPayableType.ANNUAL_PLAN)
        val savedJpaInvoice = CompanyPayableTestDataFactory.createJpaInvoice(id = 1234L)

        `when`(companyPayableRepository.findById(input.companyPayableId))
            .thenReturn(Optional.of(jpaCompanyPayable))
        `when`(invoiceMapper.mapFromContextToJpaInvoice(jpaCompanyPayable, input))
            .thenReturn(savedJpaInvoice)
        `when`(invoiceRepository.save(Mockito.any(JpaInvoice::class.java)))
            .thenReturn(savedJpaInvoice)

        // WHEN
        val result = internalGenerator.generate(financialTransaction = input)

        // THEN
        assertThat(result).isNotNull()
        assertEquals("1234", result)
        verify(companyPayableRepository).saveAndFlush(jpaCompanyPayable)
    }

    @Test
    fun `should throw payable not found when generate`() {
        // GIVEN
        val payableItem = CompanyPayableTestDataFactory.createPayableItem(
            itemType = LineItemType.PEO_SALARY_DISBURSEMENT.name,
            countryCode = "USA"
        )
        val input = createFinancialTransaction(listOf(payableItem))

        `when`(companyPayableRepository.findById(input.companyPayableId))
            .thenReturn(Optional.empty())

        // THEN
        val exception =
            assertThrows<InvoiceGenerationException> { internalGenerator.generate(financialTransaction = input) }
        assertEquals(InvoiceGenerationErrorCode.MPE_PAYABLE_NOT_FOUND, exception.invoiceGenerationErrorCode)
    }

    private fun createFinancialTransaction(items: List<PayableItem>): FinancialTransaction {
        return DefaultFinancialTransaction().apply {
            transactionId = "1234"
            companyId = 1L
            currency = CurrencyCode.SGD
            date = LocalDateTime.of(2024, 2, 5, 1, 0, 0)
            dueDate = LocalDateTime.of(2024, 2, 12, 1, 0, 0)
            reference = "awesomeRef"
            this.items = items
            companyPayableId = 1L
        }
    }
}
