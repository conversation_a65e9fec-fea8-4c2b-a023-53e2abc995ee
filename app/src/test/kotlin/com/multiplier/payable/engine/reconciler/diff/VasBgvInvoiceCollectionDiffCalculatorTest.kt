package com.multiplier.payable.engine.reconciler.diff

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.payableitem.PayableItem
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.time.LocalDate

class VasBgvInvoiceCollectionDiffCalculatorTest {

    private val diffCalculator = VasBgvInvoiceCollectionDiffCalculator()

    @Test
    fun `should return all items when no previously invoiced items exist`() {
        // Given
        val latestItems = listOf(
            createPayableItem(billId = "bill1", companyId = 1L),
            createPayableItem(billId = "bill2", companyId = 1L)
        )
        val invoicedItems = emptyList<PayableItem>()

        // When
        val diff = diffCalculator.calculate(latestItems, invoicedItems)

        // Then
        assertEquals(2, diff.toBeCreated.size)
        assertTrue(diff.toBeDeleted.isEmpty())
        assertTrue(diff.toBeUpdated.isEmpty())
        assertEquals(latestItems, diff.toBeCreated)
    }

    @Test
    fun `should filter out already invoiced items`() {
        // Given
        val latestItems = listOf(
            createPayableItem(billId = "bill1", companyId = 1L),
            createPayableItem(billId = "bill2", companyId = 1L),
            createPayableItem(billId = "bill3", companyId = 1L)
        )
        val invoicedItems = listOf(
            createPayableItem(billId = "bill1", companyId = 1L),
            createPayableItem(billId = "bill3", companyId = 1L)
        )

        // When
        val diff = diffCalculator.calculate(latestItems, invoicedItems)

        // Then
        assertEquals(1, diff.toBeCreated.size)
        assertEquals("bill2", diff.toBeCreated[0].billId)
        assertTrue(diff.toBeDeleted.isEmpty())
        assertTrue(diff.toBeUpdated.isEmpty())
    }

    @Test
    fun `should return empty when all items are already invoiced`() {
        // Given
        val latestItems = listOf(
            createPayableItem(billId = "bill1", companyId = 1L),
            createPayableItem(billId = "bill2", companyId = 1L)
        )
        val invoicedItems = listOf(
            createPayableItem(billId = "bill1", companyId = 1L),
            createPayableItem(billId = "bill2", companyId = 1L)
        )

        // When
        val diff = diffCalculator.calculate(latestItems, invoicedItems)

        // Then
        assertTrue(diff.toBeCreated.isEmpty())
        assertTrue(diff.toBeDeleted.isEmpty())
        assertTrue(diff.toBeUpdated.isEmpty())
    }

    @Test
    fun `should handle different company IDs correctly`() {
        // Given
        val latestItems = listOf(
            createPayableItem(billId = "bill1", companyId = 1L),
            createPayableItem(billId = "bill1", companyId = 2L)
        )
        val invoicedItems = listOf(
            createPayableItem(billId = "bill1", companyId = 1L)
        )

        // When
        val diff = diffCalculator.calculate(latestItems, invoicedItems)

        // Then
        assertEquals(1, diff.toBeCreated.size)
        assertEquals(2L, diff.toBeCreated[0].companyId)
        assertEquals("bill1", diff.toBeCreated[0].billId)
    }

    @Test
    fun `should handle different line item types correctly`() {
        // Given
        val latestItems = listOf(
            createPayableItem(billId = "bill1", companyId = 1L, lineItemType = LineItemType.STANDARD_BACKGROUND_VERIFICATION_CHECKS_FEE.name),
            createPayableItem(billId = "bill1", companyId = 1L, lineItemType = LineItemType.GROSS_SALARY.name)
        )
        val invoicedItems = listOf(
            createPayableItem(billId = "bill1", companyId = 1L, lineItemType = LineItemType.STANDARD_BACKGROUND_VERIFICATION_CHECKS_FEE.name)
        )

        // When
        val diff = diffCalculator.calculate(latestItems, invoicedItems)

        // Then
        assertEquals(1, diff.toBeCreated.size)
        assertEquals(LineItemType.GROSS_SALARY.name, diff.toBeCreated[0].lineItemType)
    }

    private fun createPayableItem(
        billId: String,
        companyId: Long,
        lineItemType: String = LineItemType.STANDARD_BACKGROUND_VERIFICATION_CHECKS_FEE.name
    ): PayableItem {
        return PayableItem(
            month = 1,
            year = 2024,
            lineItemType = lineItemType,
            companyId = companyId,
            amountInBaseCurrency = 100.0,
            baseCurrency = "USD",
            originalTimestamp = System.currentTimeMillis(),
            cycle = InvoiceCycle.MONTHLY,
            billId = billId,
            periodStartDate = LocalDate.of(2024, 1, 1),
            periodEndDate = LocalDate.of(2024, 1, 31)
        )
    }
}
