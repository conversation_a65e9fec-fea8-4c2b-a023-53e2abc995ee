package com.multiplier.payable.engine.reconciler.descriptionbuilder

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.InjectMocks
import org.mockito.junit.jupiter.MockitoExtension
import java.util.stream.Stream

@ExtendWith(MockitoExtension::class)
class AmountFormatterTest {

    @InjectMocks
    private lateinit var amountFormatter: AmountFormatter

    @ParameterizedTest
    @MethodSource
    fun givenAmount_whenFormat_thenReturnFormattedAmount(givenAmount: Double, expectedFormattedAmount: String) {
        // GIVEN
        val amount = givenAmount

        // WHEN
        val formattedAmount = amountFormatter.format(amount)

        // THEN
        assertThat(formattedAmount).isEqualTo(expectedFormattedAmount)
    }

    companion object {
        @JvmStatic
        fun givenAmount_whenFormat_thenReturnFormattedAmount(): Stream<Arguments> = Stream.of(
            Arguments.of(0.0, "0.00"),
            Arguments.of(4242.4242, "4,242.42"),
            Arguments.of(431.1, "431.10"),
        )
    }
}