package com.multiplier.payable.engine.reconciler.descriptionbuilder

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`

class BackgroundVerificationFeeDescriptionBuilderTest {

    private val amountFormatter = mock(AmountFormatter::class.java)
    private val builder = BackgroundVerificationFeeDescriptionBuilder(amountFormatter)

    @Test
    fun `lineItemType should return STANDARD_BACKGROUND_VERIFICATION_CHECKS_FEE`() {
        // When
        val result = builder.lineItemType

        // Then
        assertEquals(LineItemType.STANDARD_BACKGROUND_VERIFICATION_CHECKS_FEE, result)
    }

    @Test
    fun `build should return correct description format with contract ID`() {
        // Given
        val contractId = 12345L
        val currencyCode = "USD"
        val amount = 150.00
        val formattedAmount = "150.00"
        
        val context = PayableItemDescriptionBuilderContext(
            contractId = contractId,
            monthYear = MonthYear(2024, 6),
            currencyCode = currencyCode,
            amountInBaseCurrency = amount
        )

        `when`(amountFormatter.format(amount)).thenReturn(formattedAmount)

        // When
        val result = builder.build(context)

        // Then
        assertEquals("Resource Background Verification Fee for $contractId: $currencyCode $formattedAmount", result)
    }

    @Test
    fun `build should handle null contract ID`() {
        // Given
        val currencyCode = "EUR"
        val amount = 200.50
        val formattedAmount = "200.50"
        
        val context = PayableItemDescriptionBuilderContext(
            contractId = null,
            monthYear = MonthYear(2024, 6),
            currencyCode = currencyCode,
            amountInBaseCurrency = amount
        )

        `when`(amountFormatter.format(amount)).thenReturn(formattedAmount)

        // When
        val result = builder.build(context)

        // Then
        assertEquals("Resource Background Verification Fee for Unknown: $currencyCode $formattedAmount", result)
    }

    @Test
    fun `build should handle different currency codes and amounts`() {
        // Given
        val contractId = 98765L
        val currencyCode = "GBP"
        val amount = 75.25
        val formattedAmount = "75.25"
        
        val context = PayableItemDescriptionBuilderContext(
            contractId = contractId,
            monthYear = MonthYear(2024, 6),
            currencyCode = currencyCode,
            amountInBaseCurrency = amount
        )

        `when`(amountFormatter.format(amount)).thenReturn(formattedAmount)

        // When
        val result = builder.build(context)

        // Then
        assertEquals("Resource Background Verification Fee for $contractId: $currencyCode $formattedAmount", result)
    }
}
