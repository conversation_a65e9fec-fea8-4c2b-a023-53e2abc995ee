package com.multiplier.payable.engine.reconciler.descriptionbuilder

import com.multiplier.core.payable.adapters.api.LineItemType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class CanadaPayrollPayableItemDescriptionBuilderTest {

    @Test
    fun `getLineItemTypeFromDescription should return lineItemType when currentLineItemType is EOR_SALARY_DISBURSEMENT and description starts with prefix`() {
        // GIVEN
        val testBuilder = TestCanadaPayrollPayableItemDescriptionBuilder()
        val currentLineItemType = LineItemType.EOR_SALARY_DISBURSEMENT
        val description = "Test Prefix"

        // WHEN
        val result = testBuilder.getLineItemTypeFromDescription(currentLineItemType, description)

        // THEN
        assertThat(result).isEqualTo(LineItemType.CANADA_GROSS_WAGES)
    }

    @Test
    fun `getLineItemTypeFromDescription should return currentLineItemType when description does not start with prefix`() {
        // GIVEN
        val testBuilder = TestCanadaPayrollPayableItemDescriptionBuilder()
        val currentLineItemType = LineItemType.EOR_SALARY_DISBURSEMENT
        val description = "Different Prefix"

        // WHEN
        val result = testBuilder.getLineItemTypeFromDescription(currentLineItemType, description)

        // THEN
        assertThat(result).isEqualTo(currentLineItemType)
    }

    @Test
    fun `getLineItemTypeFromDescription should return currentLineItemType when currentLineItemType is not EOR_SALARY_DISBURSEMENT`() {
        // GIVEN
        val testBuilder = TestCanadaPayrollPayableItemDescriptionBuilder()
        val currentLineItemType = LineItemType.GROSS_SALARY
        val description = "Test Prefix"

        // WHEN
        val result = testBuilder.getLineItemTypeFromDescription(currentLineItemType, description)

        // THEN
        assertThat(result).isEqualTo(currentLineItemType)
    }

    private class TestCanadaPayrollPayableItemDescriptionBuilder: CanadaPayrollPayableItemDescriptionBuilder() {
        override val lineItemType = LineItemType.CANADA_GROSS_WAGES
        override fun getPrefix(): String = "Test Prefix"
    }
}
