package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaPayableItem
import com.multiplier.core.payable.repository.model.JpaPayableItemData
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.types.PayableItemType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import java.util.HashSet
import java.util.LinkedHashSet

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class FXRateRuleTest {

    @Mock
    private lateinit var featureFlagService: FeatureFlagService

    @Mock
    private lateinit var jpaAnomalyReportRepository: JpaAnomalyReportRepository

    @Mock
    private lateinit var mockCommand: InvoiceCommand

    private lateinit var fxRateRule: FXRateRule

    @BeforeEach
    fun setUp() {
        fxRateRule = FXRateRule(featureFlagService)
    }

    @Test
    fun `should return success when feature flag is disabled`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(false)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        // Use a simple empty request
        val request = InvoiceAnomalyDetectorRequest.builder().build()

        // When
        val result = fxRateRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Rule 'FXRate' is disabled by feature flag"))
    }

    @Test
    fun `should return success when request is invalid`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = InvoiceAnomalyDetectorRequest.builder().build() // Empty request

        // When
        val result = fxRateRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice data or company payable is null - validation skipped"))
    }

    @Test
    fun `should return success when FX rates match`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = createMockRequest(
            billingCurrency = CurrencyCode.USD,
            lineItemCurrency = CurrencyCode.USD,
            lineItemDescription = "Test Item",
            lineItemAmount = 100.0,
            payableItemAmount = 100.0
        )

        // When
        val result = fxRateRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("FX rates are correct"))
    }

    @Test
    fun `should return success when currencies don't match`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = createMockRequest(
            billingCurrency = CurrencyCode.USD,
            lineItemCurrency = CurrencyCode.EUR,
            lineItemDescription = "Test Item",
            lineItemAmount = 100.0,
            payableItemAmount = 120.0
        )

        // When
        val result = fxRateRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("FX rates are correct"))
    }

    @Test
    fun `should return failure when FX rates don't match`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = createMockRequest(
            billingCurrency = CurrencyCode.USD,
            lineItemCurrency = CurrencyCode.USD,
            lineItemDescription = "Test Item",
            lineItemAmount = 100.0,
            payableItemAmount = 120.0
        )

        // When
        val result = fxRateRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertEquals("Fx rate used is not 1 for 120.0 to 100.0", result.messages[0])
    }

    @Test
    fun `should handle management fee correctly`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val contractId = 12345L
        val request = createMockRequestWithManagementFee(
            billingCurrency = CurrencyCode.USD,
            lineItemCurrency = CurrencyCode.USD,
            lineItemDescription = "Management Fee for contract $contractId",
            lineItemAmount = 100.0,
            payableItemAmount = 100.0,
            contractId = contractId
        )

        // When
        val result = fxRateRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("FX rates are correct"))
    }

    @Test
    fun `should return correct rule type`() {
        // When
        val ruleType = fxRateRule.type

        // Then
        assertEquals(DetectionRuleType.FX_RATE, ruleType)
    }

    @Test
    fun `should return correct rule name`() {
        // When
        val ruleName = fxRateRule.ruleName

        // Then
        assertEquals("FXRate", ruleName)
    }

    @Test
    fun `should return correct feature flag name`() {
        // When
        val featureFlagName = fxRateRule.featureFlagName

        // Then
        assertEquals("ENABLE_FX_RATE_CHECK", featureFlagName)
    }

    private fun createMockRequest(
        billingCurrency: CurrencyCode,
        lineItemCurrency: CurrencyCode,
        lineItemDescription: String,
        lineItemAmount: Double,
        payableItemAmount: Double
    ): InvoiceAnomalyDetectorRequest {
        // Create line item DTO
        val lineItemDTO = Mockito.mock(LineItemDTO::class.java)
        Mockito.`when`(lineItemDTO.description).thenReturn(lineItemDescription)
        Mockito.`when`(lineItemDTO.unitAmount).thenReturn(lineItemAmount)

        // Create invoice DTO
        val invoiceDTO = Mockito.mock(InvoiceDTO::class.java)
        Mockito.`when`(invoiceDTO.billingCurrencyCode).thenReturn(billingCurrency)
        Mockito.`when`(invoiceDTO.lineItems).thenReturn(listOf(lineItemDTO))

        // Create payable item
        val payableItem = Mockito.mock(JpaPayableItem::class.java)
        Mockito.`when`(payableItem.description).thenReturn(lineItemDescription)
        Mockito.`when`(payableItem.currencyCode).thenReturn(lineItemCurrency)
        Mockito.`when`(payableItem.totalCost).thenReturn(payableItemAmount)
        Mockito.`when`(payableItem.type).thenReturn(PayableItemType.MEMBER_PAYROLL_COST)

        // Create company payable
        val companyPayable = Mockito.mock(JpaCompanyPayable::class.java)
        val itemsSet = LinkedHashSet<JpaPayableItem>()
        itemsSet.add(payableItem)
        Mockito.`when`(companyPayable.items).thenReturn(itemsSet)

        return InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(invoiceDTO)
            .payable(companyPayable)
            .build()
    }

    private fun createMockRequestWithManagementFee(
        billingCurrency: CurrencyCode,
        lineItemCurrency: CurrencyCode,
        lineItemDescription: String,
        lineItemAmount: Double,
        payableItemAmount: Double,
        contractId: Long
    ): InvoiceAnomalyDetectorRequest {
        // Create line item DTO
        val lineItemDTO = Mockito.mock(LineItemDTO::class.java)
        Mockito.`when`(lineItemDTO.description).thenReturn(lineItemDescription)
        Mockito.`when`(lineItemDTO.unitAmount).thenReturn(lineItemAmount)

        // Create invoice DTO
        val invoiceDTO = Mockito.mock(InvoiceDTO::class.java)
        Mockito.`when`(invoiceDTO.billingCurrencyCode).thenReturn(billingCurrency)
        Mockito.`when`(invoiceDTO.lineItems).thenReturn(listOf(lineItemDTO))

        // Create payable item data
        val payableItemData = Mockito.mock(JpaPayableItemData::class.java)
        Mockito.`when`(payableItemData.contractId).thenReturn(contractId)

        // Create payable item
        val payableItem = Mockito.mock(JpaPayableItem::class.java)
        Mockito.`when`(payableItem.description).thenReturn("Some other description")
        Mockito.`when`(payableItem.currencyCode).thenReturn(lineItemCurrency)
        Mockito.`when`(payableItem.totalCost).thenReturn(payableItemAmount)
        Mockito.`when`(payableItem.type).thenReturn(PayableItemType.MEMBER_MANAGEMENT_FEE)

        val itemDataSet = HashSet<JpaPayableItemData>()
        itemDataSet.add(payableItemData)
        Mockito.`when`(payableItem.itemData).thenReturn(itemDataSet)

        // Create company payable
        val companyPayable = Mockito.mock(JpaCompanyPayable::class.java)
        val itemsSet = LinkedHashSet<JpaPayableItem>()
        itemsSet.add(payableItem)
        Mockito.`when`(companyPayable.items).thenReturn(itemsSet)

        return InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(invoiceDTO)
            .payable(companyPayable)
            .build()
    }
}
