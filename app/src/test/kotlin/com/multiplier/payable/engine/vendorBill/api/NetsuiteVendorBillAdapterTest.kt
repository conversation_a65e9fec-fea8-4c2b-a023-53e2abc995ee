package com.multiplier.payable.engine.vendorBill.api

import com.multiplier.core.payable.adapters.netsuite.dto.VendorBillDTO
import com.multiplier.core.payable.adapters.netsuite.exception.NetsuiteAdapterException
import com.multiplier.core.payable.adapters.netsuite.mapper.NetsuiteWsVendorBillMapper
import com.multiplier.core.payable.adapters.netsuite.webservices.NetsuiteWebserviceClient
import com.multiplier.core.payable.adapters.netsuite.webservices.NetsuiteWebserviceException
import com.multiplier.core.payable.creditnote.api.ApiResponseParser
import com.multiplier.payable.engine.currency.CurrencyCode
import com.netsuite.suitetalk.proxy.v2023_1.platform.messages.WriteResponse
import com.netsuite.suitetalk.proxy.v2023_1.transactions.purchases.VendorBill
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import java.util.UUID

@DisplayName("NetsuiteVendorBillAdapter Tests")
@ExtendWith(MockitoExtension::class)
class NetsuiteVendorBillAdapterTest {

    // Mock dependencies
    private lateinit var netsuiteWebserviceClient: NetsuiteWebserviceClient
    private lateinit var apiResponseParser: ApiResponseParser
    private lateinit var netsuiteWsVendorBillMapper: NetsuiteWsVendorBillMapper
    
    // Class under test
    private lateinit var adapter: NetsuiteVendorBillAdapter
    
    @BeforeEach
    fun setUp() {
        netsuiteWebserviceClient = mockk()
        apiResponseParser = mockk()
        netsuiteWsVendorBillMapper = mockk()
        
        adapter = NetsuiteVendorBillAdapter(
            netsuiteWebserviceClient = netsuiteWebserviceClient,
            apiResponseParser = apiResponseParser,
            netsuiteWsVendorBillMapper = netsuiteWsVendorBillMapper
        )
    }
    
    @Nested
    @DisplayName("create method tests")
    inner class CreateMethodTests {
        
        @Test
        @DisplayName("should successfully create a vendor bill")
        fun shouldSuccessfullyCreateVendorBill() {
            // Arrange
            val vendorBillDTO = createSampleVendorBillDTO()
            val vendorBill = mockk<VendorBill>()
            val writeResponse = mockk<WriteResponse>()
            val internalId = "NS_123456"
            
            every { netsuiteWsVendorBillMapper.map(vendorBillDTO) } returns vendorBill
            every { netsuiteWebserviceClient.createVendorBill(vendorBill) } returns writeResponse
            every { apiResponseParser.getInternalId(writeResponse) } returns internalId
            
            // Act
            val result = adapter.create(vendorBillDTO)
            
            // Assert
            assertEquals(internalId, result.id)
            assertEquals(vendorBillDTO.transactionId, result.tranId)
            
            // Verify
            verify { netsuiteWsVendorBillMapper.map(vendorBillDTO) }
            verify { netsuiteWebserviceClient.createVendorBill(vendorBill) }
            verify { apiResponseParser.getInternalId(writeResponse) }
        }
        
        @Test
        @DisplayName("should throw NetsuiteAdapterException when NetsuiteWebserviceException occurs")
        fun shouldThrowNetsuiteAdapterExceptionWhenWebserviceExceptionOccurs() {
            // Arrange
            val vendorBillDTO = createSampleVendorBillDTO()
            val vendorBill = mockk<VendorBill>()
            val errorMessage = "Failed to create vendor bill in NetSuite"
            
            every { netsuiteWsVendorBillMapper.map(vendorBillDTO) } returns vendorBill
            every { netsuiteWebserviceClient.createVendorBill(vendorBill) } throws 
                NetsuiteWebserviceException(errorMessage)
            
            // Act & Assert
            val exception = assertThrows<NetsuiteAdapterException> {
                adapter.create(vendorBillDTO)
            }
            
            assertEquals(errorMessage, exception.message)
            
            // Verify
            verify { netsuiteWsVendorBillMapper.map(vendorBillDTO) }
            verify { netsuiteWebserviceClient.createVendorBill(vendorBill) }
            verify(exactly = 0) { apiResponseParser.getInternalId(any()) }
        }
    }
    
    @Nested
    @DisplayName("update method tests")
    inner class UpdateMethodTests {
        
        @Test
        @DisplayName("should successfully update a vendor bill")
        fun shouldSuccessfullyUpdateVendorBill() {
            // Arrange
            val vendorBillDTO = createSampleVendorBillDTO()
            val vendorBill = mockk<VendorBill>()
            val writeResponse = mockk<WriteResponse>()
            val internalId = "NS_123456"
            
            every { netsuiteWsVendorBillMapper.mapForUpdate(vendorBillDTO) } returns vendorBill
            every { netsuiteWebserviceClient.updateVendorBill(vendorBill) } returns writeResponse
            every { apiResponseParser.getInternalId(writeResponse) } returns internalId
            
            // Act
            val result = adapter.update(vendorBillDTO)
            
            // Assert
            assertEquals(internalId, result.id)
            assertEquals(vendorBillDTO.transactionId, result.tranId)
            
            // Verify
            verify { netsuiteWsVendorBillMapper.mapForUpdate(vendorBillDTO) }
            verify { netsuiteWebserviceClient.updateVendorBill(vendorBill) }
            verify { apiResponseParser.getInternalId(writeResponse) }
        }
        
        @Test
        @DisplayName("should throw NetsuiteAdapterException when NetsuiteWebserviceException occurs")
        fun shouldThrowNetsuiteAdapterExceptionWhenWebserviceExceptionOccurs() {
            // Arrange
            val vendorBillDTO = createSampleVendorBillDTO()
            val vendorBill = mockk<VendorBill>()
            val errorMessage = "Failed to update vendor bill in NetSuite"
            
            every { netsuiteWsVendorBillMapper.mapForUpdate(vendorBillDTO) } returns vendorBill
            every { netsuiteWebserviceClient.updateVendorBill(vendorBill) } throws 
                NetsuiteWebserviceException(errorMessage)
            
            // Act & Assert
            val exception = assertThrows<NetsuiteAdapterException> {
                adapter.update(vendorBillDTO)
            }
            
            assertEquals(errorMessage, exception.message)
            
            // Verify
            verify { netsuiteWsVendorBillMapper.mapForUpdate(vendorBillDTO) }
            verify { netsuiteWebserviceClient.updateVendorBill(vendorBill) }
            verify(exactly = 0) { apiResponseParser.getInternalId(any()) }
        }
    }
    
    @Nested
    @DisplayName("delete method tests")
    inner class DeleteMethodTests {
        
        @Test
        @DisplayName("should successfully delete a vendor bill")
        fun shouldSuccessfullyDeleteVendorBill() {
            // Arrange
            val vendorBillId = "NS_123456"
            val writeResponse = mockk<WriteResponse>()

            every { netsuiteWebserviceClient.deleteVendorBill(vendorBillId) } returns writeResponse

            // Act
            adapter.delete(vendorBillId)
            
            // Verify
            verify { netsuiteWebserviceClient.deleteVendorBill(vendorBillId) }
        }
        
        @Test
        @DisplayName("should throw NetsuiteAdapterException when NetsuiteWebserviceException occurs")
        fun shouldThrowNetsuiteAdapterExceptionWhenWebserviceExceptionOccurs() {
            // Arrange
            val vendorBillId = "NS_123456"
            val errorMessage = "Failed to delete vendor bill in NetSuite"
            
            every { netsuiteWebserviceClient.deleteVendorBill(vendorBillId) } throws 
                NetsuiteWebserviceException(errorMessage)
            
            // Act & Assert
            val exception = assertThrows<NetsuiteAdapterException> {
                adapter.delete(vendorBillId)
            }
            
            assertEquals(errorMessage, exception.message)
            
            // Verify
            verify { netsuiteWebserviceClient.deleteVendorBill(vendorBillId) }
        }
    }
    
    // Helper methods
    private fun createSampleVendorBillDTO(): VendorBillDTO {
        return VendorBillDTO().apply {
            transactionId = UUID.randomUUID().toString()
            externalId = "EXT_" + UUID.randomUUID().toString()
            vendorId = "VENDOR_123"
            accountId = "ACCOUNT_456"
            departmentId = "DEPT_789"
            locationId = "LOC_012"
            currency = CurrencyCode.USD
            reference = "Test Vendor Bill"
            countryCode = "US"
            items = emptyList()
        }
    }
}
