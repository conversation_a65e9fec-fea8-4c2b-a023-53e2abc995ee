package com.multiplier.payable.engine.offcycle

import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.reconciler.diff.OffCycleDiffCalculator
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
class OffCycleDiffCalculatorTest {

    private val diffCalculator = OffCycleDiffCalculator()

    @Test
    fun `calculate should return items to be created when payroll cycle ids are different`() {
        // GIVEN
        val newItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val newItem2 = mockk<PayableItem> {
            every { payrollCycleId } returns 2L
        }
        val newItems = listOf(newItem1, newItem2)

        val oldItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 3L
        }
        val oldItems = listOf(oldItem1)

        // WHEN
        val result = diffCalculator.calculate(newItems, oldItems)

        // THEN
        assertEquals(0, result.toBeDeleted.size)
        assertEquals(0, result.toBeUpdated.size)
        assertEquals(2, result.toBeCreated.size)
        assertEquals(newItems, result.toBeCreated)
    }

    @Test
    fun `calculate should filter out items with existing payroll cycle ids`() {
        // GIVEN
        val newItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val newItem2 = mockk<PayableItem> {
            every { payrollCycleId } returns 2L
        }
        val newItem3 = mockk<PayableItem> {
            every { payrollCycleId } returns 3L
        }
        val newItems = listOf(newItem1, newItem2, newItem3)

        val oldItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val oldItem2 = mockk<PayableItem> {
            every { payrollCycleId } returns 3L
        }
        val oldItems = listOf(oldItem1, oldItem2)

        // WHEN
        val result = diffCalculator.calculate(newItems, oldItems)

        // THEN
        assertEquals(0, result.toBeDeleted.size)
        assertEquals(0, result.toBeUpdated.size)
        assertEquals(1, result.toBeCreated.size)
        assertEquals(newItem2, result.toBeCreated[0])
    }

    @Test
    fun `calculate should return empty toBeCreated when all payroll cycle ids already exist`() {
        // GIVEN
        val newItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val newItem2 = mockk<PayableItem> {
            every { payrollCycleId } returns 2L
        }
        val newItems = listOf(newItem1, newItem2)

        val oldItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val oldItem2 = mockk<PayableItem> {
            every { payrollCycleId } returns 2L
        }
        val oldItems = listOf(oldItem1, oldItem2)

        // WHEN
        val result = diffCalculator.calculate(newItems, oldItems)

        // THEN
        assertEquals(0, result.toBeDeleted.size)
        assertEquals(0, result.toBeUpdated.size)
        assertEquals(0, result.toBeCreated.size)
    }

    @Test
    fun `calculate should handle empty new items list`() {
        // GIVEN
        val newItems = emptyList<PayableItem>()

        val oldItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val oldItems = listOf(oldItem1)

        // WHEN
        val result = diffCalculator.calculate(newItems, oldItems)

        // THEN
        assertEquals(0, result.toBeDeleted.size)
        assertEquals(0, result.toBeUpdated.size)
        assertEquals(0, result.toBeCreated.size)
    }

    @Test
    fun `calculate should handle empty old items list`() {
        // GIVEN
        val newItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val newItem2 = mockk<PayableItem> {
            every { payrollCycleId } returns 2L
        }
        val newItems = listOf(newItem1, newItem2)

        val oldItems = emptyList<PayableItem>()

        // WHEN
        val result = diffCalculator.calculate(newItems, oldItems)

        // THEN
        assertEquals(0, result.toBeDeleted.size)
        assertEquals(0, result.toBeUpdated.size)
        assertEquals(2, result.toBeCreated.size)
        assertEquals(newItems, result.toBeCreated)
    }

    @Test
    fun `calculate should handle both empty lists`() {
        // GIVEN
        val newItems = emptyList<PayableItem>()
        val oldItems = emptyList<PayableItem>()

        // WHEN
        val result = diffCalculator.calculate(newItems, oldItems)

        // THEN
        assertEquals(0, result.toBeDeleted.size)
        assertEquals(0, result.toBeUpdated.size)
        assertEquals(0, result.toBeCreated.size)
    }

    @Test
    fun `calculate should handle null payroll cycle ids`() {
        // GIVEN
        val newItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns null
        }
        val newItem2 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val newItems = listOf(newItem1, newItem2)

        val oldItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns null
        }
        val oldItems = listOf(oldItem1)

        // WHEN
        val result = diffCalculator.calculate(newItems, oldItems)

        // THEN
        assertEquals(0, result.toBeDeleted.size)
        assertEquals(0, result.toBeUpdated.size)
        assertEquals(1, result.toBeCreated.size)
        assertEquals(newItem2, result.toBeCreated[0])
    }

    @Test
    fun `calculate should always return empty toBeDeleted and toBeUpdated lists`() {
        // GIVEN
        val newItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val newItems = listOf(newItem1)

        val oldItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 2L
        }
        val oldItems = listOf(oldItem1)

        // WHEN
        val result = diffCalculator.calculate(newItems, oldItems)

        // THEN
        assertTrue(result.toBeDeleted.isEmpty())
        assertTrue(result.toBeUpdated.isEmpty())
        assertEquals(1, result.toBeCreated.size)
    }

    @Test
    fun `calculate should handle duplicate payroll cycle ids in new items`() {
        // GIVEN
        val newItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val newItem2 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val newItem3 = mockk<PayableItem> {
            every { payrollCycleId } returns 2L
        }
        val newItems = listOf(newItem1, newItem2, newItem3)

        val oldItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val oldItems = listOf(oldItem1)

        // WHEN
        val result = diffCalculator.calculate(newItems, oldItems)

        // THEN
        assertEquals(0, result.toBeDeleted.size)
        assertEquals(0, result.toBeUpdated.size)
        assertEquals(1, result.toBeCreated.size)
        assertEquals(newItem3, result.toBeCreated[0])
    }

    @Test
    fun `calculate should handle duplicate payroll cycle ids in old items`() {
        // GIVEN
        val newItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val newItem2 = mockk<PayableItem> {
            every { payrollCycleId } returns 2L
        }
        val newItems = listOf(newItem1, newItem2)

        val oldItem1 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val oldItem2 = mockk<PayableItem> {
            every { payrollCycleId } returns 1L
        }
        val oldItems = listOf(oldItem1, oldItem2)

        // WHEN
        val result = diffCalculator.calculate(newItems, oldItems)

        // THEN
        assertEquals(0, result.toBeDeleted.size)
        assertEquals(0, result.toBeUpdated.size)
        assertEquals(1, result.toBeCreated.size)
        assertEquals(newItem2, result.toBeCreated[0])
    }
}
