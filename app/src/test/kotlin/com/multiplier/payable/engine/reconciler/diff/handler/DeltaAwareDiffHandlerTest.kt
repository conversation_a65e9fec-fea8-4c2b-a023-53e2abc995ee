package com.multiplier.payable.engine.reconciler.diff.handler

import com.multiplier.payable.engine.reconciler.diff.Diff
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify

class DeltaAwareDiffHandlerTest {

    private val addHandler: AddHandler = mock()
    private val updateHandler: UpdateHandler = mock()
    private val deleteHandler: DeleteHandler = mock()
    private val deltaAwareDiffHandler = DeltaAwareDiffHandler(
        addHandler = addHandler,
        updateHandler = updateHandler,
        deleteHandler = deleteHandler
    )

    @Test
    fun handle() {
        //given
        val context = DefaultDiffHandlerContext(
            diff = Diff(
                toBeDeleted = emptyList(),
                toBeUpdated = emptyList(),
                toBeCreated = emptyList()
            )
        )
        //when
        deltaAwareDiffHandler.handle(context)

        //then
        verify(addHandler).handle(context)
        verify(updateHandler).handle(context)
        verify(deleteHandler).handle(context)
    }
}