package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.invoice.api.InvoiceAdapter
import com.multiplier.payable.engine.anomalydetector.provider.NetsuiteAlreadyBilledAmountService
import com.multiplier.payable.engine.anomalydetector.provider.NetsuiteInvoiceProvider
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaInvoice
import com.multiplier.payable.types.CompanyPayableType
import com.multiplier.payable.types.PayableStatus
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class AmountAlreadyBilledMismatchPayrollCostRuleTest {

    private lateinit var featureFlagService: FeatureFlagService
    private lateinit var jpaAnomalyReportRepository: JpaAnomalyReportRepository
    private lateinit var netsuiteAlreadyBilledAmountService: NetsuiteAlreadyBilledAmountService
    private lateinit var netsuiteInvoiceProvider: NetsuiteInvoiceProvider
    private lateinit var invoiceAdapter: InvoiceAdapter
    private lateinit var jpaCompanyPayableRepository: JpaCompanyPayableRepository
    private lateinit var rule: AmountAlreadyBilledMismatchPayrollCostRule
    private lateinit var mockCommand: InvoiceCommand

    @BeforeEach
    fun setUp() {
        featureFlagService = mockk()
        jpaAnomalyReportRepository = mockk()
        netsuiteAlreadyBilledAmountService = mockk()
        netsuiteInvoiceProvider = mockk()
        invoiceAdapter = mockk()
        jpaCompanyPayableRepository = mockk()

        // Mock feature flag service to return true (enabled)
        val mockGbFeatureResult = mockk<GBFeatureResult> {
            every { on } returns true
        }
        every { featureFlagService.feature("ENABLE_AMOUNT_ALREADY_BILLED_MISMATCH_PAYROLL_COST_CHECK", mapOf()) } returns mockGbFeatureResult

        // Mock anomaly report repository save operations
        every { jpaAnomalyReportRepository.save(any()) } returns mockk()

        rule = AmountAlreadyBilledMismatchPayrollCostRule(
            featureFlagService,
            netsuiteAlreadyBilledAmountService
        )

        mockCommand = mockk<InvoiceCommand>().apply {
            every { companyId } returns 123L
        }
    }

    @Test
    fun `should return correct rule properties`() {
        assertEquals(DetectionRuleType.AMOUNT_ALREADY_BILLED_MISMATCH_PAYROLL_COST, rule.type)
        assertEquals(InvoiceAnomalyDetectorType.AMOUNT_ALREADY_BILLED_MISMATCH_PAYROLL_COST, rule.detectorType)
        assertEquals("AmountAlreadyBilledMismatchPayrollCost", rule.ruleName)
        assertEquals("ENABLE_AMOUNT_ALREADY_BILLED_MISMATCH_PAYROLL_COST_CHECK", rule.featureFlagName)
    }

    @Test
    fun `should return success when no billed payroll cost line items found`() {
        // Given
        val invoiceDTO = createInvoiceDTO(emptyList())
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("No BILLED_GROSS_SALARY or BILLED_GROSS_SALARY_SUPPLEMENTARY line items found - validation skipped"))
    }

    @Test
    fun `should return success when amounts match exactly`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_GROSS_SALARY, 1000.0, 123L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // Mock NetSuite data to return matching amount
        mockNetsuiteData(1000.0)

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Amount validation passed - all 1 billed payroll cost amounts match NetSuite first invoice amounts"))
    }

    @Test
    fun `should return failure when amounts do not match`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_GROSS_SALARY, 1000.0, 123L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // Mock NetSuite data to return different amount
        mockNetsuiteData(1500.0)

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("System's already billed payroll cost amount 1000.00 USD does not match NetSuite's first invoice amount 1500.00 USD for contract 123"))
    }

    @Test
    fun `should handle both BILLED_GROSS_SALARY and BILLED_GROSS_SALARY_SUPPLEMENTARY line items`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_GROSS_SALARY, 1000.0, 123L),
            createLineItemDTO(LineItemType.BILLED_GROSS_SALARY_SUPPLEMENTARY, 500.0, 456L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // Mock NetSuite data to return matching amounts
        every {
            netsuiteAlreadyBilledAmountService.getNetsuiteAlreadyBilledAmount(
                any(), 123L, 2024, 3, LineItemType.GROSS_SALARY
            )
        } returns 1000.0
        every {
            netsuiteAlreadyBilledAmountService.getNetsuiteAlreadyBilledAmount(
                any(), 456L, 2024, 3, LineItemType.GROSS_SALARY
            )
        } returns 500.0

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Amount validation passed - all 2 billed payroll cost amounts match NetSuite first invoice amounts"))
    }

    @Test
    fun `should skip line items with null contract ID`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_GROSS_SALARY, 1000.0, null)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Amount validation passed - all 1 billed payroll cost amounts match NetSuite first invoice amounts"))
    }

    @Test
    fun `should skip line items with null unit amount`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_GROSS_SALARY, null, 123L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Amount validation passed - all 1 billed payroll cost amounts match NetSuite first invoice amounts"))
    }

    @Test
    fun `should handle exception gracefully`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_GROSS_SALARY, 1000.0, 123L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // Mock to return null (which is what the service does when exceptions occur internally)
        every {
            netsuiteAlreadyBilledAmountService.getNetsuiteAlreadyBilledAmount(
                any(), any(), any(), any(), any()
            )
        } returns null

        // When
        val result = rule.detect(mockCommand, request)

        // Then - Rule should gracefully handle null returns by skipping validation
        // When NetSuite data is not available (null), validation is skipped for problematic line items
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Amount validation passed - all 1 billed payroll cost amounts match NetSuite first invoice amounts"))
    }

    private fun createInvoiceDTO(lineItems: List<LineItemDTO>): InvoiceDTO {
        return InvoiceDTO.builder()
            .billingCurrencyCode(CurrencyCode.USD)
            .lineItems(lineItems)
            .build()
    }

    private fun createLineItemDTO(itemType: LineItemType, unitAmount: Double?, contractId: Long?): LineItemDTO {
        return LineItemDTO.builder()
            .itemType(itemType)
            .unitAmount(unitAmount)
            .contractId(contractId)
            .build()
    }

    private fun createCompanyPayable(): JpaCompanyPayable {
        return mockk<JpaCompanyPayable>().apply {
            every { year } returns 2024
            every { month } returns 3
            every { id } returns 1L  // Add missing ID mock
        }
    }

    private fun createRequest(invoiceDTO: InvoiceDTO, companyPayable: JpaCompanyPayable): InvoiceAnomalyDetectorRequest {
        return InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(invoiceDTO)
            .payable(companyPayable)
            .build()
    }

    private fun mockNetsuiteData(expectedAmount: Double) {
        every {
            netsuiteAlreadyBilledAmountService.getNetsuiteAlreadyBilledAmount(
                any(), 123L, 2024, 3, LineItemType.GROSS_SALARY
            )
        } returns expectedAmount
    }

    private fun createFirstInvoicePayable(): JpaCompanyPayable {
        return mockk<JpaCompanyPayable>().apply {
            every { status } returns PayableStatus.AUTHORIZED
            every { type } returns CompanyPayableType.FIRST_INVOICE
            every { invoice } returns createJpaInvoice()
            every { id } returns 2L  // Add missing ID mock
        }
    }

    private fun createJpaInvoice(): JpaInvoice {
        return mockk<JpaInvoice>().apply {
            every { externalId } returns "NS123"
        }
    }

    private fun createNetsuiteInvoice(): JpaInvoice {
        return mockk<JpaInvoice>().apply {
            every { externalId } returns "NS123"
        }
    }

    private fun createNetsuiteInvoiceDTO(lineItems: List<LineItemDTO>): InvoiceDTO {
        return InvoiceDTO.builder()
            .externalId("NS123")
            .lineItems(lineItems)
            .build()
    }
}
