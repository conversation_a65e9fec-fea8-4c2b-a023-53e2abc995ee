package com.multiplier.payable.engine.reconciler.companypayable.storage

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.payable.engine.fx.FxConverter
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.reconciler.companypayable.builder.CompanyPayableBuilderContext
import com.multiplier.payable.engine.reconciler.companypayable.builder.CompanyPayableEntityBuilder
import com.multiplier.payable.engine.reconciler.companypayable.collector.CompanyPayableEntityDataCollector
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.PayableStatus
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.mock

@ExtendWith(MockKExtension::class)
class FreelancerInvoiceCompanyPayableStorageTest {
    @MockK
    private lateinit var entityBuilder: CompanyPayableEntityBuilder

    @MockK
    private lateinit var entityDataCollector: CompanyPayableEntityDataCollector

    @MockK
    private lateinit var payableRepository: JpaCompanyPayableRepository

    @MockK
    private lateinit var fxConverter: FxConverter

    @InjectMockKs
    private lateinit var companyPayableStorage: FreelancerInvoiceCompanyPayableStorage

    @Test
    fun exchangeAndStore() {
        val payableItems = mutableListOf<PayableItem>()
        val context = mockk<CompanyPayableStorageContext>(){
            every { items } returns payableItems
        }
        val entity = mockk<JpaCompanyPayable>()
        val entityBuilderContext =
            CompanyPayableBuilderContext(
                company = mock(),
                companyCountryCode = CountryCode.SGP,
                contracts = mock(),
                countries = mock(),
                members = mock(),
                storeContext = mock(),
                taxIdentifierMap = mock(),
                taxTypeMap = mock(),
            )

        every { fxConverter.convertWithOnDemandToppedUpRate(context.items) } returns payableItems
        every { context.copy(items = payableItems) } returns context
        every { entityDataCollector.collect(context) } returns entityBuilderContext
        every { entityBuilder.build(entityBuilderContext) } returns entity
        val statusSlot = slot<PayableStatus>()
        every { entity.status = capture(statusSlot) } just Runs

        val companyPayableId = 42L
        val jpaCompanyPayable =
            JpaCompanyPayable.builder()
                .id(companyPayableId)
                .build()
        every { payableRepository.save(entity) } returns jpaCompanyPayable
        val savedCompanyPayableId = companyPayableStorage.exchangeAndStore(context)

        assertThat(savedCompanyPayableId).isEqualTo(companyPayableId)
        verify { fxConverter.convertWithOnDemandToppedUpRate(context.items) }
        verify { entityDataCollector.collect(context) }
        verify { entityBuilder.build(entityBuilderContext) }
        verify { payableRepository.save(entity) }

        verify { entity.status = PayableStatus.AUTHORIZED }
    }
}