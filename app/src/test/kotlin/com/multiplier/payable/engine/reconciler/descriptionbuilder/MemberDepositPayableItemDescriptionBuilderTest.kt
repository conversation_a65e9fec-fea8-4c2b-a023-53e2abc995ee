package com.multiplier.payable.engine.reconciler.descriptionbuilder

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.deposit.Deposit
import com.multiplier.payable.engine.deposit.DepositPayableItemDescriptionBuilderContext
import com.multiplier.payable.engine.deposit.DepositType
import com.multiplier.payable.engine.payableitem.deposit.description.builder.DepositPayableItemDescriptionBuilder
import com.multiplier.payable.engine.payableitem.deposit.description.builder.DepositPayableItemDescriptionBuilderContextBuilder
import com.multiplier.payable.engine.payableitem.deposit.description.builder.DepositPayableItemDescriptionBuilderFactory
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class MemberDepositPayableItemDescriptionBuilderTest {
    @Mock
    private lateinit var depositPayableItemDescriptionBuilderFactory: DepositPayableItemDescriptionBuilderFactory

    @Mock
    private lateinit var depositPayableItemDescriptionBuilderContextBuilder: DepositPayableItemDescriptionBuilderContextBuilder

    @InjectMocks
    private lateinit var memberDepositPayableItemDescriptionBuilder: MemberDepositPayableItemDescriptionBuilder

    @Test
    fun whenGetLineItemType_thenReturnLineItemType() {
        // WHEN
        val lineItemType = memberDepositPayableItemDescriptionBuilder.lineItemType

        // THEN
        assertThat(lineItemType).isEqualTo(LineItemType.MEMBER_DEPOSIT)
    }

    @Test
    fun givenContext_whenBuild_thenReturnPlaceholderDescription() {
        // GIVEN
        val deposit = mock<Deposit>()
        val depositDescriptionBuilder = mock<DepositPayableItemDescriptionBuilder>()
        val depositDescriptionContext = mock<DepositPayableItemDescriptionBuilderContext>()
        val context =
            PayableItemDescriptionBuilderContext(
                contractId = 1L,
                companyId = 10L,
                monthYear = mock(),
                countryName = "anyCountry",
                currencyCode = CurrencyCode.USD.name,
                amountInBaseCurrency = 0.0,
                deposit = deposit,
            )

        val expectedDescription = "Placeholder description for reconciliation flow"

        whenever(deposit.type).thenReturn(DepositType.SALARY)
        whenever(depositPayableItemDescriptionBuilderFactory.get(DepositType.SALARY)).thenReturn(depositDescriptionBuilder)
        whenever(depositPayableItemDescriptionBuilderContextBuilder.build(1L, 10L, deposit)).thenReturn(depositDescriptionContext)
        whenever(depositDescriptionBuilder.build(depositDescriptionContext)).thenReturn(expectedDescription)

        // WHEN
        val description = memberDepositPayableItemDescriptionBuilder.build(context)

        // THEN
        assertThat(description).isEqualTo(expectedDescription)
    }
}
