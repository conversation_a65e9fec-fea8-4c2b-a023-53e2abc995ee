package com.multiplier.payable.engine.memberpayable.managmentfee

import com.multiplier.core.payable.adapters.memberpayable.MemberPayableAdapter
import com.multiplier.core.payable.pricing.adapter.PricingServiceAdapter
import com.multiplier.payable.engine.memberpayable.MemberPayable
import com.multiplier.payable.engine.memberpayable.MemberPayableType
import com.multiplier.payable.types.Amount
import com.multiplier.payable.types.ContractType
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.doReturn
import java.time.Instant
import kotlin.test.assertIs

@ExtendWith(MockitoExtension::class)
class PricingMemberPayableManagementFeeCalculatorTest {

    @Mock
    private lateinit var pricingServiceAdapter: PricingServiceAdapter

    @Mock
    private lateinit var memberPayableAdapter: MemberPayableAdapter

    @InjectMocks
    private lateinit var pricingMemberPayableManagementFeeCalculator: PricingMemberPayableManagementFeeCalculator

    private val companyId = 1234L
    private val contractId = 100L
    private val externalId = 400L
    private val invoicingStartDate = Instant.now()
    private val invoicingEndDate = Instant.now()

    @Test
    fun `when pricing is not present and previous paid member payable is present, return zero`() {
        val memberPayable = getMemberPayable()
        val expectedAmount = Amount(0.00, CurrencyCode.USD)

        doReturn(0.00).`when`(pricingServiceAdapter).getManagementFeeByCountryAndContractType(
            memberPayable.companyId,
            memberPayable.countryCode,
            ContractType.valueOf(memberPayable.contract.contractType.name),
        )

        doReturn(true).`when`(memberPayableAdapter).isPaidMemberPayableExistsForMonthAndContractId(
            memberPayable.submittedAt,
            memberPayable.contract.id
        )

        val result = pricingMemberPayableManagementFeeCalculator.calculate(
            MemberPayableManagementFeeCalculatorInput(
                memberPayable = memberPayable,
                invoicingStartDate = invoicingStartDate,
                invoicingEndDate = invoicingEndDate,
            )
        )

        //assert type of result to be MemberPayableManagementFeePricingResponse
        assertIs<MemberPayableManagementFeePricingResponse>(result)
        val resultAmount = result.amount
        assertEquals(expectedAmount.amount, resultAmount.amount)
        assertEquals(expectedAmount.currency, resultAmount.currency)
    }

    @Test
    fun `when pricing is present and previous paid member payable is present, return pricing amount`() {

    }

    @Test
    fun `when pricing is not present and previous paid member payable is not present, return zero`() {

    }

    @Test
    fun `when pricing is present and previous paid member payable is not present, return pricing amount`() {

    }

    private fun getMemberPayable(): MemberPayable {
        return MemberPayable(
            id = 1L,
            contract = MemberPayable.Contract(
                id = contractId,
                contractType = com.multiplier.payable.engine.contract.ContractType.EMPLOYEE,
            ),
            totalAmountInBaseCurrency = Amount(100.00, CurrencyCode.USD),
            countryCode = CountryCode.USA,
            type = MemberPayableType.INVOICE,
            submittedAt = Instant.now(),
            companyId = companyId,
            fetchedTime = Instant.now(),
            externalId = externalId,
        )
    }
}