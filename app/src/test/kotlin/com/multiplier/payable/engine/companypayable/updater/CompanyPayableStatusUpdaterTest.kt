package com.multiplier.payable.engine.companypayable.updater

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever
import java.util.*

@ExtendWith(MockitoExtension::class)
class CompanyPayableStatusUpdaterTest {

    @Mock
    private lateinit var repository: JpaCompanyPayableRepository

    @InjectMocks
    private lateinit var updater: CompanyPayableStatusUpdater

    @Test
    fun givenContext_whenUpdate_thenThrowException() {
        // GIVEN
        val context = CompanyPayableStatusUpdaterContext(
            id = 42L,
            status = Mockito.mock()
        )

        whenever(repository.findById(context.id))
            .thenReturn(Optional.empty())

        // WHEN and THEN
        assertThrows<IllegalArgumentException> {
            updater.update(context)
        }
    }

    @Test
    fun givenContext_whenUpdate_thenCallRepository() {
        // GIVEN
        val context = CompanyPayableStatusUpdaterContext(
            id = 42L,
            status = Mockito.mock()
        )

        val jpaCompanyPayable = Mockito.mock<JpaCompanyPayable>()
        whenever(repository.findById(context.id))
            .thenReturn(Optional.of(jpaCompanyPayable))

        // WHEN
        updater.update(context)

        // THEN
        verify(repository).save(jpaCompanyPayable)
    }
}