package com.multiplier.payable.engine.reconciler.data.invoice

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.payable.types.CompanyPayableType
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.mapper.CompanyPayableMapper
import com.multiplier.payable.types.PayableStatus
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.LocalDateTime

class VasBgvInvoiceDataProviderTest {

    private val jpaCompanyPayableRepository = mockk<JpaCompanyPayableRepository>()
    private val mapper = mockk<CompanyPayableMapper>()
    
    private val dataProvider = VasBgvInvoiceDataProvider(jpaCompanyPayableRepository, mapper)

    @Test
    fun `should return correct transaction type`() {
        assertEquals(TransactionType.VAS_BACKGROUND_VERIFICATION_INVOICE, dataProvider.transactionType())
    }

    @Test
    fun `should fetch active invoices excluding current transaction`() {
        // Given
        val command = createInvoiceCommand()
        val jpaPayables = listOf(
            createJpaCompanyPayable(id = 1L, transactionId = "other-transaction-1"),
            createJpaCompanyPayable(id = 2L, transactionId = "test-transaction"), // Should be filtered out
            createJpaCompanyPayable(id = 3L, transactionId = "other-transaction-2")
        )
        val expectedMappedPayables = listOf(
            createCompanyPayable(id = 1L),
            createCompanyPayable(id = 3L)
        )

        every {
            jpaCompanyPayableRepository.findByCompanyIdAndTypeAndStatusNotInOrderById(
                command.companyId,
                CompanyPayableType.VAS_BACKGROUND_VERIFICATION_INVOICE,
                setOf(PayableStatus.VOIDED, PayableStatus.DELETED)
            )
        } returns jpaPayables

        every { mapper.mapCompanyPayables(any()) } returns expectedMappedPayables

        // When
        val result = dataProvider.fetchActiveInvoices(command)

        // Then
        assertEquals(expectedMappedPayables, result)
        verify {
            jpaCompanyPayableRepository.findByCompanyIdAndTypeAndStatusNotInOrderById(
                command.companyId,
                CompanyPayableType.VAS_BACKGROUND_VERIFICATION_INVOICE,
                setOf(PayableStatus.VOIDED, PayableStatus.DELETED)
            )
        }
        verify { mapper.mapCompanyPayables(match { 
            it.size == 2 && 
            it.none { payable -> payable.transactionId == "test-transaction" }
        }) }
    }

    @Test
    fun `should return empty list when no active invoices found`() {
        // Given
        val command = createInvoiceCommand()
        val jpaPayables = emptyList<JpaCompanyPayable>()
        val expectedMappedPayables = emptyList<CompanyPayable>()

        every {
            jpaCompanyPayableRepository.findByCompanyIdAndTypeAndStatusNotInOrderById(
                command.companyId,
                CompanyPayableType.VAS_BACKGROUND_VERIFICATION_INVOICE,
                setOf(PayableStatus.VOIDED, PayableStatus.DELETED)
            )
        } returns jpaPayables

        every { mapper.mapCompanyPayables(jpaPayables) } returns expectedMappedPayables

        // When
        val result = dataProvider.fetchActiveInvoices(command)

        // Then
        assertEquals(expectedMappedPayables, result)
    }

    @Test
    fun `should filter out only current transaction when all have same transaction id`() {
        // Given
        val command = createInvoiceCommand()
        val jpaPayables = listOf(
            createJpaCompanyPayable(id = 1L, transactionId = "test-transaction"),
            createJpaCompanyPayable(id = 2L, transactionId = "test-transaction")
        )
        val expectedMappedPayables = emptyList<CompanyPayable>()

        every {
            jpaCompanyPayableRepository.findByCompanyIdAndTypeAndStatusNotInOrderById(
                command.companyId,
                CompanyPayableType.VAS_BACKGROUND_VERIFICATION_INVOICE,
                setOf(PayableStatus.VOIDED, PayableStatus.DELETED)
            )
        } returns jpaPayables

        every { mapper.mapCompanyPayables(emptyList()) } returns expectedMappedPayables

        // When
        val result = dataProvider.fetchActiveInvoices(command)

        // Then
        assertEquals(expectedMappedPayables, result)
        verify { mapper.mapCompanyPayables(emptyList()) }
    }

    private fun createInvoiceCommand(): InvoiceCommand {
        return InvoiceCommand(
            transactionId = "test-transaction",
            transactionType = TransactionType.VAS_BACKGROUND_VERIFICATION_INVOICE,
            companyId = 1L,
            transactionDate = LocalDateTime.now(),
            cycle = InvoiceCycle.MONTHLY,
            dateRange = com.multiplier.payable.engine.domain.aggregates.DateRange(
                startDate = LocalDateTime.now().minusDays(1),
                endDate = LocalDateTime.now()
            )
        )
    }

    private fun createJpaCompanyPayable(id: Long, transactionId: String): JpaCompanyPayable {
        return JpaCompanyPayable().apply {
            this.id = id
            this.transactionId = transactionId
            this.companyId = 1L
            this.type = CompanyPayableType.VAS_BACKGROUND_VERIFICATION_INVOICE
            this.status = PayableStatus.PENDING
        }
    }

    private fun createCompanyPayable(id: Long): CompanyPayable {
        return CompanyPayable(
            id = id,
            companyId = 1L,
            items = emptyList(),
            itemType = TransactionType.VAS_BACKGROUND_VERIFICATION_INVOICE,
            transactionId = "other-transaction",
            status = PayableStatus.PENDING,
            month = 1,
            year = 2024,
            entityId = 123L
        )
    }
}
