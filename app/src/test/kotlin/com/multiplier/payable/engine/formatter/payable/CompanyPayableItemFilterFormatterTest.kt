package com.multiplier.payable.engine.formatter.payable

import com.multiplier.payable.engine.formatter.DataFormatterParam
import com.multiplier.payable.engine.payableitem.PayableItem
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.doReturn
import org.mockito.junit.jupiter.MockitoExtension
import org.springframework.expression.spel.standard.SpelExpressionParser

@ExtendWith(MockitoExtension::class)
class CompanyPayableItemFilterFormatterTest {

    private lateinit var formatter: CompanyPayableItemFilterFormatter

    @Mock
    private lateinit var dataFormatterParam: DataFormatterParam

    @BeforeEach
    fun setup() {
        formatter = CompanyPayableItemFilterFormatter()
        formatter.dataFormatterParam = dataFormatterParam
    }

    @Test
    fun `should return all items when filter expression is null`() {
        // GIVEN
        val items = createSamplePayableItems()
        doReturn(null).`when`(dataFormatterParam).getFilterExpression()

        // WHEN
        val result = formatter.format(items)

        // THEN
        assertEquals(items, result)
        assertEquals(3, result.size)
    }

    @Test
    fun `should filter items based on expression`() {
        // GIVEN
        val items = createSamplePayableItems()
        val filterExpression = SpelExpressionParser().parseExpression("countryCode == 'CAN'")
        doReturn(filterExpression).`when`(dataFormatterParam).getFilterExpression()

        // WHEN
        val result = formatter.format(items)

        // THEN
        assertEquals(2, result.size)
        assertEquals(listOf("USA", "SGP"), result.mapNotNull { it.countryCode })
    }

    @Test
    fun `should filter items based on complex expression`() {
        // GIVEN
        val items = createSamplePayableItems()
        val filterExpression =
            SpelExpressionParser().parseExpression("countryCode == 'CAN' and lineItemType == 'EOR_SALARY_DISBURSEMENT'")
        doReturn(filterExpression).`when`(dataFormatterParam).getFilterExpression()

        // WHEN
        val result = formatter.format(items)

        // THEN
        assertEquals(2, result.size)
        assertEquals(listOf("USA", "SGP"), result.mapNotNull { it.countryCode })
    }

    @Test
    fun `should return empty list when all items match filter expression`() {
        // GIVEN
        val items = createSamplePayableItems()
        val filterExpression = SpelExpressionParser().parseExpression("true")
        doReturn(filterExpression).`when`(dataFormatterParam).getFilterExpression()

        // WHEN
        val result = formatter.format(items)

        // THEN
        assertEquals(0, result.size)
    }

    @Test
    fun `should return all items when filter expression evaluates to false for all items`() {
        // GIVEN
        val items = createSamplePayableItems()
        val filterExpression = SpelExpressionParser().parseExpression("false")
        doReturn(filterExpression).`when`(dataFormatterParam).getFilterExpression()

        // WHEN
        val result = formatter.format(items)

        // THEN
        assertEquals(3, result.size)
        assertEquals(items, result)
    }

    private fun createSamplePayableItems(): List<PayableItem> {
        return listOf(
            PayableItem(
                month = 1,
                year = 2023,
                lineItemType = "EOR_SALARY_DISBURSEMENT",
                amountInBaseCurrency = 1000.0,
                baseCurrency = "USD",
                originalTimestamp = 123456789L,
                cycle = com.multiplier.payable.engine.domain.aggregates.InvoiceCycle.MONTHLY,
                countryCode = "CAN"
            ),
            PayableItem(
                month = 1,
                year = 2023,
                lineItemType = "MANAGEMENT_FEE",
                amountInBaseCurrency = 500.0,
                baseCurrency = "USD",
                originalTimestamp = 123456789L,
                cycle = com.multiplier.payable.engine.domain.aggregates.InvoiceCycle.MONTHLY,
                countryCode = "USA"
            ),
            PayableItem(
                month = 1,
                year = 2023,
                lineItemType = "PLATFORM_FEE",
                amountInBaseCurrency = 200.0,
                baseCurrency = "SGD",
                originalTimestamp = 123456789L,
                cycle = com.multiplier.payable.engine.domain.aggregates.InvoiceCycle.MONTHLY,
                countryCode = "SGP"
            )
        )
    }
}
