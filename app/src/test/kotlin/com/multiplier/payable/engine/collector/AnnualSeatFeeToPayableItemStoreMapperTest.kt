package com.multiplier.payable.engine.collector

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.company.OfferingCode
import com.multiplier.payable.engine.collector.planfee.AnnualSeatFeeDataKey
import com.multiplier.payable.engine.collector.planfee.AnnualSeatFeeToPayableItemStoreMapper
import com.multiplier.payable.engine.contract.CountryWorkStatus
import com.multiplier.payable.engine.domain.aggregates.AnnualSeatPaymentTerm
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.entities.AnnualSeatFee
import com.multiplier.payable.engine.domain.entities.PricingPlanStatus
import com.multiplier.payable.engine.domain.entities.SeatStatus
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mapstruct.factory.Mappers
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneOffset

class AnnualSeatFeeToPayableItemStoreMapperTest {
    private val mapper: AnnualSeatFeeToPayableItemStoreMapper =
        Mappers.getMapper(AnnualSeatFeeToPayableItemStoreMapper::class.java)
    private val objectMapper = ObjectMapper().registerModule(JavaTimeModule())

    @Test
    fun givenValidPlanFee_whenCallMap_shouldReturnCorrectItemStore() {
        // Given
        val nowDateTime = LocalDateTime.now()
        val originalTimestamp = nowDateTime.toEpochSecond(ZoneOffset.UTC) * 1000
        val calculatedDateTime = nowDateTime.minusMinutes(10)
        val transactionId = "12345"
        val startDate = LocalDateTime.of(2024, 2, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 2, 29, 0, 0)
        val period = DateRange(startDate, endDate)
        val key =
            AnnualSeatFeeDataKey(
                companyId = 1L,
                planId = 1000L,
                lineItemType = LineItemType.ANNUAL_MANAGEMENT_FEE_EOR,
                oTime = originalTimestamp,
            )
        val annualSeatFee =
            AnnualSeatFee(
                companyId = 1L,
                planId = 1000L,
                countryCode = "SGP",
                period = period,
                amount = 60000.0,
                currencyCode = "SGD",
                seatCount = 10,
                calculatedDateTime = calculatedDateTime,
                seatStatus = SeatStatus.OCCUPIED,
                pricingPlanStatus = PricingPlanStatus.ACTIVE,
                annualSeatPaymentTerm = mockDefaultAnnualSeatPaymentTerm(seatId = 5000L),
                countryWorkStatus = CountryWorkStatus.RESIDENT,
                offeringCode = OfferingCode.EOR,
                lineCode = "SERVICE_FEE",
                seatId = 0,
            )
        val subscriptionFees = mapOf(key to annualSeatFee)
        val planFeeJson = objectMapper.writeValueAsString(annualSeatFee)
        val planFeeJsonMap = mapOf(key to planFeeJson)
        val expectedDto1 =
            PayableItemStoreDto(
                amount = 60000.0,
                currency = CurrencyCode.SGD,
                companyId = 1L,
                contractId = -1,
                annualSeatPaymentTerm = mockDefaultAnnualSeatPaymentTerm(5000L),
                transactionId = transactionId,
                month = nowDateTime.monthValue,
                year = nowDateTime.year,
                itemType = LineItemType.ANNUAL_MANAGEMENT_FEE_EOR,
                itemData = planFeeJson,
                periodStartDate = startDate.toLocalDate(),
                periodEndDate = endDate.toLocalDate(),
                versionId = key.computeHash(),
                originalTimestamp = originalTimestamp,
                countryCode = "SGP",
            )

        val expectedList = listOf(expectedDto1)

        // When
        val result =
            mapper.map(
                transactionId = transactionId,
                transactionDate = nowDateTime,
                annualSeatFees = subscriptionFees,
                itemDataMap = planFeeJsonMap,
            )

        // Then
        assertEquals(expectedList, result)

    }

    private fun mockDefaultAnnualSeatPaymentTerm(
        seatId: Long = 5000L,
        timeUnit: String = "MONTH",
        interval: Int = 1,
        periodCount: Int = 12,
        periodNumber: Int = 1,
        planPeriod: DateRange = DateRange(
            startDate = LocalDate.of(2024, 1, 1).atStartOfDay(),
            endDate = LocalDate.of(2024, 12, 31).atTime(LocalTime.MAX),
        ),
        periodDateRange: DateRange = DateRange(
            startDate = LocalDate.of(2024, 1, 1).atStartOfDay(),
            endDate = LocalDate.of(2024, 12, 31).atTime(LocalTime.MAX),
        ),
    ) = AnnualSeatPaymentTerm(
        seatId = seatId,
        timeUnit = timeUnit,
        interval = interval,
        periodCount = periodCount,
        periodNumber = periodNumber,
        planPeriod = planPeriod,
        periodDateRange = periodDateRange,
    )
}
