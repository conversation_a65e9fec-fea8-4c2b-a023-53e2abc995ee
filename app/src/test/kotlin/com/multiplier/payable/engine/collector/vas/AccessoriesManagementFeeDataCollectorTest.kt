package com.multiplier.payable.engine.collector.vas

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.data.ProcessedIncidentCollectorInput
import com.multiplier.payable.engine.collector.vas.fee.AccessoriesManagementFeeDataCollector
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.vas.IncidentType
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.Instant
import kotlin.test.assertEquals

class AccessoriesManagementFeeDataCollectorTest {
    @MockK
    private lateinit var vasIncidentManagementFeeCollector: VasIncidentManagementFeeCollector

    @InjectMockKs
    private lateinit var dataCollector: AccessoriesManagementFeeDataCollector

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `getSupportedType should return VAS_INCIDENT_ACCESSORIES_MANAGEMENT_FEE`() {
        assertEquals(
            LineItemType.VAS_INCIDENT_ACCESSORIES_MANAGEMENT_FEE,
            dataCollector.getSupportedType(),
        )
    }

    @Test
    fun `handle should process and collect accessories management fee data`() {
        // Given
        val command =
            mockk<InvoiceCommand> {
                every { transactionId } returns "test-transaction-123"
            }

        val processedInput = mockk<ProcessedIncidentCollectorInput>(relaxed = true)
        every { vasIncidentManagementFeeCollector.processInput(command) } returns processedInput
        every {
            vasIncidentManagementFeeCollector.collectAndSaveIncidents(
                processedInput,
                IncidentType.ACCESSORIES,
            )
        } just runs

        // When
        dataCollector.handle(command)

        // Then
        verify(exactly = 1) {
            vasIncidentManagementFeeCollector.processInput(command)
            vasIncidentManagementFeeCollector.collectAndSaveIncidents(
                processedInput,
                IncidentType.ACCESSORIES,
            )
        }

        verify {
            processedInput.lineItemType = LineItemType.VAS_INCIDENT_ACCESSORIES_MANAGEMENT_FEE
            processedInput.originalTimestamp = any()
        }
    }

    @Test
    fun `handle should propagate exceptions from processInput`() {
        // Given
        val command = mockk<InvoiceCommand>()
        every {
            vasIncidentManagementFeeCollector.processInput(command)
        } throws RuntimeException("Processing failed")

        // When/Then
        assertThrows<RuntimeException> {
            dataCollector.handle(command)
        }
    }

    @Test
    fun `handle should propagate exceptions from collectAndSaveIncidents`() {
        // Given
        val command = mockk<InvoiceCommand>()
        val processedInput = mockk<ProcessedIncidentCollectorInput>(relaxed = true)

        every { vasIncidentManagementFeeCollector.processInput(command) } returns processedInput
        every {
            vasIncidentManagementFeeCollector.collectAndSaveIncidents(any(), any())
        } throws RuntimeException("Collection failed")

        // When/Then
        assertThrows<RuntimeException> {
            dataCollector.handle(command)
        }
    }

    @Test
    fun `handle should set timestamp within expected range`() {
        // Given
        val command = mockk<InvoiceCommand>()
        val processedInput = mockk<ProcessedIncidentCollectorInput>(relaxed = true)
        val beforeExecution = Instant.now()

        every { vasIncidentManagementFeeCollector.processInput(command) } returns processedInput
        every {
            vasIncidentManagementFeeCollector.collectAndSaveIncidents(any(), any())
        } just runs

        // When
        dataCollector.handle(command)
        val afterExecution = Instant.now()

        // Then
        verify {
            processedInput.originalTimestamp =
                withArg { timestamp ->
                    assert(timestamp in beforeExecution..afterExecution) {
                        "Timestamp should be between $beforeExecution and $afterExecution but was $timestamp"
                    }
                }
        }
    }
}
