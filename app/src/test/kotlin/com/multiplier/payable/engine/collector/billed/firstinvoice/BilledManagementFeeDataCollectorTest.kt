package com.multiplier.payable.engine.collector.billed.firstinvoice

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.service.InvoiceFetcher
import com.multiplier.payable.engine.collector.DataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.DefaultProcessedCollectorInput
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import kotlin.test.assertEquals

class BilledManagementFeeDataCollectorTest {

    private lateinit var dataCollector: BilledManagementFeeDataCollector
    private lateinit var dataCollectorInputProcessor: DataCollectorInputProcessor<DefaultProcessedCollectorInput>
    private lateinit var payableItemStoreService: PayableItemStoreService
    private lateinit var invoiceFetcher: InvoiceFetcher
    private lateinit var itemMapper: BilledFirstInvoiceItemMapper

    @BeforeEach
    fun setUp() {
        dataCollectorInputProcessor = mock(DataCollectorInputProcessor::class.java) as DataCollectorInputProcessor<DefaultProcessedCollectorInput>
        payableItemStoreService = mock(PayableItemStoreService::class.java)
        invoiceFetcher = mock(InvoiceFetcher::class.java)
        itemMapper = mock(BilledFirstInvoiceItemMapper::class.java)
        dataCollector = BilledManagementFeeDataCollector(
            dataCollectorInputProcessor,
            payableItemStoreService,
            invoiceFetcher,
            itemMapper
        )
    }

    @Test
    fun `getSupportedType should return BILLED_MANAGEMENT_FEE`() {
        val result = dataCollector.getSupportedType()
        assertEquals(LineItemType.BILLED_MANAGEMENT_FEE, result)
    }

    @Test
    fun `getBilledLineItemType should return MANAGEMENT_FEE_EOR`() {
        val result = dataCollector.getBilledLineItemType()
        assertEquals(LineItemType.MANAGEMENT_FEE_EOR, result)
    }
}