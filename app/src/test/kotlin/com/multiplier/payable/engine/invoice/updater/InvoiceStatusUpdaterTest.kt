package com.multiplier.payable.engine.invoice.updater

import com.multiplier.core.payable.invoice.database.JpaInvoiceRepository
import com.multiplier.core.payable.repository.model.JpaInvoice
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever
import java.util.*

@ExtendWith(MockitoExtension::class)
class InvoiceStatusUpdaterTest {

    @Mock
    private lateinit var repository: JpaInvoiceRepository

    @InjectMocks
    private lateinit var updater: InvoiceStatusUpdater

    @Test
    fun givenContext_whenUpdate_thenThrowException() {
        // GIVEN
        val context = InvoiceStatusUpdaterContext(
            id = 42L,
            status = mock()
        )

        whenever(repository.findById(context.id))
            .thenReturn(Optional.empty())

        // WHEN and THEN
        assertThrows<IllegalArgumentException> {
            updater.update(context)
        }
    }

    @Test
    fun givenContext_whenUpdate_thenCallRepository() {
        // GIVEN
        val context = InvoiceStatusUpdaterContext(
            id = 42L,
            status = mock()
        )

        val jpaInvoice = mock<JpaInvoice>()
        whenever(repository.findById(context.id))
            .thenReturn(Optional.of(jpaInvoice))

        // WHEN
        updater.update(context)

        // THEN
        verify(repository).save(jpaInvoice)
    }
}