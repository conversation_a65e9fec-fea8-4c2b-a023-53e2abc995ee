package com.multiplier.payable.engine.invoice.strategy.sorting

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Test

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class VasIncidentSortingStrategyTest {

    private val vasIncidentSortingStrategy = VasIncidentSortingStrategy()

    @Test
    fun getTransactionType() {
        assertEquals(TransactionType.VAS_INCIDENT_INVOICE, vasIncidentSortingStrategy.transactionType)
    }

    @Test
    fun sort() {
        val items = listOf(
            PayableItem(
                lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT.name,
                contractId = 1L,
                month = 1,
                year = 2022,
                amountInBaseCurrency = 100.0,
                baseCurrency = "INR",
                originalTimestamp = 1L,
                cycle = InvoiceCycle.MONTHLY
            ),
            PayableItem(
                lineItemType = LineItemType.VAS_INCIDENT_DISCOUNT.name,
                contractId = -1,
                month = 1,
                year = 2022,
                amountInBaseCurrency = 100.0,
                baseCurrency = "INR",
                originalTimestamp = 1L,
                cycle = InvoiceCycle.MONTHLY
            ),
            PayableItem(
                lineItemType = LineItemType.VAS_INCIDENT_MONITOR_PAYMENT.name,
                contractId = 2L,
                month = 1,
                year = 2022,
                amountInBaseCurrency = 100.0,
                baseCurrency = "INR",
                originalTimestamp = 1L,
                cycle = InvoiceCycle.MONTHLY
            ),
            PayableItem(
                lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_MANAGEMENT_FEE.name,
                contractId = 1L,
                month = 1,
                year = 2022,
                amountInBaseCurrency = 100.0,
                baseCurrency = "INR",
                originalTimestamp = 1L,
                cycle = InvoiceCycle.MONTHLY
            ),
            PayableItem(
                lineItemType = LineItemType.VAS_INCIDENT_DISCOUNT.name,
                contractId = -1,
                month = 1,
                year = 2022,
                amountInBaseCurrency = 100.0,
                baseCurrency = "INR",
                originalTimestamp = 1L,
                cycle = InvoiceCycle.MONTHLY
            ),
            PayableItem(
                lineItemType = LineItemType.VAS_INCIDENT_MONITOR_PAYMENT.name,
                contractId = 3L,
                month = 1,
                year = 2022,
                amountInBaseCurrency = 100.0,
                baseCurrency = "INR",
                originalTimestamp = 1L,
                cycle = InvoiceCycle.MONTHLY
            ),
            PayableItem(
                lineItemType = LineItemType.VAS_INCIDENT_MONITOR_MANAGEMENT_FEE.name,
                contractId = 3L,
                month = 1,
                year = 2022,
                amountInBaseCurrency = 100.0,
                baseCurrency = "INR",
                originalTimestamp = 1L,
                cycle = InvoiceCycle.MONTHLY
            ),
        )

        val result = vasIncidentSortingStrategy.sort(items)
        assertEquals(LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT.name, result[0].lineItemType)
        assertEquals(1L, result[0].contractId)
        assertEquals(LineItemType.VAS_INCIDENT_LAPTOP_MANAGEMENT_FEE.name, result[1].lineItemType)
        assertEquals(1L, result[1].contractId)
        assertEquals(LineItemType.VAS_INCIDENT_MONITOR_PAYMENT.name, result[2].lineItemType)
        assertEquals(2L, result[2].contractId)
        assertEquals(LineItemType.VAS_INCIDENT_MONITOR_PAYMENT.name, result[3].lineItemType)
        assertEquals(3L, result[3].contractId)
        assertEquals(LineItemType.VAS_INCIDENT_MONITOR_MANAGEMENT_FEE.name, result[4].lineItemType)
        assertEquals(3L, result[4].contractId)
        assertEquals(LineItemType.VAS_INCIDENT_DISCOUNT.name, result[5].lineItemType)
        assertEquals(LineItemType.VAS_INCIDENT_DISCOUNT.name, result[6].lineItemType)
    }
}