package com.multiplier.payable.engine.transaction.handler

import com.multiplier.core.payable.pricing.adapter.PricingServiceAdapter
import com.multiplier.payable.types.Pricing
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.rollback.OffCycleInvoiceRollbackService
import com.multiplier.payable.engine.testutils.InvoiceEngineTestDataFactory
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.generator.FinancialTransactionGenerator
import com.multiplier.payable.engine.domain.entities.Invoice
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.types.CurrencyCode as TypesCurrencyCode
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDateTime

@ExtendWith(MockKExtension::class)
class OffCycleInvoiceHandlerTest {

    @MockK
    private lateinit var pricingServiceAdapter: PricingServiceAdapter

    @MockK
    private lateinit var invoiceTransactionBuilder: InvoiceTransactionBuilder

    @MockK
    private lateinit var invoiceGenerator: FinancialTransactionGenerator

    @MockK
    private lateinit var offCycleInvoiceRollbackService: OffCycleInvoiceRollbackService

    @InjectMockKs
    private lateinit var handler: OffCycleInvoiceHandler

    @Test
    fun `transactionType should return PAYROLL_OFFCYCLE_INVOICE`() {
        // WHEN
        val result = handler.transactionType

        // THEN
        assertEquals(TransactionType.PAYROLL_OFFCYCLE_INVOICE, result)
    }

    @Test
    fun `given valid command when handle then processes off-cycle invoice generation correctly`() {
        // GIVEN
        val companyId = 123L
        val transactionDate = LocalDateTime.of(2024, 6, 15, 10, 30)
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            companyId = companyId,
            transactionDate = transactionDate
        )

        val paymentTermInDays = 14
        val billingCurrencyCode = TypesCurrencyCode.USD
        val pricing = mockk<Pricing>()
        every { pricing.paymentTermInDays } returns paymentTermInDays
        every { pricing.billingCurrencyCode } returns billingCurrencyCode

        val expectedDueDate = transactionDate.plusDays(paymentTermInDays.toLong())
        val expectedEnrichedCommand = command.copy(
            billingCurrencyCode = CurrencyCode.USD,
            dueDate = expectedDueDate
        )

        val invoice1 = mockk<Invoice>()
        val invoice2 = mockk<Invoice>()
        val invoicesToGenerate = listOf(invoice1, invoice2)

        every { pricingServiceAdapter.getCompanyPricing(companyId) } returns pricing
        every { invoiceTransactionBuilder.build(any()) } returns invoicesToGenerate
        every { invoiceGenerator.generate(any()) } returns "invoice-id"

        // WHEN
        handler.handle(command)

        // THEN
        verify { pricingServiceAdapter.getCompanyPricing(companyId) }

        val enrichedCommandSlot = slot<InvoiceCommand>()
        verify { invoiceTransactionBuilder.build(capture(enrichedCommandSlot)) }

        val capturedCommand = enrichedCommandSlot.captured
        assertEquals(CurrencyCode.USD, capturedCommand.billingCurrencyCode)
        assertEquals(expectedDueDate, capturedCommand.dueDate)
        assertEquals(command.companyId, capturedCommand.companyId)
        assertEquals(command.transactionId, capturedCommand.transactionId)
        assertEquals(command.transactionDate, capturedCommand.transactionDate)

        verify { invoiceGenerator.generate(invoice1) }
        verify { invoiceGenerator.generate(invoice2) }
    }

    @Test
    fun `given company pricing with null payment term when handle then uses default 7 days`() {
        // GIVEN
        val companyId = 123L
        val transactionDate = LocalDateTime.of(2024, 6, 15, 10, 30)
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            companyId = companyId,
            transactionDate = transactionDate
        )

        val pricing = mockk<Pricing> {
            every { paymentTermInDays } returns null
            every { billingCurrencyCode } returns TypesCurrencyCode.EUR
        }

        val expectedDueDate = transactionDate.plusDays(7L) // Default 7 days

        every { pricingServiceAdapter.getCompanyPricing(companyId) } returns pricing
        every { invoiceTransactionBuilder.build(any()) } returns emptyList()

        // WHEN
        handler.handle(command)

        // THEN
        val enrichedCommandSlot = slot<InvoiceCommand>()
        verify { invoiceTransactionBuilder.build(capture(enrichedCommandSlot)) }

        val capturedCommand = enrichedCommandSlot.captured
        assertEquals(expectedDueDate, capturedCommand.dueDate)
        assertEquals(CurrencyCode.EUR, capturedCommand.billingCurrencyCode)
    }

    @Test
    fun `given different billing currencies when handle then maps currency correctly`() {
        // GIVEN
        val testCases = listOf(
            TypesCurrencyCode.USD to CurrencyCode.USD,
            TypesCurrencyCode.EUR to CurrencyCode.EUR,
            TypesCurrencyCode.GBP to CurrencyCode.GBP,
            TypesCurrencyCode.CAD to CurrencyCode.CAD
        )

        val capturedCommands = mutableListOf<InvoiceCommand>()

        testCases.forEach { (typesCurrency, expectedEngineCurrency) ->
            val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

            val pricing = mockk<Pricing>()
            every { pricing.paymentTermInDays } returns 7
            every { pricing.billingCurrencyCode } returns typesCurrency

            every { pricingServiceAdapter.getCompanyPricing(command.companyId) } returns pricing
            every { invoiceTransactionBuilder.build(capture(capturedCommands)) } returns emptyList()

            // WHEN
            handler.handle(command)
        }

        // THEN
        assertEquals(testCases.size, capturedCommands.size)
        testCases.forEachIndexed { index, (_, expectedEngineCurrency) ->
            assertEquals(expectedEngineCurrency, capturedCommands[index].billingCurrencyCode)
        }
    }

    @Test
    fun `given no invoices to generate when handle then does not call invoice generator`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

        val pricing = mockk<Pricing> {
            every { paymentTermInDays } returns 7
            every { billingCurrencyCode } returns TypesCurrencyCode.USD
        }

        every { pricingServiceAdapter.getCompanyPricing(command.companyId) } returns pricing
        every { invoiceTransactionBuilder.build(any()) } returns emptyList()

        // WHEN
        handler.handle(command)

        // THEN
        verify { pricingServiceAdapter.getCompanyPricing(command.companyId) }
        verify { invoiceTransactionBuilder.build(any()) }
        verify(exactly = 0) { invoiceGenerator.generate(any()) }
    }

    @Test
    fun `given single invoice to generate when handle then calls generator once`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

        val pricing = mockk<Pricing> {
            every { paymentTermInDays } returns 7
            every { billingCurrencyCode } returns TypesCurrencyCode.USD
        }

        val invoice = mockk<Invoice>()
        val invoicesToGenerate = listOf(invoice)

        every { pricingServiceAdapter.getCompanyPricing(command.companyId) } returns pricing
        every { invoiceTransactionBuilder.build(any()) } returns invoicesToGenerate
        every { invoiceGenerator.generate(any()) } returns "invoice-id"

        // WHEN
        handler.handle(command)

        // THEN
        verify(exactly = 1) { invoiceGenerator.generate(invoice) }
    }

    @Test
    fun `given multiple invoices to generate when handle then calls generator for each invoice`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

        val pricing = mockk<Pricing> {
            every { paymentTermInDays } returns 7
            every { billingCurrencyCode } returns TypesCurrencyCode.USD
        }

        val invoices = (1..3).map { mockk<Invoice>() }

        every { pricingServiceAdapter.getCompanyPricing(command.companyId) } returns pricing
        every { invoiceTransactionBuilder.build(any()) } returns invoices
        every { invoiceGenerator.generate(any()) } returns "invoice-id"

        // WHEN
        handler.handle(command)

        // THEN
        invoices.forEach { invoice ->
            verify { invoiceGenerator.generate(invoice) }
        }
        verify(exactly = 3) { invoiceGenerator.generate(any()) }
    }

    @Test
    fun `given different payment terms when handle then calculates due date correctly`() {
        // GIVEN
        val transactionDate = LocalDateTime.of(2024, 6, 15, 10, 30)
        val testCases = listOf(
            1 to transactionDate.plusDays(1),
            7 to transactionDate.plusDays(7),
            14 to transactionDate.plusDays(14),
            30 to transactionDate.plusDays(30),
            60 to transactionDate.plusDays(60)
        )

        val capturedCommands = mutableListOf<InvoiceCommand>()

        testCases.forEach { (paymentTerm, expectedDueDate) ->
            val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
                transactionDate = transactionDate
            )

            val pricing = mockk<Pricing>()
            every { pricing.paymentTermInDays } returns paymentTerm
            every { pricing.billingCurrencyCode } returns TypesCurrencyCode.USD

            every { pricingServiceAdapter.getCompanyPricing(command.companyId) } returns pricing
            every { invoiceTransactionBuilder.build(capture(capturedCommands)) } returns emptyList()

            // WHEN
            handler.handle(command)
        }

        // THEN
        assertEquals(testCases.size, capturedCommands.size)
        testCases.forEachIndexed { index, (_, expectedDueDate) ->
            assertEquals(expectedDueDate, capturedCommands[index].dueDate)
        }
    }

    @Test
    fun `given command when handle then preserves original command properties`() {
        // GIVEN
        val originalTransactionId = "TX123456789"
        val originalCompanyId = 999L
        val originalTransactionDate = LocalDateTime.of(2024, 6, 15, 10, 30)
        val originalCycle = InvoiceCycle.MONTHLY

        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            transactionId = originalTransactionId,
            companyId = originalCompanyId,
            transactionDate = originalTransactionDate,
            cycle = originalCycle
        )

        val pricing = mockk<Pricing> {
            every { paymentTermInDays } returns 7
            every { billingCurrencyCode } returns TypesCurrencyCode.USD
        }

        every { pricingServiceAdapter.getCompanyPricing(originalCompanyId) } returns pricing
        every { invoiceTransactionBuilder.build(any()) } returns emptyList()

        // WHEN
        handler.handle(command)

        // THEN
        val enrichedCommandSlot = slot<InvoiceCommand>()
        verify { invoiceTransactionBuilder.build(capture(enrichedCommandSlot)) }

        val capturedCommand = enrichedCommandSlot.captured
        assertEquals(originalTransactionId, capturedCommand.transactionId)
        assertEquals(originalCompanyId, capturedCommand.companyId)
        assertEquals(originalTransactionDate, capturedCommand.transactionDate)
        assertEquals(originalCycle, capturedCommand.cycle)
    }

    @Test
    fun `when rollback then calls rollback service`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            transactionId = "TX123456789"
        )

        every { offCycleInvoiceRollbackService.rollbackInvoiceGeneration(any()) } returns Unit

        // WHEN
        handler.rollback(command)

        // THEN
        verify { offCycleInvoiceRollbackService.rollbackInvoiceGeneration(command) }
    }
}
