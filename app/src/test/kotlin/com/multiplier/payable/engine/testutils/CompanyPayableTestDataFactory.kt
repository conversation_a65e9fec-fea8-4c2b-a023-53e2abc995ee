package com.multiplier.payable.engine.testutils

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.creditnote.database.JpaCreditNote
import com.multiplier.core.payable.creditnote.database.JpaCreditNoteLineItem
import com.multiplier.core.payable.event.database.RecordType
import com.multiplier.core.payable.repository.model.ExternalSystem
import com.multiplier.core.payable.repository.model.InvoiceReason
import com.multiplier.core.payable.repository.model.InvoiceType
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaInvoice
import com.multiplier.core.payable.repository.model.JpaInvoiceLineItem
import com.multiplier.core.payable.repository.model.JpaPayableItem
import com.multiplier.core.payable.repository.model.JpaPayableItemData
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.contract.CountryWorkStatus
import com.multiplier.payable.engine.deposit.Deposit
import com.multiplier.payable.engine.deposit.DepositType
import com.multiplier.payable.engine.domain.aggregates.AnnualSeatPaymentTerm
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.ContractDepartment
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.types.CompanyPayableType
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CreditNoteReason
import com.multiplier.payable.types.CreditNoteStatus
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.types.InvoiceStatus
import com.multiplier.payable.types.PayableItemType
import com.multiplier.payable.types.PayableStatus
import com.multiplier.payable.types.TransactionPayableItem
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

object CompanyPayableTestDataFactory {
    // JPA SECTION
    fun createJpaCompanyPayable(
        id: Long = 1000L,
        status: PayableStatus = PayableStatus.DRAFT,
        companyId: Long = 123L,
        date: LocalDateTime = LocalDateTime.of(2024, 1, 1, 0, 0),
        month: Int = 1,
        year: Int = 2024,
        cycle: Int = 3,
        invoice: JpaInvoice? = createJpaInvoice(),
        items: Set<JpaPayableItem> = setOf(createJpaPayableItem()),
        totalAmount: Double = 1000.00,
        currency: CurrencyCode = CurrencyCode.USD,
        type: CompanyPayableType = CompanyPayableType.GLOBAL_PAYROLL_FUNDING,
        transactionId: String = "TX123456789",
    ): JpaCompanyPayable {
        return JpaCompanyPayable.builder()
            .id(id)
            .status(status)
            .companyId(companyId)
            .date(date)
            .month(month)
            .year(year)
            .cycle(cycle)
            .invoice(invoice)
            .items(items)
            .totalAmount(totalAmount)
            .currency(currency)
            .type(type)
            .transactionId(transactionId)
            .build()
    }

    fun createJpaPayableItem(
        type: PayableItemType = PayableItemType.MEMBER_GLOBAL_PAYROLL_FUNDING_COST,
        description: String = "Service Cost",
        totalCost: Double = 500.0,
        itemData: Set<JpaPayableItemData> = setOf(createJpaPayableItemData()),
        currencyCode: CurrencyCode = CurrencyCode.USD,
        countryCode: CountryCode = CountryCode.USA,
        billableCost: Double = 400.0,
        taxType: String = "GST",
        countryName: String = "United States",
        versionId: String = "v1.0",
        originalTimestamp: Long = 123456789L,
    ): JpaPayableItem {
        return JpaPayableItem.builder()
            .type(type)
            .description(description)
            .totalCost(totalCost)
            .itemData(itemData)
            .currencyCode(currencyCode)
            .countryCode(countryCode)
            .billableCost(billableCost)
            .taxType(taxType)
            .countryName(countryName)
            .versionId(versionId)
            .originalTimestamp(originalTimestamp)
            .build()
    }

    fun mockDefaultAnnualSeatPaymentTerm(
        seatId: Long = 5000L,
        timeUnit: String = "MONTH",
        interval: Int = 1,
        periodCount: Int = 12,
        periodNumber: Int = 1,
        planPeriod: DateRange = DateRange(
            startDate = LocalDate.of(2024, 1, 1).atStartOfDay(),
            endDate = LocalDate.of(2024, 12, 31).atTime(LocalTime.MAX),
        ),
        periodDateRange: DateRange = DateRange(
            startDate = LocalDate.of(2024, 1, 1).atStartOfDay(),
            endDate = LocalDate.of(2024, 12, 31).atTime(LocalTime.MAX),
        ),
    ) = AnnualSeatPaymentTerm(
        seatId = seatId,
        timeUnit = timeUnit,
        interval = interval,
        periodCount = periodCount,
        periodNumber = periodNumber,
        planPeriod = planPeriod,
        periodDateRange = periodDateRange,
    )

    fun createJpaPayableItemData(
        memberPayId: Long = 1L,
        contractId: Long = 100L,
        contractType: ContractType = ContractType.EMPLOYEE,
        amountTotalCost: Double = 300.0,
        currencyCode: CurrencyCode = CurrencyCode.USD,
        memberName: String = "John Doe",
        startPayCycleDate: LocalDate = LocalDate.of(2024, 1, 1),
        endPayCycleDate: LocalDate = LocalDate.of(2024, 1, 31),
        cycle: String = "MONTHLY",
        insuranceType: String = "A random Custome package name",
        contractDepartment: ContractDepartment? = ContractDepartment(
            id = 42L,
            name = "awesomeContractDepartment"
        ),
        deposit: Deposit? = Deposit(
            type = DepositType.SALARY,
            amount = 42.0,
            currencyCode = com.multiplier.payable.engine.currency.CurrencyCode.USD,
            contractType = ContractType.EMPLOYEE
        ),
        annualSeatPaymentTerm: AnnualSeatPaymentTerm = mockDefaultAnnualSeatPaymentTerm(),
        countryWorkStatus: CountryWorkStatus? = CountryWorkStatus.RESIDENT,
    ): JpaPayableItemData {
        return JpaPayableItemData.builder()
            .memberPayId(memberPayId)
            .contractId(contractId)
            .contractType(contractType)
            .amountTotalCost(amountTotalCost)
            .currencyCode(currencyCode)
            .memberName(memberName)
            .startPayCycleDate(startPayCycleDate)
            .endPayCycleDate(endPayCycleDate)
            .cycle(cycle)
            .annualSeatPaymentTerm(annualSeatPaymentTerm)
            .insuranceType(insuranceType)
            .contractDepartment(contractDepartment)
            .deposit(deposit)
            .annualSeatPaymentTerm(annualSeatPaymentTerm)
            .countryWorkStatus(countryWorkStatus)
            .build()
    }

    fun createJpaInvoice(
        id: Long? = 1L,
        status: InvoiceStatus = InvoiceStatus.DRAFT,
        createdDate: LocalDateTime = LocalDateTime.of(2024, 1, 1, 0, 0),
        dueDate: LocalDateTime = LocalDateTime.of(2024, 2, 1, 0, 0),
        fullyPaidOnDate: LocalDateTime? = LocalDateTime.of(2024, 2, 15, 0, 0),
        reference: String = "REF-123",
        externalId: String = "e453d445-0eda-44fb-9f99-fd5b165530f2",
        invoiceNo: String = "INV-0011",
        companyPayable: JpaCompanyPayable? = null, // eternal looping
        lineItems: List<JpaInvoiceLineItem> = listOf(createJpaInvoiceLineItem()),
        emailSent: Boolean = true,
        externalInvoiceGenerated: Boolean = true,
        errorReason: String? = "None",
        amountPaid: BigDecimal = BigDecimal.valueOf(900),
        amountDue: BigDecimal = BigDecimal.valueOf(100),
        totalAmount: BigDecimal = BigDecimal.valueOf(1000),
        externalSystem: ExternalSystem = ExternalSystem.XERO,
        creditNotes: Set<JpaCreditNote> = setOf(createJpaCreditNote()),
        reason: InvoiceReason = InvoiceReason.OTHERS,
        type: InvoiceType = InvoiceType.GLOBAL_PAYROLL_FUNDING,
        recordType: RecordType = RecordType.INVOICE,
        syncedTime: LocalDateTime? = LocalDateTime.of(2024, 1, 1, 0, 0),
    ): JpaInvoice {
        return JpaInvoice.builder()
            .id(id)
            .status(status)
            .createdDate(createdDate)
            .dueDate(dueDate)
            .fullyPaidOnDate(fullyPaidOnDate)
            .reference(reference)
            .externalId(externalId)
            .invoiceNo(invoiceNo)
            .companyPayable(companyPayable)
            .lineItems(lineItems)
            .emailSent(emailSent)
            .externalInvoiceGenerated(externalInvoiceGenerated)
            .errorReason(errorReason)
            .amountPaid(amountPaid)
            .amountDue(amountDue)
            .totalAmount(totalAmount)
            .externalSystem(externalSystem)
            .creditNotes(creditNotes)
            .reason(reason)
            .type(type)
            .recordType(recordType)
            .syncedTime(syncedTime)
            .build()
    }

    fun createJpaCreditNote(
        id: Long? = null,
        externalId: String = "CN-2024-001",
        status: CreditNoteStatus = CreditNoteStatus.DRAFT,
        currencyCode: CurrencyCode = CurrencyCode.USD,
        amountTotal: Double = 1000.0,
        amountApplied: Double = 500.0,
        amountUnapplied: Double = 500.0,
        reference: String = "Reference-2024",
        companyId: Long = 123L,
        reason: CreditNoteReason = CreditNoteReason.INVOICE_CORRECTION,
        externalSystem: ExternalSystem = ExternalSystem.XERO,
        creditNoteNo: String = "CN-2024-001",
        month: Int = 1,
        year: Int = 2024,
        appliedInvoices: Set<JpaInvoice> = emptySet(), // this is a loop so we don't repeat it here
        createdFromInvoiceId: Long? = null,
        lineItems: List<JpaCreditNoteLineItem> = listOf(createJpaCreditNoteLineItem()),
        companyPayableId: Long? = null,
        syncedTime: LocalDateTime = LocalDateTime.of(2024, 1, 1, 0, 0),
    ): JpaCreditNote {
        return JpaCreditNote.builder()
            .id(id)
            .externalId(externalId)
            .status(status)
            .currencyCode(currencyCode)
            .amountTotal(amountTotal)
            .amountApplied(amountApplied)
            .amountUnapplied(amountUnapplied)
            .reference(reference)
            .companyId(companyId)
            .reason(reason)
            .externalSystem(externalSystem)
            .creditNoteNo(creditNoteNo)
            .month(month)
            .year(year)
            .appliedInvoices(appliedInvoices)
            .createdFromInvoiceId(createdFromInvoiceId)
            .lineItems(lineItems)
            .companyPayableId(companyPayableId)
            .syncedTime(syncedTime)
            .build()
    }

    fun createJpaInvoiceLineItem(
        description: String = "Service Description",
        quantity: Double = 2.0,
        unitPrice: Double = 500.0,
        account: String = "Revenue",
        taxType: String = "GST",
        taxCode: String = "GST_CODE",
        taxRate: String = "10",
        taxAmount: Double = 100.0,
        contractId: Long = 123L,
        memberName: String = "John Doe",
        itemType: LineItemType = LineItemType.PEO_SALARY_DISBURSEMENT,
        baseCurrency: String = "USD",
        amountInBaseCurrency: Double = 1000.0,
        classValue: String = "Class 1",
        classDisplay: String = "Display 1",
        countryName: String = "India",
        grossAmount: Double = 1100.0,
    ): JpaInvoiceLineItem {
        return JpaInvoiceLineItem.builder()
            .description(description)
            .quantity(quantity)
            .unitPrice(unitPrice)
            .account(account)
            .taxType(taxType)
            .taxCode(taxCode)
            .taxRate(taxRate)
            .taxAmount(taxAmount)
            .contractId(contractId)
            .memberName(memberName)
            .itemType(itemType)
            .baseCurrency(baseCurrency)
            .amountInBaseCurrency(amountInBaseCurrency)
            .classValue(classValue)
            .classDisplay(classDisplay)
            .countryName(countryName)
            .grossAmount(grossAmount)
            .build()
    }

    fun createJpaCreditNoteLineItem(
        itemType: LineItemType = LineItemType.PEO_SALARY_DISBURSEMENT,
        description: String = "Credit Note Description",
        unitAmount: Double = 300.0,
        grossAmount: Double = 330.0,
        taxAmount: Double = 30.0,
        taxRate: String = "10",
        taxCode: String = "GST_CODE",
        countryName: String = "United States",
        contractId: Long? = 123L,
        memberName: String = "Jane Doe",
        baseCurrency: CurrencyCode = CurrencyCode.USD,
        amountInBaseCurrency: Double = 330.0,
        fxRate: Double = 1.0,
    ): JpaCreditNoteLineItem {
        return JpaCreditNoteLineItem.builder()
            .itemType(itemType)
            .description(description)
            .unitAmount(unitAmount)
            .grossAmount(grossAmount)
            .taxAmount(taxAmount)
            .taxRate(taxRate)
            .taxCode(taxCode)
            .countryName(countryName)
            .contractId(contractId)
            .memberName(memberName)
            .baseCurrency(baseCurrency)
            .amountInBaseCurrency(amountInBaseCurrency)
            .fxRate(fxRate)
            .build()
    }

    // PAYABLE ITEM SECTION
    fun createCompanyPayable(
        id: Long = 1000L,
        companyId: Long = 123L,
        items: List<PayableItem> = listOf(createPayableItem()),
        itemType: TransactionType = TransactionType.GP_FUNDING_INVOICE,
        transactionId: String = "TX123456789",
        status: PayableStatus = PayableStatus.DRAFT,
        month: Int = 1,
        year: Int = 2024,
    ): CompanyPayable {
        return CompanyPayable(
            id = id,
            companyId = companyId,
            items = items,
            itemType = itemType,
            transactionId = transactionId,
            status = status,
            month = month,
            year = year,
            entityId = 123L,
        )
    }

    fun createPayableItem(
        month: Int = 1,
        year: Int = 2024,
        itemType: String = "PEO_SALARY_DISBURSEMENT",
        contractId: Long? = 100L,
        companyId: Long? = 123L,
        description: String? = "Service Cost",
        amountInBaseCurrency: Double = 500.0,
        baseCurrency: String = "USD",
        billableCost: Double? = 400.0,
        versionId: String? = "v1.0",
        originalTimestamp: Long = 123456789L,
        cycle: InvoiceCycle = InvoiceCycle.MONTHLY,
        countryCode: String? = "USA",
        itemCount: Int = 1,
        periodStartDate: LocalDate? = LocalDate.of(2024, 1, 1),
        periodEndDate: LocalDate? = LocalDate.of(2024, 1, 31),
        contractDepartment: ContractDepartment? = null,
        annualSeatPaymentTerm: AnnualSeatPaymentTerm? = mockDefaultAnnualSeatPaymentTerm(),
        deposit: Deposit? = Deposit(
            type = DepositType.SALARY,
            amount = 42.0,
            currencyCode = com.multiplier.payable.engine.currency.CurrencyCode.USD,
            contractType = ContractType.EMPLOYEE
        ),
        insuranceType: String? = null,
        payrollCycleDateRange: DateRange? = null,
    ): PayableItem {
        return PayableItem(
            month = month,
            year = year,
            lineItemType = itemType,
            contractId = contractId,
            companyId = companyId,
            description = description,
            amountInBaseCurrency = amountInBaseCurrency,
            baseCurrency = baseCurrency,
            billableCost = billableCost,
            versionId = versionId,
            originalTimestamp = originalTimestamp,
            cycle = cycle,
            countryCode = countryCode ?: "USA",
            itemCount = itemCount,
            periodStartDate = periodStartDate,
            periodEndDate = periodEndDate,
            contractDepartment = contractDepartment,
            annualSeatPaymentTerm = annualSeatPaymentTerm,
            deposit = deposit,
            insuranceType = insuranceType,
            payrollCycleDateRange = payrollCycleDateRange
        )
    }

    fun createTransactionPayableItem(
        itemType: String = "PEO_SALARY_DISBURSEMENT",
        description: String = "Service Cost",
        contractId: Long = 100L,
        companyId: Long = 123L,
        amountInBaseCurrency: Double = 500.00,
        billableCost: Double? = 400.0,
        baseCurrency: String = "USD",
        cycle: String = "MONTHLY",
        countryCode: String = "USA",
        itemCount: Int = 1,
        periodStartDate: String = "2024-01-01",
        periodEndDate: String = "2024-01-31",
    ) = TransactionPayableItem().apply {
        this.lineItemType = itemType
        this.description = description
        this.contractId = contractId
        this.companyId = companyId
        this.amountInBaseCurrency = amountInBaseCurrency
        this.billableCost = billableCost
        this.baseCurrency = baseCurrency
        this.cycle = cycle
        this.countryCode = countryCode
        this.itemCount = itemCount
        this.periodStartDate = periodStartDate
        this.periodEndDate = periodEndDate
    }
}
