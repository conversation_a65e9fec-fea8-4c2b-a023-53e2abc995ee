package com.multiplier.payable.engine.transaction.template.graph

import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.invoicing.GraphToInternalDataMapper
import com.multiplier.payable.engine.transaction.template.v2.companytransactiontemplate.application.CompanyTransactionTemplateStoreUseCase
import com.multiplier.payable.engine.transaction.template.v2.companytransactiontemplate.application.CompanyTransactionTemplateUnAssignUseCase
import com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.application.TransactionTemplateStoreUseCase
import com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.application.TransactionTemplateUpdateUseCase
import com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.domain.TransactionTemplateEntity
import com.multiplier.payable.engine.transaction.template.v2.transactiontemplate.domain.TransactionTemplateId
import com.multiplier.payable.types.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.*
import org.mockito.kotlin.mock
import java.util.*

class TransactionTemplateMutatorTest {

    private val transactionTemplateStoreUseCase: TransactionTemplateStoreUseCase = mock()
    private val transactionTemplateUpdateUseCase: TransactionTemplateUpdateUseCase = mock()
    private val companyTransactionTemplateStoreUseCase: CompanyTransactionTemplateStoreUseCase = mock()
    private val companyTransactionTemplateUnAssignUseCase: CompanyTransactionTemplateUnAssignUseCase = mock()
    private val graphToInternalDataMapper: GraphToInternalDataMapper = mock()
    private val transactionTemplateEntityMapper: TransactionTemplateEntityMapper = mock()

    private val mutator = TransactionTemplateMutator(
        transactionTemplateStoreUseCase,
        transactionTemplateUpdateUseCase,
        companyTransactionTemplateStoreUseCase,
        companyTransactionTemplateUnAssignUseCase,
        graphToInternalDataMapper,
        transactionTemplateEntityMapper
    )

    @Test
    fun transactionTemplateCreateSuccessfullyCreatesTemplate() {
        val input = TransactionTemplateCreateInput(
            FinancialTransactionType.FIRST_INVOICE,
            "{}",
            "description",
            true,
        )
        val entity = mock<TransactionTemplateEntity>()
        val template = mock<TransactionTemplate>()

        `when`(graphToInternalDataMapper.map(input.transactionType)).thenReturn(TransactionType.FIRST_INVOICE)
        `when`(
            transactionTemplateStoreUseCase.execute(
                TransactionType.FIRST_INVOICE,
                input.isDefault,
                input.jsonConfig,
                input.description
            )
        ).thenReturn(entity)
        `when`(transactionTemplateEntityMapper.map(entity)).thenReturn(template)

        val result = mutator.transactionTemplateCreate(input)

        assertThat(result).isEqualTo(template)
    }

    @Test
    fun transactionTemplateAssignFailsWithException() {
        val input = TransactionTemplateAssignInput(
            UUID.randomUUID(), 123L, FinancialTransactionType.FIRST_INVOICE
        )

        `when`(graphToInternalDataMapper.map(input.transactionType)).thenReturn(TransactionType.FIRST_INVOICE)
        `when`(
            companyTransactionTemplateStoreUseCase.execute(
                TransactionTemplateId(input.transactionTemplateId),
                TransactionType.FIRST_INVOICE,
                input.companyId
            )
        ).thenThrow(RuntimeException("Error"))

        val result = mutator.transactionTemplateAssign(input)
        assertThat(result).isEqualTo(TaskResponse(false, "Error"))

    }

    @Test
    fun transactionTemplateAssignSuccessfullyAssignsTemplate() {
        val input = TransactionTemplateAssignInput(
            UUID.randomUUID(),
            123L, FinancialTransactionType.FIRST_INVOICE)

        val result = mutator.transactionTemplateAssign(input)

        assertThat(result).isEqualTo(TaskResponse(true, "Transaction template assigned successfully."))
    }
}