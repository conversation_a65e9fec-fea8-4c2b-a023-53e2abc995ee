package com.multiplier.payable.engine.collector.gross

import com.multiplier.core.payable.service.exception.EntityNotFoundException
import com.multiplier.payable.engine.contract.Contract
import com.multiplier.payable.engine.contract.ContractStatus
import com.multiplier.payable.engine.contract.ContractTerm
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.contract.CountryWorkStatus
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import java.math.BigDecimal

class HistoryAwareGrossSalaryEstimatorTest {
    private val salaryFetcher: PayrollSalaryFetcher = mock()
    private val grossSalaryEstimator: CompensationBasedGrossSalaryEstimator = mock()

    private val historyAwareGrossSalaryEstimator =
        HistoryAwareGrossSalaryEstimator(
            salaryFetcher = salaryFetcher,
            compensationBasedGrossSalaryEstimator = grossSalaryEstimator,
        )

    @Test
    fun `should fetch correctly`() {
        val companyId = 1L
        val invoiceMonthYear = MonthYear(4, 2024)
        val lookUpMonthYear = MonthYear(3, 2024)
        val firstContractCompensation = createData(1L, 100.0, CompensationRateFrequency.HOURLY)
        val secondContractCompensation = createData(2L, 200.0, CompensationRateFrequency.HOURLY)
        val thirdContractCompensation = createData(3L, 3000.0, CompensationRateFrequency.MONTHLY)

        // Define the expected output
        val expectedOutput =
            listOf(
                ContractGrossSalary(
                    companyId = 1,
                    contractId = 1L,
                    grossSalary = BigDecimal(5000.0),
                    currencyCode = CurrencyCode.USD,
                ),
                ContractGrossSalary(
                    companyId = 1,
                    contractId = 2L,
                    grossSalary = BigDecimal(6000.0),
                    currencyCode = CurrencyCode.USD,
                ),
            )

        // Stub the methods of the dependencies
        `when`(
            salaryFetcher.fetch(
                1L,
                contractIds = listOf(1L, 2L),
                monthYear = invoiceMonthYear,
            ),
        ).thenThrow(
            EntityNotFoundException(
                "No company payroll found for the company ids = $companyId, year = ${invoiceMonthYear.year}, month = ${invoiceMonthYear.month}",
            ),
        )
        `when`(
            salaryFetcher.fetch(
                1L,
                contractIds = listOf(1L, 2L),
                monthYear = lookUpMonthYear,
            ),
        ).thenReturn(
            listOf(
                ContractGrossSalary(
                    companyId = 1,
                    contractId = 1L,
                    grossSalary = BigDecimal(5000.0),
                    currencyCode = CurrencyCode.USD,
                ),
            ),
        )
        `when`(
            salaryFetcher.fetch(
                1L,
                contractIds = listOf(2L),
                monthYear = invoiceMonthYear.plusMonths(-3),
            ),
        ).thenReturn(
            listOf(
                ContractGrossSalary(
                    companyId = 1,
                    contractId = 2L,
                    grossSalary = BigDecimal(7000.0),
                    currencyCode = CurrencyCode.USD,
                ),
            ),
        )
        `when`(
            grossSalaryEstimator.estimate(
                companyId = 1L,
                invoiceMonthYear = invoiceMonthYear,
                compensations = listOf(secondContractCompensation),
                ignoreHourlyRate = false,
            ),
        ).thenReturn(
            listOf(
                ContractGrossSalary(
                    companyId = 1,
                    contractId = 2L,
                    grossSalary = BigDecimal(6000.0),
                    currencyCode = CurrencyCode.USD,
                ),
            ),
        )

        // Call the method with the input parameters
        val actualOutput =
            historyAwareGrossSalaryEstimator.estimate(
                companyId = 1,
                invoiceMonthYear = invoiceMonthYear,
                compensations = listOf(firstContractCompensation, secondContractCompensation, thirdContractCompensation),
            )

        // Assert that the returned value matches the expected output
        assertEquals(expectedOutput.toSet(), actualOutput.toSet())
    }

    @Test
    fun `should calculate if no payroll found`() {
        val companyId = 1L
        val invoiceMonthYear = MonthYear(4, 2024)
        val lookUpMonthYear = MonthYear(3, 2024)
        val secondMonthLookUpMonthYear = MonthYear(2, 2024)
        val firstContractCompensation = createData(1L, 100.0, CompensationRateFrequency.HOURLY)
        val secondContractCompensation = createData(2L, 200.0, CompensationRateFrequency.HOURLY)
        val thirdContractCompensation = createData(3L, 3000.0, CompensationRateFrequency.MONTHLY)

        // Define the expected output
        val expectedOutput =
            listOf(
                ContractGrossSalary(
                    companyId = 1,
                    contractId = 1L,
                    grossSalary = BigDecimal(1600.0),
                    currencyCode = CurrencyCode.USD,
                ),
                ContractGrossSalary(
                    companyId = 1,
                    contractId = 2L,
                    grossSalary = BigDecimal(3200.0),
                    currencyCode = CurrencyCode.USD,
                ),
            )

        // Stub the methods of the dependencies
        `when`(
            salaryFetcher.fetch(
                1L,
                contractIds = listOf(1L, 2L),
                monthYear = invoiceMonthYear,
            ),
        ).thenThrow(
            EntityNotFoundException(
                "No company payroll found for the company ids = $companyId, year = ${invoiceMonthYear.year}, month = ${invoiceMonthYear.month}",
            ),
        )
        `when`(
            salaryFetcher.fetch(
                1L,
                contractIds = listOf(1L, 2L),
                monthYear = lookUpMonthYear,
            ),
        ).thenThrow(
            EntityNotFoundException(
                "No company payroll found for the company ids = $companyId, year = ${lookUpMonthYear.year}, month = ${lookUpMonthYear.month}",
            ),
        )
        `when`(
            salaryFetcher.fetch(
                1L,
                contractIds = listOf(1L, 2L),
                monthYear = secondMonthLookUpMonthYear,
            ),
        ).thenThrow(
            EntityNotFoundException(
                "No company payroll found for the company ids = $companyId, year = ${lookUpMonthYear.year}, month = ${lookUpMonthYear.month}",
            ),
        )
        `when`(
            grossSalaryEstimator.estimate(
                companyId = 1L,
                invoiceMonthYear = invoiceMonthYear,
                compensations = listOf(firstContractCompensation, secondContractCompensation),
                ignoreHourlyRate = false,
            ),
        ).thenReturn(
            listOf(
                ContractGrossSalary(
                    companyId = 1,
                    contractId = 1L,
                    grossSalary = BigDecimal(1600.0),
                    currencyCode = CurrencyCode.USD,
                ),
                ContractGrossSalary(
                    companyId = 1,
                    contractId = 2L,
                    grossSalary = BigDecimal(3200.0),
                    currencyCode = CurrencyCode.USD,
                ),
            ),
        )

        // Call the method with the input parameters
        val actualOutput =
            historyAwareGrossSalaryEstimator.estimate(
                companyId = 1,
                invoiceMonthYear = invoiceMonthYear,
                compensations = listOf(firstContractCompensation, secondContractCompensation, thirdContractCompensation),
            )

        // Assert that the returned value matches the expected output
        assertEquals(expectedOutput.toSet(), actualOutput.toSet())
    }

    private fun createData(
        contractId: Long,
        amount: Double,
        rateFrequency: CompensationRateFrequency,
    ): ContractCompensation =
        ContractCompensation(
            contract =
                Contract(
                    id = contractId,
                    country = "USA",
                    type = ContractType.EMPLOYEE,
                    companyId = 1,
                    memberId = 1,
                    status = ContractStatus.ACTIVE,
                    term = ContractTerm.PERMANENT,
                    workStatus = CountryWorkStatus.RESIDENT,
                    currencyCode = CurrencyCode.USD,
                    legalEntityId = 1,
                ),
            amount = BigDecimal(amount),
            compensationRateType = CompensationRateType.GROSS,
            compensationRateFrequency = rateFrequency,
            compensationCurrencyCode = CurrencyCode.USD,
            monthYear = MonthYear(4, 2024),
            effectiveSalaryReview = null,
        )
}
