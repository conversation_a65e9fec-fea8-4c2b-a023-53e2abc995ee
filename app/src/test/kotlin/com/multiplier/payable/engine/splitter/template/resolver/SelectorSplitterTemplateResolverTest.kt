package com.multiplier.payable.engine.splitter.template.resolver

import com.multiplier.payable.engine.splitter.DefaultItemSplitter
import com.multiplier.payable.engine.splitter.SplitterConfiguration
import com.multiplier.payable.engine.splitter.exception.SelectorNotFoundException
import com.multiplier.payable.engine.splitter.template.SelectorSplitterTemplate
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.mock
import kotlin.test.assertTrue

class SelectorSplitterTemplateResolverTest {

    private var selectors = SplitterConfiguration(mock(), mock())
        .selectors()
    private var resolver: SelectorSplitterTemplateResolver = SelectorSplitterTemplateResolver(selectors)

    @Test
    fun `given with correct class name on template then resolve`() {
        // given
        val template = SelectorSplitterTemplate("com.multiplier.payable.engine.splitter.selector.CountrySelector")

        // when
        val result = resolver.resolve(listOf(template))

        // then
        assertTrue(result is DefaultItemSplitter)
    }

    @Test
    fun `given with incorrect class name on template then throw error`() {
        // given
        val template = SelectorSplitterTemplate("someRandomSelector")

        // when - then
        assertThrows<SelectorNotFoundException> { resolver.resolve(listOf(template)) }
    }

    @Test
    fun `given with no splitters defined on template should resolve properly`() {
        //given
        val result = resolver.resolve(listOf())

        //then
        assertTrue(result is DefaultItemSplitter)
    }
}
