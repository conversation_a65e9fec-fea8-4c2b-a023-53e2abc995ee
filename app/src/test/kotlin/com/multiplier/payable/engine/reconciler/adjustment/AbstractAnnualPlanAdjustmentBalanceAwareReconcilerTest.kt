package com.multiplier.payable.engine.reconciler.adjustment

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.ledger.AdvanceCollectionBalanceMetadata
import com.multiplier.payable.ledger.AdvanceCollectionLedger
import com.multiplier.payable.ledger.AdvanceCollectionReservedAmount
import com.multiplier.payable.ledger.domain.AdvanceCollectionBalance
import com.multiplier.payable.ledger.domain.AdvanceCollectionEntry
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLine
import com.multiplier.payable.ledger.domain.invoice.AdvanceCollectionInvoiceLineTaxReference
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.lenient
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.argThat
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoInteractions
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@ExtendWith(MockitoExtension::class)
class AbstractAnnualPlanAdjustmentBalanceAwareReconcilerTest {

    @Mock
    private lateinit var ledger: AdvanceCollectionLedger

    private lateinit var reconciler: AnnualPlanContractorAdjustmentBalanceAwareReconciler

    @BeforeEach
    fun setUp() {
        reconciler = AnnualPlanContractorAdjustmentBalanceAwareReconciler(ledger)
    }

    @Test
    fun `reconcile should return empty list when diffMainItems is empty`() {
        // Given
        val adjustments = listOf(createSimpleAdjustmentItem())
        emptyList<PayableItem>()

        whenever(ledger.hasBalance(any(), any(), any())).thenReturn(false)
        whenever(ledger.tryReserve(any(), any(), any(), any(), any())).thenReturn(
            AdvanceCollectionReservedAmount(reservedEntry = null, currentBalance = mock<AdvanceCollectionBalance>())
        )

        // When
        val result = reconciler.reconcile(adjustments)

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `reconcile should return empty list when no matching adjustment items`() {
        // Given
        val adjustments = listOf(createSimplePayableItem(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR))
        listOf(createSimpleMainItem())

        // When
        val result = reconciler.reconcile(adjustments)

        // Then
        assertTrue(result.isEmpty())
        verifyNoInteractions(ledger)
    }

    @Test
    fun `reconcile should throw exception when annualSeatPaymentTerm is null`() {
        // Given
        val adjustments = listOf(createSimpleAdjustmentItem())
        listOf(createPayableItem(LineItemType.ANNUAL_MANAGEMENT_FEE_AOR_CONTRACTOR, annualSeatPaymentTerm = null))

        whenever(ledger.hasBalance(any(), any(), any())).thenReturn(true)
        whenever(ledger.tryReserve(any(), any(), any(), any(), any())).thenReturn(
            AdvanceCollectionReservedAmount(reservedEntry = null, currentBalance = mock<AdvanceCollectionBalance>())
        )

        // When
        val result = reconciler.reconcile(adjustments)

        // Then
        assertTrue(result.isEmpty()) // The implementation doesn't throw exception, it just returns empty list
    }

    @Test
    fun `reconcile should filter adjustments by matching seat IDs`() {
        // Given
        val seatId1 = 100L
        val seatId2 = 200L
        val seatId3 = 300L

        val adjustments = listOf(
            createAdjustmentItem(seatId = seatId1),
            createAdjustmentItem(seatId = seatId2),
            createAdjustmentItem(seatId = seatId3)
        )
        val diffMainItems = listOf(
            createMainItem(seatId = seatId1),
            createMainItem(seatId = seatId2)
        )

        whenever(ledger.hasBalance(any(), any(), any())).thenReturn(true)
        whenever(ledger.tryReserve(any(), any(), any(), any(), any())).thenReturn(
            AdvanceCollectionReservedAmount(reservedEntry = null, currentBalance = mock<AdvanceCollectionBalance>())
        )

        // When
        val result = reconciler.reconcile(adjustments)

        // Then
        assertTrue(result.isEmpty()) // No reservation successful
        verify(ledger, times(3)).tryReserve(any(), any(), any(), any(), any()) // All 3 adjustments should be processed
    }

    @Test
    fun `reconcile should return empty list when no balance available`() {
        // Given
        val adjustments = listOf(createAdjustmentItem())
        listOf(createMainItem())

        whenever(ledger.hasBalance(any(), any(), any())).thenReturn(false)
        whenever(ledger.tryReserve(any(), any(), any(), any(), any())).thenReturn(
            AdvanceCollectionReservedAmount(reservedEntry = null, currentBalance = mock<AdvanceCollectionBalance>())
        )

        // When
        val result = reconciler.reconcile(adjustments)

        // Then
        assertTrue(result.isEmpty())
        verify(ledger).tryReserve(
            any(),
            any(),
            any(),
            any(),
            any()
        ) // The implementation still calls tryReserve even when hasBalance is false
    }

    @Test
    fun `reconcile should return empty list when reservation fails`() {
        // Given
        val adjustments = listOf(createAdjustmentItem())
        listOf(createMainItem())

        whenever(ledger.hasBalance(any(), any(), any())).thenReturn(true)
        whenever(ledger.tryReserve(any(), any(), any(), any(), any())).thenReturn(
            AdvanceCollectionReservedAmount(reservedEntry = null, currentBalance = mock<AdvanceCollectionBalance>())
        )

        // When
        val result = reconciler.reconcile(adjustments)

        // Then
        assertTrue(result.isEmpty())
        verify(ledger).tryReserve(
            eq("test-transaction-id"),
            eq(123L),
            eq(100L),
            any(),
            any()
        )
    }

    @Test
    fun `reconcile should return adjusted items when reservation succeeds`() {
        // Given
        val originalAmount = 1000.0
        val reservedAmount = 800.0
        val adjustments = listOf(createAdjustmentItem(amount = originalAmount))
        listOf(createMainItem())

        val mockTaxReference = mock<AdvanceCollectionInvoiceLineTaxReference>()
        lenient().`when`(mockTaxReference.taxCode).thenReturn("GST")

        val mockReferenceLine = mock<AdvanceCollectionInvoiceLine>()
        whenever(mockReferenceLine.taxReference).thenReturn(mockTaxReference)

        val metadata = AdvanceCollectionBalanceMetadata(
            transactionId = "test-transaction-id",
            invoiceNo = "INV-001",
            referenceLine = mockReferenceLine
        )
        val mockBalance = mock<AdvanceCollectionBalance>()
        whenever(mockBalance.metadata).thenReturn(metadata)

        whenever(ledger.hasBalance(any(), any(), any())).thenReturn(true)
        whenever(ledger.tryReserve(any(), any(), any(), any(), any())).thenReturn(
            AdvanceCollectionReservedAmount(
                reservedEntry = AdvanceCollectionEntry(
                    transactionId = "test-transaction-id",
                    balanceId = 1L,
                    amount = BigDecimal.valueOf(reservedAmount)
                ),
                currentBalance = mockBalance
            )
        )

        // When
        val result = reconciler.reconcile(adjustments)

        // Then
        assertEquals(1, result.size)
        assertEquals(reservedAmount, result[0].amountInBaseCurrency)
        verify(ledger).tryReserve(
            eq("test-transaction-id"),
            eq(123L),
            eq(100L),
            any(),
            eq(Amount(BigDecimal.valueOf(originalAmount), CurrencyCode.USD))
        )
    }

    @Test
    fun `reconcile should handle multiple adjustment items correctly`() {
        // Given
        val seatId1 = 100L
        val seatId2 = 200L
        val adjustments = listOf(
            createAdjustmentItem(seatId = seatId1, amount = 1000.0),
            createAdjustmentItem(seatId = seatId2, amount = 1500.0)
        )
        listOf(
            createMainItem(seatId = seatId1),
            createMainItem(seatId = seatId2)
        )

        val mockTaxReference1 = mock<AdvanceCollectionInvoiceLineTaxReference>()
        lenient().`when`(mockTaxReference1.taxCode).thenReturn("GST")

        val mockReferenceLine1 = mock<AdvanceCollectionInvoiceLine>()
        whenever(mockReferenceLine1.taxReference).thenReturn(mockTaxReference1)

        val metadata1 = AdvanceCollectionBalanceMetadata(
            transactionId = "test-transaction-id",
            invoiceNo = "INV-001",
            referenceLine = mockReferenceLine1
        )
        val mockBalance1 = mock<AdvanceCollectionBalance>()
        whenever(mockBalance1.metadata).thenReturn(metadata1)

        val mockTaxReference2 = mock<AdvanceCollectionInvoiceLineTaxReference>()
        lenient().`when`(mockTaxReference2.taxCode).thenReturn("GST")

        val mockReferenceLine2 = mock<AdvanceCollectionInvoiceLine>()
        whenever(mockReferenceLine2.taxReference).thenReturn(mockTaxReference2)

        val metadata2 = AdvanceCollectionBalanceMetadata(
            transactionId = "test-transaction-id",
            invoiceNo = "INV-002",
            referenceLine = mockReferenceLine2
        )
        val mockBalance2 = mock<AdvanceCollectionBalance>()
        whenever(mockBalance2.metadata).thenReturn(metadata2)

        whenever(ledger.hasBalance(any(), any(), any())).thenReturn(true)
        whenever(ledger.tryReserve(any(), any(), any(), any(), any()))
            .thenReturn(
                AdvanceCollectionReservedAmount(
                    reservedEntry = AdvanceCollectionEntry(
                        transactionId = "test-transaction-id",
                        balanceId = 1L,
                        amount = BigDecimal.valueOf(800.0)
                    ),
                    currentBalance = mockBalance1
                )
            )
            .thenReturn(
                AdvanceCollectionReservedAmount(
                    reservedEntry = AdvanceCollectionEntry(
                        transactionId = "test-transaction-id",
                        balanceId = 2L,
                        amount = BigDecimal.valueOf(1200.0)
                    ),
                    currentBalance = mockBalance2
                )
            )

        // When
        val result = reconciler.reconcile(adjustments)

        // Then
        assertEquals(2, result.size)
        assertEquals(800.0, result[0].amountInBaseCurrency)
        assertEquals(1200.0, result[1].amountInBaseCurrency)
        verify(ledger, times(2)).tryReserve(any(), any(), any(), any(), any())
    }

    @Test
    fun `reconcile should build correct advance collection product`() {
        // Given
        val adjustments = listOf(createAdjustmentItem(countryCode = "SG"))
        listOf(createMainItem())

        val mockTaxReference = mock<AdvanceCollectionInvoiceLineTaxReference>()
        lenient().`when`(mockTaxReference.taxCode).thenReturn("GST")

        val mockReferenceLine = mock<AdvanceCollectionInvoiceLine>()
        whenever(mockReferenceLine.taxReference).thenReturn(mockTaxReference)

        val metadata = AdvanceCollectionBalanceMetadata(
            transactionId = "test-transaction-id",
            invoiceNo = "INV-001",
            referenceLine = mockReferenceLine
        )
        val mockBalance = mock<AdvanceCollectionBalance>()
        whenever(mockBalance.metadata).thenReturn(metadata)

        whenever(ledger.hasBalance(any(), any(), any())).thenReturn(true)
        whenever(ledger.tryReserve(any(), any(), any(), any(), any())).thenReturn(
            AdvanceCollectionReservedAmount(
                reservedEntry = AdvanceCollectionEntry(
                    transactionId = "test-transaction-id",
                    balanceId = 1L,
                    amount = BigDecimal.valueOf(800.0)
                ),
                currentBalance = mockBalance
            )
        )

        // When
        reconciler.reconcile(adjustments)

        // Then
        verify(ledger).tryReserve(eq("test-transaction-id"), eq(123L), eq(100L), argThat { product ->
            product.lineCode == "CONTRACTOR_SERVICE_FEE" &&
                    product.dimensions["OFFERING"] == "AOR" &&
                    product.dimensions["COUNTRY"] == "SG" &&
                    product.targetType == "SUBSCRIPTION_PRODUCT"
        }, any())
    }

    private fun createAdjustmentItem(
        seatId: Long = 100L,
        amount: Double = 1000.0,
        countryCode: String = "US"
    ): PayableItem {
        return createPayableItem(
            LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR_CONTRACTOR,
            seatId = seatId,
            amount = amount,
            countryCode = countryCode,
            annualSeatPaymentTerm = createMockAnnualSeatPaymentTerm(seatId)
        )
    }

    private fun createMainItem(seatId: Long = 100L): PayableItem {
        return createPayableItem(
            LineItemType.ANNUAL_MANAGEMENT_FEE_AOR_CONTRACTOR,
            seatId = seatId,
            annualSeatPaymentTerm = createMockAnnualSeatPaymentTerm(seatId)
        )
    }

    private fun createMockAnnualSeatPaymentTerm(seatId: Long): com.multiplier.payable.engine.domain.aggregates.AnnualSeatPaymentTerm {
        return com.multiplier.payable.engine.domain.aggregates.AnnualSeatPaymentTerm(
            seatId = seatId,
            interval = 1,
            timeUnit = "MONTH",
            periodNumber = 1,
            periodCount = 12,
            planPeriod = DateRange(
                LocalDateTime.of(2024, 1, 1, 0, 0),
                LocalDateTime.of(2024, 12, 31, 23, 59)
            ),
            periodDateRange = DateRange(
                LocalDateTime.of(2024, 1, 1, 0, 0),
                LocalDateTime.of(2024, 12, 31, 23, 59)
            )
        )
    }


    private fun createSimpleAdjustmentItem(): PayableItem {
        return PayableItem(
            month = 1,
            year = 2024,
            lineItemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR_CONTRACTOR.name,
            companyId = 123L,
            entityId = 100L,
            transactionId = "test-transaction-id",
            amountInBaseCurrency = 1000.0,
            baseCurrency = "USD",
            countryCode = "US",
            originalTimestamp = System.currentTimeMillis(),
            cycle = InvoiceCycle.YEARLY,
            annualSeatPaymentTerm = null
        )
    }

    private fun createSimpleMainItem(): PayableItem {
        return PayableItem(
            month = 1,
            year = 2024,
            lineItemType = LineItemType.ANNUAL_MANAGEMENT_FEE_AOR_CONTRACTOR.name,
            companyId = 123L,
            entityId = 100L,
            transactionId = "test-transaction-id",
            amountInBaseCurrency = 1000.0,
            baseCurrency = "USD",
            countryCode = "US",
            originalTimestamp = System.currentTimeMillis(),
            cycle = InvoiceCycle.YEARLY,
            annualSeatPaymentTerm = null
        )
    }

    private fun createSimplePayableItem(lineItemType: LineItemType): PayableItem {
        return PayableItem(
            month = 1,
            year = 2024,
            lineItemType = lineItemType.name,
            companyId = 123L,
            entityId = 100L,
            transactionId = "test-transaction-id",
            amountInBaseCurrency = 1000.0,
            baseCurrency = "USD",
            countryCode = "US",
            originalTimestamp = System.currentTimeMillis(),
            cycle = InvoiceCycle.YEARLY,
            annualSeatPaymentTerm = null
        )
    }

    private fun createPayableItem(
        lineItemType: LineItemType,
        seatId: Long = 100L,
        amount: Double = 1000.0,
        countryCode: String = "US",
        annualSeatPaymentTerm: com.multiplier.payable.engine.domain.aggregates.AnnualSeatPaymentTerm? = null
    ): PayableItem {
        return PayableItem(
            month = 1,
            year = 2024,
            lineItemType = lineItemType.name,
            companyId = 123L,
            entityId = seatId,
            transactionId = "test-transaction-id",
            amountInBaseCurrency = amount,
            baseCurrency = "USD",
            countryCode = countryCode,
            originalTimestamp = System.currentTimeMillis(),
            cycle = InvoiceCycle.YEARLY,
            annualSeatPaymentTerm = annualSeatPaymentTerm
        )
    }

}
