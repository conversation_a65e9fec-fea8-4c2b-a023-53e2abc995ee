package com.multiplier.payable.engine.collector.core.benefit

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.schema.grpc.benefit.Benefit
import com.multiplier.core.util.dto.core.benefit.BenefitPackageCostWrapper
import com.multiplier.core.util.dto.core.benefit.InsuranceIndividualPremiumWrapper
import com.multiplier.core.util.dto.core.benefit.InsurancePremiumWrapper
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.types.Contract
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.mapstruct.factory.Mappers
import java.time.LocalDate
import kotlin.test.Test

class InsurancePremiumMapperTest {

    private val mapper =
        Mappers.getMapper(InsurancePremiumMapper::class.java)

    private val objectMapper = ObjectMapper().registerModule(JavaTimeModule())

    @Test
    fun `test happy path for mapping InsurancePremiumWrapper to PayableItemStoreDto`() {
        // GIVEN
        val key = createInsurancePremiumDataKey()
        val insurancePremiumWrapper = createInsurancePremiumWrapper()
        val transactionId = "defaultTransactionId"

        val expected = createPayableItemStoreDto(key = key)

        // WHEN
        val actualResult = mapper.map(transactionId, key, insurancePremiumWrapper, objectMapper)

        // THEN
        assertEquals(listOf(expected), actualResult)
    }

    private fun createPayableItemStoreDto(
        insurancePremiumWrapper: InsurancePremiumWrapper = createInsurancePremiumWrapper(),
        key: InsuranceDataKey = createInsurancePremiumDataKey(),
        transactionId: String = "defaultTransactionId",
    ): PayableItemStoreDto {
        return PayableItemStoreDto(
            amount = insurancePremiumWrapper.amountTotalCost,
            currency = insurancePremiumWrapper.self?.benefitPackageCost?.premiumCurrencyCode,
            companyId = key.companyId,
            contractId = insurancePremiumWrapper.contract.id,
            transactionId = transactionId,
            month = insurancePremiumWrapper.self?.endOn!!.monthValue!!,
            year = insurancePremiumWrapper.self?.endOn!!.year,
            itemType = LineItemType.INSURANCE_PREMIUM,
            itemData = objectMapper.writeValueAsString(insurancePremiumWrapper),
            periodStartDate = insurancePremiumWrapper.self?.startOn!!,
            periodEndDate = insurancePremiumWrapper.self?.endOn!!,
            versionId = key.computeHash(),
            originalTimestamp = key.oTime,
            countryCode = insurancePremiumWrapper.self?.benefitPackageCost?.countryCode?.name
        )
    }

    private fun createInsurancePremiumDataKey(
        companyId: Long = 456L,
        lineItemType: LineItemType = LineItemType.INSURANCE_PREMIUM,
        oTime: Long = 1707818424879L,
    ) = InsuranceDataKey(
        companyId = companyId,
        lineItemType = lineItemType,
        oTime = oTime,
    )

    private fun createInsurancePremiumWrapper(
        contractId: Long = 123L,
        amountTotalCost: Double = 1000.0,
        originalTimestamp: Long = 1707818424879L,
        companyId: Long = 456L,
        status: Benefit.ContractBenefitStatus = Benefit.ContractBenefitStatus.ACTIVE,
        benefitType: Benefit.BenefitType = Benefit.BenefitType.INSURANCE,
        billingDuration: Benefit.BenefitPartnerBillingFrequency = Benefit.BenefitPartnerBillingFrequency.BENEFIT_PARTNER_BILLING_FREQUENCY_MONTHLY,
        self: InsuranceIndividualPremiumWrapper = createInsuranceIndividualPremiumWrapper(),
        dependents: List<InsuranceIndividualPremiumWrapper> = listOf(createInsuranceIndividualPremiumWrapper()), // Populate as needed
        benefitName: String = "Custom Package Name by admin",
    ): InsurancePremiumWrapper {
        return InsurancePremiumWrapper(
            contract = Contract.newBuilder().id(contractId).build(),
            amountTotalCost = amountTotalCost,
            originalTimestamp = originalTimestamp,
            companyId = companyId,
            benefitType = benefitType,
            billingDuration = billingDuration,
            self = self,
            dependents = dependents,
            status = status,
            benefitName = benefitName,
        )
    }

    private fun createInsuranceIndividualPremiumWrapper(
        id: Long = 789L,
        firstName: String = "John",
        lastName: String = "Doe",
        startOn: LocalDate = LocalDate.of(2024, 1, 1),
        endOn: LocalDate = LocalDate.of(2024, 12, 31),
        billingPeriodInMonths: Int = 12,
        benefitPackageCost: BenefitPackageCostWrapper = createBenefitPackageCostWrapper(),
        subTotalAmount: Double = 1000.0,
        platformFee: Double = 100.0,
        subTotalPlatformFee: Double = 150.0,
    ): InsuranceIndividualPremiumWrapper {
        return InsuranceIndividualPremiumWrapper(
            id = id,
            firstName = firstName,
            lastName = lastName,
            startOn = startOn,
            endOn = endOn,
            billingPeriodInMonths = billingPeriodInMonths,
            benefitPackageCost = benefitPackageCost,
            subTotalAmount = subTotalAmount,
            platformFee = platformFee,
            subTotalPlatformFee = subTotalPlatformFee
        )
    }

    private fun createBenefitPackageCostWrapper(
        benefitPackageId: String = "BP123",
        ageLowerRange: Int = 25,
        ageHigherRange: Int = 45,
        cost: Double = 500.0,
        premium: Double = 50.0,
        countryCode: CountryCode = CountryCode.USA,
        premiumCurrencyCode: CurrencyCode = CurrencyCode.USD,
        premiumFrequency: Benefit.BenefitPremiumFrequency = Benefit.BenefitPremiumFrequency.BENEFIT_PREMIUM_FREQUENCY_MONTHLY,
        packageCostStatus: Benefit.BenefitPackageCostStatus = Benefit.BenefitPackageCostStatus.BENEFIT_PACKAGE_COST_STATUS_ACTIVE,
    ): BenefitPackageCostWrapper {
        return BenefitPackageCostWrapper(
            benefitPackageId = benefitPackageId,
            ageLowerRange = ageLowerRange,
            ageHigherRange = ageHigherRange,
            cost = cost,
            premium = premium,
            countryCode = countryCode,
            premiumCurrencyCode = premiumCurrencyCode,
            premiumFrequency = premiumFrequency,
            packageCostStatus = packageCostStatus
        )
    }
}