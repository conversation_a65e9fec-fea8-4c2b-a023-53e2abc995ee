package com.multiplier.payable.engine.reconciler.descriptionbuilder.adjustment.gp

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class StatFilingAdjustmentDescriptionBuilderTest {

    @InjectMocks
    private lateinit var builder: StatFilingAdjustmentDescriptionBuilder

    @Test
    fun `lineItemType should return ORDER_FORM_ADVANCE_ADJUSTMENT_STAT_FILING`() {
        // When
        val result = builder.lineItemType

        // Then
        assertThat(result).isEqualTo(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GP_STAT_FILING)
    }

    @Test
    fun `build should return correct description with all context values`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(1, 2024),
            countryCode = "US",
            currencyCode = "USD",
            amountInBaseCurrency = 350.0
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Statutory Payment Fee Advance Adjustment US: USD 350.0")
    }

    @Test
    fun `build should handle null countryCode`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(2, 2024),
            countryCode = null,
            currencyCode = "EUR",
            amountInBaseCurrency = 450.0
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Statutory Payment Fee Advance Adjustment N/A: EUR 450.0")
    }

    @Test
    fun `build should handle different currency codes`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(6, 2024),
            countryCode = "SG",
            currencyCode = "SGD",
            amountInBaseCurrency = 600.75
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Statutory Payment Fee Advance Adjustment SG: SGD 600.75")
    }

    @Test
    fun `build should handle zero amount`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(12, 2023),
            countryCode = "CA",
            currencyCode = "CAD",
            amountInBaseCurrency = 0.0
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Statutory Payment Fee Advance Adjustment CA: CAD 0.0")
    }

    @Test
    fun `build should handle negative amount`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(3, 2024),
            countryCode = "UK",
            currencyCode = "GBP",
            amountInBaseCurrency = -75.0
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Statutory Payment Fee Advance Adjustment UK: GBP -75.0")
    }

    @Test
    fun `build should handle empty countryCode`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(8, 2024),
            countryCode = "",
            currencyCode = "JPY",
            amountInBaseCurrency = 15000.0
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Statutory Payment Fee Advance Adjustment : JPY 15000.0")
    }

    @Test
    fun `build should handle large amounts`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(9, 2024),
            countryCode = "IN",
            currencyCode = "INR",
            amountInBaseCurrency = 999999.99
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Statutory Payment Fee Advance Adjustment IN: INR 999999.99")
    }

    @Test
    fun `build should handle decimal amounts`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(4, 2024),
            countryCode = "AU",
            currencyCode = "AUD",
            amountInBaseCurrency = 123.456
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Statutory Payment Fee Advance Adjustment AU: AUD 123.456")
    }

    @Test
    fun `build should handle very small amounts`() {
        // Given
        val context = PayableItemDescriptionBuilderContext(
            monthYear = MonthYear(5, 2024),
            countryCode = "CH",
            currencyCode = "CHF",
            amountInBaseCurrency = 0.01
        )

        // When
        val result = builder.build(context)

        // Then
        assertThat(result).isEqualTo("Statutory Payment Fee Advance Adjustment CH: CHF 0.01")
    }
}
