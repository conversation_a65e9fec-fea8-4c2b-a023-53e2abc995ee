package com.multiplier.payable.engine.reconciler.data.invoice

import com.multiplier.core.payable.adapters.NewPricingServiceAdapter
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.creditnote.database.CreditNoteService
import com.multiplier.core.payable.invoice.database.InvoiceService
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.payable.engine.domain.aggregates.AnnualSeatPaymentTerm
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.entities.AnnualSeatFee
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.testutils.CompanyPayableTestDataFactory
import com.multiplier.payable.engine.testutils.InvoiceEngineTestDataFactory
import com.multiplier.payable.engine.transaction.mapper.CompanyPayableMapper
import com.multiplier.payable.engine.transaction.mapper.LineItemTypeMapperByDescription
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import com.multiplier.payable.types.CompanyPayableType
import com.multiplier.payable.types.PayableStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.mockito.kotlin.eq
import java.time.LocalDateTime

@ExtendWith(MockitoExtension::class)
class SecondInvoiceDataProviderTest {

    @Mock
    private lateinit var transactionTemplateProvider: TransactionTemplateProvider

    @Mock
    private lateinit var pricingServiceAdapter: NewPricingServiceAdapter

    @Mock
    private lateinit var jpaCompanyPayableRepository: JpaCompanyPayableRepository

    @Mock
    private lateinit var mapper: CompanyPayableMapper

    @Mock
    private lateinit var invoiceService: InvoiceService

    @Mock
    private lateinit var creditNoteService: CreditNoteService

    @Mock
    private lateinit var lineItemTypeMapperByDescription: LineItemTypeMapperByDescription

    @InjectMocks
    private lateinit var secondInvoiceDataProvider: SecondInvoiceDataProvider

    @Test
    fun `transactionType should return SECOND_INVOICE`() {
        val result = secondInvoiceDataProvider.transactionType()
        assertEquals(TransactionType.SECOND_INVOICE, result)
    }

    @Test
    fun `fetchActiveInvoices should return empty main payables`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            transactionType = TransactionType.SECOND_INVOICE,
        )
        val mainTemplate: TransactionTemplate = mock {
            on { mergedTransactionTypes } doReturn listOf(TransactionType.ANNUAL_PLAN_INVOICE)
        }
        val apTemplate: TransactionTemplate = mock {}
        whenever(
            transactionTemplateProvider.findTemplateFor(
                TransactionType.SECOND_INVOICE,
                command.companyId
            )
        ).thenReturn(
            mainTemplate
        )
        whenever(
            transactionTemplateProvider.findTemplateFor(
                TransactionType.ANNUAL_PLAN_INVOICE,
                command.companyId
            )
        ).thenReturn(
            apTemplate
        )

        // no AP lines
        whenever(pricingServiceAdapter.getPlansWithinDuration(setOf(command.companyId), command.dateRange))
            .thenReturn(emptyList())

        // WHEN
        val result = secondInvoiceDataProvider.fetchActiveInvoices(command)

        // THEN
        assertEquals(emptyList<CompanyPayable>(), result)
    }

    @Test
    fun `fetchActiveInvoices should return merged payables correctly`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            transactionType = TransactionType.SECOND_INVOICE,
        )
        val mainTemplate: TransactionTemplate = mock {
            on { mergedTransactionTypes } doReturn listOf(TransactionType.ANNUAL_PLAN_INVOICE)
        }
        val apTemplate: TransactionTemplate = mock {
            on { lineItemTypes } doReturn listOf(LineItemType.ANNUAL_MANAGEMENT_FEE_EOR)
        }
        whenever(
            transactionTemplateProvider.findTemplateFor(
                TransactionType.SECOND_INVOICE,
                command.companyId
            )
        ).thenReturn(
            mainTemplate
        )
        whenever(
            transactionTemplateProvider.findTemplateFor(
                TransactionType.ANNUAL_PLAN_INVOICE,
                command.companyId
            )
        ).thenReturn(
            apTemplate
        )

        // AP has only 1 seat, duration: [Oct, Nov]
        val octToNovAPTerm: AnnualSeatPaymentTerm = mock {
            on { periodDateRange } doReturn DateRange(
                startDate = LocalDateTime.of(2024, 10, 1, 0, 0),
                endDate = LocalDateTime.of(2024, 11, 30, 0, 0),
            )
        }
        val mockPlan: AnnualSeatFee = mock {
            on { annualSeatPaymentTerm } doReturn octToNovAPTerm
            on { isEor() } doReturn true
        }
        whenever(pricingServiceAdapter.getPlansWithinDuration(setOf(command.companyId), command.dateRange)).thenReturn(
            listOf(mockPlan)
        )

        val mergedSalaryJpaCompanyPayable: JpaCompanyPayable = mock {
            on { id } doReturn 1000L
        }
        whenever(
            jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
                command.companyId,
                2024,
                10,
                CompanyPayableType.SECOND_INVOICE,
                setOf(PayableStatus.VOIDED, PayableStatus.DELETED)
            )
        ).thenReturn(listOf(mergedSalaryJpaCompanyPayable))

        // 1 merge item in 2nd Oct invoice
        val salaryLineItem: PayableItem = mock {
            on { lineItemType } doReturn "EOR_SALARY_DISBURSEMENT"
        }
        val apLineItem: PayableItem = mock {
            on { lineItemType } doReturn "ANNUAL_MANAGEMENT_FEE_EOR"
        }
        val mergedSalaryCompanyPayable = CompanyPayableTestDataFactory.createCompanyPayable(
            items = listOf(salaryLineItem, apLineItem),
        )

        val validExistingInvoice = InvoiceDTO.builder()
            .companyPayableId(mergedSalaryCompanyPayable.id)
            .build()
        whenever(invoiceService.getActiveInvoices(any())).thenReturn(
            listOf(validExistingInvoice)
        )

        whenever(mapper.mapCompanyPayable(mergedSalaryJpaCompanyPayable)).thenReturn(
            mergedSalaryCompanyPayable
        )

        // WHEN
        val result = secondInvoiceDataProvider.fetchActiveInvoices(command)

        // THEN
        // contains only ap item
        assertThat(result.size).isEqualTo(1)
        assertThat(result.first().items.size).isEqualTo(1)
        assertThat(result.first().items.first().lineItemType).isEqualTo("ANNUAL_MANAGEMENT_FEE_EOR")
    }

    @Test
    fun `fetchActiveInvoices should not return any payable if those cannot be mapped`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            transactionType = TransactionType.SECOND_INVOICE,
        )
        val mainTemplate: TransactionTemplate = mock {
            on { mergedTransactionTypes } doReturn listOf(TransactionType.ANNUAL_PLAN_INVOICE)
        }
        val apTemplate: TransactionTemplate = mock {
            on { lineItemTypes } doReturn listOf(LineItemType.ANNUAL_MANAGEMENT_FEE_EOR)
        }
        whenever(
            transactionTemplateProvider.findTemplateFor(
                TransactionType.SECOND_INVOICE,
                command.companyId
            )
        ).thenReturn(mainTemplate)
        whenever(
            transactionTemplateProvider.findTemplateFor(
                TransactionType.ANNUAL_PLAN_INVOICE,
                command.companyId
            )
        ).thenReturn(apTemplate)

        val octToNovAPTerm: AnnualSeatPaymentTerm = mock {
            on { periodDateRange } doReturn DateRange(
                startDate = LocalDateTime.of(2024, 10, 1, 0, 0),
                endDate = LocalDateTime.of(2024, 11, 30, 0, 0),
            )
        }
        val mockPlan: AnnualSeatFee = mock {
            on { annualSeatPaymentTerm } doReturn octToNovAPTerm
            on { isEor() } doReturn true
        }
        whenever(pricingServiceAdapter.getPlansWithinDuration(setOf(command.companyId), command.dateRange)).thenReturn(
            listOf(mockPlan)
        )

        val jpaPayable: JpaCompanyPayable = mock {
            on { id } doReturn 1001L
        }
        whenever(
            jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
                command.companyId,
                2024,
                10,
                CompanyPayableType.SECOND_INVOICE,
                setOf(PayableStatus.VOIDED, PayableStatus.DELETED)
            )
        ).thenReturn(listOf(jpaPayable))

        whenever(mapper.mapCompanyPayable(jpaPayable)).thenThrow(RuntimeException("Mapping exception"))

        val foundInvoice = mock<InvoiceDTO>()
        whenever(foundInvoice.companyPayableId).thenReturn(1001L)
        whenever(invoiceService.getActiveInvoices(any())).thenReturn(listOf(foundInvoice))
        whenever(creditNoteService.getActiveCreditNote(any(), any())).thenReturn(emptyList())

        // WHEN
        val result = secondInvoiceDataProvider.fetchActiveInvoices(command)

        // THEN
        verify(mapper).mapCompanyPayable(jpaPayable)
        assertThat(result).isEmpty()
    }

    @Test
    fun `test when allow multiple invoice then fetch old SECOND_INVOICE too`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            transactionType = TransactionType.SECOND_INVOICE,
        )
        val mainTemplate: TransactionTemplate = mock {
            on { mergedTransactionTypes } doReturn listOf(TransactionType.ANNUAL_PLAN_INVOICE)
            on {allowMultipleInvoiceSameMonth} doReturn true
        }
        val apTemplate: TransactionTemplate = mock {
            on { lineItemTypes } doReturn listOf(LineItemType.ANNUAL_MANAGEMENT_FEE_EOR)
        }
        whenever(
            transactionTemplateProvider.findTemplateFor(
                TransactionType.SECOND_INVOICE,
                command.companyId
            )
        ).thenReturn(mainTemplate)
        whenever(
            transactionTemplateProvider.findTemplateFor(
                TransactionType.ANNUAL_PLAN_INVOICE,
                command.companyId
            )
        ).thenReturn(apTemplate)

        val octToNovAPTerm: AnnualSeatPaymentTerm = mock {
            on { periodDateRange } doReturn DateRange(
                startDate = LocalDateTime.of(2024, 10, 1, 0, 0),
                endDate = LocalDateTime.of(2024, 11, 30, 0, 0),
            )
        }
        val mockPlan: AnnualSeatFee = mock {
            on { annualSeatPaymentTerm } doReturn octToNovAPTerm
            on { isEor() } doReturn true
        }
        whenever(pricingServiceAdapter.getPlansWithinDuration(setOf(command.companyId), command.dateRange)).thenReturn(
            listOf(mockPlan)
        )

        val jpaPayable: JpaCompanyPayable = mock {}
        val jpaPayableWithSameTransactionId: JpaCompanyPayable = mock {
            on { transactionId } doReturn command.transactionId
        }
        whenever(
            jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
                command.companyId,
                2024,
                1,
                CompanyPayableType.SECOND_INVOICE,
                setOf(PayableStatus.VOIDED, PayableStatus.DELETED)
            )
        ).thenReturn(listOf(jpaPayable, jpaPayableWithSameTransactionId))

        val mergedSalaryCompanyPayable = CompanyPayableTestDataFactory.createCompanyPayable()
        whenever(mapper.mapCompanyPayable(jpaPayable)).thenReturn(mergedSalaryCompanyPayable)

        val foundInvoice = mock<InvoiceDTO>()
        whenever(foundInvoice.companyPayableId).thenReturn(1001L)
        whenever(invoiceService.getActiveInvoices(any())).thenReturn(listOf(foundInvoice))
        whenever(creditNoteService.getActiveCreditNote(any(), any())).thenReturn(emptyList())
        whenever(lineItemTypeMapperByDescription.map(any(), any())).thenAnswer { invocation ->
            val lineItemType = invocation.getArgument<LineItemType>(0)
            lineItemType
        }
        val expectedPayableItems = mergedSalaryCompanyPayable.items.map { it.copy(isBilled = true) }
        val expectedPayableResult = mergedSalaryCompanyPayable.copy(
            items =  expectedPayableItems
        )

        // WHEN
        val result = secondInvoiceDataProvider.fetchActiveInvoices(command)

        // THEN
        verify(mapper).mapCompanyPayable(jpaPayable)
        assertEquals(listOf(expectedPayableResult), result)
    }

    @Test
    fun `fetchSecondInvoicePayables should map line item types based on description`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            transactionType = TransactionType.SECOND_INVOICE,
        )
        val mainTemplate: TransactionTemplate = mock {
            on { mergedTransactionTypes } doReturn emptyList<TransactionType>()
            on { allowMultipleInvoiceSameMonth } doReturn true
        }
        whenever(
            transactionTemplateProvider.findTemplateFor(
                TransactionType.SECOND_INVOICE,
                command.companyId
            )
        ).thenReturn(mainTemplate)

        val jpaPayable: JpaCompanyPayable = mock {}
        whenever(
            jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
                command.companyId,
                command.getMonthYearByStartDate().year,
                command.getMonthYearByStartDate().month,
                CompanyPayableType.SECOND_INVOICE,
                setOf(PayableStatus.VOIDED, PayableStatus.DELETED)
            )
        ).thenReturn(listOf(jpaPayable))

        // Create a payable item with a description that will be mapped
        val payableItem = CompanyPayableTestDataFactory.createPayableItem(
            itemType = LineItemType.EOR_SALARY_DISBURSEMENT.name,
            description = "Gross Wages: CAD 1000.00"
        )

        val companyPayable = CompanyPayableTestDataFactory.createCompanyPayable(
            id = 1L,
            items = listOf(payableItem)
        )
        whenever(mapper.mapCompanyPayable(jpaPayable)).thenReturn(companyPayable)

        // Mock the line item type mapper to return a different line item type
        whenever(lineItemTypeMapperByDescription.map(eq(LineItemType.EOR_SALARY_DISBURSEMENT), eq("Gross Wages: CAD 1000.00")))
            .thenReturn(LineItemType.CANADA_GROSS_WAGES)

        // WHEN
        val result = secondInvoiceDataProvider.fetchActiveInvoices(command)

        // THEN
        assertThat(result).hasSize(1)
        assertThat(result[0].items).hasSize(1)
        assertThat(result[0].items[0].lineItemType).isEqualTo(LineItemType.CANADA_GROSS_WAGES.name)
        assertThat(result[0].items[0].isBilled).isTrue()
    }

    @Test
    fun `fetchSecondInvoicePayables should not change line item type when description doesn't match`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            transactionType = TransactionType.SECOND_INVOICE,
        )
        val mainTemplate: TransactionTemplate = mock {
            on { mergedTransactionTypes } doReturn emptyList<TransactionType>()
            on { allowMultipleInvoiceSameMonth } doReturn true
        }
        whenever(
            transactionTemplateProvider.findTemplateFor(
                TransactionType.SECOND_INVOICE,
                command.companyId
            )
        ).thenReturn(mainTemplate)

        val jpaPayable: JpaCompanyPayable = mock {}
        whenever(
            jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
                command.companyId,
                command.getMonthYearByStartDate().year,
                command.getMonthYearByStartDate().month,
                CompanyPayableType.SECOND_INVOICE,
                setOf(PayableStatus.VOIDED, PayableStatus.DELETED)
            )
        ).thenReturn(listOf(jpaPayable))

        // Create a payable item with a description that won't be mapped
        val payableItem = CompanyPayableTestDataFactory.createPayableItem(
            itemType = LineItemType.EOR_SALARY_DISBURSEMENT.name,
            description = "Some other description"
        )

        val companyPayable = CompanyPayableTestDataFactory.createCompanyPayable(
            id = 2L,
            items = listOf(payableItem)
        )
        whenever(mapper.mapCompanyPayable(jpaPayable)).thenReturn(companyPayable)

        // Mock the line item type mapper to return the same line item type
        whenever(lineItemTypeMapperByDescription.map(eq(LineItemType.EOR_SALARY_DISBURSEMENT), eq("Some other description")))
            .thenReturn(LineItemType.EOR_SALARY_DISBURSEMENT)

        // WHEN
        val result = secondInvoiceDataProvider.fetchActiveInvoices(command)

        // THEN
        assertThat(result).hasSize(1)
        assertThat(result[0].items).hasSize(1)
        assertThat(result[0].items[0].lineItemType).isEqualTo(LineItemType.EOR_SALARY_DISBURSEMENT.name)
        assertThat(result[0].items[0].isBilled).isTrue()
    }
}
