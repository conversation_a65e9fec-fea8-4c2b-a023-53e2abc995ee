package com.multiplier.payable.engine.collector.gross

import com.multiplier.core.payable.service.exception.ValidationException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.math.MathContext
import java.util.stream.Stream

class CompensationRateFrequencyToDivisionFactorMapperKtTest {
    companion object {
        @JvmStatic
        private fun getFactorBasedOnFrequencyOfCompensation(): Stream<Arguments> =
            Stream.of(
                Arguments.of("MONTHLY", BigDecimal.ONE),
                Arguments.of("ANNUALLY", BigDecimal(12)),
                Arguments.of("HALFYEARLY", BigDecimal(6)),
                Arguments.of("TRI_ANNUALLY", BigDecimal(4)),
                Arguments.of("QUATERLY", BigDecimal(3)),
                Arguments.of("BI_MONTHLY", BigDecimal(2)),
                Arguments.of(
                    "SEMIMONTHLY",
                    BigDecimal.ONE.divide(
                        BigDecimal(2),
                        MathContext.DECIMAL64,
                    ),
                ),
                Arguments.of(
                    "WEEKLY",
                    BigDecimal.ONE.divide(
                        BigDecimal(4),
                        MathContext.DECIMAL64,
                    ),
                ),
                Arguments.of(
                    "DAILY",
                    BigDecimal.ONE.divide(
                        BigDecimal(30),
                        MathContext.DECIMAL64,
                    ),
                ),
                Arguments.of(
                    "HOURLY",
                    BigDecimal.ONE.divide(
                        BigDecimal(160),
                        MathContext.DECIMAL64,
                    ),
                ),
            )
    }

    @ParameterizedTest
    @MethodSource("getFactorBasedOnFrequencyOfCompensation")
    fun `should get factor based on frequency of compensation`(
        rateFrequency: String,
        expectedFactor: BigDecimal,
    ) {
        // When
        val factor =
            mapCompensationRateFrequencyToDivisionFactor(CompensationRateFrequency.valueOf(rateFrequency))

        // Then
        assertEquals(expectedFactor, factor)
    }

    @Test
    fun `should throw for unrecognised`() {
        assertThrows<ValidationException> {
            mapCompensationRateFrequencyToDivisionFactor(
                CompensationRateFrequency.UNRECOGNIZED,
            )
        }
    }
}
