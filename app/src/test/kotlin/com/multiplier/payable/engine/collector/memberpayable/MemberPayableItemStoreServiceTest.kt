package com.multiplier.payable.engine.collector.memberpayable

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.util.toLocalDateInUTC
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.domain.aggregates.MonthYearDuration
import com.multiplier.payable.engine.memberpayable.MemberPayable
import com.multiplier.payable.engine.memberpayable.MemberPayableType
import com.multiplier.payable.engine.memberpayable.managmentfee.MemberPayableManagementFeeBill
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.types.Amount
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Captor
import org.mockito.Mock
import org.mockito.Mockito.mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.verify
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime

@ExtendWith(MockitoExtension::class)
class MemberPayableItemStoreServiceTest {

    @Mock
    private lateinit var payableItemStoreService: PayableItemStoreService

    private val objectMapper: ObjectMapper = jacksonObjectMapper().registerModule(KotlinModule.Builder().build())
        .registerModule(JavaTimeModule())
        .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)

    private lateinit var memberPayableItemStoreService: MemberPayableItemStoreService

    @Captor
    private lateinit var payableItemStoreDtoCaptorList: org.mockito.ArgumentCaptor<List<PayableItemStoreDto>>

    private val companyId = 100L
    private val fetchedTime = Instant.now()
    val transactionId = ""
    val itemType = LineItemType.PAYMENT_FEE

    @BeforeEach
    fun setUp() {
        memberPayableItemStoreService = MemberPayableItemStoreService(
            payableItemStoreService = payableItemStoreService,
            objectMapper = objectMapper
        )
    }

    @Nested
    inner class NormalizeAndStore {
        @Test
        fun `test storeMemberPayable`() {
            val memberPayableId1 = 1L
            val submittedAt1 = LocalDateTime.of(2023, 2, 10, 0, 0).toInstant(java.time.ZoneOffset.UTC)
            val contractId1 = 1L
            val amountValue = 100.00
            val countryCode1 = CountryCode.USA

            val memberPayableId2 = 2L
            val submittedAt2 = LocalDateTime.of(2023, 1, 10, 0, 0).toInstant(java.time.ZoneOffset.UTC)
            val contractId2 = 2L
            val amountValue2 = 200.00
            val countryCode2 = CountryCode.CAN

            val memberPayable1 = getMemberPayable(
                id = memberPayableId1,
                contractId = contractId1,
                amountValue = amountValue,
                countryCode = countryCode1,
                submittedAt = submittedAt1,
            )

            val memberPayable2 = getMemberPayable(
                id = memberPayableId2,
                contractId = contractId2,
                amountValue = amountValue2,
                countryCode = countryCode2,
                submittedAt = submittedAt2,
            )

            val amount1 = Amount(amountValue, CurrencyCode.USD)
            val amount2 = Amount(amountValue2, CurrencyCode.GBP)

            val monthYearDuration = MonthYearDuration(MonthYear(3, 2025), MonthYear(3, 2025))

            memberPayableItemStoreService.normalizeAndSave(
                transactionId = transactionId,
                memberPayableAmountMap = mapOf(
                    memberPayable1 to amount1,
                    memberPayable2 to amount2
                ),
                monthYearDuration = monthYearDuration,
                type = LineItemType.PAYMENT_FEE,
            )

            verify(payableItemStoreService).saveAndIgnoreDuplication(payableItemStoreDtoCaptorList.capture())

            val payableItemStoreValues = payableItemStoreDtoCaptorList.value
            val payableItemStore1 = payableItemStoreValues.first()
            val payableItemStore2 = payableItemStoreValues.last()


            assertPayableItemStore(
                memberPayable = memberPayable1,
                amount = amount1,
                payableItemStore = payableItemStore1,
                monthYearDuration = monthYearDuration,
                hashCode = MemberPayableKey(
                    companyId = companyId,
                    lineItemType = LineItemType.PAYMENT_FEE,
                    oTime = fetchedTime.toEpochMilli(),
                ).computeHash(),
                startDate = memberPayable1.getStartDate(),
                endDate = memberPayable1.getEndDate(),
            )

            assertPayableItemStore(
                memberPayable = memberPayable2,
                amount = amount2,
                payableItemStore = payableItemStore2,
                monthYearDuration = monthYearDuration,
                hashCode = MemberPayableKey(
                    companyId = companyId,
                    lineItemType = LineItemType.PAYMENT_FEE,
                    oTime = fetchedTime.toEpochMilli(),
                ).computeHash(),
                startDate = memberPayable2.getStartDate(),
                endDate = memberPayable2.getEndDate(),
            )
        }
    }

    @Nested
    inner class NormalizeAndStoreBills {

        @Test
        fun `test normalize and store bills`() {
            val memberPayableId1 = 1L
            val submittedAt1 = LocalDateTime.of(2023, 2, 10, 0, 0).toInstant(java.time.ZoneOffset.UTC)
            val contractId1 = 1L
            val amountValue = 100.00
            val countryCode1 = CountryCode.USA

            val memberPayableId2 = 2L
            val submittedAt2 = LocalDateTime.of(2023, 1, 10, 0, 0).toInstant(java.time.ZoneOffset.UTC)
            val contractId2 = 2L
            val amountValue2 = 200.00
            val countryCode2 = CountryCode.CAN

            val memberPayable1 = getMemberPayable(
                id = memberPayableId1,
                contractId = contractId1,
                amountValue = amountValue,
                countryCode = countryCode1,
                submittedAt = submittedAt1,
            )

            val memberPayable2 = getMemberPayable(
                id = memberPayableId2,
                contractId = contractId2,
                amountValue = amountValue2,
                countryCode = countryCode2,
                submittedAt = submittedAt2,
            )
            val billAmount1 = Amount(10.00, CurrencyCode.USD)
            val billAmount2 = Amount(20.00, CurrencyCode.USD)
            val billAmount3 = Amount(30.00, CurrencyCode.USD)
            val billAmount4 = Amount(40.00, CurrencyCode.USD)
            val billStartDate1 = Instant.now()
            val billStartDate2 = Instant.ofEpochMilli(1740787200) //1st March, 2025
            val billEndDate1 = Instant.ofEpochMilli(1743465600) //1st April, 2025
            val billEndDate2 = Instant.ofEpochMilli(1738368000) //1st Feb, 2025

            val memberPayableManagementFeeBill1 = mock<MemberPayableManagementFeeBill>()
            val memberPayableManagementFeeBill2 = mock<MemberPayableManagementFeeBill>()
            val memberPayableManagementFeeBill3 = mock<MemberPayableManagementFeeBill>()
            val memberPayableManagementFeeBill4 = mock<MemberPayableManagementFeeBill>()

            doReturn(billAmount1).`when`(memberPayableManagementFeeBill1).amount
            doReturn(billAmount2).`when`(memberPayableManagementFeeBill2).amount
            doReturn(billAmount3).`when`(memberPayableManagementFeeBill3).amount
            doReturn(billAmount4).`when`(memberPayableManagementFeeBill4).amount
            doReturn(billStartDate1).`when`(memberPayableManagementFeeBill1).startDate
            doReturn(billStartDate2).`when`(memberPayableManagementFeeBill2).startDate
            doReturn(billStartDate1).`when`(memberPayableManagementFeeBill3).startDate
            doReturn(billStartDate2).`when`(memberPayableManagementFeeBill4).startDate
            doReturn(billEndDate1).`when`(memberPayableManagementFeeBill1).endDate
            doReturn(billEndDate2).`when`(memberPayableManagementFeeBill2).endDate
            doReturn(billEndDate1).`when`(memberPayableManagementFeeBill3).endDate
            doReturn(billEndDate2).`when`(memberPayableManagementFeeBill4).endDate

            val monthYearDuration = MonthYearDuration(MonthYear(3, 2025), MonthYear(3, 2025))

            memberPayableItemStoreService.normalizeAndSaveBills(
                transactionId = transactionId,
                memberPayableBillingMap = mapOf(
                    memberPayable1 to listOf(memberPayableManagementFeeBill1, memberPayableManagementFeeBill2),
                    memberPayable2 to listOf(memberPayableManagementFeeBill3, memberPayableManagementFeeBill4),
                ),
                monthYearDuration = monthYearDuration,
                type = LineItemType.PAYMENT_FEE,
            )

            verify(payableItemStoreService).saveAndIgnoreDuplication(payableItemStoreDtoCaptorList.capture())

            val payableItemStoreValues = payableItemStoreDtoCaptorList.value
            val payableItemStore1 = payableItemStoreValues[0]
            val payableItemStore2 = payableItemStoreValues[1]
            val payableItemStore3 = payableItemStoreValues[2]
            val payableItemStore4 = payableItemStoreValues[3]

            assertPayableItemStore(
                memberPayable = memberPayable1,
                amount = billAmount1,
                payableItemStore = payableItemStore1,
                monthYearDuration = monthYearDuration,
                hashCode = MemberPayableKey(
                    companyId = companyId,
                    lineItemType = LineItemType.PAYMENT_FEE,
                    oTime = fetchedTime.toEpochMilli(),
                ).computeHash(),
                startDate = billStartDate1.toLocalDateInUTC(),
                endDate = billEndDate1.toLocalDateInUTC(),
            )

            assertPayableItemStore(
                memberPayable = memberPayable1,
                amount = billAmount2,
                payableItemStore = payableItemStore2,
                monthYearDuration = monthYearDuration,
                hashCode = MemberPayableKey(
                    companyId = companyId,
                    lineItemType = LineItemType.PAYMENT_FEE,
                    oTime = fetchedTime.toEpochMilli(),
                ).computeHash(),
                startDate = billStartDate2.toLocalDateInUTC(),
                endDate = billEndDate2.toLocalDateInUTC(),
            )

            assertPayableItemStore(
                memberPayable = memberPayable2,
                amount = billAmount3,
                payableItemStore = payableItemStore3,
                monthYearDuration = monthYearDuration,
                hashCode = MemberPayableKey(
                    companyId = companyId,
                    lineItemType = LineItemType.PAYMENT_FEE,
                    oTime = fetchedTime.toEpochMilli(),
                ).computeHash(),
                startDate = billStartDate1.toLocalDateInUTC(),
                endDate = billEndDate1.toLocalDateInUTC()
            )

            assertPayableItemStore(
                memberPayable = memberPayable2,
                amount = billAmount4,
                payableItemStore = payableItemStore4,
                monthYearDuration = monthYearDuration,
                hashCode = MemberPayableKey(
                    companyId = companyId,
                    lineItemType = LineItemType.PAYMENT_FEE,
                    oTime = fetchedTime.toEpochMilli(),
                ).computeHash(),
                startDate = billStartDate2.toLocalDateInUTC(),
                endDate = billEndDate2.toLocalDateInUTC(),
            )

        }

    }

    private fun assertPayableItemStore(
        memberPayable: MemberPayable,
        amount: Amount,
        payableItemStore: PayableItemStoreDto,
        monthYearDuration: MonthYearDuration,
        hashCode: String,
        startDate: LocalDate,
        endDate: LocalDate,
    ) {
        assertEquals(amount.amount, payableItemStore.amount)
        assertEquals(amount.currency, payableItemStore.currency)
        assertEquals(memberPayable.companyId, payableItemStore.companyId)
        assertEquals(memberPayable.contract.id, payableItemStore.contractId)
        assertEquals(memberPayable.countryCode.name, payableItemStore.countryCode)
        assertEquals(transactionId, payableItemStore.transactionId)
        assertEquals(monthYearDuration.from.month, payableItemStore.month)
        assertEquals(monthYearDuration.from.year, payableItemStore.year)
        assertEquals(itemType, payableItemStore.itemType)
        assertEquals(startDate, payableItemStore.periodStartDate)
        assertEquals(endDate, payableItemStore.periodEndDate)
        assertEquals(hashCode, payableItemStore.versionId)
        assertEquals(fetchedTime.toEpochMilli(), payableItemStore.originalTimestamp)
        assertItemData(payableItemStore.itemData)
    }

    private fun assertItemData(itemData: String?) {
        if (itemData == null) return
        val jsonNode: JsonNode = objectMapper.readTree(itemData)

        assertTrue(jsonNode.isObject)
        assertTrue(jsonNode.has("memberPayable"))
        assertTrue(jsonNode.has("amount"))
        assertTrue(jsonNode["memberPayable"].isObject)
        assertTrue(jsonNode["amount"].isObject)
    }

    private fun getMemberPayable(
        id: Long,
        contractId: Long,
        amountValue: Double,
        countryCode: CountryCode,
        submittedAt: Instant,
        externalId: Long = 1L,
    ): MemberPayable {
        return MemberPayable(
            id = id,
            contract = MemberPayable.Contract(id = contractId, contractType = ContractType.FREELANCER),
            totalAmountInBaseCurrency = Amount(amountValue, CurrencyCode.USD),
            countryCode = countryCode,
            type = MemberPayableType.INVOICE,
            submittedAt = submittedAt,
            companyId = companyId,
            fetchedTime = fetchedTime,
            externalId = externalId
        )
    }
}