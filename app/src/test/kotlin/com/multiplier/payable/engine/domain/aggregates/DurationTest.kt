package com.multiplier.payable.engine.domain.aggregates

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.util.stream.Stream

class DurationTest {

    @Test
    fun `constructor should create Duration with correct start and end dates`() {
        // Given
        val startDate = LocalDate.of(2023, 1, 1)
        val endDate = LocalDate.of(2023, 12, 31)

        // When
        val duration = Duration(startDate, endDate)

        // Then
        assertEquals(startDate, duration.startDate)
        assertEquals(endDate, duration.endDate)
    }

    @Test
    fun `equals should return true for identical durations`() {
        // Given
        val startDate = LocalDate.of(2023, 1, 1)
        val endDate = LocalDate.of(2023, 12, 31)
        val duration1 = Duration(startDate, endDate)
        val duration2 = Duration(startDate, endDate)

        // Then
        assertEquals(duration1, duration2)
        assertEquals(duration1.hashCode(), duration2.hashCode())
    }

    @Test
    fun `equals should return false for durations with different start dates`() {
        // Given
        val startDate1 = LocalDate.of(2023, 1, 1)
        val startDate2 = LocalDate.of(2023, 2, 1)
        val endDate = LocalDate.of(2023, 12, 31)
        val duration1 = Duration(startDate1, endDate)
        val duration2 = Duration(startDate2, endDate)

        // Then
        assertNotEquals(duration1, duration2)
    }

    @Test
    fun `equals should return false for durations with different end dates`() {
        // Given
        val startDate = LocalDate.of(2023, 1, 1)
        val endDate1 = LocalDate.of(2023, 12, 31)
        val endDate2 = LocalDate.of(2023, 11, 30)
        val duration1 = Duration(startDate, endDate1)
        val duration2 = Duration(startDate, endDate2)

        // Then
        assertNotEquals(duration1, duration2)
    }

    @Test
    fun `toString should return a readable string representation`() {
        // Given
        val startDate = LocalDate.of(2023, 1, 1)
        val endDate = LocalDate.of(2023, 12, 31)
        val duration = Duration(startDate, endDate)

        // When
        val result = duration.toString()

        // Then
        assertTrue(result.contains(startDate.toString()))
        assertTrue(result.contains(endDate.toString()))
    }

    @ParameterizedTest
    @MethodSource("compareToTestCases")
    fun `compareTo should correctly compare durations`(
        duration1: Duration,
        duration2: Duration,
        expectedResult: Int
    ) {
        // When
        val result = duration1.compareTo(duration2)

        // Then
        when {
            expectedResult < 0 -> assertTrue(result < 0)
            expectedResult > 0 -> assertTrue(result > 0)
            else -> assertEquals(0, result)
        }
    }

    @Test
    fun `compareTo should return 0 for identical durations`() {
        // Given
        val startDate = LocalDate.of(2023, 1, 1)
        val endDate = LocalDate.of(2023, 12, 31)
        val duration1 = Duration(startDate, endDate)
        val duration2 = Duration(startDate, endDate)

        // When
        val result = duration1.compareTo(duration2)

        // Then
        assertEquals(0, result)
    }

    @Test
    fun `compareTo should return negative for earlier start date`() {
        // Given
        val startDate1 = LocalDate.of(2023, 1, 1)
        val startDate2 = LocalDate.of(2023, 2, 1)
        val endDate = LocalDate.of(2023, 12, 31)
        val duration1 = Duration(startDate1, endDate)
        val duration2 = Duration(startDate2, endDate)

        // When
        val result = duration1.compareTo(duration2)

        // Then
        assertTrue(result < 0)
    }

    @Test
    fun `compareTo should return positive for later start date`() {
        // Given
        val startDate1 = LocalDate.of(2023, 2, 1)
        val startDate2 = LocalDate.of(2023, 1, 1)
        val endDate = LocalDate.of(2023, 12, 31)
        val duration1 = Duration(startDate1, endDate)
        val duration2 = Duration(startDate2, endDate)

        // When
        val result = duration1.compareTo(duration2)

        // Then
        assertTrue(result > 0)
    }

    @Test
    fun `compareTo should compare end dates when start dates are equal`() {
        // Given
        val startDate = LocalDate.of(2023, 1, 1)
        val endDate1 = LocalDate.of(2023, 11, 30)
        val endDate2 = LocalDate.of(2023, 12, 31)
        val duration1 = Duration(startDate, endDate1)
        val duration2 = Duration(startDate, endDate2)

        // When
        val result = duration1.compareTo(duration2)

        // Then
        assertTrue(result < 0)
    }

    @Test
    fun `durations should be sortable`() {
        // Given
        val duration1 = Duration(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 3, 31))
        val duration2 = Duration(LocalDate.of(2023, 2, 1), LocalDate.of(2023, 4, 30))
        val duration3 = Duration(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 2, 28))
        val duration4 = Duration(LocalDate.of(2022, 12, 1), LocalDate.of(2023, 1, 31))

        val unsortedList = listOf(duration1, duration2, duration3, duration4)

        // When
        val sortedList = unsortedList.sorted()

        // Then
        assertEquals(duration4, sortedList[0]) // Earliest start date
        assertEquals(duration3, sortedList[1]) // Same start date as duration1 but earlier end date
        assertEquals(duration1, sortedList[2]) // Same start date as duration3 but later end date
        assertEquals(duration2, sortedList[3]) // Latest start date
    }

    @Test
    fun `should handle edge cases with min and max dates`() {
        // Given
        val minDate = LocalDate.MIN
        val maxDate = LocalDate.MAX
        val normalDate = LocalDate.of(2023, 1, 1)

        val duration1 = Duration(minDate, normalDate)
        val duration2 = Duration(normalDate, maxDate)
        val duration3 = Duration(minDate, maxDate)

        // Then
        assertTrue(duration1.compareTo(duration2) < 0)
        assertTrue(duration2.compareTo(duration1) > 0)
        assertTrue(duration1.compareTo(duration3) < 0) // Same start but duration3 has later end
        assertTrue(duration3.compareTo(duration2) < 0) // duration3 has earlier start
    }

    companion object {
        @JvmStatic
        fun compareToTestCases(): Stream<Arguments> {
            return Stream.of(
                // Same start date, different end dates
                Arguments.of(
                    Duration(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 3, 31)),
                    Duration(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 4, 30)),
                    -1
                ),
                Arguments.of(
                    Duration(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 4, 30)),
                    Duration(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 3, 31)),
                    1
                ),

                // Different start dates
                Arguments.of(
                    Duration(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 12, 31)),
                    Duration(LocalDate.of(2023, 2, 1), LocalDate.of(2023, 12, 31)),
                    -1
                ),
                Arguments.of(
                    Duration(LocalDate.of(2023, 2, 1), LocalDate.of(2023, 12, 31)),
                    Duration(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 12, 31)),
                    1
                ),

                // Different years
                Arguments.of(
                    Duration(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 12, 31)),
                    Duration(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 12, 31)),
                    -1
                ),

                // Identical durations
                Arguments.of(
                    Duration(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 12, 31)),
                    Duration(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 12, 31)),
                    0
                )
            )
        }
    }
}
