package com.multiplier.payable.engine.contractinvoice

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class ContractInvoiceTest {

    @Test
    fun givenContractInvoice_thenFieldsAreEqual() {
        // GIVEN
        val companyPayableId = 42L
        val invoiceId = 42L
        val externalInvoiceId = "awesomeExternalInvoiceId"
        val contractInvoice = ContractInvoice(
            companyPayableId = companyPayableId,
            invoiceId = invoiceId,
            externalInvoiceId = externalInvoiceId,
        )

        // THEN
        assertThat(contractInvoice.companyPayableId).isEqualTo(companyPayableId)
        assertThat(contractInvoice.invoiceId).isEqualTo(invoiceId)
        assertThat(contractInvoice.externalInvoiceId).isEqualTo(externalInvoiceId)
    }
}