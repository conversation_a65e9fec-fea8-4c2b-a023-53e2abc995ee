package com.multiplier.payable.engine.reconciler.descriptionbuilder.memberPayable

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.util.toLocalDateInUTC
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.memberpayable.*
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import com.multiplier.payable.types.Amount
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import org.junit.jupiter.api.Test

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset

@ExtendWith(MockKExtension::class)
class MemberPayableManagementFeePayableItemDescriptionBuilderTest {

    @InjectMockKs
    private lateinit var memberPayableManagementFeePayableItemDescriptionBuilder: MemberPayableManagementFeePayableItemDescriptionBuilder

    @Test
    fun getLineItemType() {
        assertEquals(LineItemType.MANAGEMENT_FEE_FREELANCER, memberPayableManagementFeePayableItemDescriptionBuilder.lineItemType)
    }

    @Test
    fun build_when_memberPayableWrapperNull_thenThrowException() {
        val context = mockk<PayableItemDescriptionBuilderContext>(){
            every { memberPayableWrapper } returns null
        }

        val exception = assertThrows<IllegalArgumentException> {
            memberPayableManagementFeePayableItemDescriptionBuilder.build(context)
        }

        assertEquals("memberPayableWrapper must not be null", exception.message)
    }

    @Test
    fun build_when_memberPayableNull_thenThrowException() {
        val context = mockk<PayableItemDescriptionBuilderContext>(){
            every { memberPayableWrapper } returns MemberPayableWrapper(
                memberPayable = null,
                paymentMethod = PaymentMethod.CARD
            )
        }

        val exception = assertThrows<IllegalArgumentException> {
            memberPayableManagementFeePayableItemDescriptionBuilder.build(context)
        }

        assertEquals("memberPayable must not be null", exception.message)
    }

    @Test
    fun build() {

        val instant = LocalDate.of(2022,10,1).atStartOfDay().toInstant(ZoneOffset.UTC)
        val context = mockk<PayableItemDescriptionBuilderContext>(){
            every { memberPayableWrapper } returns MemberPayableWrapper(
                memberPayable = MemberPayable(
                    type = MemberPayableType.INVOICE,
                    submittedAt = instant,
                    id = 1,
                    externalId = 1L,
                    contract = MemberPayable.Contract(1L, ContractType.FREELANCER),
                    totalAmountInBaseCurrency = Amount(100.0, CurrencyCode.USD),
                    companyId = 1L,
                    fetchedTime = Instant.now(),
                    countryCode = CountryCode.SGP
                ),
                paymentMethod = PaymentMethod.CARD
            )
        }

        assertEquals("Management Fees - ${instant.toLocalDateInUTC()}", memberPayableManagementFeePayableItemDescriptionBuilder.build(context))
    }
}