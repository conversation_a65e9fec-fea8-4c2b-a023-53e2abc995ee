package com.multiplier.payable.engine.transaction

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.repository.model.JpaInvoiceLineItem
import com.multiplier.core.payable.repository.model.JpaPayableItem
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.ContractDepartment
import com.multiplier.payable.engine.testutils.CompanyPayableTestDataFactory
import com.multiplier.payable.engine.transaction.mapper.CompanyPayableMapper
import com.multiplier.payable.types.CompanyPayableType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mapstruct.factory.Mappers
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import java.time.LocalDateTime

@ExtendWith(MockitoExtension::class)
class CompanyPayableMapperTest {
    private val mapper = Mappers.getMapper(CompanyPayableMapper::class.java)

    @Test
    fun `should map to DefaultFinancialTransaction for ANNUAL_PLAN type`() {
        // GIVEN
        val jpaCompanyPayable =
            CompanyPayableTestDataFactory.createJpaCompanyPayable(
                transactionId = "TX123456789",
                type = CompanyPayableType.ANNUAL_PLAN,
                invoice =
                CompanyPayableTestDataFactory.createJpaInvoice(
                    dueDate = LocalDateTime.of(2024, 1, 31, 0, 0),
                    reference = "REF-123",
                    externalId = "e453d445-0eda-44fb-9f99-fd5b165530f2",
                ),
                id = 1000L,
            )

        // WHEN
        val result = mapper.toFinancialTransaction(jpaCompanyPayable)

        // THEN
        assertNotNull(result)
        assertEquals("TX123456789", result.transactionId)
        assertEquals("REF-123", result.reference)
        assertEquals(1000L, result.companyPayableId)
        assertEquals("e453d445-0eda-44fb-9f99-fd5b165530f2", result.invoiceId)
    }

    @Test
    fun `should map manual insurance invoice correctly`() {
        // GIVEN
        val jpaCompanyPayable =
            CompanyPayableTestDataFactory.createJpaCompanyPayable(
                transactionId = "TX123456789",
                type = CompanyPayableType.ANNUAL_PLAN,
                invoice =
                CompanyPayableTestDataFactory.createJpaInvoice(
                    dueDate = LocalDateTime.of(2024, 1, 31, 0, 0),
                    reference = "REF-123",
                    externalId = "e453d445-0eda-44fb-9f99-fd5b165530f2",
                    lineItems =
                    listOf(
                        JpaInvoiceLineItem.builder()
                            .contractId(1L)
                            .build(),
                    ),
                ),
                id = 1000L,
                companyId = 100L,
            )

        // WHEN
        val result = mapper.mapManualInsurancePayables(listOf(jpaCompanyPayable))

        // THEN
        assertNotNull(result)
        val item = result[0].items[0]
        assertThat(item.contractId).isEqualTo(1L)
        assertThat(item.companyId).isEqualTo(100L)
    }

    @Test
    fun `should map to DefaultFinancialTransaction for GLOBAL_PAYROLL_FUNDING type`() {
        // GIVEN
        val jpaCompanyPayable =
            CompanyPayableTestDataFactory.createJpaCompanyPayable(
                transactionId = "TX123456789",
                type = CompanyPayableType.GLOBAL_PAYROLL_FUNDING,
                invoice =
                CompanyPayableTestDataFactory.createJpaInvoice(
                    dueDate = LocalDateTime.of(2024, 1, 31, 0, 0),
                    reference = "REF-123",
                    externalId = "e453d445-0eda-44fb-9f99-fd5b165530f2",
                ),
                id = 1000L,
            )

        // WHEN
        val result = mapper.toFinancialTransaction(jpaCompanyPayable)

        // THEN
        assertNotNull(result)
        assertEquals("TX123456789", result.transactionId)
        assertEquals("REF-123", result.reference)
        assertEquals(1000L, result.companyPayableId)
        assertEquals("e453d445-0eda-44fb-9f99-fd5b165530f2", result.invoiceId)
    }

    @Test
    fun `given unknown CompanyPayableType should throw IllegalArgException`() {
        // GIVEN
        val jpaCompanyPayable =
            CompanyPayableTestDataFactory.createJpaCompanyPayable(
                type = CompanyPayableType.UNKNOWN,
            )

        // WHEN & THEN
        assertThrows<IllegalArgumentException> {
            mapper.toFinancialTransaction(jpaCompanyPayable)
        }
    }

    @Test
    fun `test mapCompanyPayable with mock data`() {
        // GIVEN
        val withDepartmentJpaPayableItem = CompanyPayableTestDataFactory.createJpaPayableItem(
            itemData = setOf(
                CompanyPayableTestDataFactory.createJpaPayableItemData(
                    contractDepartment = ContractDepartment(1L, "Department One")
                )
            ),
        )
        val nullDepartmentJpaPayableItem = CompanyPayableTestDataFactory.createJpaPayableItem(
            itemData = setOf(
                CompanyPayableTestDataFactory.createJpaPayableItemData(
                    contractDepartment = null
                )
            ),
        )
        val mockJpaCompanyPayable = CompanyPayableTestDataFactory.createJpaCompanyPayable(
            items = setOf(withDepartmentJpaPayableItem, nullDepartmentJpaPayableItem),
        )
        val withDepartmentPayableItem = CompanyPayableTestDataFactory.createPayableItem(
            contractDepartment = ContractDepartment(1L, "Department One")
        )
        val nullDepartmentPayableItem = CompanyPayableTestDataFactory.createPayableItem()
        val expectedCompanyPayable = CompanyPayableTestDataFactory.createCompanyPayable(
            items = listOf(withDepartmentPayableItem, nullDepartmentPayableItem),
        )

        // WHEN
        val result = mapper.mapCompanyPayable(mockJpaCompanyPayable)

        // THEN
        assertEquals(expectedCompanyPayable.id, result.id)
        assertEquals(expectedCompanyPayable.companyId, result.companyId)
        assertEquals(expectedCompanyPayable.items.size, result.items.size) // individual mapping is tested below
        assertEquals(
            expectedCompanyPayable.items.mapNotNull { it.contractDepartment },
            result.items.mapNotNull { it.contractDepartment })
        assertEquals(expectedCompanyPayable.itemType, result.itemType)
        assertEquals(expectedCompanyPayable.transactionId, result.transactionId)
    }

    @Test
    fun `given correct contractType should map to corresponding employmentType`() {
        assertEquals("EOR", mapper.fromContractTypeToEmploymentType(ContractType.EMPLOYEE))
        assertEquals("AOR", mapper.fromContractTypeToEmploymentType(ContractType.CONTRACTOR))
        assertEquals("Freelancer", mapper.fromContractTypeToEmploymentType(ContractType.FREELANCER))
        assertEquals("HR Member", mapper.fromContractTypeToEmploymentType(ContractType.HR_MEMBER))
    }

    @Test
    fun `mapPayableItems should map to empty if empty payable items`() {
        // GIVEN
        val companyPayable = CompanyPayableTestDataFactory.createJpaCompanyPayable()

        // WHEN
        val result = mapper.mapPayableItems(companyPayable, emptyList())

        // THEN
        assertTrue(result.isEmpty())
    }

    @Test
    fun `mapType should map correctly if supported`() {
        val result = mapper.mapType(CompanyPayableType.FIRST_INVOICE)
        assertEquals(TransactionType.FIRST_INVOICE, result)

        val result2 = mapper.mapType(CompanyPayableType.DEPOSIT)
        assertEquals(TransactionType.DEPOSIT_INVOICE, result2)

        val result3 = mapper.mapType(CompanyPayableType.SECOND_INVOICE)
        assertEquals(TransactionType.SECOND_INVOICE, result3)

        assertEquals(TransactionType.INSURANCE_INVOICE, mapper.mapType(CompanyPayableType.INSURANCE))
        assertEquals(TransactionType.ANNUAL_PLAN_INVOICE, mapper.mapType(CompanyPayableType.ANNUAL_PLAN))
        assertEquals(TransactionType.FREELANCER_INVOICE, mapper.mapType(CompanyPayableType.FREELANCER))
    }

    @Test
    fun `mapType should throw exception for unsupported CompanyPayableType`() {
        // Note: PAYROLL_OFFCYCLE_INVOICE doesn't exist in external CompanyPayableType enum
        // so we test that the mapper handles unsupported types correctly
        val exception = org.junit.jupiter.api.assertThrows<IllegalArgumentException> {
            mapper.mapType(CompanyPayableType.UNKNOWN)
        }
        assertTrue(exception.message!!.contains("Unsupported mapping for CompanyPayableType"))
    }

    @Test
    fun `mapType should throw exception if not supported`() {
        // WHEN & THEN
        assertThrows<IllegalArgumentException> {
            mapper.mapType(CompanyPayableType.UNKNOWN)
        }
    }

    @Test
    fun `given null or empty itemData mapAnnualSeatPaymentTerm should return null`() {
        val item = mock<JpaPayableItem> { on { itemData }.thenReturn(null) }
        val item2 = mock<JpaPayableItem> { on { itemData }.thenReturn(emptySet()) }
        val result = mapper.mapAnnualSeatPaymentTerm(item)
        val result2 = mapper.mapAnnualSeatPaymentTerm(item2)
        assertNull(result)
        assertNull(result2)
    }

    @Test
    fun givenJpaCompanyPayable_whenMapPayableItem_thenValuesAreEqual() {
        // GIVEN
        val jpaCompanyPayable = CompanyPayableTestDataFactory.createJpaCompanyPayable()

        // WHEN
        val jpaPayableItem = jpaCompanyPayable.items.toList()[0]
        val payableItem = mapper.mapPayableItem(jpaCompanyPayable, jpaPayableItem)

        // THEN
        assertThat(payableItem.month).isEqualTo(jpaCompanyPayable.month)
        assertThat(payableItem.year).isEqualTo(jpaCompanyPayable.year)
        assertThat(payableItem.lineItemType).isEqualTo(LineItemType.GP_FUND_REQUEST.name)
        assertThat(payableItem.contractId).isEqualTo(jpaPayableItem.contractId)
        assertThat(payableItem.employmentType).isEqualTo("EOR")

        val firstItemDataItem = jpaPayableItem.itemData.toList()[0]
        assertThat(payableItem.annualSeatPaymentTerm).isEqualTo(firstItemDataItem.annualSeatPaymentTerm)
        assertThat(payableItem.countryWorkStatus).isEqualTo(firstItemDataItem.countryWorkStatus)
        assertThat(payableItem.companyId).isEqualTo(jpaCompanyPayable.companyId)
        assertThat(payableItem.description).isEqualTo(jpaPayableItem.description)
        assertThat(payableItem.amountInBaseCurrency).isEqualTo(jpaPayableItem.totalCost)
        assertThat(payableItem.baseCurrency).isEqualTo(jpaPayableItem.currencyCode.name)
        assertThat(payableItem.billableCost).isEqualTo(jpaPayableItem.billableCost)
        assertThat(payableItem.versionId).isEqualTo(jpaPayableItem.versionId)
        assertThat(payableItem.originalTimestamp).isEqualTo(jpaPayableItem.originalTimestamp)
        assertThat(payableItem.cycle.name).isEqualTo(jpaPayableItem.cycle)
        assertThat(payableItem.countryCode).isEqualTo(jpaPayableItem.countryCode.name)
        assertThat(payableItem.itemCount).isEqualTo(1)
        assertThat(payableItem.periodStartDate).isEqualTo(jpaPayableItem.startPayCycleDate)
        assertThat(payableItem.periodEndDate).isEqualTo(jpaPayableItem.endPayCycleDate)
        assertThat(payableItem.countryName).isEqualTo(jpaPayableItem.countryName)
        assertThat(payableItem.taxType).isEqualTo(jpaPayableItem.taxType)
        assertThat(payableItem.insuranceType).isEqualTo(firstItemDataItem.insuranceType)
        assertThat(payableItem.memberName).isEqualTo(firstItemDataItem.memberName)
        assertThat(payableItem.contractDepartment).isEqualTo(firstItemDataItem.contractDepartment)
        assertThat(payableItem.deposit).isEqualTo(firstItemDataItem.deposit)
    }

    @Nested
    inner class MapCycleTest {

        @Test
        fun `given null cycle should return monthly`() {
            val jpaPayableItem = mock<JpaPayableItem>()

            doReturn(null).`when`(jpaPayableItem).cycle

            val result = mapper.mapCycle(jpaPayableItem)
            assertEquals(InvoiceCycle.MONTHLY, result)
        }

        @Test
        fun `given cycle, should return correct cycle mapping`() {
            val jpaPayableItem = mock<JpaPayableItem>()

            doReturn("YEARLY").`when`(jpaPayableItem).cycle

            val result = mapper.mapCycle(jpaPayableItem)
            assertEquals(InvoiceCycle.YEARLY, result)
        }
    }
}
