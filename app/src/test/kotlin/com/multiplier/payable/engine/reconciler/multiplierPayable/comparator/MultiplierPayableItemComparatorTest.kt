package com.multiplier.payable.engine.reconciler.multiplierPayable.comparator

import com.multiplier.core.payable.adapters.netsuite.models.CountryCode
import com.multiplier.core.payable.expenseBill.ExpenseBillLineItemType
import com.multiplier.core.payable.repository.model.MultiplierPayableStatus
import com.multiplier.core.payable.service.vendorbill.itemstore.PayrollMetaData
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.reconciler.multiplierPayable.dto.MultiplierPayableDTO
import com.multiplier.payable.engine.reconciler.multiplierPayable.dto.MultiplierPayableItemDTO
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class MultiplierPayableItemComparatorTest {

    private lateinit var comparator: MultiplierPayableItemComparator

    @BeforeEach
    fun setup() {
        comparator = MultiplierPayableItemComparator()
    }

    @Test
    fun `shouldCreateNewMultiplierPayable should return true when no existing payables`() {
        // Given
        val existingPayables = emptyList<MultiplierPayableDTO>()
        val currentItems = setOf(createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD))

        // When
        val result = comparator.shouldCreateNewMultiplierPayable(existingPayables, currentItems)

        // Then
        assertThat(result).isTrue()
    }

    @Test
    fun `shouldCreateNewMultiplierPayable should return true when item counts differ`() {
        // Given
        val existingPayable = createMultiplierPayableDTO(
            1L,
            setOf(createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD))
        )
        val currentItems = setOf(
            createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD),
            createMultiplierPayableItemDTO(2L, 200L, 200.0, CurrencyCode.USD)
        )

        // When
        val result = comparator.shouldCreateNewMultiplierPayable(listOf(existingPayable), currentItems)

        // Then
        assertThat(result).isTrue()
    }

    @Test
    fun `shouldCreateNewMultiplierPayable should return true when items have different contract IDs`() {
        // Given
        val existingPayable = createMultiplierPayableDTO(
            1L,
            setOf(createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD))
        )
        val currentItems = setOf(
            createMultiplierPayableItemDTO(1L, 200L, 100.0, CurrencyCode.USD) // Different contract ID
        )

        // When
        val result = comparator.shouldCreateNewMultiplierPayable(listOf(existingPayable), currentItems)

        // Then
        assertThat(result).isTrue()
    }

    @Test
    fun `shouldCreateNewMultiplierPayable should return true when items have different amounts`() {
        // Given
        val existingPayable = createMultiplierPayableDTO(
            1L,
            setOf(createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD))
        )
        val currentItems = setOf(
            createMultiplierPayableItemDTO(1L, 100L, 200.0, CurrencyCode.USD) // Different amount
        )

        // When
        val result = comparator.shouldCreateNewMultiplierPayable(listOf(existingPayable), currentItems)

        // Then
        assertThat(result).isTrue()
    }

    @Test
    fun `shouldCreateNewMultiplierPayable should return true when items have different currencies`() {
        // Given
        val existingPayable = createMultiplierPayableDTO(
            1L,
            setOf(createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD))
        )
        val currentItems = setOf(
            createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.EUR) // Different currency
        )

        // When
        val result = comparator.shouldCreateNewMultiplierPayable(listOf(existingPayable), currentItems)

        // Then
        assertThat(result).isTrue()
    }

    @Test
    fun `shouldCreateNewMultiplierPayable should return false when items are the same`() {
        // Given
        val existingPayable = createMultiplierPayableDTO(
            1L,
            setOf(createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD))
        )
        val currentItems = setOf(
            createMultiplierPayableItemDTO(2L, 100L, 100.0, CurrencyCode.USD) // Same contract ID, amount, currency
        )

        // When
        val result = comparator.shouldCreateNewMultiplierPayable(listOf(existingPayable), currentItems)

        // Then
        assertThat(result).isFalse()
    }

    @Test
    fun `shouldCreateNewMultiplierPayable should return false when multiple items are the same`() {
        // Given
        val existingPayable = createMultiplierPayableDTO(
            1L,
            setOf(
                createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD),
                createMultiplierPayableItemDTO(2L, 200L, 200.0, CurrencyCode.USD)
            )
        )
        val currentItems = setOf(
            createMultiplierPayableItemDTO(3L, 100L, 100.0, CurrencyCode.USD), // Same contract ID, amount, currency
            createMultiplierPayableItemDTO(4L, 200L, 200.0, CurrencyCode.USD)  // Same contract ID, amount, currency
        )

        // When
        val result = comparator.shouldCreateNewMultiplierPayable(listOf(existingPayable), currentItems)

        // Then
        assertThat(result).isFalse()
    }

    @Test
    fun `shouldCreateNewMultiplierPayable should handle payables with null IDs`() {
        // Given
        val existingPayable = createMultiplierPayableDTO(
            null, // null ID
            setOf(createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD))
        )
        val currentItems = setOf(
            createMultiplierPayableItemDTO(2L, 200L, 200.0, CurrencyCode.USD) // Different contract ID
        )

        // When
        val result = comparator.shouldCreateNewMultiplierPayable(listOf(existingPayable), currentItems)

        // Then - should return true because items are different
        assertThat(result).isTrue()
    }

    @Test
    fun `shouldCreateNewMultiplierPayable should select payable with highest ID when multiple exist`() {
        // Given
        val oldPayable = createMultiplierPayableDTO(
            1L,
            setOf(createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD))
        )
        val newerPayable = createMultiplierPayableDTO(
            2L,
            setOf(createMultiplierPayableItemDTO(2L, 100L, 100.0, CurrencyCode.USD))
        )
        val newestPayable = createMultiplierPayableDTO(
            3L,
            setOf(createMultiplierPayableItemDTO(3L, 100L, 100.0, CurrencyCode.USD))
        )

        val currentItems = setOf(
            createMultiplierPayableItemDTO(4L, 100L, 100.0, CurrencyCode.USD) // Same contract ID, amount, currency
        )

        // When - should compare with the newest payable (ID 3)
        val result = comparator.shouldCreateNewMultiplierPayable(
            listOf(oldPayable, newerPayable, newestPayable),
            currentItems
        )

        // Then - should return false because items are the same
        assertThat(result).isFalse()
    }

    @Test
    fun `areItemSetsEqual should return false when contract IDs differ`() {
        // Given
        val existingItems = setOf(
            createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD),
            createMultiplierPayableItemDTO(2L, 200L, 200.0, CurrencyCode.USD)
        )
        val newItems = setOf(
            createMultiplierPayableItemDTO(3L, 100L, 100.0, CurrencyCode.USD),
            createMultiplierPayableItemDTO(4L, 300L, 200.0, CurrencyCode.USD) // Different contract ID
        )

        // When
        val result = comparator.areItemSetsEqual(existingItems, newItems)

        // Then
        assertThat(result).isFalse()
    }

    @Test
    fun `areItemSetsEqual should return false when item counts for same contract differ`() {
        // Given
        val existingItems = setOf(
            createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD),
            createMultiplierPayableItemDTO(2L, 100L, 200.0, CurrencyCode.USD) // Same contract ID, different amount
        )
        val newItems = setOf(
            createMultiplierPayableItemDTO(3L, 100L, 100.0, CurrencyCode.USD) // Only one item for contract 100
        )

        // When
        val result = comparator.areItemSetsEqual(existingItems, newItems)

        // Then
        assertThat(result).isFalse()
    }

    @Test
    fun `areItemSetsEqual should handle empty sets gracefully`() {
        // This test is to ensure the code handles empty sets properly
        // Given
        val existingItems = emptySet<MultiplierPayableItemDTO>()
        val newItems = emptySet<MultiplierPayableItemDTO>()

        // When
        val result = comparator.areItemSetsEqual(existingItems, newItems)

        // Then - empty sets are considered equal
        assertThat(result).isTrue()
    }

    @Test
    fun `areItemsEqual should return true when items have same contract ID, amount, and currency`() {
        // Given
        val item1 = createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD)
        val item2 = createMultiplierPayableItemDTO(2L, 100L, 100.0, CurrencyCode.USD)

        // When
        val result = comparator.areItemsEqual(item1, item2)

        // Then
        assertThat(result).isTrue()
    }

    @Test
    fun `areItemsEqual should return false when items have different contract IDs`() {
        // Given
        val item1 = createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD)
        val item2 = createMultiplierPayableItemDTO(2L, 200L, 100.0, CurrencyCode.USD)

        // When
        val result = comparator.areItemsEqual(item1, item2)

        // Then
        assertThat(result).isFalse()
    }

    @Test
    fun `areItemsEqual should return false when items have different amounts`() {
        // Given
        val item1 = createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD)
        val item2 = createMultiplierPayableItemDTO(2L, 100L, 200.0, CurrencyCode.USD)

        // When
        val result = comparator.areItemsEqual(item1, item2)

        // Then
        assertThat(result).isFalse()
    }

    @Test
    fun `areItemsEqual should return false when items have different currencies`() {
        // Given
        val item1 = createMultiplierPayableItemDTO(1L, 100L, 100.0, CurrencyCode.USD)
        val item2 = createMultiplierPayableItemDTO(2L, 100L, 100.0, CurrencyCode.EUR)

        // When
        val result = comparator.areItemsEqual(item1, item2)

        // Then
        assertThat(result).isFalse()
    }

    // Helper methods
    private fun createMultiplierPayableItemDTO(
        id: Long,
        contractId: Long,
        amount: Double,
        currencyCode: CurrencyCode
    ): MultiplierPayableItemDTO {
        return MultiplierPayableItemDTO(
            id = id,
            contractId = contractId,
            expenseBillLineItemType = ExpenseBillLineItemType.DEFAULT,
            description = "Test description",
            amount = amount,
            currencyCode = currencyCode,
            employeeName = "Test Employee",
            countryCode = CountryCode.USA,
            payrollMetaData = PayrollMetaData.builder().cycleId(123L).build()
        )
    }

    private fun createMultiplierPayableDTO(
        id: Long?,
        items: Set<MultiplierPayableItemDTO>
    ): MultiplierPayableDTO {
        return MultiplierPayableDTO(
            id = id,
            status = MultiplierPayableStatus.APPROVED,
            lineItemType = ExpenseBillLineItemType.DEFAULT,
            transactionId = "txn-test",
            payrollCycleId = 123L,
            items = items
        )
    }
}
