package com.multiplier.payable.engine.invoice

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.Invoice
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.types.InvoiceStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mapstruct.factory.Mappers
import org.mockito.junit.jupiter.MockitoExtension
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId

@ExtendWith(MockitoExtension::class)
class InvoiceFinancialTransactionToInvoiceMapperTest {
    private val mapper = Mappers.getMapper(InvoiceFinancialTransactionToInvoiceMapper::class.java)

    @Test
    fun `should map correctly`() {
        val startDate = LocalDate.of(2024, 3, 1)
        val endDate = LocalDate.of(2024, 3, 31)
        val nowLocalDateTime = LocalDateTime.now()
        val dueDate = nowLocalDateTime.plusDays(7L)
        val payableItem = PayableItem(
            month = nowLocalDateTime.monthValue,
            year = nowLocalDateTime.year,
            lineItemType = LineItemType.ANNUAL_MANAGEMENT_FEE_EOR.name,
            companyId = 1L,
            description = "Line Item",
            amountInBaseCurrency = 120.0,
            baseCurrency = "SGD",
            billableCost = 100.0,
            originalTimestamp = nowLocalDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            cycle = InvoiceCycle.YEARLY,
            countryCode = "SGP",
            countryName = "Singapore",
            periodStartDate = startDate,
            periodEndDate = endDate,
            itemCount = 2,
        )
        val items = listOf(payableItem)

        val invoiceFinancialTransaction = Invoice()
        invoiceFinancialTransaction.transactionId = "randomTransactionId"
        invoiceFinancialTransaction.companyId = 1L
        invoiceFinancialTransaction.reference = "Awesome Reference"
        invoiceFinancialTransaction.items = items
        invoiceFinancialTransaction.currency = CurrencyCode.USD
        invoiceFinancialTransaction.date = nowLocalDateTime
        invoiceFinancialTransaction.dueDate = dueDate
        invoiceFinancialTransaction.status = InvoiceStatus.DRAFT

        val externalCustomerId = "123"

        // WHEN
        val dto = mapper.map(invoiceFinancialTransaction, externalCustomerId)

        // THEN
        assertEquals(nowLocalDateTime.toLocalDate(), dto.date)
        assertEquals(dueDate.toLocalDate(), dto.dueDate)
        assertEquals("Awesome Reference", dto.reference)
        assertEquals(CurrencyCode.USD.name, dto.billingCurrencyCode.name)
        assertEquals(1L, dto.companyId)
        assertEquals(externalCustomerId, dto.customerId)
        assertThat(dto.lineItems).isNotEmpty
        assertEquals(1, dto.lineItems.size)
        assertEquals(InvoiceStatus.DRAFT, dto.status)
        val lineItem = dto.lineItems[0]
        assertEquals("Line Item", lineItem.description)
        assertEquals(120.0, lineItem.amountInBaseCurrency)
        assertEquals("SGD", lineItem.baseCurrency)
        assertEquals(100.0, lineItem.unitAmount)
        assertEquals("Singapore", lineItem.countryName)
        assertEquals(startDate, lineItem.startPayCycleDate)
        assertEquals(endDate, lineItem.endPayCycleDate)
        assertEquals(LineItemType.ANNUAL_MANAGEMENT_FEE_EOR, lineItem.itemType)
        assertEquals(2.0, lineItem.quantity)
    }
}
