package com.multiplier.payable.engine.transaction

import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.read.ListAppender
import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.gateway.KafkaTransactionGateway
import com.multiplier.payable.engine.orchestrator.Orchestrator
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Answers
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.verify
import org.slf4j.LoggerFactory
import org.springframework.kafka.core.KafkaTemplate
import java.time.LocalDateTime

@ExtendWith(MockitoExtension::class)
class AsynchronousGenerateFinancialTransactionCommandTest {

    private lateinit var transactionGateway: KafkaTransactionGateway
    private val objectMapper = ObjectMapper()

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private lateinit var kafkaTemplate: KafkaTemplate<String, String>

    @Mock
    private lateinit var orchestrator: Orchestrator

    @Mock
    private lateinit var transactionIdProvider: TransactionIdProvider

    private lateinit var asyncGenerateCommand: AsynchronousGenerateFinancialTransactionCommand

    private val sampleKafkaTopic = "sampleKafkaTopic"

    private val listAppender = ListAppender<ILoggingEvent>()
    private val kafkaTxGatewayLogger = LoggerFactory.getLogger(KafkaTransactionGateway::class.java) as Logger
    private val asyncTxCmdLogger = LoggerFactory.getLogger(AsynchronousGenerateFinancialTransactionCommand::class.java) as Logger

    @BeforeEach
    fun setup() {
        transactionGateway = KafkaTransactionGateway(kafkaTemplate = kafkaTemplate, objectMapper = objectMapper)

        asyncGenerateCommand = AsynchronousGenerateFinancialTransactionCommand(
            orchestrator = orchestrator,
            transactionGateway = transactionGateway,
            invoiceEngineKafkaTopic = sampleKafkaTopic,
            transactionIdProvider = transactionIdProvider
        )
        setUpLogger()

        `when`(transactionIdProvider.generate(anyOrNull())).thenReturn("12345")
    }

    private fun setUpLogger() {
        listAppender.start()
        kafkaTxGatewayLogger.addAppender(listAppender)
        asyncTxCmdLogger.addAppender(listAppender)
    }

    @AfterEach
    fun cleanupLogger() {
        kafkaTxGatewayLogger.detachAppender(listAppender)
        asyncTxCmdLogger.detachAppender(listAppender)
        listAppender.stop()
    }

    @Test
    fun `given valid command should generate async correctly`() {
        // GIVEN
        val companyIds = listOf(1L)
        val dateRange = DateRange(
            startDate = LocalDateTime.of(2024, 1, 1, 0, 0, 0),
            endDate = LocalDateTime.of(2024, 1, 31, 0, 0, 0)
        )
        val invoiceDate = LocalDateTime.now()
        val cycle = InvoiceCycle.MONTHLY

        // WHEN
        asyncGenerateCommand.generate(
            TransactionType.ANNUAL_PLAN_INVOICE, companyIds, dateRange, invoiceDate, cycle, emptyMap(), false, null, emptyMap(), null, null, null
        )

        // THEN
        verify(orchestrator).startTransaction(any())

        // assert kafka transaction gateway fire with correct topic
        val logs = listAppender.list
        Assertions.assertTrue(logs.any { it.message.contains("Publishing event to Kafka topic: $sampleKafkaTopic") })
    }

    @Test
    fun `given start transaction error should log`() {
        // GIVEN
        val companyIds = listOf(1L)
        val dateRange = DateRange(
            startDate = LocalDateTime.of(2024, 1, 1, 0, 0, 0),
            endDate = LocalDateTime.of(2024, 1, 31, 0, 0, 0)
        )
        val invoiceDate = LocalDateTime.now()
        val cycle = InvoiceCycle.MONTHLY
        `when`(orchestrator.startTransaction(any()))
            .thenThrow(RuntimeException())

        // WHEN
        val messages = asyncGenerateCommand.generate(
            TransactionType.ANNUAL_PLAN_INVOICE, companyIds, dateRange, invoiceDate, cycle, emptyMap(), false, null, emptyMap(), null, null, null
        )

        // THEN
        assertThat(messages.isNotEmpty(), equalTo(true))
        val logs = listAppender.list
        Assertions.assertTrue(logs.any { it.message.contains("Error generating invoice asynchronously") })
    }
}
