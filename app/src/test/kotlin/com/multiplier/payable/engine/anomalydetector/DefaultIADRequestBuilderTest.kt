package com.multiplier.payable.engine.anomalydetector

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.invoice.database.InvoiceDtoMapper
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.ExternalSystem
import com.multiplier.core.payable.repository.model.InvoiceType
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaInvoice
import com.multiplier.payable.engine.TransactionStatus
import com.multiplier.payable.engine.anomalydetector.provider.InvoiceAwareCompanyPayableProvider
import com.multiplier.payable.engine.anomalydetector.provider.NetsuiteInvoiceProvider
import com.multiplier.payable.engine.anomalydetector.request.DefaultIADRequestBuilder
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.testutils.CompanyPayableTestDataFactory
import com.multiplier.payable.engine.transaction.FirstInvoiceTransactionContext
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.*
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

@ExtendWith(MockitoExtension::class)
class DefaultIADRequestBuilderTest {

    @Mock
    private lateinit var companyPayableRepository: JpaCompanyPayableRepository

    @Mock
    private lateinit var invoiceDtoMapper: InvoiceDtoMapper

    @Mock
    private lateinit var netsuiteInvoiceProvider: NetsuiteInvoiceProvider

    @Mock
    private lateinit var invoiceAwareCompanyPayableProvider: InvoiceAwareCompanyPayableProvider

    private lateinit var objectMapper: ObjectMapper

    private lateinit var builder: DefaultIADRequestBuilder

    @BeforeEach
    fun setUp() {
        objectMapper = ObjectMapper()
            .registerModules(JavaTimeModule())
            .registerKotlinModule()
        builder = DefaultIADRequestBuilder(
            companyPayableRepository,
            invoiceDtoMapper,
            netsuiteInvoiceProvider,
            invoiceAwareCompanyPayableProvider,
            objectMapper
        )
    }

    @Test
    fun givenNoPayablesInDB_whenBuildIADRequestsFromCommand_thenThrowIllegalArgumentException() {
        // GIVEN
        val command: InvoiceCommand = mock {
            on { transactionId } doReturn "123"
        }
        whenever(companyPayableRepository.findByTransactionId(command.transactionId)).thenReturn(emptyList())

        // WHEN & THEN
        assertFailsWith<IllegalArgumentException> {
            builder.build(command)
        }
    }

    @Test
    fun givenNoInvoices_whenBuildIADRequestsFromCommand_thenThrowIllegalArgumentException() {
        // GIVEN
        val command: InvoiceCommand = mock {
            on { transactionId } doReturn "123"
        }
        val mockJpaCompanyPayable: JpaCompanyPayable = mock {
            on { invoice } doReturn null
        }
        whenever(companyPayableRepository.findByTransactionId(command.transactionId)).thenReturn(
            listOf(
                mockJpaCompanyPayable
            )
        )

        // WHEN & THEN
        assertFailsWith<IllegalArgumentException> {
            builder.build(command)
        }
    }

    @Test
    fun givenValidCommand_whenBuildIADRequestsFromCommand_thenReturnCorrectIADRequests() {
        // GIVEN
        val command: InvoiceCommand = mock<InvoiceCommand> {
            on { transactionId } doReturn "123"
        }

        val mockInitialJpaInvoice: JpaInvoice = mock {}
        val mockJpaCompanyPayable: JpaCompanyPayable = mock {
            on { invoice } doReturn mockInitialJpaInvoice
        }
        whenever(companyPayableRepository.findByTransactionId(command.transactionId)).thenReturn(
            listOf(
                mockJpaCompanyPayable
            )
        )

        val enrichedInvoice = CompanyPayableTestDataFactory.createJpaInvoice(
            type = InvoiceType.GROSS,
            externalId = "inv123",
            externalSystem = ExternalSystem.NETSUITE,
            totalAmount = BigDecimal.valueOf(100.0)
        )
        whenever(netsuiteInvoiceProvider.getByBatch(any<List<JpaInvoice>>())).thenReturn(listOf(enrichedInvoice))

        val enrichedPayable = CompanyPayableTestDataFactory.createJpaCompanyPayable(
            id = 1L,
            invoice = enrichedInvoice
        )
        whenever(invoiceAwareCompanyPayableProvider.get(any<List<JpaCompanyPayable>>(), any<List<JpaInvoice>>()))
            .thenReturn(listOf(enrichedPayable))

        val mockInvoiceDto: InvoiceDTO = InvoiceDTO.builder()
            .companyPayableId(1L)
            .type(InvoiceType.GROSS)
            .externalId("inv123")
            .totalAmount(100.0)
            .externalSystem(ExternalSystem.NETSUITE)
            .build()
        whenever(invoiceDtoMapper.map(listOf(enrichedPayable), listOf(enrichedInvoice))).thenReturn(
            listOf(
                mockInvoiceDto
            )
        )

        // WHEN
        val requests = builder.build(command)

        // THEN
        assertTrue(requests.size == 1)
        assertEquals(1L, requests.first().payable.id)
        assertEquals(InvoiceType.GROSS, requests.first().invoiceDTO.type)
        assertEquals("inv123", requests.first().invoiceDTO.externalId)
        assertEquals(100.0, requests.first().invoiceDTO.totalAmount)
        assertEquals(ExternalSystem.NETSUITE, requests.first().invoiceDTO.externalSystem)
    }

    @Test
    fun givenValidFirstInvoiceTxContext_whenReadValue_shouldDeserializeCorrectly() {
        val command = invoiceCommand()
        command.appendTrace(
            InvoiceCommand.FIRST_INVOICE_MANAGEMENT_FEE_CONTEXT_HOLDER,
            objectMapper.writeValueAsString(
                FirstInvoiceTransactionContext(
                    annualPlanDiscountContractIds = mapOf(
                        MonthYear(9, 2024) to setOf(1, 2, 3, 4)
                    )
                )
            )
        )
        val commandJson = objectMapper.writeValueAsString(command)
        val invoiceCommand = objectMapper.readValue(commandJson, InvoiceCommand::class.java)
        val contextHolder = invoiceCommand.tracingContext[InvoiceCommand.FIRST_INVOICE_MANAGEMENT_FEE_CONTEXT_HOLDER]

        val typeRef = object : TypeReference<Map<String, Map<String, Set<Long>>>>() {}
        val deserializedContextHolderWithRawMonthYear = objectMapper.readValue(contextHolder, typeRef)
        val deserializedContextHolder = deserializedContextHolderWithRawMonthYear
            .mapValues { (_, value) -> value.mapKeys { MonthYear.parseMonthYear(it.key) } }

        val annualPlanDiscountContractIds = deserializedContextHolder["annualPlanDiscountContractIds"] ?: emptyMap()

        assertEquals(1, annualPlanDiscountContractIds.size)
        assertEquals(annualPlanDiscountContractIds.entries.first().key, MonthYear(9, 2024))
        assertEquals(annualPlanDiscountContractIds.entries.first().value, setOf<Long>(1, 2, 3, 4))
    }

    private fun invoiceCommand() = InvoiceCommand(
        transactionId = UUID.randomUUID().toString(),
        transactionType = TransactionType.ANNUAL_PLAN_INVOICE,
        companyId = 1,
        dateRange = DateRange(
            LocalDateTime.now(),
            LocalDateTime.now()
        ),
        transactionDate = LocalDateTime.now(),
        cycle = InvoiceCycle.YEARLY,
        status = TransactionStatus.DATA_RECONCILING
    )
}