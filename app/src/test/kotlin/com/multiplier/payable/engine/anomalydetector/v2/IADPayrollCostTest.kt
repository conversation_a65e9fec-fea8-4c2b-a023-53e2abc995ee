package com.multiplier.payable.engine.anomalydetector.v2

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.payable.adapters.PayrollServiceAdapter
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.util.dto.payroll.CompanyMemberPayWrapper
import com.multiplier.core.util.dto.payroll.CompanyPayrollWrapper
import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.payable.engine.anomalydetector.rules.PayRollDifferenceRule
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payroll.schema.Payroll.PayrollCycle
import com.multiplier.schema.common.Common
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension

import org.mockito.Mockito.mock
import org.mockito.Mockito.`when` as whenever
import org.mockito.kotlin.any
import java.time.LocalDate

@ExtendWith(MockitoExtension::class)
class IADPayrollCostTest {
    private val payrollServiceAdapter = mock(PayrollServiceAdapter::class.java)
    private val featureFlagService = mock(FeatureFlagService::class.java)
    private val jpaAnomalyReportRepository = mock(JpaAnomalyReportRepository::class.java)
    private val iadPayrollCost =
        PayRollDifferenceRule(
            payrollServiceAdapter,
            featureFlagService
        )

    init {
        // Setup feature flag to always return true
        val mockedGbFeatureResult = mock(GBFeatureResult::class.java)
        whenever(mockedGbFeatureResult.on).thenReturn(true)
        whenever(featureFlagService.feature(any(), any())).thenReturn(mockedGbFeatureResult)
    }

    @Test
    fun `should log error when payroll data is empty`() {
        // GIVEN
        val command = mock(InvoiceCommand::class.java)
        val payable = mock(JpaCompanyPayable::class.java)
        whenever(payable.companyId).thenReturn(1L)
        whenever(payable.month).thenReturn(3)
        whenever(payable.year).thenReturn(2024)

        val request =
            InvoiceAnomalyDetectorRequest
                .builder()
                .payable(payable)
                .invoiceDTO(mock())
                .build()

        whenever(payrollServiceAdapter.getCompaniesPayroll(any(), any(), any())).thenReturn(emptyList())

        // WHEN
        iadPayrollCost.detect(command, request)

        // THEN
        // Verify that log.error was called (manually check logs in real tests)
    }

    @Test
    fun `should validate invoices correctly`() {
        // GIVEN
        val command = mock(InvoiceCommand::class.java)
        val payable = mock(JpaCompanyPayable::class.java)
        whenever(payable.companyId).thenReturn(1L)
        whenever(payable.month).thenReturn(3)
        whenever(payable.year).thenReturn(2024)

        val invoiceDTO =
            InvoiceDTO
                .builder()
                .lineItems(
                    listOf(
                        LineItemDTO
                            .builder()
                            .contractId(123L)
                            .itemType(LineItemType.EOR_SALARY_DISBURSEMENT)
                            .unitAmount(1000.0)
                            .amountInBaseCurrency(1000.0)
                            .quantity(1.0)
                            .startPayCycleDate(LocalDate.of(2024, 3, 1))
                            .endPayCycleDate(LocalDate.of(2024, 3, 31))
                            .build(),
                    ),
                ).build()

        val request =
            InvoiceAnomalyDetectorRequest
                .builder()
                .payable(payable)
                .invoiceDTO(invoiceDTO)
                .build()

        val payrollResponse =
            listOf(
                CompanyPayrollWrapper(
                    companyId = 1L,
                    memberPays =
                        listOf(
                            CompanyMemberPayWrapper(
                                contract = mock<ContractOuterClass.Contract>(),
                                totalExpenseAmount = 200.0,
                                amountTotalCost = 1200.0,
                                startDate = LocalDate.of(2024, 3, 1),
                                endDate = LocalDate.of(2024, 3, 31),
                            ),
                        ),
                ),
            )
        whenever(payrollServiceAdapter.getCompaniesPayroll(any(), any(), any())).thenReturn(payrollResponse)

        // WHEN
        iadPayrollCost.detect(command, request)

        // THEN
        // Assertions or log verification here
    }

    @Test
    fun `should log error when contract ID is missing in payroll data`() {
        // GIVEN
        val command = mock<InvoiceCommand>()

        val payable = mock<JpaCompanyPayable>()
        whenever(payable.companyId).thenReturn(1L)
        whenever(payable.month).thenReturn(3)
        whenever(payable.year).thenReturn(2024)

        val invoiceDTO =
            InvoiceDTO
                .builder()
                .lineItems(
                    listOf(
                        LineItemDTO
                            .builder()
                            .contractId(999L) // Contract ID not in payroll data
                            .itemType(LineItemType.EOR_SALARY_DISBURSEMENT)
                            .unitAmount(1000.0)
                            .quantity(1.0)
                            .startPayCycleDate(LocalDate.of(2024, 3, 1))
                            .endPayCycleDate(LocalDate.of(2024, 3, 31))
                            .build(),
                    ),
                ).build()

        val request =
            InvoiceAnomalyDetectorRequest
                .builder()
                .payable(payable)
                .invoiceDTO(invoiceDTO)
                .build()

        val payrollResponse =
            listOf(
                CompanyPayrollWrapper(
                    companyId = 1L,
                    memberPays =
                        listOf(
                            CompanyMemberPayWrapper(
                                contract = mock<ContractOuterClass.Contract>(),
                                totalExpenseAmount = 200.0,
                                amountTotalCost = 1200.0,
                                startDate = LocalDate.of(2024, 3, 1),
                                endDate = LocalDate.of(2024, 3, 31),
                            ),
                        ),
                ),
            )

        whenever(payrollServiceAdapter.getCompaniesPayroll(any(), any(), any())).thenReturn(payrollResponse)

        // WHEN
        iadPayrollCost.detect(command, request)

        // THEN
        // Verify that the log contains the expected error
    }

    @Test
    fun `should log error when salary amount does not match expected payroll`() {
        // GIVEN
        val command = mock<InvoiceCommand>()
        val payable = mock<JpaCompanyPayable>()
        whenever(payable.companyId).thenReturn(1L)
        whenever(payable.month).thenReturn(3)
        whenever(payable.year).thenReturn(2024)

        val invoiceDTO =
            InvoiceDTO
                .builder()
                .lineItems(
                    listOf(
                        LineItemDTO
                            .builder()
                            .contractId(123L)
                            .itemType(LineItemType.EOR_SALARY_DISBURSEMENT)
                            .unitAmount(1500.0) // Incorrect amount
                            .quantity(1.0)
                            .amountInBaseCurrency(1500.0)
                            .startPayCycleDate(LocalDate.of(2024, 3, 1))
                            .endPayCycleDate(LocalDate.of(2024, 3, 31))
                            .build(),
                    ),
                ).build()

        val request =
            InvoiceAnomalyDetectorRequest
                .builder()
                .payable(payable)
                .invoiceDTO(invoiceDTO)
                .build()

        val payrollResponse =
            listOf(
                CompanyPayrollWrapper(
                    companyId = 1L,
                    memberPays =
                        listOf(
                            CompanyMemberPayWrapper(
                                contract = mock<ContractOuterClass.Contract>(),
                                totalExpenseAmount = 200.0,
                                amountTotalCost = 1200.0, // Expected total salary should be 1200 - 200 = 1000
                                startDate = LocalDate.of(2024, 3, 1),
                                endDate = LocalDate.of(2024, 3, 31),
                            ),
                        ),
                ),
            )

        whenever(payrollServiceAdapter.getCompaniesPayroll(any(), any(), any())).thenReturn(payrollResponse)

        // WHEN
        iadPayrollCost.detect(command, request)

        // THEN
        // Verify that the log contains "Mismatch for contract ID: 123 Expected: 1000, Found: 1500"
    }

    @Test
    fun `should log error when expense amount does not match expected payroll`() {
        // GIVEN
        val command = mock<InvoiceCommand>()
        val payable = mock<JpaCompanyPayable>()
        whenever(payable.companyId).thenReturn(1L)
        whenever(payable.month).thenReturn(3)
        whenever(payable.year).thenReturn(2024)

        val invoiceDTO =
            InvoiceDTO
                .builder()
                .lineItems(
                    listOf(
                        LineItemDTO
                            .builder()
                            .contractId(123L)
                            .itemType(LineItemType.EOR_EXPENSE_DISBURSEMENT)
                            .unitAmount(500.0) // Incorrect expense amount
                            .quantity(1.0)
                            .amountInBaseCurrency(500.0)
                            .startPayCycleDate(LocalDate.of(2024, 3, 1))
                            .endPayCycleDate(LocalDate.of(2024, 3, 31))
                            .build(),
                    ),
                ).build()

        val request =
            InvoiceAnomalyDetectorRequest
                .builder()
                .payable(payable)
                .invoiceDTO(invoiceDTO)
                .build()

        val payrollResponse =
            listOf(
                CompanyPayrollWrapper(
                    companyId = 1L,
                    memberPays =
                        listOf(
                            CompanyMemberPayWrapper(
                                contract = mock<ContractOuterClass.Contract>(),
                                totalExpenseAmount = 200.0, // Expected expense is 200
                                amountTotalCost = 1200.0,
                                startDate = LocalDate.of(2024, 3, 1),
                                endDate = LocalDate.of(2024, 3, 31),
                            ),
                        ),
                ),
            )

        whenever(payrollServiceAdapter.getCompaniesPayroll(any(), any(), any())).thenReturn(payrollResponse)

        // WHEN
        iadPayrollCost.detect(command, request)

        // THEN
        // Verify that the log contains "Mismatch for contract ID: 123 Expected: 200, Found: 500"
    }

    @Test
    fun `should log error when payroll data is completely missing`() {
        // GIVEN
        val command = mock<InvoiceCommand>()
        val payable = mock<JpaCompanyPayable>()
        whenever(payable.companyId).thenReturn(1L)
        whenever(payable.month).thenReturn(3)
        whenever(payable.year).thenReturn(2024)

        val invoiceDTO = InvoiceDTO.builder().lineItems(emptyList()).build()
        val request =
            InvoiceAnomalyDetectorRequest
                .builder()
                .payable(payable)
                .invoiceDTO(invoiceDTO)
                .build()

        whenever(payrollServiceAdapter.getCompaniesPayroll(any(), any(), any())).thenReturn(emptyList())

        // WHEN
        iadPayrollCost.detect(command, request)

        // THEN
        // Verify that the log contains "Payroll data is empty for company 1"
    }

    @Test
    fun `should return success when payroll matches invoice line items`() {
        val command = mock<InvoiceCommand>()
        val payable = mock<JpaCompanyPayable>()
        whenever(payable.companyId).thenReturn(1L)
        whenever(payable.month).thenReturn(3)
        whenever(payable.year).thenReturn(2024)

        val invoiceDTO =
            InvoiceDTO
                .builder()
                .lineItems(
                    listOf(
                        LineItemDTO
                            .builder()
                            .contractId(123L)
                            .itemType(LineItemType.EOR_SALARY_DISBURSEMENT)
                            .unitAmount(1000.0)
                            .quantity(1.0)
                            .amountInBaseCurrency(1000.0)
                            .startPayCycleDate(LocalDate.of(2024, 3, 1))
                            .endPayCycleDate(LocalDate.of(2024, 3, 31))
                            .build(),
                        LineItemDTO
                            .builder()
                            .contractId(123L)
                            .itemType(LineItemType.EOR_EXPENSE_DISBURSEMENT)
                            .unitAmount(200.0)
                            .quantity(1.0)
                            .amountInBaseCurrency(200.0)
                            .startPayCycleDate(LocalDate.of(2024, 3, 1))
                            .endPayCycleDate(LocalDate.of(2024, 3, 31))
                            .build(),
                    ),
                ).build()

        val mockedContract =
            ContractOuterClass.Contract
                .newBuilder()
                .setId(123L)
                .build()

        val request =
            InvoiceAnomalyDetectorRequest
                .builder()
                .payable(payable)
                .invoiceDTO(invoiceDTO)
                .build()

        val startDate = LocalDate.of(2024, 3, 1)
        val endDate = LocalDate.of(2024, 3, 31)

        val payrollCycle =
            PayrollCycle
                .newBuilder()
                .setStartDate(
                    Common.Date
                        .newBuilder()
                        .setMonth(startDate.monthValue)
                        .setDay(startDate.dayOfMonth)
                        .setYear(startDate.year)
                        .build(),
                ).setEndDate(
                    Common.Date
                        .newBuilder()
                        .setMonth(endDate.monthValue)
                        .setDay(endDate.dayOfMonth)
                        .setYear(endDate.year)
                        .build(),
                ).build()
        val payrollResponse =
            listOf(
                CompanyPayrollWrapper(
                    companyId = 1L,
                    memberPays =
                        listOf(
                            CompanyMemberPayWrapper(
                                contract = mockedContract,
                                totalExpenseAmount = 200.0,
                                amountTotalCost = 1200.0,
                                payrollCycle = payrollCycle,
                                startDate = LocalDate.of(2024, 3, 1),
                                endDate = LocalDate.of(2024, 3, 31),
                            ),
                        ),
                ),
            )

        whenever(payrollServiceAdapter.getCompaniesPayroll(any(), any(), any())).thenReturn(payrollResponse)

        iadPayrollCost.detect(command, request)
    }
}
