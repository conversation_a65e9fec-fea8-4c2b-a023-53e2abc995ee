package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.AnomalyResultType
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorType
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.invoice.api.InvoiceAdapter
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaInvoice
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.payable.engine.anomalydetector.provider.NetsuiteAlreadyBilledAmountService
import com.multiplier.payable.engine.anomalydetector.provider.NetsuiteInvoiceProvider
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CompanyPayableType
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.types.PayableStatus
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class AmountAlreadyBilledMismatchMfeeRuleTest {

    private lateinit var featureFlagService: FeatureFlagService
    private lateinit var jpaAnomalyReportRepository: JpaAnomalyReportRepository
    private lateinit var netsuiteAlreadyBilledAmountService: NetsuiteAlreadyBilledAmountService
    private lateinit var netsuiteInvoiceProvider: NetsuiteInvoiceProvider
    private lateinit var invoiceAdapter: InvoiceAdapter
    private lateinit var jpaCompanyPayableRepository: JpaCompanyPayableRepository
    private lateinit var rule: AmountAlreadyBilledMismatchMfeeRule
    private lateinit var mockCommand: InvoiceCommand

    @BeforeEach
    fun setUp() {
        featureFlagService = mockk(relaxed = true)
        jpaAnomalyReportRepository = mockk(relaxed = true)
        netsuiteAlreadyBilledAmountService = mockk(relaxed = true)
        netsuiteInvoiceProvider = mockk(relaxed = true)
        invoiceAdapter = mockk(relaxed = true)
        jpaCompanyPayableRepository = mockk(relaxed = true)

        rule = AmountAlreadyBilledMismatchMfeeRule(
            featureFlagService,
            netsuiteAlreadyBilledAmountService
        )

        mockCommand = createInvoiceCommand()

        // Mock feature flag to be enabled by default
        val mockGbFeatureResult = mockk<GBFeatureResult>()
        every { mockGbFeatureResult.on } returns true
        every { featureFlagService.feature(any(), any()) } returns mockGbFeatureResult

        // Mock the repository save method to avoid ClassCastException
        every { jpaAnomalyReportRepository.save(any()) } returns mockk(relaxed = true)
    }

    @Test
    fun `should have correct rule properties`() {
        assertEquals(DetectionRuleType.AMOUNT_ALREADY_BILLED_MISMATCH_MFEE, rule.type)
        assertEquals(InvoiceAnomalyDetectorType.AMOUNT_ALREADY_BILLED_MISMATCH_MFEE, rule.detectorType)
        assertEquals("AmountAlreadyBilledMismatchMfee", rule.ruleName)
        assertEquals("ENABLE_AMOUNT_ALREADY_BILLED_MISMATCH_MFEE_CHECK", rule.featureFlagName)
    }

    @Test
    fun `should return warning when no BILLED_MANAGEMENT_FEE line items found`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.GROSS_SALARY, 1000.0, 123L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success) // WARNING doesn't block processing
        assertEquals(AnomalyResultType.WARN, result.severity)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("No BILLED_MANAGEMENT_FEE line items found - validation skipped"))
    }

    @Test
    fun `should return success when amounts match exactly`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_MANAGEMENT_FEE, 500.0, 123L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // Mock NetSuite data to return matching amount
        mockNetsuiteData(500.0)

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Amount validation passed - all 1 BILLED_MANAGEMENT_FEE amounts match NetSuite first invoice amounts"))
    }

    @Test
    fun `should return success when multiple line items have matching amounts`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_MANAGEMENT_FEE, 500.0, 123L),
            createLineItemDTO(LineItemType.BILLED_MANAGEMENT_FEE, 300.0, 456L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // Mock NetSuite data to return matching amounts for both contracts
        every {
            netsuiteAlreadyBilledAmountService.getNetsuiteAlreadyBilledAmount(
                any(), 123L, 2024, 3, LineItemType.MANAGEMENT_FEE_EOR
            )
        } returns 500.0
        every {
            netsuiteAlreadyBilledAmountService.getNetsuiteAlreadyBilledAmount(
                any(), 456L, 2024, 3, LineItemType.MANAGEMENT_FEE_EOR
            )
        } returns 300.0

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Amount validation passed - all 2 BILLED_MANAGEMENT_FEE amounts match NetSuite first invoice amounts"))
    }

    @Test
    fun `should return failure when amounts do not match`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_MANAGEMENT_FEE, 500.0, 123L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // Mock NetSuite data to return different amount
        mockNetsuiteData(750.0)

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("System's already billed management fee amount 500.00 USD does not match NetSuite's first invoice amount 750.00 USD for contract 123"))
    }

    @Test
    fun `should skip validation when line item has null contract ID`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_MANAGEMENT_FEE, 500.0, null), // null contract ID
            createLineItemDTO(LineItemType.BILLED_MANAGEMENT_FEE, 300.0, 456L)  // valid contract ID
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // Mock NetSuite data for the valid contract
        mockNetsuiteData(300.0, 456L)

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        // The message should indicate that validation passed for the valid line item (the one with null contract ID is skipped)
        assertTrue(result.messages[0].contains("Amount validation passed") && result.messages[0].contains("BILLED_MANAGEMENT_FEE amounts match NetSuite first invoice amounts"))
    }

    @Test
    fun `should skip validation when line item has null unit amount`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_MANAGEMENT_FEE, null, 123L), // null unit amount
            createLineItemDTO(LineItemType.BILLED_MANAGEMENT_FEE, 300.0, 456L)  // valid unit amount
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // Mock NetSuite data for the valid contract
        mockNetsuiteData(300.0, 456L)

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        // The message should indicate that validation passed for the valid line item (the one with null unit amount is skipped)
        assertTrue(result.messages[0].contains("Amount validation passed") && result.messages[0].contains("BILLED_MANAGEMENT_FEE amounts match NetSuite first invoice amounts"))
    }

    @Test
    fun `should skip validation when NetSuite data is not found`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_MANAGEMENT_FEE, 500.0, 123L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // Mock NetSuite to return no data
        every {
            netsuiteAlreadyBilledAmountService.getNetsuiteAlreadyBilledAmount(
                any(), 123L, 2024, 3, LineItemType.MANAGEMENT_FEE_EOR
            )
        } returns null

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        // When NetSuite data is not found, the validation still passes but skips the problematic line items
        assertTrue(result.messages[0].contains("Amount validation passed") && result.messages[0].contains("BILLED_MANAGEMENT_FEE amounts match NetSuite first invoice amounts"))
    }

    @Test
    fun `should skip validation when no first invoices with external IDs found`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_MANAGEMENT_FEE, 500.0, 123L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // Mock NetSuite service to return null (no external IDs found)
        every {
            netsuiteAlreadyBilledAmountService.getNetsuiteAlreadyBilledAmount(
                any(), 123L, 2024, 3, LineItemType.MANAGEMENT_FEE_EOR
            )
        } returns null

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        // When no external IDs are found, the validation still passes but skips the problematic line items
        assertTrue(result.messages[0].contains("Amount validation passed") && result.messages[0].contains("BILLED_MANAGEMENT_FEE amounts match NetSuite first invoice amounts"))
    }

    @Test
    fun `should return success when feature flag is disabled`() {
        // Given
        val mockGbFeatureResult = mockk<GBFeatureResult>()
        every { mockGbFeatureResult.on } returns false
        every { featureFlagService.feature(any(), any()) } returns mockGbFeatureResult

        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_MANAGEMENT_FEE, 500.0, 123L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Rule 'AmountAlreadyBilledMismatchMfee' is disabled by feature flag"))
    }

    @Test
    fun `should return success when invoice data is null`() {
        // Given
        val companyPayable = createCompanyPayable()
        val request = InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(null) // Null invoice
            .payable(companyPayable)
            .build()

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice data or company payable is null - validation skipped"))
    }

    @Test
    fun `should return success when company payable is null`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_MANAGEMENT_FEE, 500.0, 123L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val request = InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(invoiceDTO)
            .payable(null) // Null payable
            .build()

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice data or company payable is null - validation skipped"))
    }

    @Test
    fun `should handle exception during validation`() {
        // Given
        val lineItems = listOf(
            createLineItemDTO(LineItemType.BILLED_MANAGEMENT_FEE, 500.0, 123L)
        )
        val invoiceDTO = createInvoiceDTO(lineItems)
        val companyPayable = createCompanyPayable()
        val request = createRequest(invoiceDTO, companyPayable)

        // Mock to return null (which is what the service does when exceptions occur internally)
        every {
            netsuiteAlreadyBilledAmountService.getNetsuiteAlreadyBilledAmount(
                any(), any(), any(), any(), any()
            )
        } returns null

        // When
        val result = rule.detect(mockCommand, request)

        // Then
        // The rule handles null returns gracefully by skipping validation for problematic line items
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Amount validation passed") && result.messages[0].contains("BILLED_MANAGEMENT_FEE amounts match NetSuite first invoice amounts"))
    }

    // Helper methods
    private fun createInvoiceCommand(): InvoiceCommand {
        val transactionDate = LocalDateTime.of(2024, 3, 15, 10, 0, 0)
        return InvoiceCommand(
            transactionId = "test-transaction-id",
            transactionType = TransactionType.SECOND_INVOICE,
            companyId = 123L,
            dateRange = DateRange(transactionDate, transactionDate),
            transactionDate = transactionDate,
            cycle = InvoiceCycle.MONTHLY
        )
    }

    private fun createInvoiceDTO(lineItems: List<LineItemDTO>): InvoiceDTO {
        return InvoiceDTO.builder()
            .billingCurrencyCode(CurrencyCode.USD)
            .lineItems(lineItems)
            .build()
    }

    private fun createLineItemDTO(itemType: LineItemType, unitAmount: Double?, contractId: Long?): LineItemDTO {
        return LineItemDTO.builder()
            .itemType(itemType)
            .unitAmount(unitAmount)
            .contractId(contractId)
            .build()
    }

    private fun createCompanyPayable(): JpaCompanyPayable {
        return mockk<JpaCompanyPayable>().apply {
            every { year } returns 2024
            every { month } returns 3
            every { id } returns 1L
        }
    }

    private fun createRequest(invoiceDTO: InvoiceDTO, companyPayable: JpaCompanyPayable): InvoiceAnomalyDetectorRequest {
        return InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(invoiceDTO)
            .payable(companyPayable)
            .build()
    }

    private fun mockNetsuiteData(expectedAmount: Double, contractId: Long = 123L) {
        every {
            netsuiteAlreadyBilledAmountService.getNetsuiteAlreadyBilledAmount(
                any(), contractId, 2024, 3, LineItemType.MANAGEMENT_FEE_EOR
            )
        } returns expectedAmount
    }

    private fun createFirstInvoicePayable(): JpaCompanyPayable {
        return mockk<JpaCompanyPayable>().apply {
            every { status } returns PayableStatus.AUTHORIZED
            every { type } returns CompanyPayableType.FIRST_INVOICE
            every { invoice } returns createJpaInvoice()
            every { id } returns 2L
        }
    }

    private fun createJpaInvoice(): JpaInvoice {
        return mockk<JpaInvoice>().apply {
            every { externalId } returns "NS123"
        }
    }

    private fun createNetsuiteInvoice(): JpaInvoice {
        return mockk<JpaInvoice>().apply {
            every { externalId } returns "NS123"
        }
    }

    private fun createNetsuiteInvoiceDTO(lineItems: List<LineItemDTO>): InvoiceDTO {
        return InvoiceDTO.builder()
            .externalId("NS123")
            .lineItems(lineItems)
            .build()
    }
}
