package com.multiplier.payable.engine.reconciler.order

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorage
import com.multiplier.payable.engine.reconciler.companypayable.storage.CompanyPayableStorageContext
import com.multiplier.payable.engine.reconciler.data.invoice.InvoiceDataProvider
import com.multiplier.payable.engine.reconciler.data.item.ItemStoreDataProvider
import com.multiplier.payable.engine.reconciler.diff.reconciler.InvoiceDiffReconciler
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import java.time.LocalDateTime

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class OrderFormAdvanceDataReconcilerTest {

    @Mock
    private lateinit var templateProvider: TransactionTemplateProvider

    @Mock
    private lateinit var activeInvoiceProvider: InvoiceDataProvider

    @Mock
    private lateinit var itemStoreProvider: ItemStoreDataProvider

    @Mock
    private lateinit var diffReconciler: InvoiceDiffReconciler

    @Mock
    private lateinit var companyPayableStorage: CompanyPayableStorage

    @InjectMocks
    private lateinit var reconciler: OrderFormAdvanceDataReconciler

    @Test
    fun `test getTransactionType`() {
        Assertions.assertEquals(TransactionType.ORDER_FORM_ADVANCE, reconciler.transactionType)
    }

    @Test
    fun `test handle`() {
        val dateRange1 = DateRange(
            startDate = LocalDateTime.of(2025, 3, 1, 0, 0),
            endDate = LocalDateTime.of(2025, 3, 31, 0, 0),
        )
        val command = mock<InvoiceCommand> {
            on { companyId } doReturn 1L
            on { transactionType } doReturn TransactionType.ORDER_FORM_ADVANCE
            on { transactionId } doReturn "txn1"
            on { transactionDate } doReturn LocalDateTime.now()
            on { getMonthYear() } doReturn MonthYear(month = 1, year = 2023)
            on { cycle } doReturn mock()
            on { dateRange } doReturn dateRange1
        }
        val invoicedItem = mock<PayableItem>()
        doReturn(listOf(invoicedItem)).`when`(activeInvoiceProvider).fetchAndAggregateInvoiceItems(command)
        val template = mock<TransactionTemplate>()
        doReturn(template).`when`(templateProvider).findTemplateFor(
            transactionType = TransactionType.ORDER_FORM_ADVANCE,
            companyId = 1L,
        )
        val latestItem = mock<PayableItem>()
        val lineItemTypes = listOf(LineItemType.ORDER_FORM_ADVANCE_EOR)
        doReturn(lineItemTypes).`when`(template).lineItemTypes
        doReturn(listOf(latestItem)).`when`(itemStoreProvider).fetchLatest(command, lineItemTypes)
        val diffItem = mock<PayableItem> {
            on { billingDuration } doReturn DateRange(
                startDate = LocalDateTime.of(2025, 3, 1, 0, 0),
                endDate = LocalDateTime.of(2025, 3, 31, 0, 0),
            )
        }
        doReturn(false).`when`(template).skipIsrGeneration
        doReturn(listOf(diffItem)).`when`(diffReconciler).reconcile(
            command = command,
            template = template,
            old = listOf(invoicedItem),
            new = listOf(latestItem),
        )

        reconciler.handle(command)

        val expectedContext = CompanyPayableStorageContext(
            transactionId = command.transactionId,
            invoiceDate = command.transactionDate,
            monthYear = command.getMonthYear(),
            items = listOf(diffItem),
            companyId = command.companyId,
            cycle = command.cycle,
            cycleDuration = dateRange1,
            transactionType = command.transactionType,
            skipIsrGeneration = false,
        )
        Mockito.verify(companyPayableStorage).exchangeAndStore(expectedContext)
    }

}