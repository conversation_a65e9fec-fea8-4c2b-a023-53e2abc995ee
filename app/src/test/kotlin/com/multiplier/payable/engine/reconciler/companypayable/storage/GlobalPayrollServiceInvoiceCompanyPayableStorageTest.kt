package com.multiplier.payable.engine.reconciler.companypayable.storage

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.fx.EntityLevelFXConverter
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.reconciler.companypayable.builder.CompanyPayableBuilderContext
import com.multiplier.payable.engine.reconciler.companypayable.builder.CompanyPayableEntityBuilder
import com.multiplier.payable.engine.reconciler.companypayable.collector.CompanyPayableEntityDataCollector
import com.multiplier.payable.types.CountryCode
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.mock
import java.time.LocalDateTime

@ExtendWith(MockKExtension::class)
class GlobalPayrollServiceInvoiceCompanyPayableStorageTest {

    @MockK
    private lateinit var entityBuilder: CompanyPayableEntityBuilder

    @MockK
    private lateinit var entityDataCollector: CompanyPayableEntityDataCollector

    @MockK
    private lateinit var payableRepository: JpaCompanyPayableRepository

    @MockK
    private lateinit var fxConverter: EntityLevelFXConverter

    @InjectMockKs
    private lateinit var globalPayrollServiceInvoiceCompanyPayableStorage: GlobalPayrollServiceInvoiceCompanyPayableStorage

    @Test
    fun `exchangeAndStore should convert items with three percent topped up value and store entity`() {
        // Given
        val companyId = 456L
        val transactionId = "gp-service-transaction-123"
        val invoiceDate = LocalDateTime.now()
        val monthYear = MonthYear(9, 2024)
        val cycle = InvoiceCycle.MONTHLY
        val cycleDuration = DateRange(invoiceDate, invoiceDate.plusDays(30))
        val entityId = 789L

        val originalItems = listOf(
            PayableItem(
                companyId = companyId,
                entityId = entityId,
                contractId = 3L,
                amountInBaseCurrency = 1500.0,
                baseCurrency = "USD",
                month = 9,
                year = 2024,
                lineItemType = "SERVICE_FEE",
                originalTimestamp = System.currentTimeMillis(),
                cycle = cycle
            ),
            PayableItem(
                companyId = companyId,
                entityId = entityId,
                contractId = 4L,
                amountInBaseCurrency = 2500.0,
                baseCurrency = "EUR",
                month = 9,
                year = 2024,
                lineItemType = "PLATFORM_FEE",
                originalTimestamp = System.currentTimeMillis(),
                cycle = cycle
            )
        )

        val convertedItems = listOf(
            PayableItem(
                companyId = companyId,
                entityId = entityId,
                contractId = 3L,
                amountInBaseCurrency = 1500.0,
                baseCurrency = "USD",
                billableCost = 1545.0, // With 3% markup
                billingCurrency = "SGD",
                month = 9,
                year = 2024,
                lineItemType = "SERVICE_FEE",
                originalTimestamp = System.currentTimeMillis(),
                cycle = cycle
            ),
            PayableItem(
                companyId = companyId,
                entityId = entityId,
                contractId = 4L,
                amountInBaseCurrency = 2500.0,
                baseCurrency = "EUR",
                billableCost = 2575.0, // With 3% markup
                billingCurrency = "SGD",
                month = 9,
                year = 2024,
                lineItemType = "PLATFORM_FEE",
                originalTimestamp = System.currentTimeMillis(),
                cycle = cycle
            )
        )

        val context = CompanyPayableStorageContext(
            invoiceDate = invoiceDate,
            monthYear = monthYear,
            cycle = cycle,
            cycleDuration = cycleDuration,
            items = originalItems,
            companyId = companyId,
            transactionId = transactionId,
            transactionType = TransactionType.GP_SERVICE_INVOICE,
            entityId = entityId
        )

        val exchangedContext = context.copy(items = convertedItems)

        val entityBuilderContext = CompanyPayableBuilderContext(
            company = mock(),
            companyCountryCode = CountryCode.SGP,
            contracts = mock(),
            countries = mock(),
            members = mock(),
            storeContext = mock(),
            taxIdentifierMap = mock(),
            taxTypeMap = mock()
        )

        val entity = mockk<JpaCompanyPayable>()
        val savedEntity = mockk<JpaCompanyPayable>()
        val expectedId = 555L

        every { fxConverter.convertWithThreePercentToppedUpValue(originalItems, null, TransactionType.GP_SERVICE_INVOICE) } returns convertedItems
        every { entityDataCollector.collect(exchangedContext) } returns entityBuilderContext
        every { entityBuilder.build(entityBuilderContext) } returns entity
        every { savedEntity.id } returns expectedId
        every { payableRepository.save(entity) } returns savedEntity

        // When
        val result = globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(context)

        // Then
        assertEquals(expectedId, result)

        verify { fxConverter.convertWithThreePercentToppedUpValue(originalItems, null, TransactionType.GP_SERVICE_INVOICE) }
        verify { entityDataCollector.collect(exchangedContext) }
        verify { entityBuilder.build(entityBuilderContext) }
        verify { payableRepository.save(entity) }
    }

    @Test
    fun `exchangeAndStore should handle single item conversion`() {
        // Given
        val companyId = 789L
        val transactionId = "gp-service-single-item"
        val invoiceDate = LocalDateTime.now()
        val monthYear = MonthYear(10, 2024)
        val cycle = InvoiceCycle.MONTHLY
        val cycleDuration = DateRange(invoiceDate, invoiceDate.plusDays(90))

        val originalItems = listOf(
            PayableItem(
                companyId = companyId,
                contractId = 5L,
                amountInBaseCurrency = 3000.0,
                baseCurrency = "GBP",
                month = 10,
                year = 2024,
                lineItemType = "MANAGEMENT_FEE",
                originalTimestamp = System.currentTimeMillis(),
                cycle = cycle
            )
        )

        val convertedItems = listOf(
            PayableItem(
                companyId = companyId,
                contractId = 5L,
                amountInBaseCurrency = 3000.0,
                baseCurrency = "GBP",
                billableCost = 3090.0, // With 3% markup
                billingCurrency = "USD",
                month = 10,
                year = 2024,
                lineItemType = "MANAGEMENT_FEE",
                originalTimestamp = System.currentTimeMillis(),
                cycle = cycle
            )
        )

        val context = CompanyPayableStorageContext(
            invoiceDate = invoiceDate,
            monthYear = monthYear,
            cycle = cycle,
            cycleDuration = cycleDuration,
            items = originalItems,
            companyId = companyId,
            transactionId = transactionId,
            transactionType = TransactionType.GP_SERVICE_INVOICE
        )

        val exchangedContext = context.copy(items = convertedItems)

        val entityBuilderContext = CompanyPayableBuilderContext(
            company = mock(),
            companyCountryCode = CountryCode.GBR,
            contracts = mock(),
            countries = mock(),
            members = mock(),
            storeContext = mock(),
            taxIdentifierMap = mock(),
            taxTypeMap = mock()
        )

        val entity = mockk<JpaCompanyPayable>()
        val savedEntity = mockk<JpaCompanyPayable>()
        val expectedId = 777L

        every { fxConverter.convertWithThreePercentToppedUpValue(originalItems, null, TransactionType.GP_SERVICE_INVOICE) } returns convertedItems
        every { entityDataCollector.collect(exchangedContext) } returns entityBuilderContext
        every { entityBuilder.build(entityBuilderContext) } returns entity
        every { savedEntity.id } returns expectedId
        every { payableRepository.save(entity) } returns savedEntity

        // When
        val result = globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(context)

        // Then
        assertEquals(expectedId, result)

        verify { fxConverter.convertWithThreePercentToppedUpValue(originalItems, null, TransactionType.GP_SERVICE_INVOICE) }
        verify { entityDataCollector.collect(exchangedContext) }
        verify { entityBuilder.build(entityBuilderContext) }
        verify { payableRepository.save(entity) }
    }

    @Test
    fun `exchangeAndStore should handle empty items list`() {
        // Given
        val companyId = 999L
        val transactionId = "gp-service-empty-items"
        val invoiceDate = LocalDateTime.now()
        val monthYear = MonthYear(11, 2024)
        val cycle = InvoiceCycle.MONTHLY
        val cycleDuration = DateRange(invoiceDate, invoiceDate.plusDays(365))

        val emptyItems = emptyList<PayableItem>()

        val context = CompanyPayableStorageContext(
            invoiceDate = invoiceDate,
            monthYear = monthYear,
            cycle = cycle,
            cycleDuration = cycleDuration,
            items = emptyItems,
            companyId = companyId,
            transactionId = transactionId,
            transactionType = TransactionType.GP_SERVICE_INVOICE
        )

        val exchangedContext = context.copy(items = emptyItems)

        val entityBuilderContext = CompanyPayableBuilderContext(
            company = mock(),
            companyCountryCode = CountryCode.AUS,
            contracts = mock(),
            countries = mock(),
            members = mock(),
            storeContext = mock(),
            taxIdentifierMap = mock(),
            taxTypeMap = mock()
        )

        val entity = mockk<JpaCompanyPayable>()
        val savedEntity = mockk<JpaCompanyPayable>()
        val expectedId = 888L

        every { fxConverter.convertWithThreePercentToppedUpValue(emptyItems, null, TransactionType.GP_SERVICE_INVOICE) } returns emptyItems
        every { entityDataCollector.collect(exchangedContext) } returns entityBuilderContext
        every { entityBuilder.build(entityBuilderContext) } returns entity
        every { savedEntity.id } returns expectedId
        every { payableRepository.save(entity) } returns savedEntity

        // When
        val result = globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(context)

        // Then
        assertEquals(expectedId, result)

        verify { fxConverter.convertWithThreePercentToppedUpValue(emptyItems, null, TransactionType.GP_SERVICE_INVOICE) }
        verify { entityDataCollector.collect(exchangedContext) }
        verify { entityBuilder.build(entityBuilderContext) }
        verify { payableRepository.save(entity) }
    }

    @Test
    fun `exchangeAndStore should handle different transaction types correctly`() {
        // Given
        val companyId = 111L
        val transactionId = "gp-service-different-type"
        val invoiceDate = LocalDateTime.now()
        val monthYear = MonthYear(12, 2024)
        val cycle = InvoiceCycle.MONTHLY
        val cycleDuration = DateRange(invoiceDate, invoiceDate.plusDays(30))

        val originalItems = listOf(
            PayableItem(
                companyId = companyId,
                contractId = 6L,
                amountInBaseCurrency = 500.0,
                baseCurrency = "CAD",
                month = 12,
                year = 2024,
                lineItemType = "SETUP_FEE",
                originalTimestamp = System.currentTimeMillis(),
                cycle = cycle
            )
        )

        val convertedItems = listOf(
            PayableItem(
                companyId = companyId,
                contractId = 6L,
                amountInBaseCurrency = 500.0,
                baseCurrency = "CAD",
                billableCost = 515.0, // With 3% markup
                billingCurrency = "USD",
                month = 12,
                year = 2024,
                lineItemType = "SETUP_FEE",
                originalTimestamp = System.currentTimeMillis(),
                cycle = cycle
            )
        )

        val context = CompanyPayableStorageContext(
            invoiceDate = invoiceDate,
            monthYear = monthYear,
            cycle = cycle,
            cycleDuration = cycleDuration,
            items = originalItems,
            companyId = companyId,
            transactionId = transactionId,
            transactionType = TransactionType.GP_SERVICE_INVOICE, // Specific transaction type
            skipIsrGeneration = true
        )

        val exchangedContext = context.copy(items = convertedItems)

        val entityBuilderContext = CompanyPayableBuilderContext(
            company = mock(),
            companyCountryCode = CountryCode.CAN,
            contracts = mock(),
            countries = mock(),
            members = mock(),
            storeContext = mock(),
            taxIdentifierMap = mock(),
            taxTypeMap = mock()
        )

        val entity = mockk<JpaCompanyPayable>()
        val savedEntity = mockk<JpaCompanyPayable>()
        val expectedId = 222L

        every { fxConverter.convertWithThreePercentToppedUpValue(originalItems, null, TransactionType.GP_SERVICE_INVOICE) } returns convertedItems
        every { entityDataCollector.collect(exchangedContext) } returns entityBuilderContext
        every { entityBuilder.build(entityBuilderContext) } returns entity
        every { savedEntity.id } returns expectedId
        every { payableRepository.save(entity) } returns savedEntity

        // When
        val result = globalPayrollServiceInvoiceCompanyPayableStorage.exchangeAndStore(context)

        // Then
        assertEquals(expectedId, result)

        verify { fxConverter.convertWithThreePercentToppedUpValue(originalItems, null, TransactionType.GP_SERVICE_INVOICE) }
        verify { entityDataCollector.collect(exchangedContext) }
        verify { entityBuilder.build(entityBuilderContext) }
        verify { payableRepository.save(entity) }
    }
}
