package com.multiplier.payable.engine.anomalydetector.rules.processor

import com.multiplier.core.anomalydetector.model.AnomalyResultType
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.payable.engine.anomalydetector.helper.AnomalyDetectionResult
import com.multiplier.payable.engine.anomalydetector.request.IADRequestBuilder
import com.multiplier.payable.engine.anomalydetector.rules.DetectionRuleType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CurrencyCode
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class AnomalyDetectionRulesProcessorTest {

    private lateinit var mockRule1: AnomalyDetectionRule
    private lateinit var mockRule2: AnomalyDetectionRule
    private lateinit var mockRule3: AnomalyDetectionRule
    private lateinit var mockIADRequestBuilder: IADRequestBuilder
    private lateinit var processor: AnomalyDetectionRulesProcessor
    private lateinit var mockCommand: InvoiceCommand

    @BeforeEach
    fun setUp() {
        mockRule1 = mockk(relaxed = true)
        mockRule2 = mockk(relaxed = true)
        mockRule3 = mockk(relaxed = true)
        mockIADRequestBuilder = mockk(relaxed = true)
        mockCommand = createInvoiceCommand()

        // Setup mock rules with different types
        every { mockRule1.type } returns DetectionRuleType.BILLING_CURRENCY
        every { mockRule2.type } returns DetectionRuleType.REFERENCE_TEXT_CHECK
        every { mockRule3.type } returns DetectionRuleType.HIGH_VALUE_INVOICE

        processor = AnomalyDetectionRulesProcessor(
            listOf(mockRule1, mockRule2, mockRule3),
            mockIADRequestBuilder,
            mockk(relaxed = true) // jpaAnomalyReportRepository
        )
    }

    @Test
    fun `should return true when all rules pass`() {
        // Given
        val request = createRequest()
        val requests = listOf(request)
        val ruleTypes = listOf(DetectionRuleType.BILLING_CURRENCY, DetectionRuleType.REFERENCE_TEXT_CHECK, DetectionRuleType.HIGH_VALUE_INVOICE)

        // Mock IAD request builder to return requests
        every { mockIADRequestBuilder.build(mockCommand) } returns requests

        // Mock all rules to return success
        every { mockRule1.detect(mockCommand, request) } returns createSuccessResult("Rule 1 passed")
        every { mockRule2.detect(mockCommand, request) } returns createSuccessResult("Rule 2 passed")
        every { mockRule3.detect(mockCommand, request) } returns createSuccessResult("Rule 3 passed")

        // When
        val result = processor.detect(mockCommand, ruleTypes)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should return true when rules have mixed success and warning results`() {
        // Given
        val request = createRequest()
        val requests = listOf(request)
        val ruleTypes = listOf(DetectionRuleType.BILLING_CURRENCY, DetectionRuleType.REFERENCE_TEXT_CHECK, DetectionRuleType.HIGH_VALUE_INVOICE)

        // Mock IAD request builder to return requests
        every { mockIADRequestBuilder.build(mockCommand) } returns requests

        // Mock rules to return mixed results (WARNING is considered non-blocking)
        every { mockRule1.detect(mockCommand, request) } returns createSuccessResult("Rule 1 passed")
        every { mockRule2.detect(mockCommand, request) } returns createWarningResult("Rule 2 warning")
        every { mockRule3.detect(mockCommand, request) } returns createSuccessResult("Rule 3 passed")

        // When
        val result = processor.detect(mockCommand, ruleTypes)

        // Then
        assertTrue(result) // WARNING is non-blocking, so overall result should be true
    }

    @Test
    fun `should return false when rules have failure results`() {
        // Given
        val request = createRequest()
        val requests = listOf(request)
        val ruleTypes = listOf(DetectionRuleType.BILLING_CURRENCY, DetectionRuleType.REFERENCE_TEXT_CHECK, DetectionRuleType.HIGH_VALUE_INVOICE)

        // Mock IAD request builder to return requests
        every { mockIADRequestBuilder.build(mockCommand) } returns requests

        // Mock rules to return mixed results with failure (ERROR blocks processing)
        every { mockRule1.detect(mockCommand, request) } returns createSuccessResult("Rule 1 passed")
        every { mockRule2.detect(mockCommand, request) } returns createFailureResult("Rule 2 failed")
        every { mockRule3.detect(mockCommand, request) } returns createSuccessResult("Rule 3 passed")

        // When
        val result = processor.detect(mockCommand, ruleTypes)

        // Then
        assertFalse(result) // ERROR blocks processing, so overall result should be false
    }

    @Test
    fun `should return true when request list is empty`() {
        // Given
        val requests = emptyList<InvoiceAnomalyDetectorRequest>()
        val ruleTypes = listOf(DetectionRuleType.BILLING_CURRENCY)

        // Mock IAD request builder to return empty requests
        every { mockIADRequestBuilder.build(mockCommand) } returns requests

        // When
        val result = processor.detect(mockCommand, ruleTypes)

        // Then
        assertTrue(result) // Empty list should return true (no failures)
    }

    @Test
    fun `should handle single request with single rule`() {
        // Given
        val singleRuleProcessor = AnomalyDetectionRulesProcessor(
            listOf(mockRule1),
            mockIADRequestBuilder,
            mockk(relaxed = true) // jpaAnomalyReportRepository
        )
        val request = createRequest()
        val requests = listOf(request)
        val ruleTypes = listOf(DetectionRuleType.BILLING_CURRENCY)

        // Mock IAD request builder to return requests
        every { mockIADRequestBuilder.build(mockCommand) } returns requests
        every { mockRule1.detect(mockCommand, request) } returns createSuccessResult("Single rule passed")

        // When
        val result = singleRuleProcessor.detect(mockCommand, ruleTypes)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should handle multiple requests`() {
        // Given
        val request1 = createRequest(1L)
        val request2 = createRequest(2L)
        val requests = listOf(request1, request2)
        val ruleTypes = listOf(DetectionRuleType.BILLING_CURRENCY, DetectionRuleType.REFERENCE_TEXT_CHECK, DetectionRuleType.HIGH_VALUE_INVOICE)

        // Mock IAD request builder to return requests
        every { mockIADRequestBuilder.build(mockCommand) } returns requests

        // Mock rules for both requests - all success
        every { mockRule1.detect(mockCommand, request1) } returns createSuccessResult("Rule 1 passed for request 1")
        every { mockRule2.detect(mockCommand, request1) } returns createSuccessResult("Rule 2 passed for request 1")
        every { mockRule3.detect(mockCommand, request1) } returns createSuccessResult("Rule 3 passed for request 1")

        every { mockRule1.detect(mockCommand, request2) } returns createSuccessResult("Rule 1 passed for request 2")
        every { mockRule2.detect(mockCommand, request2) } returns createWarningResult("Rule 2 warning for request 2")
        every { mockRule3.detect(mockCommand, request2) } returns createSuccessResult("Rule 3 passed for request 2")

        // When
        val result = processor.detect(mockCommand, ruleTypes)

        // Then
        assertTrue(result) // All requests should pass (WARNING is non-blocking)
    }

    @Test
    fun `should return false when multiple requests have failures`() {
        // Given
        val request1 = createRequest(1L)
        val request2 = createRequest(2L)
        val requests = listOf(request1, request2)
        val ruleTypes = listOf(DetectionRuleType.BILLING_CURRENCY, DetectionRuleType.REFERENCE_TEXT_CHECK)

        // Mock IAD request builder to return requests
        every { mockIADRequestBuilder.build(mockCommand) } returns requests

        // Mock rules - first request passes, second request fails
        every { mockRule1.detect(mockCommand, request1) } returns createSuccessResult("Rule 1 passed for request 1")
        every { mockRule2.detect(mockCommand, request1) } returns createSuccessResult("Rule 2 passed for request 1")

        every { mockRule1.detect(mockCommand, request2) } returns createSuccessResult("Rule 1 passed for request 2")
        every { mockRule2.detect(mockCommand, request2) } returns createFailureResult("Rule 2 failed for request 2")

        // When
        val result = processor.detect(mockCommand, ruleTypes)

        // Then
        assertFalse(result) // One request failed, so overall result should be false
    }

    @Test
    fun `should filter rules by rule types`() {
        // Given
        val request = createRequest()
        val requests = listOf(request)
        val ruleTypes = listOf(DetectionRuleType.BILLING_CURRENCY) // Only include one rule type

        // Mock IAD request builder to return requests
        every { mockIADRequestBuilder.build(mockCommand) } returns requests

        // Mock only the filtered rule to return success
        every { mockRule1.detect(mockCommand, request) } returns createSuccessResult("Rule 1 passed")

        // When
        val result = processor.detect(mockCommand, ruleTypes)

        // Then
        assertTrue(result)
        // Verify only the filtered rule was called
        verify(exactly = 1) { mockRule1.detect(mockCommand, request) }
        verify(exactly = 0) { mockRule2.detect(mockCommand, request) }
        verify(exactly = 0) { mockRule3.detect(mockCommand, request) }
    }

    @Test
    fun `should handle exception during rule execution`() {
        // Given
        val request = createRequest()
        val requests = listOf(request)
        val ruleTypes = listOf(DetectionRuleType.BILLING_CURRENCY, DetectionRuleType.REFERENCE_TEXT_CHECK, DetectionRuleType.HIGH_VALUE_INVOICE)

        // Mock IAD request builder to return requests
        every { mockIADRequestBuilder.build(mockCommand) } returns requests

        // Mock first rule to throw exception, others to succeed
        every { mockRule1.detect(mockCommand, request) } throws RuntimeException("Rule 1 failed with exception")
        every { mockRule2.detect(mockCommand, request) } returns createSuccessResult("Rule 2 passed")
        every { mockRule3.detect(mockCommand, request) } returns createSuccessResult("Rule 3 passed")

        // When
        val result = processor.detect(mockCommand, ruleTypes)

        // Then
        assertFalse(result) // Exception in rule should cause overall failure
    }

    @Test
    fun `should handle exception during IAD request building`() {
        // Given
        val ruleTypes = listOf(DetectionRuleType.BILLING_CURRENCY)

        // Mock IAD request builder to throw exception
        every { mockIADRequestBuilder.build(mockCommand) } throws RuntimeException("Failed to build IAD requests")

        // When
        val result = processor.detect(mockCommand, ruleTypes)

        // Then
        assertFalse(result) // Exception during request building should cause failure
    }

    @Test
    fun `should return true when no rules match the filter`() {
        // Given
        val request = createRequest()
        val requests = listOf(request)
        val ruleTypes = listOf(DetectionRuleType.NEGATIVE_AMOUNT) // Rule type not in our processor

        // Mock IAD request builder to return requests
        every { mockIADRequestBuilder.build(mockCommand) } returns requests

        // When
        val result = processor.detect(mockCommand, ruleTypes)

        // Then
        assertTrue(result) // No rules to execute means success
        // Verify no rules were called
        verify(exactly = 0) { mockRule1.detect(any(), any()) }
        verify(exactly = 0) { mockRule2.detect(any(), any()) }
        verify(exactly = 0) { mockRule3.detect(any(), any()) }
    }

    @Test
    fun `should return false when all rules fail`() {
        // Given
        val request = createRequest()
        val requests = listOf(request)
        val ruleTypes = listOf(DetectionRuleType.BILLING_CURRENCY, DetectionRuleType.REFERENCE_TEXT_CHECK, DetectionRuleType.HIGH_VALUE_INVOICE)

        // Mock IAD request builder to return requests
        every { mockIADRequestBuilder.build(mockCommand) } returns requests

        // Mock all rules to return failure
        every { mockRule1.detect(mockCommand, request) } returns createFailureResult("Rule 1 failed")
        every { mockRule2.detect(mockCommand, request) } returns createFailureResult("Rule 2 failed")
        every { mockRule3.detect(mockCommand, request) } returns createFailureResult("Rule 3 failed")

        // When
        val result = processor.detect(mockCommand, ruleTypes)

        // Then
        assertFalse(result) // All rules failed, so overall result should be false
    }

    // Helper methods
    private fun createInvoiceCommand(): InvoiceCommand {
        return mockk<InvoiceCommand>().apply {
            every { transactionId } returns "test-transaction-id"
            every { companyId } returns 123L
            every { transactionType } returns mockk()
        }
    }

    private fun createRequest(payableId: Long = 1L): InvoiceAnomalyDetectorRequest {
        val mockInvoiceDTO = mockk<InvoiceDTO>().apply {
            every { billingCurrencyCode } returns CurrencyCode.USD
            every { companyPayableId } returns payableId
        }

        val mockCompanyPayable = mockk<JpaCompanyPayable>().apply {
            every { id } returns payableId
            every { companyId } returns 123L
        }

        return InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(mockInvoiceDTO)
            .payable(mockCompanyPayable)
            .build()
    }

    private fun createSuccessResult(message: String): AnomalyDetectionResult {
        return AnomalyDetectionResult(
            success = true,
            messages = listOf(message),
            severity = AnomalyResultType.SUCCESS
        )
    }

    private fun createWarningResult(message: String): AnomalyDetectionResult {
        return AnomalyDetectionResult(
            success = true,
            messages = listOf(message),
            severity = AnomalyResultType.WARN
        )
    }

    private fun createFailureResult(message: String): AnomalyDetectionResult {
        return AnomalyDetectionResult(
            success = false,
            messages = listOf(message),
            severity = AnomalyResultType.ERROR
        )
    }
}
