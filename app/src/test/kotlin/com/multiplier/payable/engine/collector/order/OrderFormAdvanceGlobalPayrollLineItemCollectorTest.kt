package com.multiplier.payable.engine.collector.order

import com.multiplier.core.payable.adapters.BillingServiceAdapter
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.adapters.billing.BilledItemWrapper
import com.multiplier.core.payable.adapters.pricing.ReferenceChargePolicy
import com.multiplier.core.payable.adapters.product.CompanyProductWrapper
import com.multiplier.payable.engine.collector.DataKey
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.Duration
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transformer.bill.OrderFormAdvanceBillToItemStoreTransformer
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.util.getCurrentMonthDateRange
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

class OrderFormAdvanceGlobalPayrollLineItemCollectorTest {

    private lateinit var billingServiceAdapter: BillingServiceAdapter
    private lateinit var billToItemStoreTransformer: OrderFormAdvanceBillToItemStoreTransformer
    private lateinit var payableItemStoreService: PayableItemStoreService
    private lateinit var collector: OrderFormAdvanceGlobalPayrollLineItemCollector

    @BeforeEach
    fun setUp() {
        billingServiceAdapter = mock(BillingServiceAdapter::class.java)
        billToItemStoreTransformer = mock(OrderFormAdvanceBillToItemStoreTransformer::class.java)
        payableItemStoreService = mock(PayableItemStoreService::class.java)
        collector = OrderFormAdvanceGlobalPayrollLineItemCollector(
            billingServiceAdapter,
            billToItemStoreTransformer,
            payableItemStoreService
        )
    }

    @Test
    fun `getSupportedType should return ORDER_FORM_ADVANCE_GLOBAL_PAYROLL`() {
        // When
        val result = collector.getSupportedType()

        // Then
        assertEquals(LineItemType.ORDER_FORM_ADVANCE_GLOBAL_PAYROLL, result)
    }


    @Test
    fun `handle should filter bills by GLOBAL_PAYROLL offering type`() {
        // Given
        val companyId = 123L
        val entityId = 456L
        val now = LocalDateTime.now()
        val dateRange = DateRange(now, now.plusMonths(1))
        val command = InvoiceCommand(
            transactionId = "test-transaction-id",
            companyId = companyId,
            entityId = entityId,
            transactionType = TransactionType.ORDER_FORM_ADVANCE,
            transactionDate = now,
            cycle = InvoiceCycle.MONTHLY,
            dateRange = dateRange
        )

        val eorBill = createBilledItemWrapper(1L, "EOR")
        val gpBill = createBilledItemWrapper(2L, "GLOBAL_PAYROLL")
        val aorBill = createBilledItemWrapper(3L, "AOR")

        val bills = listOf(eorBill, gpBill, aorBill)

        `when`(billingServiceAdapter.getOrCreateOrderFormAdvanceBills(companyId, entityId, command.dateRange))
            .thenReturn(bills)

        val payableItemStoreDto = mock(PayableItemStoreDto::class.java)
        `when`(billToItemStoreTransformer.transform(eq(listOf(gpBill)), eq(LocalDate.now().getCurrentMonthDateRange()), any())).thenReturn(listOf(payableItemStoreDto))

        val dataKey = mock<DataKey>()
        `when`(payableItemStoreDto.versionedItemKey).thenReturn(dataKey)

        // When
        collector.handle(command)

        // Then
        verify(billingServiceAdapter).getOrCreateOrderFormAdvanceBills(companyId, entityId, command.dateRange)
        verify(payableItemStoreService).saveAndIgnoreDuplication(listOf(payableItemStoreDto))
    }

    private fun createBilledItemWrapper(billId: Long, offeringType: String): BilledItemWrapper {
        val now = LocalDateTime.now()
        val endDate = now.plusMonths(1)

        return BilledItemWrapper(
            billId = billId,
            transactionId = "test-transaction-id",
            companyId = 123L,
            entityId = 456L,
            companyProduct = CompanyProductWrapper(
                startDate = now,
                endDate = endDate,
                lineCode = "ORDER_FORM_ADVANCE",
                dimensions = mapOf("OFFERING" to offeringType),
                chargePolicy = ReferenceChargePolicy(emptyList())
            ),
            billingAmount = Amount(BigDecimal("100.00"), CurrencyCode.USD),
            billingDuration = Duration(
                startDate = LocalDate.now(),
                endDate = LocalDate.now().plusMonths(1)
            ),
            usageDuration = Duration(
                startDate = LocalDate.now(),
                endDate = LocalDate.now().plusMonths(1)
            ),
            billingTime = System.currentTimeMillis(),
            referenceBills = emptyList(),
            usages = emptyList()
        )
    }
}
