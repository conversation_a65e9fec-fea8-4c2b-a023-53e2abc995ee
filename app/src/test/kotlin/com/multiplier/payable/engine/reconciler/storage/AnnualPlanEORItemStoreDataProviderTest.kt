package com.multiplier.payable.engine.reconciler.storage

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.AnnualPayableItemMapper
import com.multiplier.payable.engine.payableitem.JpaPayableItemStore
import com.multiplier.payable.engine.payableitem.JpaPayableItemStoreRepository
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.payableitem.PayableItemKey
import com.multiplier.payable.engine.reconciler.data.item.AnnualPlanEORItemStoreDataProvider
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*
import kotlin.random.Random
import kotlin.test.assertEquals

@ExtendWith(MockitoExtension::class)
class AnnualPlanEORItemStoreDataProviderTest {

    @Mock
    private lateinit var itemStoreRepository: JpaPayableItemStoreRepository

    @Mock
    private lateinit var annualPayableItemMapper: AnnualPayableItemMapper

    @InjectMocks
    private lateinit var itemStoreDataProvider: AnnualPlanEORItemStoreDataProvider

    @Test
    fun `given no line item types when fetching latest then return empty`() {
        // given
        val lineItemTypes = emptyList<LineItemType>()
        val command = mock<InvoiceCommand>()

        // when
        val latestBatch = itemStoreDataProvider.fetchLatest(command, lineItemTypes)

        // then
        assertThat(latestBatch).isEmpty()
    }

    @Test
    fun `given multiple versions exist when fetching latest then latest batch will be returned`() {
        // given
        val companyId = 123L
        val fromTime = LocalDateTime.now()
        val toTime = LocalDateTime.now()
        val range = DateRange(fromTime, toTime)
        val transactionType = TransactionType.ANNUAL_PLAN_INVOICE
        val lineItemTypes = mutableListOf(LineItemType.ANNUAL_MANAGEMENT_FEE_EOR)
        val command =
            InvoiceCommand(
                transactionId = UUID.randomUUID().toString(),
                companyId = companyId,
                dateRange = range,
                transactionType = transactionType,
                cycle = InvoiceCycle.YEARLY,
                transactionDate = LocalDateTime.now(),
            )

        val batchKey =
            PayableItemKey(
                month = 1,
                year = 2024,
                itemType = LineItemType.ANNUAL_MANAGEMENT_FEE_EOR.name,
                companyId = companyId,
            )
        val batchSize = 3
        val batches = generateItems(1, batchSize, batchKey)
        val rangeStartDate = range.startDate.toLocalDate()
        val rangeEndDate = range.endDate.toLocalDate()
        Mockito.`when`(
            itemStoreRepository.findByTransactionIdAndItemTypeIn(
                eq(command.transactionId),
                eq(lineItemTypes),
            ),
        ).thenReturn(batches)
        val payableItems = batches.map { map(it) }

        Mockito.`when`(
            annualPayableItemMapper.mapList(
                items = batches,
                cycle = InvoiceCycle.YEARLY,
            ),
        ).thenReturn(payableItems)

        // when
        val latestBatch = itemStoreDataProvider.fetchLatest(command, lineItemTypes)

        // then
        assertEquals(batchSize, latestBatch.size)
    }

    private fun generateItems(
        numBatch: Int,
        batchSize: Int,
        key: PayableItemKey,
    ): List<JpaPayableItemStore> {
        val results = mutableListOf<JpaPayableItemStore>()
        for (i in 0..<numBatch) {
            results.addAll(generateBatchItems(i, batchSize, key))
            Thread.sleep(1000)
        }

        return results
    }

    private fun generateBatchItems(
        index: Int,
        size: Int,
        key: PayableItemKey,
    ): List<JpaPayableItemStore> {
        val results = mutableListOf<JpaPayableItemStore>()
        val oTime = Timestamp(System.currentTimeMillis())
        for (i in 0..<size) {
            results.add(
                JpaPayableItemStore.builder()
                    .amount(Random.nextDouble())
                    .itemData("")
                    .month(key.month)
                    .year(key.year)
                    .itemType(LineItemType.valueOf(key.itemType))
                    .currency(CurrencyCode.CUC)
                    .companyId(key.companyId)
                    .contractId(1000L + i)
                    .versionId(index.toString())
                    .originalTimestamp(oTime)
                    .countryCode(CountryCode.USA)
                    .build(),
            )
        }

        return results
    }

    private fun map(jpa: JpaPayableItemStore): PayableItem {
        return PayableItem(
            companyId = jpa.companyId,
            month = jpa.month,
            year = jpa.year,
            lineItemType = jpa.itemType.name,
            contractId = jpa.contractId,
            description = "random description",
            amountInBaseCurrency = jpa.amount,
            baseCurrency = jpa.currency.name,
            versionId = jpa.versionId,
            originalTimestamp = jpa.originalTimestamp.time,
            cycle = InvoiceCycle.YEARLY,
            countryCode = jpa.countryCode.name,
            itemCount = 1,
            periodStartDate = jpa.periodStartDate,
            periodEndDate = jpa.periodEndDate,
        )
    }
}
