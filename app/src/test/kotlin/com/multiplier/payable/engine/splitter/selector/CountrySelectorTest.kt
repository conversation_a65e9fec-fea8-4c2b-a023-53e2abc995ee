package com.multiplier.payable.engine.splitter.selector

import com.multiplier.core.payable.adapters.CountryServiceAdapter
import com.multiplier.country.schema.Country
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.splitter.context.CountryNameEnricher
import com.multiplier.payable.engine.splitter.context.SplitterContext
import com.multiplier.payable.engine.testutils.CompanyPayableTestDataFactory
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import java.util.*
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CountrySelectorTest {
    private val countryServiceAdapter = mock<CountryServiceAdapter>()
    private val countryNameEnricher = CountryNameEnricher(countryServiceAdapter)
    private val countrySelector = CountrySelector(countryNameEnricher)

    companion object {
        @JvmStatic
        fun providePayableItems(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(CompanyPayableTestDataFactory.createPayableItem(countryCode = "VNM"), "Vietnam"),
                Arguments.of(
                    CompanyPayableTestDataFactory.createPayableItem(countryCode = "USA"),
                    "United States"
                ),
            )
        }
    }

    @ParameterizedTest
    @MethodSource("providePayableItems")
    fun givenValidPayableItem_whenSelectAndTranslate_thenReturnCorrectly(
        payableItem: PayableItem,
        expectedCountry: String?
    ) {
        // GIVEN
        val context = SplitterContext(
            transactionId = UUID.randomUUID().toString(),
            payableItems = listOf(payableItem),
        )
        `when`(
            countryServiceAdapter.getCountryNamesByCodes(
                setOf(Country.GrpcCountryCode.valueOf(payableItem.countryCode!!))
            )
        ).thenReturn(mapOf(payableItem.countryCode!! to expectedCountry!!))
        val enrichedContext = countrySelector.contextEnricher.enrich(context)

        // WHEN
        val result = countrySelector.selectAndTranslate()(payableItem, enrichedContext)

        // THEN
        assertEquals(expectedCountry, result)
    }
}