package com.multiplier.payable.engine.reconciler.order

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock

@ExtendWith(MockitoExtension::class)
class OrderFormAdvanceCompanyPayableValidatorTest {

    @Mock
    private lateinit var templateProvider: TransactionTemplateProvider

    @InjectMocks
    private lateinit var validator: OrderFormAdvanceCompanyPayableValidator

    @Test
    fun `test validate items empty`() {

        val command = mock<InvoiceCommand>()
        val payable = mock<CompanyPayable>()
        val companyPayables = listOf(payable)

        assertThrows<MplBusinessException> {
            validator.validate(command, companyPayables)
        }
    }

    @Test
    fun `test validate not allowed types`() {

        val command = mock<InvoiceCommand> {
            on { companyId } doReturn 1
            on { transactionType } doReturn TransactionType.ORDER_FORM_ADVANCE
        }
        val item = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.ORDER_FORM_ADVANCE_EOR.name
        }
        val payable = mock<CompanyPayable> {
            on { items } doReturn listOf(item)
        }
        val companyPayables = listOf(payable, payable)
        doReturn(mock<TransactionTemplate>()).`when`(templateProvider).findTemplateFor(
            transactionType = TransactionType.ORDER_FORM_ADVANCE,
            companyId = 1,
        )

        assertThrows<MplBusinessException> {
            validator.validate(command, companyPayables)
        }
    }

    @Test
    fun `test validate not having billId`() {

        val command = mock<InvoiceCommand> {
            on { companyId } doReturn 1
            on { transactionType } doReturn TransactionType.ORDER_FORM_ADVANCE
        }
        val item = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.ORDER_FORM_ADVANCE_EOR.name
        }
        val payable = mock<CompanyPayable> {
            on { items } doReturn listOf(item)
        }
        val companyPayables = listOf(payable, payable)
        val transactionTemplate = mock<TransactionTemplate> {
            on { lineItemTypes } doReturn listOf(LineItemType.ORDER_FORM_ADVANCE_EOR)
        }
        doReturn(transactionTemplate).`when`(templateProvider).findTemplateFor(
            transactionType = TransactionType.ORDER_FORM_ADVANCE,
            companyId = 1,
        )

        assertThrows<MplBusinessException> {
            validator.validate(command, companyPayables)
        }
    }

    @Test
    fun `test validate legit payable`() {

        val command = mock<InvoiceCommand> {
            on { companyId } doReturn 1
            on { transactionType } doReturn TransactionType.ORDER_FORM_ADVANCE
        }
        val item = mock<PayableItem> {
            on { billId } doReturn "billId1"
            on { lineItemType } doReturn LineItemType.ORDER_FORM_ADVANCE_EOR.name
        }
        val payable = mock<CompanyPayable> {
            on { items } doReturn listOf(item)
        }
        val companyPayables = listOf(payable, payable)
        val transactionTemplate = mock<TransactionTemplate> {
            on { lineItemTypes } doReturn listOf(LineItemType.ORDER_FORM_ADVANCE_EOR)
        }
        doReturn(transactionTemplate).`when`(templateProvider).findTemplateFor(
            transactionType = TransactionType.ORDER_FORM_ADVANCE,
            companyId = 1,
        )

        assertDoesNotThrow {
            validator.validate(command, companyPayables)
        }
    }
}
