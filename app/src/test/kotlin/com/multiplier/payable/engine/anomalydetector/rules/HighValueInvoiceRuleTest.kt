package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.fetcher.IADCompanyPayableFetcher
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.core.currency.CurrencyServiceV2
import com.multiplier.core.currency.data.Amount
import com.multiplier.core.currency.data.ConversionInstruction
import com.multiplier.core.currency.data.CurrencyConversionDetails
import com.multiplier.core.currency.data.CurrencyConversionResponse
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaInvoice
import com.multiplier.core.payable.repository.model.JpaInvoiceLineItem
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.types.PayableStatus
import java.math.BigDecimal
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class HighValueInvoiceRuleTest {

    @Mock
    private lateinit var featureFlagService: FeatureFlagService

    @Mock
    private lateinit var jpaAnomalyReportRepository: JpaAnomalyReportRepository

    @Mock
    private lateinit var iadCompanyPayableFetcher: IADCompanyPayableFetcher

    @Mock
    private lateinit var currencyServiceV2: CurrencyServiceV2

    @Mock
    private lateinit var mockCommand: InvoiceCommand

    private lateinit var highValueInvoiceRule: HighValueInvoiceRule

    @BeforeEach
    fun setUp() {
        highValueInvoiceRule = HighValueInvoiceRule(iadCompanyPayableFetcher, currencyServiceV2, featureFlagService)
    }

    @Test
    fun `should return success when feature flag is disabled`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(false)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        // Use a simple empty request
        val request = InvoiceAnomalyDetectorRequest.builder().build()

        // When
        val result = highValueInvoiceRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Rule 'HighValueInvoice' is disabled by feature flag"))
    }

    @Test
    fun `should return success when request is invalid`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = InvoiceAnomalyDetectorRequest.builder().build() // Empty request

        // When
        val result = highValueInvoiceRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice data or company payable is null - validation skipped"))
    }

    @Test
    fun `should return success when invoice amount is below absolute threshold with insufficient historical data`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = createMockRequestWithEmployees(50000.0, CurrencyCode.USD, 5, 123L, 3, 2024)

        // Mock insufficient historical data (less than 3 months)
        Mockito.`when`(iadCompanyPayableFetcher.getInvoicesForReqTime(Mockito.any(), Mockito.eq(false)))
            .thenReturn(emptyList()) // No historical data

        // When
        val result = highValueInvoiceRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("All invoice amounts are within acceptable absolute thresholds"))
    }

    @Test
    fun `should return warning when invoice amount exceeds absolute threshold with insufficient historical data`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = createMockRequestWithEmployees(150000.0, CurrencyCode.USD, 5, 123L, 3, 2024)

        // Mock insufficient historical data (less than 3 months)
        Mockito.`when`(iadCompanyPayableFetcher.getInvoicesForReqTime(Mockito.any(), Mockito.eq(false)))
            .thenReturn(emptyList()) // No historical data

        // When
        val result = highValueInvoiceRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success) // ERROR for validation failures
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("exceeds absolute threshold"))
        assertTrue(result.messages[0].contains("150000.00"))
        assertTrue(result.messages[0].contains("100000.00"))
        assertTrue(result.messages[0].contains("USD"))
    }

    @Test
    fun `should return correct rule type`() {
        // When
        val ruleType = highValueInvoiceRule.type

        // Then
        assertEquals(DetectionRuleType.HIGH_VALUE_INVOICE, ruleType)
    }

    @Test
    fun `should return correct rule name`() {
        // When
        val ruleName = highValueInvoiceRule.ruleName

        // Then
        assertEquals("HighValueInvoice", ruleName)
    }

    @Test
    fun `should return correct feature flag name`() {
        // When
        val featureFlagName = highValueInvoiceRule.featureFlagName

        // Then
        assertEquals("ENABLE_HIGH_VALUE_INVOICE_CHECK", featureFlagName)
    }

    @Test
    fun `should return success when per-employee amount is below historical threshold`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        // Current: 50000 for 5 employees = 10000 per employee
        val request = createMockRequestWithEmployees(50000.0, CurrencyCode.USD, 5, 123L, 3, 2024)

        // Historical: max 8000 per employee, threshold = 8000 * 1.5 = 12000
        // Current 10000 < threshold 12000 -> should pass
        val historicalPayables = listOf(
            createHistoricalPayable(40000.0, 5), // 8000 per employee
            createHistoricalPayable(35000.0, 5), // 7000 per employee
            createHistoricalPayable(30000.0, 4)  // 7500 per employee
        )
        mockHistoricalData(request.payable!!, historicalPayables)

        // When
        val result = highValueInvoiceRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("All invoice amounts are within acceptable per-employee thresholds"))
    }

    @Test
    fun `should return warning when per-employee amount exceeds historical threshold`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        // Current: 100000 for 5 employees = 20000 per employee
        val request = createMockRequestWithEmployees(100000.0, CurrencyCode.USD, 5, 123L, 3, 2024)

        // Historical: max 8000 per employee, threshold = 8000 * 1.5 = 12000
        // Current 20000 > threshold 12000 -> should trigger warning
        val historicalPayables = listOf(
            createHistoricalPayable(40000.0, 5), // 8000 per employee
            createHistoricalPayable(35000.0, 5), // 7000 per employee
            createHistoricalPayable(30000.0, 4)  // 7500 per employee
        )
        mockHistoricalData(request.payable!!, historicalPayables)

        // When
        val result = highValueInvoiceRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success) // ERROR for validation failures
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Per-employee amount"))
        assertTrue(result.messages[0].contains("20000.00"))
        assertTrue(result.messages[0].contains("12000.00"))
        assertTrue(result.messages[0].contains("150.0%"))
    }

    @Test
    fun `should use absolute threshold when no employees detected`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        // Invoice with no employee line items (0 employees)
        val request = createMockRequestWithEmployees(150000.0, CurrencyCode.USD, 0, 123L, 3, 2024)

        // Mock sufficient historical data
        val historicalPayables = listOf(
            createHistoricalPayable(40000.0, 5),
            createHistoricalPayable(35000.0, 5),
            createHistoricalPayable(30000.0, 4)
        )
        mockHistoricalData(request.payable!!, historicalPayables)

        // When
        val result = highValueInvoiceRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success) // ERROR for validation failures
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("exceeds absolute threshold"))
        assertTrue(result.messages[0].contains("150000.00"))
        assertTrue(result.messages[0].contains("100000.00"))
        assertTrue(result.messages[0].contains("USD"))
    }

    @Test
    fun `should filter out inactive payable statuses from historical data`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = createMockRequestWithEmployees(50000.0, CurrencyCode.USD, 5, 123L, 3, 2024)

        // Mix of valid and invalid statuses
        val authorizedPayable = createHistoricalPayable(40000.0, 5, PayableStatus.AUTHORIZED)
        val paidPayable = createHistoricalPayable(35000.0, 5, PayableStatus.PAID)
        val draftPayable = createHistoricalPayable(30000.0, 4, PayableStatus.DRAFT) // Should be filtered out
        val voidedPayable = createHistoricalPayable(25000.0, 3, PayableStatus.VOIDED) // Should be filtered out

        // Mock fetcher to return all statuses, but rule should filter to only valid ones
        Mockito.`when`(iadCompanyPayableFetcher.getInvoicesForReqTime(Mockito.any(), Mockito.eq(false)))
            .thenReturn(listOf(authorizedPayable, paidPayable, draftPayable, voidedPayable))

        // When
        val result = highValueInvoiceRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("All invoice amounts are within acceptable per-employee thresholds"))
        // Should only consider AUTHORIZED and PAID payables for threshold calculation
        // Max per-employee from valid payables: max(8000, 7000) = 8000, threshold = 12000
        // Current: 10000 < 12000 -> should pass
    }



    private fun createMockRequestWithEmployees(
        amount: Double,
        currency: CurrencyCode,
        employeeCount: Int,
        companyId: Long,
        month: Int,
        year: Int
    ): InvoiceAnomalyDetectorRequest {
        val mockCompanyPayable = Mockito.mock(JpaCompanyPayable::class.java)
        Mockito.`when`(mockCompanyPayable.totalAmount).thenReturn(amount)
        Mockito.`when`(mockCompanyPayable.currency).thenReturn(currency)
        Mockito.`when`(mockCompanyPayable.companyId).thenReturn(companyId)
        Mockito.`when`(mockCompanyPayable.month).thenReturn(month)
        Mockito.`when`(mockCompanyPayable.year).thenReturn(year)

        // Create line items with unique contract IDs for employee count
        val lineItems = (1..employeeCount).map { contractId ->
            val lineItem = Mockito.mock(LineItemDTO::class.java)
            Mockito.`when`(lineItem.itemType).thenReturn(LineItemType.GROSS_SALARY)
            Mockito.`when`(lineItem.contractId).thenReturn(contractId.toLong())
            lineItem
        }

        val mockInvoiceDTO = Mockito.mock(InvoiceDTO::class.java)
        Mockito.`when`(mockInvoiceDTO.totalAmount).thenReturn(amount)
        Mockito.`when`(mockInvoiceDTO.lineItems).thenReturn(lineItems)

        return InvoiceAnomalyDetectorRequest.builder()
            .payable(mockCompanyPayable)
            .invoiceDTO(mockInvoiceDTO)
            .build()
    }

    private fun createHistoricalPayable(
        amount: Double,
        employeeCount: Int,
        status: PayableStatus = PayableStatus.AUTHORIZED
    ): JpaCompanyPayable {
        val mockPayable = Mockito.mock(JpaCompanyPayable::class.java)
        Mockito.`when`(mockPayable.totalAmount).thenReturn(amount)
        Mockito.`when`(mockPayable.status).thenReturn(status)

        // Create mock invoice with line items
        val mockInvoice = Mockito.mock(JpaInvoice::class.java)
        val lineItems = (1..employeeCount).map { contractId ->
            val lineItem = Mockito.mock(JpaInvoiceLineItem::class.java)
            Mockito.`when`(lineItem.itemType).thenReturn(LineItemType.GROSS_SALARY)
            Mockito.`when`(lineItem.contractId).thenReturn(contractId.toLong())
            lineItem
        }
        Mockito.`when`(mockInvoice.lineItems).thenReturn(lineItems)
        Mockito.`when`(mockPayable.invoice).thenReturn(mockInvoice)

        return mockPayable
    }

    private fun mockHistoricalData(companyPayable: JpaCompanyPayable, historicalPayables: List<JpaCompanyPayable>) {
        Mockito.`when`(iadCompanyPayableFetcher.getInvoicesForReqTime(Mockito.any(), Mockito.eq(false)))
            .thenReturn(historicalPayables)
    }


}
