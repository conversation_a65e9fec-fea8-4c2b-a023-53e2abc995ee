package com.multiplier.payable.engine.collector.gp.serviceInvoice

import com.multiplier.billing.grpc.billing.Billing.BilledItem
import com.multiplier.metering.grpc.schema.Metering.Metric
import com.multiplier.metering.grpc.schema.Metering.MetricLabel
import com.multiplier.common.exception.MplBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.junit.jupiter.params.provider.CsvSource
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class BilledItemCompanyEntityProviderTest {

    @Mock
    private lateinit var billedItem: BilledItem

    @Mock
    private lateinit var command: InvoiceCommand

    @InjectMocks
    private lateinit var provider: BilledItemCompanyEntityProvider

    private val companyId = 123L
    private val transactionId = "test-transaction-123"
    private val billId = 456L
    private val billedItemEntityId = 0L

    @BeforeEach
    fun setUp() {
        whenever(command.companyId).thenReturn(companyId)
        whenever(command.transactionId).thenReturn(transactionId)
        whenever(billedItem.id).thenReturn(billId)
        whenever(billedItem.entityId).thenReturn(billedItemEntityId)
    }

    @Test
    fun `should return billed item entity when both command and meter entity IDs are null`() {
        // Given
        whenever(command.entityId).thenReturn(null)
        whenever(billedItem.usageList).thenReturn(emptyList())

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(billedItemEntityId)
    }

    @Test
    fun `should return billed item entity when command has entity ID but meter has none`() {
        // Given
        val commandEntityId = 789L
        whenever(command.entityId).thenReturn(commandEntityId)
        whenever(billedItem.usageList).thenReturn(emptyList())

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(billedItemEntityId)
    }

    @Test
    fun `should return meter entity when command has no entity ID but meter has one`() {
        // Given
        val meterEntityId = 555L
        whenever(command.entityId).thenReturn(null)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(meterEntityId.toString()))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(meterEntityId)
    }

    @Test
    fun `should throw exception when command and meter entity IDs don't match`() {
        // Given
        val commandEntityId = 789L
        val meterEntityId = 555L
        whenever(command.entityId).thenReturn(commandEntityId)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(meterEntityId.toString()))

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            provider.getCompanyEntityId(billedItem, command)
        }

        assertThat(exception.errorCode).isEqualTo(PayableErrorCode.COMPANY_ENTITY_ID_MISMATCH)
        assertThat(exception.message).contains("Entity ID mismatch")
        assertThat(exception.message).contains("command entity ID ($commandEntityId)")
        assertThat(exception.message).contains("meter entity ID ($meterEntityId)")
        assertThat(exception.message).contains("bill $billId")
    }

    @Test
    fun `should return meter entity when command and meter entity IDs match`() {
        // Given
        val entityId = 789L
        whenever(command.entityId).thenReturn(entityId)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(entityId.toString()))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(entityId)
    }

    @Test
    fun `should return entity ID when multiple usages have same entity ID`() {
        // Given
        val entityId = 789L
        whenever(command.entityId).thenReturn(null)
        
        val usageList = listOf(
            createMetricWithEntityId(entityId.toString()),
            createMetricWithEntityId(entityId.toString()),
            createMetricWithEntityId(entityId.toString())
        )
        whenever(billedItem.usageList).thenReturn(usageList)

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(entityId)
    }

    @Test
    fun `should throw exception when multiple usages have different entity IDs`() {
        // Given
        whenever(command.entityId).thenReturn(null)
        
        val usageList = listOf(
            createMetricWithEntityId("111"),
            createMetricWithEntityId("222"),
            createMetricWithEntityId("333")
        )
        whenever(billedItem.usageList).thenReturn(usageList)

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            provider.getCompanyEntityId(billedItem, command)
        }

        assertThat(exception.errorCode).isEqualTo(PayableErrorCode.MULTIPLE_ENTITIES_FOUND_IN_METER)
        assertThat(exception.message).contains("Multiple entity IDs found in metering data")
        assertThat(exception.message).contains("bill $billId")
        assertThat(exception.message).contains("[111, 222, 333]")
    }

    @Test
    fun `should handle usage with no entity_id labels`() {
        // Given
        whenever(command.entityId).thenReturn(null)

        val usageWithoutEntityId = Metric.newBuilder()
            .addLabels(MetricLabel.newBuilder().setKey("other_key").setValue("other_value").build())
            .build()

        whenever(billedItem.usageList).thenReturn(listOf(usageWithoutEntityId))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(billedItemEntityId)
    }

    @Test
    fun `should handle usage with null entity_id value`() {
        // Given
        whenever(command.entityId).thenReturn(null)

        val usageWithNullEntityId = Metric.newBuilder()
            .addLabels(MetricLabel.newBuilder().setKey("entity_id").build()) // No value set
            .build()

        whenever(billedItem.usageList).thenReturn(listOf(usageWithNullEntityId))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(billedItemEntityId)
    }

    @Test
    fun `should handle mixed usage list with some having entity_id and some not`() {
        // Given
        val entityId = 789L
        whenever(command.entityId).thenReturn(null)
        
        val usageList = listOf(
            createMetricWithEntityId(entityId.toString()),
            Metric.newBuilder()
                .addLabels(MetricLabel.newBuilder().setKey("other_key").setValue("other_value").build())
                .build(),
            createMetricWithEntityId(entityId.toString())
        )
        whenever(billedItem.usageList).thenReturn(usageList)

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(entityId)
    }

    @Test
    fun `should throw NumberFormatException when entity_id is not a valid number`() {
        // Given
        whenever(command.entityId).thenReturn(null)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId("invalid-number"))

        // When & Then
        assertThrows<NumberFormatException> {
            provider.getCompanyEntityId(billedItem, command)
        }
    }

    private fun createMetricListWithEntityId(entityId: String): List<Metric> {
        return listOf(createMetricWithEntityId(entityId))
    }

    private fun createMetricWithEntityId(entityId: String): Metric {
        return Metric.newBuilder()
            .addLabels(
                MetricLabel.newBuilder()
                    .setKey("entity_id")
                    .setValue(entityId)
                    .build()
            )
            .build()
    }

    @Test
    fun `should handle empty string entity_id value`() {
        // Given
        whenever(command.entityId).thenReturn(null)

        val usageWithEmptyEntityId = Metric.newBuilder()
            .addLabels(MetricLabel.newBuilder().setKey("entity_id").setValue("").build())
            .build()

        whenever(billedItem.usageList).thenReturn(listOf(usageWithEmptyEntityId))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then - Should return billed item entity since empty string is treated as no entity_id
        assertThat(result).isEqualTo(billedItemEntityId)
    }

    @Test
    fun `should handle usage with multiple labels including entity_id`() {
        // Given
        val entityId = 789L
        whenever(command.entityId).thenReturn(null)

        val usageWithMultipleLabels = Metric.newBuilder()
            .addLabels(MetricLabel.newBuilder().setKey("other_key1").setValue("value1").build())
            .addLabels(MetricLabel.newBuilder().setKey("entity_id").setValue(entityId.toString()).build())
            .addLabels(MetricLabel.newBuilder().setKey("other_key2").setValue("value2").build())
            .build()

        whenever(billedItem.usageList).thenReturn(listOf(usageWithMultipleLabels))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(entityId)
    }

    @Test
    fun `should handle usage with duplicate entity_id labels having same value`() {
        // Given
        val entityId = 789L
        whenever(command.entityId).thenReturn(null)

        val usageWithDuplicateLabels = Metric.newBuilder()
            .addLabels(MetricLabel.newBuilder().setKey("entity_id").setValue(entityId.toString()).build())
            .addLabels(MetricLabel.newBuilder().setKey("entity_id").setValue(entityId.toString()).build())
            .build()

        whenever(billedItem.usageList).thenReturn(listOf(usageWithDuplicateLabels))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(entityId)
    }

    @Test
    fun `should throw exception when usage has duplicate entity_id labels with different values`() {
        // Given
        whenever(command.entityId).thenReturn(null)

        val usageWithConflictingLabels = Metric.newBuilder()
            .addLabels(MetricLabel.newBuilder().setKey("entity_id").setValue("111").build())
            .addLabels(MetricLabel.newBuilder().setKey("entity_id").setValue("222").build())
            .build()

        whenever(billedItem.usageList).thenReturn(listOf(usageWithConflictingLabels))

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            provider.getCompanyEntityId(billedItem, command)
        }

        assertThat(exception.errorCode).isEqualTo(PayableErrorCode.MULTIPLE_ENTITIES_FOUND_IN_METER)
        assertThat(exception.message).contains("Multiple entity IDs found in metering data")
        assertThat(exception.message).contains("bill $billId")
    }

    @Test
    fun `should handle large entity ID values`() {
        // Given
        val largeEntityId = Long.MAX_VALUE
        whenever(command.entityId).thenReturn(null)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(largeEntityId.toString()))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(largeEntityId)
    }

    @Test
    fun `should handle negative entity ID values`() {
        // Given
        val negativeEntityId = -123L
        whenever(command.entityId).thenReturn(null)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(negativeEntityId.toString()))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(negativeEntityId)
    }

    @Test
    fun `should handle zero entity ID value`() {
        // Given
        val zeroEntityId = 0L
        whenever(command.entityId).thenReturn(null)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(zeroEntityId.toString()))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(zeroEntityId)
    }

    @Test
    fun `should match command and meter entity IDs when both are zero`() {
        // Given
        val zeroEntityId = 0L
        whenever(command.entityId).thenReturn(zeroEntityId)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(zeroEntityId.toString()))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(zeroEntityId)
    }

    @Test
    fun `should match command and meter entity IDs when both are negative`() {
        // Given
        val negativeEntityId = -456L
        whenever(command.entityId).thenReturn(negativeEntityId)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(negativeEntityId.toString()))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(negativeEntityId)
    }

    @Test
    fun `should handle case sensitive entity_id key`() {
        // Given
        val entityId = 789L
        whenever(command.entityId).thenReturn(null)

        // Test with different case - should not match
        val usageWithWrongCase = Metric.newBuilder()
            .addLabels(MetricLabel.newBuilder().setKey("ENTITY_ID").setValue(entityId.toString()).build())
            .addLabels(MetricLabel.newBuilder().setKey("Entity_Id").setValue(entityId.toString()).build())
            .build()

        whenever(billedItem.usageList).thenReturn(listOf(usageWithWrongCase))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then - Should return billed item entity since "entity_id" key is case sensitive
        assertThat(result).isEqualTo(billedItemEntityId)
    }

    @Test
    fun `should handle very large usage list with same entity ID`() {
        // Given
        val entityId = 789L
        whenever(command.entityId).thenReturn(null)

        // Create a large list of usages with the same entity ID
        val largeUsageList = (1..1000).map { createMetricWithEntityId(entityId.toString()) }
        whenever(billedItem.usageList).thenReturn(largeUsageList)

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(entityId)
    }

    @Test
    fun `should handle usage list with mixed entity IDs and non-entity labels`() {
        // Given
        whenever(command.entityId).thenReturn(null)

        val mixedUsageList = listOf(
            Metric.newBuilder()
                .addLabels(MetricLabel.newBuilder().setKey("entity_id").setValue("111").build())
                .addLabels(MetricLabel.newBuilder().setKey("other_key").setValue("other_value").build())
                .build(),
            Metric.newBuilder()
                .addLabels(MetricLabel.newBuilder().setKey("different_key").setValue("different_value").build())
                .build(),
            Metric.newBuilder()
                .addLabels(MetricLabel.newBuilder().setKey("entity_id").setValue("222").build())
                .build()
        )
        whenever(billedItem.usageList).thenReturn(mixedUsageList)

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            provider.getCompanyEntityId(billedItem, command)
        }

        assertThat(exception.errorCode).isEqualTo(PayableErrorCode.MULTIPLE_ENTITIES_FOUND_IN_METER)
        assertThat(exception.message).contains("[111, 222]")
    }

    @Test
    fun `should handle whitespace in entity_id values`() {
        // Given
        whenever(command.entityId).thenReturn(null)

        val usageWithWhitespace = Metric.newBuilder()
            .addLabels(MetricLabel.newBuilder().setKey("entity_id").setValue("  123  ").build())
            .build()

        whenever(billedItem.usageList).thenReturn(listOf(usageWithWhitespace))

        // When & Then - Should fail because "  123  " is not a valid Long
        assertThrows<NumberFormatException> {
            provider.getCompanyEntityId(billedItem, command)
        }
    }

    @Test
    fun `should handle decimal entity_id values`() {
        // Given
        whenever(command.entityId).thenReturn(null)

        val usageWithDecimal = Metric.newBuilder()
            .addLabels(MetricLabel.newBuilder().setKey("entity_id").setValue("123.45").build())
            .build()

        whenever(billedItem.usageList).thenReturn(listOf(usageWithDecimal))

        // When & Then - Should fail because "123.45" is not a valid Long
        assertThrows<NumberFormatException> {
            provider.getCompanyEntityId(billedItem, command)
        }
    }

    @Test
    fun `should handle scientific notation entity_id values`() {
        // Given
        whenever(command.entityId).thenReturn(null)

        val usageWithScientific = Metric.newBuilder()
            .addLabels(MetricLabel.newBuilder().setKey("entity_id").setValue("1e3").build())
            .build()

        whenever(billedItem.usageList).thenReturn(listOf(usageWithScientific))

        // When & Then - Should fail because "1e3" is not a valid Long format
        assertThrows<NumberFormatException> {
            provider.getCompanyEntityId(billedItem, command)
        }
    }

    @Test
    fun `should verify logging behavior for billed item entity fallback`() {
        // Given
        whenever(command.entityId).thenReturn(null)
        whenever(billedItem.usageList).thenReturn(emptyList())

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(billedItemEntityId)
        // Note: In a real test environment, you might want to verify log messages
        // using a logging framework test utility like Logback's ListAppender
    }

    @Test
    fun `should verify logging behavior for entity found in meter`() {
        // Given
        val entityId = 789L
        whenever(command.entityId).thenReturn(null)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(entityId.toString()))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(entityId)
        // Note: In a real test environment, you might want to verify log messages
    }

    @Test
    fun `should handle null usage list`() {
        // Given
        whenever(command.entityId).thenReturn(null)
        whenever(billedItem.usageList).thenReturn(null)

        // When & Then - This would likely cause a NullPointerException
        // depending on the implementation, but let's test the current behavior
        assertThrows<NullPointerException> {
            provider.getCompanyEntityId(billedItem, command)
        }
    }

    @Test
    fun `should handle usage with null labels list`() {
        // Given
        whenever(command.entityId).thenReturn(null)

        val usageWithNullLabels = Metric.newBuilder().build() // No labels added
        whenever(billedItem.usageList).thenReturn(listOf(usageWithNullLabels))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then - Should return billed item entity since no entity_id labels found
        assertThat(result).isEqualTo(billedItemEntityId)
    }

    @ParameterizedTest
    @ValueSource(strings = ["abc", "123abc", "abc123", "12.34", "1e5"])
    fun `should throw NumberFormatException for invalid entity ID formats`(invalidEntityId: String) {
        // Given
        whenever(command.entityId).thenReturn(null)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(invalidEntityId))

        // When & Then
        assertThrows<NumberFormatException> {
            provider.getCompanyEntityId(billedItem, command)
        }
    }

    @ParameterizedTest
    @ValueSource(strings = [" ", "        ", "\t", "\n", "\r", "\r\n"])
    fun `should handle whitespace-only entity ID values by falling back to billed item entity`(whitespaceEntityId: String) {
        // Given
        whenever(command.entityId).thenReturn(null)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(whitespaceEntityId))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then - Should return billed item entity since whitespace-only values are treated as blank
        assertThat(result).isEqualTo(billedItemEntityId)
    }

    @ParameterizedTest
    @ValueSource(longs = [1L, 100L, 999L, 1000L, 9999L, Long.MAX_VALUE, Long.MIN_VALUE, 0L, -1L, -999L])
    fun `should handle various valid entity ID values`(entityId: Long) {
        // Given
        whenever(command.entityId).thenReturn(null)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(entityId.toString()))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(entityId)
    }

    @ParameterizedTest
    @CsvSource(
        "123, 123, true",
        "123, 456, false",
        "0, 0, true",
        "-1, -1, true",
        "-1, 1, false",
        "9223372036854775807, 9223372036854775807, true", // Long.MAX_VALUE
        "-9223372036854775808, -9223372036854775808, true" // Long.MIN_VALUE
    )
    fun `should handle command and meter entity ID matching scenarios`(
        commandEntityId: Long,
        meterEntityId: Long,
        shouldMatch: Boolean
    ) {
        // Given
        whenever(command.entityId).thenReturn(commandEntityId)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(meterEntityId.toString()))

        if (shouldMatch) {
            // When
            val result = provider.getCompanyEntityId(billedItem, command)

            // Then
            assertThat(result).isEqualTo(meterEntityId)
        } else {
            // When & Then
            val exception = assertThrows<MplBusinessException> {
                provider.getCompanyEntityId(billedItem, command)
            }

            assertThat(exception.errorCode).isEqualTo(PayableErrorCode.COMPANY_ENTITY_ID_MISMATCH)
        }
    }

    @Test
    fun `should verify ENTITY_ID_KEY constant is used correctly`() {
        // Given
        val entityId = 789L
        whenever(command.entityId).thenReturn(null)

        // Test that only "entity_id" key works, not other variations
        val usageWithCorrectKey = Metric.newBuilder()
            .addLabels(MetricLabel.newBuilder().setKey("entity_id").setValue(entityId.toString()).build())
            .build()

        val usageWithWrongKey = Metric.newBuilder()
            .addLabels(MetricLabel.newBuilder().setKey("entityId").setValue(entityId.toString()).build())
            .addLabels(MetricLabel.newBuilder().setKey("entity-id").setValue(entityId.toString()).build())
            .addLabels(MetricLabel.newBuilder().setKey("ENTITY_ID").setValue(entityId.toString()).build())
            .build()

        whenever(billedItem.usageList).thenReturn(listOf(usageWithCorrectKey, usageWithWrongKey))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then - Should only find the one with correct "entity_id" key
        assertThat(result).isEqualTo(entityId)
    }

    @Test
    fun `should handle concurrent access scenarios`() {
        // Given
        val entityId = 789L
        whenever(command.entityId).thenReturn(null)
        whenever(billedItem.usageList).thenReturn(createMetricListWithEntityId(entityId.toString()))

        // When - Simulate multiple concurrent calls
        val results = (1..10).map {
            provider.getCompanyEntityId(billedItem, command)
        }

        // Then - All results should be consistent
        results.forEach { result ->
            assertThat(result).isEqualTo(entityId)
        }
    }

    @Test
    fun `should handle edge case with maximum number of labels per usage`() {
        // Given
        val entityId = 789L
        whenever(command.entityId).thenReturn(null)

        // Create usage with many labels including entity_id
        val usageBuilder = Metric.newBuilder()

        // Add many non-entity_id labels
        repeat(100) { index ->
            usageBuilder.addLabels(
                MetricLabel.newBuilder()
                    .setKey("key_$index")
                    .setValue("value_$index")
                    .build()
            )
        }

        // Add the entity_id label
        usageBuilder.addLabels(
            MetricLabel.newBuilder()
                .setKey("entity_id")
                .setValue(entityId.toString())
                .build()
        )

        whenever(billedItem.usageList).thenReturn(listOf(usageBuilder.build()))

        // When
        val result = provider.getCompanyEntityId(billedItem, command)

        // Then
        assertThat(result).isEqualTo(entityId)
    }
}
