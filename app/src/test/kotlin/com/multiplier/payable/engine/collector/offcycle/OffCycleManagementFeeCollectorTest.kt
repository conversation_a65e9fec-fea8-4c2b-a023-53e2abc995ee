package com.multiplier.payable.engine.collector.offcycle

import com.multiplier.payable.engine.collector.OffCycleDataCollectorInputProcessor
import com.multiplier.payable.engine.collector.data.ProcessedOffCycleCollectorInput
import com.multiplier.payable.engine.collector.payroll.ContractPayroll
import com.multiplier.payable.engine.collector.payroll.ContractPayrollService
import com.multiplier.payable.types.Amount
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.domain.aggregates.MonthYearDuration
import com.multiplier.payable.engine.offcycle.OffCycleManagementFee
import com.multiplier.payable.engine.offcycle.managementfee.OffCycleManagementFeeCalculateInput
import com.multiplier.payable.engine.offcycle.managementfee.OffCycleManagementFeeCalculationService
import com.multiplier.payable.engine.payableitem.PayableItemStoreDto
import com.multiplier.payable.engine.payableitem.PayableItemStoreService
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentCaptor
import org.mockito.Captor
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.time.LocalDate
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull

@ExtendWith(MockitoExtension::class)
class OffCycleManagementFeeCollectorTest {

    @Mock
    private lateinit var offCycleDataCollectorInputProcessor: OffCycleDataCollectorInputProcessor

    @Mock
    private lateinit var payableItemStoreService: PayableItemStoreService

    @Mock
    private lateinit var contractPayrollService: ContractPayrollService

    @Mock
    private lateinit var offCycleManagementFeeCalculationService: OffCycleManagementFeeCalculationService

    @Mock
    private lateinit var offCycleManagementFeePayableItemStoreNormalizer: OffCycleManagementFeePayableItemStoreNormalizer

    @InjectMocks
    private lateinit var offCycleManagementFeeCollector: OffCycleManagementFeeCollector

    @Captor
    private lateinit var payableItemStoreDtosCaptor: ArgumentCaptor<List<PayableItemStoreDto>>

    private lateinit var invoiceCommand: InvoiceCommand
    private lateinit var processedInput: ProcessedOffCycleCollectorInput

    @BeforeEach
    fun setUp() {
        invoiceCommand = InvoiceCommand(
            transactionId = "test-transaction-123",
            companyId = 1L,
            transactionType = com.multiplier.payable.engine.domain.entities.TransactionType.PAYROLL_OFFCYCLE_INVOICE,
            dateRange = com.multiplier.payable.engine.domain.aggregates.DateRange(
                startDate = java.time.LocalDateTime.of(2024, 6, 1, 0, 0),
                endDate = java.time.LocalDateTime.of(2024, 6, 30, 23, 59)
            ),
            transactionDate = java.time.LocalDateTime.of(2024, 6, 15, 12, 0),
            cycle = com.multiplier.payable.engine.domain.aggregates.InvoiceCycle.MONTHLY
        )

        processedInput = ProcessedOffCycleCollectorInput(
            transactionId = "test-transaction-123",
            transactionDate = java.time.LocalDateTime.of(2024, 6, 15, 12, 0),
            companyIds = setOf(1L),
            timeQueryDuration = MonthYearDuration(
                from = MonthYear(6, 2024),
                to = MonthYear(6, 2024)
            )
        )
    }

    @Nested
    inner class ProcessInput {

        @Test
        fun `should process input command and return processed input`() {
            // GIVEN
            whenever(offCycleDataCollectorInputProcessor.process(invoiceCommand))
                .thenReturn(processedInput)

            // WHEN
            val result = offCycleManagementFeeCollector.processInput(invoiceCommand)

            // THEN
            assertEquals(processedInput, result)
            verify(offCycleDataCollectorInputProcessor).process(invoiceCommand)
        }
    }

    @Nested
    inner class CollectAndSaveOffCycleManagementFees {

        @Test
        fun `should collect and save off-cycle management fees for valid payrolls`() {
            // GIVEN
            val contractPayroll = createContractPayroll()
            val managementFeeAmount = Amount.newBuilder().amount(100.0).currency(com.multiplier.payable.types.CurrencyCode.USD).build()
            val offCycleManagementFee = createOffCycleManagementFee()
            val payableItemStoreDto = createPayableItemStoreDto()

            whenever(contractPayrollService.getContractPayrolls(
                companyIds = listOf(1L),
                month = 6,
                year = 2024
            )).thenReturn(listOf(contractPayroll))

            whenever(offCycleManagementFeeCalculationService.calculateAmount(any<OffCycleManagementFeeCalculateInput>()))
                .thenReturn(managementFeeAmount)

            whenever(offCycleManagementFeePayableItemStoreNormalizer.normalize(any(), eq(processedInput)))
                .thenReturn(payableItemStoreDto)

            // WHEN
            offCycleManagementFeeCollector.collectAndSaveOffCycleManagementFees(processedInput)

            // THEN
            verify(contractPayrollService).getContractPayrolls(
                companyIds = listOf(1L),
                month = 6,
                year = 2024
            )
            verify(offCycleManagementFeeCalculationService).calculateAmount(any<OffCycleManagementFeeCalculateInput>())
            verify(offCycleManagementFeePayableItemStoreNormalizer).normalize(any(), eq(processedInput))
            verify(payableItemStoreService).saveAndIgnoreDuplication(listOf(payableItemStoreDto))
        }

        @Test
        fun `should filter out non-EOR contracts`() {
            // GIVEN
            val eorContractPayroll = createContractPayroll(contractType = ContractType.EMPLOYEE)
            val freelancerContractPayroll = createContractPayroll(contractType = ContractType.FREELANCER)
            val managementFeeAmount = Amount.newBuilder().amount(100.0).currency(com.multiplier.payable.types.CurrencyCode.USD).build()
            val payableItemStoreDto = createPayableItemStoreDto()

            whenever(contractPayrollService.getContractPayrolls(
                companyIds = listOf(1L),
                month = 6,
                year = 2024
            )).thenReturn(listOf(eorContractPayroll, freelancerContractPayroll))

            whenever(offCycleManagementFeeCalculationService.calculateAmount(any<OffCycleManagementFeeCalculateInput>()))
                .thenReturn(managementFeeAmount)

            whenever(offCycleManagementFeePayableItemStoreNormalizer.normalize(any(), eq(processedInput)))
                .thenReturn(payableItemStoreDto)

            // WHEN
            offCycleManagementFeeCollector.collectAndSaveOffCycleManagementFees(processedInput)

            // THEN
            verify(payableItemStoreService).saveAndIgnoreDuplication(listOf(payableItemStoreDto))
            // Only one call for EOR contract
            verify(offCycleManagementFeeCalculationService).calculateAmount(any<OffCycleManagementFeeCalculateInput>())
        }

        @Test
        fun `should filter out non-off-cycle payrolls`() {
            // GIVEN
            val offCyclePayroll = createContractPayroll(isOffCycle = true)
            val regularPayroll = createContractPayroll(isOffCycle = false)
            val managementFeeAmount = Amount.newBuilder().amount(100.0).currency(com.multiplier.payable.types.CurrencyCode.USD).build()
            val payableItemStoreDto = createPayableItemStoreDto()

            whenever(contractPayrollService.getContractPayrolls(
                companyIds = listOf(1L),
                month = 6,
                year = 2024
            )).thenReturn(listOf(offCyclePayroll, regularPayroll))

            whenever(offCycleManagementFeeCalculationService.calculateAmount(any<OffCycleManagementFeeCalculateInput>()))
                .thenReturn(managementFeeAmount)

            whenever(offCycleManagementFeePayableItemStoreNormalizer.normalize(any(), eq(processedInput)))
                .thenReturn(payableItemStoreDto)

            // WHEN
            offCycleManagementFeeCollector.collectAndSaveOffCycleManagementFees(processedInput)

            // THEN
            verify(payableItemStoreService).saveAndIgnoreDuplication(listOf(payableItemStoreDto))
            // Only one call for off-cycle payroll
            verify(offCycleManagementFeeCalculationService).calculateAmount(any<OffCycleManagementFeeCalculateInput>())
        }

        @Test
        fun `should filter out payrolls with billing exceptions`() {
            // GIVEN
            val normalPayroll = createContractPayroll(hasBillingException = false)
            val exceptionPayroll = createContractPayroll(hasBillingException = true)
            val managementFeeAmount = Amount.newBuilder().amount(100.0).currency(com.multiplier.payable.types.CurrencyCode.USD).build()
            val payableItemStoreDto = createPayableItemStoreDto()

            whenever(contractPayrollService.getContractPayrolls(
                companyIds = listOf(1L),
                month = 6,
                year = 2024
            )).thenReturn(listOf(normalPayroll, exceptionPayroll))

            whenever(offCycleManagementFeeCalculationService.calculateAmount(any<OffCycleManagementFeeCalculateInput>()))
                .thenReturn(managementFeeAmount)

            whenever(offCycleManagementFeePayableItemStoreNormalizer.normalize(any(), eq(processedInput)))
                .thenReturn(payableItemStoreDto)

            // WHEN
            offCycleManagementFeeCollector.collectAndSaveOffCycleManagementFees(processedInput)

            // THEN
            verify(payableItemStoreService).saveAndIgnoreDuplication(listOf(payableItemStoreDto))
            // Only one call for normal payroll
            verify(offCycleManagementFeeCalculationService).calculateAmount(any<OffCycleManagementFeeCalculateInput>())
        }

        @Test
        fun `should handle multiple companies`() {
            // GIVEN
            val processedInputMultipleCompanies = ProcessedOffCycleCollectorInput(
                transactionId = "test-transaction-123",
                transactionDate = java.time.LocalDateTime.of(2024, 6, 15, 12, 0),
                companyIds = setOf(1L, 2L),
                timeQueryDuration = MonthYearDuration(
                    from = MonthYear(6, 2024),
                    to = MonthYear(6, 2024)
                )
            )
            val contractPayroll1 = createContractPayroll(companyId = 1L)
            val contractPayroll2 = createContractPayroll(companyId = 2L)
            val managementFeeAmount = Amount.newBuilder().amount(100.0).currency(com.multiplier.payable.types.CurrencyCode.USD).build()
            val payableItemStoreDto = createPayableItemStoreDto()

            whenever(contractPayrollService.getContractPayrolls(
                companyIds = listOf(1L),
                month = 6,
                year = 2024
            )).thenReturn(listOf(contractPayroll1))

            whenever(contractPayrollService.getContractPayrolls(
                companyIds = listOf(2L),
                month = 6,
                year = 2024
            )).thenReturn(listOf(contractPayroll2))

            whenever(offCycleManagementFeeCalculationService.calculateAmount(any<OffCycleManagementFeeCalculateInput>()))
                .thenReturn(managementFeeAmount)

            whenever(offCycleManagementFeePayableItemStoreNormalizer.normalize(any(), eq(processedInputMultipleCompanies)))
                .thenReturn(payableItemStoreDto)

            // WHEN
            offCycleManagementFeeCollector.collectAndSaveOffCycleManagementFees(processedInputMultipleCompanies)

            // THEN
            verify(contractPayrollService).getContractPayrolls(
                companyIds = listOf(1L),
                month = 6,
                year = 2024
            )
            verify(contractPayrollService).getContractPayrolls(
                companyIds = listOf(2L),
                month = 6,
                year = 2024
            )
            verify(payableItemStoreService).saveAndIgnoreDuplication(listOf(payableItemStoreDto, payableItemStoreDto))
        }

        @Test
        fun `should handle empty payroll list`() {
            // GIVEN
            whenever(contractPayrollService.getContractPayrolls(
                companyIds = listOf(1L),
                month = 6,
                year = 2024
            )).thenReturn(emptyList())

            // WHEN
            offCycleManagementFeeCollector.collectAndSaveOffCycleManagementFees(processedInput)

            // THEN
            verify(contractPayrollService).getContractPayrolls(
                companyIds = listOf(1L),
                month = 6,
                year = 2024
            )
            verify(payableItemStoreService).saveAndIgnoreDuplication(emptyList())
        }
    }

    private fun createContractPayroll(
        contractId: Long = 123L,
        companyId: Long = 1L,
        contractType: ContractType = ContractType.EMPLOYEE,
        isOffCycle: Boolean = true,
        hasBillingException: Boolean? = false
    ): ContractPayroll {
        return ContractPayroll(
            contractId = contractId,
            companyId = companyId,
            type = contractType,
            isOffCycle = isOffCycle,
            hasBillingException = hasBillingException,
            countryCode = "USA",
            startDate = LocalDate.of(2024, 6, 1),
            endDate = LocalDate.of(2024, 6, 30),
            payrollCycleId = 456L,
            amountTotalCost = 1000.0,
            totalExpenseAmount = 100.0,
            currencyCode = com.multiplier.payable.types.CurrencyCode.USD,
            payCycleCount = 1,
            fetchedTime = System.currentTimeMillis(),
            totalSeveranceAccruals = 0.0
        )
    }

    private fun createOffCycleManagementFee(): OffCycleManagementFee {
        return OffCycleManagementFee(
            contractId = 123L,
            amount = com.multiplier.payable.engine.common.Amount(BigDecimal.valueOf(100.0), com.multiplier.payable.types.CurrencyCode.USD),
            originalFee = 100.0,
            appliedDiscount = null,
            countryCode = com.multiplier.payable.types.CountryCode.USA,
            companyId = 1L,
            month = 6,
            year = 2024,
            payrollCycleId = 456L
        )
    }

    private fun createPayableItemStoreDto(): PayableItemStoreDto {
        return PayableItemStoreDto(
            amount = 100.0,
            currency = com.multiplier.payable.types.CurrencyCode.USD,
            contractId = 123L,
            companyId = 1L,
            transactionId = "test-transaction-123",
            month = 6,
            year = 2024,
            itemType = com.multiplier.core.payable.adapters.api.LineItemType.PAYROLL_OFFCYCLE_MANAGEMENT_FEE,
            itemData = "{}",
            periodStartDate = LocalDate.of(2024, 6, 1),
            periodEndDate = LocalDate.of(2024, 6, 30),
            versionId = "test-version-id",
            originalTimestamp = System.currentTimeMillis(),
            countryCode = "USA"
        )
    }
}
