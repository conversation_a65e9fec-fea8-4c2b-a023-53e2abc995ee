package com.multiplier.payable.engine.reconciler.storage

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.JpaPayableItemStore
import com.multiplier.payable.engine.payableitem.JpaPayableItemStoreRepository
import com.multiplier.payable.engine.payableitem.PayableItemKey
import com.multiplier.payable.engine.payableitem.PayableItemMapper
import com.multiplier.payable.engine.reconciler.data.item.FundingItemStoreDataProvider
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Spy
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*
import kotlin.random.Random
import kotlin.test.assertEquals

@ExtendWith(MockitoExtension::class)
class FundingItemStoreDataProviderTest {

    @Mock
    private lateinit var itemStoreRepository: JpaPayableItemStoreRepository

    @Spy
    private val payableItemMapper = PayableItemMapper(ObjectMapper())

    @InjectMocks
    private lateinit var itemStoreDataProvider: FundingItemStoreDataProvider

    @Test
    fun `given no line item types when fetching latest then return empty`() {
        // given
        val lineItemTypes = emptyList<LineItemType>()
        val command = mock<InvoiceCommand>()

        // when
        val latestBatch = itemStoreDataProvider.fetchLatest(command, lineItemTypes)

        // then
        assertThat(latestBatch).isEmpty()
    }

    @Test
    fun `given multiple versions exist when fetching latest then latest batch will be returned`() {
        // given
        val companyId = 123L
        val entityId = 101L
        val fromTime = LocalDateTime.now()
        val toTime = LocalDateTime.now()
        val range = DateRange(fromTime, toTime)
        val transactionType = TransactionType.GP_FUNDING_INVOICE
        val lineItemTypes = mutableListOf(LineItemType.PEO_SALARY_DISBURSEMENT)
        val command =
            InvoiceCommand(
                transactionId = UUID.randomUUID().toString(),
                companyId = companyId,
                dateRange = range,
                transactionType = transactionType,
                cycle = InvoiceCycle.MONTHLY,
                transactionDate = LocalDateTime.now(),
                entityId = entityId,
            )

        val batchKey =
            PayableItemKey(
                month = 1,
                year = 2024,
                itemType = LineItemType.PEO_SALARY_DISBURSEMENT.name,
                companyId = companyId,
            )
        val batchSize = 3
        val batches = generateItems(3, batchSize, batchKey)
        val rangeStartDate = range.startDate.toLocalDate()
        val rangeEndDate = range.endDate.toLocalDate()
        Mockito.`when`(
            itemStoreRepository.findByCompanyIdAndEntityIdAndPayableItemTypesWithinDateRange(
                eq(companyId),
                eq(entityId),
                any(),
                eq(rangeStartDate),
                eq(rangeEndDate),
            ),
        ).thenReturn(batches)

        // when
        val latestBatch = itemStoreDataProvider.fetchLatest(command, lineItemTypes)

        // then
        assertEquals(latestBatch.size, batchSize)
        assertEquals(latestBatch[0].versionId, "2")
    }

    private fun generateItems(
        numBatch: Int,
        batchSize: Int,
        key: PayableItemKey,
    ): List<JpaPayableItemStore> {
        val results = mutableListOf<JpaPayableItemStore>()
        for (i in 0..<numBatch) {
            results.addAll(generateBatchItems(i, batchSize, key))
            Thread.sleep(1)
        }

        return results
    }

    private fun generateBatchItems(
        index: Int,
        size: Int,
        key: PayableItemKey,
    ): List<JpaPayableItemStore> {
        val results = mutableListOf<JpaPayableItemStore>()
        val oTime = Timestamp(System.currentTimeMillis())
        for (i in 0..<size) {
            results.add(
                JpaPayableItemStore.builder()
                    .amount(Random.nextDouble())
                    .itemData("{}")
                    .month(key.month)
                    .year(key.year)
                    .itemType(LineItemType.valueOf(key.itemType))
                    .currency(CurrencyCode.CUC)
                    .companyId(key.companyId)
                    .contractId(1000L + i)
                    .versionId(index.toString())
                    .originalTimestamp(oTime)
                    .countryCode(CountryCode.USA)
                    .build(),
            )
        }

        return results
    }
}
