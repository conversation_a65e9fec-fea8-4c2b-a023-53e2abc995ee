package com.multiplier.payable.engine.reconciler.diff

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.payableitem.PayableItemKey
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock

@Suppress("UNCHECKED_CAST")
@ExtendWith(MockitoExtension::class)
class SecondInvoiceTrinetDiffCalculatorTest {
    @Mock
    private lateinit var grossItemDiffHelper: GrossItemDiffHelper

    @Mock
    private lateinit var managementFeeDiffHelper: ManagementFeeDiffHelper

    @Mock
    private lateinit var valueAddedTaxDiffCalculator: ValueAddedTaxDiffCalculator

    @InjectMocks
    private lateinit var secondInvoiceTrinetDiffCalculator: SecondInvoiceTrinetDiffCalculator

    @Test
    fun given_with_invoiced_and_latest_items_then_should_calculate() {
        // given
        val latestItem = mock<PayableItem> {
            on { payrollCycleId } doReturn null
            on { isBilled } doReturn false
            on { amountInBaseCurrency } doReturn 10.0
            on { lineItemType } doReturn LineItemType.BILLED_MANAGEMENT_FEE.name
            on { month } doReturn 1
            on { year } doReturn 2024
            on { contractId } doReturn 1L
            on { companyId } doReturn 100L
        }
        val invoicedItem = mock<PayableItem> {
            on { payrollCycleId } doReturn null
            on { isBilled } doReturn true
            on { amountInBaseCurrency } doReturn 10.0
            on { lineItemType } doReturn LineItemType.BILLED_MANAGEMENT_FEE.name
            on { month } doReturn 2
            on { year } doReturn 2024
            on { contractId } doReturn 2L
            on { companyId } doReturn 200L
        }
        val invoicedItems = listOf(invoicedItem)
        val latestItems = listOf(latestItem)

        // Only the latest item should be created since invoiced items are filtered out by isBilled = true
        // and the items have different keys so they don't conflict
        val diff =
            Diff(
                emptyList(),
                emptyList(),
                listOf(latestItem),
            )

        // when
        val result = secondInvoiceTrinetDiffCalculator.calculate(latestItems, invoicedItems)

        // then
        assertThat(result).isEqualTo(diff)
    }

    @Test
    fun `given item already invoiced in previous payroll cycle then don't add`() {
        // given
        val item1 = mock<PayableItem>(lenient = true) {
            on { amountInBaseCurrency } doReturn 11.0
            on { payrollCycleId } doReturn 1
            on { contractId } doReturn 11
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
        }
        val item2 = mock<PayableItem>(lenient = true) {
            on { amountInBaseCurrency } doReturn 12.0
            on { payrollCycleId } doReturn 2
            on { contractId } doReturn 12
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
        }

        val invoicedItems = listOf(item1)
        val latestItems = listOf(item1, item2)

        val diff =
            Diff(
                emptyList(),
                emptyList(),
                listOf(item2),
            )

        // when
        val result = secondInvoiceTrinetDiffCalculator.calculate(latestItems, invoicedItems)

        // then
        assertThat(result).isEqualTo(diff)
    }

    @Test
    fun `getNegativeItems should include severance deposit EOR payroll refund items`() {
        // given
        val severanceRefundItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL_REFUND.name
            on { amountInBaseCurrency } doReturn 100.0 // positive amount should still be included
        }
        val otherItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
            on { amountInBaseCurrency } doReturn -50.0
        }
        val combinedItems = listOf(severanceRefundItem, otherItem)

        // when
        val getNegativeItemsMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getNegativeItems", List::class.java
        ).apply { isAccessible = true }
        val result = getNegativeItemsMethod.invoke(secondInvoiceTrinetDiffCalculator, combinedItems) as List<PayableItem>

        // then
        assertThat(result).hasSize(1)
        assertThat(result).contains(severanceRefundItem)
        assertThat(result).doesNotContain(otherItem)
    }

    @Test
    fun `getNegativeItems should include negative Canada payroll items`() {
        // given
        val canadaGrossWagesItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.CANADA_GROSS_WAGES.name
            on { amountInBaseCurrency } doReturn -100.0
        }
        val canadaEmployerBenefitsItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.CANADA_EMPLOYER_BENEFITS.name
            on { amountInBaseCurrency } doReturn -50.0
        }
        val canadaEmploymentInsuranceItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.CANADA_EMPLOYMENT_INSURANCE.name
            on { amountInBaseCurrency } doReturn -25.0
        }
        val otherItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
            on { amountInBaseCurrency } doReturn -75.0
        }
        val combinedItems = listOf(
            canadaGrossWagesItem,
            canadaEmployerBenefitsItem,
            canadaEmploymentInsuranceItem,
            otherItem
        )

        // when
        val getNegativeItemsMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getNegativeItems", List::class.java
        ).apply { isAccessible = true }
        val result = getNegativeItemsMethod.invoke(secondInvoiceTrinetDiffCalculator, combinedItems) as List<PayableItem>

        // then
        assertThat(result).hasSize(3)
        assertThat(result).contains(canadaGrossWagesItem)
        assertThat(result).contains(canadaEmployerBenefitsItem)
        assertThat(result).contains(canadaEmploymentInsuranceItem)
        assertThat(result).doesNotContain(otherItem)
    }

    @Test
    fun `getNegativeItems should not include positive Canada payroll items`() {
        // given
        val positiveCanadaGrossWagesItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.CANADA_GROSS_WAGES.name
            on { amountInBaseCurrency } doReturn 100.0
        }
        val negativeCanadaGrossWagesItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.CANADA_GROSS_WAGES.name
            on { amountInBaseCurrency } doReturn -100.0
        }
        val combinedItems = listOf(positiveCanadaGrossWagesItem, negativeCanadaGrossWagesItem)

        // when
        val getNegativeItemsMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getNegativeItems", List::class.java
        ).apply { isAccessible = true }
        val result = getNegativeItemsMethod.invoke(secondInvoiceTrinetDiffCalculator, combinedItems) as List<PayableItem>

        // then
        assertThat(result).hasSize(1)
        assertThat(result).contains(negativeCanadaGrossWagesItem)
        assertThat(result).doesNotContain(positiveCanadaGrossWagesItem)
    }

    @Test
    fun `getNegativeItems should include both severance refund and negative Canada items`() {
        // given
        val severanceRefundItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL_REFUND.name
            on { amountInBaseCurrency } doReturn 100.0
        }
        val negativeCanadaGrossWagesItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.CANADA_GROSS_WAGES.name
            on { amountInBaseCurrency } doReturn -100.0
        }
        val combinedItems = listOf(severanceRefundItem, negativeCanadaGrossWagesItem)

        // when
        val getNegativeItemsMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getNegativeItems", List::class.java
        ).apply { isAccessible = true }
        val result = getNegativeItemsMethod.invoke(secondInvoiceTrinetDiffCalculator, combinedItems) as List<PayableItem>

        // then
        assertThat(result).hasSize(2)
        assertThat(result).contains(severanceRefundItem)
        assertThat(result).contains(negativeCanadaGrossWagesItem)
    }

    @Test
    fun `getItemKey should return simplified key when contractId and payrollCycleId are not null`() {
        // given
        val item = mock<PayableItem> {
            on { contractId } doReturn 123L
            on { payrollCycleId } doReturn 456L
            on { month } doReturn 5
            on { year } doReturn 2024
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
        }

        // when
        val getItemKeyMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getItemKey", PayableItem::class.java
        ).apply { isAccessible = true }
        val result = getItemKeyMethod.invoke(secondInvoiceTrinetDiffCalculator, item) as PayableItemKey

        // then
        assertThat(result.month).isEqualTo(5)
        assertThat(result.year).isEqualTo(2024)
        assertThat(result.itemType).isEqualTo(LineItemType.EOR_SALARY_DISBURSEMENT.name)
        assertThat(result.contractId).isEqualTo(123L)
        assertThat(result.payrollCycleId).isEqualTo(456L)
        // Other fields should be null in simplified key
        assertThat(result.companyId).isNull()
        assertThat(result.periodStartDate).isNull()
        assertThat(result.periodEndDate).isNull()
        assertThat(result.annualSeatPaymentTerm).isNull()
    }

    @Test
    fun `getItemKey should return full key when contractId is null`() {
        // given
        val item = mock<PayableItem> {
            on { contractId } doReturn null
            on { payrollCycleId } doReturn 456L
            on { month } doReturn 5
            on { year } doReturn 2024
            on { lineItemType } doReturn LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name
            on { companyId } doReturn 789L
            on { periodStartDate } doReturn java.time.LocalDate.of(2024, 5, 1)
            on { periodEndDate } doReturn java.time.LocalDate.of(2024, 5, 31)
        }

        // when
        val getItemKeyMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getItemKey", PayableItem::class.java
        ).apply { isAccessible = true }
        val result = getItemKeyMethod.invoke(secondInvoiceTrinetDiffCalculator, item) as PayableItemKey

        // then
        assertThat(result.month).isEqualTo(5)
        assertThat(result.year).isEqualTo(2024)
        assertThat(result.itemType).isEqualTo(LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name)
        assertThat(result.contractId).isNull()
        assertThat(result.companyId).isEqualTo(789L)
        assertThat(result.payrollCycleId).isEqualTo(456L)
        assertThat(result.periodStartDate).isEqualTo(java.time.LocalDate.of(2024, 5, 1))
        assertThat(result.periodEndDate).isEqualTo(java.time.LocalDate.of(2024, 5, 31))
    }

    @Test
    fun `getItemKey should return full key when payrollCycleId is null`() {
        // given
        val item = mock<PayableItem> {
            on { contractId } doReturn 123L
            on { payrollCycleId } doReturn null
            on { month } doReturn 5
            on { year } doReturn 2024
            on { lineItemType } doReturn LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name
            on { companyId } doReturn 789L
            on { periodStartDate } doReturn java.time.LocalDate.of(2024, 5, 1)
            on { periodEndDate } doReturn java.time.LocalDate.of(2024, 5, 31)
        }

        // when
        val getItemKeyMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getItemKey", PayableItem::class.java
        ).apply { isAccessible = true }
        val result = getItemKeyMethod.invoke(secondInvoiceTrinetDiffCalculator, item) as PayableItemKey

        // then
        assertThat(result.month).isEqualTo(5)
        assertThat(result.year).isEqualTo(2024)
        assertThat(result.itemType).isEqualTo(LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name)
        assertThat(result.contractId).isEqualTo(123L)
        assertThat(result.companyId).isEqualTo(789L)
        assertThat(result.payrollCycleId).isNull()
        assertThat(result.periodStartDate).isEqualTo(java.time.LocalDate.of(2024, 5, 1))
        assertThat(result.periodEndDate).isEqualTo(java.time.LocalDate.of(2024, 5, 31))
    }

    @Test
    fun `getItemsToCreate should filter out management fee and VAT items from combined items`() {
        // given
        val regularItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
            on { isBilled } doReturn false
            on { amountInBaseCurrency } doReturn 100.0
        }
        val managementFeeItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name
        }
        val vatItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.VAT_PAYROLL_COST.name
        }
        val combinedItems = listOf(regularItem, managementFeeItem, vatItem)
        val itemsToAddOnList = emptyList<PayableItem>()
        val negativeItems = emptyList<PayableItem>()
        val alreadyInvoicedPayrollCycles = emptySet<PayableItemKey>()

        // when
        val getItemsToCreateMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getItemsToCreate", List::class.java, List::class.java, List::class.java, Set::class.java
        ).apply { isAccessible = true }
        val result = getItemsToCreateMethod.invoke(
            secondInvoiceTrinetDiffCalculator,
            combinedItems,
            itemsToAddOnList,
            negativeItems,
            alreadyInvoicedPayrollCycles
        ) as List<PayableItem>

        // then
        assertThat(result).hasSize(1)
        assertThat(result).contains(regularItem)
        assertThat(result).doesNotContain(managementFeeItem)
        assertThat(result).doesNotContain(vatItem)
    }

    @Test
    fun `getItemsToCreate should filter out billed items`() {
        // given
        val unbilledItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
            on { isBilled } doReturn false
            on { amountInBaseCurrency } doReturn 100.0
        }
        val billedItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
            on { isBilled } doReturn true
        }
        val combinedItems = listOf(unbilledItem, billedItem)
        val itemsToAddOnList = emptyList<PayableItem>()
        val negativeItems = emptyList<PayableItem>()
        val alreadyInvoicedPayrollCycles = emptySet<PayableItemKey>()

        // when
        val getItemsToCreateMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getItemsToCreate", List::class.java, List::class.java, List::class.java, Set::class.java
        ).apply { isAccessible = true }
        val result = getItemsToCreateMethod.invoke(
            secondInvoiceTrinetDiffCalculator,
            combinedItems,
            itemsToAddOnList,
            negativeItems,
            alreadyInvoicedPayrollCycles
        ) as List<PayableItem>

        // then
        assertThat(result).hasSize(1)
        assertThat(result).contains(unbilledItem)
        assertThat(result).doesNotContain(billedItem)
    }

    @Test
    fun `getItemsToCreate should filter out items already invoiced in payroll cycles`() {
        // given
        val item1 = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
            on { isBilled } doReturn false
            on { amountInBaseCurrency } doReturn 100.0
            on { contractId } doReturn 1L
            on { payrollCycleId } doReturn 1L
            on { month } doReturn 5
            on { year } doReturn 2024
        }
        val item2 = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
            on { isBilled } doReturn false
            on { amountInBaseCurrency } doReturn 200.0
            on { contractId } doReturn 2L
            on { payrollCycleId } doReturn 2L
            on { month } doReturn 5
            on { year } doReturn 2024
        }
        val combinedItems = listOf(item1, item2)
        val itemsToAddOnList = emptyList<PayableItem>()
        val negativeItems = emptyList<PayableItem>()

        // Create already invoiced payroll cycle for item1
        val alreadyInvoicedPayrollCycles = setOf(
            PayableItemKey(
                month = 5,
                year = 2024,
                itemType = LineItemType.EOR_SALARY_DISBURSEMENT.name,
                contractId = 1L,
                payrollCycleId = 1L
            )
        )

        // when
        val getItemsToCreateMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getItemsToCreate", List::class.java, List::class.java, List::class.java, Set::class.java
        ).apply { isAccessible = true }
        val result = getItemsToCreateMethod.invoke(
            secondInvoiceTrinetDiffCalculator,
            combinedItems,
            itemsToAddOnList,
            negativeItems,
            alreadyInvoicedPayrollCycles
        ) as List<PayableItem>

        // then
        assertThat(result).hasSize(1)
        assertThat(result).contains(item2)
        assertThat(result).doesNotContain(item1)
    }

    @Test
    fun `getItemsToCreate should filter out negative amount items but include negative items parameter`() {
        // given
        val positiveItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
            on { isBilled } doReturn false
            on { amountInBaseCurrency } doReturn 100.0
        }
        val negativeAmountItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
            on { isBilled } doReturn false
            on { amountInBaseCurrency } doReturn -50.0
        }
        val negativeItemFromParameter = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL_REFUND.name
        }
        val combinedItems = listOf(positiveItem, negativeAmountItem)
        val itemsToAddOnList = emptyList<PayableItem>()
        val negativeItems = listOf(negativeItemFromParameter)
        val alreadyInvoicedPayrollCycles = emptySet<PayableItemKey>()

        // when
        val getItemsToCreateMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getItemsToCreate", List::class.java, List::class.java, List::class.java, Set::class.java
        ).apply { isAccessible = true }
        val result = getItemsToCreateMethod.invoke(
            secondInvoiceTrinetDiffCalculator,
            combinedItems,
            itemsToAddOnList,
            negativeItems,
            alreadyInvoicedPayrollCycles
        ) as List<PayableItem>

        // then
        assertThat(result).hasSize(2)
        assertThat(result).contains(positiveItem)
        assertThat(result).contains(negativeItemFromParameter)
        assertThat(result).doesNotContain(negativeAmountItem)
    }

    @Test
    fun `getItemsToCreate should include VAT items only when corresponding EOR payroll items exist`() {
        // given
        val eorPayrollItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
            on { isBilled } doReturn false
            on { amountInBaseCurrency } doReturn 100.0
            on { contractId } doReturn 1L
            on { periodStartDate } doReturn java.time.LocalDate.of(2024, 5, 1)
            on { periodEndDate } doReturn java.time.LocalDate.of(2024, 5, 31)
        }
        val vatItemWithMatchingPayroll = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.VAT_PAYROLL_COST.name
            on { contractId } doReturn 1L
            on { periodStartDate } doReturn java.time.LocalDate.of(2024, 5, 1)
            on { periodEndDate } doReturn java.time.LocalDate.of(2024, 5, 31)
        }
        val vatItemWithoutMatchingPayroll = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.VAT_PAYROLL_COST.name
            on { contractId } doReturn 2L
            on { periodStartDate } doReturn java.time.LocalDate.of(2024, 5, 1)
            on { periodEndDate } doReturn java.time.LocalDate.of(2024, 5, 31)
        }
        val combinedItems = listOf(eorPayrollItem)
        val itemsToAddOnList = listOf(vatItemWithMatchingPayroll, vatItemWithoutMatchingPayroll)
        val negativeItems = emptyList<PayableItem>()
        val alreadyInvoicedPayrollCycles = emptySet<PayableItemKey>()

        // when
        val getItemsToCreateMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getItemsToCreate", List::class.java, List::class.java, List::class.java, Set::class.java
        ).apply { isAccessible = true }
        val result = getItemsToCreateMethod.invoke(
            secondInvoiceTrinetDiffCalculator,
            combinedItems,
            itemsToAddOnList,
            negativeItems,
            alreadyInvoicedPayrollCycles
        ) as List<PayableItem>

        // then
        assertThat(result).hasSize(2)
        assertThat(result).contains(eorPayrollItem)
        assertThat(result).contains(vatItemWithMatchingPayroll)
        assertThat(result).doesNotContain(vatItemWithoutMatchingPayroll)
    }

    @Test
    fun `getItemsToCreate should include management fee items not already invoiced`() {
        // given
        val regularItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
            on { isBilled } doReturn false
            on { amountInBaseCurrency } doReturn 100.0
            on { contractId } doReturn 1L
            on { payrollCycleId } doReturn 1L
            on { month } doReturn 5
            on { year } doReturn 2024
        }
        val newMgtFeeItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name
            on { contractId } doReturn 2L
        }
        val alreadyInvoicedMgtFeeItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name
            on { contractId } doReturn 1L
        }
        val combinedItems = listOf(regularItem)
        val itemsToAddOnList = listOf(newMgtFeeItem, alreadyInvoicedMgtFeeItem)
        val negativeItems = emptyList<PayableItem>()

        // Create already invoiced payroll cycle for management fee with contractId 1L
        val alreadyInvoicedPayrollCycles = setOf(
            PayableItemKey(
                itemType = LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name,
                contractId = 1L
            )
        )

        // when
        val getItemsToCreateMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getItemsToCreate", List::class.java, List::class.java, List::class.java, Set::class.java
        ).apply { isAccessible = true }
        val result = getItemsToCreateMethod.invoke(
            secondInvoiceTrinetDiffCalculator,
            combinedItems,
            itemsToAddOnList,
            negativeItems,
            alreadyInvoicedPayrollCycles
        ) as List<PayableItem>

        // then
        assertThat(result).hasSize(2)
        assertThat(result).contains(regularItem)
        assertThat(result).contains(newMgtFeeItem)
        assertThat(result).doesNotContain(alreadyInvoicedMgtFeeItem)
    }

    @Test
    fun `calculate should handle complex scenario with VAT filtering and negative items`() {
        // given
        val eorPayrollItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.EOR_SALARY_DISBURSEMENT.name
            on { isBilled } doReturn false
            on { amountInBaseCurrency } doReturn 100.0
            on { contractId } doReturn 1L
            on { payrollCycleId } doReturn 1L
            on { month } doReturn 5
            on { year } doReturn 2024
            on { periodStartDate } doReturn java.time.LocalDate.of(2024, 5, 1)
            on { periodEndDate } doReturn java.time.LocalDate.of(2024, 5, 31)
        }
        val severanceRefundItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL_REFUND.name
            on { amountInBaseCurrency } doReturn 50.0
        }
        val vatItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.VAT_PAYROLL_COST.name
            on { contractId } doReturn 1L
            on { periodStartDate } doReturn java.time.LocalDate.of(2024, 5, 1)
            on { periodEndDate } doReturn java.time.LocalDate.of(2024, 5, 31)
        }
        val managementFeeItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name
            on { contractId } doReturn 1L
        }

        val secondInvoiceItems = listOf(eorPayrollItem, severanceRefundItem)
        val firstInvoiceItems = emptyList<PayableItem>()

        // Mock helper responses
        org.mockito.kotlin.whenever(managementFeeDiffHelper.getNew(org.mockito.kotlin.any())).thenReturn(listOf(managementFeeItem))
        org.mockito.kotlin.whenever(managementFeeDiffHelper.getRefunds(org.mockito.kotlin.any())).thenReturn(emptyList())
        org.mockito.kotlin.whenever(managementFeeDiffHelper.getAdjustments(org.mockito.kotlin.any())).thenReturn(emptyList())
        org.mockito.kotlin.whenever(managementFeeDiffHelper.getAdjustmentManagementFeeWithZeroAmountDifference(org.mockito.kotlin.any())).thenReturn(emptyList())
        org.mockito.kotlin.whenever(grossItemDiffHelper.getGrossItems(org.mockito.kotlin.any())).thenReturn(emptyList())
        org.mockito.kotlin.whenever(grossItemDiffHelper.getBonusItems(org.mockito.kotlin.any())).thenReturn(emptyList())
        org.mockito.kotlin.whenever(grossItemDiffHelper.getPaySupplementItems(org.mockito.kotlin.any())).thenReturn(emptyList())
        org.mockito.kotlin.whenever(valueAddedTaxDiffCalculator.calculate(org.mockito.kotlin.any(), org.mockito.kotlin.any(), org.mockito.kotlin.any())).thenReturn(listOf(vatItem))

        // when
        val result = secondInvoiceTrinetDiffCalculator.calculate(secondInvoiceItems, firstInvoiceItems)

        // then
        assertThat(result.toBeDeleted).isEmpty()
        assertThat(result.toBeUpdated).isEmpty()
        // Note: severanceRefundItem appears twice because it's both in the regular items and negative items
        assertThat(result.toBeCreated).hasSize(5) // eorPayrollItem, severanceRefundItem (twice), vatItem, managementFeeItem
        assertThat(result.toBeCreated).contains(eorPayrollItem)
        assertThat(result.toBeCreated).contains(severanceRefundItem)
        assertThat(result.toBeCreated).contains(vatItem)
        assertThat(result.toBeCreated).contains(managementFeeItem)
    }

    @Test
    fun `calculate should exclude VAT items when no corresponding EOR payroll items exist`() {
        // given
        val nonEorItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.BILLED_MANAGEMENT_FEE.name
            on { isBilled } doReturn false
            on { amountInBaseCurrency } doReturn 100.0
            on { contractId } doReturn 1L
            on { payrollCycleId } doReturn null
            on { month } doReturn 5
            on { year } doReturn 2024
            on { companyId } doReturn 100L
        }
        val vatItem = mock<PayableItem>(lenient = true) {
            on { lineItemType } doReturn LineItemType.VAT_PAYROLL_COST.name
            on { contractId } doReturn 1L
            on { periodStartDate } doReturn java.time.LocalDate.of(2024, 5, 1)
            on { periodEndDate } doReturn java.time.LocalDate.of(2024, 5, 31)
        }

        val secondInvoiceItems = listOf(nonEorItem)
        val firstInvoiceItems = emptyList<PayableItem>()

        // Mock helper responses
        org.mockito.kotlin.whenever(managementFeeDiffHelper.getNew(org.mockito.kotlin.any())).thenReturn(emptyList())
        org.mockito.kotlin.whenever(managementFeeDiffHelper.getRefunds(org.mockito.kotlin.any())).thenReturn(emptyList())
        org.mockito.kotlin.whenever(managementFeeDiffHelper.getAdjustments(org.mockito.kotlin.any())).thenReturn(emptyList())
        org.mockito.kotlin.whenever(managementFeeDiffHelper.getAdjustmentManagementFeeWithZeroAmountDifference(org.mockito.kotlin.any())).thenReturn(emptyList())
        org.mockito.kotlin.whenever(grossItemDiffHelper.getGrossItems(org.mockito.kotlin.any())).thenReturn(emptyList())
        org.mockito.kotlin.whenever(grossItemDiffHelper.getBonusItems(org.mockito.kotlin.any())).thenReturn(emptyList())
        org.mockito.kotlin.whenever(grossItemDiffHelper.getPaySupplementItems(org.mockito.kotlin.any())).thenReturn(emptyList())
        org.mockito.kotlin.whenever(valueAddedTaxDiffCalculator.calculate(org.mockito.kotlin.any(), org.mockito.kotlin.any(), org.mockito.kotlin.any())).thenReturn(listOf(vatItem))

        // when
        val result = secondInvoiceTrinetDiffCalculator.calculate(secondInvoiceItems, firstInvoiceItems)

        // then
        assertThat(result.toBeDeleted).isEmpty()
        assertThat(result.toBeUpdated).isEmpty()
        assertThat(result.toBeCreated).hasSize(1) // only nonEorItem, VAT item should be excluded
        assertThat(result.toBeCreated).contains(nonEorItem)
        assertThat(result.toBeCreated).doesNotContain(vatItem)
    }

    @Test
    fun `getNegativeItems should include all Canada allowed negative item types`() {
        // given
        val canadaPensionPlanItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.CANADA_PENSION_PLAN.name
            on { amountInBaseCurrency } doReturn -30.0
        }
        val canadaQuebecPensionPlanItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.CANADA_QUEBEC_PENSION_PLAN.name
            on { amountInBaseCurrency } doReturn -40.0
        }
        val canadaProvincialHealthTaxItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.CANADA_PROVINCIAL_HEALTH_TAX.name
            on { amountInBaseCurrency } doReturn -50.0
        }
        val canadaWorkersCompensationItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.CANADA_WORKERS_COMPENSATION.name
            on { amountInBaseCurrency } doReturn -60.0
        }
        val canadaParentalPlanTaxItem = mock<PayableItem> {
            on { lineItemType } doReturn LineItemType.CANADA_PARENTAL_PLAN_TAX.name
            on { amountInBaseCurrency } doReturn -70.0
        }
        val combinedItems = listOf(
            canadaPensionPlanItem,
            canadaQuebecPensionPlanItem,
            canadaProvincialHealthTaxItem,
            canadaWorkersCompensationItem,
            canadaParentalPlanTaxItem
        )

        // when
        val getNegativeItemsMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getNegativeItems", List::class.java
        ).apply { isAccessible = true }
        val result = getNegativeItemsMethod.invoke(secondInvoiceTrinetDiffCalculator, combinedItems) as List<PayableItem>

        // then
        assertThat(result).hasSize(5)
        assertThat(result).contains(canadaPensionPlanItem)
        assertThat(result).contains(canadaQuebecPensionPlanItem)
        assertThat(result).contains(canadaProvincialHealthTaxItem)
        assertThat(result).contains(canadaWorkersCompensationItem)
        assertThat(result).contains(canadaParentalPlanTaxItem)
    }

    @Test
    fun `getItemKey should handle item with minimal required fields`() {
        // given
        val item = mock<PayableItem> {
            on { contractId } doReturn null
            on { payrollCycleId } doReturn null
            on { month } doReturn 5
            on { year } doReturn 2024
            on { lineItemType } doReturn LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name
            on { companyId } doReturn 100L
            on { periodStartDate } doReturn null
            on { periodEndDate } doReturn null
            on { annualSeatPaymentTerm } doReturn null
        }

        // when
        val getItemKeyMethod = SecondInvoiceTrinetDiffCalculator::class.java.getDeclaredMethod(
            "getItemKey", PayableItem::class.java
        ).apply { isAccessible = true }
        val result = getItemKeyMethod.invoke(secondInvoiceTrinetDiffCalculator, item) as PayableItemKey?

        // then - should create a full key since contractId is null
        assertThat(result).isNotNull
        assertThat(result!!.month).isEqualTo(5)
        assertThat(result.year).isEqualTo(2024)
        assertThat(result.itemType).isEqualTo(LineItemType.MANAGEMENT_FEE_EOR_PAYROLL.name)
        assertThat(result.contractId).isNull()
        assertThat(result.companyId).isEqualTo(100L)
        assertThat(result.payrollCycleId).isNull()
        assertThat(result.periodStartDate).isNull()
        assertThat(result.periodEndDate).isNull()
        assertThat(result.annualSeatPaymentTerm).isNull()
    }
}
