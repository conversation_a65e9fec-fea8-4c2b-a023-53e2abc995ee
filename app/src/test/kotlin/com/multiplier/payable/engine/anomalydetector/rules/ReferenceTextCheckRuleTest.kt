package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import java.time.LocalDate

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ReferenceTextCheckRuleTest {

    @Mock
    private lateinit var featureFlagService: FeatureFlagService

    @Mock
    private lateinit var jpaAnomalyReportRepository: JpaAnomalyReportRepository

    @Mock
    private lateinit var mockCommand: InvoiceCommand

    private lateinit var referenceTextCheckRule: ReferenceTextCheckRule

    @BeforeEach
    fun setUp() {
        referenceTextCheckRule = ReferenceTextCheckRule(featureFlagService)
    }

    @Test
    fun `should return success when feature flag is disabled`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(false)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        // Use a simple empty request
        val request = InvoiceAnomalyDetectorRequest.builder().build()

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Rule 'ReferenceTextCheck' is disabled by feature flag"))
    }

    @Test
    fun `should return success when request is invalid`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val request = InvoiceAnomalyDetectorRequest.builder().build() // Empty request

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice data or company payable is null - validation skipped"))
    }

    @Test
    fun `should return success when reference text matches expected format`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val billingMonth = 1 // January
        val billingYear = 2024
        val expectedReference = "Jan'24 Salary - EOR"
        val request = createMockRequest(expectedReference, billingMonth, billingYear)

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Reference text validation passed"))
    }

    @Test
    fun `should return success when reference text contains expected date part case insensitive`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val billingMonth = 2 // February
        val billingYear = 2024
        val invoiceReference = "feb'24 salary - eor" // lowercase
        val request = createMockRequest(invoiceReference, billingMonth, billingYear)

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Reference text validation passed"))
    }

    @Test
    fun `should return failure when reference text has wrong month`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val billingMonth = 3 // March
        val billingYear = 2024
        val wrongReference = "Feb'24 Salary" // Wrong month
        val request = createMockRequest(wrongReference, billingMonth, billingYear)

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Reference text should contain 'Mar' and '24' for billing period 03/2024 but found 'Feb'24 Salary'"))
    }

    @Test
    fun `should return failure when reference text has wrong year`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val invoiceDate = LocalDate.of(2024, 4, 20) // April 2024
        val wrongReference = "Apr'23 Salary" // Wrong year
        val request = createMockRequest(wrongReference, invoiceDate)

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Reference text should contain 'Apr' and '24' for billing period 04/2024 but found 'Apr'23 Salary'"))
    }

    @Test
    fun `should return failure when reference text is missing month`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val billingMonth = 5 // May
        val billingYear = 2024
        val wrongReference = "24 Salary Invoice" // Missing month
        val request = createMockRequest(wrongReference, billingMonth, billingYear)

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Reference text should contain 'May' and '24' for billing period 05/2024 but found '24 Salary Invoice'"))
    }

    @Test
    fun `should return failure when reference text is missing`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val invoiceDate = LocalDate.of(2024, 6, 15) // June 2024
        val request = createMockRequest(null, invoiceDate) // Missing reference

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice reference text is missing"))
    }

    @Test
    fun `should return failure when reference text is blank`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val invoiceDate = LocalDate.of(2024, 7, 20) // July 2024
        val request = createMockRequest("   ", invoiceDate) // Blank reference

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice reference text is missing"))
    }

    @Test
    fun `should return failure when billing month is missing`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val correctReference = "Aug'24 Salary - EOR"
        val request = createMockRequest(correctReference, null, 2024) // Missing month

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Billing month or year is missing from company payable"))
    }

    @Test
    fun `should return failure when billing year is missing`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val correctReference = "Aug'24 Salary - EOR"
        val request = createMockRequest(correctReference, 8, null) // Missing year

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Billing month or year is missing from company payable"))
    }

    @Test
    fun `should handle different months correctly`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val testCases = listOf(
            LocalDate.of(2024, 1, 1) to "Jan'24 Salary - EOR",
            LocalDate.of(2024, 2, 15) to "Feb'24 Salary - EOR",
            LocalDate.of(2024, 3, 31) to "Mar'24 Salary - EOR",
            LocalDate.of(2024, 4, 10) to "Apr'24 Salary - EOR",
            LocalDate.of(2024, 5, 20) to "May'24 Salary - EOR",
            LocalDate.of(2024, 6, 5) to "Jun'24 Salary - EOR",
            LocalDate.of(2024, 7, 25) to "Jul'24 Salary - EOR",
            LocalDate.of(2024, 8, 12) to "Aug'24 Salary - EOR",
            LocalDate.of(2024, 9, 8) to "Sep'24 Salary - EOR",
            LocalDate.of(2024, 10, 18) to "Oct'24 Salary - EOR",
            LocalDate.of(2024, 11, 22) to "Nov'24 Salary - EOR",
            LocalDate.of(2024, 12, 30) to "Dec'24 Salary - EOR"
        )

        testCases.forEach { (date, expectedReference) ->
            // Given
            val request = createMockRequest(expectedReference, date)

            // When
            val result = referenceTextCheckRule.detect(mockCommand, request)

            // Then
            assertTrue(result.success, "Failed for date $date with reference $expectedReference")
            assertEquals(1, result.messages.size)
            assertTrue(result.messages[0].contains("Reference text validation passed"))
        }
    }

    @Test
    fun `should handle different years correctly`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val testCases = listOf(
            LocalDate.of(2023, 6, 15) to "Jun'23 Salary - EOR",
            LocalDate.of(2024, 6, 15) to "Jun'24 Salary - EOR",
            LocalDate.of(2025, 6, 15) to "Jun'25 Salary - EOR"
        )

        testCases.forEach { (date, expectedReference) ->
            // Given
            val request = createMockRequest(expectedReference, date)

            // When
            val result = referenceTextCheckRule.detect(mockCommand, request)

            // Then
            assertTrue(result.success, "Failed for date $date with reference $expectedReference")
            assertEquals(1, result.messages.size)
            assertTrue(result.messages[0].contains("Reference text validation passed"))
        }
    }

    @Test
    fun `should return correct rule type`() {
        // When
        val ruleType = referenceTextCheckRule.type

        // Then
        assertEquals(DetectionRuleType.REFERENCE_TEXT_CHECK, ruleType)
    }

    @Test
    fun `should return correct rule name`() {
        // When
        val ruleName = referenceTextCheckRule.ruleName

        // Then
        assertEquals("ReferenceTextCheck", ruleName)
    }

    @Test
    fun `should return correct feature flag name`() {
        // When
        val featureFlagName = referenceTextCheckRule.featureFlagName

        // Then
        assertEquals("ENABLE_REFERENCE_TEXT_CHECK", featureFlagName)
    }

    @Test
    fun `should return success when reference text contains correct date part with different text`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val billingMonth = 3 // March
        val billingYear = 2024
        val invoiceReference = "Mar'24 Payroll Invoice" // Different text but contains correct date
        val request = createMockRequest(invoiceReference, billingMonth, billingYear)

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Reference text validation passed"))
    }

    @Test
    fun `should return success when reference text contains correct date part anywhere in text`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val billingMonth = 9 // September
        val billingYear = 2024
        val invoiceReference = "Invoice for Sep'24 period - Company XYZ" // Date part in middle
        val request = createMockRequest(invoiceReference, billingMonth, billingYear)

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Reference text validation passed"))
    }

    @Test
    fun `should return success when reference text contains MMM and YY without apostrophe`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val billingMonth = 6 // June
        val billingYear = 2024
        val invoiceReference = "Jun 24 Salary Invoice" // No apostrophe
        val request = createMockRequest(invoiceReference, billingMonth, billingYear)

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Reference text validation passed"))
    }

    @Test
    fun `should return success when reference text contains MMM and YY in different positions`() {
        // Given
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        Mockito.`when`(mockGbFeatureResult.on).thenReturn(true)
        Mockito.`when`(featureFlagService.feature(Mockito.anyString(), Mockito.anyMap())).thenReturn(mockGbFeatureResult)

        val billingMonth = 8 // August
        val billingYear = 2024
        val invoiceReference = "24 Year Aug Month Payroll" // Year before month
        val request = createMockRequest(invoiceReference, billingMonth, billingYear)

        // When
        val result = referenceTextCheckRule.detect(mockCommand, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Reference text validation passed"))
    }

    private fun createMockRequest(
        reference: String?,
        billingMonth: Int?,
        billingYear: Int?
    ): InvoiceAnomalyDetectorRequest {
        val mockInvoiceDTO = Mockito.mock(InvoiceDTO::class.java)
        Mockito.`when`(mockInvoiceDTO.reference).thenReturn(reference)

        val mockCompanyPayable = Mockito.mock(JpaCompanyPayable::class.java)
        Mockito.`when`(mockCompanyPayable.month).thenReturn(billingMonth)
        Mockito.`when`(mockCompanyPayable.year).thenReturn(billingYear)

        return InvoiceAnomalyDetectorRequest
            .builder()
            .invoiceDTO(mockInvoiceDTO)
            .payable(mockCompanyPayable)
            .build()
    }

    // Legacy method for tests that still need date parameter
    private fun createMockRequest(
        reference: String?,
        date: LocalDate?
    ): InvoiceAnomalyDetectorRequest {
        val billingMonth = date?.monthValue
        val billingYear = date?.year
        return createMockRequest(reference, billingMonth, billingYear)
    }
}
