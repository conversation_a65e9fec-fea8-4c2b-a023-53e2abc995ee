package com.multiplier.payable.engine.formatter.orderer

import com.multiplier.payable.engine.formatter.DataFormatter
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.mockito.Mockito.doReturn
import org.mockito.Mockito.mock

class DefaultDataFormatterOrdererTest {

    @Nested
    inner class Order {

        @Test
        fun testWhenEmptyListIsPassed_returnsEmptyList() {
            val defaultDataFormatterOrderer = DefaultDataFormatterOrderer()

            val result = defaultDataFormatterOrderer.order(listOf())

            assertEquals(0, result.size)
        }

        @Test
        fun testWhenDataFormattersArePassed_correctOrderIsObtained() {
            val dataFormatter1 = mock(DataFormatter::class.java)
            val dataFormatter2 = mock(DataFormatter::class.java)

            doReturn(1L).`when`(dataFormatter1).getOrder()
            doReturn(2L).`when`(dataFormatter2).getOrder()

            val result = DefaultDataFormatterOrderer().order(listOf(dataFormatter2, dataFormatter1))

            assertEquals(2, result.size)
            assertSame(dataFormatter1, result[0])
            assertSame(dataFormatter2, result[1])
        }

    }
}