package com.multiplier.payable.engine.scheduler.ap

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.core.payable.adapters.NewPricingServiceAdapter
import com.multiplier.core.payable.adapters.PricingPlanQuery
import com.multiplier.core.payable.repository.JpaTransactionCommandLogRepository
import com.multiplier.core.payable.repository.model.JpaTransactionCommandLog
import com.multiplier.payable.engine.TransactionStatus
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.AnnualPricingPlan
import com.multiplier.payable.engine.domain.entities.PricingPlanStatus
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.orchestrator.TransactionTriggeringType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.doThrow
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import org.springframework.data.domain.PageRequest
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@ExtendWith(MockitoExtension::class)
class AORAnnualPlanTransactionCommandContextProviderTest {

    @Mock
    private lateinit var pricingServiceAdapter: NewPricingServiceAdapter

    @Mock
    private lateinit var transactionCommandLogRepository: JpaTransactionCommandLogRepository

    @Mock
    private lateinit var objectMapper: ObjectMapper

    @InjectMocks
    private lateinit var provider: AORAnnualPlanTransactionCommandContextProvider

    private lateinit var currentMonthDateRange: DateRange
    private lateinit var mockPricingPlan: AnnualPricingPlan
    private lateinit var mockInvoiceCommand: InvoiceCommand

    @BeforeEach
    fun setup() {
        currentMonthDateRange = DateRange.currentMonth()
        mockPricingPlan = AnnualPricingPlan(
            planId = 123L,
            companyId = 456L,
            duration = DateRange(
                startDate = LocalDate.of(2024, 1, 1).atStartOfDay(),
                endDate = LocalDate.of(2024, 1, 31).atTime(LocalTime.MAX)
            ),
            offeringCode = "AOR",
            lineCode = "FREELANCER_SERVICE_FEE",
            status = PricingPlanStatus.ACTIVE
        )
        mockInvoiceCommand = InvoiceCommand(
            transactionId = "test-transaction-id",
            transactionType = TransactionType.ANNUAL_PLAN_AOR_INVOICE,
            companyId = 456L,
            dateRange = currentMonthDateRange,
            transactionDate = LocalDateTime.now(),
            cycle = InvoiceCycle.MONTHLY
        ).apply {
            tracingContext[AORAnnualPlanTransactionCommandContext.PLAN_ID_TRACE_KEY] = "123"
        }
    }

    @Test
    fun `should return contexts for active pricing plans with AOR service fee lines`() {
        // given
        `when`(transactionCommandLogRepository.findLastCommands(
            any(), any(), any(), any(), any(), any()
        )).thenReturn(emptyList())
        `when`(pricingServiceAdapter.query(any())).thenReturn(listOf(mockPricingPlan))

        // when
        val contexts = provider.poll()

        // then
        assertEquals(1, contexts.size)
        val context = contexts.first() as AORAnnualPlanTransactionCommandContext
        assertEquals(456L, context.companyId)
        assertEquals(mockPricingPlan.duration, context.dateRange)
        assertEquals(123L, context.planId)
        assertEquals("123", context.traces[AORAnnualPlanTransactionCommandContext.PLAN_ID_TRACE_KEY])
    }

    @Test
    fun `should query pricing service with correct parameters`() {
        // given
        `when`(transactionCommandLogRepository.findLastCommands(
            any(), any(), any(), any(), any(), any()
        )).thenReturn(emptyList())
        `when`(pricingServiceAdapter.query(any())).thenReturn(emptyList())

        // when
        provider.poll()

        // then
        val queryCaptor = argumentCaptor<PricingPlanQuery>()
        verify(pricingServiceAdapter).query(queryCaptor.capture())

        val query = queryCaptor.firstValue
        assertEquals(currentMonthDateRange, query.dateRange)
        assertEquals(PricingPlanStatus.ACTIVE, query.status)
        assertTrue(query.lineCodes.contains("FREELANCER_SERVICE_FEE"))
        assertTrue(query.lineCodes.contains("CONTRACTOR_SERVICE_FEE"))
        assertEquals(0L, query.lastPlanId)
        assertEquals(10, query.chunkSize)
    }

    @Test
    fun `should sort pricing plans by planId`() {
        // given
        val plan1 = mockPricingPlan.copy(planId = 300L)
        val plan2 = mockPricingPlan.copy(planId = 100L)
        val plan3 = mockPricingPlan.copy(planId = 200L)

        `when`(transactionCommandLogRepository.findLastCommands(
            any(), any(), any(), any(), any(), any()
        )).thenReturn(emptyList())
        `when`(pricingServiceAdapter.query(any())).thenReturn(listOf(plan1, plan2, plan3))

        // when
        val contexts = provider.poll()

        // then
        assertEquals(3, contexts.size)
        val planIds = contexts.map { (it as AORAnnualPlanTransactionCommandContext).planId }
        assertEquals(listOf(100L, 200L, 300L), planIds)
    }

    @Test
    fun `should use last plan id from transaction log when available`() {
        // given
        val mockLog = JpaTransactionCommandLog().apply {
            command = """{"trace":{"planId":"999"}}"""
        }
        `when`(transactionCommandLogRepository.findLastCommands(
            any(), any(), any(), any(), any(), any()
        )).thenReturn(listOf(mockLog))
        `when`(objectMapper.readValue(any<String>(), eq(InvoiceCommand::class.java)))
            .thenReturn(mockInvoiceCommand)
        `when`(pricingServiceAdapter.query(any())).thenReturn(emptyList())

        // when
        provider.poll()

        // then
        val queryCaptor = argumentCaptor<PricingPlanQuery>()
        verify(pricingServiceAdapter).query(queryCaptor.capture())
        assertEquals(123L, queryCaptor.firstValue.lastPlanId) // from mockInvoiceCommand trace
    }

    @Test
    fun `should use default last plan id when transaction log is empty`() {
        // given
        `when`(transactionCommandLogRepository.findLastCommands(
            any(), any(), any(), any(), any(), any()
        )).thenReturn(emptyList())
        `when`(pricingServiceAdapter.query(any())).thenReturn(emptyList())

        // when
        provider.poll()

        // then
        val queryCaptor = argumentCaptor<PricingPlanQuery>()
        verify(pricingServiceAdapter).query(queryCaptor.capture())
        assertEquals(0L, queryCaptor.firstValue.lastPlanId)
    }

    @Test
    fun `should handle JSON parsing exception gracefully`() {
        // given
        val mockLog = JpaTransactionCommandLog().apply {
            command = """invalid json"""
        }
        `when`(transactionCommandLogRepository.findLastCommands(
            any(), any(), any(), any(), any(), any()
        )).thenReturn(listOf(mockLog))
        doThrow(RuntimeException("JSON parsing error")).`when`(objectMapper)
            .readValue(any<String>(), eq(InvoiceCommand::class.java))
        `when`(pricingServiceAdapter.query(any())).thenReturn(emptyList())

        // when
        provider.poll()

        // then - should use the default last plan id (0) when parsing fails
        val queryCaptor = argumentCaptor<PricingPlanQuery>()
        verify(pricingServiceAdapter).query(queryCaptor.capture())
        assertEquals(0L, queryCaptor.firstValue.lastPlanId)
    }

    @Test
    fun `should query transaction log with correct parameters`() {
        // given
        `when`(transactionCommandLogRepository.findLastCommands(
            any(), any(), any(), any(), any(), any()
        )).thenReturn(emptyList())
        `when`(pricingServiceAdapter.query(any())).thenReturn(emptyList())

        // when
        provider.poll()

        // then
        verify(transactionCommandLogRepository).findLastCommands(
            eq(currentMonthDateRange.startDate),
            eq(currentMonthDateRange.endDate),
            eq(TransactionType.ANNUAL_PLAN_AOR_INVOICE.name),
            eq(TransactionTriggeringType.SCHEDULED),
            eq(setOf(TransactionStatus.INIT.name)),
            eq(PageRequest.of(0, 1))
        )
    }

    @Test
    fun `should handle missing planId in trace gracefully`() {
        // given
        val commandWithoutPlanId = InvoiceCommand(
            transactionId = "test-transaction-id",
            transactionType = TransactionType.ANNUAL_PLAN_AOR_INVOICE,
            companyId = 456L,
            dateRange = currentMonthDateRange,
            transactionDate = LocalDateTime.now(),
            cycle = InvoiceCycle.MONTHLY
        )
        val mockLog = JpaTransactionCommandLog().apply {
            command = """{"trace":{}}"""
        }
        `when`(transactionCommandLogRepository.findLastCommands(
            any(), any(), any(), any(), any(), any()
        )).thenReturn(listOf(mockLog))
        `when`(objectMapper.readValue(any<String>(), eq(InvoiceCommand::class.java)))
            .thenReturn(commandWithoutPlanId)
        `when`(pricingServiceAdapter.query(any())).thenReturn(emptyList())

        // when
        provider.poll()

        // then - should use the default last plan id (0) when planId is missing from trace
        val queryCaptor = argumentCaptor<PricingPlanQuery>()
        verify(pricingServiceAdapter).query(queryCaptor.capture())
        assertEquals(0L, queryCaptor.firstValue.lastPlanId)
    }

    @Test
    fun `should return empty list when no pricing plans found`() {
        // given
        `when`(transactionCommandLogRepository.findLastCommands(
            any(), any(), any(), any(), any(), any()
        )).thenReturn(emptyList())
        `when`(pricingServiceAdapter.query(any())).thenReturn(emptyList())

        // when
        val contexts = provider.poll()

        // then
        assertTrue(contexts.isEmpty())
    }



    @Test
    fun `should generate unique transaction IDs for multiple pricing plans`() {
        // given
        val plan1 = mockPricingPlan.copy(planId = 100L, companyId = 200L)
        val plan2 = mockPricingPlan.copy(planId = 200L, companyId = 300L)
        val plan3 = mockPricingPlan.copy(planId = 300L, companyId = 400L)

        `when`(transactionCommandLogRepository.findLastCommands(
            any(), any(), any(), any(), any(), any()
        )).thenReturn(emptyList())
        `when`(pricingServiceAdapter.query(any())).thenReturn(listOf(plan1, plan2, plan3))

        // when
        val contexts = provider.poll()

        // then
        assertEquals(3, contexts.size)
        val transactionIds = contexts.map { it.transactionId }
        assertEquals(3, transactionIds.toSet().size) // All transaction IDs should be unique

        // Verify each context has correct data
        val context1 = contexts[0] as AORAnnualPlanTransactionCommandContext
        assertEquals(100L, context1.planId)
        assertEquals(200L, context1.companyId)
        assertEquals(plan1.duration, context1.dateRange)

        val context2 = contexts[1] as AORAnnualPlanTransactionCommandContext
        assertEquals(200L, context2.planId)
        assertEquals(300L, context2.companyId)
        assertEquals(plan2.duration, context2.dateRange)

        val context3 = contexts[2] as AORAnnualPlanTransactionCommandContext
        assertEquals(300L, context3.planId)
        assertEquals(400L, context3.companyId)
        assertEquals(plan3.duration, context3.dateRange)
    }

    @Test
    fun `should verify AOR_SERVICE_FEE_LINES constant values`() {
        // This test ensures the constant values are correct and haven't changed
        val queryCaptor = argumentCaptor<PricingPlanQuery>()

        `when`(transactionCommandLogRepository.findLastCommands(
            any(), any(), any(), any(), any(), any()
        )).thenReturn(emptyList())
        `when`(pricingServiceAdapter.query(any())).thenReturn(emptyList())

        // when
        provider.poll()

        // then
        verify(pricingServiceAdapter).query(queryCaptor.capture())
        val lineCodes = queryCaptor.firstValue.lineCodes
        assertEquals(2, lineCodes.size)
        assertTrue(lineCodes.contains("FREELANCER_SERVICE_FEE"))
        assertTrue(lineCodes.contains("CONTRACTOR_SERVICE_FEE"))
    }

    @Test
    fun `should verify CHUNK_SIZE constant value`() {
        // This test ensures the chunk size constant is correct
        val queryCaptor = argumentCaptor<PricingPlanQuery>()

        `when`(transactionCommandLogRepository.findLastCommands(
            any(), any(), any(), any(), any(), any()
        )).thenReturn(emptyList())
        `when`(pricingServiceAdapter.query(any())).thenReturn(emptyList())

        // when
        provider.poll()

        // then
        verify(pricingServiceAdapter).query(queryCaptor.capture())
        assertEquals(10, queryCaptor.firstValue.chunkSize)
    }
}
