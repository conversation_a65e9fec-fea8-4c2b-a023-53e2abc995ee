package com.multiplier.payable.engine.collector.payroll.severance

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.data.DefaultProcessedCollectorInput
import com.multiplier.payable.engine.collector.data.GrossInvoiceProcessedCollectorInput
import com.multiplier.payable.engine.collector.payroll.*
import com.multiplier.payable.engine.collector.severance.SeverancePayrollDataCollectorHandler
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.engine.domain.aggregates.MonthYearDuration
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.*
import org.mockito.Mockito.doReturn
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.capture
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class SeverancePayrollDataCollectorHandlerTest {
    @Mock
    private lateinit var contractPayrollService: ContractPayrollService

    @Mock
    private lateinit var contractPayrollItemStoreService: ContractPayrollItemStoreService

    @InjectMocks
    private lateinit var severancePayrollDataCollector: SeverancePayrollDataCollectorHandler

    @Captor
    private lateinit var memberPayableItemStoreHelperInputCaptor: ArgumentCaptor<ContractPayrollsItemStoreInput>

    @Nested
    inner class Handle {

        @Test
        fun `when not DefaultProcessedCollectorInput then should throw exception`() {
            val companyId = 1L
            val transactionId = "transactionId"
            val timeQueryDuration = MonthYearDuration(
                from = MonthYear(1, 2021),
                to = MonthYear(1, 2021)
            )

            val dateRange = timeQueryDuration.toDateRange()
            val command = InvoiceCommand(
                transactionId = transactionId,
                companyId = companyId,
                dateRange = dateRange,
                transactionType = TransactionType.SECOND_INVOICE,
                transactionDate = dateRange.startDate,
                cycle = InvoiceCycle.MONTHLY
            )

            val queryTime =
                MonthYearDuration(
                    from = MonthYear.fromLocalDateTime(command.dateRange.startDate),
                    to = MonthYear.fromLocalDateTime(command.dateRange.endDate),
                )

            val defaultProcessedCollectorInput = GrossInvoiceProcessedCollectorInput(
                transactionId = command.transactionId,
                transactionDate = command.transactionDate,
                timeQueryDuration = queryTime,
                companyIds = setOf(companyId),
                forcedContractIds = setOf(2L)
            )

            val assertThrows = org.junit.jupiter.api.assertThrows<IllegalArgumentException> {
                severancePayrollDataCollector.handle(
                    defaultProcessedCollectorInput,
                    LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL
                )
            }
            assertEquals(
                "Non-supported collector processed input for ${LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL}",
                assertThrows.message
            )
        }

        @Test
        fun `should call processInput and handle`() {
            val companyId = 1L
            val transactionId = "transactionId"
            val timeQueryDuration = MonthYearDuration(
                from = MonthYear(1, 2021),
                to = MonthYear(1, 2021)
            )

            val dateRange = timeQueryDuration.toDateRange()
            val command = InvoiceCommand(
                transactionId = transactionId,
                companyId = companyId,
                dateRange = dateRange,
                transactionType = TransactionType.SECOND_INVOICE,
                transactionDate = dateRange.startDate,
                cycle = InvoiceCycle.MONTHLY
            )

            val queryTime =
                MonthYearDuration(
                    from = MonthYear.fromLocalDateTime(command.dateRange.startDate),
                    to = MonthYear.fromLocalDateTime(command.dateRange.endDate),
                )

            val defaultProcessedCollectorInput = DefaultProcessedCollectorInput(
                transactionId = command.transactionId,
                transactionDate = command.transactionDate,
                timeQueryDuration = queryTime,
                companyIds = setOf(companyId)
            )

            val contractPayroll = Mockito.mock(ContractPayroll::class.java)

            val expectedMemberPayableItemStoreHelperInput = ContractPayrollsItemStoreInput(
                transactionId = transactionId,
                contractPayrolls = listOf(contractPayroll),
                monthYearDuration = timeQueryDuration,
                itemType = LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL,
                isBillable = { it.totalSeveranceAccruals != null },
                amountCost = { it.totalSeveranceAccruals ?: 0.0 }
            )

            doReturn(listOf(contractPayroll)).whenever(contractPayrollService).getContractPayrolls(
                defaultProcessedCollectorInput.companyIds.toList(), 1, 2021
            )
            doReturn(ContractType.EMPLOYEE).whenever(contractPayroll).type
            doReturn(false).whenever(contractPayroll).isOffCycle

            severancePayrollDataCollector.handle(
                defaultProcessedCollectorInput,
                LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL
            )

            // THEN
            verify(contractPayrollItemStoreService)
                .normalizeAndSaveForCompanyPayrolls(capture(memberPayableItemStoreHelperInputCaptor))

            assertThat(memberPayableItemStoreHelperInputCaptor.value)
                .usingRecursiveComparison()
                .isEqualTo(expectedMemberPayableItemStoreHelperInput)
        }
    }
}