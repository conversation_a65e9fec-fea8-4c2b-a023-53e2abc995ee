package com.multiplier.payable.engine.reconciler.descriptionbuilder

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.entities.PayrollCycleFrequency
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class AdditionalManagementFeePayableItemDescriptionBuilderTest {
    private val amountFormatter: AmountFormatter = mock()
    private val additionalManagementFeePayableItemDescriptionBuilder =
        AdditionalManagementFeePayableItemDescriptionBuilder(amountFormatter,)

    @Test
    fun `should return correct lineItemType`() {
        // When
        val result = additionalManagementFeePayableItemDescriptionBuilder.lineItemType

        // Then
        assertEquals(LineItemType.ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL, result)
    }

    @Test
    fun `should build description correctly`() {
        // Given
        val context = mock<PayableItemDescriptionBuilderContext>()
        whenever(context.amountInBaseCurrency).thenReturn(50.0)
        whenever(context.currencyCode).thenReturn("USD")
        whenever(context.payrollCycleFrequency).thenReturn(PayrollCycleFrequency.SEMIMONTHLY)
        whenever(context.payCycleCount).thenReturn(2)
        whenever(amountFormatter.format(50.0)).thenReturn("50.0")

        // When
        val result = additionalManagementFeePayableItemDescriptionBuilder.build(context)

        // Then
        assertEquals("Management Fee for SM2: USD 50.0", result)
    }
}
