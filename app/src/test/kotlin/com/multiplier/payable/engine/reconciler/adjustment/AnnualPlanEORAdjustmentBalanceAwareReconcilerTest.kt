package com.multiplier.payable.engine.reconciler.adjustment

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.adapters.pricing.ReferenceTargetType
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.ledger.AdvanceCollectionLedger
import com.multiplier.payable.ledger.domain.AdvanceCollectionProduct
import com.multiplier.payable.engine.contract.CountryWorkStatus
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@ExtendWith(MockitoExtension::class)
class AnnualPlanEORAdjustmentBalanceAwareReconcilerTest {

    @Mock
    private lateinit var ledger: AdvanceCollectionLedger

    private lateinit var reconciler: AnnualPlanEORAdjustmentBalanceAwareReconciler

    @BeforeEach
    fun setUp() {
        reconciler = AnnualPlanEORAdjustmentBalanceAwareReconciler(ledger)
    }

    @Test
    fun `supportedAdjustmentItemType should return ORDER_FORM_ADVANCE_ADJUSTMENT_EOR_SERVICE_FEE`() {
        // When
        val result = reconciler.supportedAdjustmentItemType()

        // Then
        assertEquals(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR_SERVICE_FEE, result)
    }

    @Test
    fun `reconciler should be properly instantiated with correct dependencies`() {
        // This test ensures the reconciler can be created with all required dependencies
        // and has the correct supported adjustment item type
        assertEquals(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR_SERVICE_FEE, reconciler.supportedAdjustmentItemType())
    }

    @Test
    fun `reconciler should extend AbstractAnnualPlanAORAdjustmentDiffReconciler`() {
        // This test verifies the inheritance structure
        val isInstanceOfAbstract = reconciler is AbstractAnnualPlanAdjustmentBalanceAwareReconciler
        assertEquals(true, isInstanceOfAbstract)
    }

    @Test
    fun `reconciler should implement AdjustmentDiffReconciler interface`() {
        // This test verifies the interface implementation
        val isInstanceOfInterface = reconciler is AdjustmentBalanceAwareReconciler
        assertEquals(true, isInstanceOfInterface)
    }

    @Test
    fun `reconciler should have correct supported adjustment item type`() {
        // When
        val result = reconciler.supportedAdjustmentItemType()

        // Then
        assertEquals(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR_SERVICE_FEE, result)
    }

    @Test
    fun `reconciler should handle EOR adjustment items correctly`() {
        // When
        val supportedType = reconciler.supportedAdjustmentItemType()

        // Then
        assertEquals(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR_SERVICE_FEE, supportedType)
    }

    @Test
    fun `reconciler should be properly instantiated with all required dependencies`() {
        // This test ensures the reconciler can be created with all required dependencies
        // and has the correct supported adjustment item type
        assertEquals(LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR_SERVICE_FEE, reconciler.supportedAdjustmentItemType())
    }

    @Test
    fun `reconciler should have correct qualifier annotation`() {
        // This test verifies that the reconciler has the correct Spring qualifier
        val qualifierAnnotation = reconciler.javaClass.getAnnotation(Qualifier::class.java)
        assertNotNull(qualifierAnnotation)
        assertEquals("eor", qualifierAnnotation.value)
    }

    @Test
    fun `reconciler should be a Spring component`() {
        // This test verifies that the reconciler is properly annotated as a Spring component
        val componentAnnotation = reconciler.javaClass.getAnnotation(Component::class.java)
        assertNotNull(componentAnnotation)
    }
}
