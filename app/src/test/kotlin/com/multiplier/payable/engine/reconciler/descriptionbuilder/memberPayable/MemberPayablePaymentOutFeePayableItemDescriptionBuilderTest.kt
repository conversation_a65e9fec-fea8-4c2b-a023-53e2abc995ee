package com.multiplier.payable.engine.reconciler.descriptionbuilder.memberPayable

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import org.junit.jupiter.api.Test

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class MemberPayablePaymentOutFeePayableItemDescriptionBuilderTest {

    @InjectMockKs
    private lateinit var memberPayablePaymentOutFeePayableItemDescriptionBuilder: MemberPayablePaymentOutFeePayableItemDescriptionBuilder

    @Test
    fun getLineItemType() {
        assertEquals(LineItemType.FREELANCER_PAYOUT_FEE, memberPayablePaymentOutFeePayableItemDescriptionBuilder.lineItemType)
    }

    @Test
    fun build() {
        val context = mockk<PayableItemDescriptionBuilderContext>(){
            every { contractId } returns 1L
        }
        val result = memberPayablePaymentOutFeePayableItemDescriptionBuilder.build(context)
        assertEquals("Payout fee for contractId: 1", result)
    }
}