package com.multiplier.payable.engine.reconciler.diff.reconciler

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.reconciler.diff.Diff
import com.multiplier.payable.engine.reconciler.diff.FirstInvoiceDiffCalculator
import com.multiplier.payable.engine.reconciler.diff.handler.AddOnlyDiffHandler
import com.multiplier.payable.engine.reconciler.diff.handler.DefaultDiffHandlerContext
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.`when`
import org.mockito.kotlin.mock
import java.math.BigDecimal
import java.time.LocalDate

class FirstInvoiceDiffReconcilerTest {

    private lateinit var diffCalculator: FirstInvoiceDiffCalculator
    private lateinit var diffHandler: AddOnlyDiffHandler
    private lateinit var reconciler: FirstInvoiceDiffReconciler

    @BeforeEach
    fun setUp() {
        diffCalculator = mock()
        diffHandler = mock()
        reconciler = FirstInvoiceDiffReconciler(diffCalculator, diffHandler)
    }

    @Test
    fun `should handle diff only for applicable items`() {
        // Given
        val command = mock<InvoiceCommand>()
        val template = mock<TransactionTemplate>()
        val applicableItemTypes = setOf(LineItemType.GROSS_SALARY.name, LineItemType.MANAGEMENT_FEE_EOR.name)

        val oldItems = listOf(
            createPayableItem(
                lineItemType = LineItemType.GROSS_SALARY.name,
                amountInBaseCurrency = BigDecimal("100.00")
            ),
            createPayableItem(
                lineItemType = LineItemType.MANAGEMENT_FEE_EOR.name,
                amountInBaseCurrency = BigDecimal("150.00")
            ),
            createPayableItem(
                lineItemType = LineItemType.EOR_EXPENSE_DISBURSEMENT.name,
                amountInBaseCurrency = BigDecimal("200.00")
            )
        )

        val newItems = listOf(
            createPayableItem(
                lineItemType = LineItemType.GROSS_SALARY.name,
                amountInBaseCurrency = BigDecimal("150.00")
            ),
            createPayableItem(
                lineItemType = LineItemType.MANAGEMENT_FEE_EOR.name,
                amountInBaseCurrency = BigDecimal("150.00")
            ),

            createPayableItem(
                lineItemType = LineItemType.EOR_EXPENSE_DISBURSEMENT.name,
                amountInBaseCurrency = BigDecimal("250.00")
            )
        )

        val applicableOldItems = oldItems.filter { applicableItemTypes.contains(it.lineItemType) }
        val applicableNewItems = newItems.filter { applicableItemTypes.contains(it.lineItemType) }

        `when`(template.lineItemTypes).thenReturn(listOf(LineItemType.GROSS_SALARY, LineItemType.MANAGEMENT_FEE_EOR))
        val diff = Diff(toBeCreated = applicableNewItems, toBeDeleted = emptyList(), toBeUpdated = emptyList())
        `when`(
            diffCalculator.calculate(
                applicableNewItems,
                applicableOldItems
            )
        ).thenReturn(diff)
        `when`(diffHandler.handle(DefaultDiffHandlerContext(diff))).thenReturn(applicableNewItems)

        // When
        val reconciledItems = reconciler.reconcile(command, template, oldItems, newItems)

        // Then
        assertEquals(2, reconciledItems.size)
        assertTrue(reconciledItems.all { applicableItemTypes.contains(it.lineItemType) })
    }

    @Test
    fun `should return transaction type as FIRST_INVOICE`() {
        assertEquals(TransactionType.FIRST_INVOICE, reconciler.transactionType())
    }

    private fun createPayableItem(
        lineItemType: String,
        amountInBaseCurrency: BigDecimal,
    ): PayableItem {
        return PayableItem(
            month = 1,
            year = 2024,
            lineItemType = lineItemType,
            contractId = 1,
            annualSeatPaymentTerm = null,
            companyId = 123,
            description = "Sample description",
            amountInBaseCurrency = amountInBaseCurrency.toDouble(),
            billableCost = amountInBaseCurrency.toDouble(),
            baseCurrency = "USD",
            versionId = "v1",
            originalTimestamp = System.currentTimeMillis(),
            cycle = InvoiceCycle.MONTHLY,
            countryCode = "US",
            periodStartDate = LocalDate.now().minusDays(30),
            periodEndDate = LocalDate.now(),
            insuranceType = null
        )
    }
}
