package com.multiplier.payable.engine.payableitem.key.provider

import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.payableitem.PayableItemKey
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.junit.jupiter.MockitoExtension
import java.time.Instant

@ExtendWith(MockitoExtension::class)
class FirstInvoicePayableItemKeyProviderTest {

    @InjectMocks
    private lateinit var provider: FirstInvoicePayableItemKeyProvider

    @Test
    fun whenGetType_thenReturnType() {
        // WHEN
        val type = provider.getType();

        // WHEN and THEN
        assertThat(type).isEqualTo(TransactionType.FIRST_INVOICE)
    }

    @Test
    fun givenDeposit_whenBuild_thenReturnDescription() {
        // GIVEN
        val item = PayableItem(
            month = 42,
            year = 42,
            lineItemType = "awesomeItemType",
            amountInBaseCurrency = 42.0,
            baseCurrency = "awesomeBaseCurrency",
            cycle = InvoiceCycle.MONTHLY,
            originalTimestamp = 42L,
            countryCode = "awesomeCountryCode",
        )

        val expectedItemKey = PayableItemKey(
            month = item.month,
            year = item.year,
            itemType = item.lineItemType,
            contractId = item.contractId,
            companyId = item.companyId,
        )

        // WHEN
        val itemKey = provider.getKey(item)

        // THEN
        assertThat(itemKey).isEqualTo(expectedItemKey)
    }
}