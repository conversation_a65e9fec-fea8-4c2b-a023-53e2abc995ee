package com.multiplier.payable.engine.collector.vas

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.collector.data.ProcessedIncidentCollectorInput
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.transaction.IncidentsInvoiceCommand
import com.multiplier.payable.engine.vas.IncidentAmountChargePolicy
import com.multiplier.payable.engine.vas.IncidentManagementFee
import com.multiplier.payable.engine.vas.IncidentType
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDateTime
import java.time.Month
import java.util.*

class IncidentManagementFeePayableItemStoreNormalizerTest {

    private lateinit var objectMapper: ObjectMapper
    private lateinit var normalizer: IncidentManagementFeePayableItemStoreNormalizer

    @BeforeEach
    fun setUp() {
        objectMapper = ObjectMapper().apply {
            registerModule(JavaTimeModule())
            disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        }
        normalizer = IncidentManagementFeePayableItemStoreNormalizer(objectMapper)
    }

    @Test
    fun `should create normalizer successfully`() {
        // Assert
        assertThat(normalizer).isNotNull()
    }

    @Test
    fun `should normalize IncidentManagementFee to PayableItemStoreDto correctly`() {
        // Arrange
        val chargePolicy = IncidentAmountChargePolicy(Amount(BigDecimal("25.00"), CurrencyCode.USD))
        val incidentManagementFee = IncidentManagementFee(
            incidentId = 123L,
            amount = Amount(BigDecimal("150.75"), CurrencyCode.USD),
            chargePolicy = chargePolicy,
            contractId = 456L,
            countryCode = CountryCode.IND,
            incidentType = IncidentType.LAPTOP
        )

        val startDate = LocalDateTime.of(2024, 5, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 5, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 5, 15, 10, 30)
        val originalTimestamp = Instant.parse("2024-05-15T12:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "txn-123",
            transactionDate = transactionDate,
            companyId = 789L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(incidentManagementFee, processedInput)

        // Assert
        assertThat(result.amount).isEqualTo(150.75)
        assertThat(result.currency).isEqualTo(CurrencyCode.USD)
        assertThat(result.companyId).isEqualTo(789L)
        assertThat(result.contractId).isEqualTo(456L)
        assertThat(result.transactionId).isEqualTo("txn-123")
        assertThat(result.month).isEqualTo(Month.MAY.value)
        assertThat(result.year).isEqualTo(2024)
        assertThat(result.itemData).isNotNull()
        assertThat(result.itemData).contains("\"incidentId\":123")
        assertThat(result.itemData).contains("\"contractId\":456")
        assertThat(result.itemType).isEqualTo(LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT)
        assertThat(result.periodStartDate).isEqualTo(transactionDate.toLocalDate())
        assertThat(result.periodEndDate).isEqualTo(transactionDate.toLocalDate())
        assertThat(result.countryCode).isEqualTo("IND")
        assertThat(result.originalTimestamp).isEqualTo(originalTimestamp.toEpochMilli())
        assertThat(result.versionId).isNotNull()
    }

    @Test
    fun `should handle IncidentManagementFee with null contractId`() {
        // Arrange
        val chargePolicy = IncidentAmountChargePolicy(Amount(BigDecimal("30.00"), CurrencyCode.EUR))
        val incidentManagementFee = IncidentManagementFee(
            incidentId = 456L,
            amount = Amount(BigDecimal("100.00"), CurrencyCode.EUR),
            chargePolicy = chargePolicy,
            contractId = null,
            countryCode = CountryCode.SGP,
            incidentType = IncidentType.STORAGE
        )

        val startDate = LocalDateTime.of(2024, 3, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 3, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 3, 10, 15, 0)
        val originalTimestamp = Instant.parse("2024-03-10T15:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "txn-456",
            transactionDate = transactionDate,
            companyId = 999L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(incidentManagementFee, processedInput)

        // Assert
        assertThat(result.amount).isEqualTo(100.00)
        assertThat(result.currency).isEqualTo(CurrencyCode.EUR)
        assertThat(result.contractId).isNull()
        assertThat(result.countryCode).isEqualTo("SGP")
        assertThat(result.itemType).isEqualTo(LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT)
        assertThat(result.month).isEqualTo(Month.MARCH.value)
        assertThat(result.year).isEqualTo(2024)
        assertThat(result.itemData).contains("\"contractId\":null")
    }

    @ParameterizedTest
    @EnumSource(IncidentType::class)
    fun `should handle different incident types correctly`(incidentType: IncidentType) {
        // Arrange
        val chargePolicy = IncidentAmountChargePolicy(Amount(BigDecimal("40.00"), CurrencyCode.GBP))
        val incidentManagementFee = IncidentManagementFee(
            incidentId = 789L,
            amount = Amount(BigDecimal("250.50"), CurrencyCode.GBP),
            chargePolicy = chargePolicy,
            contractId = 111L,
            countryCode = CountryCode.GBR,
            incidentType = incidentType
        )

        val startDate = LocalDateTime.of(2024, 12, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 12, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 12, 25, 10, 0)
        val originalTimestamp = Instant.parse("2024-12-25T10:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "txn-789",
            transactionDate = transactionDate,
            companyId = 555L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(incidentManagementFee, processedInput)

        // Assert
        assertThat(result.amount).isEqualTo(250.50)
        assertThat(result.currency).isEqualTo(CurrencyCode.GBP)
        assertThat(result.contractId).isEqualTo(111L)
        assertThat(result.countryCode).isEqualTo("GBR")
        assertThat(result.itemType).isEqualTo(LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT)
        assertThat(result.month).isEqualTo(Month.DECEMBER.value)
        assertThat(result.year).isEqualTo(2024)
        assertThat(result.itemData).contains("\"incidentType\":\"${incidentType.name}\"")
    }

    @Test
    fun `should handle zero amount correctly`() {
        // Arrange
        val chargePolicy = IncidentAmountChargePolicy(Amount(BigDecimal.ZERO, CurrencyCode.USD))
        val incidentManagementFee = IncidentManagementFee(
            incidentId = 999L,
            amount = Amount(BigDecimal.ZERO, CurrencyCode.USD),
            chargePolicy = chargePolicy,
            contractId = 123L,
            countryCode = CountryCode.USA,
            incidentType = IncidentType.LAPTOP
        )

        val startDate = LocalDateTime.of(2024, 1, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 1, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 1, 15, 12, 0)
        val originalTimestamp = Instant.parse("2024-01-15T12:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "txn-zero",
            transactionDate = transactionDate,
            companyId = 100L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(incidentManagementFee, processedInput)

        // Assert
        assertThat(result.amount).isEqualTo(0.0)
        assertThat(result.currency).isEqualTo(CurrencyCode.USD)
        assertThat(result.countryCode).isEqualTo("USA")
        assertThat(result.itemData).contains("\"amount\":{\"value\":0")
    }

    @Test
    fun `should handle negative amount correctly`() {
        // Arrange
        val chargePolicy = IncidentAmountChargePolicy(Amount(BigDecimal("-10.00"), CurrencyCode.EUR))
        val incidentManagementFee = IncidentManagementFee(
            incidentId = 888L,
            amount = Amount(BigDecimal("-50.25"), CurrencyCode.EUR),
            chargePolicy = chargePolicy,
            contractId = 999L,
            countryCode = CountryCode.DEU,
            incidentType = IncidentType.LEGAL_CONSULTATION
        )

        val startDate = LocalDateTime.of(2024, 6, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 6, 30, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 6, 15, 14, 30)
        val originalTimestamp = Instant.parse("2024-06-15T14:30:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "txn-negative",
            transactionDate = transactionDate,
            companyId = 200L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(incidentManagementFee, processedInput)

        // Assert
        assertThat(result.amount).isEqualTo(-50.25)
        assertThat(result.currency).isEqualTo(CurrencyCode.EUR)
        assertThat(result.countryCode).isEqualTo("DEU")
        assertThat(result.month).isEqualTo(Month.JUNE.value)
        assertThat(result.year).isEqualTo(2024)
    }

    @Test
    fun `should handle very large amounts correctly`() {
        // Arrange
        val largeAmount = BigDecimal("*********.99")
        val chargePolicy = IncidentAmountChargePolicy(Amount(BigDecimal("50000.00"), CurrencyCode.JPY))
        val incidentManagementFee = IncidentManagementFee(
            incidentId = 777L,
            amount = Amount(largeAmount, CurrencyCode.JPY),
            chargePolicy = chargePolicy,
            contractId = 888L,
            countryCode = CountryCode.JPN,
            incidentType = IncidentType.STORAGE
        )

        val startDate = LocalDateTime.of(2024, 9, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 9, 30, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 9, 15, 9, 0)
        val originalTimestamp = Instant.parse("2024-09-15T09:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "txn-large",
            transactionDate = transactionDate,
            companyId = 300L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(incidentManagementFee, processedInput)

        // Assert
        assertThat(result.amount).isEqualTo(*********.99)
        assertThat(result.currency).isEqualTo(CurrencyCode.JPY)
        assertThat(result.countryCode).isEqualTo("JPN")
    }

    @Test
    fun `should handle cross-year date ranges correctly`() {
        // Arrange
        val chargePolicy = IncidentAmountChargePolicy(Amount(BigDecimal("20.00"), CurrencyCode.CAD))
        val incidentManagementFee = IncidentManagementFee(
            incidentId = 666L,
            amount = Amount(BigDecimal("75.50"), CurrencyCode.CAD),
            chargePolicy = chargePolicy,
            contractId = 777L,
            countryCode = CountryCode.CAN,
            incidentType = IncidentType.OTHERS_SERVICE
        )

        val startDate = LocalDateTime.of(2023, 12, 15, 0, 0)
        val endDate = LocalDateTime.of(2024, 1, 15, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 1, 1, 0, 0)
        val originalTimestamp = Instant.parse("2024-01-01T00:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "txn-cross-year",
            transactionDate = transactionDate,
            companyId = 400L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(incidentManagementFee, processedInput)

        // Assert
        assertThat(result.month).isEqualTo(Month.DECEMBER.value) // From startDate
        assertThat(result.year).isEqualTo(2023) // From startDate
        assertThat(result.periodStartDate).isEqualTo(transactionDate.toLocalDate())
        assertThat(result.periodEndDate).isEqualTo(transactionDate.toLocalDate())
    }

    @Test
    fun `should handle ObjectMapper serialization correctly`() {
        // Arrange
        val chargePolicy = IncidentAmountChargePolicy(Amount(BigDecimal("35.00"), CurrencyCode.USD))
        val incidentManagementFee = IncidentManagementFee(
            incidentId = 555L,
            amount = Amount(BigDecimal("123.45"), CurrencyCode.USD),
            chargePolicy = chargePolicy,
            contractId = 666L,
            countryCode = CountryCode.USA,
            incidentType = IncidentType.LAPTOP
        )

        val startDate = LocalDateTime.of(2024, 4, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 4, 30, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 4, 15, 11, 0)
        val originalTimestamp = Instant.parse("2024-04-15T11:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "txn-json",
            transactionDate = transactionDate,
            companyId = 500L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(incidentManagementFee, processedInput)

        // Assert
        assertThat(result.itemData).isNotNull()
        assertThat(result.itemData).isNotEmpty()

        // Verify JSON contains all expected fields
        assertThat(result.itemData).contains("\"incidentId\":555")
        assertThat(result.itemData).contains("\"amount\":")
        assertThat(result.itemData).contains("\"value\":123.45")
        assertThat(result.itemData).contains("\"currency\":\"USD\"")
        assertThat(result.itemData).contains("\"contractId\":666")
        assertThat(result.itemData).contains("\"countryCode\":\"USA\"")
        assertThat(result.itemData).contains("\"incidentType\":\"LAPTOP\"")
    }

    @Test
    fun `should handle ObjectMapper serialization failure gracefully`() {
        // Arrange
        val mockObjectMapper = mockk<ObjectMapper>()
        val normalizerWithMockMapper = IncidentManagementFeePayableItemStoreNormalizer(mockObjectMapper)

        val chargePolicy = IncidentAmountChargePolicy(Amount(BigDecimal("15.00"), CurrencyCode.USD))
        val incidentManagementFee = IncidentManagementFee(
            incidentId = 444L,
            amount = Amount(BigDecimal("100.00"), CurrencyCode.USD),
            chargePolicy = chargePolicy,
            contractId = 555L,
            countryCode = CountryCode.USA,
            incidentType = IncidentType.LAPTOP
        )

        val startDate = LocalDateTime.of(2024, 7, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 7, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 7, 15, 13, 0)
        val originalTimestamp = Instant.parse("2024-07-15T13:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "txn-error",
            transactionDate = transactionDate,
            companyId = 600L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT
        processedInput.originalTimestamp = originalTimestamp

        every { mockObjectMapper.writeValueAsString(any()) } throws RuntimeException("Serialization failed")

        // Act & Assert
        assertThrows<RuntimeException> {
            normalizerWithMockMapper.normalize(incidentManagementFee, processedInput)
        }

        verify { mockObjectMapper.writeValueAsString(incidentManagementFee) }
    }

    @Test
    fun `should generate consistent versionId for same input`() {
        // Arrange
        val chargePolicy = IncidentAmountChargePolicy(Amount(BigDecimal("45.00"), CurrencyCode.USD))
        val incidentManagementFee = IncidentManagementFee(
            incidentId = 333L,
            amount = Amount(BigDecimal("200.00"), CurrencyCode.USD),
            chargePolicy = chargePolicy,
            contractId = 444L,
            countryCode = CountryCode.USA,
            incidentType = IncidentType.LAPTOP
        )

        val startDate = LocalDateTime.of(2024, 8, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 8, 31, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 8, 15, 16, 0)
        val originalTimestamp = Instant.parse("2024-08-15T16:00:00Z")

        val processedInput1 = ProcessedIncidentCollectorInput(
            transactionId = "txn-version1",
            transactionDate = transactionDate,
            companyId = 700L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput1.lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT
        processedInput1.originalTimestamp = originalTimestamp

        val processedInput2 = ProcessedIncidentCollectorInput(
            transactionId = "txn-version2",
            transactionDate = transactionDate,
            companyId = 700L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput2.lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT
        processedInput2.originalTimestamp = originalTimestamp

        // Act
        val result1 = normalizer.normalize(incidentManagementFee, processedInput1)
        val result2 = normalizer.normalize(incidentManagementFee, processedInput2)

        // Assert
        assertThat(result1.versionId).isEqualTo(result2.versionId)
        assertThat(result1.originalTimestamp).isEqualTo(result2.originalTimestamp)
    }

    @Test
    fun `should handle different currencies correctly`() {
        // Arrange
        val currencies = listOf(
            CurrencyCode.USD to "USD",
            CurrencyCode.EUR to "EUR",
            CurrencyCode.GBP to "GBP",
            CurrencyCode.JPY to "JPY",
            CurrencyCode.CAD to "CAD"
        )

        currencies.forEach { (currencyCode, expectedString) ->
            val chargePolicy = IncidentAmountChargePolicy(Amount(BigDecimal("10.00"), currencyCode))
            val incidentManagementFee = IncidentManagementFee(
                incidentId = 222L,
                amount = Amount(BigDecimal("50.00"), currencyCode),
                chargePolicy = chargePolicy,
                contractId = 333L,
                countryCode = CountryCode.USA,
                incidentType = IncidentType.LAPTOP
            )

            val startDate = LocalDateTime.of(2024, 10, 1, 0, 0)
            val endDate = LocalDateTime.of(2024, 10, 31, 23, 59)
            val transactionDate = LocalDateTime.of(2024, 10, 15, 18, 0)
            val originalTimestamp = Instant.parse("2024-10-15T18:00:00Z")

            val processedInput = ProcessedIncidentCollectorInput(
                transactionId = "txn-currency-$expectedString",
                transactionDate = transactionDate,
                companyId = 800L,
                dateRange = DateRange(startDate, endDate),
                incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
            )
            processedInput.lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT
            processedInput.originalTimestamp = originalTimestamp

            // Act
            val result = normalizer.normalize(incidentManagementFee, processedInput)

            // Assert
            assertThat(result.currency).isEqualTo(currencyCode)
            assertThat(result.itemData).contains("\"currency\":\"$expectedString\"")
        }
    }

    @Test
    fun `should validate all required fields are populated in result`() {
        // Arrange
        val chargePolicy = IncidentAmountChargePolicy(Amount(BigDecimal("55.00"), CurrencyCode.USD))
        val incidentManagementFee = IncidentManagementFee(
            incidentId = 111L,
            amount = Amount(BigDecimal("300.00"), CurrencyCode.USD),
            chargePolicy = chargePolicy,
            contractId = 222L,
            countryCode = CountryCode.USA,
            incidentType = IncidentType.LAPTOP
        )

        val startDate = LocalDateTime.of(2024, 11, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 11, 30, 23, 59)
        val transactionDate = LocalDateTime.of(2024, 11, 15, 20, 0)
        val originalTimestamp = Instant.parse("2024-11-15T20:00:00Z")

        val processedInput = ProcessedIncidentCollectorInput(
            transactionId = "txn-validation",
            transactionDate = transactionDate,
            companyId = 900L,
            dateRange = DateRange(startDate, endDate),
            incidentsInvoiceCommand = IncidentsInvoiceCommand(UUID.randomUUID())
        )
        processedInput.lineItemType = LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT
        processedInput.originalTimestamp = originalTimestamp

        // Act
        val result = normalizer.normalize(incidentManagementFee, processedInput)

        // Assert - Verify all required fields are populated
        assertThat(result.amount).isNotNull()
        assertThat(result.currency).isNotNull()
        assertThat(result.companyId).isNotNull()
        assertThat(result.transactionId).isNotNull()
        assertThat(result.month).isNotNull()
        assertThat(result.year).isNotNull()
        assertThat(result.itemData).isNotNull()
        assertThat(result.itemType).isNotNull()
        assertThat(result.periodStartDate).isNotNull()
        assertThat(result.periodEndDate).isNotNull()
        assertThat(result.versionId).isNotNull()
        assertThat(result.originalTimestamp).isNotNull()
        assertThat(result.countryCode).isNotNull()

        // Verify specific values
        assertThat(result.amount).isEqualTo(300.0)
        assertThat(result.currency).isEqualTo(CurrencyCode.USD)
        assertThat(result.companyId).isEqualTo(900L)
        assertThat(result.contractId).isEqualTo(222L)
        assertThat(result.transactionId).isEqualTo("txn-validation")
        assertThat(result.month).isEqualTo(Month.NOVEMBER.value)
        assertThat(result.year).isEqualTo(2024)
        assertThat(result.itemType).isEqualTo(LineItemType.VAS_INCIDENT_LAPTOP_PAYMENT)
        assertThat(result.countryCode).isEqualTo("USA")
    }
}
