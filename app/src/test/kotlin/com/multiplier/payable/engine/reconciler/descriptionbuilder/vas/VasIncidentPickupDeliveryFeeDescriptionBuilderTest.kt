package com.multiplier.payable.engine.reconciler.descriptionbuilder.vas

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.payable.engine.reconciler.descriptionbuilder.PayableItemDescriptionBuilderContext
import com.multiplier.payable.engine.vas.Incident
import com.multiplier.payable.engine.vas.IncidentWrapper
import com.multiplier.payable.types.CountryCode
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class VasIncidentPickupDeliveryFeeDescriptionBuilderTest {
    private val builder = VasIncidentPickupDeliveryFeeDescriptionBuilder()

    @Test
    fun `should return correct line item type`() {
        assertEquals(LineItemType.VAS_INCIDENT_PICKUP_DELIVERY_FEE, builder.lineItemType)
    }

    @Test
    fun `should return India specific description for IND country code`() {
        // Arrange
        val incident = mockk<Incident> {
            every { countryCode } returns CountryCode.IND
        }
        val incidentWrapper = mockk<IncidentWrapper> {
            every { <EMAIL> } returns incident
        }
        val context = mockk<PayableItemDescriptionBuilderContext> {
            every { <EMAIL> } returns incidentWrapper
        }

        // Act
        val description = builder.build(context)

        // Assert
        assertEquals("Pick up and delivery Fees India", description)
    }

    @Test
    fun `should return NA specific description for USA country code`() {
        // Arrange
        val incident = mockk<Incident> {
            every { countryCode } returns CountryCode.USA
        }
        val incidentWrapper = mockk<IncidentWrapper> {
            every { <EMAIL> } returns incident
        }
        val context = mockk<PayableItemDescriptionBuilderContext> {
            every { <EMAIL> } returns incidentWrapper
        }

        // Act
        val description = builder.build(context)

        // Assert
        assertEquals("Pick up and delivery Fees NA", description)
    }

    @Test
    fun `should return NA specific description for CAN country code`() {
        // Arrange
        val incident = mockk<Incident> {
            every { countryCode } returns CountryCode.CAN
        }
        val incidentWrapper = mockk<IncidentWrapper> {
            every { <EMAIL> } returns incident
        }
        val context = mockk<PayableItemDescriptionBuilderContext> {
            every { <EMAIL> } returns incidentWrapper
        }

        // Act
        val description = builder.build(context)

        // Assert
        assertEquals("Pick up and delivery Fees NA", description)
    }

    @Test
    fun `should return NA specific description for MEX country code`() {
        // Arrange
        val incident = mockk<Incident> {
            every { countryCode } returns CountryCode.MEX
        }
        val incidentWrapper = mockk<IncidentWrapper> {
            every { <EMAIL> } returns incident
        }
        val context = mockk<PayableItemDescriptionBuilderContext> {
            every { <EMAIL> } returns incidentWrapper
        }

        // Act
        val description = builder.build(context)

        // Assert
        assertEquals("Pick up and delivery Fees NA", description)
    }

    @Test
    fun `should return ROW description for other country codes`() {
        // Arrange
        val incident = mockk<Incident> {
            every { countryCode } returns CountryCode.GBR
        }
        val incidentWrapper = mockk<IncidentWrapper> {
            every { <EMAIL> } returns incident
        }
        val context = mockk<PayableItemDescriptionBuilderContext> {
            every { <EMAIL> } returns incidentWrapper
        }

        // Act
        val description = builder.build(context)

        // Assert
        assertEquals("Pick up and delivery Fees ROW", description)
    }
}
