package com.multiplier.payable.engine.anomalydetector.rules

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.core.anomalydetector.model.InvoiceAnomalyDetectorRequest
import com.multiplier.core.anomalydetector.model.repository.JpaAnomalyReportRepository
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.invoice.database.JpaInvoiceRepository
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaInvoice
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.types.CompanyPayableType
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.types.PayableStatus
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.mockito.Mockito.lenient
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import java.time.LocalDateTime
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@ExtendWith(MockitoExtension::class)
class FirstInvoiceCurrencyMatchRuleTest {

    @Mock
    private lateinit var jpaInvoiceRepository: JpaInvoiceRepository

    @Mock
    private lateinit var jpaCompanyPayableRepository: JpaCompanyPayableRepository

    @Mock
    private lateinit var featureFlagService: FeatureFlagService

    @Mock
    private lateinit var jpaAnomalyReportRepository: JpaAnomalyReportRepository

    private lateinit var firstInvoiceCurrencyMatchRule: FirstInvoiceCurrencyMatchRule

    @BeforeEach
    fun setUp() {
        firstInvoiceCurrencyMatchRule = FirstInvoiceCurrencyMatchRule(
            jpaInvoiceRepository,
            jpaCompanyPayableRepository,
            featureFlagService
        )
    }

    @Test
    fun `should pass when single first invoice and second invoice currencies match`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        lenient().`when`(mockGbFeatureResult.on).thenReturn(true)
        lenient().`when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = createRequest(
            secondInvoiceCurrency = CurrencyCode.USD,
            firstInvoiceCurrency = CurrencyCode.USD
        )

        // Mock single first invoice lookup
        mockFirstInvoiceLookup(command, listOf(CurrencyCode.USD))

        // When
        val result = firstInvoiceCurrencyMatchRule.detect(command, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Currency validation passed - second invoice currency USD matches first invoice currency"))
    }

    @Test
    fun `should pass when multiple first invoices with same currency match second invoice currency`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        lenient().`when`(mockGbFeatureResult.on).thenReturn(true)
        lenient().`when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = createRequest(
            secondInvoiceCurrency = CurrencyCode.EUR,
            firstInvoiceCurrency = CurrencyCode.EUR
        )

        // Mock multiple first invoices with same currency
        mockFirstInvoiceLookup(command, listOf(CurrencyCode.EUR, CurrencyCode.EUR, CurrencyCode.EUR))

        // When
        val result = firstInvoiceCurrencyMatchRule.detect(command, request)

        // Then
        assertTrue(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Currency validation passed - second invoice currency EUR matches first invoice currency (validated against 3 first invoices)"))
    }

    @Test
    fun `should fail when consistent first invoices and second invoice currencies do not match`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        lenient().`when`(mockGbFeatureResult.on).thenReturn(true)
        lenient().`when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = createRequest(
            secondInvoiceCurrency = CurrencyCode.SGD,
            firstInvoiceCurrency = CurrencyCode.USD
        )

        // Mock multiple first invoices with consistent currency
        mockFirstInvoiceLookup(command, listOf(CurrencyCode.USD, CurrencyCode.USD))

        // When
        val result = firstInvoiceCurrencyMatchRule.detect(command, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Second invoice currency SGD does not match consistent first invoice currency USD"))
    }

    @Test
    fun `should fail when multiple first invoices have different currencies`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        lenient().`when`(mockGbFeatureResult.on).thenReturn(true)
        lenient().`when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = createRequest(
            secondInvoiceCurrency = CurrencyCode.USD,
            firstInvoiceCurrency = CurrencyCode.USD
        )

        // Mock multiple first invoices with different currencies
        mockFirstInvoiceLookup(command, listOf(CurrencyCode.USD, CurrencyCode.EUR, CurrencyCode.GBP))

        // When
        val result = firstInvoiceCurrencyMatchRule.detect(command, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Multiple first invoices found with different currencies"))
        assertTrue(result.messages[0].contains("USD, EUR, GBP"))
        assertTrue(result.messages[0].contains("All first invoices must use the same currency"))
    }

    @Test
    fun `should fail when current invoice currency is null`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        lenient().`when`(mockGbFeatureResult.on).thenReturn(true)
        lenient().`when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(createInvoiceDTO(null)) // Null currency
            .payable(createCompanyPayable())
            .build()

        // When
        val result = firstInvoiceCurrencyMatchRule.detect(command, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Current invoice billing currency is null"))
    }

    @Test
    fun `should skip validation when first invoice cannot be found`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        lenient().`when`(mockGbFeatureResult.on).thenReturn(true)
        lenient().`when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(createInvoiceDTO(CurrencyCode.USD))
            .payable(createCompanyPayable())
            .build()

        // Mock first invoice lookup to return empty
        mockFirstInvoiceLookup(command, emptyList())

        // When
        val result = firstInvoiceCurrencyMatchRule.detect(command, request)

        // Then
        assertTrue(result.success) // Should skip validation when first invoice not found
        assertEquals(1, result.messages.size) // Should have skip message
        assertTrue(result.messages[0].contains("No first invoices found for company 123, month 11, year 2024 - validation skipped"))
    }

    @Test
    fun `should handle repository exception gracefully`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        lenient().`when`(mockGbFeatureResult.on).thenReturn(true)
        lenient().`when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(createInvoiceDTO(CurrencyCode.USD))
            .payable(createCompanyPayable())
            .build()

        // Mock repository to throw exception
        val monthYear = command.getMonthYear()
        `when`(jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
            eq(command.companyId),
            eq(monthYear.year),
            eq(monthYear.month),
            eq(CompanyPayableType.FIRST_INVOICE),
            any()
        )).thenThrow(RuntimeException("Database error"))

        // When
        val result = firstInvoiceCurrencyMatchRule.detect(command, request)

        // Then
        assertTrue(result.success) // Should skip validation when exception occurs during first invoice lookup
        assertEquals(1, result.messages.size) // Should have skip message
        assertTrue(result.messages[0].contains("No first invoices found for company 123, month 11, year 2024 - validation skipped"))
    }

    @Test
    fun `should fail when first invoices have mixed currencies including second invoice currency`() {
        // Given
        // Mock feature flag to be enabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        lenient().`when`(mockGbFeatureResult.on).thenReturn(true)
        lenient().`when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = createRequest(
            secondInvoiceCurrency = CurrencyCode.USD,
            firstInvoiceCurrency = CurrencyCode.USD
        )

        // Mock multiple first invoices with different currencies (including the second invoice currency)
        mockFirstInvoiceLookup(command, listOf(CurrencyCode.USD, CurrencyCode.EUR, CurrencyCode.USD))

        // When
        val result = firstInvoiceCurrencyMatchRule.detect(command, request)

        // Then
        assertFalse(result.success)
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Multiple first invoices found with different currencies"))
        assertTrue(result.messages[0].contains("USD, EUR, USD"))
        assertTrue(result.messages[0].contains("All first invoices must use the same currency"))
    }

    @Test
    fun `should skip check when feature flag is disabled`() {
        // Given
        // Mock feature flag to be disabled
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        lenient().`when`(mockGbFeatureResult.on).thenReturn(false)
        lenient().`when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(createInvoiceDTO(CurrencyCode.SGD))
            .payable(createCompanyPayable())
            .build()

        // When
        val result = firstInvoiceCurrencyMatchRule.detect(command, request)

        // Then
        assertTrue(result.success) // Should pass when feature flag is disabled
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Rule 'FirstInvoiceCurrencyMatch' is disabled by feature flag"))
    }

    @Test
    fun `should skip check when invoice data is null`() {
        // Given
        // Mock feature flag to be enabled (but should skip due to null invoice)
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        lenient().`when`(mockGbFeatureResult.on).thenReturn(true)
        lenient().`when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(null) // Null invoice
            .payable(createCompanyPayable())
            .build()

        // When
        val result = firstInvoiceCurrencyMatchRule.detect(command, request)

        // Then
        assertTrue(result.success) // Should pass when request is invalid
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice data or company payable is null - validation skipped"))
    }

    @Test
    fun `should skip check when company payable is null`() {
        // Given
        // Mock feature flag to be enabled (but should skip due to null payable)
        val mockGbFeatureResult = Mockito.mock(GBFeatureResult::class.java)
        lenient().`when`(mockGbFeatureResult.on).thenReturn(true)
        lenient().`when`(featureFlagService.feature(any(), any())).thenReturn(mockGbFeatureResult)

        val command = createInvoiceCommand()
        val request = InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(createInvoiceDTO(CurrencyCode.USD))
            .payable(null) // Null payable
            .build()

        // When
        val result = firstInvoiceCurrencyMatchRule.detect(command, request)

        // Then
        assertTrue(result.success) // Should pass when request is invalid
        assertEquals(1, result.messages.size)
        assertTrue(result.messages[0].contains("Invoice data or company payable is null - validation skipped"))
    }

    private fun createInvoiceCommand(): InvoiceCommand {
        // Use a fixed date that matches the company payable (November 2024)
        val transactionDate = LocalDateTime.of(2024, 11, 15, 10, 0, 0)
        return InvoiceCommand(
            transactionId = "test-transaction-id",
            transactionType = TransactionType.SECOND_INVOICE,
            companyId = 123L,
            dateRange = DateRange(transactionDate, transactionDate),
            transactionDate = transactionDate,
            cycle = InvoiceCycle.MONTHLY
        )
    }

    private fun createRequest(
        secondInvoiceCurrency: CurrencyCode?,
        firstInvoiceCurrency: CurrencyCode?
    ): InvoiceAnomalyDetectorRequest {
        return InvoiceAnomalyDetectorRequest.builder()
            .invoiceDTO(createInvoiceDTO(secondInvoiceCurrency))
            .payable(createCompanyPayable())
            .build()
    }

    private fun createInvoiceDTO(billingCurrency: CurrencyCode?): InvoiceDTO {
        return InvoiceDTO.builder()
            .id(1L)
            .billingCurrencyCode(billingCurrency)
            .totalAmount(1000.0)
            .lineItems(emptyList())
            .build()
    }

    private fun createCompanyPayable(): JpaCompanyPayable {
        return JpaCompanyPayable().apply {
            id = 456L // Set the ID to avoid NullPointerException
            companyId = 123L
            month = 11
            year = 2024
            totalAmount = 1000.0
            currency = CurrencyCode.USD
        }
    }

    private fun mockFirstInvoiceLookup(command: InvoiceCommand, currencies: List<CurrencyCode>) {
        val monthYear = command.getMonthYear()

        if (currencies.isEmpty()) {
            // Mock empty result
            `when`(jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
                eq(command.companyId),
                eq(monthYear.year),
                eq(monthYear.month),
                eq(CompanyPayableType.FIRST_INVOICE),
                any()
            )).thenReturn(emptyList())
        } else {
            // Mock successful lookup with multiple payables
            val firstInvoicePayables = currencies.mapIndexed { index, currency ->
                JpaCompanyPayable().apply {
                    id = 789L + index
                    companyId = command.companyId
                    month = monthYear.month
                    year = monthYear.year
                    this.currency = currency
                    type = CompanyPayableType.FIRST_INVOICE
                    status = PayableStatus.AUTHORIZED
                }
            }

            `when`(jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
                eq(command.companyId),
                eq(monthYear.year),
                eq(monthYear.month),
                eq(CompanyPayableType.FIRST_INVOICE),
                any()
            )).thenReturn(firstInvoicePayables)

            // Mock the invoice lookup for each payable
            firstInvoicePayables.forEach { payable ->
                val firstInvoice = JpaInvoice().apply {
                    id = 999L + (payable.id!! - 789L)
                    companyPayable = payable
                }

                `when`(jpaInvoiceRepository.findByCompanyPayableId(eq(payable.id!!)))
                    .thenReturn(Optional.of(firstInvoice))
            }
        }
    }
}
