package com.multiplier.payable.engine.transaction.template

import com.multiplier.core.payable.expenseBill.ExpenseBillLineItemType
import com.multiplier.payable.engine.splitter.RecordSplitter
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito.mock
import org.mockito.junit.jupiter.MockitoExtension
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

@DisplayName("DefaultCountryTransactionTemplate Tests")
@ExtendWith(MockitoExtension::class)
class DefaultCountryTransactionTemplateTest {

    @Nested
    @DisplayName("Constructor and Property Initialization")
    inner class ConstructorAndPropertyInitialization {
        
        @Test
        fun `should initialize with all properties`() {
            // Arrange
            val identifier = "test-template"
            val expenseBillLineItemTypes = setOf(
                ExpenseBillLineItemType.INDIA_PAYROLL,
                ExpenseBillLineItemType.INDIA_PAYROLL_STATUTORY_TDS
            )
            val splitter1 = mock(RecordSplitter::class.java)
            val splitter2 = mock(RecordSplitter::class.java)
            val splitters = listOf(splitter1, splitter2)
            
            // Act
            val template = DefaultCountryTransactionTemplate(
                identifier = identifier,
                expenseBillLineItemTypes = expenseBillLineItemTypes,
                splitters = splitters
            )
            
            // Assert
            assertEquals(identifier, template.identifier)
            assertEquals(expenseBillLineItemTypes, template.expenseBillLineItemTypes)
            assertEquals(splitters, template.splitters)
        }
        
        @Test
        fun `should initialize with minimal properties`() {
            // Arrange
            val identifier = "minimal-template"
            
            // Act
            val template = DefaultCountryTransactionTemplate(
                identifier = identifier,
                expenseBillLineItemTypes = null,
                splitters = null
            )
            
            // Assert
            assertEquals(identifier, template.identifier)
            assertNull(template.expenseBillLineItemTypes)
            assertNull(template.splitters)
        }
    }
    
    @Nested
    @DisplayName("Interface Implementation")
    inner class InterfaceImplementation {
        
        @Test
        fun `should implement CountryTransactionTemplate interface`() {
            // Arrange
            val template = DefaultCountryTransactionTemplate(
                identifier = "interface-test",
                expenseBillLineItemTypes = setOf(ExpenseBillLineItemType.UK_PAYROLL),
                splitters = emptyList()
            )
            
            // Assert
            assertTrue(template is CountryTransactionTemplate)
        }
        
        @Test
        fun `should provide access to all interface properties`() {
            // Arrange
            val identifier = "property-test"
            val expenseBillLineItemTypes = setOf(ExpenseBillLineItemType.PHILIPPINES_PAYROLL)
            val splitters = listOf<RecordSplitter>(mock(RecordSplitter::class.java))
            
            // Act
            val template: CountryTransactionTemplate = DefaultCountryTransactionTemplate(
                identifier = identifier,
                expenseBillLineItemTypes = expenseBillLineItemTypes,
                splitters = splitters
            )
            
            // Assert
            assertEquals(identifier, template.identifier)
            assertEquals(expenseBillLineItemTypes, template.expenseBillLineItemTypes)
            assertEquals(splitters, template.splitters)
        }
    }
    
    @Nested
    @DisplayName("Behavior with Different Values")
    inner class BehaviorWithDifferentValues {
        
        @Test
        fun `should handle empty collections`() {
            // Arrange & Act
            val template = DefaultCountryTransactionTemplate(
                identifier = "empty-collections",
                expenseBillLineItemTypes = emptySet(),
                splitters = emptyList()
            )
            
            // Assert
            assertTrue(template.expenseBillLineItemTypes?.isEmpty() == true)
            assertTrue(template.splitters?.isEmpty() == true)
        }
        
        @Test
        fun `should handle multiple expense bill line item types`() {
            // Arrange
            val lineItemTypes = setOf(
                ExpenseBillLineItemType.CANADA_PAYROLL_NET_PAY,
                ExpenseBillLineItemType.CANADA_PAYROLL_RETIREMENT_SAVINGS_PLAN,
                ExpenseBillLineItemType.CANADA_REVENUE_AGENCY_CRA_STAT_PAY
            )
            
            // Act
            val template = DefaultCountryTransactionTemplate(
                identifier = "multiple-line-items",
                expenseBillLineItemTypes = lineItemTypes,
                splitters = null
            )
            
            // Assert
            assertEquals(3, template.expenseBillLineItemTypes?.size)
            assertTrue(template.expenseBillLineItemTypes?.contains(ExpenseBillLineItemType.CANADA_PAYROLL_NET_PAY) == true)
            assertTrue(template.expenseBillLineItemTypes?.contains(ExpenseBillLineItemType.CANADA_PAYROLL_RETIREMENT_SAVINGS_PLAN) == true)
            assertTrue(template.expenseBillLineItemTypes?.contains(ExpenseBillLineItemType.CANADA_REVENUE_AGENCY_CRA_STAT_PAY) == true)
        }
        
        @Test
        fun `should handle empty identifier`() {
            // Arrange & Act
            val template = DefaultCountryTransactionTemplate(
                identifier = "",
                expenseBillLineItemTypes = null,
                splitters = null
            )
            
            // Assert
            assertEquals("", template.identifier)
        }
    }
}
