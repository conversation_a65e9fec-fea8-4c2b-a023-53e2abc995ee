package com.multiplier.payable.engine.offcycle

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository
import com.multiplier.core.payable.repository.model.JpaCompanyPayable
import com.multiplier.core.payable.repository.model.JpaPayableItem
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.reconciler.data.invoice.OffCycleInvoiceDataProvider
import com.multiplier.payable.engine.testutils.InvoiceEngineTestDataFactory
import com.multiplier.payable.engine.transaction.mapper.CompanyPayableMapper
import com.multiplier.payable.types.CompanyPayableType
import com.multiplier.payable.types.PayableStatus
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class OffCycleInvoiceDataProviderTest {

    @MockK
    private lateinit var jpaCompanyPayableRepository: JpaCompanyPayableRepository

    @MockK
    private lateinit var mapper: CompanyPayableMapper

    @InjectMockKs
    private lateinit var dataProvider: OffCycleInvoiceDataProvider

    @Test
    fun `transactionType should return PAYROLL_OFFCYCLE_INVOICE`() {
        // WHEN
        val result = dataProvider.transactionType()

        // THEN
        assertEquals(TransactionType.PAYROLL_OFFCYCLE_INVOICE, result)
    }

    @Test
    fun `fetchActiveInvoices should return mapped company payables with items`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            companyId = 123L,
            transactionId = "TX123456789"
        )

        val companyPayable1 = mockk<JpaCompanyPayable> {
            every { items } returns setOf(mockk<JpaPayableItem>())
            every { id } returns 1L
            every { transactionId } returns "TX111111111"
        }
        val companyPayable2 = mockk<JpaCompanyPayable> {
            every { items } returns setOf(mockk<JpaPayableItem>(), mockk<JpaPayableItem>())
            every { id } returns 2L
            every { transactionId } returns "TX222222222"
        }
        val companyPayable3 = mockk<JpaCompanyPayable> {
            every { items } returns emptySet()
            every { id } returns 3L
            every { transactionId } returns "TX333333333"
        }

        val companyPayables = listOf(companyPayable1, companyPayable2, companyPayable3)

        val mappedCompanyPayable1 = mockk<CompanyPayable>()
        val mappedCompanyPayable2 = mockk<CompanyPayable>()

        every { jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
            command.companyId,
            command.getMonthYear().year,
            command.getMonthYear().month,
            CompanyPayableType.PAYROLL_OFFCYCLE_INVOICE,
            setOf(PayableStatus.VOIDED, PayableStatus.DELETED)
        ) } returns companyPayables

        every { mapper.mapCompanyPayable(companyPayable1) } returns mappedCompanyPayable1
        every { mapper.mapCompanyPayable(companyPayable2) } returns mappedCompanyPayable2

        // WHEN
        val result = dataProvider.fetchActiveInvoices(command)

        // THEN
        assertEquals(2, result.size)
        assertEquals(listOf(mappedCompanyPayable1, mappedCompanyPayable2), result)

        verify { jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
            command.companyId,
            command.getMonthYear().year,
            command.getMonthYear().month,
            CompanyPayableType.PAYROLL_OFFCYCLE_INVOICE,
            setOf(PayableStatus.VOIDED, PayableStatus.DELETED)
        ) }
        verify { mapper.mapCompanyPayable(companyPayable1) }
        verify { mapper.mapCompanyPayable(companyPayable2) }
        verify(exactly = 0) { mapper.mapCompanyPayable(companyPayable3) }
    }

    @Test
    fun `fetchActiveInvoices should filter out company payables with empty items`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

        val companyPayable1 = mockk<JpaCompanyPayable> {
            every { items } returns emptySet()
            every { transactionId } returns "TX111111111"
        }
        val companyPayable2 = mockk<JpaCompanyPayable> {
            every { items } returns emptySet()
            every { transactionId } returns "TX222222222"
        }

        val companyPayables = listOf(companyPayable1, companyPayable2)

        every { jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
            any(), any(), any(), any(), any()
        ) } returns companyPayables

        // WHEN
        val result = dataProvider.fetchActiveInvoices(command)

        // THEN
        assertEquals(0, result.size)
        verify(exactly = 0) { mapper.mapCompanyPayable(any()) }
    }

    @Test
    fun `fetchActiveInvoices should handle mapping exceptions gracefully`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

        val companyPayable1 = mockk<JpaCompanyPayable> {
            every { items } returns setOf(mockk<JpaPayableItem>())
            every { id } returns 1L
            every { transactionId } returns "TX111111111"
        }
        val companyPayable2 = mockk<JpaCompanyPayable> {
            every { items } returns setOf(mockk<JpaPayableItem>())
            every { id } returns 2L
            every { transactionId } returns "TX222222222"
        }

        val companyPayables = listOf(companyPayable1, companyPayable2)

        val mappedCompanyPayable2 = mockk<CompanyPayable>()

        every { jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
            any(), any(), any(), any(), any()
        ) } returns companyPayables

        every { mapper.mapCompanyPayable(companyPayable1) } throws RuntimeException("Mapping error")
        every { mapper.mapCompanyPayable(companyPayable2) } returns mappedCompanyPayable2

        // WHEN
        val result = dataProvider.fetchActiveInvoices(command)

        // THEN
        assertEquals(1, result.size)
        assertEquals(mappedCompanyPayable2, result[0])
    }

    @Test
    fun `fetchActiveInvoices should return empty list when no company payables found`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

        every { jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
            any(), any(), any(), any(), any()
        ) } returns emptyList()

        // WHEN
        val result = dataProvider.fetchActiveInvoices(command)

        // THEN
        assertEquals(0, result.size)
    }

    @Test
    fun `fetchActiveInvoices should use correct parameters for repository query`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            companyId = 456L,
            transactionId = "TX987654321"
        )

        every { jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
            any(), any(), any(), any(), any()
        ) } returns emptyList()

        // WHEN
        dataProvider.fetchActiveInvoices(command)

        // THEN
        verify { jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
            456L,
            command.getMonthYear().year,
            command.getMonthYear().month,
            CompanyPayableType.PAYROLL_OFFCYCLE_INVOICE,
            setOf(PayableStatus.VOIDED, PayableStatus.DELETED)
        ) }
    }

    @Test
    fun `fetchActiveInvoices should exclude voided and deleted statuses`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

        every { jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
            any(), any(), any(), any(), any()
        ) } returns emptyList()

        // WHEN
        dataProvider.fetchActiveInvoices(command)

        // THEN
        verify { jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
            any(), any(), any(), any(),
            setOf(PayableStatus.VOIDED, PayableStatus.DELETED)
        ) }
    }

    @Test
    fun `fetchActiveInvoices should filter out company payables with same transactionId as command`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand(
            companyId = 123L,
            transactionId = "TX123456789"
        )

        val companyPayable1 = mockk<JpaCompanyPayable> {
            every { items } returns setOf(mockk<JpaPayableItem>())
            every { id } returns 1L
            every { transactionId } returns "TX123456789" // Same as command
        }
        val companyPayable2 = mockk<JpaCompanyPayable> {
            every { items } returns setOf(mockk<JpaPayableItem>())
            every { id } returns 2L
            every { transactionId } returns "TX222222222" // Different from command
        }

        val companyPayables = listOf(companyPayable1, companyPayable2)
        val mappedCompanyPayable2 = mockk<CompanyPayable>()

        every { jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
            any(), any(), any(), any(), any()
        ) } returns companyPayables

        every { mapper.mapCompanyPayable(companyPayable2) } returns mappedCompanyPayable2

        // WHEN
        val result = dataProvider.fetchActiveInvoices(command)

        // THEN
        assertEquals(1, result.size)
        assertEquals(mappedCompanyPayable2, result[0])
        verify { mapper.mapCompanyPayable(companyPayable2) }
        verify(exactly = 0) { mapper.mapCompanyPayable(companyPayable1) }
    }

    @Test
    fun `fetchActiveInvoices should handle all mapping exceptions and return partial results`() {
        // GIVEN
        val command = InvoiceEngineTestDataFactory.createInvoiceCommand()

        val companyPayable1 = mockk<JpaCompanyPayable> {
            every { items } returns setOf(mockk<JpaPayableItem>())
            every { id } returns 1L
            every { transactionId } returns "TX111111111"
        }
        val companyPayable2 = mockk<JpaCompanyPayable> {
            every { items } returns setOf(mockk<JpaPayableItem>())
            every { id } returns 2L
            every { transactionId } returns "TX222222222"
        }

        val companyPayables = listOf(companyPayable1, companyPayable2)

        every { jpaCompanyPayableRepository.findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn(
            any(), any(), any(), any(), any()
        ) } returns companyPayables

        every { mapper.mapCompanyPayable(companyPayable1) } throws RuntimeException("Mapping error 1")
        every { mapper.mapCompanyPayable(companyPayable2) } throws RuntimeException("Mapping error 2")

        // WHEN
        val result = dataProvider.fetchActiveInvoices(command)

        // THEN
        assertEquals(0, result.size)
    }
}
