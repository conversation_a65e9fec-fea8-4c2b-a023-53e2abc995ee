package com.multiplier.payable.engine.orchestrator

import com.multiplier.payable.engine.TransactionCommand
import com.multiplier.payable.engine.TransactionCommandHandler
import com.multiplier.payable.engine.TransactionStatus
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.lock.distributed.DistributedLockProvider
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.verify
import org.hamcrest.MatcherAssert.*
import org.hamcrest.Matchers.*
import org.mockito.Mockito.*
import org.mockito.kotlin.mock
import java.time.Duration
import kotlin.test.assertTrue

class OrchestratorConfigurationTest {

    private val dataCollector: TransactionCommandHandler<TransactionCommand> = mock()
    private val dataReconciler: TransactionCommandHandler<TransactionCommand> = mock()
    private val invoiceGenerator: TransactionCommandHandler<TransactionCommand> = mock()
    private val anomalyDetector: TransactionCommandHandler<TransactionCommand> = mock()
    private val isrGenerator: TransactionCommandHandler<TransactionCommand> = mock()
    private val expenseBillCollector: TransactionCommandHandler<TransactionCommand> = mock()
    private val expenseBillReconciler: TransactionCommandHandler<TransactionCommand> = mock()
    private val expenseBillGenerator: TransactionCommandHandler<TransactionCommand> = mock()
    private val distributedLockProvider: DistributedLockProvider = mock()
    private val configurationProperties: InvoiceOrchestratorConfigurationProperties = mock()

    @Nested
    inner class SecondInvoiceStateMachine {
        private val stateMachine =
            OrchestratorConfiguration(
                dataCollector = dataCollector,
                dataReconciler = dataReconciler,
                invoiceGenerator = invoiceGenerator,
                anomalyDetector = anomalyDetector,
                isrGenerator = isrGenerator,
                expenseBillDataCollector = expenseBillCollector,
                expenseBillReconciler = expenseBillReconciler,
                expenseBillGenerator = expenseBillGenerator,
                distributedLockProvider = distributedLockProvider,
                configurationProperties = configurationProperties,
            ).secondInvoiceStateMachine()

        @Test
        fun `INIT transit to DATA_COLLECTING when COMMIT`() {
            // given
            val cur = TransactionStatus.INIT
            val action = TransactionAction.COMMIT

            // when
            val transactionAction = stateMachine.next(cur, action)

            // then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.DATA_COLLECTING))
        }

        @Test
        fun `DATA_COLLECTING transit to DATA_RECONCILING when COMMIT`() {
            // given
            val cur = TransactionStatus.DATA_COLLECTING
            val action = TransactionAction.COMMIT

            // when
            val transactionAction = stateMachine.next(cur, action)

            // then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.DATA_RECONCILING))
        }

        @Test
        fun `DATA_RECONCILING transit to INVOICE_GENERATING when COMMIT`() {
            // given
            val cur = TransactionStatus.DATA_RECONCILING
            val action = TransactionAction.COMMIT

            // when
            val transactionAction = stateMachine.next(cur, action)

            // then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.INVOICE_GENERATING))
        }

        @Test
        fun `INVOICE_GENERATING transit to ANOMALY_DETECTING when COMMIT`() {
            // given
            val cur = TransactionStatus.INVOICE_GENERATING
            val action = TransactionAction.COMMIT

            // when
            val transactionAction = stateMachine.next(cur, action)

            // then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.ANOMALY_DETECTING))
        }

        @Test
        fun `ANOMALY_DETECTING transit to ISR_GENERATING when COMMIT`() {
            // given
            val cur = TransactionStatus.ANOMALY_DETECTING
            val action = TransactionAction.COMMIT

            // when
            val transactionAction = stateMachine.next(cur, action)

            // then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.ISR_GENERATING))
        }

        @Test
        fun `ISR_GENERATING transit to DONE when COMMIT`() {
            // given
            val cur = TransactionStatus.ISR_GENERATING
            val action = TransactionAction.COMMIT

            // when
            val transactionAction = stateMachine.next(cur, action)

            // then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.DONE))
        }

        @Test
        fun `ISR_GENERATING transit to ERROR when STOP`() {
            // given
            val cur = TransactionStatus.ISR_GENERATING
            val action = TransactionAction.STOP

            // when
            val transactionAction = stateMachine.next(cur, action)

            // then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.ERROR))
        }

        @Test
        fun `ANOMALY_DETECTING transit to ERROR when STOP`() {
            // given
            val cur = TransactionStatus.ANOMALY_DETECTING
            val action = TransactionAction.STOP

            // when
            val transactionAction = stateMachine.next(cur, action)

            // then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.ERROR))
        }

    }

    @Nested
    inner class FirstInvoiceStateMachine {
        private val stateMachine =
            OrchestratorConfiguration(
                dataCollector,
                dataReconciler,
                invoiceGenerator,
                anomalyDetector,
                isrGenerator,
                expenseBillCollector,
                expenseBillReconciler,
                expenseBillGenerator,
                distributedLockProvider,
                configurationProperties,
            ).firstInvoiceStateMachine()

        @Test
        fun `INVOICE_GENERATING transit to ANOMALY_DETECTING when COMMIT`() {
            //given
            val cur = TransactionStatus.INVOICE_GENERATING
            val action = TransactionAction.COMMIT

            //when
            val transactionAction = stateMachine.next(cur, action)

            //then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.ANOMALY_DETECTING))
            val command = mock<TransactionCommand>()
            transactionAction.action.accept(command)
            verify(anomalyDetector, times(1)).handle(command)
        }

        @Test
        fun `ANOMALY_DETECTING transit to DONE when COMMIT`() {
            //given
            val cur = TransactionStatus.ANOMALY_DETECTING
            val action = TransactionAction.COMMIT

            //when
            val transactionAction = stateMachine.next(cur, action)

            //then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.DONE))
        }
    }

    @Nested
    inner class CommonStateMachine {
        private val stateMachine =
            OrchestratorConfiguration(
                dataCollector,
                dataReconciler,
                invoiceGenerator,
                anomalyDetector,
                isrGenerator,
                expenseBillCollector,
                expenseBillReconciler,
                expenseBillGenerator,
                distributedLockProvider,
                configurationProperties,
            ).commonStateMachine()

        @Test
        fun `INIT transit to DATA_COLLECTING when COMMIT`() {
            //given
            val cur = TransactionStatus.INIT
            val action = TransactionAction.COMMIT

            //when
            val transactionAction = stateMachine.next(cur, action)

            //then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.DATA_COLLECTING))
            val command = mock<TransactionCommand>()
            transactionAction.action.accept(command)
            verify(dataCollector).handle(command)
        }

        @Test
        fun `DATA_COLLECTING transit to DATA_RECONCILING when COMMIT`() {
            //given
            val cur = TransactionStatus.DATA_COLLECTING
            val action = TransactionAction.COMMIT

            //when
            val transactionAction = stateMachine.next(cur, action)

            //then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.DATA_RECONCILING))
            val command = mock<TransactionCommand>()
            transactionAction.action.accept(command)
            verify(dataReconciler).handle(command)
        }

        @Test
        fun `DATA_RECONCILING transit to INVOICE_GENERATING when COMMIT`() {
            //given
            val cur = TransactionStatus.DATA_RECONCILING
            val action = TransactionAction.COMMIT

            //when
            val transactionAction = stateMachine.next(cur, action)

            //then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.INVOICE_GENERATING))
            val command = mock<TransactionCommand>()
            transactionAction.action.accept(command)
            verify(invoiceGenerator).handle(command)
        }

        @Test
        fun `INVOICE_GENERATING transit to DONE when COMMIT`() {
            //given
            val cur = TransactionStatus.INVOICE_GENERATING
            val action = TransactionAction.COMMIT

            //when
            val transactionAction = stateMachine.next(cur, action)

            //then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.DONE))
            val command = mock<TransactionCommand>()
            transactionAction.action.accept(command)
        }

        @Test
        fun `transit to ERROR when STOP`() {
            //given
            val cur = TransactionStatus.INVOICE_GENERATING
            val action = TransactionAction.STOP

            //when
            val command = mock<TransactionCommand>()
            `when`(command.transactionType).thenReturn(TransactionType.FIRST_INVOICE)
            val transactionAction = stateMachine.next(cur, action)

            //then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.ERROR))
            transactionAction.action.accept(command)
            verify(dataCollector).rollback(command)
            verify(dataReconciler).rollback(command)
            verify(invoiceGenerator).rollback(command)
        }

        @Test
        fun `invalid transition`() {
            //given
            val cur = TransactionStatus.DONE
            val action = TransactionAction.COMMIT

            //then
            assertThrows<InvalidStateTransitionException> { stateMachine.next(cur, action) }
        }
    }

    @Nested
    inner class OrderFormAdvanceStateMachine {
        private val stateMachine =
            OrchestratorConfiguration(
                dataCollector = dataCollector,
                dataReconciler = dataReconciler,
                invoiceGenerator = invoiceGenerator,
                anomalyDetector = anomalyDetector,
                isrGenerator = isrGenerator,
                expenseBillDataCollector = expenseBillCollector,
                expenseBillReconciler = expenseBillReconciler,
                expenseBillGenerator = expenseBillGenerator,
                distributedLockProvider,
                configurationProperties,
            ).orderFormAdvanceStateMachine()

        @Test
        fun `INIT transit to DONE after INVOICE_GENERATING`() {
            // given
            val cur = TransactionStatus.INVOICE_GENERATING
            val action = TransactionAction.COMMIT

            // when
            val transactionAction = stateMachine.next(cur, action)

            // then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.DONE))
        }
    }

    @Nested
    inner class VasBgvInvoiceStateMachine {
        private val stateMachine =
            OrchestratorConfiguration(
                dataCollector = dataCollector,
                dataReconciler = dataReconciler,
                invoiceGenerator = invoiceGenerator,
                anomalyDetector = anomalyDetector,
                isrGenerator = isrGenerator,
                expenseBillDataCollector = expenseBillCollector,
                expenseBillReconciler = expenseBillReconciler,
                expenseBillGenerator = expenseBillGenerator,
                distributedLockProvider = distributedLockProvider,
                configurationProperties = configurationProperties,
            ).vasBgvInvoiceStateMachine()

        @Test
        fun `INVOICE_GENERATING transit to DONE when COMMIT`() {
            // given
            val cur = TransactionStatus.INVOICE_GENERATING
            val action = TransactionAction.COMMIT

            // when
            val transactionAction = stateMachine.next(cur, action)

            // then
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.DONE))
        }
    }

    @Nested
    inner class AorAnnualPlanInvoiceStateMachine {
        private val stateMachine =
            OrchestratorConfiguration(
                dataCollector = dataCollector,
                dataReconciler = dataReconciler,
                invoiceGenerator = invoiceGenerator,
                anomalyDetector = anomalyDetector,
                isrGenerator = isrGenerator,
                expenseBillDataCollector = expenseBillCollector,
                expenseBillReconciler = expenseBillReconciler,
                expenseBillGenerator = expenseBillGenerator,
                distributedLockProvider = distributedLockProvider,
                configurationProperties = mock<InvoiceOrchestratorConfigurationProperties> {
                    on { aorAnnualPlanInvoice }.thenReturn(
                        InvoiceOrchestratorConfigurationProperties.AorAnnualPlanInvoiceConfigurationProperties(
                            leaseTime = Duration.ofMinutes(1),
                        )
                    )
                },
            ).aorAnnualPlanInvoiceStateMachine()

        @Test
        fun `ANOMALY_DETECTING transit to DONE when COMMIT`() {
            //given
            val cur = TransactionStatus.ANOMALY_DETECTING
            val action = TransactionAction.COMMIT

            //when
            val transactionAction = stateMachine.next(cur, action)

            //then
            assertTrue(stateMachine is LockingStateMachine)
            assertThat(transactionAction.targetStatus, equalTo(TransactionStatus.DONE))
        }
    }
}