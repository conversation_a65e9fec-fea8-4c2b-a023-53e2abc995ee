package com.multiplier.payable.bankfee.savedsearch.domain

import com.multiplier.payable.bankfee.testutils.CompanyBankFeeBalanceTestDataFactory
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
@DisplayName("NetsuiteSavedSearchCompanyBankFeeBalanceAggregate Tests")
class NetsuiteSavedSearchCompanyBankFeeBalanceAggregateTest {

    @MockK
    private lateinit var repository: NetsuiteSavedSearchCompanyBankFeeBalanceRepository

    @Nested
    @DisplayName("Aggregate Creation")
    inner class AggregateCreation {

        @Test
        @DisplayName("Should create aggregate with null entity")
        fun shouldCreateAggregateWithNullEntity() {
            // When
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate()

            // Then
            assertThat(aggregate).isNotNull()
        }

        @Test
        @DisplayName("Should create aggregate with existing entity")
        fun shouldCreateAggregateWithExistingEntity() {
            // Given
            val existingEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity()

            // When
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(existingEntity)

            // Then
            assertThat(aggregate).isNotNull()
        }
    }

    @Nested
    @DisplayName("upsert Method")
    inner class UpsertMethod {

        @Test
        @DisplayName("Should create new entity when aggregate has no existing entity")
        fun shouldCreateNewEntityWhenAggregateHasNoExistingEntity() {
            // Given
            val input = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput()
            val createdEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                id = 1L,
                companyId = input.companyId,
                entityId = input.entityId,
                month = input.month,
                year = input.year,
                amount = input.amount
            )
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(null)

            every { repository.create(input) } returns createdEntity

            // When
            val result = aggregate.upsert(input, repository)

            // Then
            assertThat(result).isNotNull()
            assertThat(result.id).isEqualTo(1L)
            assertThat(result.companyId).isEqualTo(input.companyId)
            assertThat(result.entityId).isEqualTo(input.entityId)
            assertThat(result.month).isEqualTo(input.month)
            assertThat(result.year).isEqualTo(input.year)
            assertThat(result.amount).isEqualTo(input.amount)

            verify { repository.create(input) }
            verify(exactly = 0) { repository.update(any()) }
        }

        @Test
        @DisplayName("Should update existing entity when aggregate has existing entity")
        fun shouldUpdateExistingEntityWhenAggregateHasExistingEntity() {
            // Given
            val input = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput(
                amount = CompanyBankFeeBalanceTestDataFactory.createAmountWithEuroCurrency() // Different amount to trigger update
            )
            val existingEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                id = 1L,
                companyId = input.companyId,
                entityId = input.entityId,
                month = input.month,
                year = input.year,
                amount = CompanyBankFeeBalanceTestDataFactory.createAmount() // Default USD amount
            )
            val updatedEntity = existingEntity.copy(amount = input.amount)
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(existingEntity)

            every { repository.update(input) } returns updatedEntity

            // When
            val result = aggregate.upsert(input, repository)

            // Then
            assertThat(result).isNotNull()
            assertThat(result.id).isEqualTo(1L)
            assertThat(result.companyId).isEqualTo(input.companyId)
            assertThat(result.entityId).isEqualTo(input.entityId)
            assertThat(result.month).isEqualTo(input.month)
            assertThat(result.year).isEqualTo(input.year)
            assertThat(result.amount).isEqualTo(input.amount)

            verify { repository.update(input) }
            verify(exactly = 0) { repository.create(any()) }
        }

        @Test
        @DisplayName("Should handle different input scenarios for create")
        fun shouldHandleDifferentInputScenariosForCreate() {
            // Given
            val testCases = listOf(
                CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput(
                    month = 1, year = 2024
                ), // January
                CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput(
                    month = 12, year = 2024
                ), // December
                CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput(
                    amount = CompanyBankFeeBalanceTestDataFactory.createAmountWithZeroValue()
                ), // Zero amount
                CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput(
                    amount = CompanyBankFeeBalanceTestDataFactory.createAmountWithEuroCurrency()
                ) // Different currency
            )

            testCases.forEachIndexed { index, input ->
                val createdEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                    id = (index + 1).toLong(),
                    companyId = input.companyId,
                    entityId = input.entityId,
                    month = input.month,
                    year = input.year,
                    amount = input.amount
                )
                val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(null)

                every { repository.create(input) } returns createdEntity

                // When
                val result = aggregate.upsert(input, repository)

                // Then
                assertThat(result.companyId).isEqualTo(input.companyId)
                assertThat(result.entityId).isEqualTo(input.entityId)
                assertThat(result.month).isEqualTo(input.month)
                assertThat(result.year).isEqualTo(input.year)
                assertThat(result.amount).isEqualTo(input.amount)
            }
        }

        @Test
        @DisplayName("Should handle different input scenarios for update")
        fun shouldHandleDifferentInputScenariosForUpdate() {
            // Given
            val testCases = listOf(
                CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput(
                    amount = CompanyBankFeeBalanceTestDataFactory.createAmountWithLargeValue()
                ), // Large amount
                CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput(
                    amount = CompanyBankFeeBalanceTestDataFactory.createAmountWithSgdCurrency()
                ), // SGD currency
                CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput(
                    companyId = Long.MAX_VALUE - 1, entityId = Long.MAX_VALUE
                ) // Large IDs
            )

            testCases.forEachIndexed { index, input ->
                val existingEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                    id = (index + 1).toLong(),
                    companyId = input.companyId,
                    entityId = input.entityId,
                    month = input.month,
                    year = input.year
                )
                val updatedEntity = existingEntity.copy(amount = input.amount)
                val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(existingEntity)

                every { repository.update(input) } returns updatedEntity

                // When
                val result = aggregate.upsert(input, repository)

                // Then
                assertThat(result.companyId).isEqualTo(input.companyId)
                assertThat(result.entityId).isEqualTo(input.entityId)
                assertThat(result.month).isEqualTo(input.month)
                assertThat(result.year).isEqualTo(input.year)
                assertThat(result.amount).isEqualTo(input.amount)
            }
        }

        @Test
        @DisplayName("Should update internal entity state after upsert")
        fun shouldUpdateInternalEntityStateAfterUpsert() {
            // Given
            val input = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput()
            val createdEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                id = 1L,
                companyId = input.companyId,
                entityId = input.entityId,
                month = input.month,
                year = input.year,
                amount = input.amount
            )
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(null)

            every { repository.create(input) } returns createdEntity

            // When
            val firstResult = aggregate.upsert(input, repository)

            // Now the aggregate should have the entity, so subsequent upserts should update
            val updatedInput = input.copy(amount = CompanyBankFeeBalanceTestDataFactory.createAmountWithEuroCurrency())
            val updatedEntity = createdEntity.copy(amount = updatedInput.amount)

            every { repository.update(updatedInput) } returns updatedEntity

            val secondResult = aggregate.upsert(updatedInput, repository)

            // Then
            assertThat(firstResult.id).isEqualTo(1L)
            assertThat(secondResult.id).isEqualTo(1L)
            assertThat(secondResult.amount).isEqualTo(updatedInput.amount)

            verify { repository.create(input) }
            verify { repository.update(updatedInput) }
        }
    }

    @Nested
    @DisplayName("Error Handling")
    inner class ErrorHandling {

        @Test
        @DisplayName("Should propagate repository exceptions during create")
        fun shouldPropagateRepositoryExceptionsDuringCreate() {
            // Given
            val input = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput()
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(null)
            val expectedException = RuntimeException("Create operation failed")

            every { repository.create(input) } throws expectedException

            // When & Then
            val exception = assertThrows<RuntimeException> {
                aggregate.upsert(input, repository)
            }

            assertThat(exception).isEqualTo(expectedException)
            assertThat(exception.message).isEqualTo("Create operation failed")

            verify { repository.create(input) }
        }

        @Test
        @DisplayName("Should propagate repository exceptions during update")
        fun shouldPropagateRepositoryExceptionsDuringUpdate() {
            // Given
            val input = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput(
                amount = CompanyBankFeeBalanceTestDataFactory.createAmountWithEuroCurrency() // Different amount to trigger update
            )
            val existingEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity()
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(existingEntity)
            val expectedException = RuntimeException("Update operation failed")

            every { repository.update(input) } throws expectedException

            // When & Then
            val exception = assertThrows<RuntimeException> {
                aggregate.upsert(input, repository)
            }

            assertThat(exception).isEqualTo(expectedException)
            assertThat(exception.message).isEqualTo("Update operation failed")

            verify { repository.update(input) }
        }
    }

    @Nested
    @DisplayName("Logging Behavior")
    inner class LoggingBehavior {

        @Test
        @DisplayName("Should log upsert operation")
        fun shouldLogUpsertOperation() {
            // Given
            val input = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput()
            val createdEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity()
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(null)

            every { repository.create(input) } returns createdEntity

            // When
            aggregate.upsert(input, repository)

            // Then
            // Note: Actual logging verification would require a logging framework mock
            // This test documents the expected logging behavior for upsert operation
            verify { repository.create(input) }
        }

        @Test
        @DisplayName("Should log create operation")
        fun shouldLogCreateOperation() {
            // Given
            val input = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput()
            val createdEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity()
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(null)

            every { repository.create(input) } returns createdEntity

            // When
            aggregate.upsert(input, repository)

            // Then
            // Note: Actual logging verification would require a logging framework mock
            // This test documents the expected logging behavior for create operation
            verify { repository.create(input) }
        }

        @Test
        @DisplayName("Should log update operation with entity ID")
        fun shouldLogUpdateOperationWithEntityId() {
            // Given
            val input = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput(
                amount = CompanyBankFeeBalanceTestDataFactory.createAmountWithEuroCurrency() // Different amount to trigger update
            )
            val existingEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(id = 42L)
            val updatedEntity = existingEntity.copy(amount = input.amount)
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(existingEntity)

            every { repository.update(input) } returns updatedEntity

            // When
            aggregate.upsert(input, repository)

            // Then
            // Note: Actual logging verification would require a logging framework mock
            // This test documents the expected logging behavior for update operation with entity ID
            verify { repository.update(input) }
        }
    }

    @Nested
    @DisplayName("Domain Logic")
    inner class DomainLogic {

        @Test
        @DisplayName("Should encapsulate create vs update decision logic")
        fun shouldEncapsulateCreateVsUpdateDecisionLogic() {
            // Given
            val input = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput(
                amount = CompanyBankFeeBalanceTestDataFactory.createAmountWithEuroCurrency() // Different amount to trigger update
            )

            // Test create path
            val createAggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(null)
            val createdEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity()
            every { repository.create(input) } returns createdEntity

            // Test update path
            val existingEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity()
            val updateAggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(existingEntity)
            val updatedEntity = existingEntity.copy(amount = input.amount)
            every { repository.update(input) } returns updatedEntity

            // When
            val createResult = createAggregate.upsert(input, repository)
            val updateResult = updateAggregate.upsert(input, repository)

            // Then
            assertThat(createResult).isNotNull()
            assertThat(updateResult).isNotNull()

            verify { repository.create(input) }
            verify { repository.update(input) }
        }

        @Test
        @DisplayName("Should maintain aggregate state consistency")
        fun shouldMaintainAggregateStateConsistency() {
            // Given
            val input = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput()
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(null)
            val createdEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(id = 1L)

            every { repository.create(input) } returns createdEntity

            // When
            val result = aggregate.upsert(input, repository)

            // Then
            // The aggregate should now have the entity internally
            // Subsequent operations should use update path
            assertThat(result).isEqualTo(createdEntity)

            // Verify that the aggregate's internal state has been updated
            val secondInput = input.copy(amount = CompanyBankFeeBalanceTestDataFactory.createAmountWithEuroCurrency())
            val updatedEntity = createdEntity.copy(amount = secondInput.amount)
            every { repository.update(secondInput) } returns updatedEntity

            val secondResult = aggregate.upsert(secondInput, repository)

            assertThat(secondResult).isEqualTo(updatedEntity)
            verify { repository.create(input) }
            verify { repository.update(secondInput) }
        }

        @Test
        @DisplayName("Should handle null entity gracefully in upsert return")
        fun shouldHandleNullEntityGracefullyInUpsertReturn() {
            // Given
            val input = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput()
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(null)
            val createdEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity()

            every { repository.create(input) } returns createdEntity

            // When
            val result = aggregate.upsert(input, repository)

            // Then
            // The method should return the created entity, not null
            assertThat(result).isNotNull()
            assertThat(result).isEqualTo(createdEntity)

            verify { repository.create(input) }
        }
    }

    @Nested
    @DisplayName("Performance Considerations")
    inner class PerformanceConsiderations {

        @Test
        @DisplayName("Should make single repository call for create scenario")
        fun shouldMakeSingleRepositoryCallForCreateScenario() {
            // Given
            val input = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput()
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(null)
            val createdEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity()

            every { repository.create(input) } returns createdEntity

            // When
            aggregate.upsert(input, repository)

            // Then
            verify(exactly = 1) { repository.create(input) }
            verify(exactly = 0) { repository.update(any()) }
        }

        @Test
        @DisplayName("Should make single repository call for update scenario")
        fun shouldMakeSingleRepositoryCallForUpdateScenario() {
            // Given
            val input = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceInput(
                amount = CompanyBankFeeBalanceTestDataFactory.createAmountWithEuroCurrency() // Different amount to trigger update
            )
            val existingEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity()
            val aggregate = NetsuiteSavedSearchCompanyBankFeeBalanceAggregate(existingEntity)
            val updatedEntity = existingEntity.copy(amount = input.amount)

            every { repository.update(input) } returns updatedEntity

            // When
            aggregate.upsert(input, repository)

            // Then
            verify(exactly = 1) { repository.update(input) }
            verify(exactly = 0) { repository.create(any()) }
        }
    }

}
