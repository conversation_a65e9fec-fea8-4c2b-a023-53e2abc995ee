package com.multiplier.payable.bankfee.savedsearch

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.payable.bankfee.CompanyBankFeeBalance
import com.multiplier.payable.bankfee.savedsearch.application.NetsuiteSavedSearchCompanyBankFeeBalanceReadUseCase
import com.multiplier.payable.bankfee.testutils.CompanyBankFeeBalanceTestDataFactory
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import java.math.BigDecimal

@ExtendWith(MockKExtension::class)
@DisplayName("NetsuiteSavedSearchCompanyBankFeeBalanceAdapter Tests")
class NetsuiteSavedSearchCompanyBankFeeBalanceAdapterTest {

    @MockK
    private lateinit var netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase: NetsuiteSavedSearchCompanyBankFeeBalanceReadUseCase

    @InjectMockKs
    private lateinit var adapter: NetsuiteSavedSearchCompanyBankFeeBalanceAdapter

    @Nested
    @DisplayName("getBankFee Method")
    inner class GetBankFeeMethod {

        @Test
        @DisplayName("Should return CompanyBankFeeBalance when single entity exists")
        fun shouldReturnCompanyBankFeeBalanceWhenSingleEntityExists() {
            // Given
            val companyId = 123L
            val entityId = 456L
            val monthYear = MonthYear(month = 6, year = 2024)
            val savedSearchEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                id = 1L,
                companyId = companyId,
                entityId = entityId,
                month = monthYear.month,
                year = monthYear.year
            )

            every {
                netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase.findByCompanyIdAndEntityIdAndMonthAndYear(
                    companyId = companyId,
                    entityId = entityId,
                    month = monthYear.month,
                    year = monthYear.year
                )
            } returns listOf(savedSearchEntity)

            // When
            val result = adapter.getBankFee(companyId, entityId, monthYear)

            // Then
            assertThat(result).isNotNull()
            assertThat(result.ids).containsExactly(savedSearchEntity.id!!)
            assertThat(result.companyId).isEqualTo(savedSearchEntity.companyId)
            assertThat(result.entityId).isEqualTo(savedSearchEntity.entityId)
            assertThat(result.month).isEqualTo(savedSearchEntity.month)
            assertThat(result.year).isEqualTo(savedSearchEntity.year)
            assertThat(result.amount).isEqualTo(savedSearchEntity.amount)

            verify {
                netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase.findByCompanyIdAndEntityIdAndMonthAndYear(
                    companyId = companyId,
                    entityId = entityId,
                    month = monthYear.month,
                    year = monthYear.year
                )
            }
        }

        @Test
        @DisplayName("Should return zero amount when no entities found")
        fun shouldReturnZeroAmountWhenNoEntitiesFound() {
            // Given
            val companyId = 123L
            val entityId = 456L
            val monthYear = MonthYear(month = 6, year = 2024)

            every {
                netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase.findByCompanyIdAndEntityIdAndMonthAndYear(
                    companyId = companyId,
                    entityId = entityId,
                    month = monthYear.month,
                    year = monthYear.year
                )
            } returns emptyList()

            // When
            val result = adapter.getBankFee(companyId, entityId, monthYear)

            // Then
            assertThat(result).isNotNull()
            assertThat(result.ids).isEmpty()
            assertThat(result.companyId).isEqualTo(companyId)
            assertThat(result.entityId).isEqualTo(entityId)
            assertThat(result.month).isEqualTo(monthYear.month)
            assertThat(result.year).isEqualTo(monthYear.year)
            assertThat(result.amount.value).isEqualTo(BigDecimal.ZERO)
            assertThat(result.amount.currency).isEqualTo(com.multiplier.payable.types.CurrencyCode.USD)

            verify {
                netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase.findByCompanyIdAndEntityIdAndMonthAndYear(
                    companyId = companyId,
                    entityId = entityId,
                    month = monthYear.month,
                    year = monthYear.year
                )
            }
        }

        @Test
        @DisplayName("Should aggregate multiple entities with same currency")
        fun shouldAggregateMultipleEntitiesWithSameCurrency() {
            // Given
            val companyId = 123L
            val entityId = 456L
            val monthYear = MonthYear(month = 6, year = 2024)
            val entities = CompanyBankFeeBalanceTestDataFactory.createMultipleNetsuiteSavedSearchCompanyBankFeeBalanceEntities(
                count = 3,
                companyId = companyId,
                entityId = entityId,
                month = monthYear.month,
                year = monthYear.year,
                currency = com.multiplier.payable.types.CurrencyCode.USD
            )

            every {
                netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase.findByCompanyIdAndEntityIdAndMonthAndYear(
                    companyId = companyId,
                    entityId = entityId,
                    month = monthYear.month,
                    year = monthYear.year
                )
            } returns entities

            // When
            val result = adapter.getBankFee(companyId, entityId, monthYear)

            // Then
            assertThat(result).isNotNull()
            assertThat(result.ids).hasSize(3)
            assertThat(result.ids).containsExactly(1L, 2L, 3L)
            assertThat(result.companyId).isEqualTo(companyId)
            assertThat(result.entityId).isEqualTo(entityId)
            assertThat(result.month).isEqualTo(monthYear.month)
            assertThat(result.year).isEqualTo(monthYear.year)
            // Total amount should be 100 + 200 + 300 = 600
            assertThat(result.amount.value).isEqualTo(BigDecimal("600.00"))
            assertThat(result.amount.currency).isEqualTo(com.multiplier.payable.types.CurrencyCode.USD)
        }

        @Test
        @DisplayName("Should throw exception when entities have different currencies")
        fun shouldThrowExceptionWhenEntitiesHaveDifferentCurrencies() {
            // Given
            val companyId = 123L
            val entityId = 456L
            val monthYear = MonthYear(month = 6, year = 2024)
            val entity1 = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                id = 1L,
                companyId = companyId,
                entityId = entityId,
                month = monthYear.month,
                year = monthYear.year,
                amount = CompanyBankFeeBalanceTestDataFactory.createAmount(currency = com.multiplier.payable.types.CurrencyCode.USD),
                paymentVoucherId = "DOC001"
            )
            val entity2 = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                id = 2L,
                companyId = companyId,
                entityId = entityId,
                month = monthYear.month,
                year = monthYear.year,
                amount = CompanyBankFeeBalanceTestDataFactory.createAmount(currency = com.multiplier.payable.types.CurrencyCode.EUR),
                paymentVoucherId = "DOC002"
            )

            every {
                netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase.findByCompanyIdAndEntityIdAndMonthAndYear(
                    companyId = companyId,
                    entityId = entityId,
                    month = monthYear.month,
                    year = monthYear.year
                )
            } returns listOf(entity1, entity2)

            // When & Then
            val exception = assertThrows<MplBusinessException> {
                adapter.getBankFee(companyId, entityId, monthYear)
            }

            assertThat(exception.message).contains("Currency mismatch detected")
        }

        @Test
        @DisplayName("Should handle different month and year combinations")
        fun shouldHandleDifferentMonthAndYearCombinations() {
            // Given
            val companyId = 123L
            val entityId = 456L
            val testCases = listOf(
                MonthYear(1, 2024),    // January
                MonthYear(12, 2024),   // December
                MonthYear(6, 2020),    // Past year
                MonthYear(6, 2030)     // Future year
            )

            testCases.forEach { monthYear ->
                val savedSearchEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                    companyId = companyId,
                    entityId = entityId,
                    month = monthYear.month,
                    year = monthYear.year
                )

                every {
                    netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase.findByCompanyIdAndEntityIdAndMonthAndYear(
                        companyId = companyId,
                        entityId = entityId,
                        month = monthYear.month,
                        year = monthYear.year
                    )
                } returns listOf(savedSearchEntity)

                // When
                val result = adapter.getBankFee(companyId, entityId, monthYear)

                // Then
                assertThat(result.month).isEqualTo(monthYear.month)
                assertThat(result.year).isEqualTo(monthYear.year)
            }
        }

        @Test
        @DisplayName("Should handle different company and entity IDs")
        fun shouldHandleDifferentCompanyAndEntityIds() {
            // Given
            val testCases = listOf(
                Pair(123L, 456L),
                Pair(999L, 888L),
                Pair(1L, 1L),
                Pair(Long.MAX_VALUE, Long.MAX_VALUE - 1)
            )
            val monthYear = MonthYear(6, 2024)

            testCases.forEach { (companyId, entityId) ->
                val savedSearchEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                    companyId = companyId,
                    entityId = entityId,
                    month = monthYear.month,
                    year = monthYear.year
                )

                every {
                    netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase.findByCompanyIdAndEntityIdAndMonthAndYear(
                        companyId = companyId,
                        entityId = entityId,
                        month = monthYear.month,
                        year = monthYear.year
                    )
                } returns listOf(savedSearchEntity)

                // When
                val result = adapter.getBankFee(companyId, entityId, monthYear)

                // Then
                assertThat(result.companyId).isEqualTo(companyId)
                assertThat(result.entityId).isEqualTo(entityId)
            }
        }
    }

    @Nested
    @DisplayName("Error Handling")
    inner class ErrorHandling {

        @Test
        @DisplayName("Should filter out entities with null IDs during aggregation")
        fun shouldFilterOutEntitiesWithNullIdsDuringAggregation() {
            // Given
            val companyId = 123L
            val entityId = 456L
            val monthYear = MonthYear(6, 2024)
            val validEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                id = 1L,
                companyId = companyId,
                entityId = entityId,
                month = monthYear.month,
                year = monthYear.year
            )
            val entityWithNullId = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                id = null, // Null ID
                companyId = companyId,
                entityId = entityId,
                month = monthYear.month,
                year = monthYear.year
            )

            every {
                netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase.findByCompanyIdAndEntityIdAndMonthAndYear(
                    companyId = companyId,
                    entityId = entityId,
                    month = monthYear.month,
                    year = monthYear.year
                )
            } returns listOf(validEntity, entityWithNullId)

            // When
            val result = adapter.getBankFee(companyId, entityId, monthYear)

            // Then
            assertThat(result.ids).containsExactly(1L) // Only valid ID included
            // Amount should be aggregated from both entities (100.00 + 100.00 = 200.00)
            assertThat(result.amount.value).isEqualTo(BigDecimal("200.00"))
            assertThat(result.amount.currency).isEqualTo(validEntity.amount.currency)
        }

        @Test
        @DisplayName("Should propagate exceptions from use case")
        fun shouldPropagateExceptionsFromUseCase() {
            // Given
            val companyId = 123L
            val entityId = 456L
            val monthYear = MonthYear(6, 2024)
            val expectedException = RuntimeException("Database connection failed")

            every {
                netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase.findByCompanyIdAndEntityIdAndMonthAndYear(
                    companyId = companyId,
                    entityId = entityId,
                    month = monthYear.month,
                    year = monthYear.year
                )
            } throws expectedException

            // When & Then
            val exception = assertThrows<RuntimeException> {
                adapter.getBankFee(companyId, entityId, monthYear)
            }

            assertThat(exception).isEqualTo(expectedException)
            assertThat(exception.message).isEqualTo("Database connection failed")
        }
    }

    @Nested
    @DisplayName("Data Mapping")
    inner class DataMapping {

        @Test
        @DisplayName("Should correctly map all fields from entity to domain model")
        fun shouldCorrectlyMapAllFieldsFromEntityToDomainModel() {
            // Given
            val companyId = 123L
            val entityId = 456L
            val monthYear = MonthYear(6, 2024)
            val amount = CompanyBankFeeBalanceTestDataFactory.createAmountWithEuroCurrency()
            val savedSearchEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                id = 42L,
                companyId = companyId,
                entityId = entityId,
                month = monthYear.month,
                year = monthYear.year,
                amount = amount
            )

            every {
                netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase.findByCompanyIdAndEntityIdAndMonthAndYear(
                    companyId = companyId,
                    entityId = entityId,
                    month = monthYear.month,
                    year = monthYear.year
                )
            } returns listOf(savedSearchEntity)

            // When
            val result = adapter.getBankFee(companyId, entityId, monthYear)

            // Then
            assertThat(result).isInstanceOf(CompanyBankFeeBalance::class.java)
            assertThat(result.ids).containsExactly(42L)
            assertThat(result.companyId).isEqualTo(companyId)
            assertThat(result.entityId).isEqualTo(entityId)
            assertThat(result.month).isEqualTo(monthYear.month)
            assertThat(result.year).isEqualTo(monthYear.year)
            assertThat(result.amount).isEqualTo(amount)
            assertThat(result.amount.currency).isEqualTo(amount.currency)
            assertThat(result.amount.value).isEqualTo(amount.value)
        }

        @Test
        @DisplayName("Should preserve Amount object integrity during mapping")
        fun shouldPreserveAmountObjectIntegrityDuringMapping() {
            // Given
            val companyId = 123L
            val entityId = 456L
            val monthYear = MonthYear(6, 2024)
            val originalAmount = CompanyBankFeeBalanceTestDataFactory.createAmountWithLargeValue()
            val savedSearchEntity = CompanyBankFeeBalanceTestDataFactory.createNetsuiteSavedSearchCompanyBankFeeBalanceEntity(
                companyId = companyId,
                entityId = entityId,
                month = monthYear.month,
                year = monthYear.year,
                amount = originalAmount
            )

            every {
                netsuiteSavedSearchCompanyBankFeeBalanceReadUseCase.findByCompanyIdAndEntityIdAndMonthAndYear(
                    companyId = companyId,
                    entityId = entityId,
                    month = monthYear.month,
                    year = monthYear.year
                )
            } returns listOf(savedSearchEntity)

            // When
            val result = adapter.getBankFee(companyId, entityId, monthYear)

            // Then
            assertThat(result.amount).isEqualTo(originalAmount)
            assertThat(result.amount.value).isEqualTo(originalAmount.value)
            assertThat(result.amount.currency).isEqualTo(originalAmount.currency)
            assertThat(result.amount.isZero()).isEqualTo(originalAmount.isZero())
        }
    }
}
