package com.multiplier.payable.report

import com.multiplier.core.payable.adapters.api.InvoiceGenerationAdapter
import com.multiplier.core.payable.adapters.api.InvoiceGenerationFactory
import com.multiplier.core.payable.creditnote.facade.UpdateCreditNoteRequest
import com.multiplier.core.payable.event.database.RecordType
import com.multiplier.core.payable.report.InvoiceSourceReportFinancialTransactionUpdater
import com.multiplier.core.payable.report.dataholder.IsrDataHolder
import com.multiplier.core.payable.service.dataholder.ISRFileMetaData
import com.multiplier.core.payable.service.secondinvoice.NegativeInvoiceCreditNoteService
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.doReturn
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class InvoiceSourceReportFinancialTransactionUpdaterTest {

    @Mock
    private lateinit var invoiceGenerationFactory: InvoiceGenerationFactory
    @Mock
    private lateinit var negativeInvoiceCreditNoteService: NegativeInvoiceCreditNoteService
    @InjectMocks
    private lateinit var invoiceSourceReportFinancialTransactionUpdater: InvoiceSourceReportFinancialTransactionUpdater


    @Nested
    inner class UpdateFinancialTransaction {

        @Test
        fun `given record type as invoice when update financial transaction then add ISR link to invoice`() {
            // given
            val isrFileMetadata = ISRFileMetaData.builder()
                .sourceReportLink("sourceReportLink")
                .build()
            val isrDataHolder = IsrDataHolder.builder()
                .recordType(RecordType.INVOICE)
                .externalId("externalId")
                .isrFileMetaData(isrFileMetadata)
                .build()

            val invoiceGenerationAdapter = mock(InvoiceGenerationAdapter::class.java)

            doReturn(invoiceGenerationAdapter).`when`(invoiceGenerationFactory).adapter

            // when
            invoiceSourceReportFinancialTransactionUpdater.updateFinancialTransaction(isrDataHolder)

            // then
            verify(invoiceGenerationAdapter).addInvoiceSourceReportLink(
                isrDataHolder.externalId,
                isrFileMetadata.sourceReportLink
            )
        }

        @Test
        fun `given record type as credit note, when update financial transaction then update credit note with ISR link`() {
            // given
            val isrFileMetadata = ISRFileMetaData.builder()
                .sourceReportLink("sourceReportLink")
                .build()
            val isrDataHolder = IsrDataHolder.builder()
                .recordType(RecordType.CREDIT_NOTE)
                .externalId("externalId")
                .customerId("customerId")
                .isrFileMetaData(isrFileMetadata)
                .build()

            val updateCreditNoteRequest = UpdateCreditNoteRequest.builder()
                .externalId(isrDataHolder.externalId)
                .invoiceSourceReportLink(isrFileMetadata.sourceReportLink)
                .customerId(isrDataHolder.customerId)
                .build()

            // when
            invoiceSourceReportFinancialTransactionUpdater.updateFinancialTransaction(isrDataHolder)

            // then
            verify(negativeInvoiceCreditNoteService).update(
                isrDataHolder.externalId,
                updateCreditNoteRequest
            )
        }
    }

}