package com.multiplier.payable.creditnote.facade

import com.multiplier.core.payable.creditnote.database.CreditNoteDto
import com.multiplier.payable.types.CreditNote
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class CreditNoteDtoToGraphMapperTest {

    @InjectMocks
    private lateinit var mapper: CreditNoteDtoToGraphMapper

    @Test
    fun givenDto_whenMap_returnGraph() {
        // GIVEN
        val dto = getDto()

        // WHEN
        val graph = mapper.map(dto)

        // THEN
        assertFields(graph, dto)
    }

    private fun getDto(): CreditNoteDto {
        return CreditNoteDto.builder()
            .creditNoteNo("awesomeCreditNoteNo")
            .reference("awesomeReference")
            .companyPayableId(42L)
            .build()
    }

    private fun assertFields(
        graph: CreditNote,
        dto: CreditNoteDto,
    ) {
        assertThat(graph.creditNoteNo).isEqualTo(dto.creditNoteNo)
        assertThat(graph.reference).isEqualTo(dto.reference)
        assertThat(graph.companyPayableId).isEqualTo(dto.companyPayableId)
    }

    @Test
    fun givenDtos_whenMap_returnGraphs() {
        // GIVEN
        val dto = getDto()
        val dtos = listOf(dto)

        // WHEN
        val graphs = mapper.map(dtos)

        // THEN
        assertFields(graphs[0], dtos[0])
    }
}