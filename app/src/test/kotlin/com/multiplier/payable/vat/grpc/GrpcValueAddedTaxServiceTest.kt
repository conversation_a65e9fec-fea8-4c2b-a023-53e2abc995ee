package com.multiplier.payable.vat.grpc

import com.google.protobuf.Int64Value
import com.multiplier.grpc.common.country.v2.Country
import com.multiplier.payable.grpc.schema.GetCompanyCountryValueAddedTaxRequest
import com.multiplier.payable.grpc.schema.GetCompanyCountryValueAddedTaxResponse
import com.multiplier.payable.grpc.schema.GrpcCompanyCountryValueAddedTaxRequest
import com.multiplier.payable.grpc.schema.GrpcCountryValueAddedTaxRequest
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.vat.ValueAddedTax
import com.multiplier.payable.vat.ValueAddedTaxRateType
import com.multiplier.payable.vat.application.CountryValueAddedTaxReadUseCase
import com.multiplier.payable.vat.company.application.CompanyCountryValueAddedTaxReadUseCase
import com.multiplier.payable.vat.exception.UnsupportedValueAddedTaxRateTypeException
import io.grpc.StatusRuntimeException
import io.grpc.stub.StreamObserver
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentCaptor
import org.mockito.Captor
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any

@ExtendWith(MockitoExtension::class)
class GrpcValueAddedTaxServiceTest {

    @Mock
    private lateinit var countryValueAddedTaxReadUseCase: CountryValueAddedTaxReadUseCase

    @Mock
    private lateinit var companyCountryValueAddedTaxReadUseCase: CompanyCountryValueAddedTaxReadUseCase

    @Mock
    private lateinit var responseObserver: StreamObserver<GetCompanyCountryValueAddedTaxResponse>

    @Captor
    private lateinit var responseCaptor: ArgumentCaptor<GetCompanyCountryValueAddedTaxResponse>

    @Mock
    private lateinit var grpcValueAddedTaxMapper: GrpcValueAddedTaxMapper
    private lateinit var service: GrpcValueAddedTaxService

    @Mock
    private lateinit var validator: GrpcValueAddedTaxValidator

    @Mock
    private lateinit var errorHandler: GrpcValueAddedTaxErrorHandler

    @Mock
    private lateinit var responseBuilder: GrpcValueAddedTaxResponseBuilder

    @Captor
    private lateinit var errorCaptor: ArgumentCaptor<StatusRuntimeException>

    @BeforeEach
    fun setUp() {
        service = GrpcValueAddedTaxService(
            countryValueAddedTaxReadUseCase,
            companyCountryValueAddedTaxReadUseCase,
            grpcValueAddedTaxMapper,
            validator,
            errorHandler,
            responseBuilder
        )
    }

    @Test
    fun `test getValueAddedTax for country only - success`() {
        // Given
        val countryCode = CountryCode.valueOf("DEU")
        val grpcCountryCode = Country.CountryCode.COUNTRY_CODE_DEU
        val valueAddedTax = ValueAddedTax(19.0, ValueAddedTaxRateType.PERCENTAGE)

        val request = GetCompanyCountryValueAddedTaxRequest.newBuilder()
            .setCountryValueAddedTax(
                GrpcCountryValueAddedTaxRequest.newBuilder()
                    .setCountryCode(grpcCountryCode)
                    .build()
            )
            .build()

        `when`(validator.validateCountryVatRequest(request, responseObserver)).thenReturn(true)
        `when`(grpcValueAddedTaxMapper.mapGrpcToCountryCode(grpcCountryCode)).thenReturn(countryCode)
        `when`(countryValueAddedTaxReadUseCase.read(countryCode)).thenReturn(valueAddedTax)

        // When
        service.getValueAddedTax(request, responseObserver)

        // Then
        verify(responseBuilder).sendCountryVatResponse(countryCode, valueAddedTax, responseObserver)
    }

    @Test
    fun `test getValueAddedTax for country only - not found`() {
        // Given
        val countryCode = CountryCode.valueOf("USA")
        val grpcCountryCode = Country.CountryCode.COUNTRY_CODE_USA

        val request = GetCompanyCountryValueAddedTaxRequest.newBuilder()
            .setCountryValueAddedTax(
                GrpcCountryValueAddedTaxRequest.newBuilder()
                    .setCountryCode(grpcCountryCode)
                    .build()
            )
            .build()

        `when`(validator.validateCountryVatRequest(request, responseObserver)).thenReturn(true)
        `when`(grpcValueAddedTaxMapper.mapGrpcToCountryCode(grpcCountryCode)).thenReturn(countryCode)
        `when`(countryValueAddedTaxReadUseCase.read(countryCode)).thenReturn(null)

        // When
        service.getValueAddedTax(request, responseObserver)

        // Then
        verify(errorHandler).sendCountryVatNotFoundError(countryCode, responseObserver)
    }

    @Test
    fun `test getValueAddedTax for company and country - success with company VAT`() {
        // Given
        val countryCode = CountryCode.valueOf("FRA")
        val grpcCountryCode = Country.CountryCode.COUNTRY_CODE_FRA
        val companyId = 100L
        val valueAddedTax = ValueAddedTax(20.0, ValueAddedTaxRateType.PERCENTAGE)

        val request = GetCompanyCountryValueAddedTaxRequest.newBuilder()
            .setCompanyCountryValueAddedTax(
                GrpcCompanyCountryValueAddedTaxRequest.newBuilder()
                    .setCountryCode(grpcCountryCode)
                    .setCompanyId(Int64Value.of(companyId))
                    .build()
            )
            .build()

        `when`(validator.validateCompanyCountryVatRequest(request, responseObserver)).thenReturn(true)
        `when`(grpcValueAddedTaxMapper.mapGrpcToCountryCode(grpcCountryCode)).thenReturn(countryCode)
        `when`(companyCountryValueAddedTaxReadUseCase.handleCommand(any())).thenReturn(valueAddedTax)

        // When
        service.getValueAddedTax(request, responseObserver)

        // Then
        verify(responseBuilder).sendCompanyVatResponse(countryCode, companyId, valueAddedTax, responseObserver)
    }

    @Test
    fun `test getValueAddedTax for company and country - not found without fallback`() {
        // Given
        val countryCode = CountryCode.valueOf("ITA")
        val grpcCountryCode = Country.CountryCode.COUNTRY_CODE_ITA
        val companyId = 200L

        val request = GetCompanyCountryValueAddedTaxRequest.newBuilder()
            .setCompanyCountryValueAddedTax(
                GrpcCompanyCountryValueAddedTaxRequest.newBuilder()
                    .setCountryCode(grpcCountryCode)
                    .setCompanyId(Int64Value.of(companyId))
                    .build()
            )
            .build()

        `when`(validator.validateCompanyCountryVatRequest(request, responseObserver)).thenReturn(true)
        `when`(grpcValueAddedTaxMapper.mapGrpcToCountryCode(grpcCountryCode)).thenReturn(countryCode)
        `when`(companyCountryValueAddedTaxReadUseCase.handleCommand(any())).thenReturn(null)

        // When
        service.getValueAddedTax(request, responseObserver)

        // Then
        verify(errorHandler).sendCompanyCountryVatNotFoundError(companyId, countryCode, responseObserver)
    }

    @Test
    fun `test getValueAddedTax for company and country - not found with different country`() {
        // Given
        val countryCode = CountryCode.valueOf("JPN")
        val grpcCountryCode = Country.CountryCode.COUNTRY_CODE_JPN
        val companyId = 300L

        val request = GetCompanyCountryValueAddedTaxRequest.newBuilder()
            .setCompanyCountryValueAddedTax(
                GrpcCompanyCountryValueAddedTaxRequest.newBuilder()
                    .setCountryCode(grpcCountryCode)
                    .setCompanyId(Int64Value.of(companyId))
                    .build()
            )
            .build()

        `when`(validator.validateCompanyCountryVatRequest(request, responseObserver)).thenReturn(true)
        `when`(grpcValueAddedTaxMapper.mapGrpcToCountryCode(grpcCountryCode)).thenReturn(countryCode)
        `when`(companyCountryValueAddedTaxReadUseCase.handleCommand(any())).thenReturn(null)

        // When
        service.getValueAddedTax(request, responseObserver)

        // Then
        verify(errorHandler).sendCompanyCountryVatNotFoundError(companyId, countryCode, responseObserver)
    }

    @Test
    fun `test getValueAddedTax with invalid request type`() {
        // Given
        val request = GetCompanyCountryValueAddedTaxRequest.newBuilder().build()

        // When
        service.getValueAddedTax(request, responseObserver)

        // Then
        verify(errorHandler).handleInvalidRequestType(request, responseObserver)
    }

    @Test
    fun `test getValueAddedTax for company and country - missing company ID`() {
        // Given
        val request = GetCompanyCountryValueAddedTaxRequest.newBuilder()
            .setCompanyCountryValueAddedTax(
                GrpcCompanyCountryValueAddedTaxRequest.newBuilder()
                    .setCountryCode(Country.CountryCode.COUNTRY_CODE_ESP)
                    // Intentionally not setting companyId
                    .build()
            )
            .build()

        `when`(validator.validateCompanyCountryVatRequest(request, responseObserver)).thenReturn(false)

        // When
        service.getValueAddedTax(request, responseObserver)

        // Then
        // No further interactions should happen after validation fails
        verify(validator).validateCompanyCountryVatRequest(request, responseObserver)
    }

    @Test
    fun `test getValueAddedTax for country - missing country code`() {
        // Given
        val request = GetCompanyCountryValueAddedTaxRequest.newBuilder()
            .setCountryValueAddedTax(
                GrpcCountryValueAddedTaxRequest.newBuilder()
                    // Intentionally not setting countryCode
                    .build()
            )
            .build()

        `when`(validator.validateCountryVatRequest(request, responseObserver)).thenReturn(false)

        // When
        service.getValueAddedTax(request, responseObserver)

        // Then
        // No further interactions should happen after validation fails
        verify(validator).validateCountryVatRequest(request, responseObserver)
    }

    @Test
    fun `test getValueAddedTax for company and country - missing country code`() {
        // Given
        val request = GetCompanyCountryValueAddedTaxRequest.newBuilder()
            .setCompanyCountryValueAddedTax(
                GrpcCompanyCountryValueAddedTaxRequest.newBuilder()
                    // Intentionally not setting countryCode
                    .setCompanyId(Int64Value.of(123L))
                    .build()
            )
            .build()

        `when`(validator.validateCompanyCountryVatRequest(request, responseObserver)).thenReturn(false)

        // When
        service.getValueAddedTax(request, responseObserver)

        // Then
        // No further interactions should happen after validation fails
        verify(validator).validateCompanyCountryVatRequest(request, responseObserver)
    }
    @Test
    fun `test getValueAddedTax for country - mapper throws exception`() {
        // Given
        val countryCode = CountryCode.valueOf("DEU")
        val grpcCountryCode = Country.CountryCode.COUNTRY_CODE_DEU
        val valueAddedTax = ValueAddedTax(19.0, ValueAddedTaxRateType.ABSOLUTE) // Using ABSOLUTE to trigger exception
        val exception = UnsupportedValueAddedTaxRateTypeException()

        val request = GetCompanyCountryValueAddedTaxRequest.newBuilder()
            .setCountryValueAddedTax(
                GrpcCountryValueAddedTaxRequest.newBuilder()
                    .setCountryCode(grpcCountryCode)
                    .build()
            )
            .build()

        `when`(validator.validateCountryVatRequest(request, responseObserver)).thenReturn(true)
        `when`(grpcValueAddedTaxMapper.mapGrpcToCountryCode(grpcCountryCode)).thenReturn(countryCode)
        `when`(countryValueAddedTaxReadUseCase.read(countryCode)).thenReturn(valueAddedTax)
        `when`(responseBuilder.sendCountryVatResponse(countryCode, valueAddedTax, responseObserver)).thenThrow(exception)

        // When
        service.getValueAddedTax(request, responseObserver)

        // Then
        verify(errorHandler).handleException(exception, responseObserver)
    }

    @Test
    fun `test getValueAddedTax for country - unsupported country code`() {
        // Given
        val grpcCountryCode = Country.CountryCode.COUNTRY_CODE_DEU
        val exception = IllegalArgumentException("Unsupported country code: DEU")

        val request = GetCompanyCountryValueAddedTaxRequest.newBuilder()
            .setCountryValueAddedTax(
                GrpcCountryValueAddedTaxRequest.newBuilder()
                    .setCountryCode(grpcCountryCode)
                    .build()
            )
            .build()

        `when`(validator.validateCountryVatRequest(request, responseObserver)).thenReturn(true)
        `when`(grpcValueAddedTaxMapper.mapGrpcToCountryCode(grpcCountryCode)).thenThrow(exception)

        // When
        service.getValueAddedTax(request, responseObserver)

        // Then
        verify(errorHandler).handleException(exception, responseObserver)
    }

    @Test
    fun `test getValueAddedTax - use case throws exception`() {
        // Given
        val countryCode = CountryCode.valueOf("DEU")
        val grpcCountryCode = Country.CountryCode.COUNTRY_CODE_DEU
        val exception = RuntimeException("Database connection error")

        val request = GetCompanyCountryValueAddedTaxRequest.newBuilder()
            .setCountryValueAddedTax(
                GrpcCountryValueAddedTaxRequest.newBuilder()
                    .setCountryCode(grpcCountryCode)
                    .build()
            )
            .build()

        `when`(validator.validateCountryVatRequest(request, responseObserver)).thenReturn(true)
        `when`(grpcValueAddedTaxMapper.mapGrpcToCountryCode(grpcCountryCode)).thenReturn(countryCode)
        `when`(countryValueAddedTaxReadUseCase.read(countryCode)).thenThrow(exception)

        // When
        service.getValueAddedTax(request, responseObserver)

        // Then
        verify(errorHandler).handleException(exception, responseObserver)
    }
}