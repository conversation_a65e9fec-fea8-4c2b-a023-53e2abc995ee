package com.multiplier.core.currency.mapper

import com.multiplier.core.currency.data.CompanySlabCustomisation
import com.multiplier.core.currency.data.Customisation
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.core.schema.currency.CurrencyV2
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers
import org.mockito.Mockito

class CurrencyConversionCustomisationMapperTest {

    @Test
    fun `should call stub with correct request`() {
        val companySlab = CompanySlabCustomisation(100L)
        val customisedRequest = CurrencyConversionCustomisationMapper.map(companySlab,CurrencyV2.CustomisationInstruction.newBuilder())
        assertTrue(customisedRequest.hasCompanySlabBasedCustomisation())
    }

}