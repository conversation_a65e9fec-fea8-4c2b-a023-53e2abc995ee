package com.multiplier.core.payable.invoice.mapper

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.common.exception.toBusinessException
import com.multiplier.core.exception.PayableErrorCode
import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.adapters.api.LineItemDTO
import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.adapters.netsuite.client.NetsuiteMappings
import com.multiplier.core.payable.adapters.netsuite.mapper.NetsuiteWsInvoiceMapper
import com.multiplier.core.payable.company.adapter.CompanyServiceAdapter
import com.multiplier.core.payable.invoice.provider.AdjustmentTaxCodeProvider
import com.multiplier.core.payable.invoice.provider.TaxCodeQuery
import com.multiplier.payable.types.CountryCode
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.RecordRef
import com.netsuite.suitetalk.proxy.v2023_1.transactions.sales.Invoice
import com.netsuite.suitetalk.proxy.v2023_1.transactions.sales.InvoiceItem
import com.netsuite.suitetalk.proxy.v2023_1.transactions.sales.InvoiceItemList
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.assertThrows
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockedStatic
import org.mockito.Mockito.mockStatic
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@ExtendWith(MockitoExtension::class)
class AdjustmentAwareNetsuiteInvoiceMapperTest {

    @Mock
    private lateinit var netsuiteWsInvoiceMapper: NetsuiteWsInvoiceMapper

    @Mock
    private lateinit var companyServiceAdapter: CompanyServiceAdapter

    @Mock
    private lateinit var adjustmentTaxCodeProvider: AdjustmentTaxCodeProvider

    @InjectMocks
    private lateinit var mapper: AdjustmentAwareNetsuiteInvoiceMapper

    private lateinit var netsuiteMappingsMock: MockedStatic<NetsuiteMappings>

    @BeforeEach
    fun setUp() {
        netsuiteMappingsMock = mockStatic(NetsuiteMappings::class.java)
        // Mock the static methods with default values
        netsuiteMappingsMock.`when`<String> { NetsuiteMappings.getSubsidiaryId(any()) }.thenReturn("1")
        netsuiteMappingsMock.`when`<String> { NetsuiteMappings.getTaxCodeId(any()) }.thenReturn("1")
    }

    @AfterEach
    fun tearDown() {
        netsuiteMappingsMock.close()
    }

    @Test
    fun `map should use legacy mapper when no line items`() {
        // GIVEN
        val invoiceDTO = createInvoiceDTO(lineItems = null)
        val expectedInvoice = createMockInvoice()

        whenever(netsuiteWsInvoiceMapper.map(invoiceDTO))
            .thenReturn(expectedInvoice)

        // WHEN
        val result = mapper.map(invoiceDTO)

        // THEN
        assertEquals(expectedInvoice, result)
    }

    @Test
    fun `map should use legacy mapper when line items are empty`() {
        // GIVEN
        val invoiceDTO = createInvoiceDTO(lineItems = emptyList())
        val expectedInvoice = createMockInvoice()

        whenever(netsuiteWsInvoiceMapper.map(invoiceDTO))
            .thenReturn(expectedInvoice)

        // WHEN
        val result = mapper.map(invoiceDTO)

        // THEN
        assertEquals(expectedInvoice, result)
    }

    @Test
    fun `map should use legacy mapper when no adjustment line items found`() {
        // GIVEN
        val nonAdjustmentLineItem = createLineItemDTO(LineItemType.GROSS_SALARY)
        val invoiceDTO = createInvoiceDTO(lineItems = listOf(nonAdjustmentLineItem))
        val expectedInvoice = createMockInvoice()

        whenever(netsuiteWsInvoiceMapper.map(invoiceDTO))
            .thenReturn(expectedInvoice)

        // WHEN
        val result = mapper.map(invoiceDTO)

        // THEN
        assertEquals(expectedInvoice, result)
    }

    @Test
    fun `map should throw exception when line item has null itemType`() {
        // GIVEN
        val lineItemWithNullType = createLineItemDTO(null)
        val invoiceDTO = createInvoiceDTO(lineItems = listOf(lineItemWithNullType))

        // WHEN & THEN
        assertThrows<IllegalArgumentException> {
            mapper.map(invoiceDTO)
        }
    }

    @Test
    fun `map should process adjustment line items with tax code mapping`() {
        // GIVEN
        val adjustmentLineItem = createLineItemDTO(
            itemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR,
            matchingAdvanceCollectionLineTaxCode = "GST_SG:SR-SG",
            taxType = "DEFAULT_TAX"
        )
        val nonAdjustmentLineItem = createLineItemDTO(LineItemType.GROSS_SALARY)
        val invoiceDTO = createInvoiceDTO(
            lineItems = listOf(adjustmentLineItem, nonAdjustmentLineItem),
            subsidiaryName = "Multiplier Pte Ltd"
        )

        val legacyInvoice = createMockInvoice()
        val adjustmentInvoiceItem = createMockInvoiceItem("DEFAULT_TAX")
        val nonAdjustmentInvoiceItem = createMockInvoiceItem("ORIGINAL_TAX")

        whenever(netsuiteWsInvoiceMapper.map(invoiceDTO))
            .thenReturn(legacyInvoice)
        whenever(netsuiteWsInvoiceMapper.map(eq(adjustmentLineItem), eq(invoiceDTO)))
            .thenReturn(adjustmentInvoiceItem)
        whenever(netsuiteWsInvoiceMapper.map(eq(nonAdjustmentLineItem), eq(invoiceDTO)))
            .thenReturn(nonAdjustmentInvoiceItem)
        whenever(companyServiceAdapter.getCompanyPrimaryEntityCountryCodeOrThrow(123L))
            .thenReturn(CountryCode.SGP)
        whenever(adjustmentTaxCodeProvider.getOrDefault(any<TaxCodeQuery>(), eq("DEFAULT_TAX")))
            .thenReturn("GST_SG:SR-SG")

        // WHEN
        val result = mapper.map(invoiceDTO)

        // THEN
        assertNotNull(result)
        assertNotNull(result.itemList)
        assertEquals(2, result.itemList.item.size)

        // Verify the adjustment item has the correct tax code
        val adjustmentItem = result.itemList.item.find { it.taxCode?.internalId == "1" }
        assertNotNull(adjustmentItem)

        verify(companyServiceAdapter).getCompanyPrimaryEntityCountryCodeOrThrow(123L)
        verify(adjustmentTaxCodeProvider).getOrDefault(any<TaxCodeQuery>(), eq("DEFAULT_TAX"))
    }

    @Test
    fun `map should handle mixed adjustment and non-adjustment line items`() {
        // GIVEN
        val adjustmentLineItem1 = createLineItemDTO(
            itemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_GLOBAL_PAYROLL,
            matchingAdvanceCollectionLineTaxCode = "GST_SG:ZR-SG",
            taxType = "TAX_1"
        )
        val adjustmentLineItem2 = createLineItemDTO(
            itemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_AOR,
            matchingAdvanceCollectionLineTaxCode = "GST_SG:SR-SG",
            taxType = "TAX_2"
        )
        val nonAdjustmentLineItem = createLineItemDTO(LineItemType.MANAGEMENT_FEE_EOR)

        val invoiceDTO = createInvoiceDTO(
            lineItems = listOf(adjustmentLineItem1, nonAdjustmentLineItem, adjustmentLineItem2),
            subsidiaryName = "Multiplier Pte Ltd"
        )

        val legacyInvoice = createMockInvoice()
        val adjustmentItem1 = createMockInvoiceItem("TAX_1")
        val adjustmentItem2 = createMockInvoiceItem("TAX_2")
        val nonAdjustmentItem = createMockInvoiceItem("ORIGINAL_TAX")

        whenever(netsuiteWsInvoiceMapper.map(invoiceDTO))
            .thenReturn(legacyInvoice)
        whenever(netsuiteWsInvoiceMapper.map(eq(adjustmentLineItem1), eq(invoiceDTO)))
            .thenReturn(adjustmentItem1)
        whenever(netsuiteWsInvoiceMapper.map(eq(adjustmentLineItem2), eq(invoiceDTO)))
            .thenReturn(adjustmentItem2)
        whenever(netsuiteWsInvoiceMapper.map(eq(nonAdjustmentLineItem), eq(invoiceDTO)))
            .thenReturn(nonAdjustmentItem)
        whenever(companyServiceAdapter.getCompanyPrimaryEntityCountryCodeOrThrow(123L))
            .thenReturn(CountryCode.SGP)
        whenever(adjustmentTaxCodeProvider.getOrDefault(any<TaxCodeQuery>(), eq("TAX_1")))
            .thenReturn("ADJUSTED_TAX_1")
        whenever(adjustmentTaxCodeProvider.getOrDefault(any<TaxCodeQuery>(), eq("TAX_2")))
            .thenReturn("ADJUSTED_TAX_2")

        // WHEN
        val result = mapper.map(invoiceDTO)

        // THEN
        assertNotNull(result)
        assertNotNull(result.itemList)
        assertEquals(3, result.itemList.item.size)

        verify(companyServiceAdapter).getCompanyPrimaryEntityCountryCodeOrThrow(123L)
        verify(adjustmentTaxCodeProvider).getOrDefault(any<TaxCodeQuery>(), eq("TAX_1"))
        verify(adjustmentTaxCodeProvider).getOrDefault(any<TaxCodeQuery>(), eq("TAX_2"))
    }



    @Test
    fun `map should handle all adjustment line item types`() {
        // GIVEN
        val adjustmentLineItemTypes = LineItemType.getAdvanceCollectionAdjustmentLineItemTypes()
        val adjustmentLineItems = adjustmentLineItemTypes.map { lineItemType ->
            createLineItemDTO(
                itemType = lineItemType,
                matchingAdvanceCollectionLineTaxCode = "GST_SG:SR-SG",
                taxType = "DEFAULT_TAX"
            )
        }

        val invoiceDTO = createInvoiceDTO(
            lineItems = adjustmentLineItems.toList(),
            subsidiaryName = "Multiplier Pte Ltd"
        )

        val legacyInvoice = createMockInvoice()

        // Mock all the line item mappings
        adjustmentLineItems.forEach { lineItem ->
            whenever(netsuiteWsInvoiceMapper.map(eq(lineItem), eq(invoiceDTO)))
                .thenReturn(createMockInvoiceItem("DEFAULT_TAX"))
        }

        whenever(netsuiteWsInvoiceMapper.map(invoiceDTO))
            .thenReturn(legacyInvoice)
        whenever(companyServiceAdapter.getCompanyPrimaryEntityCountryCodeOrThrow(123L))
            .thenReturn(CountryCode.SGP)
        whenever(adjustmentTaxCodeProvider.getOrDefault(any<TaxCodeQuery>(), eq("DEFAULT_TAX")))
            .thenReturn("GST_SG:SR-SG")

        // WHEN
        val result = mapper.map(invoiceDTO)

        // THEN
        assertNotNull(result)
        assertNotNull(result.itemList)
        assertEquals(adjustmentLineItemTypes.size, result.itemList.item.size)

        verify(companyServiceAdapter).getCompanyPrimaryEntityCountryCodeOrThrow(123L)
    }

    @Test
    fun `map should handle different subsidiary names`() {
        // GIVEN
        val adjustmentLineItem = createLineItemDTO(
            itemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR,
            matchingAdvanceCollectionLineTaxCode = "GST_SG:SR-SG",
            taxType = "DEFAULT_TAX"
        )
        val invoiceDTO = createInvoiceDTO(
            lineItems = listOf(adjustmentLineItem),
            subsidiaryName = "Different Subsidiary"
        )

        val legacyInvoice = createMockInvoice()
        val adjustmentInvoiceItem = createMockInvoiceItem("DEFAULT_TAX")

        whenever(netsuiteWsInvoiceMapper.map(invoiceDTO))
            .thenReturn(legacyInvoice)
        whenever(netsuiteWsInvoiceMapper.map(eq(adjustmentLineItem), eq(invoiceDTO)))
            .thenReturn(adjustmentInvoiceItem)
        whenever(companyServiceAdapter.getCompanyPrimaryEntityCountryCodeOrThrow(123L))
            .thenReturn(CountryCode.USA)
        whenever(adjustmentTaxCodeProvider.getOrDefault(any<TaxCodeQuery>(), eq("DEFAULT_TAX")))
            .thenReturn("US_TAX_CODE")

        // WHEN
        val result = mapper.map(invoiceDTO)

        // THEN
        assertNotNull(result)
        verify(companyServiceAdapter).getCompanyPrimaryEntityCountryCodeOrThrow(123L)
        verify(adjustmentTaxCodeProvider).getOrDefault(any<TaxCodeQuery>(), eq("DEFAULT_TAX"))
    }

    @Test
    fun `map should handle different country codes`() {
        // GIVEN
        val adjustmentLineItem = createLineItemDTO(
            itemType = LineItemType.ORDER_FORM_ADVANCE_ADJUSTMENT_EOR,
            matchingAdvanceCollectionLineTaxCode = "GST_SG:SR-SG",
            taxType = "DEFAULT_TAX"
        )
        val invoiceDTO = createInvoiceDTO(lineItems = listOf(adjustmentLineItem))

        val legacyInvoice = createMockInvoice()
        val adjustmentInvoiceItem = createMockInvoiceItem("DEFAULT_TAX")

        whenever(netsuiteWsInvoiceMapper.map(invoiceDTO))
            .thenReturn(legacyInvoice)
        whenever(netsuiteWsInvoiceMapper.map(eq(adjustmentLineItem), eq(invoiceDTO)))
            .thenReturn(adjustmentInvoiceItem)
        whenever(companyServiceAdapter.getCompanyPrimaryEntityCountryCodeOrThrow(123L))
            .thenReturn(CountryCode.AUS)
        whenever(adjustmentTaxCodeProvider.getOrDefault(any<TaxCodeQuery>(), eq("DEFAULT_TAX")))
            .thenReturn("AUS_TAX_CODE")

        // WHEN
        val result = mapper.map(invoiceDTO)

        // THEN
        assertNotNull(result)

        // Verify the TaxCodeQuery was created with correct parameters
        verify(adjustmentTaxCodeProvider).getOrDefault(
            eq(TaxCodeQuery(CountryCode.AUS, "1", "GST_SG:SR-SG")),
            eq("DEFAULT_TAX")
        )
    }

    private fun createInvoiceDTO(
        companyId: Long = 123L,
        customerId: String = "456",
        lineItems: List<LineItemDTO>? = null,
        subsidiaryName: String = "Multiplier Pte Ltd"
    ): InvoiceDTO {
        return InvoiceDTO.builder()
            .id(789L)
            .companyId(companyId)
            .customerId(customerId)
            .lineItems(lineItems)
            .subsidiaryName(subsidiaryName)
            .build()
    }

    private fun createLineItemDTO(
        itemType: LineItemType?,
        matchingAdvanceCollectionLineTaxCode: String? = null,
        taxType: String? = "DEFAULT_TAX"
    ): LineItemDTO {
        return LineItemDTO.builder()
            .itemType(itemType)
            .matchingAdvanceCollectionLineTaxCode(matchingAdvanceCollectionLineTaxCode)
            .taxType(taxType)
            .build()
    }

    private fun createMockInvoice(): Invoice {
        val invoice = Invoice()
        invoice.itemList = InvoiceItemList(arrayOf(), false)
        return invoice
    }

    private fun createMockInvoiceItem(taxCodeId: String = "DEFAULT_TAX"): InvoiceItem {
        val item = InvoiceItem()
        item.taxCode = RecordRef(null, taxCodeId, null, null)
        return item
    }
}
