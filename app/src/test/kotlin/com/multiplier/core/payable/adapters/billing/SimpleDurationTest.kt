package com.multiplier.core.payable.adapters.billing

import com.multiplier.payable.engine.domain.aggregates.Duration
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.LocalDate

class SimpleDurationTest {
    @Test
    fun `test Duration class`() {
        // Given
        val today = LocalDate.now()
        val tomorrow = today.plusDays(1)
        
        // When
        val duration = Duration(today, tomorrow)
        
        // Then
        assertEquals(today, duration.startDate)
        assertEquals(tomorrow, duration.endDate)
    }
}
