package com.multiplier.core.payable.adapters

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.core.payable.adapters.deposit.toGrpc
import com.multiplier.deposit.grpc.schema.Deposit.*
import com.multiplier.deposit.grpc.schema.DepositServiceGrpc
import com.multiplier.grpc.common.currency.v2.Currency
import com.multiplier.payable.engine.common.TimeUnit
import com.multiplier.payable.engine.contract.Contract
import com.multiplier.payable.engine.contract.ContractFromGrpcMapper
import com.multiplier.payable.engine.deposit.AdditionalPayFixedPolicyFeed
import com.multiplier.payable.engine.deposit.DepositPolicyFeed
import com.multiplier.payable.engine.deposit.DepositType
import com.multiplier.payable.engine.deposit.LeaveEntitlementSurplusPolicyFeed
import com.multiplier.payable.engine.deposit.SalaryPeriodPolicyFeed
import com.multiplier.deposit.grpc.schema.Deposit.SalaryPeriodPolicyFeed as GrpcSalaryPeriodPolicyFeed
import com.multiplier.deposit.grpc.schema.Deposit.LeaveEntitlementSurplusPolicyFeed as GrpcLeaveEntitlementSurplusPolicyFeed
import com.multiplier.deposit.grpc.schema.Deposit.AdditionalPayFixedPolicyFeed as GrpcAdditionalPayFixedPolicyFeed
import com.multiplier.deposit.grpc.schema.Deposit.Amount as GrpcAmount
import com.multiplier.deposit.grpc.schema.Deposit.PayComponent as GrpcPayComponent
import com.multiplier.deposit.grpc.schema.Deposit.TimePeriod as GrpcTimePeriod
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.any
import org.mockito.Mockito.mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DepositServiceAdapterTest {
    @Mock
    private lateinit var depositServiceClient: DepositServiceGrpc.DepositServiceBlockingStub

    @Mock
    private lateinit var contractServiceAdapter: ContractServiceAdapter

    @Mock
    private lateinit var contractFromGrpcMapper: ContractFromGrpcMapper

    @InjectMocks
    private lateinit var depositServiceAdapter: DepositServiceAdapterImpl

    @Test
    fun `given with depositId then return deposit batch`() {
        // given
        val depositId = 1L
        val employmentDeposit = mock(GrpcEmploymentDeposit::class.java)
        val deposit = mock(GrpcDeposit::class.java)
        val policyAmount = mock(GrpcAmount::class.java)
        whenever(policyAmount.value).thenReturn(1.0)
        whenever(policyAmount.currency).thenReturn(Currency.CurrencyCode.CURRENCY_CODE_USD)

        val policy = mock(DepositPolicy::class.java)
        whenever(policy.type).thenReturn(DepositPolicyType.SALARY_PERIOD)
        whenever(policy.amount).thenReturn(policyAmount)

        val grpcContract = mock(ContractOuterClass.Contract::class.java)
        val contract = mock(Contract::class.java)

        val getDepositResponse = mock(GrpcGetDepositResponse::class.java)
        whenever(getDepositResponse.depositCase).thenReturn(GrpcGetDepositResponse.DepositCase.EMPLOYMENTDEPOSIT)
        whenever(getDepositResponse.employmentDeposit).thenReturn(employmentDeposit)

        whenever(employmentDeposit.deposit).thenReturn(deposit)
        whenever(employmentDeposit.contractId).thenReturn(2L)
        whenever(deposit.policiesList).thenReturn(listOf(policy))
        whenever(depositServiceClient.getDeposit(any())).thenReturn(getDepositResponse)
        whenever(contractServiceAdapter.getContractById(2L)).thenReturn(grpcContract)
        whenever(grpcContract.type).thenReturn(ContractOuterClass.ContractType.EMPLOYEE)
        whenever(contractFromGrpcMapper.map(grpcContract)).thenReturn(contract)

        // when
        val result = depositServiceAdapter.getDepositBatchByDepositId(depositId)

        // then
        assertThat(result).isNotNull
    }

    @Test
    fun `given with depositId then return null deposit batch`() {
        // given
        val depositId = 1L
        val getDepositResponse = mock(GrpcGetDepositResponse::class.java)
        whenever(getDepositResponse.depositCase).thenReturn(GrpcGetDepositResponse.DepositCase.DEPOSIT_NOT_SET)

        whenever(depositServiceClient.getDeposit(any())).thenReturn(getDepositResponse)
        // when
        val result = depositServiceAdapter.getDepositBatchByDepositId(depositId)

        // then
        assertThat(result).isNull()
    }

    @Test
    fun `given with depositId then return employment deposit`() {
        // given
        val depositId = 1L
        val contractId = 2L
        val companyId = 3L
        val employmentDeposit = mock(GrpcEmploymentDeposit::class.java)
        val deposit = mock(GrpcDeposit::class.java)

        val grpcGetDeposit = mock(GrpcGetDepositResponse::class.java)
        whenever(grpcGetDeposit.depositCase).thenReturn(GrpcGetDepositResponse.DepositCase.EMPLOYMENTDEPOSIT)
        whenever(grpcGetDeposit.employmentDeposit).thenReturn(employmentDeposit)

        whenever(depositServiceClient.getDeposit(any())).thenReturn(grpcGetDeposit)
        whenever(employmentDeposit.deposit).thenReturn(deposit)
        whenever(employmentDeposit.contractId).thenReturn(contractId)
        whenever(deposit.companyId).thenReturn(companyId)

        // when
        val result = depositServiceAdapter.getEmploymentDeposit(depositId)

        // then
        val expected = EmploymentDeposit(contractId, companyId)
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `given with depositId and unknown deposit case then throw IllegalArgumentException`() {
        // given
        val depositId = 1L
        val grpcGetDeposit = mock(GrpcGetDepositResponse::class.java)
        whenever(grpcGetDeposit.depositCase).thenReturn(GrpcGetDepositResponse.DepositCase.DEPOSIT_NOT_SET)

        whenever(depositServiceClient.getDeposit(any())).thenReturn(grpcGetDeposit)

        // when & then
        assertThrows<IllegalArgumentException> {
            depositServiceAdapter.getEmploymentDeposit(depositId)
        }
    }

    @Test
    fun `test getDepositFeed with SALARY type properly mocks GRPC response`() {
        // given
        val depositId = 1L
        val type = DepositType.SALARY
        val normalizingUnit = TimeUnit.MONTHS

        // Create mock GRPC response objects
        val grossSalaryAmount = mock(GrpcAmount::class.java)
        whenever(grossSalaryAmount.value).thenReturn(1000.0)
        whenever(grossSalaryAmount.currency).thenReturn(Currency.CurrencyCode.CURRENCY_CODE_USD)

        val grossSalary = mock(GrpcPayComponent::class.java)
        whenever(grossSalary.amount).thenReturn(grossSalaryAmount)

        val contributionAmount = mock(GrpcAmount::class.java)
        whenever(contributionAmount.value).thenReturn(200.0)
        whenever(contributionAmount.currency).thenReturn(Currency.CurrencyCode.CURRENCY_CODE_USD)

        val contribution = mock(GrpcPayComponent::class.java)
        whenever(contribution.amount).thenReturn(contributionAmount)

        val noticePeriod = mock(GrpcTimePeriod::class.java)
        whenever(noticePeriod.value).thenReturn(2.0)
        whenever(noticePeriod.timeUnit).thenReturn(TimeUnit.MONTHS.toGrpc())

        val salaryPeriodPolicyFeed = mock(GrpcSalaryPeriodPolicyFeed::class.java)
        whenever(salaryPeriodPolicyFeed.grossSalary).thenReturn(grossSalary)
        whenever(salaryPeriodPolicyFeed.contribution).thenReturn(contribution)
        whenever(salaryPeriodPolicyFeed.noticePeriod).thenReturn(noticePeriod)

        val policyAmount = mock(GrpcAmount::class.java)
        whenever(policyAmount.value).thenReturn(1000.0)
        whenever(policyAmount.currency).thenReturn(Currency.CurrencyCode.CURRENCY_CODE_USD)

        val policyPeriod = mock(GrpcTimePeriod::class.java)
        whenever(policyPeriod.value).thenReturn(2.0)
        whenever(policyPeriod.timeUnit).thenReturn(TimeUnit.MONTHS.toGrpc())

        val policy = mock(DepositPolicy::class.java)
        whenever(policy.type).thenReturn(DepositPolicyType.SALARY_PERIOD)
        whenever(policy.amount).thenReturn(policyAmount)
        whenever(policy.period).thenReturn(policyPeriod)
        whenever(policy.salaryPeriodPolicyFeed).thenReturn(salaryPeriodPolicyFeed)

        val response = mock(GetDepositPolicyResponse::class.java)
        whenever(response.policy).thenReturn(policy)

        // Mock the GRPC client to return our response
        whenever(depositServiceClient.getDepositPolicy(any())).thenReturn(response)

        // when
        val result = depositServiceAdapter.getDepositFeed(depositId, type, normalizingUnit)

        // then
        assertThat(result).isInstanceOf(SalaryPeriodPolicyFeed::class.java)
        val salaryFeed = result as SalaryPeriodPolicyFeed
        assertThat(salaryFeed.grossAmount.value.toDouble()).isEqualTo(1000.0)
        assertThat(salaryFeed.contributionAmount.value.toDouble()).isEqualTo(200.0)
        assertThat(salaryFeed.period.value).isEqualTo(2.0)
        assertThat(salaryFeed.period.unit).isEqualTo(TimeUnit.MONTHS)
    }

    @Test
    fun `test getDepositFeed with LEAVE type properly mocks GRPC response`() {
        // given
        val depositId = 1L
        val type = DepositType.LEAVE
        val normalizingUnit = TimeUnit.DAYS

        // Create mock GRPC response objects
        val countryLeave = mock(GrpcTimePeriod::class.java)
        whenever(countryLeave.value).thenReturn(20.0)
        whenever(countryLeave.timeUnit).thenReturn(TimeUnit.DAYS.toGrpc())

        val contractLeave = mock(GrpcTimePeriod::class.java)
        whenever(contractLeave.value).thenReturn(25.0)
        whenever(contractLeave.timeUnit).thenReturn(TimeUnit.DAYS.toGrpc())

        val surplusLeave = mock(GrpcTimePeriod::class.java)
        whenever(surplusLeave.value).thenReturn(5.0)
        whenever(surplusLeave.timeUnit).thenReturn(TimeUnit.DAYS.toGrpc())

        val leaveEntitlementSurplusPolicyFeed = mock(GrpcLeaveEntitlementSurplusPolicyFeed::class.java)
        whenever(leaveEntitlementSurplusPolicyFeed.countryLeave).thenReturn(countryLeave)
        whenever(leaveEntitlementSurplusPolicyFeed.contractLeave).thenReturn(contractLeave)
        whenever(leaveEntitlementSurplusPolicyFeed.surplusLeave).thenReturn(surplusLeave)

        val policyAmount = mock(GrpcAmount::class.java)
        whenever(policyAmount.value).thenReturn(500.0)
        whenever(policyAmount.currency).thenReturn(Currency.CurrencyCode.CURRENCY_CODE_USD)

        val policyPeriod = mock(GrpcTimePeriod::class.java)
        whenever(policyPeriod.value).thenReturn(5.0)
        whenever(policyPeriod.timeUnit).thenReturn(TimeUnit.DAYS.toGrpc())

        val policy = mock(DepositPolicy::class.java)
        whenever(policy.type).thenReturn(DepositPolicyType.COUNTRY_LEAVE_ENTITLEMENT_SURPLUS)
        whenever(policy.amount).thenReturn(policyAmount)
        whenever(policy.period).thenReturn(policyPeriod)
        whenever(policy.leaveEntitlementSurplusPolicyFeed).thenReturn(leaveEntitlementSurplusPolicyFeed)

        val response = mock(GetDepositPolicyResponse::class.java)
        whenever(response.policy).thenReturn(policy)

        // Mock the GRPC client to return our response
        whenever(depositServiceClient.getDepositPolicy(any())).thenReturn(response)

        // when
        val result = depositServiceAdapter.getDepositFeed(depositId, type, normalizingUnit)

        // then
        assertThat(result).isInstanceOf(LeaveEntitlementSurplusPolicyFeed::class.java)
        val leaveFeed = result as LeaveEntitlementSurplusPolicyFeed
        assertThat(leaveFeed.countryLeaves.value).isEqualTo(20.0)
        assertThat(leaveFeed.countryLeaves.unit).isEqualTo(TimeUnit.DAYS)
        assertThat(leaveFeed.contractLeaves.value).isEqualTo(25.0)
        assertThat(leaveFeed.contractLeaves.unit).isEqualTo(TimeUnit.DAYS)
        assertThat(leaveFeed.surplusLeaves.value).isEqualTo(5.0)
        assertThat(leaveFeed.surplusLeaves.unit).isEqualTo(TimeUnit.DAYS)
        assertThat(leaveFeed.dailyRate).isEqualTo(100.0) // 500 / 5 = 100
    }

    @Test
    fun `test getDepositFeed with FIXED_PAY type properly mocks GRPC response`() {
        // given
        val depositId = 1L
        val type = DepositType.FIXED_PAY
        val normalizingUnit = TimeUnit.MONTHS

        // Create mock GRPC response objects
        val additionalFixPay = mock(com.multiplier.deposit.grpc.schema.Deposit.AdditionalPayFixedComponent::class.java)
        whenever(additionalFixPay.name).thenReturn("Bonus")

        val additionalPayFixedPolicyFeed = mock(GrpcAdditionalPayFixedPolicyFeed::class.java)
        whenever(additionalPayFixedPolicyFeed.additionalFixPaysList).thenReturn(listOf(additionalFixPay))

        val policyAmount = mock(GrpcAmount::class.java)
        whenever(policyAmount.value).thenReturn(300.0)
        whenever(policyAmount.currency).thenReturn(Currency.CurrencyCode.CURRENCY_CODE_USD)

        val policyPeriod = mock(GrpcTimePeriod::class.java)
        whenever(policyPeriod.value).thenReturn(1.0)
        whenever(policyPeriod.timeUnit).thenReturn(TimeUnit.MONTHS.toGrpc())

        val policy = mock(DepositPolicy::class.java)
        whenever(policy.type).thenReturn(DepositPolicyType.ADDITIONAL_PAY_FIXED)
        whenever(policy.amount).thenReturn(policyAmount)
        whenever(policy.period).thenReturn(policyPeriod)
        whenever(policy.additionalPayFixedPolicyFeed).thenReturn(additionalPayFixedPolicyFeed)

        val response = mock(GetDepositPolicyResponse::class.java)
        whenever(response.policy).thenReturn(policy)

        // Mock the GRPC client to return our response
        whenever(depositServiceClient.getDepositPolicy(any())).thenReturn(response)

        // when
        val result = depositServiceAdapter.getDepositFeed(depositId, type, normalizingUnit)

        // then
        assertThat(result).isInstanceOf(AdditionalPayFixedPolicyFeed::class.java)
        val fixedPayFeed = result as AdditionalPayFixedPolicyFeed
        assertThat(fixedPayFeed.fixedPayNames).containsExactly("Bonus")
    }

    @Test
    fun `test getDepositFeed with unsupported type throws error`() {
        // given
        val depositId = 1L
        val type = DepositType.SALARY
        val normalizingUnit = TimeUnit.MONTHS

        // Create mock GRPC response with unsupported type
        val policy = mock(DepositPolicy::class.java)
        whenever(policy.type).thenReturn(DepositPolicyType.UNRECOGNIZED) // Unsupported type

        val response = mock(GetDepositPolicyResponse::class.java)
        whenever(response.policy).thenReturn(policy)

        // Mock the GRPC client to return our response
        whenever(depositServiceClient.getDepositPolicy(any())).thenReturn(response)

        // when & then
        assertThrows<IllegalStateException> {
            depositServiceAdapter.getDepositFeed(depositId, type, normalizingUnit)
        }
    }


}
