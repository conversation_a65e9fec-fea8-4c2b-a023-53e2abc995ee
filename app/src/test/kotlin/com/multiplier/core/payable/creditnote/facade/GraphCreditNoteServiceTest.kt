package com.multiplier.core.payable.creditnote.facade

import com.multiplier.core.payable.creditnote.database.CreditNoteDto
import com.multiplier.core.payable.creditnote.database.CreditNoteService
import com.multiplier.payable.creditnote.facade.CreditNoteDtoToGraphMapper
import com.multiplier.payable.types.CreditNote
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class GraphCreditNoteServiceTest {

    @Mock
    private lateinit var creditNoteService: CreditNoteService

    @Mock
    private lateinit var creditNoteDtoToGraphMapper: CreditNoteDtoToGraphMapper

    @InjectMocks
    private lateinit var graphCreditNoteService: GraphCreditNoteService

    @Test
    fun givenCompanyPayableIds_whenGet_thenReturnCreditNotesByCompanyPayableId() {

        // GIVEN
        val companyPayableId = 42L
        val companyPayableIds = listOf(companyPayableId)
        val creditNoteDto = CreditNoteDto.builder()
            .companyPayableId(companyPayableId)
            .build()
        val creditNoteDtos = listOf(creditNoteDto)
        whenever(creditNoteService.getByCompanyPayableIds(companyPayableIds))
            .thenReturn(creditNoteDtos)

        val mappedCreditNote = CreditNote.newBuilder()
            .companyPayableId(creditNoteDto.companyPayableId)
            .build()
        val creditNotes = listOf(mappedCreditNote)
        whenever(creditNoteDtoToGraphMapper.map(creditNoteDtos))
            .thenReturn(creditNotes)

        // WHEN
        val creditNotesByCompanyPayableId = graphCreditNoteService.get(companyPayableIds)

        // THEN
        assertThat(creditNotesByCompanyPayableId).containsKey(companyPayableId)
        val creditNote = creditNotesByCompanyPayableId[companyPayableId]
        assertThat(creditNote).isEqualTo(mappedCreditNote)
    }
}