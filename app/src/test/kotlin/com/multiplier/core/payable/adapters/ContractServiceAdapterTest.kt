package com.multiplier.core.payable.adapters

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractServiceGrpc
import com.multiplier.payable.engine.collector.deposit.GrpcDepositToEngineDepositMapper
import com.multiplier.payable.engine.contract.Contract
import com.multiplier.payable.engine.contract.ContractFromGrpcMapper
import com.multiplier.payable.engine.contract.ContractStatus
import com.multiplier.payable.engine.contract.ContractTerm
import com.multiplier.payable.engine.contract.ContractType
import com.multiplier.payable.engine.contract.CountryWorkStatus
import com.multiplier.payable.engine.currency.CurrencyCode
import com.multiplier.payable.types.ContractFilters
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.kotlin.whenever

class ContractServiceAdapterTest {
    private val stub: ContractServiceGrpc.ContractServiceBlockingStub = mock()

    private val contractFromGrpcMapper: ContractFromGrpcMapper = mock()
    private val depositMapper: GrpcDepositToEngineDepositMapper = mock()

    private val adapter = DefaultContractServiceAdapterImpl(contractFromGrpcMapper, depositMapper)

    @BeforeEach
    fun setUp() {
        adapter.stub = stub
    }

    @Nested
    inner class GetContractByIdsAnyStatus {
        // verify that the stub is called with the correct request
        @Test
        fun `should call stub with correct request`() {
            // given
            val contractIds = listOf(1L, 2L, 3L)

            // when (call the method and return empty list)
            `when`(stub.getContractsByIdsAnyStatus(any())).thenReturn(
                ContractOuterClass.ListOfContractsResponse.newBuilder()
                    .build(),
            )

            adapter.getContractsByIdsAnyStatus(contractIds)

            // then
            verify(stub).getContractsByIdsAnyStatus(any())
        }
    }

    @Nested
    inner class GetContractComplianceById {
        // verify that the stub is called with the correct request
        @Test
        fun `should call stub with correct request`() {
            // given
            val contractId = 1L

            // when
            `when`(stub.getContractCompliance(any())).thenReturn(ContractOuterClass.Compliance.newBuilder().build())

            adapter.getComplianceByContractId(contractId)

            // then
            verify(stub).getContractCompliance(any())
        }
    }

    @Nested
    inner class GetIsContractSignedByMember {
        // verify that the stub is called with the correct request
        @Test
        fun `should call stub with correct request`() {
            // given
            val contractId = 1L

            // when
            `when`(stub.getIsContractSignedByMember(any()))
                .thenReturn(ContractOuterClass.IsContractSignedByMemberResponse.newBuilder().build())

            adapter.getIsContractSignedByMember(contractId)

            // then
            verify(stub).getIsContractSignedByMember(any())
        }
    }

    @Test
    fun givenCompanyId_whenGetActiveContractsForCompanyMappedToInternal_thenReturnContracts() {
        // GIVEN
        val companyId = 42L

        val request = ContractOuterClass.CompanyIdRequest.newBuilder()
            .setId(companyId)
            .build()

        val grpcContract = ContractOuterClass.Contract.newBuilder()
            .build()
        val response = ContractOuterClass.ListOfContractsResponse.newBuilder()
            .addAllContracts(listOf(grpcContract))
            .build()
        whenever(stub.getContractsForCompany(request))
            .thenReturn(response)

        val contract = Contract(
            id = 42L,
            companyId = 42L,
            memberId = 42L,
            country = "USA",
            type = ContractType.EMPLOYEE,
            status = ContractStatus.ONBOARDING,
            term = ContractTerm.FIXED,
            workStatus = CountryWorkStatus.RESIDENT,
            currencyCode = CurrencyCode.USD
        )
        whenever(contractFromGrpcMapper.map(grpcContract))
            .thenReturn(contract)
        val expectedContracts = listOf(contract)

        // WHEN
        val contracts = adapter.getActiveContractsForCompanyMappedToInternal(companyId)

        // THEN
        assertThat(contracts).isEqualTo(expectedContracts)
    }

    @Test
    fun givenContractId_whenGetContractById_thenReturnGrpcContract() {
        // GIVEN
        val contractId = 42L

        val request = ContractOuterClass.GetContractByIdRequest.newBuilder()
            .setId(contractId)
            .build()
        val retrievedGrpcContract = ContractOuterClass.Contract.newBuilder()
            .build()
        whenever(stub.getContractById(request))
            .thenReturn(retrievedGrpcContract)

        // WHEN
        val grpcContract = adapter.getContractById(contractId)

        // THEN
        assertThat(grpcContract).isEqualTo(retrievedGrpcContract)
    }

    @Nested
    inner class GetContractsForInvoiceMonthTests {
        @Test
        fun `should return empty list when no contracts found`() {
            val companyId = 1L
            val month = 5
            val year = 2021
            `when`(stub.getContractsForInvoiceMonth(org.mockito.kotlin.any())).thenReturn(ContractOuterClass.ListOfContractsResponse.newBuilder().build())

            val result = adapter.getContractsForInvoiceMonth(companyId, month, year)

            assertTrue(result.isEmpty())
        }

        @Test
        fun `should return list of contracts when contracts are found`() {
            val companyId = 1L
            val month = 5
            val year = 2021
            val expectedContracts = listOf(
                ContractOuterClass.Contract.newBuilder().setId(1L).build(),
                ContractOuterClass.Contract.newBuilder().setId(2L).build()
            )
            `when`(stub.getContractsForInvoiceMonth(org.mockito.kotlin.any())).thenReturn(
                ContractOuterClass.ListOfContractsResponse.newBuilder().addAllContracts(expectedContracts).build()
            )

            val result = adapter.getContractsForInvoiceMonth(companyId, month, year)

            assertEquals(expectedContracts.size, result.size)
            assertTrue(result.containsAll(expectedContracts))
        }

        @Test
        fun `should return list of contracts with forced ids`() {
            val companyId = 1L
            val month = 5
            val year = 2021
            val expectedNormalContracts = ContractOuterClass.Contract.newBuilder().setId(1L).build()
            val forcedContract = ContractOuterClass.Contract.newBuilder().setId(2L).build()
            val expectedResult = listOf(expectedNormalContracts, forcedContract)
            `when`(stub.findContractsEligibleForInvoiceGenerationForGivenCompanyAndMonth(org.mockito.kotlin.any())).thenReturn(
                ContractOuterClass.ListOfContractsResponse.newBuilder()
                    .addContracts(expectedNormalContracts)
                    .build()
            )
            `when`(stub.getContractsByIdsAnyStatus(org.mockito.kotlin.any())).thenReturn(
                ContractOuterClass.ListOfContractsResponse.newBuilder()
                    .addContracts(forcedContract)
                    .build()
            )

            val result = adapter.findContractsEligibleForInvoiceGenerationForGivenCompanyAndMonth(
                companyId, month, year, setOf(2L))

            assertEquals(expectedResult.size, result.size)
            assertTrue(result.contains(expectedNormalContracts))
            assertTrue(result.contains(forcedContract))
        }
    }

    @Nested
    inner class GetContractByIdTests {
        @Test
        fun `should return contract when contract exists`() {
            val contractId = 1L
            val expectedContract = ContractOuterClass.Contract.newBuilder().setId(contractId).build()
            `when`(stub.getContractById(org.mockito.kotlin.any())).thenReturn(expectedContract)

            val result = adapter.getContractById(contractId)

            assertEquals(expectedContract, result)
        }

        @Test
        fun `should throw exception when contract does not exist`() {
            val contractId = 999L
            `when`(stub.getContractById(org.mockito.kotlin.any())).thenThrow(RuntimeException("Contract not found"))

            val exception = org.junit.jupiter.api.assertThrows<RuntimeException> {
                adapter.getContractById(contractId)
            }

            assertTrue(exception.message!!.contains("Contract not found"))
        }
    }

    @Nested
    inner class GetFilteredContractsForInvoiceMonth {
        @Test
        fun `should return empty list when no contracts found`() {
            val companyId = 1L
            val month = 5
            val year = 2021
            `when`(stub.getFilteredContractsForInvoiceMonth(
                any(ContractOuterClass.GetFilteredContractsForInvoiceMonthInput::class.java))
            ).thenReturn(ContractOuterClass.ListOfContractsResponse.newBuilder().build())

            val cFilter = ContractFilters.newBuilder().contractStatuses(listOf(
                com.multiplier.payable.types.ContractStatus.ONBOARDING
            )).build()
            val result = adapter.getFilteredContractsForInvoiceMonth(companyId, month, year, cFilter)

            assertTrue(result.isEmpty())
        }

        @Test
        fun `should return list of contracts when contracts are found`() {
            val companyId = 1L
            val month = 5
            val year = 2021
            val expectedContracts = listOf(
                ContractOuterClass.Contract.newBuilder().setId(1L).build(),
                ContractOuterClass.Contract.newBuilder().setId(2L).build()
            )
            `when`(stub.getFilteredContractsForInvoiceMonth(any(ContractOuterClass.GetFilteredContractsForInvoiceMonthInput::class.java))).thenReturn(
                ContractOuterClass.ListOfContractsResponse.newBuilder().addAllContracts(expectedContracts).build()
            )

            val cFilter = ContractFilters.newBuilder().contractStatuses(listOf(
                com.multiplier.payable.types.ContractStatus.ONBOARDING
            )).build()
            val result = adapter.getFilteredContractsForInvoiceMonth(companyId, month, year, cFilter)

            assertEquals(expectedContracts.size, result.size)
            assertTrue(result.containsAll(expectedContracts))
        }
    }

    @Nested
    inner class GetContractsByCompanyIdsAndContractStatuses {
        @Test
        fun `should return empty list when no contracts found`() {
            `when`(stub.getContractsByCompanyIdsAndStatuses(
                any(ContractOuterClass.GetContractsByCompanyIdsAndStatusesRequest::class.java))
            ).thenReturn(ContractOuterClass.ListOfContractsResponse.newBuilder().build())

            val result = adapter.getContractsByCompanyIdsAndStatuses(listOf(1L), ContractStatus.ENDED)

            assertTrue(result.isEmpty())
        }

        @Test
        fun `should return list of contracts when contracts are found`() {
            val grpcContract = ContractOuterClass.Contract.newBuilder().setId(1L).build()
            val contract = Contract(
                id = 42L,
                companyId = 42L,
                memberId = 42L,
                country = "USA",
                type = ContractType.EMPLOYEE,
                status = ContractStatus.ONBOARDING,
                term = ContractTerm.FIXED,
                workStatus = CountryWorkStatus.RESIDENT,
                currencyCode = CurrencyCode.USD
            )

            `when`(stub.getContractsByCompanyIdsAndStatuses(
                any(ContractOuterClass.GetContractsByCompanyIdsAndStatusesRequest::class.java))
            ).thenReturn(ContractOuterClass.ListOfContractsResponse.newBuilder().addContracts(grpcContract).build())

            whenever(contractFromGrpcMapper.map(grpcContract))
                .thenReturn(contract)
            val expectedContracts = listOf(contract)

            val result = adapter.getContractsByCompanyIdsAndStatuses(listOf(1L), ContractStatus.ONBOARDING)

            assertThat(contract).isEqualTo(expectedContracts.first())
            assertEquals(expectedContracts.size, result.size)
        }
    }
}
