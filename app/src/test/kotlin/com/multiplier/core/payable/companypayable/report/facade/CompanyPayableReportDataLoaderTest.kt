package com.multiplier.core.payable.companypayable.report.facade

import com.multiplier.payable.types.CompanyPayableReport
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.mock

@ExtendWith(MockitoExtension::class)
class CompanyPayableReportDataLoaderTest {

    @Mock
    private lateinit var companyPayableReportService: CompanyPayableReportService

    @InjectMocks
    private lateinit var companyPayableReportDataLoader: CompanyPayableReportDataLoader

    @Test
    fun givenCompanyPayableIds_whenLoad_thenReturnCompletionStage() {
        // GIVEN
        val companyPayableId = 42L
        val companyPayableIds = setOf(companyPayableId)

        val companyPayableReport = mock<CompanyPayableReport>()
        val returnedCompanyPayableReportsByCompanyPayableId = mapOf(42L to companyPayableReport)
        Mockito.`when`(companyPayableReportService.get(companyPayableIds.toList()))
            .thenReturn(returnedCompanyPayableReportsByCompanyPayableId)

        // WHEN
        val completionStage = companyPayableReportDataLoader.load(companyPayableIds)

        // THEN
        val companyPayableReportsByCompanyPayableId = completionStage.toCompletableFuture().get()
        assertThat(companyPayableReportsByCompanyPayableId).isEqualTo(returnedCompanyPayableReportsByCompanyPayableId)
    }
}