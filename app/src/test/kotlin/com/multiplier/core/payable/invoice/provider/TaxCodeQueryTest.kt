package com.multiplier.core.payable.invoice.provider

import com.multiplier.payable.types.CountryCode
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertNull

class TaxCodeQueryTest {

    @Test
    fun `should create TaxCodeQuery with all parameters`() {
        // GIVEN
        val companyCountryCode = CountryCode.SGP
        val mtplSubsidiary = "1"
        val matchingAdvanceCollectionLineTaxCode = "GST_SG:SR-SG"

        // WHEN
        val query = TaxCodeQuery(
            companyCountryCode = companyCountryCode,
            mtplSubsidiary = mtplSubsidiary,
            matchingAdvanceCollectionLineTaxCode = matchingAdvanceCollectionLineTaxCode
        )

        // THEN
        assertEquals(companyCountryCode, query.companyCountryCode)
        assertEquals(mtplSubsidiary, query.mtplSubsidiary)
        assertEquals(matchingAdvanceCollectionLineTaxCode, query.matchingAdvanceCollectionLineTaxCode)
    }

    @Test
    fun `should create TaxCodeQuery with null matchingAdvanceCollectionLineTaxCode by default`() {
        // GIVEN
        val companyCountryCode = CountryCode.USA
        val mtplSubsidiary = "2"

        // WHEN
        val query = TaxCodeQuery(
            companyCountryCode = companyCountryCode,
            mtplSubsidiary = mtplSubsidiary
        )

        // THEN
        assertEquals(companyCountryCode, query.companyCountryCode)
        assertEquals(mtplSubsidiary, query.mtplSubsidiary)
        assertNull(query.matchingAdvanceCollectionLineTaxCode)
    }

    @Test
    fun `should create TaxCodeQuery with explicit null matchingAdvanceCollectionLineTaxCode`() {
        // GIVEN
        val companyCountryCode = CountryCode.GBR
        val mtplSubsidiary = "3"

        // WHEN
        val query = TaxCodeQuery(
            companyCountryCode = companyCountryCode,
            mtplSubsidiary = mtplSubsidiary,
            matchingAdvanceCollectionLineTaxCode = null
        )

        // THEN
        assertEquals(companyCountryCode, query.companyCountryCode)
        assertEquals(mtplSubsidiary, query.mtplSubsidiary)
        assertNull(query.matchingAdvanceCollectionLineTaxCode)
    }

    @ParameterizedTest
    @MethodSource("provideCountryCodes")
    fun `should handle different country codes`(countryCode: CountryCode) {
        // GIVEN
        val mtplSubsidiary = "1"
        val matchingAdvanceCollectionLineTaxCode = "TAX_CODE"

        // WHEN
        val query = TaxCodeQuery(
            companyCountryCode = countryCode,
            mtplSubsidiary = mtplSubsidiary,
            matchingAdvanceCollectionLineTaxCode = matchingAdvanceCollectionLineTaxCode
        )

        // THEN
        assertEquals(countryCode, query.companyCountryCode)
        assertEquals(mtplSubsidiary, query.mtplSubsidiary)
        assertEquals(matchingAdvanceCollectionLineTaxCode, query.matchingAdvanceCollectionLineTaxCode)
    }

    @ParameterizedTest
    @MethodSource("provideSubsidiaryValues")
    fun `should handle different subsidiary values`(subsidiary: String) {
        // GIVEN
        val companyCountryCode = CountryCode.SGP
        val matchingAdvanceCollectionLineTaxCode = "TAX_CODE"

        // WHEN
        val query = TaxCodeQuery(
            companyCountryCode = companyCountryCode,
            mtplSubsidiary = subsidiary,
            matchingAdvanceCollectionLineTaxCode = matchingAdvanceCollectionLineTaxCode
        )

        // THEN
        assertEquals(companyCountryCode, query.companyCountryCode)
        assertEquals(subsidiary, query.mtplSubsidiary)
        assertEquals(matchingAdvanceCollectionLineTaxCode, query.matchingAdvanceCollectionLineTaxCode)
    }

    @ParameterizedTest
    @MethodSource("provideTaxCodeValues")
    fun `should handle different tax code values`(taxCode: String?) {
        // GIVEN
        val companyCountryCode = CountryCode.SGP
        val mtplSubsidiary = "1"

        // WHEN
        val query = TaxCodeQuery(
            companyCountryCode = companyCountryCode,
            mtplSubsidiary = mtplSubsidiary,
            matchingAdvanceCollectionLineTaxCode = taxCode
        )

        // THEN
        assertEquals(companyCountryCode, query.companyCountryCode)
        assertEquals(mtplSubsidiary, query.mtplSubsidiary)
        assertEquals(taxCode, query.matchingAdvanceCollectionLineTaxCode)
    }

    @Test
    fun `should support equality comparison`() {
        // GIVEN
        val query1 = TaxCodeQuery(
            companyCountryCode = CountryCode.SGP,
            mtplSubsidiary = "1",
            matchingAdvanceCollectionLineTaxCode = "GST_SG:SR-SG"
        )
        val query2 = TaxCodeQuery(
            companyCountryCode = CountryCode.SGP,
            mtplSubsidiary = "1",
            matchingAdvanceCollectionLineTaxCode = "GST_SG:SR-SG"
        )
        val query3 = TaxCodeQuery(
            companyCountryCode = CountryCode.USA,
            mtplSubsidiary = "1",
            matchingAdvanceCollectionLineTaxCode = "GST_SG:SR-SG"
        )

        // WHEN & THEN
        assertEquals(query1, query2)
        assertNotEquals(query1, query3)
        assertEquals(query1.hashCode(), query2.hashCode())
    }

    @Test
    fun `should support toString`() {
        // GIVEN
        val query = TaxCodeQuery(
            companyCountryCode = CountryCode.SGP,
            mtplSubsidiary = "1",
            matchingAdvanceCollectionLineTaxCode = "GST_SG:SR-SG"
        )

        // WHEN
        val result = query.toString()

        // THEN
        assert(result.contains("SGP"))
        assert(result.contains("1"))
        assert(result.contains("GST_SG:SR-SG"))
    }

    @Test
    fun `should support copy with modifications`() {
        // GIVEN
        val originalQuery = TaxCodeQuery(
            companyCountryCode = CountryCode.SGP,
            mtplSubsidiary = "1",
            matchingAdvanceCollectionLineTaxCode = "GST_SG:SR-SG"
        )

        // WHEN
        val modifiedQuery = originalQuery.copy(
            companyCountryCode = CountryCode.USA,
            matchingAdvanceCollectionLineTaxCode = "GST_SG:ZR-SG"
        )

        // THEN
        assertEquals(CountryCode.USA, modifiedQuery.companyCountryCode)
        assertEquals("1", modifiedQuery.mtplSubsidiary) // Should remain unchanged
        assertEquals("GST_SG:ZR-SG", modifiedQuery.matchingAdvanceCollectionLineTaxCode)
        
        // Original should remain unchanged
        assertEquals(CountryCode.SGP, originalQuery.companyCountryCode)
        assertEquals("GST_SG:SR-SG", originalQuery.matchingAdvanceCollectionLineTaxCode)
    }

    companion object {
        @JvmStatic
        fun provideCountryCodes(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(CountryCode.SGP),
                Arguments.of(CountryCode.USA),
                Arguments.of(CountryCode.GBR),
                Arguments.of(CountryCode.AUS),
                Arguments.of(CountryCode.CAN),
                Arguments.of(CountryCode.DEU),
                Arguments.of(CountryCode.FRA),
                Arguments.of(CountryCode.JPN),
                Arguments.of(CountryCode.KOR),
                Arguments.of(CountryCode.CHN),
                Arguments.of(CountryCode.IND),
                Arguments.of(CountryCode.BRA),
                Arguments.of(CountryCode.MEX),
                Arguments.of(CountryCode.ZAF),
                Arguments.of(CountryCode.THA),
                Arguments.of(CountryCode.VNM),
                Arguments.of(CountryCode.PHL),
                Arguments.of(CountryCode.IDN),
                Arguments.of(CountryCode.MYS)
            )
        }

        @JvmStatic
        fun provideSubsidiaryValues(): Stream<Arguments> {
            return Stream.of(
                Arguments.of("1"),
                Arguments.of("2"),
                Arguments.of("3"),
                Arguments.of("10"),
                Arguments.of("0"),
                Arguments.of(""),
                Arguments.of("SUBSIDIARY_NAME"),
                Arguments.of("subsidiary-123")
            )
        }

        @JvmStatic
        fun provideTaxCodeValues(): Stream<Arguments> {
            return Stream.of(
                Arguments.of("GST_SG:SR-SG"),
                Arguments.of("GST_SG:ZR-SG"),
                Arguments.of("GST_SG:OS-SG"),
                Arguments.of("VAT_UK:SR-UK"),
                Arguments.of("TAX_US:STANDARD"),
                Arguments.of(""),
                Arguments.of(null)
            )
        }
    }
}
