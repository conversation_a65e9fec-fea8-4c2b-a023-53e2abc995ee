package com.multiplier.core.payable.report

import com.multiplier.core.payable.report.database.JpaInvoiceSourceReport
import com.multiplier.core.payable.report.database.JpaInvoiceSourceReportRepository
import com.multiplier.core.payable.report.dataholder.IsrActionInput
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.whenever

class IsrAutoGenerationValidatorTest {
    private val repo: JpaInvoiceSourceReportRepository = mock()
    private val validator = IsrAutoGenerationValidator(repo)

    @Test
    fun `should return true when no record found allowing fallback`() {
        // GIVEN
        val input =
            IsrActionInput
                .builder()
                .month(1)
                .year(2025)
                .companyId(1L)
                .build()

        whenever(
            repo.findByMonthAndYearAndCompanyIdAndCompanyPayableIdIsNull(
                input.month,
                input.year,
                input.companyId,
            ),
        ).thenReturn(null)

        // WHEN
        val result = validator.shouldSkip(input)

        // THEN
        assertThat(result).isTrue()
    }

    @Test
    fun `should return true when record found allowing fallback but manually updated`() {
        // GIVEN
        val input =
            IsrActionInput
                .builder()
                .month(1)
                .year(2025)
                .companyId(1L)
                .build()
        val report = mock<JpaInvoiceSourceReport>()
        whenever(report.manuallyUploadedBy).thenReturn(1L)

        whenever(
            repo.findByMonthAndYearAndCompanyIdAndCompanyPayableIdIsNull(
                input.month,
                input.year,
                input.companyId,
            ),
        ).thenReturn(report)

        // WHEN
        val result = validator.shouldSkip(input)

        // THEN
        assertThat(result).isTrue()
    }

    @Test
    fun `should return false when record found allowing fallback and not manually updated`() {
        // GIVEN
        val input =
            IsrActionInput
                .builder()
                .month(1)
                .year(2025)
                .companyId(1L)
                .build()
        val report = mock<JpaInvoiceSourceReport>()
        whenever(report.manuallyUploadedBy).thenReturn(null)
        whenever(
            repo.findByMonthAndYearAndCompanyIdAndCompanyPayableIdIsNull(
                input.month,
                input.year,
                input.companyId,
            ),
        ).thenReturn(report)

        // WHEN
        val result = validator.shouldSkip(input)

        // THEN
        assertThat(result).isFalse()
    }

    @Test
    fun `should return true when record found without fallback but manually updated`() {
        // GIVEN
        val isrActionInput =
            IsrActionInput
                .builder()
                .month(1)
                .year(2025)
                .companyId(1L)
                .companyPayableId(2L)
                .build()
        val report = mock<JpaInvoiceSourceReport>()
        whenever(report.manuallyUploadedBy).thenReturn(1L)
        whenever(
            repo.findByCompanyPayableId(2L),
        ).thenReturn(report)

        // WHEN
        val result = validator.shouldSkip(isrActionInput)

        // THEN
        assertThat(result).isTrue()
    }

    @Test
    fun `should not throw exception when record found without fallback and not manually updated`() {
        // GIVEN
        val input =
            IsrActionInput
                .builder()
                .month(1)
                .year(2025)
                .companyId(1L)
                .companyPayableId(2L)
                .build()
        val report = mock<JpaInvoiceSourceReport>()
        whenever(report.manuallyUploadedBy).thenReturn(null)
        whenever(
            repo.findByCompanyPayableId(2L),
        ).thenReturn(report)

        // WHEN
        val result = validator.shouldSkip(input)

        // THEN
        assertThat(result).isFalse()
    }
}
