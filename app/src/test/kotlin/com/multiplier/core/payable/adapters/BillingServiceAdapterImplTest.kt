package com.multiplier.core.payable.adapters

import com.google.protobuf.Timestamp
import com.multiplier.billing.grpc.billing.Billing
import com.multiplier.billing.grpc.billing.BillingServiceGrpc
import com.multiplier.core.mapper.bill.BilledItemGrpcToDomainMapper
import com.multiplier.core.payable.adapters.billing.BilledItemWrapper
import com.multiplier.core.payable.adapters.pricing.ReferenceChargePolicy
import com.multiplier.core.payable.adapters.product.CompanyProductWrapper
import com.multiplier.core.payable.mapper.GrpcDateTimeMapper
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.engine.domain.aggregates.DateRange
import com.multiplier.payable.engine.domain.aggregates.Duration
import com.multiplier.payable.types.CurrencyCode
import io.grpc.Status
import io.grpc.StatusRuntimeException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import com.multiplier.common.exception.MplBusinessException
import com.multiplier.core.exception.PayableErrorCode
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.never
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.time.LocalDateTime
import com.multiplier.grpc.common.time.v2.DurationOuterClass.Duration as GrpcDuration

@ExtendWith(MockitoExtension::class)
class BillingServiceAdapterImplTest {
    @Mock
    private lateinit var billingServiceClient: BillingServiceGrpc.BillingServiceBlockingStub

    @Mock
    private lateinit var grpcDateTimeMapper: GrpcDateTimeMapper

    @Mock
    private lateinit var billedItemGrpcToDomainMapper: BilledItemGrpcToDomainMapper

    @InjectMocks
    private lateinit var billingServiceAdapter: BillingServiceAdapterImpl

    @BeforeEach
    fun setup() {
        // Use lenient mode to avoid unnecessary stubbing errors
        org.mockito.Mockito.lenient().`when`(grpcDateTimeMapper.toTimestamp(any()))
            .thenReturn(Timestamp.newBuilder().setSeconds(1000L).build())
    }

    @Test
    fun `getOrCreateOrderFormAdvanceBills should return billed items for ORDER_FORM_ADVANCE`() {
        // Given
        val companyId = 123L
        val entityId = 456L
        val startDate = LocalDateTime.of(2023, 1, 1, 0, 0)
        val endDate = LocalDateTime.of(2023, 2, 1, 0, 0)
        val dateRange = DateRange(startDate, endDate)
        val orderFormAdvanceLineCode = "ORDER_FORM_ADVANCE"

        val billedItemWrapper = BilledItemWrapper(
            billId = 1L,
            companyId = companyId,
            entityId = entityId,
            transactionId = "",
            billingAmount = Amount(
                value = BigDecimal.valueOf(100.0),
                currency = CurrencyCode.USD
            ),
            companyProduct = CompanyProductWrapper(
                startDate = startDate,
                endDate = endDate,
                lineCode = "TEST_LINE_CODE",
                dimensions = mapOf("key1" to "value1"),
                chargePolicy = ReferenceChargePolicy(emptyList())
            ),
            billingDuration = Duration(
                startDate = startDate.toLocalDate(),
                endDate = endDate.toLocalDate()
            ),
            usageDuration = Duration(
                startDate = startDate.toLocalDate(),
                endDate = endDate.toLocalDate()
            ),
            billingTime = 0L,
            referenceBills = emptyList(),
            usages = emptyList()
        )

        val billedItem = Billing.BilledItem.newBuilder()
            .setId(1L)
            .setCompanyId(companyId)
            .setEntityId(entityId)
            .build()

        val grpcBillingResult = Billing.BillingResultPerLineCode.newBuilder()
            .setLineCode(orderFormAdvanceLineCode)
            .addItems(billedItem)
            .build()

        val generateBillsResponse = Billing.BillingCommandResult.newBuilder()
            .addResults(grpcBillingResult)
            .build()

        `when`(billingServiceClient.generateBills(org.mockito.ArgumentMatchers.any()))
            .thenReturn(generateBillsResponse)

        `when`(billedItemGrpcToDomainMapper.map(listOf(billedItem)))
            .thenReturn(listOf(billedItemWrapper))

        // When
        val result = billingServiceAdapter.getOrCreateOrderFormAdvanceBills(companyId, entityId, dateRange)

        // Then
        assertEquals(1, result.size)
        assertEquals(billedItemWrapper, result[0])
        org.mockito.Mockito.verify(billingServiceClient).generateBills(org.mockito.ArgumentMatchers.any())
    }

    @Test
    fun `getOrCreateOrderFormAdvanceBills should return empty list when no results for ORDER_FORM_ADVANCE`() {
        // Given
        val companyId = 123L
        val entityId = 456L
        val startDate = LocalDateTime.of(2023, 1, 1, 0, 0)
        val endDate = LocalDateTime.of(2023, 2, 1, 0, 0)
        val dateRange = DateRange(startDate, endDate)

        val generateBillsResponse = Billing.BillingCommandResult.newBuilder().build()

        `when`(billingServiceClient.generateBills(org.mockito.ArgumentMatchers.any()))
            .thenReturn(generateBillsResponse)

        // When
        val result = billingServiceAdapter.getOrCreateOrderFormAdvanceBills(companyId, entityId, dateRange)

        // Then
        assertEquals(0, result.size)
        org.mockito.Mockito.verify(billingServiceClient).generateBills(org.mockito.ArgumentMatchers.any())
    }

    @Test
    fun `getOrCreateOrderFormAdvanceBills should throw exception when billing generation error occurs`() {
        // Given
        val companyId = 123L
        val entityId = 456L
        val startDate = LocalDateTime.of(2023, 1, 1, 0, 0)
        val endDate = LocalDateTime.of(2023, 2, 1, 0, 0)
        val dateRange = DateRange(startDate, endDate)
        val orderFormAdvanceLineCode = "ORDER_FORM_ADVANCE"

        val billedItem = Billing.BilledItem.newBuilder()
            .setId(1L)
            .setCompanyId(companyId)
            .setEntityId(entityId)
            .build()

        val grpcBillingResult = Billing.BillingResultPerLineCode.newBuilder()
            .setLineCode(orderFormAdvanceLineCode)
            .addItems(billedItem)
            .setError(
                Billing.BillingError.newBuilder()
                    .setErrorCode(Billing.BillingErrorCode.BILLING_ERROR_CODE_ERROR_HANDLING_BILLING_EVENT)
                    .setErrorMessage("Invalid company ID")
                    .build()
            )
            .build()

        val generateBillsResponse = Billing.BillingCommandResult.newBuilder()
            .addResults(grpcBillingResult)
            .build()

        `when`(billingServiceClient.generateBills(org.mockito.ArgumentMatchers.any()))
            .thenReturn(generateBillsResponse)

        // When/Then
        val exception = assertThrows<MplBusinessException> {
            billingServiceAdapter.getOrCreateOrderFormAdvanceBills(companyId, entityId, dateRange)
        }

        // Verify the exception details
        assertTrue(exception.message.contains("Billing generation failed for ORDER_FORM_ADVANCE"))
        assertTrue(exception.message.contains("companyId=$companyId"))
        assertTrue(exception.message.contains("entityId=$entityId"))
        assertTrue(exception.message.contains("BILLING_ERROR_CODE_ERROR_HANDLING_BILLING_EVENT"))
        assertTrue(exception.message.contains("Invalid company ID"))

        org.mockito.Mockito.verify(billingServiceClient).generateBills(org.mockito.ArgumentMatchers.any())
    }


    @Test
    fun `getBilledItems should return list of billed items when successful`() {
        // Given
        val companyId = 123L
        val startDateSeconds = 1640995200L // 2022-01-01
        val endDateSeconds = 1643673600L   // 2022-02-01
        val excludeBillingIds = listOf(1L, 2L)
        val lineItemCodes = listOf("CODE1", "CODE2")

        val expectedBilledItem = Billing.BilledItem.newBuilder().build()
        val response = Billing.GetBilledItemsResponse.newBuilder()
            .addBilledItems(expectedBilledItem)
            .build()

        whenever(billingServiceClient.getBilledItems(any()))
            .thenReturn(response)

        // When
        val result = billingServiceAdapter.getBilledItems(
            companyId,
            startDateSeconds,
            endDateSeconds,
            excludeBillingIds,
            lineItemCodes
        )

        // Then
        assertEquals(listOf(expectedBilledItem), result)
        verify(billingServiceClient).getBilledItems(
            Billing.GetBilledItemsRequest.newBuilder()
                .setCompanyId(companyId)
                .setDuration(
                    GrpcDuration.newBuilder()
                        .setStartDate(Timestamp.newBuilder().setSeconds(startDateSeconds).build())
                        .setEndDate(Timestamp.newBuilder().setSeconds(endDateSeconds).build())
                        .build()
                )
                .addAllExcludeBillingIds(excludeBillingIds)
                .addAllLineItemCodes(lineItemCodes)
                .build()
        )
    }

    @Test
    fun `getBilledItems should throw exception when gRPC call fails`() {
        // Given
        val errorMessage = "GRPC error"
        whenever(billingServiceClient.getBilledItems(any()))
            .thenThrow(StatusRuntimeException(Status.INTERNAL.withDescription(errorMessage)))

        // When/Then
        assertThrows<StatusRuntimeException> {
            billingServiceAdapter.getBilledItems(
                companyId = 123L,
                startDateSeconds = 1640995200L,
                endDateSeconds = 1643673600L
            )
        }
    }

    @Test
    fun `generateBills should return billing results when successful`() {
        // Given
        val companyId = 123L
        val startDateSeconds = 1640995200L
        val endDateSeconds = 1643673600L
        val lineItemCodes = listOf("CODE1", "CODE2")

        val expectedResult = Billing.BillingResultPerLineCode.newBuilder().build()
        val response = Billing.BillingCommandResult.newBuilder()
            .addResults(expectedResult)
            .build()

        whenever(billingServiceClient.generateBills(any()))
            .thenReturn(response)

        // When
        val result = billingServiceAdapter.generateBills(
            companyId,
            startDateSeconds,
            endDateSeconds,
            lineItemCodes
        )

        // Then
        assertEquals(listOf(expectedResult), result)
        verify(billingServiceClient).generateBills(
            Billing.BillingCommand.newBuilder()
                .setCompanyId(companyId)
                .setDuration(
                    GrpcDuration.newBuilder()
                        .setStartDate(Timestamp.newBuilder().setSeconds(startDateSeconds).build())
                        .setEndDate(Timestamp.newBuilder().setSeconds(endDateSeconds).build())
                        .build()
                )
                .addAllLineCodes(lineItemCodes)
                .build()
        )
    }

    @Test
    fun `generateBills should throw exception when gRPC call fails`() {
        // Given
        val errorMessage = "GRPC error"
        whenever(billingServiceClient.generateBills(any()))
            .thenThrow(StatusRuntimeException(Status.INTERNAL.withDescription(errorMessage)))

        // When/Then
        assertThrows<StatusRuntimeException> {
            billingServiceAdapter.generateBills(
                companyId = 123L,
                startDateSeconds = 1640995200L,
                endDateSeconds = 1643673600L
            )
        }
    }

    @Test
    fun `getBilledItems should handle empty response`() {
        // Given
        val response = Billing.GetBilledItemsResponse.getDefaultInstance()
        whenever(billingServiceClient.getBilledItems(any()))
            .thenReturn(response)

        // When
        val result = billingServiceAdapter.getBilledItems(
            companyId = 123L,
            startDateSeconds = 1640995200L,
            endDateSeconds = 1643673600L
        )

        // Then
        assertEquals(emptyList<Billing.BilledItem>(), result)
    }

    @Test
    fun `getBilledItems should include entityId in request when provided`() {
        // Given
        val companyId = 123L
        val entityId = 456L
        val startDateSeconds = 1640995200L
        val endDateSeconds = 1643673600L
        val excludeBillingIds = listOf(1L, 2L)
        val lineItemCodes = listOf("CODE1", "CODE2")

        val expectedBilledItem = Billing.BilledItem.newBuilder().build()
        val response = Billing.GetBilledItemsResponse.newBuilder()
            .addBilledItems(expectedBilledItem)
            .build()

        whenever(billingServiceClient.getBilledItems(any()))
            .thenReturn(response)

        // When
        val result = billingServiceAdapter.getBilledItems(
            companyId,
            startDateSeconds,
            endDateSeconds,
            excludeBillingIds,
            lineItemCodes,
            entityId
        )

        // Then
        assertEquals(listOf(expectedBilledItem), result)
        verify(billingServiceClient).getBilledItems(
            Billing.GetBilledItemsRequest.newBuilder()
                .setCompanyId(companyId)
                .setEntityId(entityId)
                .setDuration(
                    GrpcDuration.newBuilder()
                        .setStartDate(Timestamp.newBuilder().setSeconds(startDateSeconds).build())
                        .setEndDate(Timestamp.newBuilder().setSeconds(endDateSeconds).build())
                        .build()
                )
                .addAllExcludeBillingIds(excludeBillingIds)
                .addAllLineItemCodes(lineItemCodes)
                .build()
        )
    }

    @Test
    fun `getBilledItems should not include entityId in request when null`() {
        // Given
        val companyId = 123L
        val startDateSeconds = 1640995200L
        val endDateSeconds = 1643673600L

        val expectedBilledItem = Billing.BilledItem.newBuilder().build()
        val response = Billing.GetBilledItemsResponse.newBuilder()
            .addBilledItems(expectedBilledItem)
            .build()

        whenever(billingServiceClient.getBilledItems(any()))
            .thenReturn(response)

        // When
        val result = billingServiceAdapter.getBilledItems(
            companyId,
            startDateSeconds,
            endDateSeconds,
            entityId = null
        )

        // Then
        assertEquals(listOf(expectedBilledItem), result)
        verify(billingServiceClient).getBilledItems(
            Billing.GetBilledItemsRequest.newBuilder()
                .setCompanyId(companyId)
                .setDuration(
                    GrpcDuration.newBuilder()
                        .setStartDate(Timestamp.newBuilder().setSeconds(startDateSeconds).build())
                        .setEndDate(Timestamp.newBuilder().setSeconds(endDateSeconds).build())
                        .build()
                )
                .addAllExcludeBillingIds(emptyList())
                .addAllLineItemCodes(emptyList())
                .build()
        )
    }

    @Test
    fun `generateBills should handle empty response`() {
        // Given
        val response = Billing.BillingCommandResult.getDefaultInstance()
        whenever(billingServiceClient.generateBills(any()))
            .thenReturn(response)

        // When
        val result = billingServiceAdapter.generateBills(
            companyId = 123L,
            startDateSeconds = 1640995200L,
            endDateSeconds = 1643673600L
        )

        // Then
        assertEquals(emptyList<Billing.BillingResultPerLineCode>(), result)
    }

    @Test
    fun `get should return billed items when all requested IDs are found`() {
        // Given
        val billIds = setOf(1L, 2L, 3L)
        val billedItemWrapper1 = createBilledItemWrapper(1L)
        val billedItemWrapper2 = createBilledItemWrapper(2L)
        val billedItemWrapper3 = createBilledItemWrapper(3L)
        val expectedBilledItems = listOf(billedItemWrapper1, billedItemWrapper2, billedItemWrapper3)

        val grpcBilledItem1 = Billing.BilledItem.newBuilder().setId(1L).build()
        val grpcBilledItem2 = Billing.BilledItem.newBuilder().setId(2L).build()
        val grpcBilledItem3 = Billing.BilledItem.newBuilder().setId(3L).build()
        val grpcResponse = Billing.GetBilledItemsResponse.newBuilder()
            .addAllBilledItems(listOf(grpcBilledItem1, grpcBilledItem2, grpcBilledItem3))
            .build()

        whenever(billingServiceClient.getBilledItemsByIds(any()))
            .thenReturn(grpcResponse)
        whenever(billedItemGrpcToDomainMapper.map(listOf(grpcBilledItem1, grpcBilledItem2, grpcBilledItem3)))
            .thenReturn(expectedBilledItems)

        // When
        val result = billingServiceAdapter.getBillsByIds(billIds)

        // Then
        assertEquals(expectedBilledItems, result)
        verify(billingServiceClient).getBilledItemsByIds(
            Billing.GetBilledItemsByIdsRequest.newBuilder()
                .addAllBilledItemIds(billIds)
                .build()
        )
        verify(billedItemGrpcToDomainMapper).map(listOf(grpcBilledItem1, grpcBilledItem2, grpcBilledItem3))
    }

    @Test
    fun `get should return empty list when no bill IDs provided`() {
        // Given
        val billIds = emptySet<Long>()

        // When
        val result = billingServiceAdapter.getBillsByIds(billIds)

        // Then
        assertEquals(emptyList<BilledItemWrapper>(), result)
        // Verify no gRPC calls were made
        org.mockito.Mockito.verifyNoInteractions(billingServiceClient)
        org.mockito.Mockito.verifyNoInteractions(billedItemGrpcToDomainMapper)
    }

    @Test
    fun `get should throw BILLING_ITEMS_NOT_FOUND when some IDs are missing`() {
        // Given
        val requestedBillIds = setOf(1L, 2L, 3L)
        val returnedBillId = 1L
        val billedItemWrapper = createBilledItemWrapper(returnedBillId)

        val grpcBilledItem = Billing.BilledItem.newBuilder().setId(returnedBillId).build()
        val grpcResponse = Billing.GetBilledItemsResponse.newBuilder()
            .addBilledItems(grpcBilledItem)
            .build()

        whenever(billingServiceClient.getBilledItemsByIds(any()))
            .thenReturn(grpcResponse)
        whenever(billedItemGrpcToDomainMapper.map(listOf(grpcBilledItem)))
            .thenReturn(listOf(billedItemWrapper))

        // When/Then
        val exception = assertThrows<MplBusinessException> {
            billingServiceAdapter.getBillsByIds(requestedBillIds)
        }

        // Verify the exception details
        assertTrue(exception.message.contains("Billing items not found for IDs: [2, 3]"))
        assertTrue(exception.message.contains("Requested 3 items but only 1 were returned"))
        assertEquals(PayableErrorCode.BILLING_ITEMS_NOT_FOUND, exception.errorCode)

        verify(billingServiceClient).getBilledItemsByIds(
            Billing.GetBilledItemsByIdsRequest.newBuilder()
                .addAllBilledItemIds(requestedBillIds)
                .build()
        )
    }

    @Test
    fun `get should throw BILLING_ITEMS_NOT_FOUND when no items are returned`() {
        // Given
        val requestedBillIds = setOf(1L, 2L)
        val grpcResponse = Billing.GetBilledItemsResponse.newBuilder().build() // Empty response

        whenever(billingServiceClient.getBilledItemsByIds(any()))
            .thenReturn(grpcResponse)
        whenever(billedItemGrpcToDomainMapper.map(emptyList()))
            .thenReturn(emptyList())

        // When/Then
        val exception = assertThrows<MplBusinessException> {
            billingServiceAdapter.getBillsByIds(requestedBillIds)
        }

        // Verify the exception details
        assertTrue(exception.message.contains("Billing items not found for IDs: [1, 2]"))
        assertTrue(exception.message.contains("Requested 2 items but only 0 were returned"))
        assertEquals(PayableErrorCode.BILLING_ITEMS_NOT_FOUND, exception.errorCode)
    }

    @Test
    fun `get should propagate gRPC exceptions`() {
        // Given
        val billIds = setOf(1L, 2L)
        val grpcException = StatusRuntimeException(Status.INTERNAL.withDescription("Internal server error"))

        whenever(billingServiceClient.getBilledItemsByIds(any()))
            .thenThrow(grpcException)

        // When/Then
        val exception = assertThrows<StatusRuntimeException> {
            billingServiceAdapter.getBillsByIds(billIds)
        }

        assertEquals(grpcException, exception)
        verify(billingServiceClient).getBilledItemsByIds(
            Billing.GetBilledItemsByIdsRequest.newBuilder()
                .addAllBilledItemIds(billIds)
                .build()
        )
    }

    @Test
    fun `get should handle single bill ID correctly`() {
        // Given
        val billIds = setOf(42L)
        val billedItemWrapper = createBilledItemWrapper(42L)

        val grpcBilledItem = Billing.BilledItem.newBuilder().setId(42L).build()
        val grpcResponse = Billing.GetBilledItemsResponse.newBuilder()
            .addBilledItems(grpcBilledItem)
            .build()

        whenever(billingServiceClient.getBilledItemsByIds(any()))
            .thenReturn(grpcResponse)
        whenever(billedItemGrpcToDomainMapper.map(listOf(grpcBilledItem)))
            .thenReturn(listOf(billedItemWrapper))

        // When
        val result = billingServiceAdapter.getBillsByIds(billIds)

        // Then
        assertEquals(listOf(billedItemWrapper), result)
        verify(billingServiceClient).getBilledItemsByIds(
            Billing.GetBilledItemsByIdsRequest.newBuilder()
                .addAllBilledItemIds(billIds)
                .build()
        )
    }

    @Test
    fun `get should handle mapper exceptions`() {
        // Given
        val billIds = setOf(1L)
        val grpcBilledItem = Billing.BilledItem.newBuilder().setId(1L).build()
        val grpcResponse = Billing.GetBilledItemsResponse.newBuilder()
            .addBilledItems(grpcBilledItem)
            .build()
        val mapperException = RuntimeException("Mapping failed")

        whenever(billingServiceClient.getBilledItemsByIds(any()))
            .thenReturn(grpcResponse)
        whenever(billedItemGrpcToDomainMapper.map(listOf(grpcBilledItem)))
            .thenThrow(mapperException)

        // When/Then
        val exception = assertThrows<RuntimeException> {
            billingServiceAdapter.getBillsByIds(billIds)
        }

        assertEquals(mapperException, exception)
    }

    private fun createBilledItemWrapper(billId: Long): BilledItemWrapper {
        return BilledItemWrapper(
            billId = billId,
            transactionId = "txn-$billId",
            companyId = 123L,
            entityId = 456L,
            companyProduct = CompanyProductWrapper(
                startDate = LocalDateTime.of(2023, 1, 1, 0, 0),
                endDate = LocalDateTime.of(2023, 2, 1, 0, 0),
                lineCode = "TEST_LINE_CODE",
                dimensions = mapOf("key1" to "value1"),
                chargePolicy = ReferenceChargePolicy(emptyList())
            ),
            billingAmount = Amount(BigDecimal.valueOf(100.0), CurrencyCode.USD),
            billingDuration = Duration(
                startDate = LocalDateTime.of(2023, 1, 1, 0, 0).toLocalDate(),
                endDate = LocalDateTime.of(2023, 2, 1, 0, 0).toLocalDate()
            ),
            usageDuration = Duration(
                startDate = LocalDateTime.of(2023, 1, 1, 0, 0).toLocalDate(),
                endDate = LocalDateTime.of(2023, 2, 1, 0, 0).toLocalDate()
            ),
            billingTime = System.currentTimeMillis() / 1000,
            referenceBills = emptyList(),
            usages = emptyList()
        )
    }

    @Test
    fun `deleteBills should return deleted bill IDs when all bills are successfully deleted`() {
        // Given
        val billIds = listOf(1L, 2L, 3L)
        val deletedBillIds = listOf(1L, 2L, 3L)

        val response = Billing.DeleteBilledItemsByIdsResponse.newBuilder()
            .addAllDeletedBilledItemIds(deletedBillIds)
            .build()

        whenever(billingServiceClient.deleteBilledItemsByIds(any()))
            .thenReturn(response)

        // When
        val result = billingServiceAdapter.deleteBills(billIds)

        // Then
        assertEquals(deletedBillIds, result)
        verify(billingServiceClient).deleteBilledItemsByIds(
            Billing.DeleteBilledItemsByIdsRequest.newBuilder()
                .addAllBilledItemIds(billIds)
                .build()
        )
    }

    @Test
    fun `deleteBills should return empty list when no bill IDs provided`() {
        // Given
        val billIds = emptyList<Long>()

        // When
        val result = billingServiceAdapter.deleteBills(billIds)

        // Then
        assertTrue(result.isEmpty())
        // Verify that gRPC client is not called for empty input
        verify(billingServiceClient, never()).deleteBilledItemsByIds(any())
    }

    @Test
    fun `deleteBills should throw BILLING_DELETION_FAILED when not all bills are deleted`() {
        // Given
        val billIds = listOf(1L, 2L, 3L)
        val deletedBillIds = listOf(1L, 2L) // Missing bill ID 3

        val response = Billing.DeleteBilledItemsByIdsResponse.newBuilder()
            .addAllDeletedBilledItemIds(deletedBillIds)
            .build()

        whenever(billingServiceClient.deleteBilledItemsByIds(any()))
            .thenReturn(response)

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            billingServiceAdapter.deleteBills(billIds)
        }

        assertEquals(PayableErrorCode.BILLING_DELETION_FAILED, exception.errorCode)
        assertTrue(exception.message.contains("Failed to delete all requested bills"))
        assertTrue(exception.message.contains("Successfully deleted 2 out of 3 bills"))
        assertTrue(exception.message.contains("Failed to delete bill IDs: [3]"))
    }

    @Test
    fun `deleteBills should throw BILLING_DELETION_FAILED when gRPC response has error`() {
        // Given
        val billIds = listOf(1L, 2L, 3L)

        val billingError = Billing.BillingError.newBuilder()
            .setErrorCode(Billing.BillingErrorCode.BILLING_ERROR_CODE_ERROR_HANDLING_BILLING_EVENT)
            .setErrorMessage("Database connection failed")
            .build()

        val response = Billing.DeleteBilledItemsByIdsResponse.newBuilder()
            .setError(billingError)
            .build()

        whenever(billingServiceClient.deleteBilledItemsByIds(any()))
            .thenReturn(response)

        // When & Then
        val exception = assertThrows<MplBusinessException> {
            billingServiceAdapter.deleteBills(billIds)
        }

        assertEquals(PayableErrorCode.BILLING_DELETION_FAILED, exception.errorCode)
        assertTrue(exception.message.contains("Failed to delete bills with IDs: [1, 2, 3]"))
        assertTrue(exception.message.contains("Database connection failed"))
    }

    @Test
    fun `deleteBills should propagate gRPC exceptions`() {
        // Given
        val billIds = listOf(1L, 2L)
        val grpcException = StatusRuntimeException(Status.INTERNAL.withDescription("Internal server error"))

        whenever(billingServiceClient.deleteBilledItemsByIds(any()))
            .thenThrow(grpcException)

        // When/Then
        val exception = assertThrows<StatusRuntimeException> {
            billingServiceAdapter.deleteBills(billIds)
        }

        assertEquals(grpcException, exception)
        verify(billingServiceClient).deleteBilledItemsByIds(
            Billing.DeleteBilledItemsByIdsRequest.newBuilder()
                .addAllBilledItemIds(billIds)
                .build()
        )
    }

}
