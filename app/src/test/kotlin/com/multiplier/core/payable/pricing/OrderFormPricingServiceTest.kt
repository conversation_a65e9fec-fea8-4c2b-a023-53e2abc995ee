package com.multiplier.core.payable.pricing

import com.multiplier.core.payable.company.adapter.CompanyServiceAdapter
import com.multiplier.core.payable.repository.JpaEmployeePricingRepository
import com.multiplier.core.payable.repository.JpaPricingRepository
import com.multiplier.core.payable.repository.model.JpaEmployeePricing
import com.multiplier.core.payable.repository.model.JpaPricing
import com.multiplier.core.payable.service.mapper.EmployeePricingMapper
import com.multiplier.core.payable.service.mapper.PricingMapper
import com.multiplier.core.payable.service.mapper.VisaPricingMapper
import com.multiplier.payable.service.CompanyUpdateKafkaPublisherService
import com.multiplier.payable.types.CompanyEmployeePricingInput
import com.multiplier.payable.types.CompanyGlobalPricingInput
import com.multiplier.payable.types.CompanyVisaGlobalPricingInput
import com.multiplier.payable.types.CompanyVisaPricingInput
import com.multiplier.payable.types.ContractType
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.PricingInput
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers
import org.mockito.Mockito.anyList
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDate

class OrderFormPricingServiceTest {
    private val companyServiceAdapter: CompanyServiceAdapter = mock()
    private val jpaPricingRepository: JpaPricingRepository = mock()
    private val jpaEmployeePricingRepository: JpaEmployeePricingRepository = mock()
    private val employeePricingMapper: EmployeePricingMapper = mock()
    private val pricingMapper: PricingMapper = mock()
    private val visaPricingMapper: VisaPricingMapper = mock()
    private val companyUpdateKafkaPublisherService: CompanyUpdateKafkaPublisherService = mock()
    private val orderFormPricingService: OrderFormPricingService = OrderFormPricingService(
        companyServiceAdapter,
        jpaPricingRepository,
        jpaEmployeePricingRepository,
        employeePricingMapper,
        pricingMapper,
        visaPricingMapper,
        companyUpdateKafkaPublisherService
    )

    @Test
    fun `should create new pricing`() {
        // Given
        val companyId = 1L
        val vnmEmployeePricingInput = CompanyEmployeePricingInput.newBuilder()
            .employeeType(ContractType.EMPLOYEE)
            .country(CountryCode.VNM)
            .fixedRate(3000.0)
            .build()
        val vnmContractorPricingInput = CompanyEmployeePricingInput.newBuilder()
            .employeeType(ContractType.CONTRACTOR)
            .country(CountryCode.VNM)
            .fixedRate(4000.0)
            .build()
        val vnmFreelancerPricingInput = CompanyEmployeePricingInput.newBuilder()
            .employeeType(ContractType.FREELANCER)
            .country(CountryCode.VNM)
            .fixedRate(5000.0)
            .build()
        val visaVnmInput = CompanyVisaPricingInput.newBuilder()
            .employeeType(ContractType.EMPLOYEE)
            .fixedRate(13000.0)
            .validUntil(LocalDate.of(2099, 1, 1))
            .country(CountryCode.VNM)
            .build()
        val pricingInput = PricingInput.newBuilder()
            .globalPricing(
                listOf(
                    CompanyGlobalPricingInput.newBuilder()
                        .employeeType(ContractType.EMPLOYEE)
                        .fixedRate(500.0)
                        .build(),
                    CompanyGlobalPricingInput.newBuilder()
                        .employeeType(ContractType.CONTRACTOR)
                        .fixedRate(1000.0)
                        .build(),
                    CompanyGlobalPricingInput.newBuilder()
                        .employeeType(ContractType.FREELANCER)
                        .fixedRate(1500.0)
                        .build()
                )
            )
            .visaGlobalPricing(
                listOf(
                    CompanyVisaGlobalPricingInput.newBuilder()
                        .employeeType(ContractType.EMPLOYEE)
                        .fixedRate(2000.0)
                        .build(),
                )
            )
            .employeePricing(
                listOf(
                    vnmEmployeePricingInput,
                    vnmContractorPricingInput,
                    vnmFreelancerPricingInput,
                )
            )
            .visaPricing(
                listOf(
                    visaVnmInput,
                )
            )
            .build()
        whenever(jpaPricingRepository.findByCompanyId(companyId)).thenReturn(null)
        whenever(pricingMapper.getDiscountTermFromInput(pricingInput.discountTerms)).thenReturn(emptyList())
        val mockJpaPricing = mock<JpaPricing>()
        val mockSavedJpaPricing = mock<JpaPricing>()
        whenever(mockSavedJpaPricing.id).thenReturn(1L)
        whenever(pricingMapper.mapPricing(companyId, pricingInput, emptyList())).thenReturn(mockJpaPricing)
        whenever(jpaPricingRepository.save(mockJpaPricing)).thenReturn(mockSavedJpaPricing)
        whenever(jpaEmployeePricingRepository.findByPricingIdAndAndType(ArgumentMatchers.eq(1L), any()))
            .thenReturn(emptyList())
        val mockVnmEmployeeJpa = mock<JpaEmployeePricing>().apply {
            whenever(this.employeeType).thenReturn(ContractType.EMPLOYEE)
            whenever(this.country).thenReturn(CountryCode.VNM)
            whenever(this.fixedRate).thenReturn(3000.0)
        }
        val mockVnmContractorJpa = mock<JpaEmployeePricing>().apply {
            whenever(this.employeeType).thenReturn(ContractType.CONTRACTOR)
            whenever(this.country).thenReturn(CountryCode.VNM)
            whenever(this.fixedRate).thenReturn(4000.0)
        }
        val mockVnmFreelancerJpa = mock<JpaEmployeePricing>().apply {
            whenever(this.employeeType).thenReturn(ContractType.FREELANCER)
            whenever(this.country).thenReturn(CountryCode.VNM)
            whenever(this.fixedRate).thenReturn(5000.0)
        }
        val mockSavedVnmEmployeeJpa = mock<JpaEmployeePricing>().apply {
            whenever(this.id).thenReturn(1L)
            whenever(this.employeeType).thenReturn(ContractType.EMPLOYEE)
            whenever(this.country).thenReturn(CountryCode.VNM)
            whenever(this.fixedRate).thenReturn(3000.0)
        }
        val mockSavedVnmContractorJpa = mock<JpaEmployeePricing>().apply {
            whenever(this.id).thenReturn(2L)
            whenever(this.employeeType).thenReturn(ContractType.CONTRACTOR)
            whenever(this.country).thenReturn(CountryCode.VNM)
            whenever(this.fixedRate).thenReturn(4000.0)
        }
        val mockSavedVnmFreelancerJpa = mock<JpaEmployeePricing>().apply {
            whenever(this.id).thenReturn(3L)
            whenever(this.employeeType).thenReturn(ContractType.FREELANCER)
            whenever(this.country).thenReturn(CountryCode.VNM)
            whenever(this.fixedRate).thenReturn(5000.0)
        }
        whenever(employeePricingMapper.buildJpaEmployeePricing(1L, vnmEmployeePricingInput))
            .thenReturn(mockVnmEmployeeJpa)
        whenever(employeePricingMapper.buildJpaEmployeePricing(1L, vnmContractorPricingInput))
            .thenReturn(mockVnmContractorJpa)
        whenever(employeePricingMapper.buildJpaEmployeePricing(1L, vnmFreelancerPricingInput))
            .thenReturn(mockVnmFreelancerJpa)
        whenever(
            jpaEmployeePricingRepository.saveAll(
                listOf(
                    mockVnmEmployeeJpa,
                    mockVnmContractorJpa,
                    mockVnmFreelancerJpa
                )
            )
        )
            .thenReturn(listOf(mockSavedVnmEmployeeJpa, mockSavedVnmContractorJpa, mockSavedVnmFreelancerJpa))
        whenever(pricingMapper.mapFromJpa(mockSavedJpaPricing))
            .thenReturn(mock())
        whenever(employeePricingMapper.map(anyList()))
            .thenReturn(
                listOf(
                    mock(),
                    mock(),
                    mock(),
                )
            )

        // When
        val result = orderFormPricingService.backfillPricingFromOrderForm(companyId, pricingInput)

        // Then
        assertThat(result).isNotNull
        verify(jpaPricingRepository).save(any())
    }

    @Test
    fun `should update existing pricing`() {
        // Given
        val companyId = 1L
        val vnmEmployeePricingInput = CompanyEmployeePricingInput.newBuilder()
            .employeeType(ContractType.EMPLOYEE)
            .country(CountryCode.VNM)
            .fixedRate(3000.0)
            .build()
        val vnmContractorPricingInput = CompanyEmployeePricingInput.newBuilder()
            .employeeType(ContractType.CONTRACTOR)
            .country(CountryCode.VNM)
            .fixedRate(4000.0)
            .build()
        val vnmFreelancerPricingInput = CompanyEmployeePricingInput.newBuilder()
            .employeeType(ContractType.FREELANCER)
            .country(CountryCode.VNM)
            .fixedRate(5000.0)
            .build()
        val visaVnmInput = CompanyVisaPricingInput.newBuilder()
            .employeeType(ContractType.EMPLOYEE)
            .fixedRate(13000.0)
            .validUntil(LocalDate.of(2099, 1, 1))
            .country(CountryCode.VNM)
            .build()
        val pricingInput = PricingInput.newBuilder()
            .globalPricing(
                listOf(
                    CompanyGlobalPricingInput.newBuilder()
                        .employeeType(ContractType.EMPLOYEE)
                        .fixedRate(500.0)
                        .build(),
                    CompanyGlobalPricingInput.newBuilder()
                        .employeeType(ContractType.CONTRACTOR)
                        .fixedRate(1000.0)
                        .build(),
                    CompanyGlobalPricingInput.newBuilder()
                        .employeeType(ContractType.FREELANCER)
                        .fixedRate(1500.0)
                        .build()
                )
            )
            .visaGlobalPricing(
                listOf(
                    CompanyVisaGlobalPricingInput.newBuilder()
                        .employeeType(ContractType.EMPLOYEE)
                        .fixedRate(2000.0)
                        .build(),
                )
            )
            .employeePricing(
                listOf(
                    vnmEmployeePricingInput,
                    vnmContractorPricingInput,
                    vnmFreelancerPricingInput,
                )
            )
            .visaPricing(
                listOf(
                    visaVnmInput,
                )
            )
            .build()
        val existingJpaPricing = mock<JpaPricing>().apply {
            whenever(this.id).thenReturn(1L)
        }
        whenever(jpaPricingRepository.findByCompanyId(companyId)).thenReturn(existingJpaPricing)
        whenever(pricingMapper.getDiscountTermFromInput(pricingInput.discountTerms)).thenReturn(emptyList())
        val mockJpaPricing = mock<JpaPricing>()
        val mockSavedJpaPricing = mock<JpaPricing>()
        whenever(mockSavedJpaPricing.id).thenReturn(1L)
        whenever(pricingMapper.mapPricing(companyId, pricingInput, emptyList(), 1L)).thenReturn(mockJpaPricing)
        whenever(jpaPricingRepository.save(mockJpaPricing)).thenReturn(mockSavedJpaPricing)
        whenever(jpaEmployeePricingRepository.findByPricingIdAndAndType(ArgumentMatchers.eq(1L), any()))
            .thenReturn(emptyList())
        val mockVnmEmployeeJpa = mock<JpaEmployeePricing>().apply {
            whenever(this.employeeType).thenReturn(ContractType.EMPLOYEE)
            whenever(this.country).thenReturn(CountryCode.VNM)
            whenever(this.fixedRate).thenReturn(3000.0)
        }
        val mockVnmContractorJpa = mock<JpaEmployeePricing>().apply {
            whenever(this.employeeType).thenReturn(ContractType.CONTRACTOR)
            whenever(this.country).thenReturn(CountryCode.VNM)
            whenever(this.fixedRate).thenReturn(4000.0)
        }
        val mockVnmFreelancerJpa = mock<JpaEmployeePricing>().apply {
            whenever(this.employeeType).thenReturn(ContractType.FREELANCER)
            whenever(this.country).thenReturn(CountryCode.VNM)
            whenever(this.fixedRate).thenReturn(5000.0)
        }
        val mockSavedVnmEmployeeJpa = mock<JpaEmployeePricing>().apply {
            whenever(this.id).thenReturn(1L)
            whenever(this.employeeType).thenReturn(ContractType.EMPLOYEE)
            whenever(this.country).thenReturn(CountryCode.VNM)
            whenever(this.fixedRate).thenReturn(3000.0)
        }
        val mockSavedVnmContractorJpa = mock<JpaEmployeePricing>().apply {
            whenever(this.id).thenReturn(2L)
            whenever(this.employeeType).thenReturn(ContractType.CONTRACTOR)
            whenever(this.country).thenReturn(CountryCode.VNM)
            whenever(this.fixedRate).thenReturn(4000.0)
        }
        val mockSavedVnmFreelancerJpa = mock<JpaEmployeePricing>().apply {
            whenever(this.id).thenReturn(3L)
            whenever(this.employeeType).thenReturn(ContractType.FREELANCER)
            whenever(this.country).thenReturn(CountryCode.VNM)
            whenever(this.fixedRate).thenReturn(5000.0)
        }
        whenever(employeePricingMapper.buildJpaEmployeePricing(1L, vnmEmployeePricingInput))
            .thenReturn(mockVnmEmployeeJpa)
        whenever(employeePricingMapper.buildJpaEmployeePricing(1L, vnmContractorPricingInput))
            .thenReturn(mockVnmContractorJpa)
        whenever(employeePricingMapper.buildJpaEmployeePricing(1L, vnmFreelancerPricingInput))
            .thenReturn(mockVnmFreelancerJpa)
        whenever(
            jpaEmployeePricingRepository.saveAll(
                listOf(
                    mockVnmEmployeeJpa,
                    mockVnmContractorJpa,
                    mockVnmFreelancerJpa
                )
            )
        )
            .thenReturn(listOf(mockSavedVnmEmployeeJpa, mockSavedVnmContractorJpa, mockSavedVnmFreelancerJpa))
        whenever(pricingMapper.mapFromJpa(mockSavedJpaPricing))
            .thenReturn(mock())
        whenever(employeePricingMapper.map(anyList()))
            .thenReturn(
                listOf(
                    mock(),
                    mock(),
                    mock(),
                )
            )

        // When
        val result = orderFormPricingService.backfillPricingFromOrderForm(companyId, pricingInput)

        // Then
        assertThat(result).isNotNull
        verify(jpaPricingRepository).save(any())
    }
}