package com.multiplier.core.payable.expenseBill

import com.multiplier.core.payable.adapters.netsuite.models.CountryCode
import com.multiplier.payable.engine.ExceptionTranslator
import com.multiplier.payable.engine.TransactionCommand
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.gateway.BlockingTransactionGatewayMessage
import com.multiplier.payable.engine.gateway.TransactionGateway
import com.multiplier.payable.engine.transaction.ExpenseBillCommand
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.handler.ExpenseBillTransactionHandler
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import java.time.LocalDateTime
import java.util.*

@ExtendWith(MockitoExtension::class)
class ExpenseBillGenerationCommandHandlerTest {

    private lateinit var expenseBillHandler: ExpenseBillTransactionHandler
    private lateinit var transactionGateway: TransactionGateway<TransactionCommand>
    private lateinit var exceptionTranslator: ExceptionTranslator
    private lateinit var commandHandler: ExpenseBillGenerationCommandHandler

    @BeforeEach
    fun setup() {
        expenseBillHandler = mockk()
        transactionGateway = mockk()
        exceptionTranslator = mockk()
        commandHandler = ExpenseBillGenerationCommandHandler(
            expenseBillHandler = expenseBillHandler,
            transactionGateway = transactionGateway,
            exceptionTranslator = exceptionTranslator
        )
    }

    @Test
    fun `handle should process valid ExpenseBillCommand successfully`() {
        // Given
        val command = createSampleExpenseBillCommand()

        every { expenseBillHandler.handle(command) } just runs
        every { transactionGateway.fire(any()) } just runs

        // When
        commandHandler.handle(command)

        // Then
        verify { expenseBillHandler.handle(eq(command)) }
        verify { transactionGateway.fire(eq(BlockingTransactionGatewayMessage(payload = command))) }
        verify(exactly = 0) { exceptionTranslator.translate(any()) }
    }

    @Test
    fun `handle should handle exception and fire compensate command`() {
        // Given
        val command = createSampleExpenseBillCommand()
        val exception = RuntimeException("Test error")
        val translatedError = "Translated error"
        
        every { expenseBillHandler.handle(eq(command)) } throws exception
        every { exceptionTranslator.translate(eq(exception)) } returns translatedError
        every { transactionGateway.fire(any()) } just runs

        // When
        commandHandler.handle(command)

        // Then
        verify { expenseBillHandler.handle(eq(command)) }
        verify { exceptionTranslator.translate(eq(exception)) }
        verify { transactionGateway.fire(eq(BlockingTransactionGatewayMessage(
            payload = command.compensate("Error generating expense bill for $command, error = $translatedError")
        ))) }
    }

    @Test
    fun `handle should reject non-ExpenseBillCommand`() {
        // Given
        val command = InvoiceCommand(
            transactionId = UUID.randomUUID().toString(),
            transactionType = TransactionType.GP_FUNDING_INVOICE,
            companyId = 1L,
            dateRange = mockk(),
            transactionDate = LocalDateTime.now(),
            cycle = mockk()
        )
        val exception = IllegalStateException("Accept command of type ExpenseBillCommand only")
        every { exceptionTranslator.translate(any()) } returns "Invalid command type"
        every { transactionGateway.fire(any()) } just runs

        // When/Then
            commandHandler.handle(command)
        verify(exactly = 0) { expenseBillHandler.handle(any()) }
        verify(exactly = 1) { transactionGateway.fire(any()) } //  For compensate broken failure case
        verify(exactly = 1) { exceptionTranslator.translate(any()) }
    }

    @Test
    fun `rollback should process valid ExpenseBillCommand successfully`() {
        // Given
        val command = createSampleExpenseBillCommand()
        every { expenseBillHandler.rollback(command) } just runs

        // When
        commandHandler.rollback(command)

        // Then
        verify { expenseBillHandler.rollback(eq(command)) }
    }

    @Test
    fun `rollback should handle exception gracefully`() {
        // Given
        val command = createSampleExpenseBillCommand()
        every { expenseBillHandler.rollback(eq(command)) } throws RuntimeException("Test error")

        // When
        commandHandler.rollback(command)

        // Then
        verify { expenseBillHandler.rollback(eq(command)) }
    }

    @Test
    fun `rollback should reject non-ExpenseBillCommand`() {
        // Given
        every { transactionGateway.fire(any()) } just runs
        val command = InvoiceCommand(
            transactionId = UUID.randomUUID().toString(),
            transactionType = TransactionType.GP_FUNDING_INVOICE,
            companyId = 1L,
            dateRange = mockk(),
            transactionDate = LocalDateTime.now(),
            cycle = mockk()
        )
        val exception = IllegalStateException("Accept command of type ExpenseBillCommand only")
        every { exceptionTranslator.translate(eq(exception)) } returns "Invalid command type"

        // When/Then
        commandHandler.rollback(command)

        verify(exactly = 0) { expenseBillHandler.rollback(any()) }
    }

    private fun createSampleExpenseBillCommand(countryCode: CountryCode? = CountryCode.USA): ExpenseBillCommand {
        return ExpenseBillCommand(
            transactionId = UUID.randomUUID().toString(),
            transactionDate = LocalDateTime.now(),
            payrollCycleId = 123L,
            countryCode = countryCode,
            transactionType = TransactionType.VENDOR_BILL
        )
    }
}