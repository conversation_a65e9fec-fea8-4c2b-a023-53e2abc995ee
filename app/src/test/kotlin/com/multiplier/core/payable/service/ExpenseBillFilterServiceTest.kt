package com.multiplier.core.payable.service

import com.multiplier.core.payable.expenseBill.ExpenseBillLineItemType
import com.multiplier.core.payable.repository.ExpenseBillRepository
import com.multiplier.core.payable.repository.filter.ExpenseBillFilter
import com.multiplier.core.payable.repository.model.ExpenseBillStatus
import com.multiplier.core.payable.repository.model.JpaExpenseBill
import com.multiplier.core.payable.repository.specification.ExpenseBillSpecifications
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentCaptor
import org.mockito.Captor
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.jpa.domain.Specification
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@ExtendWith(MockitoExtension::class)
class ExpenseBillFilterServiceTest {

    @Mock
    private lateinit var expenseBillRepository: ExpenseBillRepository

    private lateinit var expenseBillFilterService: ExpenseBillFilterService

    @Captor
    private lateinit var specificationCaptor: ArgumentCaptor<Specification<JpaExpenseBill>>

    @BeforeEach
    fun setup() {
        expenseBillFilterService = ExpenseBillFilterService(expenseBillRepository)
    }

    @Test
    fun `findByFilter should call repository with correct specification`() {
        // Given
        val filter = ExpenseBillFilter(
            payrollCycleId = 123L,
            vendorType = ExpenseBillLineItemType.UNITED_STATES_PAYROLL
        )
        val expectedExpenseBills = listOf(
            JpaExpenseBill().apply {
                payrollCycleId = 123L
                vendorType = ExpenseBillLineItemType.UNITED_STATES_PAYROLL
            }
        )
        
        `when`(expenseBillRepository.findAll(any<Specification<JpaExpenseBill>>())).thenReturn(expectedExpenseBills)

        // When
        val result = expenseBillFilterService.findByFilter(filter)

        // Then
        verify(expenseBillRepository).findAll(capture(specificationCaptor))
        assertNotNull(specificationCaptor.value)
        assertEquals(expectedExpenseBills, result)
    }

    @Test
    fun `findByFilterWithPagination should call repository with correct specification and pageable`() {
        // Given
        val filter = ExpenseBillFilter(
            status = ExpenseBillStatus.APPROVED
        )
        val pageable = PageRequest.of(0, 10)
        val expectedPage = PageImpl(
            listOf(
                JpaExpenseBill().apply {
                    status = ExpenseBillStatus.APPROVED
                }
            ),
            pageable,
            1
        )
        
        `when`(expenseBillRepository.findAll(any<Specification<JpaExpenseBill>>(), eq(pageable))).thenReturn(expectedPage)

        // When
        val result = expenseBillFilterService.findByFilterWithPagination(filter, pageable)

        // Then
        verify(expenseBillRepository).findAll(capture(specificationCaptor), eq(pageable))
        assertNotNull(specificationCaptor.value)
        assertEquals(expectedPage, result)
    }

    @Test
    fun `findByTransactionId should call repository with correct specification`() {
        // Given
        val transactionId = "TX123"
        val expectedExpenseBills = listOf(
            JpaExpenseBill().apply {
                this.transactionId = transactionId
            }
        )
        
        `when`(expenseBillRepository.findAll(any<Specification<JpaExpenseBill>>())).thenReturn(expectedExpenseBills)

        // When
        val result = expenseBillFilterService.findByTransactionId(transactionId)

        // Then
        verify(expenseBillRepository).findAll(capture(specificationCaptor))
        assertNotNull(specificationCaptor.value)
        assertEquals(expectedExpenseBills, result)
    }

    @Test
    fun `findByFilter should handle empty results`() {
        // Given
        val filter = ExpenseBillFilter(
            transactionDateFrom = LocalDate.now(),
            transactionDateTo = LocalDate.now().plusDays(30)
        )
        
        `when`(expenseBillRepository.findAll(any<Specification<JpaExpenseBill>>())).thenReturn(emptyList())

        // When
        val result = expenseBillFilterService.findByFilter(filter)

        // Then
        verify(expenseBillRepository).findAll(any<Specification<JpaExpenseBill>>())
        assertEquals(emptyList(), result)
    }

    @Test
    fun `findByFilterWithPagination should handle empty results`() {
        // Given
        val filter = ExpenseBillFilter(
            countryCode = "US"
        )
        val pageable = PageRequest.of(0, 10)
        val emptyPage = PageImpl<JpaExpenseBill>(emptyList(), pageable, 0)
        
        `when`(expenseBillRepository.findAll(any<Specification<JpaExpenseBill>>(), eq(pageable))).thenReturn(emptyPage)

        // When
        val result = expenseBillFilterService.findByFilterWithPagination(filter, pageable)

        // Then
        verify(expenseBillRepository).findAll(any<Specification<JpaExpenseBill>>(), eq(pageable))
        assertEquals(0, result.totalElements)
        assertEquals(emptyList<JpaExpenseBill>(), result.content)
    }

    @Test
    fun `findByTransactionId should handle empty results`() {
        // Given
        val transactionId = "NONEXISTENT"
        
        `when`(expenseBillRepository.findAll(any<Specification<JpaExpenseBill>>())).thenReturn(emptyList())

        // When
        val result = expenseBillFilterService.findByTransactionId(transactionId)

        // Then
        verify(expenseBillRepository).findAll(any<Specification<JpaExpenseBill>>())
        assertEquals(emptyList(), result)
    }

    // Helper functions for Mockito argument matchers
    private fun <T> any(): T = org.mockito.Mockito.any()
    private fun <T> eq(obj: T): T = org.mockito.Mockito.eq(obj)
    private fun <T> capture(argumentCaptor: ArgumentCaptor<T>): T = argumentCaptor.capture()
}
