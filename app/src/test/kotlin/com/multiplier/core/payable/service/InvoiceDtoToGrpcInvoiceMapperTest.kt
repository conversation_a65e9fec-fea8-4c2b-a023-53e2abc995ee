package com.multiplier.core.payable.service

import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.repository.model.ExternalSystem
import com.multiplier.core.payable.repository.model.InvoiceType
import com.multiplier.payable.common.schema.GrpcDate
import com.multiplier.payable.grpc.schema.GrpcInvoice
import com.multiplier.payable.grpc.schema.GrpcInvoiceExternalSystemType
import com.multiplier.payable.grpc.schema.GrpcInvoiceStatus
import com.multiplier.payable.grpc.schema.GrpcInvoiceType
import com.multiplier.payable.types.InvoiceStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mapstruct.factory.Mappers
import java.time.LocalDate

class InvoiceDtoToGrpcInvoiceMapperTest {
    private var invoiceDtoToGrpcInvoiceMapper = Mappers.getMapper(InvoiceDtoToGrpcInvoiceMapper::class.java)

    @Test
    fun `given with invoiceDTO then should map properly`() {
        // given
        val expected =
            GrpcInvoice.newBuilder()
                .setId(1L)
                .setInvoiceId("1234")
                .setReference("sample reference")
                .setInvoiceNo("MTPLINV1234")
                .setExternalSystem(GrpcInvoiceExternalSystemType.NETSUITE)
                .setCompanyPayableId(2L)
                .setStatus(GrpcInvoiceStatus.INVOICE_STATUS_AUTHORIZED)
                .setAmountPaid(0.0)
                .setAmountDue(100.0)
                .setTotalAmount(100.0)
                .setCreatedDate(GrpcDate.newBuilder().setYear(2025).setMonth(3).setDay(31).build())
                .setType(GrpcInvoiceType.DEPOSIT_INVOICE)
                .build()

        val invoiceDto =
            InvoiceDTO.builder()
                .id(1L)
                .externalId("1234")
                .reference("sample reference")
                .invoiceNo("MTPLINV1234")
                .externalSystem(ExternalSystem.NETSUITE)
                .companyPayableId(2L)
                .status(InvoiceStatus.AUTHORIZED)
                .amountPaid(0.0)
                .amountDue(100.0)
                .totalAmount(100.0)
                .date(LocalDate.of(2025,3,31))
                .type(InvoiceType.DEPOSIT)
                .build()

        // when
        val result = invoiceDtoToGrpcInvoiceMapper.map(invoiceDto)

        // then
        assertThat(result).usingRecursiveComparison().isEqualTo(expected)
    }

    @Test
    fun whenInvoiceTypeIsNull_returnInvoiceWithDefaultValue() {
        // given
        val expected =
            GrpcInvoice.newBuilder()
                .setId(1L)
                .setInvoiceId("1234")
                .setReference("sample reference")
                .setInvoiceNo("MTPLINV1234")
                .setExternalSystem(GrpcInvoiceExternalSystemType.NETSUITE)
                .setCompanyPayableId(2L)
                .setStatus(GrpcInvoiceStatus.INVOICE_STATUS_AUTHORIZED)
                .setAmountPaid(0.0)
                .setAmountDue(100.0)
                .setTotalAmount(100.0)
                .setType(GrpcInvoiceType.UNKNOWN_INVOICE_TYPE)
                .build()

        val invoiceDto =
            InvoiceDTO.builder()
                .id(1L)
                .externalId("1234")
                .reference("sample reference")
                .invoiceNo("MTPLINV1234")
                .externalSystem(ExternalSystem.NETSUITE)
                .companyPayableId(2L)
                .status(InvoiceStatus.AUTHORIZED)
                .amountPaid(0.0)
                .amountDue(100.0)
                .totalAmount(100.0)
                .type(null)
                .build()

        // when
        val result = invoiceDtoToGrpcInvoiceMapper.map(invoiceDto)

        // then
        assertThat(result).usingRecursiveComparison().isEqualTo(expected)
    }
}
