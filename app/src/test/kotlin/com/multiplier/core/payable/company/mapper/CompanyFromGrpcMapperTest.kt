package com.multiplier.core.payable.company.mapper

import com.google.protobuf.Int32Value
import com.google.protobuf.Int64Value
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.core.payable.company.Company
import com.multiplier.core.payable.company.CompanyChannel
import com.multiplier.core.payable.company.CompanyInactiveReason
import com.multiplier.core.payable.company.CompanyUser
import com.multiplier.core.payable.pricing.DiscountRule
import com.multiplier.core.payable.pricing.DiscountRuleCountry
import com.multiplier.core.payable.pricing.DiscountRuleDeadlineType
import com.multiplier.core.schema.common.Common
import com.multiplier.payable.types.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.junit.jupiter.MockitoExtension
import kotlin.test.assertEquals

// TODO: Implement non-happy path testing
@ExtendWith(MockitoExtension::class)
class CompanyFromGrpcMapperTest {
    @InjectMocks
    lateinit var mapper: CompanyFromGrpcMapperImpl

    // test Company
    @Test
    fun `test mapCompany`() {
        // GIVEN
        val input = mockOuterCompany()
        val expectedResult = mockDtoCompany()
        // WHEN
        val actualResult = mapper.map(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test mapAddress
    @Test
    fun `test mapAddress`() {
        // GIVEN
        val input = mockOuterAddress()
        val expectedResult = mockDtoAddress()

        // WHEN
        val actualResult = mapper.mapAddress(input)

        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test mapBankAccountDetail
    @Test
    fun `test mapBankAccountDetail`() {
        // GIVEN
        val input = mockOuterBankAccountDetail()
        val expectedResult = mockDtoBankAccountDetail()
        // WHEN
        val actualResult = mapper.mapBankAccountDetail(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test mapBankAccount
    @Test
    fun `test mapBankAccount`() {
        // GIVEN
        val input = mockOuterBankAccount()
        val expectedResult = mockDtoBankAccount()

        // WHEN
        val actualResult = mapper.mapBankAccount(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test mapLegalEntity
    @Test
    fun `test mapLegalEntity`() {
        // GIVEN
        val input = mockOuterLegalEntity()
        val expectedResult = mockDtoLegalEntity()
        // WHEN
        val actualResult = mapper.mapLegalEntity(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test mapCompanyUser
    @Test
    fun `test mapCompanyUser`() {
        // GIVEN
        val input = mockOuterCompanyUser()
        val expectedResult = mockDtoCompanyUser()
        // WHEN
        val actualResult = mapper.mapCompanyUser(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test mapDiscountRule
    @Test
    fun `test mapDiscountRule`() {
        // GIVEN
        val input = mockOuterDiscountRuleCountry()
        val expectedResult  = mockDtoDiscountRuleCountry()
        // WHEN
        val actualResult: DiscountRule = mapper.mapToDiscountRule(input)
        val actualResultCast = actualResult as DiscountRuleCountry
        // THEN
        assertEquals(expectedResult.id, actualResultCast.id)
        assertEquals(expectedResult.countries, actualResultCast.countries)
        assertEquals(expectedResult.anyCountry, actualResultCast.anyCountry)
        assertEquals(expectedResult.excludedCountries, actualResultCast.excludedCountries)
    }

    // test CompanyInactiveReason
    @Test
    fun `test mapCompanyInactiveReason`() {
        // GIVEN
        val expectedResult = CompanyInactiveReason.CHURN
        val input = CompanyOuterClass.CompanyInactiveReason.CHURN
        // WHEN
        val actualResult = mapper.map(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test CompanyUserRole
    @Test
    fun `test mapCompanyUserRole`() {
        // GIVEN
        val expectedResult = CompanyUserRole.ADMIN
        val input = CompanyOuterClass.CompanyUserRole.ADMIN
        // WHEN
        val actualResult = mapper.map(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test CompanyUserCapability
    @Test
    fun `test mapCompanyUserCapability`() {
        // GIVEN
        val expectedResult = CompanyUserCapability.SIGNATORY
        val input = CompanyOuterClass.CompanyUserCapability.SIGNATORY
        // WHEN
        val actualResult = mapper.mapCompanyUserCapability(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test CountryCode
    @Test
    fun `test mapCountryCode`() {
        // GIVEN
        val expectedResult = CountryCode.USA
        val input = "USA"
        // WHEN
        val actualResult = mapper.mapCountryCode(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test CompanyChannel
    @Test
    fun `test mapCompanyChannel`() {
        // GIVEN
        val expectedResult = CompanyChannel.SALES
        val input = CompanyOuterClass.CompanyChannel.SALES
        // WHEN
        val actualResult = mapper.map(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test DiscountRuleDeadlineType
    @Test
    fun `test mapDiscountRuleDeadlineType`() {
        // GIVEN
        val expectedResult = DiscountRuleDeadlineType.MSA_SIGNED
        val input = CompanyOuterClass.DiscountRuleDeadlineType.MSA_SIGNED
        // WHEN
        val actualResult = mapper.mapDiscountRuleDeadlineType(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test PhoneNumber
    @Test
    fun `test mapPhoneNumber`() {
        // GIVEN
        val expectedResult = PhoneNumber.newBuilder().type("type").phoneNo("phoneNumber").build()
        val input = Common.PhoneNumber.newBuilder().setType("type").setPhoneNo("phoneNumber").build()
        // WHEN
        val actualResult = mapper.mapPhoneNumber(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test EmailAddress
    @Test
    fun `test mapEmailAddress`() {
        // GIVEN
        val expectedResult = EmailAddress.newBuilder().type("type").email("email").build()
        val input = Common.EmailAddress.newBuilder().setType("type").setEmail("email").build()
        // WHEN
        val actualResult = mapper.mapEmailAddress(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test CurrencyCode
    @Test
    fun `test mapCurrencyCode`() {
        // GIVEN
        val expectedResult = CurrencyCode.USD
        val input = "USD"
        // WHEN
        val actualResult = mapper.mapCurrencyCode(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test CompanyUserStatus
    @Test
    fun `test mapCompanyUserStatus`() {
        // GIVEN
        val expectedResult = CompanyUserStatus.ACTIVE
        val input = CompanyOuterClass.CompanyUserStatus.ACTIVE
        // WHEN
        val actualResult = mapper.map(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // test CompanyStatus
    @Test
    fun `test mapCompanyStatus`() {
        // GIVEN
        val expectedResult = CompanyStatus.ACTIVE
        val input = CompanyOuterClass.CompanyStatus.newBuilder()
            .setCompanyStatusEnum(CompanyOuterClass.CompanyStatus.CompanyStatusEnum.ACTIVE)
            .build()
        // WHEN
        val actualResult = mapper.map(input)
        // THEN
        assertEquals(expectedResult, actualResult)
    }

    // mock inner bank account
    private fun mockDtoBankAccount(): BankAccount? = BankAccount.newBuilder()
        .version(1)
        .accountName("accountName")
        .accountNumber("accountNumber")
        .bankName("bankName")
        .branchName("branchName")
        .currency(CurrencyCode.USD)
        .country(CountryCode.USA)
        .swiftCode("swiftCode")
        .localBankCode("localBankCode")
        .paymentAccountRequirementType("paymentAccountRequirementType")
        .accountDetails(
            listOf(
                mockDtoBankAccountDetail()
            )
        )
        .build()

    // mock outer bank account
    private fun mockOuterBankAccount(): CompanyOuterClass.BankAccount? =
        CompanyOuterClass.BankAccount.newBuilder()
            .setVersion(1)
            .setAccountName("accountName")
            .setAccountNumber("accountNumber")
            .setBankName("bankName")
            .setBranchName("branchName")
            .setCurrency("USD")
            .setCountry("USA")
            .setSwiftCode("swiftCode")
            .setLocalBankCode("localBankCode")
            .setPaymentAccountRequirementType("paymentAccountRequirementType")
            .addAllBankAccountDetail(
                listOf(
                    mockOuterBankAccountDetail()
                )
            )
            .build()

    // mock inner Company
    private fun mockDtoCompany(): Company? = Company.builder()
        .id(1)
        .financialYear(1)
        .displayName("displayName")
        .primaryEntity(mockDtoLegalEntity())
        .isTest(true)
        .companyLogoId(1)
        .companyLogo(DocumentReadable.newBuilder().id(1).build())
        .status(CompanyStatus.CREATED)
        .msa(
            DocumentReadable.newBuilder()
                .id(1)
                .build()
        )
        .channel(CompanyChannel.ORGANIC)
        .domain("domain")
        .inactiveReason(CompanyInactiveReason.CHURN)
        .billingContact(mockDtoCompanyUser())
        .sfdcAccountNo("AA123456")
        .build()

    // mock outer Company
    private fun mockOuterCompany(): CompanyOuterClass.Company? =
        CompanyOuterClass.Company.newBuilder()
            .setId(1)
            .setFinancialYear(Int32Value.newBuilder().setValue(1).build())
            .setDisplayName("displayName")
            .setPrimaryEntity(mockOuterLegalEntity())
            .setIsTest(true)
            .setCompanyLogoId(Int64Value.newBuilder().setValue(1).build())
            .setStatus(
                CompanyOuterClass.CompanyStatus.newBuilder()
                    .setCompanyStatusEnum(CompanyOuterClass.CompanyStatus.CompanyStatusEnum.CREATED)
                    .build()
            )
            .setMsaDocId(Int64Value.newBuilder().setValue(1).build())
            .setChannel(CompanyOuterClass.CompanyChannel.ORGANIC)
            .setDomain("domain")
            .setInactiveReason(CompanyOuterClass.CompanyInactiveReason.CHURN)
            .setBillingContact(mockOuterCompanyUser())
            .setSfdcAccountNo("AA123456")
            .build()

    // mock inner discount rule country
    private fun mockDtoDiscountRuleCountry() = DiscountRuleCountry.builder()
        .id(1)
        .countries(listOf(CountryCode.USA))
        .anyCountry(true)
        .excludedCountries(listOf(CountryCode.CAN))
        .build()

    // mock outer discount rule (set country)
    private fun mockOuterDiscountRuleCountry(): CompanyOuterClass.DiscountRule? =
        CompanyOuterClass.DiscountRule.newBuilder().setDiscountRuleCountry(
            CompanyOuterClass.DiscountRuleCountry.newBuilder()
                .setId(1)
                .addAllCountries(listOf("USA"))
                .setAnyCountry(true)
                .addAllExcludedCountries(listOf("CAN"))
                .build()
        ).build()

    // mock inner company user
    private fun mockDtoCompanyUser(): CompanyUser? = CompanyUser.builder()
        .id(1)
        .firstName("firstName")
        .lastName("lastName")
        .roles(listOf(CompanyUserRole.ADMIN))
        .emails(listOf(mockDtoEmailAddress()))
        .company(Company.builder().id(1).build())
        .userId("userId")
        .phoneNos(listOf(mockDtoPhoneNumber()))
        .title("title")
        .status(CompanyUserStatus.CREATED)
        .hasLogin(true)
        .capabilities(listOf(CompanyUserCapability.SIGNATORY))
        .isSignatory(true)
        .isBillingContact(true)
        .role(CompanyUserRole.ADMIN)
        .build()

    // mock outer company user
    private fun mockOuterCompanyUser() =
        CompanyOuterClass.CompanyUser.newBuilder()
            .setId(1)
            .setFirstName("firstName")
            .setLastName("lastName")
            .addAllRoles(listOf(CompanyOuterClass.CompanyUserRole.ADMIN))
            .addAllEmails(listOf(mockOuterEmailAddress()))
            .setCompanyId(1)
            .setUserId("userId")
            .addAllPhoneNos(listOf(mockOuterPhoneNumber()))
            .setTitle("title")
            .setStatus(CompanyOuterClass.CompanyUserStatus.CREATED)
            .setHasLogin(true)
            .addAllCompanyUserCapability(listOf(CompanyOuterClass.CompanyUserCapability.SIGNATORY))
            .setIsSignatory(true)
            .setIsBillingContact(true)
            .setRole(CompanyOuterClass.CompanyUserRole.ADMIN)
            .build()

    // mock inner legal entity
    private fun mockDtoLegalEntity(): LegalEntity? = LegalEntity.newBuilder()
        .address(mockDtoAddress())
        .legalName("legalName")
        .registrationNo("registrationNo")
        .currency(CurrencyCode.USD)
        .phone("phone")
        .bankAccount(mockDtoBankAccount())
        .status(LegalEntityStatus.DRAFT)
        .id(1)
        .build()

    // mock outer legal entity
    private fun mockOuterLegalEntity(): CompanyOuterClass.LegalEntity? =
        CompanyOuterClass.LegalEntity.newBuilder()
            .setAddress(mockOuterAddress())
            .setLegalName("legalName")
            .setRegistrationNo("registrationNo")
            .setCurrencyCode("USD")
            .setPhone("phone")
            .setBankAccount(mockOuterBankAccount())
            .setId(1)
            .build()

    // mock inner phone number
    private fun mockDtoPhoneNumber(): PhoneNumber? = PhoneNumber.newBuilder()
        .type("type")
        .phoneNo("phoneNo")
        .build()

    // mock outer phone number
    private fun mockOuterPhoneNumber(): Common.PhoneNumber? =
        Common.PhoneNumber.newBuilder()
            .setType("type")
            .setPhoneNo("phoneNo")
            .build()

    // mock inner email address
    private fun mockDtoEmailAddress(): EmailAddress? = EmailAddress.newBuilder()
        .type("type")
        .email("email")
        .build()

    // mock outer email
    private fun mockOuterEmailAddress(): Common.EmailAddress? =
        Common.EmailAddress.newBuilder()
            .setType("type")
            .setEmail("email")
            .build()

    private fun mockOuterBankAccountDetail(): CompanyOuterClass.BankAccountDetail? =
        CompanyOuterClass.BankAccountDetail.newBuilder()
            .setKey("key")
            .setValue("value")
            .build()

    private fun mockDtoBankAccountDetail(): BankAccountDetail? = BankAccountDetail.newBuilder()
        .key("key")
        .value("value")
        .build()

    private fun mockOuterAddress(): CompanyOuterClass.Address? =
        CompanyOuterClass.Address.newBuilder()
            .setStreet("street")
            .setKey("key")
            .setLine1("addressLine1")
            .setLine2("addressLine2")
            .setCity("city")
            .setState("state")
            .setProvince("province")
            .setCountry("USA")
            .setZipcode("zipcode")
            .setPostalCode("postalCode")
            .build()

    private fun mockDtoAddress(): Address? = Address.newBuilder()
        .street("street")
        .key("key")
        .line1("addressLine1")
        .line2("addressLine2")
        .city("city")
        .state("state")
        .province("province")
        .country(CountryCode.USA)
        .zipcode("zipcode")
        .postalCode("postalCode")
        .build()
}