package com.multiplier.core.payable.service.companypayablegeneration.blocking

import com.multiplier.payable.engine.domain.aggregates.InvoiceCycle
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.grpc.schema.GrpcGenerateCompanyPayableForIncidentsRequest
import com.multiplier.payable.grpc.schema.GrpcGenerateCompanyPayableFromEngineV2Request
import com.multiplier.payable.grpc.schema.GrpcInvoiceType
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class IncidentFinancialTransactionCommandInputBuilderTest {

    private lateinit var builder: IncidentFinancialTransactionCommandInputBuilder
    private lateinit var request: GrpcGenerateCompanyPayableFromEngineV2Request
    private val transactionId = UUID.randomUUID()
    private val companyId = 12345L

    @BeforeEach
    fun setup() {
        builder = IncidentFinancialTransactionCommandInputBuilder()
        
        // Create the incidents request according to the proto definition
        val incidentsRequest = GrpcGenerateCompanyPayableForIncidentsRequest.newBuilder()
            .setCompanyId(companyId)
            .setIncidentsTransactionId(transactionId.toString())
            .build()
            
        // Create the main request
        request = GrpcGenerateCompanyPayableFromEngineV2Request.newBuilder()
            .setInvoiceType(GrpcInvoiceType.VAS_INCIDENT_INVOICE)
            .setIncidentRequest(incidentsRequest)
            .build()
    }

    @Test
    fun `should return correct invoice type`() {
        assertEquals(GrpcInvoiceType.VAS_INCIDENT_INVOICE, builder.getType())
    }

    @Test
    fun `should build command input successfully with valid request`() {
        val result = builder.build(request)

        assertNotNull(result)
        assertEquals(TransactionType.VAS_INCIDENT_INVOICE, result.transactionType)
        assertEquals(listOf(companyId), result.companyIds)
        assertTrue(
            result.dateRange.startDate.isBefore(result.dateRange.endDate)
                || result.dateRange.startDate.isEqual(result.dateRange.endDate)
        )
        assertEquals(InvoiceCycle.MONTHLY, result.cycle)
        assertTrue(result.autoSubmit)
        assertNull(result.depositId)
        assertTrue(result.forcedContractIdsByCompanyId.isEmpty())
        assertTrue(result.companyIdToEntityIdsMap.isEmpty())
        assertNull(result.memberPayableInvoiceCommand)
        
        val incidentalCommand = result.incidentsInvoiceCommand
        assertNotNull(incidentalCommand)
        assertEquals(transactionId, incidentalCommand.transactionId)
    }

    @Test
    fun `should throw exception when incident request is null`() {
        val invalidRequest = GrpcGenerateCompanyPayableFromEngineV2Request.newBuilder()
            .setInvoiceType(GrpcInvoiceType.VAS_INCIDENT_INVOICE)
            .build()
        
        val exception = assertThrows<IllegalArgumentException> {
            builder.build(invalidRequest)
        }
        
        assertEquals("Incident Request cannot be null or blank", exception.message)
    }

    @Test
    fun `should throw exception when transaction id is null`() {
        // Create an incident request with missing transaction ID
        val incidentsRequest = GrpcGenerateCompanyPayableForIncidentsRequest.newBuilder()
            .setCompanyId(companyId)
            .build()
            
        val invalidRequest = GrpcGenerateCompanyPayableFromEngineV2Request.newBuilder()
            .setInvoiceType(GrpcInvoiceType.VAS_INCIDENT_INVOICE)
            .setIncidentRequest(incidentsRequest)
            .build()
        
        val exception = assertThrows<IllegalArgumentException> {
            builder.build(invalidRequest)
        }
        
        assertEquals("Incident transactionId cannot be null or blank", exception.message)
    }
} 