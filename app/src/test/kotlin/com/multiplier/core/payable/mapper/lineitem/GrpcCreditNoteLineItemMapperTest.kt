package com.multiplier.core.payable.mapper.lineitem

import com.multiplier.core.payable.adapters.api.LineItemType
import com.multiplier.core.payable.companypayable.database.CompanyPayableDto
import com.multiplier.core.payable.companypayable.database.CompanyPayableService
import com.multiplier.core.payable.companypayable.database.PayableItemDto
import com.multiplier.core.payable.creditnote.database.CreditNoteDto
import com.multiplier.core.payable.creditnote.database.CreditNoteItemDto
import com.multiplier.core.payable.creditnote.database.CreditNoteService
import com.multiplier.core.payable.mapper.toGrpcCurrencyCode
import com.multiplier.payable.types.CurrencyCode
import com.multiplier.payable.types.PayableItemType
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever
import java.time.LocalDate
import java.util.*
import kotlin.test.assertEquals

@ExtendWith(MockKExtension::class)
class GrpcCreditNoteLineItemMapperTest {
    @Mock
    private lateinit var creditNoteService: CreditNoteService

    @Mock
    private lateinit var companyPayableService: CompanyPayableService

    @Mock
    private lateinit var grpcLineItemTypeMapper: GrpcLineItemTypeMapper

    @InjectMocks
    private lateinit var grpcCreditNoteLineItemMapper: GrpcCreditNoteLineItemMapper

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
    }

    @Test
    fun `should mapGraphToGrpc`() {
        // given
        val creditNoteId = 1L
        val uuid = UUID.randomUUID()
        val creditNoteItemDto = CreditNoteItemDto.builder()
            .itemType(LineItemType.EOR_SALARY_DISBURSEMENT)
            .taxCode("tax-code")
            .taxAmount(10.0)
            .taxRate(10.0)
            .grossAmount(110.0)
            .unitAmount(100.0)
            .baseCurrency(CurrencyCode.USD)
            .amountInBaseCurrency(100.0)
            .fxRate(1.0)
            .contractId(1L)
            .companyPayableLineItemIds(listOf(uuid))
            .startPayCycleDate(LocalDate.now())
            .endPayCycleDate(LocalDate.now())
            .description("description")
            .quantity(1.0)
            .build()

        val creditNoteDto = CreditNoteDto.builder()
            .id(creditNoteId)
            .companyPayableId(1L)
            .items(listOf(creditNoteItemDto))
            .build()

        val payableItemDto = PayableItemDto.builder()
            .id(uuid)
            .totalCost(100.0)
            .type(PayableItemType.MEMBER_PAYROLL_COST)
            .billableCost(100.0)
            .currencyCode(CurrencyCode.USD)
            .description("description")
            .totalCost(100.0)
            .build()

        val companyPayableDto = CompanyPayableDto.builder()
            .companyId(1L)
            .items(listOf(payableItemDto))
            .currency(CurrencyCode.USD)
            .build()

        val grpcLineItemType = LineItemType.EOR_SALARY_DISBURSEMENT.toGrpc()

        whenever(creditNoteService.getById(creditNoteId)).thenReturn(creditNoteDto)
        whenever(companyPayableService.get(creditNoteDto.companyPayableId)).thenReturn(companyPayableDto)
        whenever(
            grpcLineItemTypeMapper.mapGrpcLineItemType(
                LineItemType.EOR_SALARY_DISBURSEMENT,
                listOf(PayableItemType.MEMBER_PAYROLL_COST)
            )
        ).thenReturn(
            grpcLineItemType
        )

        val grpcCreditNoteLineItems = grpcCreditNoteLineItemMapper.mapGraphToGrpc(creditNoteId)
        assertEquals(1, grpcCreditNoteLineItems.size)
        assertEquals("description", grpcCreditNoteLineItems[0].description)
        assertEquals(100.0, grpcCreditNoteLineItems[0].unitAmount.value)
        assertEquals(CurrencyCode.USD.toGrpcCurrencyCode(), grpcCreditNoteLineItems[0].unitAmount.currencyCode)
        assertEquals("10.0", grpcCreditNoteLineItems[0].tax.taxRate)
        assertEquals(10.0, grpcCreditNoteLineItems[0].tax.taxAmount.value)
        assertEquals("tax-code", grpcCreditNoteLineItems[0].tax.taxType)
        assertEquals(110.0, grpcCreditNoteLineItems[0].grossAmount.value)
        assertEquals(CurrencyCode.USD.toGrpcCurrencyCode(), grpcCreditNoteLineItems[0].grossAmount.currencyCode)
        assertEquals(1L, grpcCreditNoteLineItems[0].contractId)
        assertEquals(grpcLineItemType, grpcCreditNoteLineItems[0].lineItemType)
        assertEquals(
            CurrencyCode.USD.toGrpcCurrencyCode(),
            grpcCreditNoteLineItems[0].amountInBaseCurrency.currencyCode
        )
        assertEquals(100.0, grpcCreditNoteLineItems[0].amountInBaseCurrency.value)
        assertEquals(1.0,grpcCreditNoteLineItems[0].quantity)
    }
}