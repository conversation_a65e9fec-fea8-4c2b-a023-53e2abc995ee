package com.multiplier.core.payable.adapters.compensation

import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.payable.types.CurrencyCode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

class LegacyCompensationMapperTest {
    private val mapper = LegacyCompensationMapper()

    // Test mapping of compensation with contract-service source
    @Test
    fun `given compensation with contract-service source when map then map to legacy domain`() {
        val compensation =
            Compensation(
                contractId = 1L,
                basePay =
                    CompensationPayComponent(
                        id = "1",
                        name = "basePay",
                        amount = 1000.0,
                        currencyCode = CurrencyCode.USD,
                        category = CompensationCategory.CONTRACT_BASE_PAY,
                        frequency = RateFrequency.MONTHLY,
                        payFrequency = PayFrequency.MONTHLY,
                        rateType = RateType.GROSS,
                    ),
                otherPays = emptyList(),
                source = CompensationSource.CONTRACT_SERVICE,
            )

        val result = mapper.map(compensation)

        val expectedCompensation =
            CompensationOuterClass.Compensation
                .newBuilder()
                .setBasePay(
                    CompensationOuterClass.CompensationPayComponent
                        .newBuilder()
                        .setName("basePay")
                        .setAmount(1000.0)
                        .setCurrency("USD")
                        .setFrequency(CompensationOuterClass.RateFrequency.MONTHLY)
                        .setPayFrequency(CompensationOuterClass.PayFrequency.PAY_FREQUENCY_MONTHLY)
                        .setRateType(CompensationOuterClass.RateType.GROSS),
                ).build()

        assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields("basePay_.id_")
            .isEqualTo(expectedCompensation)
    }

    @Test
    fun `given compensation with compensation-service source when map then map to legacy domain`() {
        val compensation =
            Compensation(
                contractId = 1L,
                basePay =
                    CompensationPayComponent(
                        id = "1",
                        name = "",
                        amount = 1000.0,
                        currencyCode = CurrencyCode.USD,
                        category = CompensationCategory.CONTRACT_BASE_PAY,
                        frequency = RateFrequency.MONTHLY,
                        payFrequency = PayFrequency.MONTHLY,
                        rateType = RateType.NULL,
                    ),
                otherPays = emptyList(),
                source = CompensationSource.COMPENSATION_SERVICE,
            )

        val result = mapper.map(compensation)

        val expectedCompensation =
            CompensationOuterClass.Compensation
                .newBuilder()
                .setBasePay(
                    CompensationOuterClass.CompensationPayComponent
                        .newBuilder()
                        .setName("basePay")
                        .setAmount(1000.0)
                        .setCurrency("USD")
                        .setFrequency(CompensationOuterClass.RateFrequency.MONTHLY)
                        .setPayFrequency(CompensationOuterClass.PayFrequency.PAY_FREQUENCY_MONTHLY)
                        .setRateType(CompensationOuterClass.RateType.RATE_TYPE_NULL),
                ).build()

        assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields("basePay_.id_")
            .isEqualTo(expectedCompensation)
    }

    @Test
    fun `given compensation with otherPays when map then map all components to legacy domain`() {
        val compensation =
            Compensation(
                contractId = 1L,
                basePay =
                    CompensationPayComponent(
                        id = "1",
                        name = "basePay",
                        amount = 1000.0,
                        currencyCode = CurrencyCode.USD,
                        category = CompensationCategory.CONTRACT_BASE_PAY,
                        frequency = RateFrequency.MONTHLY,
                        payFrequency = PayFrequency.MONTHLY,
                        rateType = RateType.GROSS,
                    ),
                otherPays =
                    listOf(
                        CompensationPayComponent(
                            id = "2",
                            name = "13thMonth",
                            amount = 500.0,
                            currencyCode = CurrencyCode.USD,
                            category = CompensationCategory.CATEGORY_MONTH_PAY_13TH_14TH,
                            frequency = RateFrequency.ANNUALLY,
                            payFrequency = PayFrequency.ANNUALLY,
                            rateType = RateType.GROSS,
                        ),
                        CompensationPayComponent(
                            id = "3",
                            name = "Housing Allowance",
                            amount = 200.0,
                            currencyCode = CurrencyCode.USD,
                            category = CompensationCategory.CONTRACT_ALLOWANCE,
                            frequency = RateFrequency.MONTHLY,
                            payFrequency = PayFrequency.MONTHLY,
                            rateType = RateType.GROSS,
                        ),
                    ),
                source = CompensationSource.CONTRACT_SERVICE,
            )

        val result = mapper.map(compensation)

        // Verify base pay is mapped correctly
        assertThat(result.basePay.name).isEqualTo("basePay")
        assertThat(result.basePay.amount).isEqualTo(1000.0)

        // Verify other pays are mapped correctly
        assertThat(result.additionalPaysList).hasSize(2)

        val firstOtherPay = result.additionalPaysList[0]
        assertThat(firstOtherPay.name).isEqualTo("13thMonth")
        assertThat(firstOtherPay.amount).isEqualTo(500.0)
        assertThat(firstOtherPay.frequency).isEqualTo(CompensationOuterClass.RateFrequency.ANNUALLY)

        val secondOtherPay = result.additionalPaysList[1]
        assertThat(secondOtherPay.name).isEqualTo("Housing Allowance")
        assertThat(secondOtherPay.amount).isEqualTo(200.0)
        assertThat(secondOtherPay.frequency).isEqualTo(CompensationOuterClass.RateFrequency.MONTHLY)
    }

    // Test mapping of pay component
    @Test
    fun `given pay component when map then map to legacy domain`() {
        val payComponent =
            CompensationPayComponent(
                id = "1",
                name = "Housing Allowance",
                amount = 500.0,
                currencyCode = CurrencyCode.EUR,
                category = CompensationCategory.CONTRACT_ALLOWANCE,
                frequency = RateFrequency.MONTHLY,
                payFrequency = PayFrequency.MONTHLY,
                rateType = RateType.GROSS,
            )

        val result = mapper.map(payComponent)

        assertThat(result.name).isEqualTo("Housing Allowance")
        assertThat(result.amount).isEqualTo(500.0)
        assertThat(result.currency).isEqualTo("EUR")
        assertThat(result.frequency).isEqualTo(CompensationOuterClass.RateFrequency.MONTHLY)
        assertThat(result.payFrequency).isEqualTo(CompensationOuterClass.PayFrequency.PAY_FREQUENCY_MONTHLY)
        assertThat(result.rateType).isEqualTo(CompensationOuterClass.RateType.GROSS)
    }

    // Test name mapping logic
    @Test
    fun `given component with blank name and CONTRACT_BASE_PAY category when map then use BASE_PAY constant`() {
        val payComponent =
            CompensationPayComponent(
                id = "1",
                name = "",
                amount = 1000.0,
                currencyCode = CurrencyCode.USD,
                category = CompensationCategory.CONTRACT_BASE_PAY,
                frequency = RateFrequency.MONTHLY,
                payFrequency = PayFrequency.MONTHLY,
                rateType = RateType.GROSS,
            )

        val result = mapper.map(payComponent)

        assertThat(result.name).isEqualTo("basePay")
    }

    @Test
    fun `given component with blank name and non-base category when map then use empty string`() {
        val payComponent =
            CompensationPayComponent(
                id = "1",
                name = "",
                amount = 500.0,
                currencyCode = CurrencyCode.USD,
                category = CompensationCategory.CONTRACT_ALLOWANCE,
                frequency = RateFrequency.MONTHLY,
                payFrequency = PayFrequency.MONTHLY,
                rateType = RateType.GROSS,
            )

        val result = mapper.map(payComponent)

        assertThat(result.name).isEmpty()
    }

    @Test
    fun `given component with non-blank name when map then use provided name`() {
        val payComponent =
            CompensationPayComponent(
                id = "1",
                name = "Custom Name",
                amount = 500.0,
                currencyCode = CurrencyCode.USD,
                category = CompensationCategory.CONTRACT_ALLOWANCE,
                frequency = RateFrequency.MONTHLY,
                payFrequency = PayFrequency.MONTHLY,
                rateType = RateType.GROSS,
            )

        val result = mapper.map(payComponent)

        assertThat(result.name).isEqualTo("Custom Name")
    }

    // Test RateFrequency mapping
    @ParameterizedTest
    @MethodSource("rateFrequencyMappings")
    fun `test rate frequency mapping for all possible values`(
        domainFrequency: RateFrequency,
        expectedLegacyFrequency: CompensationOuterClass.RateFrequency,
    ) {
        // Given
        val payComponent =
            CompensationPayComponent(
                id = "1",
                name = "Test",
                amount = 1000.0,
                currencyCode = CurrencyCode.USD,
                category = CompensationCategory.CONTRACT_BASE_PAY,
                frequency = domainFrequency,
                payFrequency = PayFrequency.MONTHLY,
                rateType = RateType.GROSS,
            )

        // When
        val result = mapper.map(payComponent)

        // Then
        assertThat(result.frequency).isEqualTo(expectedLegacyFrequency)
    }

    companion object {
        @JvmStatic
        fun rateFrequencyMappings(): Stream<Arguments> =
            Stream.of(
                Arguments.of(RateFrequency.ANNUALLY, CompensationOuterClass.RateFrequency.ANNUALLY),
                Arguments.of(RateFrequency.SEMIANNUALLY, CompensationOuterClass.RateFrequency.HALFYEARLY),
                Arguments.of(RateFrequency.QUARTERLY, CompensationOuterClass.RateFrequency.QUATERLY),
                Arguments.of(RateFrequency.MONTHLY, CompensationOuterClass.RateFrequency.MONTHLY),
                Arguments.of(RateFrequency.SEMIMONTHLY, CompensationOuterClass.RateFrequency.SEMIMONTHLY),
                Arguments.of(RateFrequency.BIWEEKLY, CompensationOuterClass.RateFrequency.BI_WEEKLY),
                Arguments.of(RateFrequency.WEEKLY, CompensationOuterClass.RateFrequency.WEEKLY),
                Arguments.of(RateFrequency.DAILY, CompensationOuterClass.RateFrequency.DAILY),
                Arguments.of(RateFrequency.HOURLY, CompensationOuterClass.RateFrequency.HOURLY),
                Arguments.of(RateFrequency.ONETIME, CompensationOuterClass.RateFrequency.ONCE),
                Arguments.of(RateFrequency.BI_MONTHLY, CompensationOuterClass.RateFrequency.BI_MONTHLY),
                Arguments.of(RateFrequency.TRI_ANNUALLY, CompensationOuterClass.RateFrequency.TRI_ANNUALLY),
                Arguments.of(RateFrequency.CUSTOM, CompensationOuterClass.RateFrequency.CUSTOM),
            )
    }

    // Test PayFrequency mapping
    @Test
    fun `test monthly pay frequency mapping`() {
        val payComponent =
            CompensationPayComponent(
                id = "1",
                name = "Test",
                amount = 1000.0,
                currencyCode = CurrencyCode.USD,
                category = CompensationCategory.CONTRACT_BASE_PAY,
                frequency = RateFrequency.MONTHLY,
                payFrequency = PayFrequency.MONTHLY,
                rateType = RateType.GROSS,
            )

        val result = mapper.map(payComponent)

        assertThat(result.payFrequency).isEqualTo(CompensationOuterClass.PayFrequency.PAY_FREQUENCY_MONTHLY)
    }

    @Test
    fun `test unsupported pay frequency mapping`() {
        val payComponent =
            CompensationPayComponent(
                id = "1",
                name = "Test",
                amount = 1000.0,
                currencyCode = CurrencyCode.USD,
                category = CompensationCategory.CONTRACT_BASE_PAY,
                frequency = RateFrequency.MONTHLY,
                payFrequency = PayFrequency.ANNUALLY,
                rateType = RateType.GROSS,
            )

        val result = mapper.map(payComponent)

        assertThat(result.payFrequency).isEqualTo(CompensationOuterClass.PayFrequency.PAY_FREQUENCY_NULL)
    }

    // Test RateType mapping
    @Test
    fun `test gross rate type mapping`() {
        val payComponent =
            CompensationPayComponent(
                id = "1",
                name = "Test",
                amount = 1000.0,
                currencyCode = CurrencyCode.USD,
                category = CompensationCategory.CONTRACT_BASE_PAY,
                frequency = RateFrequency.MONTHLY,
                payFrequency = PayFrequency.MONTHLY,
                rateType = RateType.GROSS,
            )

        val result = mapper.map(payComponent)

        assertThat(result.rateType).isEqualTo(CompensationOuterClass.RateType.GROSS)
    }

    @Test
    fun `test null rate type mapping`() {
        val payComponent =
            CompensationPayComponent(
                id = "1",
                name = "Test",
                amount = 1000.0,
                currencyCode = CurrencyCode.USD,
                category = CompensationCategory.CONTRACT_BASE_PAY,
                frequency = RateFrequency.MONTHLY,
                payFrequency = PayFrequency.MONTHLY,
                rateType = RateType.NULL,
            )

        val result = mapper.map(payComponent)

        assertThat(result.rateType).isEqualTo(CompensationOuterClass.RateType.RATE_TYPE_NULL)
    }
}
