package com.multiplier.core.payable.adapters

import com.google.protobuf.Empty
import com.google.protobuf.Int32Value
import com.google.protobuf.Timestamp
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass.GetCompanyMsaSignedDateResponse
import com.multiplier.company.schema.grpc.CompanyServiceGrpc
import com.multiplier.core.payable.company.Company
import com.multiplier.core.payable.company.CompanyUser
import com.multiplier.core.payable.company.adapter.DefaultCompanyServiceAdapter
import com.multiplier.core.payable.company.mapper.CompanyFromGrpcMapper
import com.multiplier.core.payable.company.mapper.GrpcPricingInputFromGraphMapper
import com.multiplier.core.payable.service.CompanyGraphToServiceMapper
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDate
import kotlin.test.assertEquals

// TODO: Implement non-happy path testing
@ExtendWith(MockitoExtension::class)
class CompanyServiceAdapterTest {
    @Mock
    private lateinit var blockingStub: CompanyServiceGrpc.CompanyServiceBlockingStub

    @Mock
    lateinit var companyGraphToServiceMapper: CompanyGraphToServiceMapper

    @Mock
    private lateinit var companyFromGrpcMapper: CompanyFromGrpcMapper

    @Mock
    private lateinit var grpcPricingInputFromGraphMapper: GrpcPricingInputFromGraphMapper

    @InjectMocks
    private lateinit var companyServiceAdapter: DefaultCompanyServiceAdapter

    @Nested
    inner class RecordMSADataChanged {
        @Test
        fun `when send correct request return correct data`() {
            // GIVEN
            val request =
                CompanyOuterClass.RecordMSADataChangedRequest.newBuilder()
                    .setCompanyId(1)
                    .setHasDataChanged(true)
                    .build()
            whenever(blockingStub.recordMSADataChanged(request)).thenReturn(Empty.newBuilder().build())
            val expectedResult = Unit

            // WHEN
            val actualResult = companyServiceAdapter.recordMSADataChanged(1, true)

            // THEN
            assertEquals(expectedResult, actualResult) // assert final return
            verify(
                blockingStub,
            ).recordMSADataChanged(request) // assert request sent through stub is true to what was created before sending
        }
    }

    @Nested
    inner class GetReadableDiscount {
        // extract variable in setup become property
        private lateinit var discountRuleCountry: CompanyOuterClass.DiscountRule
        private lateinit var discountRuleDeadlineBased: CompanyOuterClass.DiscountRule
        private lateinit var request: com.multiplier.company.schema.grpc.CompanyOuterClass.GetReadableDiscountRequest

        // setup data for this test before all
        @BeforeEach
        fun setup() {
            discountRuleCountry =
                CompanyOuterClass.DiscountRule.newBuilder()
                    .setDiscountRuleCountry(CompanyOuterClass.DiscountRuleCountry.newBuilder().build())
                    .build()
            discountRuleDeadlineBased =
                CompanyOuterClass.DiscountRule.newBuilder()
                    .setDiscountRuleDeadlineBased(CompanyOuterClass.DiscountRuleDeadlineBased.newBuilder().build())
                    .build()
            request =
                CompanyOuterClass.GetReadableDiscountRequest.newBuilder()
                    .setId(1)
                    .setDiscount(1.0)
                    .setDiscountType(CompanyOuterClass.DiscountType.ABSOLUTE)
                    .addAllDiscountRules(
                        listOf(
                            discountRuleCountry,
                            discountRuleDeadlineBased,
                        ),
                    )
                    .build()
        }

        // TODO: fix logic when have time
        @Test
        fun `when send correct request return correct data`() {
            // GIVEN
//            whenever(blockingStub.getReadableDiscount(request)).thenReturn(com.google.protobuf.StringValue.newBuilder().setValue("expected_call_result").build())
//            whenever(payableSchemaToCompanyOuterMapper.mapDiscountTypeToOuter(DiscountType.ABSOLUTE)).thenReturn(CompanyOuterClass.DiscountType.ABSOLUTE)
//            whenever(payableSchemaToCompanyOuterMapper.mapDiscountRuleToOuter(DiscountRuleCountry.newBuilder().build())).thenReturn(CompanyOuterClass.DiscountRule.newBuilder().setDiscountRuleCountry(CompanyOuterClass.DiscountRuleCountry.newBuilder().build()).build())
//            whenever(payableSchemaToCompanyOuterMapper.mapDiscountRuleToOuter(DiscountRuleDeadlineBased.newBuilder().build())).thenReturn(CompanyOuterClass.DiscountRule.newBuilder().setDiscountRuleDeadlineBased(CompanyOuterClass.DiscountRuleDeadlineBased.newBuilder().build()).build())
//            whenever(companyGraphToServiceMapper.mapToDiscountRule(DiscountRuleCountry.newBuilder().build())).thenReturn(com.multiplier.core.payable.pricing.DiscountRuleCountry.builder().build())
//            whenever(companyGraphToServiceMapper.mapToDiscountRule(DiscountRuleDeadlineBased.newBuilder().build())).thenReturn(com.multiplier.core.payable.pricing.DiscountRuleDeadlineBased.builder().build())
//
//            val expectedResult = "expected_call_result"
//
//            // WHEN
//            val actualResult = companyServiceAdapter.getReadableDiscount(
//                DiscountTerm.newBuilder()
//                    .id(1)
//                    .discount(1.0)
//                    .discountType(DiscountType.ABSOLUTE)
//                    .discountRules(listOf(
//                        DiscountRuleCountry.newBuilder().build(),
//                        DiscountRuleDeadlineBased.newBuilder().build()))
//                .build())
//
//            // THEN
//            assertEquals(expectedResult, actualResult) // assert final return
//            verify(blockingStub).getReadableDiscount(request) // assert request sent through stub is true to what was created before sending
        }
    }

    @Nested
    inner class GetCompanyById {
        @Test
        fun `when send correct request return correct data`() {
            // GIVEN
            val request =
                CompanyOuterClass.GetCompanyByIdRequest.newBuilder()
                    .setId(1)
                    .build()
            whenever(blockingStub.getCompanyById(request)).thenReturn(CompanyOuterClass.Company.newBuilder().setId(1).build())
            whenever(
                companyFromGrpcMapper.map(CompanyOuterClass.Company.newBuilder().setId(1).build()),
            ).thenReturn(Company.builder().id(1).build())
            val expectedResult = Company.builder().id(1).build()

            // WHEN
            val actualResult = companyServiceAdapter.getCompanyById(1)

            // THEN
            assertEquals(expectedResult, actualResult) // assert final return
            verify(blockingStub).getCompanyById(request) // assert request sent through stub is true to what was created before sending
        }
    }

    @Nested
    inner class GetCompanyByIdWithoutException {
        @Test
        fun `when send correct request return correct data`() {
            // GIVEN
            val request =
                CompanyOuterClass.GetCompanyByIdWithoutExceptionRequest.newBuilder()
                    .setId(1)
                    .build()
            whenever(blockingStub.getCompanyByIdWithoutException(request)).thenReturn(CompanyOuterClass.Company.newBuilder().build())
            whenever(companyFromGrpcMapper.map(CompanyOuterClass.Company.newBuilder().build())).thenReturn(Company.builder().build())
            val expectedResult = Company.builder().build()

            // WHEN
            val actualResult = companyServiceAdapter.getCompanyByIdWithoutException(1)

            // THEN
            assertEquals(expectedResult, actualResult) // assert final return
            verify(
                blockingStub,
            ).getCompanyByIdWithoutException(request) // assert request sent through stub is true to what was created before sending
        }
    }

    @Nested
    inner class GetCompanies {
        private lateinit var company1Outer: CompanyOuterClass.Company
        private lateinit var company2Outer: CompanyOuterClass.Company
        private lateinit var company1: Company
        private lateinit var company2: Company

        @BeforeEach
        fun setup() {
            company1Outer = CompanyOuterClass.Company.newBuilder().setId(1).build()
            company2Outer = CompanyOuterClass.Company.newBuilder().setId(2).build()
            company1 = Company.builder().id(1).build()
            company2 = Company.builder().id(2).build()
        }

        @Test
        fun `when send correct request return correct data`() {
            // GIVEN
            val request =
                CompanyOuterClass.GetCompaniesRequest.newBuilder()
                    .addAllIds(listOf(1L, 2L))
                    .build()
            whenever(blockingStub.getCompanies(request)).thenReturn(
                CompanyOuterClass.Companies.newBuilder()
                    .addAllCompanies(listOf(company1Outer, company2Outer))
                    .build(),
            )
            whenever(companyFromGrpcMapper.map(company1Outer)).thenReturn(company1)
            whenever(companyFromGrpcMapper.map(company2Outer)).thenReturn(company2)
            val expectedResult = listOf(company1, company2)

            // WHEN
            val actualResult = companyServiceAdapter.getCompanies(setOf(1, 2))

            // THEN
            assertEquals(expectedResult, actualResult) // assert final return
            verify(blockingStub).getCompanies(request) // assert request sent through stub is true to what was created before sending
        }
    }

    @Nested
    inner class GetActiveCompanies {
        private lateinit var company1Outer: CompanyOuterClass.Company
        private lateinit var company2Outer: CompanyOuterClass.Company
        private lateinit var company1: Company
        private lateinit var company2: Company

        @BeforeEach
        fun setup() {
            company1Outer = CompanyOuterClass.Company.newBuilder().setId(1).build()
            company2Outer = CompanyOuterClass.Company.newBuilder().setId(2).build()
            company1 = Company.builder().id(1).build()
            company2 = Company.builder().id(2).build()
        }

        @Test
        fun `when send correct request return correct data`() {
            // GIVEN
            val request = Empty.newBuilder().build()
            whenever(blockingStub.getActiveCompanies(request)).thenReturn(
                CompanyOuterClass.Companies.newBuilder()
                    .addAllCompanies(listOf(company1Outer, company2Outer))
                    .build(),
            )
            // write company map
            whenever(companyFromGrpcMapper.map(company1Outer)).thenReturn(company1)
            whenever(companyFromGrpcMapper.map(company2Outer)).thenReturn(company2)
            val expectedResult = listOf(company1, company2)

            // WHEN
            val actualResult = companyServiceAdapter.getActiveCompanies()

            // THEN
            assertEquals(expectedResult, actualResult) // assert final return
            verify(blockingStub).getActiveCompanies(request) // assert request sent through stub is true to what was created before sending
        }
    }

    @Nested
    inner class GetActiveCompaniesByIds {
        // setup before all
        private lateinit var company1Outer: CompanyOuterClass.Company
        private lateinit var company2Outer: CompanyOuterClass.Company
        private lateinit var company1: Company
        private lateinit var company2: Company

        @BeforeEach
        fun setup() {
            company1Outer = CompanyOuterClass.Company.newBuilder().setId(1).build()
            company2Outer = CompanyOuterClass.Company.newBuilder().setId(2).build()
            company1 = Company.builder().id(1).build()
            company2 = Company.builder().id(2).build()
        }

        @Test
        fun `when send correct request return correct data`() {
            // GIVEN
            val request =
                CompanyOuterClass.GetActiveCompaniesByIdsRequest.newBuilder()
                    .addAllIds(listOf(1L, 2L))
                    .build()
            whenever(blockingStub.getActiveCompaniesByIds(request)).thenReturn(
                CompanyOuterClass.Companies.newBuilder()
                    .addAllCompanies(listOf(company1Outer, company2Outer))
                    .build(),
            )
            whenever(companyFromGrpcMapper.map(company1Outer)).thenReturn(company1)
            whenever(companyFromGrpcMapper.map(company2Outer)).thenReturn(company2)
            val expectedResult = listOf(company1, company2)

            // WHEN
            val actualResult = companyServiceAdapter.getActiveCompaniesByIds(setOf(1, 2))

            // THEN
            assertEquals(expectedResult, actualResult) // assert final return
            verify(
                blockingStub,
            ).getActiveCompaniesByIds(request) // assert request sent through stub is true to what was created before sending
        }
    }

    @Nested
    inner class GetCompanyAdmins {
        private lateinit var companyUser1Outer: CompanyOuterClass.CompanyUser
        private lateinit var companyUser2Outer: CompanyOuterClass.CompanyUser
        private lateinit var companyUser1: CompanyUser
        private lateinit var companyUser2: CompanyUser
        private lateinit var companyUser3Outer: CompanyOuterClass.CompanyUser
        private lateinit var companyUser3: CompanyUser
        private lateinit var companyUser4Outer: CompanyOuterClass.CompanyUser
        private lateinit var companyUser4: CompanyUser

        // setup before all with above users
        @BeforeEach
        fun setup() {
            // setup for company user 1 and 2 with company id 1
            companyUser1Outer =
                CompanyOuterClass.CompanyUser.newBuilder()
                    .setId(1)
                    .setCompanyId(1)
                    .build()
            companyUser2Outer =
                CompanyOuterClass.CompanyUser.newBuilder().setId(2)
                    .setCompanyId(1)
                    .build()
            companyUser1 =
                CompanyUser.builder().id(1)
                    .company(Company.builder().id(1).build())
                    .build()
            companyUser2 =
                CompanyUser.builder().id(2)
                    .company(Company.builder().id(1).build())
                    .build()
            // setup similarly for company user 3 and 4 but with company id 2
            companyUser3Outer =
                CompanyOuterClass.CompanyUser.newBuilder().setId(3)
                    .setCompanyId(2)
                    .build()
            companyUser3 =
                CompanyUser.builder().id(3)
                    .company(Company.builder().id(2).build())
                    .build()
            companyUser4Outer =
                CompanyOuterClass.CompanyUser.newBuilder().setId(4)
                    .setCompanyId(2)
                    .build()
            companyUser4 =
                CompanyUser.builder().id(4)
                    .company(Company.builder().id(2).build())
                    .build()
        }

        @Test
        fun `when send correct request return correct data`() {
            // complete the function
            val request = CompanyOuterClass.GetCompanyRequest.newBuilder().setId(1).build()
            whenever(blockingStub.getCompanyAdmins(request)).thenReturn(
                CompanyOuterClass.CompanyUsers.newBuilder()
                    .addAllUsers(listOf(companyUser1Outer, companyUser2Outer))
                    .build(),
            )
            whenever(companyFromGrpcMapper.mapCompanyUser(companyUser1Outer)).thenReturn(companyUser1)
            whenever(companyFromGrpcMapper.mapCompanyUser(companyUser2Outer)).thenReturn(companyUser2)
            // create expected result of company user 1 and 2
            val expectedResult = listOf(companyUser1, companyUser2)

            // WHEN
            val actualResult = companyServiceAdapter.getCompanyAdmins(1)

            // THEN
            assertEquals(expectedResult, actualResult) // assert final return
            verify(blockingStub).getCompanyAdmins(request) // assert request sent through stub is true to what was created before sending
        }
    }

    @Nested
    inner class IsTestCompany {
        @Test
        fun `when send correct request return correct data`() {
            // GIVEN
            val request =
                CompanyOuterClass.GetCompanyRequest.newBuilder()
                    .setId(1)
                    .build()
            whenever(blockingStub.isTestCompany(request)).thenReturn(
                CompanyOuterClass.IsTestCompanyResponse.newBuilder()
                    .setIsTest(true).build(),
            )
            val expectedResult = true

            // WHEN
            val actualResult = companyServiceAdapter.isTestCompany(1)

            // THEN
            assertEquals(expectedResult, actualResult) // assert final return
            verify(blockingStub).isTestCompany(request) // assert request sent through stub is true to what was created before sending
        }
    }

    @Nested
    inner class GetAllCompanyBillingContactsOrderByLastBillingRoleUpdateDesc {
        private lateinit var companyUser1Outer: CompanyOuterClass.CompanyUser
        private lateinit var companyUser2Outer: CompanyOuterClass.CompanyUser
        private lateinit var companyUser1: CompanyUser
        private lateinit var companyUser2: CompanyUser

        @BeforeEach
        fun setup() {
            // setup for company user 1 and 2 with company id 1
            companyUser1Outer =
                CompanyOuterClass.CompanyUser.newBuilder()
                    .setId(1)
                    .setCompanyId(1)
                    .build()
            companyUser2Outer =
                CompanyOuterClass.CompanyUser.newBuilder().setId(2)
                    .setCompanyId(1)
                    .build()
            companyUser1 =
                CompanyUser.builder().id(1)
                    .company(Company.builder().id(1).build())
                    .build()
            companyUser2 =
                CompanyUser.builder().id(2)
                    .company(Company.builder().id(1).build())
                    .build()
        }

        @Test
        fun `when send correct request return correct data`() {
            // GIVEN
            val request =
                CompanyOuterClass.GetCompanyRequest.newBuilder()
                    .setId(1)
                    .build()
            val resp =
                CompanyOuterClass.CompanyUsers.newBuilder()
                    .addAllUsers(listOf(companyUser1Outer, companyUser2Outer))
                    .build()

            whenever(blockingStub.getAllCompanyBillingContactsOrderByLastBillingRoleUpdateDesc(request)).thenReturn(resp)
            whenever(companyFromGrpcMapper.mapCompanyUser(companyUser1Outer)).thenReturn(companyUser1)
            whenever(companyFromGrpcMapper.mapCompanyUser(companyUser2Outer)).thenReturn(companyUser2)

            val expectedResult = listOf(companyUser1, companyUser2)

            // WHEN
            val actualResult = companyServiceAdapter.getAllCompanyBillingContactsOrderByLastBillingRoleUpdateDesc(1)

            // THEN
            assertEquals(expectedResult, actualResult) // assert final return
            verify(
                blockingStub,
            ).getAllCompanyBillingContactsOrderByLastBillingRoleUpdateDesc(
                request,
            ) // assert request sent through stub is true to what was created before sending
        }
    }

    @Nested
    inner class GetAllCompanyAdmins {
        private lateinit var companyUser1Outer: CompanyOuterClass.CompanyUser
        private lateinit var companyUser2Outer: CompanyOuterClass.CompanyUser
        private lateinit var companyUser1: CompanyUser
        private lateinit var companyUser2: CompanyUser

        @BeforeEach
        fun setup() {
            // setup for company user 1 and 2 with company id 1
            companyUser1Outer =
                CompanyOuterClass.CompanyUser.newBuilder()
                    .setId(1)
                    .setCompanyId(1)
                    .build()
            companyUser2Outer =
                CompanyOuterClass.CompanyUser.newBuilder().setId(2)
                    .setCompanyId(1)
                    .build()
            companyUser1 =
                CompanyUser.builder().id(1)
                    .company(Company.builder().id(1).build())
                    .build()
            companyUser2 =
                CompanyUser.builder().id(2)
                    .company(Company.builder().id(1).build())
                    .build()
        }

        @Test
        fun `when send correct request return correct data`() {
            // GIVEN
            val request = CompanyOuterClass.GetCompanyRequest.newBuilder().setId(1).build()
            val resp =
                CompanyOuterClass.CompanyUsers.newBuilder()
                    .addAllUsers(listOf(companyUser1Outer, companyUser2Outer))
                    .build()
            whenever(blockingStub.getAllCompanyAdmins(request)).thenReturn(resp)
            whenever(companyFromGrpcMapper.mapCompanyUser(companyUser1Outer)).thenReturn(companyUser1)
            whenever(companyFromGrpcMapper.mapCompanyUser(companyUser2Outer)).thenReturn(companyUser2)
            val expectedResult = listOf(companyUser1, companyUser2)

            // WHEN
            val actualResult = companyServiceAdapter.getAllCompanyAdmins(1)

            // THEN
            assertEquals(expectedResult, actualResult) // assert final return
            verify(blockingStub).getAllCompanyAdmins(request) // assert request sent through stub is true to what was created before sending
        }
    }

    @Test
    fun `given with company id then should retrieve msa sign date`() {
        // given
        val request =
            CompanyOuterClass.GetCompanyMsaSignedDateInput.newBuilder()
                .setCompanyId(1L)
                .build()

        val response =
            GetCompanyMsaSignedDateResponse.newBuilder()
                .setDate(Timestamp.getDefaultInstance())
                .build()

        Mockito.`when`(blockingStub.getCompanyMsaSignedDate(request)).thenReturn(response)
        Mockito.`when`(companyFromGrpcMapper.mapLocalDate(Timestamp.getDefaultInstance())).thenReturn(LocalDate.MAX)
        // when

        val result = companyServiceAdapter.getCompanyMsaSignedDate(1L)

        // then
        assertEquals(LocalDate.MAX, result)
    }
}
