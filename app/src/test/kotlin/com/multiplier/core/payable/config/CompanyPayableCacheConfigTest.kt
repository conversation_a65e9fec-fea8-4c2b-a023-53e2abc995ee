package com.multiplier.core.payable.config

import com.google.common.cache.LoadingCache
import com.multiplier.core.payable.companypayable.database.CompanyPayableDto
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import java.util.Optional

@ExtendWith(MockitoExtension::class)
class CompanyPayableCacheConfigTest {

    private lateinit var config: CompanyPayableCacheConfig

    @BeforeEach
    fun setUp() {
        config = CompanyPayableCacheConfig()
    }

    @Test
    fun `should create companyPayableCache bean with correct configuration`() {
        // WHEN
        val cache = config.companyPayableCache()

        // THEN
        assertThat(cache).isNotNull
        assertThat(cache).isInstanceOf(LoadingCache::class.java)
    }

    @Test
    fun `should configure cache with 2 minutes expiration time`() {
        // WHEN
        val cache = config.companyPayableCache()

        // THEN
        // We can't directly access the expiration time from Guava cache,
        // but we can verify the cache was created successfully
        assertThat(cache).isNotNull
        
        // Test that cache works by putting and getting a value
        val testKey = "test-key"
        val testValue = createTestCompanyPayableDto()
        cache.put(testKey, Optional.of(testValue))

        val retrievedValue = cache.getIfPresent(testKey)
        assertThat(retrievedValue).isEqualTo(Optional.of(testValue))
    }

    @Test
    fun `should configure cache with maximum size of 1000`() {
        // WHEN
        val cache = config.companyPayableCache()

        // THEN
        assertThat(cache).isNotNull
        
        // Test that we can add multiple entries (up to reasonable limit for testing)
        for (i in 1..10) {
            cache.put("key-$i", Optional.of(createTestCompanyPayableDto(id = i.toLong())))
        }
        
        // Verify all entries are present
        for (i in 1..10) {
            assertThat(cache.getIfPresent("key-$i")).isNotNull
        }
    }

    @Test
    fun `should return empty optional when cache loader is called`() {
        // GIVEN
        val cache = config.companyPayableCache()
        val testKey = "non-existent-key"

        // WHEN
        val result = cache.get(testKey)

        // THEN
        // Cache loader now returns Optional.empty() instead of null
        assertThat(result).isEqualTo(Optional.empty<CompanyPayableDto>())
        assertThat(result.isPresent).isFalse()
    }

    @Test
    fun `should handle multiple cache misses correctly`() {
        // GIVEN
        val cache = config.companyPayableCache()
        val keys = listOf("key1", "key2", "key3")

        // WHEN & THEN
        keys.forEach { key ->
            val result = cache.get(key)
            assertThat(result).isEqualTo(Optional.empty<CompanyPayableDto>())
            assertThat(result.isPresent).isFalse()
        }
    }

    @Test
    fun `should allow manual cache population and retrieval`() {
        // GIVEN
        val cache = config.companyPayableCache()
        val testKey = "manual-key"
        val testDto = createTestCompanyPayableDto()

        // WHEN
        cache.put(testKey, Optional.of(testDto))
        val retrievedDto = cache.getIfPresent(testKey)

        // THEN
        assertThat(retrievedDto).isNotNull
        assertThat(retrievedDto).isEqualTo(Optional.of(testDto))
        assertThat(retrievedDto?.get()?.id).isEqualTo(testDto.id)
        assertThat(retrievedDto?.get()?.companyId).isEqualTo(testDto.companyId)
    }

    @Test
    fun `should allow empty optional values in cache`() {
        // GIVEN
        val cache = config.companyPayableCache()
        val testKey = "empty-optional-key"

        // WHEN
        cache.put(testKey, Optional.empty())
        val retrievedValue = cache.getIfPresent(testKey)

        // THEN
        // Optional.empty() is allowed and should be retrievable
        assertThat(retrievedValue).isNotNull
        assertThat(retrievedValue).isEqualTo(Optional.empty<CompanyPayableDto>())
        assertThat(retrievedValue?.isPresent).isFalse()
    }

    @Test
    fun `should invalidate cache entries correctly`() {
        // GIVEN
        val cache = config.companyPayableCache()
        val testKey = "invalidation-key"
        val testDto = createTestCompanyPayableDto()

        // WHEN
        cache.put(testKey, Optional.of(testDto))
        assertThat(cache.getIfPresent(testKey)).isNotNull

        cache.invalidate(testKey)
        val retrievedAfterInvalidation = cache.getIfPresent(testKey)

        // THEN
        assertThat(retrievedAfterInvalidation).isNull()
    }

    @Test
    fun `should clear all cache entries`() {
        // GIVEN
        val cache = config.companyPayableCache()
        val entries = mapOf(
            "key1" to createTestCompanyPayableDto(id = 1L),
            "key2" to createTestCompanyPayableDto(id = 2L),
            "key3" to createTestCompanyPayableDto(id = 3L)
        )

        // WHEN
        entries.forEach { (key, value) -> cache.put(key, Optional.of(value)) }
        
        // Verify entries are present
        entries.keys.forEach { key ->
            assertThat(cache.getIfPresent(key)).isNotNull
        }

        cache.invalidateAll()

        // THEN
        entries.keys.forEach { key ->
            assertThat(cache.getIfPresent(key)).isNull()
        }
    }

    private fun createTestCompanyPayableDto(id: Long = 42L): CompanyPayableDto {
        return CompanyPayableDto.builder()
            .id(id)
            .companyId(123L)
            .totalAmount(1000.0)
            .build()
    }
}
