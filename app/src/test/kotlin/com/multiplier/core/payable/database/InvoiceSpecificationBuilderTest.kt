package com.multiplier.core.payable.database


import com.multiplier.core.payable.invoice.database.DateRange
import com.multiplier.core.payable.invoice.database.InvoiceExcludeCriteria
import com.multiplier.core.payable.invoice.database.InvoiceFilters
import com.multiplier.core.payable.invoice.database.InvoiceSpecificationBuilder
import com.multiplier.core.payable.repository.model.InvoiceType
import com.multiplier.payable.types.InvoiceStatus
import com.multiplier.core.payable.repository.model.JpaInvoice
import jakarta.persistence.criteria.*
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.*
import jakarta.persistence.criteria.CriteriaBuilder.In
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

class InvoiceSpecificationBuilderTest {

    private lateinit var invoiceSpecificationBuilder: InvoiceSpecificationBuilder
    private lateinit var root: Root<JpaInvoice>
    private lateinit var criteriaBuilder: CriteriaBuilder
    private lateinit var typePath: Path<InvoiceType>
    private lateinit var statusPath: Path<InvoiceStatus>
    private lateinit var dueDatePath: Path<LocalDateTime>
    private lateinit var typeInPredicate: In<InvoiceType>
    private lateinit var statusInPredicate: In<InvoiceStatus>
    private lateinit var typeNotInPredicate: Predicate
    private lateinit var betweenPredicate: Predicate



    @BeforeEach
    fun setUp() {
        invoiceSpecificationBuilder = InvoiceSpecificationBuilder()
        root = mock(Root::class.java) as Root<JpaInvoice>
        criteriaBuilder = mock(CriteriaBuilder::class.java)
        typePath = mock(Path::class.java) as Path<InvoiceType>
        dueDatePath = mock(Path::class.java) as Path<LocalDateTime>
        statusPath = mock(Path::class.java) as Path<InvoiceStatus>

        typeInPredicate = mock(In::class.java) as In<InvoiceType>
        statusInPredicate = mock(In::class.java) as In<InvoiceStatus>
        typeNotInPredicate = mock(Predicate::class.java)
        betweenPredicate = mock(Predicate::class.java)

        `when`(root.get<InvoiceType>("type")).thenReturn(typePath)
        `when`(root.get<InvoiceStatus>("status")).thenReturn(statusPath)
        `when`(root.get<LocalDateTime>("dueDate")).thenReturn(dueDatePath)
        `when`(criteriaBuilder.`in`(typePath)).thenReturn(typeInPredicate)
        `when`(criteriaBuilder.`in`(statusPath)).thenReturn(statusInPredicate)
        `when`(root.get<InvoiceType>("type").`in`(anyCollection<InvoiceType>())).thenReturn(typeInPredicate)
        `when`(criteriaBuilder.not(typeInPredicate)).thenReturn(typeNotInPredicate)
        `when`(
            criteriaBuilder.between(
                eq(dueDatePath),
                eq(LocalDate.of(2024, 1, 1).atTime(LocalTime.MIN)),
                eq(LocalDate.of(2024, 1, 31).atTime(LocalTime.MAX))
            )
        ).thenReturn(betweenPredicate)
    }


    @Test
    fun `should create specification with correct invoice types`() {
        val filters = InvoiceFilters(
            invoiceTypes = setOf(InvoiceType.GP_SERVICE_INVOICE, InvoiceType.GLOBAL_PAYROLL_FUNDING),
            companyId = null,
            dueDate = null,
            invoiceStatuses = setOf(InvoiceStatus.AUTHORIZED),
            inclusionCriteria = null,
            exclusionCriteria = null,
            dueDateRange = null
        )

        val specification = invoiceSpecificationBuilder.build(filters)
        assertNotNull(specification)

        specification.toPredicate(root, mock(CriteriaQuery::class.java), criteriaBuilder)
        verify(criteriaBuilder).`in`(typePath)
        filters.invoiceTypes!!.forEach { verify(typeInPredicate).value(it) }
        verify(criteriaBuilder).`in`(statusPath)
        filters.invoiceStatuses.forEach { verify(statusInPredicate).value(it) }
        verifyNoMoreInteractions(typeInPredicate, statusInPredicate)
    }


    @Test
    fun `should create specification with not in criteria, statuses and due date range`() {
        val filters = InvoiceFilters(
            invoiceTypes = null,
            companyId = null,
            dueDate = null,
            invoiceStatuses = setOf(InvoiceStatus.AUTHORIZED),
            inclusionCriteria = null,
            exclusionCriteria = InvoiceExcludeCriteria(setOf(InvoiceType.GP_SERVICE_INVOICE)),
            dueDateRange = DateRange(
                fromDate = LocalDate.of(2024, 1, 1),
                toDate = LocalDate.of(2024, 1, 31)
            )
        )

        filters.exclusionCriteria!!.invoiceTypes.forEach {
            `when`(typeInPredicate.value(it)).thenReturn(typeInPredicate)
        }

        filters.invoiceStatuses.forEach {
            `when`(statusInPredicate.value(it)).thenReturn(statusInPredicate)
        }

        val specification = invoiceSpecificationBuilder.build(filters)
        assertNotNull(specification)

        specification.toPredicate(root, mock(CriteriaQuery::class.java), criteriaBuilder)

        verify(criteriaBuilder).not(typeInPredicate)
        filters.exclusionCriteria!!.invoiceTypes.forEach {
            verify(typeInPredicate).value(it)
        }

        verify(criteriaBuilder).`in`(statusPath)
        filters.invoiceStatuses.forEach {
            verify(statusInPredicate).value(it)
        }

        verify(criteriaBuilder).between(
            eq(dueDatePath),
            eq(LocalDate.of(2024, 1, 1).atTime(LocalTime.MIN)),
            eq(LocalDate.of(2024, 1, 31).atTime(LocalTime.MAX))
        )
        verifyNoMoreInteractions(typeNotInPredicate, statusInPredicate, betweenPredicate)
    }

}
