package com.multiplier.core.payable.adapters

import com.google.protobuf.DoubleValue
import com.google.protobuf.Int32Value
import com.google.protobuf.Timestamp
import com.multiplier.core.payable.service.exception.EntityNotFoundException
import com.multiplier.core.schema.contract.Contract.ContractStatus
import com.multiplier.core.schema.currency.Currency
import com.multiplier.core.schema.grpc.benefit.Benefit
import com.multiplier.core.schema.grpc.benefit.Benefit.*
import com.multiplier.core.schema.grpc.benefit.BenefitServiceGrpc.BenefitServiceBlockingStub
import com.multiplier.core.util.dto.core.benefit.BenefitPackageCostWrapper
import com.multiplier.core.util.dto.core.benefit.InsuranceIndividualPremiumWrapper
import com.multiplier.core.util.dto.core.benefit.InsurancePremiumWrapper
import com.multiplier.country.schema.Country.GrpcCountryCode
import com.multiplier.payable.engine.collector.core.benefit.GrpcContractBenefitMapper
import com.multiplier.payable.engine.domain.aggregates.MonthYear
import com.multiplier.payable.types.Contract
import com.multiplier.payable.types.CountryCode
import com.multiplier.payable.types.CurrencyCode
import io.grpc.Status
import io.grpc.StatusRuntimeException
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.*
import java.time.Instant
import java.time.LocalDate

class DefaultBenefitServiceAdapterTest {

    private lateinit var benefitServiceAdapter: DefaultBenefitServiceAdapter
    private lateinit var stub: BenefitServiceBlockingStub
    private lateinit var contractBenefitMapper: GrpcContractBenefitMapper

    @BeforeEach
    fun setUp() {
        stub = mock(BenefitServiceBlockingStub::class.java)
        contractBenefitMapper = mock(GrpcContractBenefitMapper::class.java)
        benefitServiceAdapter = DefaultBenefitServiceAdapter(contractBenefitMapper)
        benefitServiceAdapter.stub = stub
    }

    @Test
    fun givenValidId_whenGetBenefit_thenReturnCorrectly() {
        val id = "testId"
        val grpcBenefit = GrpcBenefit.newBuilder().setId(id).build()

        // Mock the gRPC call
        whenever(stub.getBenefitByID(any())).thenReturn(grpcBenefit)

        // Call the method and assert
        val result = benefitServiceAdapter.getBenefitByID(id)
        assertNotNull(result)
        assertEquals(id, result.id)
    }

    @Test
    fun givenContractIds_whenGetBenefits_thenReturnCorrectly() {
        val contractId = 123L
        val contractBenefit = Benefit.GrpcContractBenefit.newBuilder().build()
        val contractBenefitList = listOf(contractBenefit)

        // Mock the gRPC call
        val response = Benefit.GetBenefitForContractsResponse.newBuilder()
            .addContractBenefitMap(
                Benefit.ContractIdAndBenefitMap.newBuilder()
                    .setContractId(contractId)
                    .addAllContractBenefit(contractBenefitList)
                    .build()
            ).build()

        whenever(stub.getContractBenefitsByContractIds(any())).thenReturn(response)

        // Call the method and assert
        val result = benefitServiceAdapter.getContractBenefitsByContractIds(listOf(contractId))
        assertTrue(result.containsKey(contractId))
        assertEquals(contractBenefitList, result[contractId])
    }

    @Test
    fun givenValidInput_whenGetActiveContractBenefitsForMonthYear_thenReturnCorrectly() {
        // GIVEN
        val companyId = 123L
        val monthYear = MonthYear(2024, 7)

        val contractBenefit = GrpcContractBenefit.newBuilder()
            .setId(666L)
            .setContractId(123L)
            .setContractBenefitStatus(ContractBenefitStatus.ACTIVE)
            .setBenefit(GrpcBenefit.newBuilder().setName("Custom Package Name THOR").build())
            .setInsurancePremium(
                GrpcInsurancePremium.newBuilder()
                    .setEmployerPayAmount(500.0)
                    .setBenefitType(BenefitType.INSURANCE)
                    .setBillingDuration(BenefitPartnerBillingFrequency.BENEFIT_PARTNER_BILLING_FREQUENCY_MONTHLY)
                    .setSelf(mockGrpcInsuranceIndividualPremium())
                    .addAllDependents(
                        listOf(
                            mockGrpcInsuranceIndividualPremium(),
                            mockGrpcInsuranceIndividualPremium(),
                        )
                    )
                    .build()
            )
            .build()
        val companyBenefitList = listOf(contractBenefit)
        val response = GetBenefitForCompaniesResponse.newBuilder()
            .addCompanyBenefitMap(
                CompanyIdAndBenefitMap.newBuilder()
                    .setCompanyId(companyId)
                    .addAllContractBenefit(companyBenefitList)
                    .build()
            ).build()
        whenever(stub.getContractBenefitsByCompanyIds(any())).thenReturn(response)

        val mockInsurancePremium = InsurancePremiumWrapper(
            id=666L,
            contract = Contract.newBuilder().id(123L).build(),
            amountTotalCost = 500.0,
            benefitType = BenefitType.INSURANCE,
            billingDuration = BenefitPartnerBillingFrequency.BENEFIT_PARTNER_BILLING_FREQUENCY_MONTHLY,
            self = mockInsuranceIndividualPremiumWrapper(),
            status = ContractBenefitStatus.ACTIVE,
            benefitName = "Custom Package Name THOR",
            dependents = listOf(mockInsuranceIndividualPremiumWrapper(), mockInsuranceIndividualPremiumWrapper()),
            // these 2 are ignored thus taking 0L as default
            companyId = 0L,
            originalTimestamp = 0L
        )
        val insurancePremiums = listOf(mockInsurancePremium)
        whenever(contractBenefitMapper.toInsurancePremiums(any(), any(), any())).thenReturn(insurancePremiums)

        // WHEN
        benefitServiceAdapter.getActiveContractBenefitsForMonthYear(companyId, monthYear)

        // THEN
        val expectedRequest = GetBenefitForCompaniesInput.newBuilder()
            .addAllCompanyIds(listOf(companyId))
            .setMonth(monthYear.month)
            .setYear(monthYear.year)
            .addAllContractStatuses(setOf(ContractStatus.ACTIVE))
            .build()
        verify(stub).getContractBenefitsByCompanyIds(expectedRequest)
        val captor = argumentCaptor<Long>()
        verify(contractBenefitMapper).toInsurancePremiums(eq(companyId), eq(companyBenefitList), captor.capture())
        assertNotNull(captor.firstValue)
    }

    @Test
    fun givenAnyStubException_whenGetActiveContractBenefitsForMonthYear_thenThrowException() {
        val companyId = 123L
        val monthYear = MonthYear(2024, 7)

        // Mock a NOT_FOUND gRPC exception
        val exception = StatusRuntimeException(Status.NOT_FOUND)
        whenever(stub.getContractBenefitsByCompanyIds(any())).thenThrow(exception)

        // Call the method and assert the exception
        val exceptionThrown = assertThrows(EntityNotFoundException::class.java) {
            benefitServiceAdapter.getActiveContractBenefitsForMonthYear(companyId, monthYear)
        }

        assertEquals(
            "No contract benefits found for the company id = $companyId, year = ${monthYear.year}, month = ${monthYear.month}",
            exceptionThrown.message
        )
    }

    private fun mockGrpcInsuranceIndividualPremium(): GrpcInsuranceIndividualPremium {
        return GrpcInsuranceIndividualPremium.newBuilder()
            .setId(1L)
            .setFirstName("John")
            .setLastName("Doe")
            .setBillingPeriodInMonths(12)
            .setStartOn(
                Timestamp.newBuilder()
                    .setSeconds(Instant.parse("2022-01-01T00:00:00Z").epochSecond)
                    .build()
            )
            .setEndOn(
                Timestamp.newBuilder()
                    .setSeconds(Instant.parse("2022-12-31T00:00:00Z").epochSecond)
                    .build()
            )
            .setSubTotalAmount(DoubleValue.of(100.0))
            .setPlatformFee(DoubleValue.of(10.0))
            .setSubTotalPlatformFee(DoubleValue.of(110.0))
            .setBenefitPackageCost(
                GrpcBenefitPackageCost.newBuilder()
                    .setCountryCode(GrpcCountryCode.AUS)
                    .setBenefitPackageId("PKG123")
                    .setAgeLowerRange(Int32Value.of(25))
                    .setAgeHigherRange(Int32Value.of(50))
                    .setCost(DoubleValue.of(1000.0))
                    .setPremium(DoubleValue.of(100.0))
                    .setPremiumCurrencyCode(Currency.CurrencyCode.USD)
                    .setPremiumFrequency(BenefitPremiumFrequency.BENEFIT_PREMIUM_FREQUENCY_MONTHLY)
                    .setPackageCostStatus(BenefitPackageCostStatus.BENEFIT_PACKAGE_COST_STATUS_ACTIVE)
                    .build()
            )
            .build()
    }

    private fun mockInsuranceIndividualPremiumWrapper(): InsuranceIndividualPremiumWrapper {
        return InsuranceIndividualPremiumWrapper(
            id = 1L,
            firstName = "John",
            lastName = "Doe",
            billingPeriodInMonths = 12,
            startOn = LocalDate.of(2022, 1, 1),
            endOn = LocalDate.of(2022, 12, 31),
            subTotalAmount = 100.0,
            platformFee = 10.0,
            subTotalPlatformFee = 110.0,
            benefitPackageCost = BenefitPackageCostWrapper(
                benefitPackageId = "PKG123",
                ageLowerRange = 25,
                ageHigherRange = 50,
                cost = 1000.0,
                premium = 100.0,
                premiumCurrencyCode = CurrencyCode.USD,
                premiumFrequency = BenefitPremiumFrequency.BENEFIT_PREMIUM_FREQUENCY_MONTHLY,
                packageCostStatus = BenefitPackageCostStatus.BENEFIT_PACKAGE_COST_STATUS_ACTIVE,
                countryCode = CountryCode.AUS
            )
        )
    }


}
