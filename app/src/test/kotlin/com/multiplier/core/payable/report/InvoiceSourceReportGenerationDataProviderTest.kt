package com.multiplier.core.payable.report

import com.multiplier.core.payable.adapters.CompanyBindingAdapter
import com.multiplier.core.payable.company.Company
import com.multiplier.core.payable.company.adapter.CompanyServiceAdapter
import com.multiplier.core.payable.event.database.RecordType
import com.multiplier.core.payable.report.composition.InvoiceSourceReportCompositionService
import com.multiplier.core.payable.service.dataholder.ISRFileMetaData
import com.multiplier.payable.engine.domain.aggregates.CompanyPayable
import com.multiplier.payable.engine.domain.entities.TransactionType
import com.multiplier.payable.engine.payableitem.PayableItem
import com.multiplier.payable.engine.reconciler.data.invoice.InvoiceDataProvider
import com.multiplier.payable.engine.splitter.ItemSplitter
import com.multiplier.payable.engine.splitter.selector.Selector
import com.multiplier.payable.engine.transaction.InvoiceCommand
import com.multiplier.payable.engine.transaction.data.CompanyPayableDataProvider
import com.multiplier.payable.engine.transaction.template.TransactionTemplate
import com.multiplier.payable.engine.transaction.template.provider.TransactionTemplateProvider
import com.multiplier.payable.types.PayableStatus
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.ArgumentCaptor
import org.mockito.Mockito.any
import org.mockito.Mockito.`when`
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import java.time.LocalDate

class InvoiceSourceReportGenerationDataProviderTest {
    private val companyPayableDataProvider: CompanyPayableDataProvider = mock()
    private val companyBindingAdapter: CompanyBindingAdapter = mock()
    private val companyServiceAdapter: CompanyServiceAdapter = mock()
    private val invoiceSourceReportCompositionService: InvoiceSourceReportCompositionService = mock()
    private val transactionTemplateProvider: TransactionTemplateProvider = mock()
    private val isrExternalReferenceDataProvider: IsrExternalReferenceDataProvider = mock()
    private val secondInvoiceDataProvider: InvoiceDataProvider = mock()
    private val invoiceSourceReportGenerationDataProvider =
        InvoiceSourceReportGenerationDataProvider(
            companyPayableDataProvider,
            companyBindingAdapter,
            companyServiceAdapter,
            invoiceSourceReportCompositionService,
            transactionTemplateProvider,
            isrExternalReferenceDataProvider,
            secondInvoiceDataProvider,
        )

    @Test
    fun `should generate correct ISR data holder`() {
        // given
        val dateTime = LocalDate.of(2024, 8, 1).atStartOfDay()
        val command =
            mock<InvoiceCommand> {
                on { transactionId } doReturn "transactionId"
                on { companyId } doReturn 1
                on { transactionType } doReturn TransactionType.SECOND_INVOICE
                on { transactionDate } doReturn dateTime
            }
        val companyPayable =
            CompanyPayable(
                id = 1L,
                companyId = 1L,
                items = listOf(),
                itemType = TransactionType.SECOND_INVOICE,
                transactionId = "transactionId",
                status = PayableStatus.DRAFT,
                month = 7,
                year = 2024,
            )
        val template = mock<TransactionTemplate>()
        `when`(template.identifier).thenReturn("identifier")
        val mockItemSplitter = mock<ItemSplitter>()
        `when`(template.itemSplitter).thenReturn(mockItemSplitter)
        `when`(mockItemSplitter.selectors).thenReturn(listOf())

        val company: Company = mock()
        `when`(company.displayName).thenReturn("displayName")
        `when`(companyServiceAdapter.getCompanyById(1)).thenReturn(company)
        `when`(companyPayableDataProvider.findByTransactionId("transactionId")).thenReturn(listOf(companyPayable))
        `when`(secondInvoiceDataProvider.fetchActiveInvoices(command)).thenReturn(listOf())
        `when`(companyBindingAdapter.getCustomerIdForCompanyId(1)).thenReturn("1")
        `when`(transactionTemplateProvider.findTemplateFor(TransactionType.SECOND_INVOICE, 1)).thenReturn(template)
        `when`(invoiceSourceReportCompositionService.populateAuthCodeAndEmailLink(any()))
            .thenReturn(listOf(mock<ISRFileMetaData>()))
        `when`(isrExternalReferenceDataProvider.find(1)).thenReturn(
            IsrExternalReference(
                externalId = "externalId",
                recordType = RecordType.INVOICE,
            ),
        )

        // when
        val result = invoiceSourceReportGenerationDataProvider.createInvoiceSourceReportData(command)

        // then
        assertEquals(1, result.size)
        assertEquals(1, result[0].companyId)
        assertEquals("1", result[0].customerId)
        assertEquals("externalId", result[0].externalId)
        assertEquals(RecordType.INVOICE, result[0].recordType)
    }

    @Test
    fun `should generate correct ISR data holder with suffix if there is exsting payable even with no selectors`() {
        // given
        val dateTime = LocalDate.of(2024, 8, 1).atStartOfDay()
        val command =
            mock<InvoiceCommand> {
                on { transactionId } doReturn "transactionId"
                on { companyId } doReturn 1
                on { transactionType } doReturn TransactionType.SECOND_INVOICE
                on { transactionDate } doReturn dateTime
            }
        val companyPayable =
            CompanyPayable(
                id = 1L,
                companyId = 1L,
                items = listOf(),
                itemType = TransactionType.SECOND_INVOICE,
                transactionId = "transactionId",
                status = PayableStatus.DRAFT,
                month = 7,
                year = 2024,
            )
        val template = mock<TransactionTemplate>()
        `when`(template.identifier).thenReturn("identifier")
        val mockItemSplitter = mock<ItemSplitter>()
        `when`(template.itemSplitter).thenReturn(mockItemSplitter)
        `when`(mockItemSplitter.selectors).thenReturn(listOf())

        val company: Company = mock()
        `when`(company.displayName).thenReturn("displayName")
        `when`(companyServiceAdapter.getCompanyById(1)).thenReturn(company)
        `when`(companyPayableDataProvider.findByTransactionId("transactionId")).thenReturn(listOf(companyPayable))
        `when`(secondInvoiceDataProvider.fetchActiveInvoices(command)).thenReturn(listOf(mock<CompanyPayable>()))
        `when`(companyBindingAdapter.getCustomerIdForCompanyId(1)).thenReturn("1")
        `when`(transactionTemplateProvider.findTemplateFor(TransactionType.SECOND_INVOICE, 1)).thenReturn(template)
        val argumentCaptor: ArgumentCaptor<List<ISRFileMetaData>> = ArgumentCaptor.captor()
        `when`(invoiceSourceReportCompositionService.populateAuthCodeAndEmailLink(argumentCaptor.capture()))
            .thenReturn(listOf(mock<ISRFileMetaData>()))
        `when`(isrExternalReferenceDataProvider.find(1)).thenReturn(
            IsrExternalReference(
                externalId = "externalId",
                recordType = RecordType.INVOICE,
            ),
        )

        // when
        val result = invoiceSourceReportGenerationDataProvider.createInvoiceSourceReportData(command)

        // then
        assertEquals(1, result.size)
        assertEquals(1, result[0].companyId)
        assertEquals("1", result[0].customerId)
        assertEquals("externalId", result[0].externalId)
        assertEquals(RecordType.INVOICE, result[0].recordType)
        Assertions.assertThat(argumentCaptor.value[0].fileSuffixes).containsExactlyInAnyOrder("1")
    }

    @Test
    fun `should generate correct ISR data holder with suffix if there is exsting payable and existing selectors`() {
        // given
        val dateTime = LocalDate.of(2024, 8, 1).atStartOfDay()
        val command =
            mock<InvoiceCommand> {
                on { transactionId } doReturn "transactionId"
                on { companyId } doReturn 1
                on { transactionType } doReturn TransactionType.SECOND_INVOICE
                on { transactionDate } doReturn dateTime
            }
        val payableItem = mock<PayableItem>()
        `when`(payableItem.contractId).thenReturn(1L)
        `when`(payableItem.countryCode).thenReturn("VNM")
        val companyPayable =
            CompanyPayable(
                id = 1L,
                companyId = 1L,
                items = listOf(payableItem),
                itemType = TransactionType.SECOND_INVOICE,
                transactionId = "transactionId",
                status = PayableStatus.DRAFT,
                month = 7,
                year = 2024,
            )
        val template = mock<TransactionTemplate>()
        `when`(template.identifier).thenReturn("identifier")
        val mockItemSplitter = mock<ItemSplitter>()
        `when`(template.itemSplitter).thenReturn(mockItemSplitter)
        val selector = mock<Selector<String>>()
        `when`(selector.contextEnricher).thenReturn { context -> context }
        `when`(selector.select()).thenReturn { _, _ -> "VNM" }
        `when`(selector.selectAndTranslate()).thenReturn { _, _ -> "Vietnam" }
        `when`(mockItemSplitter.selectors).thenReturn(listOf(selector))

        val company: Company = mock()
        `when`(company.displayName).thenReturn("displayName")
        `when`(companyServiceAdapter.getCompanyById(1)).thenReturn(company)
        `when`(companyPayableDataProvider.findByTransactionId("transactionId")).thenReturn(listOf(companyPayable))
        `when`(secondInvoiceDataProvider.fetchActiveInvoices(command)).thenReturn(listOf(mock<CompanyPayable>()))
        `when`(companyBindingAdapter.getCustomerIdForCompanyId(1)).thenReturn("1")
        `when`(transactionTemplateProvider.findTemplateFor(TransactionType.SECOND_INVOICE, 1)).thenReturn(template)
        val argumentCaptor: ArgumentCaptor<List<ISRFileMetaData>> = ArgumentCaptor.captor()
        `when`(invoiceSourceReportCompositionService.populateAuthCodeAndEmailLink(argumentCaptor.capture()))
            .thenReturn(listOf(mock<ISRFileMetaData>()))
        `when`(isrExternalReferenceDataProvider.find(1)).thenReturn(
            IsrExternalReference(
                externalId = "externalId",
                recordType = RecordType.INVOICE,
            ),
        )

        // when
        val result = invoiceSourceReportGenerationDataProvider.createInvoiceSourceReportData(command)

        // then
        assertEquals(1, result.size)
        assertEquals(1, result[0].companyId)
        assertEquals("1", result[0].customerId)
        assertEquals("externalId", result[0].externalId)
        assertEquals(RecordType.INVOICE, result[0].recordType)
        Assertions.assertThat(argumentCaptor.value[0].fileSuffixes).containsExactlyInAnyOrder("Vietnam", "1")
    }

    @Test
    fun `should generate correct ISR data holder with suffix if there is no exsting payable and existing selectors`() {
        // given
        val dateTime = LocalDate.of(2024, 8, 1).atStartOfDay()
        val command =
            mock<InvoiceCommand> {
                on { transactionId } doReturn "transactionId"
                on { companyId } doReturn 1
                on { transactionType } doReturn TransactionType.SECOND_INVOICE
                on { transactionDate } doReturn dateTime
            }
        val payableItem = mock<PayableItem>()
        `when`(payableItem.contractId).thenReturn(1L)
        `when`(payableItem.countryCode).thenReturn("VNM")
        val secondPayableItem = mock<PayableItem>()
        `when`(secondPayableItem.contractId).thenReturn(2L)
        `when`(secondPayableItem.countryCode).thenReturn("VNM")
        val companyPayable =
            CompanyPayable(
                id = 1L,
                companyId = 1L,
                items = listOf(payableItem),
                itemType = TransactionType.SECOND_INVOICE,
                transactionId = "transactionId",
                status = PayableStatus.DRAFT,
                month = 7,
                year = 2024,
            )
        val secondCompanyPayable = companyPayable.copy(
            id = 2L,
            items = listOf(secondPayableItem),
        )
        val template = mock<TransactionTemplate>()
        `when`(template.identifier).thenReturn("identifier")
        val mockItemSplitter = mock<ItemSplitter>()
        `when`(template.itemSplitter).thenReturn(mockItemSplitter)
        val selector = mock<Selector<String>>()
        `when`(selector.contextEnricher).thenReturn { context -> context }
        `when`(selector.select()).thenReturn { _, _ -> "VNM" }
        `when`(selector.selectAndTranslate()).thenReturn { _, _ -> "Vietnam" }
        `when`(mockItemSplitter.selectors).thenReturn(listOf(selector))

        val company: Company = mock()
        `when`(company.displayName).thenReturn("displayName")
        `when`(companyServiceAdapter.getCompanyById(1)).thenReturn(company)
        `when`(companyPayableDataProvider.findByTransactionId("transactionId")).thenReturn(listOf(companyPayable, secondCompanyPayable))
        `when`(secondInvoiceDataProvider.fetchActiveInvoices(command)).thenReturn(listOf())
        `when`(companyBindingAdapter.getCustomerIdForCompanyId(1)).thenReturn("1")
        `when`(transactionTemplateProvider.findTemplateFor(TransactionType.SECOND_INVOICE, 1)).thenReturn(template)
        val argumentCaptor: ArgumentCaptor<List<ISRFileMetaData>> = ArgumentCaptor.captor()
        `when`(invoiceSourceReportCompositionService.populateAuthCodeAndEmailLink(argumentCaptor.capture()))
            .thenReturn(listOf(mock<ISRFileMetaData>()))
        `when`(isrExternalReferenceDataProvider.find(1)).thenReturn(
            IsrExternalReference(
                externalId = "externalId",
                recordType = RecordType.INVOICE,
            ),
        )
        `when`(isrExternalReferenceDataProvider.find(2)).thenReturn(
            IsrExternalReference(
                externalId = "externalId2",
                recordType = RecordType.INVOICE,
            ),
        )

        // when
        val result = invoiceSourceReportGenerationDataProvider.createInvoiceSourceReportData(command)

        // then
        assertEquals(2, result.size)
        assertEquals(1, result[0].companyId)
        assertEquals("1", result[0].customerId)
        assertEquals("externalId", result[0].externalId)
        assertEquals(RecordType.INVOICE, result[0].recordType)
        Assertions.assertThat(argumentCaptor.allValues[0][0].fileSuffixes).containsExactlyInAnyOrder("Vietnam")
        Assertions.assertThat(argumentCaptor.allValues[1][0].fileSuffixes).containsExactlyInAnyOrder("Vietnam")
    }
}
