package com.multiplier.core.payable.adapters

import com.multiplier.contract.schema.performance.PerformanceReviewServiceGrpc.PerformanceReviewServiceBlockingStub
import com.multiplier.core.payable.service.exception.EntityNotFoundException
import io.grpc.Status
import io.grpc.StatusRuntimeException
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import java.lang.IllegalStateException

class PerformanceReviewServiceImplTest {
    private val blockingStub: PerformanceReviewServiceBlockingStub = mock()
    private val performanceReviewServiceImpl = PerformanceReviewServiceImpl()

    @BeforeEach
    fun setUp() {
        performanceReviewServiceImpl.stub = blockingStub
    }

    @Test
    fun `should not throw exception but return empty list`() {
        `when`(performanceReviewServiceImpl.getApprovedSalaryReviewsForContractIgnoreException(1L))
            .thenThrow(IllegalStateException("Some error"))
        assertThat(performanceReviewServiceImpl.getApprovedSalaryReviewsForContractIgnoreException(1L)).isEmpty()
    }

    @Test
    fun `should throw exceptions for existing method`() {
        `when`(performanceReviewServiceImpl.getApprovedSalaryReviewsForContractIgnoreException(1L))
            .thenThrow(StatusRuntimeException(Status.NOT_FOUND))
        assertThrows<EntityNotFoundException> { performanceReviewServiceImpl.getApprovedSalaryReviewsForContract(1L) }
    }
}
