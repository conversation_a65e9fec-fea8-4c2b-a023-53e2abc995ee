package com.multiplier.core.util.dto.orgmanagementservice

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class HeadOfDepartmentWrapperTest {
    @Test
    fun `test HeadOfDepartmentWrapper initialization and properties`() {
        val hod = HeadOfDepartmentWrapper(contractId = 123, managerId = 456)
        assertEquals(123, hod.contractId)
        assertEquals(456, hod.managerId)
    }

    @Test
    fun `given all nulls test HeadOfDepartmentWrapper initialization and properties`() {
        val hod = HeadOfDepartmentWrapper()
        assertEquals(null, hod.contractId)
        assertEquals(null, hod.managerId)
    }
}