package com.multiplier.core.mapper.pricing

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.core.payable.adapters.pricing.ReferenceTargetType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream
import com.multiplier.productcatalogue.grpc.schema.pricing.policy.ReferenceTargetType as GrpcReferenceTargetType

class ReferenceTargetTypeGrpcToDomainMapperTest {

    private val mapper = ReferenceTargetTypeGrpcToDomainMapper()

    @ParameterizedTest
    @MethodSource("testParameters")
    fun givenGrpc_whenMap_thenReturnDomain(
        grpcReferenceTargetType: GrpcReferenceTargetType,
        expectedReferenceTargetType: ReferenceTargetType,
    ) {
        // WHEN
        val result = mapper.map(grpcReferenceTargetType)

        // THEN
        assertEquals(expectedReferenceTargetType, result)
    }

    @Test
    fun givenUnspecifiedGrpc_whenMap_thenThrowException() {
        // WHEN & THEN
        assertThrows<MplBusinessException> {
            mapper.map(GrpcReferenceTargetType.REFERENCE_TARGET_TYPE_UNSPECIFIED)
        }
    }

    @Test
    fun givenUnrecognizedGrpc_whenMap_thenThrowException() {
        // WHEN & THEN
        assertThrows<MplBusinessException> {
            mapper.map(GrpcReferenceTargetType.UNRECOGNIZED)
        }
    }

    companion object {
        @JvmStatic
        private fun testParameters(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    GrpcReferenceTargetType.REFERENCE_TARGET_TYPE_COMPANY_PRODUCT,
                    ReferenceTargetType.COMPANY_PRODUCT
                ),
                Arguments.of(
                    GrpcReferenceTargetType.REFERENCE_TARGET_TYPE_SUBSCRIPTION_PRODUCT,
                    ReferenceTargetType.SUBSCRIPTION_PRODUCT
                ),
            )
        }
    }
}