package com.multiplier.core.mapper.bill

import com.google.protobuf.Timestamp
import com.multiplier.billing.grpc.billing.Billing.BilledItem
import com.multiplier.billing.grpc.billing.Billing.BillingPeriod
import com.multiplier.core.mapper.AmountGrpcToDomainMapper
import com.multiplier.core.mapper.product.CompanyProductGrpcToDomainMapper
import com.multiplier.core.payable.adapters.billing.MeteringMetricWrapper
import com.multiplier.core.payable.adapters.billing.MeteringMetricLabelWrapper
import com.multiplier.core.payable.adapters.pricing.ReferenceChargePolicy
import com.multiplier.core.payable.adapters.product.CompanyProductWrapper
import com.multiplier.core.payable.mapper.GrpcDateTimeMapper
import com.multiplier.grpc.common.toTimestamp
import com.multiplier.metering.grpc.schema.Metering
import com.multiplier.payable.engine.common.Amount
import com.multiplier.payable.types.CurrencyCode
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.mock

import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.ZoneOffset
import com.multiplier.grpc.common.time.v2.DurationOuterClass.Duration as GrpcDuration

@ExtendWith(MockitoExtension::class)
class BilledItemGrpcToDomainMapperTest {

    @Mock
    private lateinit var companyProductGrpcToDomainMapper: CompanyProductGrpcToDomainMapper

    @Mock
    private lateinit var grpcDateTimeMapper: GrpcDateTimeMapper

    @Mock
    private lateinit var amountGrpcToDomainMapper: AmountGrpcToDomainMapper

    @Mock
    private lateinit var billedItemUsageGrpcToDomainMapper: BilledItemUsageGrpcToDomainMapper

    @InjectMocks
    private lateinit var mapper: BilledItemGrpcToDomainMapper

    @Mock
    private lateinit var billedItem: BilledItem

    private val startDateTime = LocalDateTime.of(2023, 1, 1, 0, 0)
    private val endDateTime = LocalDateTime.of(2023, 2, 1, 0, 0)
    private val billingPeriod: BillingPeriod = BillingPeriod.newBuilder()
        .setBillingDuration(
            GrpcDuration.newBuilder()
                .setStartDate(startDateTime.toTimestamp(ZoneOffset.UTC))
                .setEndDate(endDateTime.toTimestamp(ZoneOffset.UTC))
                .build()
        )
        .setUsageDuration(
            GrpcDuration.newBuilder()
                .setStartDate(startDateTime.toTimestamp(ZoneOffset.UTC))
                .setEndDate(endDateTime.toTimestamp(ZoneOffset.UTC))
                .build()
        )
        .build()

    private lateinit var companyProductWrapper: CompanyProductWrapper
    private lateinit var amount: Amount
    private lateinit var meteringMetricWrappers: List<MeteringMetricWrapper>

    @BeforeEach
    fun setup() {
        companyProductWrapper = CompanyProductWrapper(
            startDate = startDateTime,
            endDate = endDateTime,
            lineCode = "TEST_LINE_CODE",
            dimensions = mapOf("key1" to "value1", "key2" to "value2"),
            chargePolicy = ReferenceChargePolicy(emptyList())
        )

        amount = Amount(
            value = BigDecimal.valueOf(100.0),
            currency = CurrencyCode.USD
        )

        meteringMetricWrappers = listOf(
            MeteringMetricWrapper(
                id = "metric-1",
                name = "test-metric",
                source = "test-source",
                labels = listOf(
                    MeteringMetricLabelWrapper(key = "label1", value = "value1")
                )
            )
        )

        val grpcUsages = listOf(
            Metering.Metric.newBuilder()
                .setId("metric-1")
                .setName("test-metric")
                .setSource("test-source")
                .addLabels(
                    Metering.MetricLabel.newBuilder()
                        .setKey("label1")
                        .setValue("value1")
                        .build()
                )
                .build()
        )

        `when`(billedItem.id).thenReturn(123L)
        `when`(billedItem.transactionId).thenReturn("txn-123")
        `when`(billedItem.companyId).thenReturn(456L)
        `when`(billedItem.companyProduct).thenReturn(mock())
        `when`(billedItem.billingAmount).thenReturn(mock())
        `when`(billedItem.billingTime).thenReturn(Timestamp.newBuilder().setSeconds(1000).build())
        `when`(billedItem.entityId).thenReturn(789L)
        `when`(billedItem.billingPeriod).thenReturn(billingPeriod)
        `when`(billedItem.referencedBillsList).thenReturn(emptyList())
        `when`(billedItem.usageList).thenReturn(grpcUsages)

        `when`(companyProductGrpcToDomainMapper.map(billedItem.companyProduct)).thenReturn(companyProductWrapper)
        `when`(amountGrpcToDomainMapper.map(billedItem.billingAmount)).thenReturn(amount)
        `when`(grpcDateTimeMapper.toLocalDateTime(billingPeriod.billingDuration.startDate)).thenReturn(startDateTime)
        `when`(grpcDateTimeMapper.toLocalDateTime(billingPeriod.billingDuration.endDate)).thenReturn(endDateTime)
        `when`(grpcDateTimeMapper.toLocalDateTime(billingPeriod.usageDuration.startDate)).thenReturn(startDateTime)
        `when`(grpcDateTimeMapper.toLocalDateTime(billingPeriod.usageDuration.endDate)).thenReturn(endDateTime)
        `when`(billedItemUsageGrpcToDomainMapper.map(billedItem.usageList)).thenReturn(meteringMetricWrappers)
    }

    @Test
    fun `should map single BilledItem to BilledItemWrapper`() {
        // When
        val result = mapper.map(billedItem)

        // Then
        assertEquals(123L, result.billId)
        assertEquals("txn-123", result.transactionId)
        assertEquals(456L, result.companyId)
        assertEquals(789L, result.entityId)
        assertEquals(companyProductWrapper, result.companyProduct)
        assertEquals(amount, result.billingAmount)
        assertEquals(startDateTime.toLocalDate(), result.billingDuration.startDate)
        assertEquals(endDateTime.toLocalDate(), result.billingDuration.endDate)
        assertEquals(startDateTime.toLocalDate(), result.usageDuration.startDate)
        assertEquals(endDateTime.toLocalDate(), result.usageDuration.endDate)
        assertEquals(1000L, result.billingTime)
        assertEquals(emptyList<Any>(), result.referenceBills)
        assertEquals(meteringMetricWrappers, result.usages)
    }

    @Test
    fun `should map list of BilledItems to list of BilledItemWrappers`() {
        // Given
        val billedItems = listOf(billedItem, billedItem)

        // When
        val result = mapper.map(billedItems)

        // Then
        assertEquals(2, result.size)
        result.forEach { wrapper ->
            assertEquals(123L, wrapper.billId)
            assertEquals("txn-123", wrapper.transactionId)
            assertEquals(456L, wrapper.companyId)
            assertEquals(789L, wrapper.entityId)
        }
    }
}
