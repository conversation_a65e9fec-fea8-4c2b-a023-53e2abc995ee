package com.multiplier.core.mapper.product

import com.google.protobuf.Timestamp
import com.multiplier.core.mapper.pricing.ChargePolicyGrpcToDomainMapper
import com.multiplier.core.payable.adapters.pricing.ReferenceChargePolicy
import com.multiplier.core.payable.adapters.product.CompanyProductWrapper
import com.multiplier.core.payable.mapper.GrpcDateTimeMapper
import com.multiplier.productcatalogue.grpc.schema.product.CompanyProduct
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import java.time.LocalDateTime
import com.multiplier.productcatalogue.grpc.schema.pricing.policy.ChargePolicy as GrpcChargePolicy

@ExtendWith(MockitoExtension::class)
class CompanyProductGrpcToDomainMapperTest {

    @Mock
    private lateinit var timestampToLocalDateTimeMapper: GrpcDateTimeMapper

    @Mock
    private lateinit var chargePolicyGrpcToDomainMapper: ChargePolicyGrpcToDomainMapper

    @InjectMocks
    private lateinit var mapper: CompanyProductGrpcToDomainMapper

    @Test
    fun `should map CompanyProduct to CompanyProductWrapper`() {
        // Given
        val startTimestamp = Timestamp.newBuilder().setSeconds(1609459200L).build() // 2021-01-01
        val endTimestamp = Timestamp.newBuilder().setSeconds(1612137600L).build() // 2021-02-01

        val companyProduct = CompanyProduct.newBuilder()
            .setLineCode("TEST_LINE_CODE")
            .setStartDate(startTimestamp)
            .setEndDate(endTimestamp)
            .putDimensions("key1", "value1")
            .putDimensions("key2", "value2")
            .build()

        val startDateTime = LocalDateTime.of(2021, 1, 1, 0, 0)
        val endDateTime = LocalDateTime.of(2021, 2, 1, 0, 0)
        val defaultChargePolicy = ReferenceChargePolicy(emptyList())

        `when`(timestampToLocalDateTimeMapper.toLocalDateTime(startTimestamp)).thenReturn(startDateTime)
        `when`(timestampToLocalDateTimeMapper.toLocalDateTime(endTimestamp)).thenReturn(endDateTime)
        `when`(chargePolicyGrpcToDomainMapper.map(any())).thenReturn(defaultChargePolicy)

        // When
        val result = mapper.map(companyProduct)

        // Then
        assertEquals(
            CompanyProductWrapper(
                lineCode = "TEST_LINE_CODE",
                startDate = startDateTime,
                endDate = endDateTime,
                dimensions = mapOf("key1" to "value1", "key2" to "value2"),
                chargePolicy = defaultChargePolicy
            ), result
        )
    }

    @Test
    fun `should map list of CompanyProducts to list of CompanyProductWrappers`() {
        // Given
        val startTimestamp = Timestamp.newBuilder().setSeconds(1609459200L).build() // 2021-01-01
        val endTimestamp = Timestamp.newBuilder().setSeconds(1612137600L).build() // 2021-02-01

        val companyProduct1 = CompanyProduct.newBuilder()
            .setLineCode("TEST_LINE_CODE_1")
            .setStartDate(startTimestamp)
            .setEndDate(endTimestamp)
            .putDimensions("key1", "value1")
            .build()

        val companyProduct2 = CompanyProduct.newBuilder()
            .setLineCode("TEST_LINE_CODE_2")
            .setStartDate(startTimestamp)
            .setEndDate(endTimestamp)
            .putDimensions("key2", "value2")
            .build()

        val startDateTime = LocalDateTime.of(2021, 1, 1, 0, 0)
        val endDateTime = LocalDateTime.of(2021, 2, 1, 0, 0)
        val defaultChargePolicy = ReferenceChargePolicy(emptyList())

        `when`(timestampToLocalDateTimeMapper.toLocalDateTime(startTimestamp)).thenReturn(startDateTime)
        `when`(timestampToLocalDateTimeMapper.toLocalDateTime(endTimestamp)).thenReturn(endDateTime)
        `when`(chargePolicyGrpcToDomainMapper.map(any())).thenReturn(defaultChargePolicy)

        // When
        val result = mapper.map(listOf(companyProduct1, companyProduct2))

        // Then
        assertEquals(2, result.size)
        assertEquals("TEST_LINE_CODE_1", result[0].lineCode)
        assertEquals("TEST_LINE_CODE_2", result[1].lineCode)
        assertEquals(mapOf("key1" to "value1"), result[0].dimensions)
        assertEquals(mapOf("key2" to "value2"), result[1].dimensions)
    }

    @Test
    fun `should map CompanyProduct with ChargePolicy to CompanyProductWrapper`() {
        // Given
        val startTimestamp = Timestamp.newBuilder().setSeconds(1609459200L).build() // 2021-01-01
        val endTimestamp = Timestamp.newBuilder().setSeconds(1612137600L).build() // 2021-02-01

        val companyProduct = CompanyProduct.newBuilder()
            .setLineCode("TEST_LINE_CODE")
            .setStartDate(startTimestamp)
            .setEndDate(endTimestamp)
            .putDimensions("key1", "value1")
            .putDimensions("key2", "value2")
            .build()

        val startDateTime = LocalDateTime.of(2021, 1, 1, 0, 0)
        val endDateTime = LocalDateTime.of(2021, 2, 1, 0, 0)

        val chargePolicy = mock<GrpcChargePolicy>()
        val mappedChargePolicy = ReferenceChargePolicy(emptyList())

        `when`(timestampToLocalDateTimeMapper.toLocalDateTime(startTimestamp)).thenReturn(startDateTime)
        `when`(timestampToLocalDateTimeMapper.toLocalDateTime(endTimestamp)).thenReturn(endDateTime)
        `when`(chargePolicyGrpcToDomainMapper.map(chargePolicy)).thenReturn(mappedChargePolicy)

        // Create a CompanyProduct with chargePolicy
        val companyProductWithChargePolicy = CompanyProduct.newBuilder()
            .setLineCode("TEST_LINE_CODE")
            .setStartDate(startTimestamp)
            .setEndDate(endTimestamp)
            .putDimensions("key1", "value1")
            .putDimensions("key2", "value2")
            .setChargePolicy(chargePolicy)
            .build()

        // When
        val result = mapper.map(companyProductWithChargePolicy)

        // Then
        assertEquals(
            CompanyProductWrapper(
                lineCode = "TEST_LINE_CODE",
                startDate = startDateTime,
                endDate = endDateTime,
                dimensions = mapOf("key1" to "value1", "key2" to "value2"),
                chargePolicy = mappedChargePolicy
            ), result
        )
    }
}
