[{"id": 165659, "createdDate": "2023-10-05T00:00:00", "dueDate": "2023-10-19T00:00:00", "status": "DRAFT", "reference": "Oct'23 Salary - EOR ( (Singapore))", "createdOn": "2024-07-31T09:49:12.438119", "createdBy": 400005, "updatedOn": "2024-07-31T09:49:13.060120", "updatedBy": -1, "invoiceNo": "MTPLINV0079273", "externalId": "1220552", "lineItems": [{"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": ***********.6, "classValue": "111", "contractId": 680827, "memberName": "Multiplier tester", "countryName": "Singapore", "description": "Payroll Cost: SGD 17,500,007,631.25", "grossAmount": ***********.6, "baseCurrency": "SGD", "classDisplay": "Asia Pacific : Singapore", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": ***********.25}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 70.53, "classValue": "111", "contractId": 680827, "memberName": "Multiplier tester", "countryName": "Singapore", "description": "Total Expenses: SGD 89.99", "grossAmount": 70.53, "baseCurrency": "SGD", "classDisplay": "Asia Pacific : Singapore", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 89.99}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "MANAGEMENT_FEE_EOR", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 150.0, "classValue": "111", "contractId": 680827, "memberName": "Multiplier tester", "countryName": "Singapore", "description": "Management Fee: USD 150.00", "grossAmount": 150.0, "baseCurrency": "USD", "classDisplay": "Asia Pacific : Singapore", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 150.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "PLATFORM_FEE", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 5.0, "classValue": "", "contractId": -1, "memberName": "", "countryName": "", "description": "Processing Fee: USD 5.00", "grossAmount": 5.0, "baseCurrency": "USD", "classDisplay": "", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 5.0}], "emailSent": false, "externalInvoiceGenerated": true, "errorReason": null, "amountPaid": 0, "amountDue": ***********.13, "fullyPaidOnDate": null, "externalSystem": "NETSUITE", "syncedTime": "2024-07-31T09:49:10", "reason": null, "type": "SALARY", "totalAmount": ***********.13, "recordType": "INVOICE", "companyPayable": {"id": 167314}}, {"id": 165658, "createdDate": "2023-10-05T00:00:00", "dueDate": "2023-10-19T00:00:00", "status": "DRAFT", "reference": "Oct'23 Salary - EOR ( (India))", "createdOn": "2024-07-31T09:49:02.929516", "createdBy": 400005, "updatedOn": "2024-07-31T09:54:02.709235", "updatedBy": -1, "invoiceNo": "MTPLINV0079272", "externalId": "1220551", "lineItems": [{"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 6816.71, "classValue": "115", "contractId": 680828, "memberName": "Multiplier tester", "countryName": "India", "description": "Payroll Cost: INR 532,250.00", "grossAmount": 6816.71, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 532250.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 128.07, "classValue": "115", "contractId": 680828, "memberName": "Multiplier tester", "countryName": "India", "description": "Total Expenses: INR 10,000.00", "grossAmount": 128.07, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 10000.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "MANAGEMENT_FEE_EOR", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 150.0, "classValue": "115", "contractId": 680828, "memberName": "Multiplier tester", "countryName": "India", "description": "Management Fee: USD 150.00", "grossAmount": 150.0, "baseCurrency": "USD", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 150.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 69319.77, "classValue": "115", "contractId": 680830, "memberName": "Multiplier tester", "countryName": "India", "description": "Payroll Cost: INR 5,412,500.00", "grossAmount": 69319.77, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 5412500.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 128.07, "classValue": "115", "contractId": 680830, "memberName": "Multiplier tester", "countryName": "India", "description": "Total Expenses: INR 10,000.00", "grossAmount": 128.07, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 10000.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "MANAGEMENT_FEE_EOR", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 150.0, "classValue": "115", "contractId": 680830, "memberName": "Multiplier tester", "countryName": "India", "description": "Management Fee: USD 150.00", "grossAmount": 150.0, "baseCurrency": "USD", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 150.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 6816.71, "classValue": "115", "contractId": 685100, "memberName": "Multiplier tester", "countryName": "India", "description": "Payroll Cost (1): INR 532,250.00", "grossAmount": 6816.71, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 532250.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 6803.9, "classValue": "115", "contractId": 685100, "memberName": "Multiplier tester", "countryName": "India", "description": "Payroll Cost (2): INR 531,250.00", "grossAmount": 6803.9, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 11, 4], "startPayCycleDate": [2023, 10, 21], "amountInBaseCurrency": 531250.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 128.07, "classValue": "115", "contractId": 685100, "memberName": "Multiplier tester", "countryName": "India", "description": "Total Expenses (1): INR 10,000.00", "grossAmount": 128.07, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 10000.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 128.07, "classValue": "115", "contractId": 685100, "memberName": "Multiplier tester", "countryName": "India", "description": "Total Expenses (2): INR 10,000.00", "grossAmount": 128.07, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 11, 4], "startPayCycleDate": [2023, 10, 21], "amountInBaseCurrency": 10000.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "MANAGEMENT_FEE_EOR", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 150.0, "classValue": "115", "contractId": 685100, "memberName": "Multiplier tester", "countryName": "India", "description": "Management Fee: USD 150.00", "grossAmount": 150.0, "baseCurrency": "USD", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 150.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 6816.71, "classValue": "115", "contractId": 687931, "memberName": "Multiplier tester", "countryName": "India", "description": "Payroll Cost: INR 532,250.00", "grossAmount": 6816.71, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 532250.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 128.07, "classValue": "115", "contractId": 687931, "memberName": "Multiplier tester", "countryName": "India", "description": "Total Expenses: INR 10,000.00", "grossAmount": 128.07, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 10000.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "MANAGEMENT_FEE_EOR", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 150.0, "classValue": "115", "contractId": 687931, "memberName": "Multiplier tester", "countryName": "India", "description": "Management Fee: USD 150.00", "grossAmount": 150.0, "baseCurrency": "USD", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 150.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 6816.71, "classValue": "115", "contractId": 689936, "memberName": "Multiplier tester", "countryName": "India", "description": "Payroll Cost: INR 532,250.00", "grossAmount": 6816.71, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 532250.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 128.07, "classValue": "115", "contractId": 689936, "memberName": "Multiplier tester", "countryName": "India", "description": "Total Expenses: INR 10,000.00", "grossAmount": 128.07, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 10000.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "MANAGEMENT_FEE_EOR", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 150.0, "classValue": "115", "contractId": 689936, "memberName": "Multiplier tester", "countryName": "India", "description": "Management Fee: USD 150.00", "grossAmount": 150.0, "baseCurrency": "USD", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 150.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 69319.77, "classValue": "115", "contractId": 689937, "memberName": "Multiplier tester", "countryName": "India", "description": "Payroll Cost: INR 5,412,500.00", "grossAmount": 69319.77, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 5412500.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "EOR_SALARY_DISBURSEMENT", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 128.07, "classValue": "115", "contractId": 689937, "memberName": "Multiplier tester", "countryName": "India", "description": "Total Expenses: INR 10,000.00", "grossAmount": 128.07, "baseCurrency": "INR", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 10000.0}, {"account": null, "taxCode": "7", "taxRate": "0", "taxType": "GST_SG:ZR-SG", "itemType": "MANAGEMENT_FEE_EOR", "quantity": 1.0, "taxAmount": 0.0, "unitPrice": 150.0, "classValue": "115", "contractId": 689937, "memberName": "Multiplier tester", "countryName": "India", "description": "Management Fee: USD 150.00", "grossAmount": 150.0, "baseCurrency": "USD", "classDisplay": "Asia Pacific : India", "endPayCycleDate": [2023, 10, 31], "startPayCycleDate": [2023, 10, 1], "amountInBaseCurrency": 150.0}], "emailSent": false, "externalInvoiceGenerated": true, "errorReason": null, "amountPaid": 0, "amountDue": 174506.77, "fullyPaidOnDate": null, "externalSystem": "NETSUITE", "syncedTime": "2024-07-31T09:49:00", "reason": null, "type": "SALARY", "totalAmount": 174506.77, "recordType": "INVOICE", "companyPayable": {"id": 167312}}]