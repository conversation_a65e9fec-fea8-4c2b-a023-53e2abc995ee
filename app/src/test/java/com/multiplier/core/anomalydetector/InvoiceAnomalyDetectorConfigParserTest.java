package com.multiplier.core.anomalydetector;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.Charset;
import scala.collection.immutable.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InvoiceAnomalyDetectorConfigParserTest {

    @Spy
    InvoiceAnomalyDetectorConfigParser parser;

    @Test
    void givenConfigFile_whenReadJSONFile_thenValuesAreEquals() {
        // GIVEN
        var config = """
                {
                    "FX_RATE": {
                        "config": {
                          "variance": 0.05
                        },
                        "fxResult": {
                          "type": "ERROR",
                          "fxMessage":  {
                            "different_currency": "Variance between fx rate used is expected to be %s, but is %s",
                            "same_currency": "Fx rate used is not 1 for %s to %s"
                          }
                        },
                        "condition": {
                          "active": true,
                          "supported_invoice_types": ["FIRST_INVOICE", "SECOND_INVOICE"]
                        }
                    }
                }
                """;

        when(parser.getInputStream()).thenReturn(IOUtils.toInputStream(config, Charset.defaultCharset()));

        // WHEN
        var map = parser.readJSONFile();

        // THEN
        var fxRate = (Map<String, Map<String, Object>>) map.get("FX_RATE");
        var result =  (Map<String, Object>) fxRate.get("fxResult").get();
        var fxConfig = (Map<String, Object>) fxRate.get("config").get();
        var message = (Map<String, Object>) result.get("fxMessage").get();
        var differentCurrency = message.get("different_currency");

        assertEquals("Variance between fx rate used is expected to be %s, but is %s", differentCurrency.get());
        assertEquals(0.05, fxConfig.get("variance").get());
        // TODO Assert more fields
    }

    @Test
    void given_configString_shouldReturnReferenceText() {
        // GIVEN
        var config = """
                {
                    "REFERENCE_TEXT": {
                        "config": {
                          "first_invoice_text": "%s Gross Salary - EOR %s",
                          "second_invoice_text" : "%s Salary - EOR",
                          "invoice_text_date_format": "MMM''yy"
                        },
                        "result": {
                          "type": "ERROR",
                          "message": "Invoice Text is expected to be %s, but is %s"
                        },
                        "condition": {
                          "active": true,
                          "supported_invoice_types": ["FIRST_INVOICE", "SECOND_INVOICE"]
                        }
                    }
                }
                """;

        when(parser.getInputStream()).thenReturn(IOUtils.toInputStream(config, Charset.defaultCharset()));

        // WHEN
        var iad = parser.readJSONFile();

        // THEN
        var referenceText = (Map<String, Object>) iad.get("REFERENCE_TEXT");
        var iadConfig = (Map<String, Object>) referenceText.get("config").get();

        assertEquals(1, iad.size());
        assertEquals(3, iadConfig.size());
        assertEquals("%s Gross Salary - EOR %s", iadConfig.get("first_invoice_text").get());
    }

    @Test
    void given_nullConfig_should_returnMessage() {
        // GIVEN
        when(parser.getInputStream()).thenReturn(null);

        // WHEN
        var iad = parser.readJSONFile();

        // THEN
        assertEquals(0, iad.size());
    }
}