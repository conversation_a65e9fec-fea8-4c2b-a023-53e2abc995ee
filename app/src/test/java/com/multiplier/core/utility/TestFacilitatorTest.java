package com.multiplier.core.utility;

import lombok.Builder;
import lombok.Getter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.io.InputStream;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TestFacilitatorTest {

    private TestFacilitator testFacilitator = new TestFacilitator();

    @Test
    void givenInvalidFilePath_whenReadFileAsString_thenThrowIllegalArgumentException() {
        // GIVEN, WHEN, and THEN
        assertThrows(IllegalArgumentException.class,
                () -> testFacilitator.readFileAsString(null),
                TestFacilitator.FAILED_TO_READ_FILE);
    }

    @Test
    void givenEmptyFile_whenReadFileAsString_thenThrowIllegalArgumentException() {
        // GIVEN
        var relativeFilePath = "json/empty.json";
        var inputStream = this.getClass().getClassLoader().getResourceAsStream(relativeFilePath);

        // WHEN and THEN
        assertThrows(IllegalArgumentException.class,
                () -> testFacilitator.readFileAsString(inputStream),
                TestFacilitator.FAILED_TO_READ_FILE);
    }

    @Test
    void givenValidFile_whenReadFileAsString_thenThrowIllegalArgumentException() {
        // GIVEN
        var relativeFilePath = "json/creditNoteApiResponse.json";
        var inputStream = this.getClass().getClassLoader().getResourceAsStream(relativeFilePath);

        // WHEN
        var str = testFacilitator.readFileAsString(inputStream);

        // THEN
        assertNotNull(str);
    }

    @Test
    void givenFailedReadFile_whenReadFileAsString_thenThrowIllegalArgumentException() throws IOException {
        // GIVEN
        var inputStream = mock(InputStream.class);
        when(inputStream.readAllBytes()).thenThrow(new IOException("Something wrong"));

        // WHEN and THEN
        assertThrows(IllegalArgumentException.class,
                () -> testFacilitator.readFileAsString(inputStream),
                TestFacilitator.FAILED_TO_READ_FILE);
    }

    @Test
    void givenObject_whenConvertToMap_thenValuesAreCorrect() {
        // GIVEN
        var ingredient = "cheese";
        var pizza = Pizza.builder()
                .ingredient(ingredient)
                .build();

        // WHEN
        var map = testFacilitator.convertToMap(pizza);

        // THEN
        assertThat(map.get("ingredient")).isEqualTo(ingredient);
    }

    @Builder
    @Getter
    private static class Pizza {

        private final String ingredient;

    }

}