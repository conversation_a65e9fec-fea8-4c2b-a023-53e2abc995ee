package com.multiplier.core.payable.creditnote.composition;

import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.creditnote.facade.CreateCreditNoteItemRequest;
import com.multiplier.core.payable.creditnote.facade.CreateCreditNoteRequest;
import com.multiplier.core.payable.repository.model.ExternalSystem;
import com.multiplier.payable.types.CreditNoteReason;
import com.multiplier.payable.types.CurrencyCode;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;
import org.mockito.InjectMocks;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

class CreateCreditNoteApiRequestMapperTest {

    @InjectMocks
    private final CreateCreditNoteApiRequestMapper mapper = Mappers.getMapper(CreateCreditNoteApiRequestMapper.class);

    @Test
    void givenCreateRequestWithAllFields_whenMap_thenFieldsAreEquals() {
        // GIVEN
        var date = LocalDate.of(2023, 8, 24);
        var createRequest = CreateCreditNoteRequest.builder()
                .creditNoteNo("awesomeCreditNote")
                .date(date)
                .reference("awesomeReference")
                .customerId("awesomeCustomerId")
                .reason(CreditNoteReason.INVOICE_VOIDED)
                .companyId(42L)
                .currencyCode(CurrencyCode.USD)
                .month(12)
                .year(2023)
                .amountTotal(42d)
                .amountApplied(42d)
                .amountUnapplied(42d)
                .externalSystem(ExternalSystem.NETSUITE)
                .items(List.of(
                        CreateCreditNoteItemRequest.builder()
                                .itemType(LineItemType.GROSS_SALARY)
                                .description("awesomeDescription")
                                .unitAmount(42d)
                                .taxCode("awesomeTaxCode")
                                .countryName("awesome")
                                .contractId(42L)
                                .memberName("awesomeMemberNamme")
                                .baseCurrency(CurrencyCode.USD)
                                .amountInBaseCurrency(42d)
                                .revRecStartDate(date)
                                .revRecEndDate(date)
                                .build()
                ))
                .build();

        // WHEN
        var apiRequest = mapper.map(createRequest);

        // THEN
        assertEquals(createRequest.getDate(), apiRequest.getDate());
        assertEquals(createRequest.getReference(), apiRequest.getReference());
        assertEquals(createRequest.getCustomerId(), apiRequest.getCustomerId());
        assertEquals(com.multiplier.core.payable.creditnote.api.CreditNoteReason.valueOf(createRequest.getReason().name()), apiRequest.getReason());
        var item = createRequest.getItems().get(0);
        var apiItem = apiRequest.getItems().get(0);
        assertEquals(item.getUnitAmount(), apiItem.getUnitAmount());
        assertEquals(item.getTaxCode(), apiItem.getTaxCode());
        assertEquals(item.getCountryName(), apiItem.getCountryName());
        assertEquals(item.getContractId(), apiItem.getContractId());
        assertEquals(item.getMemberName(), apiItem.getMemberName());
        assertEquals(item.getBaseCurrency().name(), apiItem.getBaseCurrency());
        assertEquals(item.getAmountInBaseCurrency(), apiItem.getAmountInBaseCurrency());
        assertEquals(item.getRevRecStartDate(), apiItem.getRevRecStartDate());
        assertEquals(item.getRevRecEndDate(), apiItem.getRevRecEndDate());
    }

    @Test
    void givenCreateRequestWithoutRevRecStartAndEndDate_whenMap_thenDatesAreCalculatedBasedOnInvoiceDate() {
        // GIVEN
        var createRequest = CreateCreditNoteRequest.builder()
                .date(LocalDate.of(2023, 8, 1))
                .month(3)
                .year(2024)
                .items(List.of(
                        CreateCreditNoteItemRequest.builder()
                                .build()
                ))
                .build();

        // WHEN
        var apiRequest = mapper.map(createRequest);

        // THEN
        var apiItem = apiRequest.getItems().get(0);
        assertEquals(LocalDate.of(2024, 3, 1), apiItem.getRevRecStartDate());
        assertEquals(LocalDate.of(2024, 3, 31), apiItem.getRevRecEndDate());
    }

}