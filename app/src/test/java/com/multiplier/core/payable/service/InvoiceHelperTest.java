package com.multiplier.core.payable.service;

import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.repository.model.*;
import com.multiplier.core.payable.service.dataholder.ContractInvoiceOrCreditNoteData;
import com.multiplier.core.payable.service.dataholder.UpdateContractPayableInput;
import com.multiplier.payable.engine.domain.aggregates.AdditionalFeePayrollKey;
import com.multiplier.payable.engine.domain.entities.PayrollCycleFrequency;
import com.multiplier.payable.types.CurrencyCode;
import com.multiplier.payable.types.PayableItemType;
import lombok.val;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.multiplier.core.payable.service.InvoiceHelper.updateContractDataFromInvoiceOrCreditNote;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

class InvoiceHelperTest {
    private final static String SAMPLE_SALARY_DESCRIPTION = "568388 - Vivek EmpIND  (India) - Gross Salary : INR 10000.00";
    private final static String SAMPLE_PAYROLL_DESCRIPTION = "568388 - Vivek EmpIND  (India) - Actual Salary : INR 10000.00";
    private final static String SAMPLE_PAYROLL_NEW_DESCRIPTION = "568388 - Vivek EmpIND  (India) - Payroll Cost : INR 10000.00";
    private final static String SAMPLE_EXPENSE_DESCRIPTION = "568388 - Vivek EmpIND  (India) - Expenses : INR 1000.00";
    private final static String SAMPLE_MGT_FEE_DESCRIPTION = "568388 - Vivek EmpIND  (India) - Management Fee: USD 300.00";
    private final static String EXPECTED_LINE_ITEM_TYPE_SALARY = "Gross";
    private final static String EXPECTED_LINE_ITEM_TYPE_PAYROLL = "Payroll";
    private final static String EXPECTED_LINE_ITEM_TYPE_EXPENSES = "Expenses";
    private final static String EXPECTED_LINE_ITEM_TYPE_MGT = "Management";

    private final static String SAMPLE_SALARY_DESCRIPTION_2 = "568389 - Pham EmpIND  (Vietnam) - Gross Salary : VND 10000000.00";
    private final static Long SAMPLE_CONTRACT_ID = 568388L;
    private final static Long SAMPLE_CONTRACT_ID_2 = 568389L;
    private final static Double SAMPLE_SALARY_AMOUNT = 10000.00;
    private final static JpaPayableItemData SAMPLE_JPA_PAYABLE_ITEM_DATA_1 = JpaPayableItemData.builder()
            .contractId(SAMPLE_CONTRACT_ID).currencyCode(CurrencyCode.INR).build();
    private final static JpaPayableItem SAMPLE_JPA_PAYABLE_ITEM_1 = JpaPayableItem.builder()
            .type(PayableItemType.MEMBER_PAYROLL_COST).itemData(Set.of(SAMPLE_JPA_PAYABLE_ITEM_DATA_1))
            .description(SAMPLE_SALARY_DESCRIPTION).build();

    private final static JpaPayableItemData SAMPLE_JPA_PAYABLE_ITEM_DATA_2 = JpaPayableItemData.builder()
            .contractId(SAMPLE_CONTRACT_ID_2).currencyCode(CurrencyCode.VND).build();
    private final static JpaPayableItem SAMPLE_JPA_PAYABLE_ITEM_2 = JpaPayableItem.builder()
            .type(PayableItemType.MEMBER_PAYROLL_COST).itemData(Set.of(SAMPLE_JPA_PAYABLE_ITEM_DATA_2))
            .description(SAMPLE_SALARY_DESCRIPTION_2).build();
    private final static JpaCompanyPayable SAMPLE_COMPANY_PAYABLE = JpaCompanyPayable.builder()
            .companyId(123L).items(Set.of(SAMPLE_JPA_PAYABLE_ITEM_1, SAMPLE_JPA_PAYABLE_ITEM_2)).build();
    private final static JpaInvoiceLineItem SAMPLE_INVOICE_LINE_ITEM = JpaInvoiceLineItem.builder()
            .description(SAMPLE_SALARY_DESCRIPTION).build();

    @Test
    void should_return_correct_contract_id_from_description() {
        Optional<Long> contractIdFromSalaryDescription = InvoiceHelper.contractIdFromDescription(SAMPLE_SALARY_DESCRIPTION);
        assertTrue(contractIdFromSalaryDescription.isPresent());
        assertEquals(SAMPLE_CONTRACT_ID, contractIdFromSalaryDescription.get());
        Optional<Long> contractIdFromMgtFeeDescription = InvoiceHelper.contractIdFromDescription(SAMPLE_MGT_FEE_DESCRIPTION);
        assertTrue(contractIdFromMgtFeeDescription.isPresent());
        assertEquals(SAMPLE_CONTRACT_ID, contractIdFromMgtFeeDescription.get());
    }

    @Test
    void should_return_correct_currency_code_from_description() {
        val expected = CurrencyCode.INR;
        val actual = InvoiceHelper.getCurrencyCodeFromDescription(SAMPLE_SALARY_DESCRIPTION);
        assertEquals(expected, actual);
    }

    @Test
    void should_return_correct_contract_id_from_jpa_company_payable() {
        var contractIdOptional = InvoiceHelper.getContractIdFromCompanyPayable(SAMPLE_JPA_PAYABLE_ITEM_1);
        assertTrue(contractIdOptional.isPresent());
        val contractId = contractIdOptional.get();
        assertEquals(SAMPLE_CONTRACT_ID, contractId);
    }

    @Test
    void should_return_correct_amount_in_member_currency_from_description() {
        var amount = InvoiceHelper.getAmountInMemberCurrencyFromDescription(SAMPLE_INVOICE_LINE_ITEM.getDescription());
        assertEquals(SAMPLE_SALARY_AMOUNT, amount);
    }

    @Test
    void should_return_correct_contract_id_from_payable_item_first_invoice() {
        val result = InvoiceHelper.getPayrollCostByContractIdInFirstInvoice(SAMPLE_COMPANY_PAYABLE, SAMPLE_CONTRACT_ID_2);
        assertEquals(SAMPLE_JPA_PAYABLE_ITEM_2, result);
    }

    @Test
    void should_return_correct_contract_id_from_description_gross_pay_items() {
        var result = InvoiceHelper.contractIdFromDescriptionOfGrossPayItems(SAMPLE_SALARY_DESCRIPTION);
        assertEquals(SAMPLE_CONTRACT_ID, result);
    }

    @Test
    void should_return_null_from_description_mgt_fee() {
        var result = InvoiceHelper.contractIdFromDescriptionOfGrossPayItems(SAMPLE_MGT_FEE_DESCRIPTION);
        assertNull(result);
    }

    @Test
    void should_return_correct_line_item_type_from_description() {
        val resultSalary = InvoiceHelper.invoiceLineItemTypeFromDescription(SAMPLE_SALARY_DESCRIPTION);
        assertEquals(EXPECTED_LINE_ITEM_TYPE_SALARY, resultSalary);
        val resultPayroll = InvoiceHelper.invoiceLineItemTypeFromDescription(SAMPLE_PAYROLL_DESCRIPTION);
        assertEquals(EXPECTED_LINE_ITEM_TYPE_PAYROLL, resultPayroll);
        val resultExpenses = InvoiceHelper.invoiceLineItemTypeFromDescription(SAMPLE_EXPENSE_DESCRIPTION);
        assertEquals(EXPECTED_LINE_ITEM_TYPE_EXPENSES, resultExpenses);
        val resultMgt = InvoiceHelper.invoiceLineItemTypeFromDescription(SAMPLE_MGT_FEE_DESCRIPTION);
        assertEquals(EXPECTED_LINE_ITEM_TYPE_MGT, resultMgt);
    }

    @Test
    void should_return_payable_items_for_all_contracts() {
        val result = InvoiceHelper.jpaPayableItemDataForContractIds(List.of(SAMPLE_CONTRACT_ID, SAMPLE_CONTRACT_ID_2));
        val expectedResult = Set.of(SAMPLE_CONTRACT_ID, SAMPLE_CONTRACT_ID_2);
        assertFalse(result.isEmpty());
        val contractIdSetInResult = result.stream().map(JpaPayableItemData::getContractId).collect(Collectors.toSet());
        assertEquals(expectedResult, contractIdSetInResult);
    }

    @Nested
    class UpdateContractDataFromInvoiceOrCreditNote {
        @Test
        void givenCorrectData_shouldAddCorrectContractData() {
            // Given
            ContractInvoiceOrCreditNoteData contractData = ContractInvoiceOrCreditNoteData.builder().build();
            UpdateContractPayableInput input = UpdateContractPayableInput.builder()
                    .lineItemType(LineItemType.GROSS_SALARY)
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .description("Gross Salary")
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(100.0)
                    .unitPrice(50.0)
                    .taxRate(0.1)
                    .grossAmount(150.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();

            // When
            updateContractDataFromInvoiceOrCreditNote(contractData, input);

            // Then
            assertEquals(50.0, contractData.getGrossUnitPrice());
            assertEquals(Pair.of(CurrencyCode.USD, 100.0), contractData.getGrossSalary());
        }

        @Test
        void givenAlreadyBilledData_shouldAddCorrectContractData() {
            // Given
            ContractInvoiceOrCreditNoteData contractData = ContractInvoiceOrCreditNoteData.builder().build();
            UpdateContractPayableInput input = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .lineItemType(LineItemType.GROSS_SALARY)
                    .description("Gross Salary already billed")
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(100.0)
                    .unitPrice(50.0)
                    .taxRate(0.1)
                    .grossAmount(55.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();

            // When
            updateContractDataFromInvoiceOrCreditNote(contractData, input);

            // Then
            assertEquals(55.0, contractData.getRefundedGrossSalaryGrossAmount(), 0.01);
            assertEquals(Pair.of(CurrencyCode.USD, 100.0), contractData.getRefundedGrossSalary());
        }

        @Test
        void givenRandomDescriptionAndNullData_shouldRemainContractData() {
            // Given
            ContractInvoiceOrCreditNoteData contractData = ContractInvoiceOrCreditNoteData.builder().build();
            UpdateContractPayableInput input = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .lineItemType(LineItemType.GROSS_SALARY)
                    .description("Some random description")
                    .build();

            // When
            updateContractDataFromInvoiceOrCreditNote(contractData, input);

            // Then
            // Verify that contractData is not updated
            assertNull(contractData.getRefundedGrossSalaryGrossAmount());
            assertNull(contractData.getRefundedGrossSalary());
            assertNull(contractData.getExpensesFeeUnitPrice());
            assertNull(contractData.getExpensesFee());
            assertNull(contractData.getGrossUnitPrice());
            assertNull(contractData.getGrossSalary());
            assertNull(contractData.getPayrollUnitPrice());
            assertNull(contractData.getPayroll());
            assertThat(contractData.getTaxRates()).isEmpty();
            assertNull(contractData.getRefundedManagementFeeUnitPrice());
            assertNull(contractData.getRefundedManagementFee());
            assertNull(contractData.getRefundManagementFeeGrossAmount());
            assertNull(contractData.getMemberManagementFeeReversalUnitPrice());
            assertNull(contractData.getMemberManagementFeeReversal());
            assertNull(contractData.getActualManagementFeeUnitPrice());
            assertNull(contractData.getActualManagementFee());
            assertNull(contractData.getActualManagementFeeGrossAmount());
            assertNull(contractData.getManagementFeeInFirstInvoiceUnitPrice());
            assertNull(contractData.getManagementFeeInFirstInvoice());
            assertNull(contractData.getManagementFeeInFirstInvoiceGrossAmount());
            assertNull(contractData.getManagementFeeUnitPrice());
            assertNull(contractData.getManagementFee());
            assertNull(contractData.getTotalManagementFee());
        }

        @Test
        void givenBonusInfo_shouldUpdateContractDataWithTotal() {
            // Given
            ContractInvoiceOrCreditNoteData contractData = ContractInvoiceOrCreditNoteData.builder().build();
            UpdateContractPayableInput input1 = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .lineItemType(LineItemType.GROSS_SALARY)
                    .description("Gross Salary")
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(100.0)
                    .unitPrice(50.0)
                    .taxRate(0.2)
                    .grossAmount(120.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();

            UpdateContractPayableInput input2 = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .lineItemType(LineItemType.GROSS_SALARY)
                    .description("13th Month Pay")
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(200.0)
                    .unitPrice(75.0)
                    .taxRate(0.15)
                    .grossAmount(230.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();

            UpdateContractPayableInput input3 = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .lineItemType(LineItemType.GROSS_SALARY)
                    .description("Festive Bonus")
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(300.0)
                    .unitPrice(100.0)
                    .taxRate(0.15)
                    .grossAmount(345.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();

            // When
            updateContractDataFromInvoiceOrCreditNote(contractData, input1);
            updateContractDataFromInvoiceOrCreditNote(contractData, input2);
            updateContractDataFromInvoiceOrCreditNote(contractData, input3  );

            // Then
            assertEquals(225.0, contractData.getGrossUnitPrice());
            assertEquals(Pair.of(CurrencyCode.USD, 600.0), contractData.getGrossSalary());
        }

        @Test
        void givenMix2ndAnd1st_shouldUpdateContractDataWithTotalCorrectly() {
            // Given
            ContractInvoiceOrCreditNoteData contractData = ContractInvoiceOrCreditNoteData.builder()
                    .contractId(1L).build();
            UpdateContractPayableInput input1 = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .lineItemType(LineItemType.GROSS_SALARY)
                    .description("Gross Salary")
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(100.0)
                    .unitPrice(50.0)
                    .taxRate(0.2)
                    .grossAmount(120.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();

            UpdateContractPayableInput input2 = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .lineItemType(LineItemType.GROSS_SALARY)
                    .description("13th Month Pay")
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(200.0)
                    .unitPrice(75.0)
                    .taxRate(0.15)
                    .grossAmount(230.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();

            UpdateContractPayableInput input3 = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .lineItemType(LineItemType.GROSS_SALARY)
                    .description("Gross Salary already billed")
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(100.0)
                    .unitPrice(50.0)
                    .taxRate(0.2)
                    .grossAmount(6.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();

            UpdateContractPayableInput input4 = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .lineItemType(LineItemType.GROSS_SALARY)
                    .description("Month Pay already billed")
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(200.0)
                    .unitPrice(75.0)
                    .taxRate(0.15)
                    .grossAmount(87.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();

            // When
            updateContractDataFromInvoiceOrCreditNote(contractData, input1);
            updateContractDataFromInvoiceOrCreditNote(contractData, input2);
            updateContractDataFromInvoiceOrCreditNote(contractData, input3);
            updateContractDataFromInvoiceOrCreditNote(contractData, input4);

            // Then
            assertEquals(125.0, contractData.getGrossUnitPrice());
            assertEquals(Pair.of(CurrencyCode.USD, 300.0), contractData.getGrossSalary());
            assertEquals(93.0, contractData.getRefundedGrossSalaryGrossAmount());
            assertEquals(Pair.of(CurrencyCode.USD, 300.0), contractData.getRefundedGrossSalary());
        }

        @Test
        void givenPayrollEnumNullDesc_shouldNotThrow() {
            // Given
            ContractInvoiceOrCreditNoteData contractData = ContractInvoiceOrCreditNoteData.builder().build();
            UpdateContractPayableInput input = UpdateContractPayableInput.builder()
                    .lineItemType(LineItemType.EOR_SALARY_DISBURSEMENT)
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .description(null)
                    .build();

            // When + Then
            assertDoesNotThrow(() -> updateContractDataFromInvoiceOrCreditNote(contractData, input));
        }

        @Test
        void testUpdateContractDataFromInvoiceWithGrossSalaryAndManagementFee() {
            // Given
            ContractInvoiceOrCreditNoteData contractData = ContractInvoiceOrCreditNoteData.builder().build();
            UpdateContractPayableInput input1 = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .lineItemType(LineItemType.GROSS_SALARY)
                    .description("Gross Salary")
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(100.0)
                    .unitPrice(50.0)
                    .taxRate(0.1)
                    .grossAmount(150.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();

            UpdateContractPayableInput input2 = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_MANAGEMENT_FEE)
                    .lineItemType(LineItemType.MANAGEMENT_FEE_EOR)
                    .description("Management Fee")
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(200.0)
                    .unitPrice(100.0)
                    .taxRate(0.2)
                    .grossAmount(300.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();

            // When
            updateContractDataFromInvoiceOrCreditNote(contractData, input1);
            updateContractDataFromInvoiceOrCreditNote(contractData, input2);

            // Then
            assertEquals(50.0, contractData.getGrossUnitPrice());
            assertEquals(Pair.of(CurrencyCode.USD, 100.0), contractData.getGrossSalary());
            assertEquals(100.0, contractData.getManagementFeeUnitPrice());
            assertEquals(Pair.of(CurrencyCode.USD, 200.0), contractData.getManagementFee());
            assertEquals(300.0, contractData.getTotalManagementFee());
        }

        @Test
        void givenNullDataForAllFieldsExceptPayableItemType_shouldReturnNullContractData() {
            // Given
            ContractInvoiceOrCreditNoteData contractData = ContractInvoiceOrCreditNoteData.builder().build();
            UpdateContractPayableInput input = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .lineItemType(LineItemType.GROSS_SALARY)
                    .build();

            // When
            updateContractDataFromInvoiceOrCreditNote(contractData, input);

            // Then
            // Assert that the contract data remains unchanged
            assertNull(contractData.getRefundedGrossSalaryGrossAmount());
            assertNull(contractData.getRefundedGrossSalary());
            assertNull(contractData.getExpensesFeeUnitPrice());
            assertNull(contractData.getExpensesFee());
            assertNull(contractData.getGrossUnitPrice());
            assertNull(contractData.getGrossSalary());
            assertNull(contractData.getPayrollUnitPrice());
            assertNull(contractData.getPayroll());
            assertThat(contractData.getTaxRates()).isEmpty();
            assertNull(contractData.getRefundedManagementFeeUnitPrice());
            assertNull(contractData.getRefundedManagementFee());
            assertNull(contractData.getRefundManagementFeeGrossAmount());
            assertNull(contractData.getMemberManagementFeeReversalUnitPrice());
            assertNull(contractData.getMemberManagementFeeReversal());
            assertNull(contractData.getActualManagementFeeUnitPrice());
            assertNull(contractData.getActualManagementFee());
            assertNull(contractData.getActualManagementFeeGrossAmount());
            assertNull(contractData.getManagementFeeInFirstInvoiceUnitPrice());
            assertNull(contractData.getManagementFeeInFirstInvoice());
            assertNull(contractData.getManagementFeeInFirstInvoiceGrossAmount());
            assertNull(contractData.getManagementFeeUnitPrice());
            assertNull(contractData.getManagementFee());
            assertNull(contractData.getTotalManagementFee());
        }

        @Test
        void givenGrossAmountSalary_shouldUpdateContractDataWithGrossAmountSalary() {
            // Given
            ContractInvoiceOrCreditNoteData contractData = ContractInvoiceOrCreditNoteData.builder().build();
            UpdateContractPayableInput input = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .lineItemType(LineItemType.EOR_SALARY_DISBURSEMENT)
                    .description(SAMPLE_PAYROLL_DESCRIPTION)
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(100.0)
                    .unitPrice(50.0)
                    .taxRate(0.2)
                    .grossAmount(120.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();

            // When
            updateContractDataFromInvoiceOrCreditNote(contractData, input);

            // Then
            assertEquals(120.0, contractData.getPayrollGrossAmount());
            assertEquals(50.0, contractData.getPayrollUnitPrice());
            assertEquals(Pair.of(CurrencyCode.USD, 100.0), contractData.getPayroll());
        }

        @Test
        void givenGrossAmountSalaryWithNewDescription_shouldUpdateContractDataWithGrossAmountSalary() {
            // Given
            ContractInvoiceOrCreditNoteData contractData = ContractInvoiceOrCreditNoteData.builder().build();
            UpdateContractPayableInput input = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .lineItemType(LineItemType.EOR_SALARY_DISBURSEMENT)
                    .description(SAMPLE_PAYROLL_NEW_DESCRIPTION)
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(100.0)
                    .unitPrice(50.0)
                    .taxRate(0.2)
                    .grossAmount(120.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();

            // When
            updateContractDataFromInvoiceOrCreditNote(contractData, input);

            // Then
            assertEquals(120.0, contractData.getPayrollGrossAmount());
            assertEquals(50.0, contractData.getPayrollUnitPrice());
            assertEquals(Pair.of(CurrencyCode.USD, 100.0), contractData.getPayroll());
        }

        @Test
        void givenGrossSalaryAndPayrollWithDifferentTaxRates_thenReturnSetOfTaxRates() {
            // Given
            ContractInvoiceOrCreditNoteData contractData = ContractInvoiceOrCreditNoteData.builder().build();
            UpdateContractPayableInput grossSalaryInput = UpdateContractPayableInput.builder()
                    .lineItemType(LineItemType.GROSS_SALARY)
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .description(SAMPLE_SALARY_DESCRIPTION)
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(100.0)
                    .unitPrice(50.0)
                    .taxRate(0.0)
                    .grossAmount(120.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.GROSS)
                    .build();
            UpdateContractPayableInput salaryInput = UpdateContractPayableInput.builder()
                    .lineItemType(LineItemType.EOR_SALARY_DISBURSEMENT)
                    .payableItemType(PayableItemType.MEMBER_PAYROLL_COST)
                    .description(SAMPLE_PAYROLL_DESCRIPTION)
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(100.0)
                    .unitPrice(50.0)
                    .taxRate(9.0)
                    .grossAmount(120.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.SALARY)
                    .build();

            // When
            updateContractDataFromInvoiceOrCreditNote(contractData, grossSalaryInput);
            updateContractDataFromInvoiceOrCreditNote(contractData, salaryInput);

            // Then
            assertEquals(Map.of(LineItemType.GROSS_SALARY, Set.of(0.0), LineItemType.EOR_SALARY_DISBURSEMENT, Set.of(9.0)), contractData.getTaxRates());
        }

        @Test
        void givenSeveranceLineItemWithNewDescription_shouldUpdateContractDataWithSeveranceTaxRate() {
            // Given
            ContractInvoiceOrCreditNoteData contractData = ContractInvoiceOrCreditNoteData.builder().build();
            UpdateContractPayableInput input = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL)
                    .lineItemType(LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL)
                    .description("severance accrual")
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(100.0)
                    .unitPrice(50.0)
                    .taxRate(0.2)
                    .grossAmount(120.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.SALARY)
                    .build();

            // When
            updateContractDataFromInvoiceOrCreditNote(contractData, input);
            Double taxRate = contractData.getTaxRates().get(LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL).stream().max(Double::compareTo).orElse(0.0);
            // Then
            assertEquals(0.2, taxRate);
            assertEquals(100, contractData.getSeveranceInBaseCurrency());
            assertEquals(120, contractData.getSeveranceInBillingCurrency());
        }

        @Test
        void givenAdditionalMgtFee_shouldAddCorrectContractData() {
            // Given
            ContractInvoiceOrCreditNoteData contractData = ContractInvoiceOrCreditNoteData.builder().build();
            UpdateContractPayableInput input = UpdateContractPayableInput.builder()
                    .payableItemType(PayableItemType.ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL)
                    .lineItemType(LineItemType.ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL)
                    .description("Management Fee for SM2: USD 50.0")
                    .currencyCode(CurrencyCode.USD)
                    .billableCost(50.0)
                    .unitPrice(50.0)
                    .taxRate(0.1)
                    .grossAmount(55.0)
                    .invoice(true)
                    .invoiceType(InvoiceType.SALARY)
                    .build();

            // When
            updateContractDataFromInvoiceOrCreditNote(contractData, input);

            // Then
            assertThat(contractData.getAdditionalManagementFeeUnitPrices()).isNotNull()
                    .containsEntry(new AdditionalFeePayrollKey(PayrollCycleFrequency.SEMIMONTHLY, 2), 50.0);
            assertThat(contractData.getAdditionalManagementFeeGrossAmounts()).isNotNull()
                    .containsEntry(new AdditionalFeePayrollKey(PayrollCycleFrequency.SEMIMONTHLY, 2), 55.0);
            assertThat(contractData.getAdditionalManagementFees()).isNotNull()
                    .containsEntry(new AdditionalFeePayrollKey(PayrollCycleFrequency.SEMIMONTHLY, 2), Pair.of(CurrencyCode.USD, 50.0));
        }
    }

    @ParameterizedTest
    @CsvSource(value = {
            "Gross Salary, MEMBER_PAYROLL_COST",
            "1st Month Pay, MEMBER_PAYROLL_COST",
            "2nd Month Pay, MEMBER_PAYROLL_COST",
            "Festive Bonus, MEMBER_PAYROLL_COST",
            "Management Fee, MEMBER_MANAGEMENT_FEE",
            "Deposit, MEMBER_DEPOSIT",
            "Processing Fee, MEMBER_PROCESSING_FEE_FOR_EOR",
            "Random, null",
            "Pay Supplement, MEMBER_PAYROLL_COST",
            "Expense, MEMBER_PAYROLL_COST",
            "Severance accrual, SEVERANCE_DEPOSIT_EOR_PAYROLL",
            "Severance accrual adjustment, SEVERANCE_DEPOSIT_EOR_PAYROLL_REFUND",

    },
            nullValues = "null")
    void should_return_correct_payable_item_type(String description, String expectedPayableItemTypeString) {
        // WHEN
        var result = InvoiceHelper.payableItemTypeFromDescription(description);

        // THEN
        if (expectedPayableItemTypeString == null) {
            assertThat(result).isNull();
        } else {
            assertEquals(PayableItemType.valueOf(expectedPayableItemTypeString), result);
        }

    }

    @ParameterizedTest
    @CsvSource(
            value = {
                    "GROSS_SALARY, MEMBER_PAYROLL_COST",
                    "EOR_SALARY_DISBURSEMENT, MEMBER_PAYROLL_COST",
                    "SEVERANCE_DEPOSIT_EOR_PAYROLL, SEVERANCE_DEPOSIT_EOR_PAYROLL",
                    "SEVERANCE_DEPOSIT_EOR_PAYROLL_REFUND, SEVERANCE_DEPOSIT_EOR_PAYROLL",
                    "ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL, ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL",
                    "MANAGEMENT_FEE_PEO, null"
            },
            nullValues = "null"
    )
    void should_return_correct_payable_item_type_from_line_item_type(LineItemType lineItemType, PayableItemType expectedPayableItemType) {
        assertThat(InvoiceHelper.fromLineItemType(lineItemType)).isEqualTo(expectedPayableItemType);
    }
}
