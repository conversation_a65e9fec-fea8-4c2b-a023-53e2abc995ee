package com.multiplier.core.payable.docgen.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class DocumentResponseTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void givenJson_whenDeserialize_thenValuesAreEqual() throws IOException {
        // GIVEN
        var documentResponse = DocumentResponse.builder()
                .country("test")
                .frozen(true)
                .recipients(List.of(
                        DocumentRecipient.builder()
                                .firstName("John")
                                .build()
                ))
                .build();
        var json = objectMapper.writeValueAsString(documentResponse);

        // WHEN
        var documentResponseAgain = objectMapper.readValue(json, DocumentResponse.class);

        // THEN
        assertEquals(documentResponse.getCountry(), documentResponseAgain.getCountry());
        assertTrue(documentResponseAgain.isFrozen());
        assertEquals(documentResponse.getRecipients().get(0).getFirstName(),
                documentResponseAgain.getRecipients().get(0).getFirstName());
    }
}