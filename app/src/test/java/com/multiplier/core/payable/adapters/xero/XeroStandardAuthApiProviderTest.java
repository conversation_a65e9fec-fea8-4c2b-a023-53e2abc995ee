package com.multiplier.core.payable.adapters.xero;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import jakarta.xml.bind.DatatypeConverter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.sql.Date;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

class XeroStandardAuthApiProviderTest {

    private static final long TWELVE_MINUTES_IN_MILLIS = 12 * 60 * 1000;
    public static final String SECRET = "asdfaefaefawefawefawefaewfawefawefawefdsfgfgsertrhtjtretertrggeawfsthregserfaw";
    private static final long NINE_MINUTES_IN_MILLIS = 9 * 60 * 1000;

    private final XeroStandardAuthApiProvider provider = new XeroStandardAuthApiProvider("clientid"
            , "clientsecret", "http://token.server.url", null);

    @Test
    void should_ReturnFalse_when_TokenIsNull() {
        boolean actual = provider.isValidForAtLeast10Minutes(null);
        assertThat(actual, is(false));

    }

    @Test
    void should_ReturnTrue_when_TokenIsValidMoreThan10Minutes() {
        String token = generateTokenExpireIn(TWELVE_MINUTES_IN_MILLIS);
        boolean actual = provider.isValidForAtLeast10Minutes(token);
        assertThat(actual, is(true));

    }

    @Test
    void should_ReturnFalse_when_TokenIsValidLessThan10Minutes() {
        String token = generateTokenExpireIn(NINE_MINUTES_IN_MILLIS);
        boolean actual = provider.isValidForAtLeast10Minutes(token);
        assertThat(actual, is(false));

    }

    private String generateTokenExpireIn(Long lifetime) {
        long nowMillis = System.currentTimeMillis();
        long expiryInMillis = nowMillis + lifetime;
        Date expiry = new Date(expiryInMillis);

        byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(SECRET);
        Key signingKey = new SecretKeySpec(apiKeySecretBytes, SignatureAlgorithm.HS256.getJcaName());

        return Jwts.builder().setId("23423")
                .setIssuedAt(new Date(nowMillis))
                .setSubject("Subject")
                .setIssuer("Issuer")
                .setExpiration(expiry)
                .signWith(signingKey).compact();
    }

    @ParameterizedTest
    @CsvSource(nullValues = "NULL", value = {
            "NULL,null", // input is null
            "abc,abc",
            "abcd,abcd",
            "abcdefgh,efgh"
    })
    void should_return_null_for_null_token_input(String input, String expectedOutput) {
        String output = new XeroStandardAuthApiProvider(null, null, null, null).maskRefreshToken(input);
        assertThat(output, is(expectedOutput));
    }
}