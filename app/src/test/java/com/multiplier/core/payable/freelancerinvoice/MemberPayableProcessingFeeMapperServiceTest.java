package com.multiplier.core.payable.freelancerinvoice;

import com.multiplier.core.payable.company.Company;
import com.multiplier.core.payable.company.adapter.CompanyServiceAdapter;
import com.multiplier.core.payable.service.exception.InvoiceGenerationException;
import com.multiplier.core.payable.service.exception.ValidationException;
import com.multiplier.core.payable.tax.TaxCodeService;
import com.multiplier.payable.grpc.schema.FreelancerInvoiceLineItem;
import com.multiplier.payable.grpc.schema.LineItemType;
import com.multiplier.payable.types.*;
import lombok.val;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class MemberPayableProcessingFeeMapperServiceTest {
    private final CompanyServiceAdapter companyServiceAdapter = Mockito.mock(CompanyServiceAdapter.class);
    private final TaxCodeService taxCodeService = Mockito.mock(TaxCodeService.class);
    private final MemberPayableProcessingFeeMapperService memberPayableProcessingFeeMapperService
            = new MemberPayableProcessingFeeMapperService(companyServiceAdapter, taxCodeService);

    @Test
    void throw_exception_if_company_location_not_exists() {
        val freelancerInvoiceLineItem = FreelancerInvoiceLineItem.newBuilder()
                .setLineItemType(LineItemType.PAYIN_CREDIT_CARD_FEE)
                .setAmount(33.33)
                .build();
        when(companyServiceAdapter.getCompanyById(1L)).thenReturn(Company.builder()
                .id(1L)
                .primaryEntity(LegalEntity.newBuilder()
                        .address(Address.newBuilder().build())
                        .build())
                .build());

        assertThrows(InvoiceGenerationException.class,
                () -> memberPayableProcessingFeeMapperService.map(
                        freelancerInvoiceLineItem,
                        "USD",
                        1L
                )
        );
    }

    @Test
    void throw_exception_if_line_item_not_supported() {
        when(companyServiceAdapter.getCompanyById(1L)).thenReturn(Company.builder()
                .id(1L)
                .primaryEntity(LegalEntity.newBuilder()
                        .address(Address.newBuilder().country(CountryCode.USA).build())
                        .build())
                .build());

        val freelancerInvoiceLineItem = FreelancerInvoiceLineItem.newBuilder()
                .setLineItemType(LineItemType.MANAGEMENT_FEE)
                .setAmount(33.33)
                .build();
        assertThrows(ValidationException.class,
                () -> memberPayableProcessingFeeMapperService.map(
                        freelancerInvoiceLineItem,
                        "USD",
                        1L
                )
        );
    }

    @ParameterizedTest
    @MethodSource("should_map_correctly_input")
    void should_map_correctly(LineItemType lineItemType, String expectedDescription) {
        when(companyServiceAdapter.getCompanyById(1L)).thenReturn(Company.builder()
                .id(1L)
                .primaryEntity(LegalEntity.newBuilder()
                        .address(Address.newBuilder().country(CountryCode.USA).build())
                        .build())
                .build());
        when(taxCodeService.getTaxCode(CountryCode.USA, PayableItemType.MEMBER_PROCESSING_FEE_FOR_FREELANCER))
                .thenReturn("taxCodeId");

        val result = memberPayableProcessingFeeMapperService.map(
                FreelancerInvoiceLineItem.newBuilder()
                        .setLineItemType(lineItemType)
                        .setAmount(33.33)
                        .build(),
                "USD",
                1L
        );

        assertEquals(CurrencyCode.USD, result.getCurrencyCode());
        assertEquals(PayableItemType.MEMBER_PROCESSING_FEE_FOR_FREELANCER, result.getType());
        assertEquals(33.33, result.getTotalCost());
        assertEquals(expectedDescription, result.getDescription());
        assertEquals(33.33, result.getBillableCost());
        assertEquals("taxCodeId", result.getTaxType());
    }

    private static Stream<Arguments> should_map_correctly_input() {
        return Stream.of(
            Arguments.of(LineItemType.PAYIN_BANK_TRANSFER_FEE, MemberPayableExternalInvoiceService.BANK_TRANSFER_FEE_DESCRIPTION),
            Arguments.of(LineItemType.PAYIN_CREDIT_CARD_FEE, MemberPayableExternalInvoiceService.CREDIT_CARD_FEE_DESCRIPTION),
            Arguments.of(LineItemType.PAYIN_ACH_DIRECT_DEBIT_FEE, MemberPayableExternalInvoiceService.DIRECT_DEBIT_FEE_DESCRIPTION),
            Arguments.of(LineItemType.PAYOUT_FEE, MemberPayableProcessingFeeMapperService.PAYOUT_FEE_DESCRIPTION)
        );
    }

}