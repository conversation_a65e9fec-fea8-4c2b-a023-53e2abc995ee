package com.multiplier.core.payable.payment;

import com.google.protobuf.Timestamp;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import static com.multiplier.core.payable.payment.TimeZoneUtil.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

class TimeZoneUtilTest {

    @Test
    void testConvertTimezoneToLocalDateInUTC_NullTimestamp() {
        // Arrange
        Timestamp timestamp = null;

        // Act
        LocalDate result = convertTimezoneToLocalDateInUTC(timestamp);

        // Assert
        assertNull(result);
    }

    @Test
    void testConvertTimezoneToLocalDateInUTC() {
        // Arrange
        Timestamp timestamp = Timestamp
                .newBuilder()
                .setSeconds(1630011600L)
                .build();

        // Act
        LocalDate result = convertTimezoneToLocalDateInUTC(timestamp);

        // Assert
        assertEquals(2021, result.getYear());
        assertEquals(8, result.getMonthValue()); // August
        assertEquals(26, result.getDayOfMonth());
    }

    @Test
    void testConvertLocalDateToTimezoneUTC_NullLocalDate() {
        // Arrange
        LocalDate localDate = null;

        // Act
        Timestamp result = convertLocalDateToTimezoneUTC(localDate);

        // Assert
        assertNull(result);
    }

    @Test
    void testConvertLocalDateToTimezoneUTC() {
        // Arrange
        LocalDate localDate = LocalDate.of(2021, 8, 27); // 2021-08-27

        // Act
        Timestamp result = convertLocalDateToTimezoneUTC(localDate);

        // Assert
        long expectedEpochSeconds = localDate.atStartOfDay().toEpochSecond(ZoneOffset.UTC);
        assertEquals(expectedEpochSeconds, result.getSeconds());
    }

}