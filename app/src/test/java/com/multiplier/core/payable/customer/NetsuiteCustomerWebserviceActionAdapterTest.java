package com.multiplier.core.payable.customer;

import com.multiplier.core.config.featureflag.FeatureFlagService;
import com.multiplier.core.payable.adapters.api.CustomerDto;
import com.multiplier.core.payable.customer.NetsuiteWebserviceCustomerMapper;
import com.multiplier.core.payable.adapters.netsuite.webservices.NetsuiteWebserviceClient;
import com.multiplier.core.payable.creditnote.api.ApiResponseParser;
import com.multiplier.core.payable.customer.NetsuiteCustomerSearchAdapter;
import com.multiplier.core.payable.customer.NetsuiteCustomerWebserviceActionAdapter;
import com.multiplier.growthbook.sdk.model.GBFeatureResult;
import com.multiplier.growthbook.sdk.model.GBFeatureSource;
import com.netsuite.suitetalk.proxy.v2023_1.lists.relationships.Customer;
import com.netsuite.suitetalk.proxy.v2023_1.platform.messages.WriteResponse;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class NetsuiteCustomerWebserviceActionAdapterTest {

    @Mock
    private NetsuiteWebserviceCustomerMapper netsuiteWebserviceCustomerMapper;

    @Mock
    private NetsuiteWebserviceClient netsuiteWebserviceClient;

    @Mock
    private ApiResponseParser apiResponseParser;

    @Mock
    private NetsuiteCustomerSearchAdapter netsuiteCustomerSearchAdapter;

    @Mock
    private FeatureFlagService featureFlagService;

    @InjectMocks
    private NetsuiteCustomerWebserviceActionAdapter adapter;

    @Test
    void should_return_true_if_flag_is_on() {
        when(featureFlagService.feature("netsuite-should-use-web-service-for-customer", Map.of()))
                .thenReturn(new GBFeatureResult(null, true, false, GBFeatureSource.defaultValue, null, null));
        assertTrue(adapter.enabled());
    }

    @Test
    void should_return_true_if_flag_is_off() {
        when(featureFlagService.feature("netsuite-should-use-web-service-for-customer", Map.of()))
                .thenReturn(new GBFeatureResult(null, false, true, GBFeatureSource.defaultValue, null, null));
        assertFalse(adapter.enabled());
    }

    @Test
    void should_create_customer_from_dto() {
        var customerDto = CustomerDto.builder()
                        .build();
        var nsCustomer = new Customer();
        var writeResponse = new WriteResponse();
        when(netsuiteWebserviceCustomerMapper.map(customerDto, null))
                .thenReturn(nsCustomer);
        when(netsuiteWebserviceClient.createCustomer(nsCustomer))
                .thenReturn(writeResponse);
        when(apiResponseParser.getInternalId(writeResponse))
                .thenReturn("1234");

        var externalId = adapter.createCustomer(customerDto);

        verify(netsuiteWebserviceCustomerMapper, times(1)).map(customerDto, null);
        verify(netsuiteWebserviceClient, times(1)).createCustomer(nsCustomer);
        verify(apiResponseParser, times(1)).getInternalId(writeResponse);
        assertEquals("1234", externalId);
    }
}