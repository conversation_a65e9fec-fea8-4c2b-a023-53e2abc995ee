package com.multiplier.core.payable.service;

import com.multiplier.core.config.featureflag.FeatureFlagService;
import com.multiplier.core.payable.company.Company;
import com.multiplier.core.payable.customer.CustomerActionFactory;
import com.multiplier.core.payable.adapters.PayableServiceAdapter;
import com.multiplier.core.payable.adapters.api.AddressDto;
import com.multiplier.core.payable.adapters.api.CustomerDto;
import com.multiplier.core.payable.adapters.api.InvoiceGenerationAdapter;
import com.multiplier.core.payable.adapters.api.InvoiceGenerationFactory;
import com.multiplier.core.payable.customer.NetsuiteCustomerWebserviceActionAdapter;
import com.multiplier.core.payable.adapters.netsuite.models.CustomerUpdateOrCreateResult;
import com.multiplier.core.payable.company.adapter.CompanyServiceAdapter;
import com.multiplier.core.payable.repository.JpaCompanyBindingRepository;
import com.multiplier.core.payable.repository.model.ExternalSystem;
import com.multiplier.core.payable.repository.model.JpaCompanyBinding;
import com.multiplier.core.payable.service.mapper.AddressMapper;
import com.multiplier.core.payable.service.mapper.CompanyBindingMapper;
import com.multiplier.core.util.Pair;
import com.multiplier.payable.kafka.LegalEntityEvent;
import com.multiplier.payable.kafka.LegalEntityEventStatus;
import com.multiplier.payable.types.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CompanyBindingServiceTest {

    private static final long COMPANY_ID = 23435;
    @Mock
    JpaCompanyBindingRepository companyBindingRepository;
    @Mock
    InvoiceGenerationFactory invoiceGenerationFactory;
    @Mock
    InvoiceGenerationAdapter invoiceGenerationAdapter;
    @Mock
    PayableServiceAdapter payableServiceAdapter;
    @Mock
    private AddressMapper addressMapper;
    @Mock
    private CustomerActionFactory customerActionFactory;
    @Mock
    private NetsuiteCustomerWebserviceActionAdapter netsuiteCustomerWebserviceActionAdapter;
    @Mock
    private FeatureFlagService featureFlagService;

    @InjectMocks
    CompanyBindingService companyBindingService;

    private final CompanyMapper payableCompanyMapper = Mappers.getMapper(CompanyMapper.class);

    @Mock
    CompanyServiceAdapter companyServiceAdapter;

    @BeforeEach
    void setUp() {

        CompanyBindingMapper mapper = Mappers.getMapper(CompanyBindingMapper.class);
        ReflectionTestUtils.setField(
                mapper,
                "companyServiceAdapter",
                companyServiceAdapter
        );
        ReflectionTestUtils.setField(
                companyBindingService,
                "companyBindingMapper",
                mapper
        );
    }

    @Test
    void should_ThrowException_when_CompanyBindingNotFound() {
        doReturn(List.of()).when(companyBindingRepository).findByCompanyIdAndExternalSystem(eq(COMPANY_ID), any());
        assertEquals(Optional.empty(), companyBindingService.getBindingFor(COMPANY_ID));
    }

    @Test
    void should_ReturnCompanyBinding_when_CompanyBindingAvailable() {
        JpaCompanyBinding jpaCompanyBinding = new JpaCompanyBinding();
        doReturn(List.of(jpaCompanyBinding)).when(companyBindingRepository).findByCompanyIdAndExternalSystem(eq(COMPANY_ID), any());
        assertThat(companyBindingService.getBindingFor(COMPANY_ID).get(), is(jpaCompanyBinding));
    }

    @Nested
    class GetOrCreateCompanyBindingWithGraphInput {
        @Test
        void shouldGenerateAndReturnCompanyBinding_whenCompanyBindingNotFound() {
            Address address = Address.newBuilder().build();
            LegalEntity primaryEntity = LegalEntity.newBuilder().id(123L).legalName("LegalName")
                    .address(address)
                    .build();
            var company = com.multiplier.core.payable.company.Company.builder()
                    .primaryEntity(primaryEntity)
                    .isTest(false)
                    .id(COMPANY_ID).build();
            String netsuiteCustomerId = "NETSUITE_CONTACT_ID";
            JpaCompanyBinding netsuiteJpaCompanyBinding = JpaCompanyBinding.builder()
                    .id(2L)
                    .companyId(COMPANY_ID)
                    .externalCustomerId(netsuiteCustomerId)
                    .externalSystem(ExternalSystem.NETSUITE)
                    .build();

            when(companyServiceAdapter.getCompanyById(COMPANY_ID)).thenReturn(company);
            when(companyBindingRepository.findByCompanyIdAndExternalSystem(eq(COMPANY_ID), any())).thenReturn(List.of());
            when(customerActionFactory.getEnabledAdapter()).thenReturn(netsuiteCustomerWebserviceActionAdapter);
            when(netsuiteCustomerWebserviceActionAdapter.createCustomer(any())).thenReturn(netsuiteCustomerId);
            when(addressMapper.toAddressDto(address)).thenReturn(AddressDto.builder().build());
            doReturn(netsuiteJpaCompanyBinding).when(companyBindingRepository).save(any());

            var result = companyBindingService.getOrCreateCompanyBinding(
                    CompanyBindingInput.newBuilder().companyId(COMPANY_ID).build());

            assertEquals(
                    CompanyBinding.newBuilder()
                            .id(2L)
                            .company(payableCompanyMapper.map(company))
                            .contactId(netsuiteCustomerId)
                            .customerId(netsuiteCustomerId)
                            .externalSystem(ExternalSystemType.NETSUITE)
                            .build(),
                    result);
            verify(netsuiteCustomerWebserviceActionAdapter, times(1)).createCustomer(any());
            verify(companyBindingRepository, times(1)).save(any());
        }

        @Test
        void shouldNotGenerateAndJustReturnExistingCompanyBinding_whenCompanyBindingFound() {
            var company = com.multiplier.core.payable.company.Company.builder().id(COMPANY_ID).build();
            String customerId = "XERO_CONTACT_ID";
            JpaCompanyBinding jpaCompanyBinding = JpaCompanyBinding.builder()
                    .id(1L)
                    .companyId(COMPANY_ID)
                    .externalCustomerId(customerId)
                    .externalSystem(ExternalSystem.XERO)
                    .build();

            when(companyServiceAdapter.getCompanyById(COMPANY_ID)).thenReturn(company);
            when(companyBindingRepository.findByCompanyIdAndExternalSystem(eq(COMPANY_ID), any())).thenReturn(List.of(jpaCompanyBinding));
            when(companyServiceAdapter.getCompanyById(COMPANY_ID)).thenReturn(company);

            var result = companyBindingService.getOrCreateCompanyBinding(
                    CompanyBindingInput.newBuilder().companyId(COMPANY_ID).build());

            assertEquals(
                    CompanyBinding.newBuilder()
                            .id(1L)
                            .company(payableCompanyMapper.map(company))
                            .contactId(customerId)
                            .customerId(customerId)
                            .externalSystem(ExternalSystemType.XERO)
                            .build(),
                    result);
            verify(companyBindingRepository, never()).save(any());
        }
    }

    @Test
    void should_createNewContact_when_importExternalContacts() {
        final String externalCustomerId = "xero-contact-id";
        final Long companyId = 1L;
        final JpaCompanyBinding jpaCompanyBinding = JpaCompanyBinding.builder().companyId(companyId).externalCustomerId(externalCustomerId).build();
        final com.multiplier.core.payable.company.Company company = com.multiplier.core.payable.company.Company.builder().id(companyId).build();

        when(invoiceGenerationFactory.getAdapter()).thenReturn(invoiceGenerationAdapter);
        doNothing().when(invoiceGenerationAdapter).validateExternalCustomer(externalCustomerId);
        doReturn(List.of()).when(companyBindingRepository).findByCompanyIdAndExternalSystem(eq(companyId), any());
        doReturn(jpaCompanyBinding).when(companyBindingRepository).save(any());
        when(companyServiceAdapter.getCompanyById(companyId)).thenReturn(company);

        var result = companyBindingService.importCompanyBinding(companyId, externalCustomerId);

        assertEquals(externalCustomerId, result.getContactId());
    }

    @Test
    void should_ProcessContactUpdates_when_ProvideIdOfUpdatedCompanies() {
        JpaCompanyBinding companyBinding = JpaCompanyBinding.builder()
                .companyId(2L)
                .externalCustomerId("netsuiteContactId")
                .build();
        com.multiplier.core.payable.company.Company company = createTestCompany();
        Pricing pricing = createTestPricing();
        var result = CustomerUpdateOrCreateResult.builder()
                .failedCompanyIds(List.of())
                .newCompanyBindingMap(Map.of())
                .build();

        when(companyBindingRepository.findByCompanyIdInAndExternalSystem(
                argThat(argument -> argument.containsAll(List.of(1L, 2L))), eq(ExternalSystem.NETSUITE)))
                .thenReturn(List.of(companyBinding));
        when(companyServiceAdapter.getCompanies(Set.of(1L, 2L))).thenReturn(List.of(company));
        when(payableServiceAdapter.getPricing(2L)).thenReturn(pricing);
        when(addressMapper.toAddressDto(any())).thenReturn(AddressDto.builder().build());
        when(customerActionFactory.updateOrCreateCustomers(any())).thenReturn(result);

        ArgumentCaptor<List<CustomerDto>> captor = ArgumentCaptor.forClass(List.class);

        var failedIds = companyBindingService.updateCustomerOnExternalSystems(Set.of(1L, 2L));
        assertFalse(failedIds.isEmpty());
        assertEquals(1, failedIds.size());
        assertEquals(1L, failedIds.get(0));

        verify(customerActionFactory, times(1)).updateOrCreateCustomers(captor.capture());

        List<CustomerDto> customerDtos = captor.getValue();

        assertEquals(1, customerDtos.size());

        assertEquals(companyBinding.getExternalCustomerId(), customerDtos.get(0).getCustomerId());
        assertContactDto(company, pricing, customerDtos.get(0));
    }

    @Nested
    class GetCustomerIdForCompany {

        @Test
        void whenCustomerBindingIsPresent_shouldReturnCustomerId() {
            JpaCompanyBinding companyBinding = JpaCompanyBinding.builder()
                    .companyId(2L)
                    .externalCustomerId("netsuiteContactId")
                    .build();

            when(companyBindingRepository.findByCompanyIdAndExternalSystem(2L, ExternalSystem.NETSUITE))
                    .thenReturn(List.of(companyBinding));

            assertEquals(Optional.of("netsuiteContactId"), companyBindingService.getCustomerIdForCompany(2L));
        }

        @Test
        void whenCustomerIdIsNotPresent_shouldReturnEmptyOptional() {
            when(companyBindingRepository.findByCompanyIdAndExternalSystem(2L, ExternalSystem.NETSUITE))
                    .thenReturn(List.of());

            assertEquals(Optional.empty(), companyBindingService.getCustomerIdForCompany(2L));
        }

    }

    private static void assertContactDto(com.multiplier.core.payable.company.Company company, Pricing pricing, CustomerDto customerDto) {
        assertEquals(company.getPrimaryEntity().getLegalName(), customerDto.getCompanyName());
        assertEquals(company.getId(), customerDto.getCompanyId());
        assertEquals(pricing.getBillingCurrencyCode(), customerDto.getBillingCurrency());
        assertEquals(pricing.getPaymentTermInDays(), customerDto.getBillingDay());
        assertEquals(company.getBillingContact().getFirstName(), customerDto.getPrimaryContactFirstName());
        assertEquals(company.getBillingContact().getLastName(), customerDto.getPrimaryContactLastName());
        assertEquals("<EMAIL>", customerDto.getPrimaryContactEmail());
    }

    private static Pricing createTestPricing() {
        return Pricing.newBuilder()
                .billingCurrencyCode(CurrencyCode.AED)
                .paymentTermInDays(7)
                .build();
    }

    private static com.multiplier.core.payable.company.Company createTestCompany() {
        return com.multiplier.core.payable.company.Company.builder()
                .id(2L)
                .primaryEntity(LegalEntity.newBuilder()
                        .legalName("LegalName")
                        .build())
                .isTest(false)
                .billingContact(com.multiplier.core.payable.company.CompanyUser.builder()
                        .firstName("FirstName")
                        .lastName("LastName")
                        .emails(List.of(
                                EmailAddress.newBuilder()
                                        .email("<EMAIL>")
                                        .type("primary")
                                        .build(),
                                EmailAddress.newBuilder()
                                        .email("<EMAIL>")
                                        .build()
                        ))
                        .build())
                .status(CompanyStatus.ACTIVE)
                .build();
    }

    @Test
    void testUpdateLegalEntityBindingWhenStatusActive() {
        // GIVEN
        LegalEntityEvent testEvent = new LegalEntityEvent(10L, 15L, LegalEntityEventStatus.ACTIVE, false);
        Company company = mock(Company.class);
        when(company.getId()).thenReturn(15L);
        when(this.companyServiceAdapter.getCompanyById(15L)).thenReturn(company);
        when(customerActionFactory.updateOrCreateCustomers(any())).thenReturn(CustomerUpdateOrCreateResult.builder()
                .failedCompanyIds(List.of())
                .newCompanyBindingMap(Map.of(Pair.of(15L, 10L), "NSCustomerID"))
                .build());
        when(companyServiceAdapter.getLegalEntityNames(15L)).thenReturn(Map.of(10L, "10 Name"));
        // WHEN
        this.companyBindingService.updateLegalEntityBinding(List.of(testEvent));

        // THEN
        verify(companyServiceAdapter, times(1)).getCompanyById(15L);
    }
}