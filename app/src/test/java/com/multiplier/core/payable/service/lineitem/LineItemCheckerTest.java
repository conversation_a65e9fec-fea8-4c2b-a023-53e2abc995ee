package com.multiplier.core.payable.service.lineitem;

import com.multiplier.core.payable.adapters.api.LineItemType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LineItemCheckerTest {

    @Mock
    private GrossSalaryLineItemChecker grossSalaryLineItemChecker;

    @Mock
    private List<AbstractLineItemChecker> abstractLineItemCheckers;

    @InjectMocks
    private LineItemChecker lineItemChecker;


    @BeforeEach
    void setup() {
        when(abstractLineItemCheckers.stream()).thenReturn(Stream.of(grossSalaryLineItemChecker));
    }

    @ParameterizedTest
    @CsvSource({"true", "false"})
    void should_check(boolean isValid) {
        //GIVEN
        var processedLineItem = ProcessedLineItem.builder()
                .itemType(LineItemType.GROSS_SALARY)
                .contractId(123L)
                .build();

        when(grossSalaryLineItemChecker.check(processedLineItem)).thenReturn(isValid);

        //WHEN
        var result = lineItemChecker.check(processedLineItem);

        //THEN
        assertEquals(isValid, result);
    }

    @Test
    void should_update_item() {
        //GIVEN
        var processedLineItem = ProcessedLineItem.builder()
                .amountInBaseCurrency(1000.0)
                .amountInBillingCurrency(2000.0)
                .itemType(LineItemType.GROSS_SALARY)
                .contractId(123L)
                .build();

        when(grossSalaryLineItemChecker.check(processedLineItem)).thenReturn(true);
        when(grossSalaryLineItemChecker.generateBilledLineItemDescription(any())).thenReturn("generated");
        when(grossSalaryLineItemChecker.getLineItemCategory()).thenReturn(LineItemCategory.GROSS);

        //WHEN
        var result = lineItemChecker.update(processedLineItem);

        //THEN
        assertEquals("generated", result.getDescription());
        assertEquals(LineItemCategory.GROSS, result.getItemCategory());
        assertEquals(-1000.0, result.getAmountInBaseCurrency());
        assertEquals(-2000.0, result.getAmountInBillingCurrency());
    }

    @Test
    void should_not_generate_description() {
        //GIVEN
        var processedLineItem = ProcessedLineItem.builder()
                .amountInBaseCurrency(1000.0)
                .amountInBillingCurrency(2000.0)
                .itemType(LineItemType.GROSS_SALARY)
                .contractId(123L)
                .build();

        when(grossSalaryLineItemChecker.check(processedLineItem)).thenReturn(false);

        //WHEN
        var result = lineItemChecker.update(processedLineItem);

        //THEN
        assertEquals(processedLineItem, result);
        assertEquals(1000.0, result.getAmountInBaseCurrency());
        assertEquals(2000.0, result.getAmountInBillingCurrency());
    }
}
