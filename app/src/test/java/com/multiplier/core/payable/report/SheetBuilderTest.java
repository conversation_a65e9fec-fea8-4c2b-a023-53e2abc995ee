package com.multiplier.core.payable.report;

import com.multiplier.core.payable.report.generator.domain.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class SheetBuilderTest {

    private SheetBuilder sheetBuilder;

    @BeforeEach
    void setUp() {
        sheetBuilder = new SheetBuilder();
    }

    @Test
    void testBuild_SingleSheetWithOneRow() {
        // Given
        ReportRowCellValue cellValue1 = ReportRowCellValue.builder()
                .columnName("Column1")
                .columnValue("Value1")
                .reportRowCellType(ReportRowCellType.STRING)
                .build();

        ReportRowCellValue cellValue2 = ReportRowCellValue.builder()
                .columnName("Column2")
                .columnValue(123)
                .reportRowCellType(ReportRowCellType.INTEGER)
                .build();

        ReportRowCellValue cellValue3 = ReportRowCellValue.builder()
                .columnName("Column3")
                .columnValue(true)
                .reportRowCellType(ReportRowCellType.BOOLEAN)
                .build();

        var now = LocalDate.now();
        ReportRowCellValue cellValue4 = ReportRowCellValue.builder()
                .columnName("Column4")
                .columnValue(now)
                .reportRowCellType(ReportRowCellType.DATETIME)
                .build();

        ReportRow reportRow = ReportRow.builder()
                .reportRowCellValues(Arrays.asList(cellValue1, cellValue2, cellValue3, cellValue4))
                .build();

        ReportSheet reportSheet = ReportSheet.builder()
                .sheetName("TestSheet")
                .reportRows(Collections.singletonList(reportRow))
                .rowsToFreeze(1)
                .colsToFreeze(1)
                .build();

        List<ReportSheet> sheets = Collections.singletonList(reportSheet);

        // When
        Workbook workbook = sheetBuilder.build(sheets);

        // Then
        assertNotNull(workbook);
        assertEquals(1, workbook.getNumberOfSheets());

        Sheet sheet = workbook.getSheet("TestSheet");
        assertNotNull(sheet);

        Row headerRow = sheet.getRow(0);
        assertNotNull(headerRow);
        assertEquals("Column1", headerRow.getCell(0).getStringCellValue());
        assertEquals("Column2", headerRow.getCell(1).getStringCellValue());
        assertEquals("Column3", headerRow.getCell(2).getStringCellValue());
        assertEquals("Column4", headerRow.getCell(3).getStringCellValue());

        Row dataRow = sheet.getRow(1);
        assertNotNull(dataRow);
        assertEquals("Value1", dataRow.getCell(0).getStringCellValue());
        assertEquals(123, (int) dataRow.getCell(1).getNumericCellValue());
        assertTrue(dataRow.getCell(2).getBooleanCellValue());
        assertEquals(now.format(DateTimeFormatter.ofPattern("dd-MM-yyyy")),  dataRow.getCell(3).getStringCellValue());
    }

    @Test
    void testBuild_EmptySheet() {
        // Given
        ReportSheet reportSheet = ReportSheet.builder()
                .sheetName("EmptySheet")
                .reportRows(Collections.emptyList())
                .build();

        List<ReportSheet> sheets = Collections.singletonList(reportSheet);

        // When
        Workbook workbook = sheetBuilder.build(sheets);

        // Then
        assertNotNull(workbook);
        assertEquals(1, workbook.getNumberOfSheets());

        Sheet sheet = workbook.getSheet("EmptySheet");
        assertNotNull(sheet);
        assertEquals(0, sheet.getPhysicalNumberOfRows());
    }

    @Test
    void testBuild_MultipleSheets() {
        // Given
        ReportRowCellValue cellValue1 = ReportRowCellValue.builder()
                .columnName("Column1")
                .columnValue("Value1")
                .reportRowCellType(ReportRowCellType.STRING)
                .build();

        ReportRow reportRow1 = ReportRow.builder()
                .reportRowCellValues(Collections.singletonList(cellValue1))
                .build();

        ReportSheet reportSheet1 = ReportSheet.builder()
                .sheetName("Sheet1")
                .reportRows(Collections.singletonList(reportRow1))
                .build();

        ReportRowCellValue cellValue2 = ReportRowCellValue.builder()
                .columnName("ColumnA")
                .columnValue("ValueA")
                .reportRowCellType(ReportRowCellType.STRING)
                .build();

        ReportRow reportRow2 = ReportRow.builder()
                .reportRowCellValues(Collections.singletonList(cellValue2))
                .build();

        ReportSheet reportSheet2 = ReportSheet.builder()
                .sheetName("Sheet2")
                .reportRows(Collections.singletonList(reportRow2))
                .build();

        List<ReportSheet> sheets = Arrays.asList(reportSheet1, reportSheet2);

        // When
        Workbook workbook = sheetBuilder.build(sheets);

        // Then
        assertNotNull(workbook);
        assertEquals(2, workbook.getNumberOfSheets());

        Sheet sheet1 = workbook.getSheet("Sheet1");
        assertNotNull(sheet1);

        Sheet sheet2 = workbook.getSheet("Sheet2");
        assertNotNull(sheet2);
    }

    @Test
    void testBuildColumnsPerfomance() {
            // Given
        int totalRows = 1000;
        var rows = new ArrayList<ReportRow>();
        for (int rowNo=0; rowNo<totalRows; rowNo++) {
            ReportRow reportRow = buildReportRow(rowNo);
            rows.add(reportRow);
        }
        ReportSheet reportSheet = ReportSheet.builder()
                .sheetName("Sheet")
                .reportRows(rows)
                .build();
        List<ReportSheet> sheets = Collections.singletonList(reportSheet);

        // When
        long startTimeWithImprovement = System.currentTimeMillis();
        Workbook workbookWithImprovement = sheetBuilder.build(sheets);
        long endTimeWithImprovement = System.currentTimeMillis();
        long improvedDuration = endTimeWithImprovement - startTimeWithImprovement;

        // Then
        assertNotNull(workbookWithImprovement);
        log.info("Execution time existing method is {}ms", improvedDuration);

        assertTrue(improvedDuration < 60000, "Execution time should be less than 1 minute with actual execution time " + improvedDuration + "ms");
    }

    private ReportRow buildReportRow(long contractId) {
        List<ReportRowCellValue> cellValues = Arrays.asList(
                ReportRowCellValue.builder()
                        .columnName("Company ID")
                        .columnValue(900158)
                        .columnDataFormat("")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.STRING)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Company Legal Name")
                        .columnValue("Joseph Oneil Traders")
                        .columnDataFormat("")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.STRING)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Contract ID")
                        .columnValue(contractId)
                        .columnDataFormat("")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.STRING)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Member Name")
                        .columnValue("Abra Bentley")
                        .columnDataFormat("")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.STRING)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Country")
                        .columnValue("IND")
                        .columnDataFormat("")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.STRING)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Contract Start Date")
                        .columnValue("2024-07-11")
                        .columnDataFormat("")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.STRING)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Contract End Date")
                        .columnValue("2024-07-30")
                        .columnDataFormat("")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.STRING)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Gross Salary")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Bonus")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Commission")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Allowances")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Employer Contribution")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Expenses")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Client Deductions")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Total Payroll Cost (Local currency)")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Transaction Currency")
                        .columnValue("")
                        .columnDataFormat("")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.STRING)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Fx")
                        .columnValue(1.0)
                        .columnDataFormat("0.0000")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Payroll Cost - GST amount")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Total Payroll Cost (Billing Currency)")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Billing Currency")
                        .columnValue("USD")
                        .columnDataFormat("")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.STRING)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Amount Billed in 1st Invoice")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Management Fee(Base Currency)")
                        .columnValue(-800.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Management Fee Fx rate")
                        .columnValue(1.0)
                        .columnDataFormat("0.0000")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Management Fees(Billing Currency)")
                        .columnValue(-800.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("GST Amount")
                        .columnValue(800.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Total Management Fees")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("M fee billed in 1st invoice (Billing currency)")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Processing Fee (Billing currency)")
                        .columnValue("")
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Annual Plan Fee (including GST)")
                        .columnValue("")
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("VAT Billing Currency")
                        .columnValue("")
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("VAT - GST Amount")
                        .columnValue("")
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Total VAT Amount")
                        .columnValue("")
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Balance Amount To Billed")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build(),
                ReportRowCellValue.builder()
                        .columnName("Total Cost (Billing Currency)")
                        .columnValue(0.0)
                        .columnDataFormat("0.00")
                        .columnAlias(null)
                        .reportRowCellType(ReportRowCellType.DOUBLE)
                        .reportRowCellBorderType(ReportRowCellBorderType.LEFT_RIGHT)
                        .xlBackgroundColor(null)
                        .build()
        );
        return ReportRow.builder()
                .reportRowCellValues(cellValues)
                .build();
    }

}
