package com.multiplier.core.payable.service;

import com.multiplier.payable.types.CreditNoteStatus;
import com.multiplier.payable.types.CurrencyCode;
import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.creditnote.database.CreditNoteDto;
import com.multiplier.core.payable.creditnote.database.CreditNoteItemDto;
import com.multiplier.core.payable.repository.model.ExternalSystem;
import com.multiplier.core.payable.repository.model.JpaInvoicingError;
import com.multiplier.core.payable.service.dataholder.ContractInvoiceOrCreditNoteData;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;

import javax.annotation.Nullable;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

class CreditNoteExtractorForSourceReportTest {

    private final Long companyId = 1001L;
    private final Long contractId = 2001L;

    @Test
    void able_to_extract_data_from_credit_note() {
        // Arrange
        CreditNoteDto creditNoteDto = null;
        try {
            creditNoteDto = createCreditNoteDto(companyId, contractId, ExternalSystem.NETSUITE);
        } catch (Exception e) {
            fail();
        }

        Collection<CreditNoteDto> creditNoteDtos = Collections.singletonList(creditNoteDto);
        Collection<LineItemType> lineItemTypes = Set.of(LineItemType.GROSS_SALARY, LineItemType.EOR_SALARY_DISBURSEMENT, LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL);
        List<JpaInvoicingError> invoicingErrors = new ArrayList<>();
        Map<Long, ContractInvoiceOrCreditNoteData> contractInvoiceCreditNoteData = new HashMap<>();

        // Act
        Map<Long, ContractInvoiceOrCreditNoteData> result = CreditNoteExtractorForSourceReport.extractCreditNotesDataForSourceReport(
                creditNoteDtos, lineItemTypes, invoicingErrors, contractInvoiceCreditNoteData);

        // Assert
        assertNotNull(result);
        assertTrue(result.containsKey(contractId));
        ContractInvoiceOrCreditNoteData contractData = result.get(contractId);
        assertNotNull(contractData);
        assertEquals(100.0, contractData.getGrossUnitPrice());
        assertEquals(Pair.of(CurrencyCode.USD, 100.0), contractData.getGrossSalary());
        assertEquals(109.0, contractData.getPayrollGrossAmount());
        assertEquals(100.0, contractData.getPayrollUnitPrice());
        assertEquals(Pair.of(CurrencyCode.USD, 90.0), contractData.getPayroll());
        assertEquals(109, contractData.getSeveranceInBillingCurrency());
        assertEquals(90,contractData.getSeveranceInBaseCurrency());
        Double taxRate = contractData.getTaxRates().get(LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL).stream().max(Double::compareTo).orElse(0.0);
        assertEquals(9.0, taxRate);
    }

    @Test
    void return_empty_list_if_contract_id_is_invalid() {
        // Arrange
        CreditNoteDto creditNoteDto = null;
        try {
            creditNoteDto = createCreditNoteDto(companyId, null, ExternalSystem.NETSUITE);
        } catch (Exception e) {
            fail();
        }

        Collection<CreditNoteDto> creditNoteDtos = Collections.singletonList(creditNoteDto);
        Collection<LineItemType> lineItemTypes = Set.of(LineItemType.GROSS_SALARY);
        List<JpaInvoicingError> invoicingErrors = new ArrayList<>();
        Map<Long, ContractInvoiceOrCreditNoteData> contractInvoiceCreditNoteData = new HashMap<>();

        // Act
        Map<Long, ContractInvoiceOrCreditNoteData> result = CreditNoteExtractorForSourceReport.extractCreditNotesDataForSourceReport(
                creditNoteDtos, lineItemTypes, invoicingErrors, contractInvoiceCreditNoteData);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        assertFalse(invoicingErrors.isEmpty());
    }

    @Test
    void return_empty_list_if_contract_id_is_null() {
        // Arrange
        CreditNoteDto creditNoteDto = null;
        try {
            creditNoteDto = createCreditNoteDto(companyId, null, ExternalSystem.NETSUITE);
        } catch (Exception e) {
            fail();
        }

        Collection<CreditNoteDto> creditNoteDtos = Collections.singletonList(creditNoteDto);
        Collection<LineItemType> lineItemTypes = Set.of(LineItemType.GROSS_SALARY);
        List<JpaInvoicingError> invoicingErrors = new ArrayList<>();
        Map<Long, ContractInvoiceOrCreditNoteData> contractInvoiceCreditNoteData = new HashMap<>();

        // Act
        Map<Long, ContractInvoiceOrCreditNoteData> result = CreditNoteExtractorForSourceReport.extractCreditNotesDataForSourceReport(
                creditNoteDtos, lineItemTypes, invoicingErrors, contractInvoiceCreditNoteData);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        assertFalse(invoicingErrors.isEmpty());
    }

    @Test
    void return_empty_if_external_system_is_not_netsuite() {
        // Arrange
        CreditNoteDto creditNoteDto = null;
        try {
            creditNoteDto = createCreditNoteDto(companyId, contractId, ExternalSystem.XERO);
        } catch (Exception e) {
            fail();
        }

        Collection<CreditNoteDto> creditNoteDtos = Collections.singletonList(creditNoteDto);
        Collection<LineItemType> lineItemTypes = Set.of(LineItemType.GROSS_SALARY);
        List<JpaInvoicingError> invoicingErrors = new ArrayList<>();
        Map<Long, ContractInvoiceOrCreditNoteData> contractInvoiceCreditNoteData = new HashMap<>();

        // Act
        Map<Long, ContractInvoiceOrCreditNoteData> result = CreditNoteExtractorForSourceReport.extractCreditNotesDataForSourceReport(
                creditNoteDtos, lineItemTypes, invoicingErrors, contractInvoiceCreditNoteData);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        // Additional assertions can be made here
    }

    private CreditNoteDto createCreditNoteDto(@Nullable Long companyId, @Nullable Long inputContractId, ExternalSystem externalSystem) {
        var grossSalaryItem = CreditNoteItemDto.builder()
                .itemType(LineItemType.GROSS_SALARY)
                .description("Gross Salary")
                .grossAmount(100.0)
                .unitAmount(100.0)
                .taxCode("GST_SG:OS-SG")
                .contractId(inputContractId)
                .memberName("MEMBER NAME")
                .baseCurrency(CurrencyCode.USD)
                .amountInBaseCurrency(100.0)
                .grossAmount(100.0)
                .taxRate(0d)
                .taxAmount(0.0)
                .build();
        var payrollItem = CreditNoteItemDto.builder()
                .itemType(LineItemType.EOR_SALARY_DISBURSEMENT)
                .description("Payroll cost")
                .grossAmount(100.0)
                .unitAmount(100.0)
                .taxCode("GST_SG:SR-SG")
                .contractId(inputContractId)
                .memberName("MEMBER NAME")
                .baseCurrency(CurrencyCode.USD)
                .amountInBaseCurrency(90.0)
                .grossAmount(109.0)
                .taxRate(9.0)
                .taxAmount(9.0)
                .build();
        var severanceItem = CreditNoteItemDto.builder()
                .itemType(LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL)
                .description("Severance accrual")
                .unitAmount(100.0)
                .taxCode("GST_SG:OS-SG")
                .contractId(inputContractId)
                .memberName("MEMBER NAME")
                .baseCurrency(CurrencyCode.USD)
                .amountInBaseCurrency(90.0)
                .grossAmount(109.0)
                .taxRate(9.0)
                .taxAmount(9.0)
                .build();

        return CreditNoteDto.builder()
                .externalId("externalId")
                .status(CreditNoteStatus.DRAFT)
                .currencyCode(CurrencyCode.USD)
                .amountTotal(100.0)
                .amountApplied(100.0)
                .amountUnapplied(0.0)
                .reference("reference")
                .companyId(companyId)
                .reason(null)
                .externalSystem(externalSystem)
                .externalInvoiceIds(List.of("externalInvoiceId"))
                .items(List.of(grossSalaryItem,payrollItem, severanceItem))
                .creditNoteNo("CN123")
                .month(6)
                .year(2023).build();
    }

    @Test
    void should_handle_refund_amounts_correctly() {
        // Arrange
        CreditNoteDto creditNoteDto = null;
        try {
            creditNoteDto = createCreditNoteWithRefundItem(companyId, contractId, ExternalSystem.NETSUITE);
        } catch (Exception e) {
            fail();
        }

        Collection<CreditNoteDto> creditNoteDtos = Collections.singletonList(creditNoteDto);
        Collection<LineItemType> lineItemTypes = Set.of(LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL);
        List<JpaInvoicingError> invoicingErrors = new ArrayList<>();
        Map<Long, ContractInvoiceOrCreditNoteData> contractInvoiceCreditNoteData = new HashMap<>();

        // Act
        Map<Long, ContractInvoiceOrCreditNoteData> result = CreditNoteExtractorForSourceReport.extractCreditNotesDataForSourceReport(
                creditNoteDtos, lineItemTypes, invoicingErrors, contractInvoiceCreditNoteData);

        // Assert
        assertNotNull(result);
        assertTrue(result.containsKey(contractId));
        ContractInvoiceOrCreditNoteData contractData = result.get(contractId);
        assertNotNull(contractData);
        // Verify negative values for refund
        assertEquals(-100.0, contractData.getSeveranceInBaseCurrency());
        assertEquals(-109.0, contractData.getSeveranceInBillingCurrency());
    }

    private CreditNoteDto createCreditNoteWithRefundItem(@Nullable Long companyId, @Nullable Long inputContractId, ExternalSystem externalSystem) {
        var severanceRefundItem = CreditNoteItemDto.builder()
                .itemType(LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL)
                .description("Severance accrual adjustment")
                .unitAmount(100.0)
                .taxCode("GST_SG:OS-SG")
                .contractId(inputContractId)
                .memberName("MEMBER NAME")
                .baseCurrency(CurrencyCode.USD)
                .amountInBaseCurrency(100.0)
                .grossAmount(109.0)
                .taxRate(9.0)
                .taxAmount(9.0)
                .build();

        return CreditNoteDto.builder()
                .externalId("externalId")
                .status(CreditNoteStatus.DRAFT)
                .currencyCode(CurrencyCode.USD)
                .amountTotal(109.0)
                .amountApplied(109.0)
                .amountUnapplied(0.0)
                .reference("reference")
                .companyId(companyId)
                .reason(null)
                .externalSystem(externalSystem)
                .externalInvoiceIds(List.of("externalInvoiceId"))
                .items(List.of(severanceRefundItem))
                .creditNoteNo("CN123")
                .month(6)
                .year(2023).build();
    }

    @Test
    void should_handle_regular_and_refund_amounts_in_same_credit_note() {
        // Arrange
        CreditNoteDto creditNoteDto = null;
        try {
            creditNoteDto = createMixedCreditNote(companyId, contractId, ExternalSystem.NETSUITE);
        } catch (Exception e) {
            fail();
        }

        Collection<CreditNoteDto> creditNoteDtos = Collections.singletonList(creditNoteDto);
        Collection<LineItemType> lineItemTypes = Set.of(
                LineItemType.GROSS_SALARY, 
                LineItemType.EOR_SALARY_DISBURSEMENT, 
                LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL
        );
        List<JpaInvoicingError> invoicingErrors = new ArrayList<>();
        Map<Long, ContractInvoiceOrCreditNoteData> contractInvoiceCreditNoteData = new HashMap<>();

        // Act
        Map<Long, ContractInvoiceOrCreditNoteData> result = CreditNoteExtractorForSourceReport.extractCreditNotesDataForSourceReport(
                creditNoteDtos, lineItemTypes, invoicingErrors, contractInvoiceCreditNoteData);

        // Assert
        assertNotNull(result);
        assertTrue(result.containsKey(contractId));
        ContractInvoiceOrCreditNoteData contractData = result.get(contractId);
        assertNotNull(contractData);
        
        // Regular items should be positive
        assertEquals(100.0, contractData.getGrossSalary().getRight());
        assertEquals(90.0, contractData.getPayroll().getRight());
        
        // Refund item should be negative
        assertEquals(-100.0, contractData.getSeveranceInBaseCurrency());
    }

    private CreditNoteDto createMixedCreditNote(@Nullable Long companyId, @Nullable Long inputContractId, ExternalSystem externalSystem) {
        var grossSalaryItem = CreditNoteItemDto.builder()
                .itemType(LineItemType.GROSS_SALARY)
                .description("Gross Salary")
                .grossAmount(100.0)
                .unitAmount(100.0)
                .taxCode("GST_SG:OS-SG")
                .contractId(inputContractId)
                .memberName("MEMBER NAME")
                .baseCurrency(CurrencyCode.USD)
                .amountInBaseCurrency(100.0)
                .grossAmount(100.0)
                .taxRate(0d)
                .taxAmount(0.0)
                .build();
        var payrollItem = CreditNoteItemDto.builder()
                .itemType(LineItemType.EOR_SALARY_DISBURSEMENT)
                .description("Payroll cost")
                .grossAmount(100.0)
                .unitAmount(100.0)
                .taxCode("GST_SG:SR-SG")
                .contractId(inputContractId)
                .memberName("MEMBER NAME")
                .baseCurrency(CurrencyCode.USD)
                .amountInBaseCurrency(90.0)
                .grossAmount(109.0)
                .taxRate(9.0)
                .taxAmount(9.0)
                .build();
        var severanceRefundItem = CreditNoteItemDto.builder()
                .itemType(LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL)
                .description("Severance accrual adjustment")
                .unitAmount(100.0)
                .taxCode("GST_SG:OS-SG")
                .contractId(inputContractId)
                .memberName("MEMBER NAME")
                .baseCurrency(CurrencyCode.USD)
                .amountInBaseCurrency(100.0)
                .grossAmount(109.0)
                .taxRate(9.0)
                .taxAmount(9.0)
                .build();

        return CreditNoteDto.builder()
                .externalId("externalId")
                .status(CreditNoteStatus.DRAFT)
                .currencyCode(CurrencyCode.USD)
                .amountTotal(318.0)
                .amountApplied(318.0)
                .amountUnapplied(0.0)
                .reference("reference")
                .companyId(companyId)
                .reason(null)
                .externalSystem(externalSystem)
                .externalInvoiceIds(List.of("externalInvoiceId"))
                .items(List.of(grossSalaryItem, payrollItem, severanceRefundItem))
                .creditNoteNo("CN123")
                .month(6)
                .year(2023).build();
    }
}