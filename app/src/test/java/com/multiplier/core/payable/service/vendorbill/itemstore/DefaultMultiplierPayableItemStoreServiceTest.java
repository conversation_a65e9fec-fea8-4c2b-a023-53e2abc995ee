package com.multiplier.core.payable.service.vendorbill.itemstore;

import com.multiplier.core.payable.repository.JpaMultiplierPayableItemStoreRepository;
import com.multiplier.core.payable.repository.model.JpaMultiplierPayableItemStore;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DefaultMultiplierPayableItemStoreServiceTest {

    @Mock
    private MultiplierPayableItemStoreMapper mapper;

    @Mock
    private JpaMultiplierPayableItemStoreRepository repository;

    @InjectMocks
    private DefaultMultiplierPayableItemStoreService service;

    private MultiplierPayableItemStoreDto dto;
    private JpaMultiplierPayableItemStore entity;

    @BeforeEach
    void setUp() {
        MultiplierPayableItemStoreMetadata metadata = MultiplierPayableItemStoreMetadata.builder()
                .employeeName("John Doe")
                .payroll(
                        PayrollMetaData.builder()
                                .cycleId(123L)
                                .frequency("MONTHLY")
                                .startDate(ParsedDate.builder().year(2024).month(9).day(1).build())
                                .endDate(ParsedDate.builder().year(2024).month(9).day(30).build())
                                .payDate(ParsedDate.builder().year(2024).month(9).day(30).build())
                                .build()
                )
                .build();

        dto = MultiplierPayableItemStoreDto.builder()
                .transactionId("txn-123")
                .amount(123.45)
                .metadata(metadata)
                .build();

        entity = new JpaMultiplierPayableItemStore();
        entity.setTransactionId("txn-123");
    }

    @Test
    void testSaveAll_shouldMapAndPersistEntities() {
        // Given
        List<MultiplierPayableItemStoreDto> dtoList = List.of(dto);
        List<JpaMultiplierPayableItemStore> entityList = List.of(entity);

        when(mapper.toEntity(dtoList)).thenReturn(entityList);
        when(repository.saveAll(entityList)).thenReturn(entityList);
        when(mapper.toDto(entityList)).thenReturn(dtoList);

        // When
        List<MultiplierPayableItemStoreDto> result = service.saveAll(dtoList);

        // Then
        assertThat(result).isNotNull().hasSize(1);
        assertThat(result.get(0).getTransactionId()).isEqualTo("txn-123");

        verify(mapper).toEntity(dtoList);
        verify(repository).saveAll(entityList);
        verify(mapper).toDto(entityList);
    }

    @Test
    void testGetMultiplierPayableItemsByTransactionId_shouldReturnMappedDtos() {
        // Given
        List<JpaMultiplierPayableItemStore> entities = List.of(entity);
        List<MultiplierPayableItemStoreDto> dtos = List.of(dto);

        when(repository.getJpaMultiplierPayableItemStoresByTransactionId("txn-123")).thenReturn(entities);
        when(mapper.toDto(entities)).thenReturn(dtos);

        // When
        List<MultiplierPayableItemStoreDto> result = service.getMultiplierPayableItemsByTransactionId("txn-123");

        // Then
        assertThat(result).isNotNull().hasSize(1);
        assertThat(result.get(0).getTransactionId()).isEqualTo("txn-123");

        verify(repository).getJpaMultiplierPayableItemStoresByTransactionId("txn-123");
        verify(mapper).toDto(entities);
    }

    @Test
    void testGetMultiplierPayableItemsByTransactionId_shouldHandleEmptyList() {
        when(repository.getJpaMultiplierPayableItemStoresByTransactionId("empty")).thenReturn(Collections.emptyList());
        when(mapper.toDto(Collections.emptyList())).thenReturn(Collections.emptyList());

        List<MultiplierPayableItemStoreDto> result = service.getMultiplierPayableItemsByTransactionId("empty");

        assertThat(result).isEmpty();

        verify(repository).getJpaMultiplierPayableItemStoresByTransactionId("empty");
        verify(mapper).toDto(Collections.emptyList());
    }
}
