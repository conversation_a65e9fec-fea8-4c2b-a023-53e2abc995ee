package com.multiplier.core.payable.sync.processor.invoice;

import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.repository.model.InvoiceReason;
import com.multiplier.core.payable.repository.model.InvoiceType;
import com.multiplier.core.payable.repository.model.JpaCompanyPayable;
import com.multiplier.payable.types.CompanyPayableType;
import com.multiplier.payable.types.CurrencyCode;
import com.multiplier.payable.types.InvoiceStatus;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.Assert.assertEquals;

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class JpaCompanyPayableFromInvoiceDtoMapperTest {

    private static final Long COMPANY_ID = 12345L;
    private JpaCompanyPayableFromInvoiceDtoMapper mapper = Mappers.getMapper(JpaCompanyPayableFromInvoiceDtoMapper.class);

    @Test
    void should_map_Invoice_to_JpaCompanyPayable() {
        InvoiceDTO invoiceDTO = InvoiceDTO.builder()
                .status(InvoiceStatus.PENDING)
                .date(LocalDate.now())
                .totalAmount(255_000.0)
                .billingCurrencyCode(CurrencyCode.USD)
                .build();

        JpaCompanyPayable payable = mapper.map(invoiceDTO, COMPANY_ID);

        assertThat(payable.getCompanyId(), is(COMPANY_ID));
        assertThat(payable.getDate(), is(invoiceDTO.getDate().atStartOfDay()));
        assertThat(payable.getStatus().toString(), is(invoiceDTO.getStatus().toString()));
        assertThat(payable.getTotalAmount(), is(invoiceDTO.getTotalAmount()));
        assertThat(payable.getCurrency(), is(invoiceDTO.getBillingCurrencyCode()));
        assertThat(payable.getMonth(), is(invoiceDTO.getDate().getMonthValue()));
        assertThat(payable.getYear(), is(invoiceDTO.getDate().getYear()));
        assertThat(payable.getId(), is(nullValue()));
    }

    @ParameterizedTest
    @CsvSource(value = {
            "FIRST_INVOICE, FIRST_INVOICE",
            "SECOND_INVOICE, SECOND_INVOICE",
            "DEPOSITS, DEPOSIT",
            "INSURANCE, INSURANCE",
            "CORRECTION_OF_INVOICE, UNKNOWN"
    })
    void given_with_null_type_when_map_then_type_will_depend_on_reason(InvoiceReason reason, CompanyPayableType type) {
        //GIVEN
        var invoiceDto = InvoiceDTO.builder()
                .reason(reason)
                .build();

        //WHEN
        var jpaCompanyPayable = mapper.map(invoiceDto, COMPANY_ID);


        //THEN
        assertEquals(type, jpaCompanyPayable.getType());
    }

    @ParameterizedTest
    @CsvSource(value = {
            "GROSS, FIRST_INVOICE",
            "SALARY, SECOND_INVOICE",
            "DEPOSIT, DEPOSIT",
            "FREELANCER, FREELANCER",
            "INSURANCE, INSURANCE",
            "ANNUAL_PLAN, UNKNOWN",
    })
    void given_with_non_null_type_when_map_then_return_its_type(InvoiceType invoiceType, CompanyPayableType companyPayableType) {
        //GIVEN
        var invoiceDto = InvoiceDTO.builder()
                .type(invoiceType)
                .build();

        //WHEN
        var jpaCompanyPayable = mapper.map(invoiceDto, COMPANY_ID);


        //THEN
        assertEquals(companyPayableType, jpaCompanyPayable.getType());
    }

    @Test
    void given_with_null_type_and_reason_not_mapped_when_map_then_return_unknown_type() {
        var invoiceDto = InvoiceDTO.builder()
                .reason(InvoiceReason.CORRECTION_OF_INVOICE)
                .build();

        //WHEN
        var jpaCompanyPayable = mapper.map(invoiceDto, COMPANY_ID);


        //THEN
        assertEquals(CompanyPayableType.UNKNOWN, jpaCompanyPayable.getType());
    }
}
