package com.multiplier.core.payable.service.lineitem;

import com.multiplier.contract.schema.compensation.CompensationOuterClass;
import com.multiplier.payable.types.PayFrequency;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mapstruct.factory.Mappers;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class PayFrequencyMapperTest {

    private final PayFrequencyMapper mapper = Mappers.getMapper(PayFrequencyMapper.class);

    @ParameterizedTest
    @CsvSource(value = {
            "PAY_FREQUENCY_WEEKLY, WEEKLY",
            "PAY_FREQUENCY_SEMIMONTHLY, SEMIMONTHLY",
            "PAY_FREQUENCY_BIWEEKLY, BIWEEKLY",
            "PAY_FREQUENCY_MONTHLY, MONTHLY",
            "PAY_FREQUENCY_NULL, null",
            "UNRECOGNIZED, null"
    },
            nullValues = "null")
    void test_mapping(CompensationOuterClass.PayFrequency payFrequency, PayFrequency expectedPayFrequency) {
        //given-when
        var result = mapper.map(payFrequency);

        //then
        assertEquals(expectedPayFrequency, result);
    }
}
