package com.multiplier.core.payable.sync.eventdataprocessor;

import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.creditnote.database.CreditNoteDto;
import com.multiplier.core.payable.event.database.EventDto;
import com.multiplier.core.payable.event.database.EventType;
import com.multiplier.core.payable.event.database.RecordType;
import com.multiplier.core.payable.invoice.database.InvoiceService;
import com.multiplier.core.payable.invoicecreditnote.InvoiceCreditNoteService;
import com.multiplier.core.payable.repository.model.ExternalSystem;
import com.multiplier.core.payable.sync.processor.creditnote.CreditNoteProcessor;
import com.multiplier.core.payable.sync.processor.invoice.InvoiceProcessor;
import com.multiplier.core.payable.validator.EventDataOrderValidatorService;
import com.multiplier.payable.netsuite.transaction.appevent.publisher.invoice.InvoiceAppEventPublisher;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InvoiceEventDataProcessorTest {

    @Mock
    private InvoiceProcessor invoiceProcessor;

    @Mock
    private CreditNoteProcessor creditNoteProcessor;

    @Mock
    private InvoiceService invoiceService;

    @Mock
    private InvoiceCreditNoteService invoiceCreditNoteService;

    @Mock
    private InvoiceAppEventPublisher invoiceAppEventPublisher;

    @InjectMocks
    private InvoiceEventDataProcessor invoiceEventDataProcessor;

    @Mock
    private EventDataOrderValidatorService eventDataOrderValidatorService;

    @Test
    void whenGetType_thenValueIsCorrect() {
        // WHEN
        var type = invoiceEventDataProcessor.getType();

        // THEN
        assertEquals(RecordType.INVOICE, type);
    }

    @ParameterizedTest
    @CsvSource({"true,true,true", "true,false,true", "false,true,true", "false,false,false"})
    void givenEventWithCriteria_thenIsEventDataPresent_thenResultIsCorrect(boolean invoiceFound,
        boolean hasProposedProcessingTime,
        boolean expectedResult) {
        // GIVEN
        var id = 42L;
        var externalInvoiceId = "awesomeInvoiceId";
        var eventDto = EventDto.builder()
            .id(id)
            .externalId(externalInvoiceId)
            .recordType(RecordType.INVOICE)
            .eventType(EventType.CREATE)
            .invoice(InvoiceDTO.builder().build())
            .eventTime(LocalDateTime.now())
            .proposedProcessingTime(hasProposedProcessingTime ? LocalDateTime.now() : null)
            .build();

        var invoiceDto = InvoiceDTO.builder()
            .build();
        when(invoiceService.find(eventDto.getExternalId()))
            .thenReturn(invoiceFound ? Optional.of(invoiceDto) : Optional.empty());
        when(eventDataOrderValidatorService.validateEventOrder(eventDto, ExternalSystem.NETSUITE, hasProposedProcessingTime)).thenReturn(true);
        // WHEN
        var result = invoiceEventDataProcessor.isEventDataPresent(eventDto, ExternalSystem.NETSUITE);

        // THEN
        assertEquals(expectedResult, result);
    }

    @Test
    void givenEvent_whenInvoiceProcessorReturnsNull_thenCreditNoteProcessorHasNoInteractions() {
        // GIVEN
        var id = 42L;
        var externalInvoiceId = "awesomeInvoiceId";
        var externalInvoice = InvoiceDTO.builder()
            .build();
        var eventDto = EventDto.builder()
            .id(id)
            .externalId(externalInvoiceId)
            .recordType(RecordType.INVOICE)
            .eventType(EventType.CREATE)
            .eventTime(LocalDateTime.now())
            .invoice(externalInvoice)
            .relatedCreditNotes(List.of(CreditNoteDto.builder()
                .build()))
            .build();

        when(invoiceProcessor.process(externalInvoice, ExternalSystem.NETSUITE))
            .thenReturn(null);

        // WHEN
        invoiceEventDataProcessor.process(eventDto, ExternalSystem.NETSUITE);

        // THEN
        verifyNoInteractions(creditNoteProcessor);
        verifyNoInteractions(invoiceCreditNoteService);
        verifyNoInteractions(invoiceAppEventPublisher);
    }

    @Test
    void givenEvent_whenInvoiceProcessorThrowsException_thenThrowException() {
        // GIVEN
        var id = 42L;
        var externalInvoiceId = "awesomeInvoiceId";
        var externalInvoice = InvoiceDTO.builder()
            .build();
        var eventDto = EventDto.builder()
            .id(id)
            .externalId(externalInvoiceId)
            .recordType(RecordType.INVOICE)
            .eventType(EventType.CREATE)
            .eventTime(LocalDateTime.now())
            .invoice(externalInvoice)
            .build();

        when(invoiceProcessor.process(externalInvoice, ExternalSystem.NETSUITE))
            .thenThrow(new IllegalArgumentException());

        // WHEN and THEN
        assertThrows(IllegalArgumentException.class,
            () -> invoiceEventDataProcessor.process(eventDto, ExternalSystem.NETSUITE));
    }

    @Test
    void givenEventWithRelatedCreditNotes_whenProcess_thenCreditNoteProcessorIsCalled() {
        // GIVEN
        var id = 42L;
        var externalInvoiceId = "awesomeInvoiceId";
        var externalInvoice = InvoiceDTO.builder()
            .build();
        var relatedCreditNote = CreditNoteDto.builder()
            .build();
        var eventDto = EventDto.builder()
            .id(id)
            .externalId(externalInvoiceId)
            .recordType(RecordType.INVOICE)
            .eventType(EventType.CREATE)
            .eventTime(LocalDateTime.now())
            .invoice(externalInvoice)
            .relatedCreditNotes(List.of(relatedCreditNote))
            .build();

        var processedInvoice = externalInvoice;
        when(invoiceProcessor.process(externalInvoice, ExternalSystem.NETSUITE))
            .thenReturn(processedInvoice);

        var processedCreditNote = relatedCreditNote;
        when(creditNoteProcessor.process(relatedCreditNote))
            .thenReturn(processedCreditNote);

        // WHEN
        invoiceEventDataProcessor.process(eventDto, ExternalSystem.NETSUITE);

        // THEN
        verify(creditNoteProcessor).process(relatedCreditNote);
        verify(invoiceCreditNoteService).insert(processedInvoice.getExternalId(), processedCreditNote.getExternalId());
        verify(invoiceAppEventPublisher).publish(processedInvoice);
    }

    @ParameterizedTest
    @CsvSource({"false,false,false,false", "true,false,false,false", "false,true,true,true"})
    void givenEvent_thenIsInviteDataPresent_thenItPostponesIt(boolean invoiceNoteFound, boolean proposedProcessingTimePresent, boolean validatorResult, boolean expectedResult){
        var id = 42L;
        var externalInvoiceId = "awesomeInvoiceId";
        var eventDtoCur = EventDto.builder()
                .id(id)
                .externalId(externalInvoiceId)
                .recordType(RecordType.INVOICE)
                .eventType(EventType.CREATE)
                .invoice(InvoiceDTO.builder().build())
                .eventTime(LocalDateTime.now())
                .proposedProcessingTime( proposedProcessingTimePresent ? LocalDateTime.now().plusMinutes(5L) : null)
                .build();


        var invoiceDTO = InvoiceDTO.builder()
                .build();
        when(invoiceService.find(eventDtoCur.getExternalId()))
                .thenReturn(invoiceNoteFound ? Optional.of(invoiceDTO) : Optional.empty());

        when(eventDataOrderValidatorService.validateEventOrder(eventDtoCur, ExternalSystem.NETSUITE, proposedProcessingTimePresent)).thenReturn(validatorResult);

        // WHEN
        var result = invoiceEventDataProcessor.isEventDataPresent(eventDtoCur, ExternalSystem.NETSUITE);

        // THEN
        assertEquals(expectedResult, result);
    }
}