package com.multiplier.core.payable.report.collector;

import com.multiplier.core.payable.invoice.database.JpaInvoiceRepository;
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository;
import com.multiplier.core.payable.repository.model.JpaCompanyPayable;
import com.multiplier.core.payable.repository.model.JpaInvoice;
import com.multiplier.core.payable.service.InvoiceFetcher;
import com.multiplier.payable.types.InvoiceType;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class IsrInvoiceFetcherTest {
    @Mock
    private InvoiceFetcher invoiceFetcher;
    @Mock
    private JpaCompanyPayableRepository jpaCompanyPayableRepository;
    @Mock
    private JpaInvoiceRepository jpaInvoiceRepository;
    @InjectMocks
    private IsrInvoiceFetcher isrInvoiceFetcher;

    @Test
    void should_fetch_gross_invoice_correctly() {
        // GIVEN
        long companyId = 1L;
        int month = 1;
        int year = 2024;
        var pairList = List.of(Pair.of(2L, 3L));
        when(invoiceFetcher.getFirstInvoices(companyId, month, year)).thenReturn(pairList);
        when(jpaCompanyPayableRepository.findAllById(List.of(2L)))
                .thenReturn(List.of(JpaCompanyPayable.builder().id(2L).build()));
        when(jpaInvoiceRepository.findAllById(List.of(3L)))
                .thenReturn(List.of(JpaInvoice.builder().id(3L).build()));

        // WHEN
        var result = isrInvoiceFetcher.fetchInvoice(InvoiceType.GROSS, 1L, 1, 2024);

        // THEN
        assertEquals(1, result.size());

    }

    @Test
    void should_fetch_salary_invoice_correctly() {
        // GIVEN
        long companyId = 1L;
        int month = 1;
        int year = 2024;
        var pairList = List.of(Pair.of(2L, 3L));
        when(invoiceFetcher.getSecondInvoicePairForIsr(companyId, month, year)).thenReturn(pairList);
        when(jpaCompanyPayableRepository.findAllById(List.of(2L)))
                .thenReturn(List.of(JpaCompanyPayable.builder().id(2L).build()));
        when(jpaInvoiceRepository.findAllById(List.of(3L)))
                .thenReturn(List.of(JpaInvoice.builder().id(3L).build()));

        // WHEN
        var result = isrInvoiceFetcher.fetchInvoice(InvoiceType.SALARY, 1L, 1, 2024);

        // THEN
        assertEquals(1, result.size());

    }
}