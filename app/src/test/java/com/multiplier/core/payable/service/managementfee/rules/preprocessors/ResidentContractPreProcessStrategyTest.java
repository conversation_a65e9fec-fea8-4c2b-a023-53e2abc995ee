package com.multiplier.core.payable.service.managementfee.rules.preprocessors;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class ResidentContractPreProcessStrategyTest {

    public static final int COUNTRY_RANK = 10;
    private final List<PreProcessedContract> preProcessedContractList = new ArrayList<>();
    @Mock
    private PreProcessedContract nonResidentContract;

    @Mock
    private PreProcessedContract residentContract;

    @Test
    void should_process_resident_contracts() {

        doReturn(true).when(nonResidentContract).hasVisaCost();
        doReturn(false).when(residentContract).hasVisaCost();
        preProcessedContractList.add(nonResidentContract);
        preProcessedContractList.add(residentContract);

        ResidentContractPreProcessStrategy strategy =  new ResidentContractPreProcessStrategy() {
            @Override
            public List<PreProcessedContract> processOnlyResident(List<PreProcessedContract> residentContracts) {
                for (PreProcessedContract contract : residentContracts) {
                    contract.setCountryRank(COUNTRY_RANK);
                }

                return residentContracts;
            }
        };

        strategy.process(preProcessedContractList);

        verify(residentContract).setCountryRank(COUNTRY_RANK);
    }
}