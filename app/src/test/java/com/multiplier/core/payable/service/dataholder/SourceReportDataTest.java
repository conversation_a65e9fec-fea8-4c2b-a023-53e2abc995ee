package com.multiplier.core.payable.service.dataholder;

import com.google.type.Date;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.company.Company;
import com.multiplier.core.util.dto.payroll.CompanyMemberPayWrapper;
import com.multiplier.member.schema.Member;
import com.multiplier.payable.engine.domain.aggregates.AdditionalFeePayrollKey;
import com.multiplier.payable.engine.domain.entities.PayrollCycleFrequency;
import com.multiplier.payable.types.CurrencyCode;
import com.multiplier.payroll.schema.Payroll;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

class SourceReportDataTest {
    private final ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
            .setId(2L)
            .setMemberId(3L)
            .setCountry("SGP")
            .build();
    private final ContractOuterClass.Contract contractWithEndOn = ContractOuterClass.Contract.newBuilder()
            .setId(3L)
            .setMemberId(4L)
            .setCountry("SGP")
            .setEndOn(Date.newBuilder()
                    .setYear(2024)
                    .setMonth(7)
                    .setDay(31)
                    .build())
            .build();
    private final Company company = Company.builder()
            .id(1L)
            .displayName("Company name in Company")
            .build();
    private final Member member = Member.newBuilder()
            .setId(3L)
            .setFullLegalName("Full Legal Name")
            .build();
    private final SourceReportData sourceReportData = SourceReportData.builder()
            .companyId(1L)
            .companyDisplayName("Company name")
            .contract(contract)
            .payFrequency("MONTHLY")
            .companyMemberPayWrapper(
                    buildCompanyMemberPayWrapper(1L, contract, CurrencyCode.SGD, 1230.0, 0.0)
            )
            .contractData(ContractInvoiceOrCreditNoteData.builder()
                    .contractId(2L)
                    .managementFeeInFirstInvoiceGrossAmount(109.0)
                    .managementFeeInFirstInvoiceUnitPrice(100.0)
                    .actualManagementFeeGrossAmount(218.0)
                    .actualManagementFeeUnitPrice(200.0)
                    .actualManagementFee(Pair.of(CurrencyCode.USD, 200.0))
                    .totalManagementFee(218.0)
                    .managementFeeUnitPrice(120.0)
                    .refundManagementFeeGrossAmount(8.0)
                    .grossSalaryGrossAmount(2180.0)
                    .grossUnitPrice(2000.0)
                    .grossSalary(Pair.of(CurrencyCode.SGD, 2460.0))
                    .taxRates(Map.of(LineItemType.EOR_SALARY_DISBURSEMENT, Set.of(9.0)))
                    .build())
            .payableId(100L)
            .company(company)
            .invoiceType(2)
            .companyBillingCurrency(CurrencyCode.USD)
            .member(member)
            .exchangeRate(1.23)
            .build();

    private final SourceReportData semiMonthLyFirstDataFirstAppearance = SourceReportData.builder()
            .companyId(1L)
            .companyDisplayName("Company name")
            .contract(
                    contract.toBuilder()
                            .setStartOn(
                                    Date.newBuilder()
                                            .setYear(2024)
                                            .setMonth(1)
                                            .setDay(1)
                                            .build()
                            ).build()
            )
            .payFrequency("PAY_FREQUENCY_SEMIMONTHLY")
            .companyMemberPayWrapper(buildCompanyMemberPayWrapper(1L, contract, CurrencyCode.SGD, 1230.0, 0.0))
            .contractData(ContractInvoiceOrCreditNoteData.builder()
                    .contractId(2L)
                    .build())
            .payableId(100L)
            .company(company)
            .invoiceType(2)
            .companyBillingCurrency(CurrencyCode.USD)
            .member(member)
            .semiMonthlyAlreadyProcessed(true)
            .build();

    private final AdditionalFeePayrollKey additionalFeePayrollKey =
            new AdditionalFeePayrollKey(PayrollCycleFrequency.SEMIMONTHLY, 2);
    private final SourceReportData semiMonthLyFirstDataSecondAppearance = SourceReportData.builder()
            .companyId(1L)
            .companyDisplayName("Company name")
            .contract(
                    contract.toBuilder()
                            .setStartOn(
                                    Date.newBuilder()
                                            .setYear(2024)
                                            .setMonth(1)
                                            .setDay(1)
                                            .build()
                            ).build()
            )
            .payrollCycleFrequency(PayrollCycleFrequency.SEMIMONTHLY)
            .payFrequency("PAY_FREQUENCY_SEMIMONTHLY")
            .companyMemberPayWrapper(buildCompanyMemberPayWrapper(1L, contract, CurrencyCode.SGD, 1230.0, 0.0))
            .contractData(ContractInvoiceOrCreditNoteData.builder()
                    .contractId(2L)
                    .additionalManagementFeeGrossAmounts(
                            Map.of(additionalFeePayrollKey, 50.0)
                    )
                    .additionalManagementFeeUnitPrices(
                            Map.of(additionalFeePayrollKey, 50.0)
                    )
                    .additionalManagementFees(
                            Map.of(additionalFeePayrollKey, Pair.of(CurrencyCode.USD, 50.0))
                    )
                    .build())
            .payableId(100L)
            .company(company)
            .invoiceType(2)
            .companyBillingCurrency(CurrencyCode.USD)
            .member(member)
            .semiMonthlyAlreadyProcessed(true)
            .cycleCount(2)
            .build();

    private final SourceReportData semiMonthLySecondData = SourceReportData.builder()
            .companyId(1L)
            .companyDisplayName("Company name")
            .contract(contractWithEndOn)
            .payFrequency("PAY_FREQUENCY_BIWEEKLY")
            .companyMemberPayWrapper(buildCompanyMemberPayWrapper(1L, contract, CurrencyCode.SGD, 1230.0, 0.0))
            .contractData(ContractInvoiceOrCreditNoteData.builder()
                    .contractId(contractWithEndOn.getId())
                    .build())
            .payableId(100L)
            .company(company)
            .invoiceType(2)
            .companyBillingCurrency(CurrencyCode.USD)
            .member(member)
            .semiMonthlyAlreadyProcessed(true)
            .build();

    private final SourceReportData notSemiMonthLySecondData = SourceReportData.builder()
            .companyId(1L)
            .companyDisplayName("Company name")
            .contract(contractWithEndOn)
            .payFrequency("PAY_FREQUENCY_BIWEEKLY")
            .companyMemberPayWrapper(buildCompanyMemberPayWrapper(1L, contract, CurrencyCode.SGD, 1230.0, 0.0))
            .contractData(ContractInvoiceOrCreditNoteData.builder()
                    .contractId(contractWithEndOn.getId())
                    .build())
            .payableId(100L)
            .company(company)
            .invoiceType(1)
            .companyBillingCurrency(CurrencyCode.USD)
            .member(member)
            .build();

    private final SourceReportData processingFeeSourceReportData = SourceReportData.builder()
            .companyId(1L)
            .companyDisplayName("Company name")
            .payFrequency("MONTHLY")
            .processingFee(5.0)
            .payableId(100L)
            .company(company)
            .invoiceType(2)
            .companyBillingCurrency(CurrencyCode.USD)
            .currencyCode(CurrencyCode.USD)
            .build();

    @Test
    void isProcessingFeeRow() {
        assertThat(processingFeeSourceReportData.isProcessingFeeRow()).isTrue();
        assertThat(sourceReportData.isProcessingFeeRow()).isFalse();
    }

    @Test
    void isSemiMonthlyRecord() {
        assertThat(semiMonthLyFirstDataFirstAppearance.isSemiMonthlyRecord()).isTrue();
        assertThat(semiMonthLyFirstDataSecondAppearance.isSemiMonthlyRecord()).isTrue();
        assertThat(semiMonthLySecondData.isSemiMonthlyRecord()).isTrue();
        assertThat(notSemiMonthLySecondData.isSemiMonthlyRecord()).isFalse();
        assertThat(sourceReportData.isSemiMonthlyRecord()).isFalse();
    }

    @Test
    void companyId() {
        assertThat(sourceReportData.companyId()).isEqualTo(1L);
    }

    @Test
    void contractId() {
        assertThat(sourceReportData.contractId()).isEqualTo(2L);
    }

    @Test
    void calculate_management_fee_semi_monthly() {
        var result = semiMonthLyFirstDataSecondAppearance.calculateManagementFeeFxRate();
        assertThat(result).isEqualTo(1.0);
    }

    @Test
    void calculate_management_fee_normal() {
        var result = sourceReportData.calculateManagementFeeFxRate();
        assertThat(result).isEqualTo(1.0);
    }

    @Test
    void calculate_management_fee_with_zero_mgt_fee_in_base_currency() {
        var testReportData = sourceReportData.toBuilder()
                .contractData(
                        sourceReportData.contractData.toBuilder()
                                .actualManagementFee(Pair.of(CurrencyCode.USD, 0.0))
                                .build()
                )
                .build();
        var result = testReportData.calculateManagementFeeFxRate();
        assertThat(result).isEqualTo(1.0);
    }

    @Test
    void calculate_management_fee_with_zero_mgt_fee_in_billed_currency() {
        var testReportData = sourceReportData.toBuilder()
                .contractData(
                        sourceReportData.contractData.toBuilder()
                                .actualManagementFeeUnitPrice(0.0)
                                .build()
                )
                .build();
        var result = testReportData.calculateManagementFeeFxRate();
        assertThat(result).isEqualTo(1.0);
    }

    @Test
    void calculateManagementFeesBilledInFirstInvoiceBillingCurrency() {
        assertThat(sourceReportData.calculateManagementFeesBilledInFirstInvoiceBillingCurrency())
                .isEqualTo(109.0);
    }

    @Test
    void calculate_mgt_fee_billed_currency_semimonthly() {
        assertThat(semiMonthLyFirstDataSecondAppearance.calculateManagementFeesBilledInFirstInvoiceBillingCurrency())
                .isEqualTo(0.0);
    }

    @Test
    void calculate_mgt_fee_billed_currency_null_contract_data() {
        assertThat(processingFeeSourceReportData.calculateManagementFeesBilledInFirstInvoiceBillingCurrency())
                .isEqualTo(0.0);
    }

    @Test
    void calculate_mgt_fee_billed_currency_null_gross_amount() {
        var testReportData = sourceReportData.toBuilder()
                .contractData(
                        sourceReportData.contractData.toBuilder()
                                .managementFeeInFirstInvoiceGrossAmount(null)
                                .build()
                )
                .build();
        assertThat(testReportData.calculateManagementFeesBilledInFirstInvoiceBillingCurrency())
                .isEqualTo(100.0);
    }

    @Test
    void calculate_mgt_fee_billed_currency_null_gross_amount_unit_price() {
        var testReportData = sourceReportData.toBuilder()
                .contractData(
                        sourceReportData.contractData.toBuilder()
                                .managementFeeInFirstInvoiceGrossAmount(null)
                                .managementFeeInFirstInvoiceUnitPrice(null)
                                .build()
                )
                .build();
        assertThat(testReportData.calculateManagementFeesBilledInFirstInvoiceBillingCurrency())
                .isEqualTo(0.0);
    }

    @Test
    void calculateTotalManagementFees() {
        assertThat(sourceReportData.calculateTotalManagementFees()).isEqualTo(218.0);
    }

    @Test
    void calculate_total_mgt_fees_semi_monthly() {
        assertThat(semiMonthLyFirstDataSecondAppearance.calculateTotalManagementFees()).isEqualTo(50.0);
    }

    @Test
    void calculate_total_mgt_fees_null_contract_data() {
        assertThat(processingFeeSourceReportData.calculateTotalManagementFees()).isEqualTo(0.0);
    }

    @Test
    void calculate_total_mgt_fees_null_actual_mgt_fee_gross() {
        var testReportData = sourceReportData.toBuilder()
                .contractData(
                        sourceReportData.contractData.toBuilder()
                                .actualManagementFeeGrossAmount(null)
                                .build()
                )
                .build();
        assertThat(testReportData.calculateTotalManagementFees()).isEqualTo(210.0);
    }

    @Test
    void calculate_total_mgt_fees_null_actual_mgt_fee_gross_null_total_mgt_fee() {
        var testReportData = sourceReportData.toBuilder()
                .contractData(
                        sourceReportData.contractData.toBuilder()
                                .actualManagementFeeGrossAmount(null)
                                .totalManagementFee(null)
                                .build()
                )
                .build();
        assertThat(testReportData.calculateTotalManagementFees()).isEqualTo(112.0);
    }

    @Test
    void calculateManagementFeesInBillingCurrency() {
        assertThat(sourceReportData.calculateManagementFeesInBillingCurrency()).isEqualTo(200.0);
    }

    @Test
    void whenSemimonthly_calculateManagementFeesInBillingCurrency_returnAmount() {
        assertThat(semiMonthLyFirstDataSecondAppearance.calculateManagementFeesInBillingCurrency()).isEqualTo(50.0);
    }

    @Test
    void whenContractIsNull_calculateManagementFeesInBillingCurrency_returnZero() {
        var testReportData = sourceReportData.toBuilder()
                .contractData(null)
                .build();
        assertThat(testReportData.calculateManagementFeesInBillingCurrency()).isEqualTo(0.0);
    }

    @Test
    void whenActualAndUnitPriceAndRefundAndReversalAreAllNull_calculateManagementFeesInBillingCurrency() {
        var testReportData = sourceReportData.toBuilder()
                .contractData(
                        sourceReportData.contractData.toBuilder()
                                .actualManagementFeeUnitPrice(null)
                                .managementFeeUnitPrice(null)
                                .build()
                )
                .build();
        assertThat(testReportData.calculateManagementFeesInBillingCurrency()).isEqualTo(0.0);
    }

    @Test
    void whenActualIsNullWithRefunded_calculateManagementFeesInBillingCurrency() {
        var testReportData = sourceReportData.toBuilder()
                .contractData(
                        sourceReportData.contractData.toBuilder()
                                .actualManagementFeeUnitPrice(null)
                                .refundedManagementFeeUnitPrice(8.0)
                                .build()
                )
                .build();
        assertThat(testReportData.calculateManagementFeesInBillingCurrency()).isEqualTo(112.0);
    }

    @Test
    void whenActualIsNullWithReversal_calculateManagementFeesInBillingCurrency() {
        var testReportData = sourceReportData.toBuilder()
                .contractData(
                        sourceReportData.contractData.toBuilder()
                                .actualManagementFeeUnitPrice(null)
                                .memberManagementFeeReversalUnitPrice(7.0)
                                .build()
                )
                .build();
        assertThat(testReportData.calculateManagementFeesInBillingCurrency()).isEqualTo(113.0);
    }

    @Test
    void calculateManagementFeesBaseCurrency() {
        assertThat(sourceReportData.calculateManagementFeesBaseCurrency()).isEqualTo(200.0);
    }

    @Test
    void calculate_billing_currency() {
        assertThat(sourceReportData.calculateBillingCurrency()).isEqualTo(CurrencyCode.USD);
        var testReportData = sourceReportData.toBuilder()
                .companyBillingCurrency(null)
                .build();
        assertThat(testReportData.calculateBillingCurrency()).isEqualTo("");
    }

    @Test
    void calculateCountry() {
        assertThat(sourceReportData.calculateCountry()).isEqualTo("SGP");
        assertThat(processingFeeSourceReportData.calculateCountry()).isEqualTo("");
    }

    @Test
    void calculateGSTAmount() {
        assertThat(sourceReportData.calculateGSTAmount()).isEqualTo(18.0);
    }

    @Test
    void calculateTotalCostInBillingCurrency() {
        assertThat(sourceReportData.calculateTotalCostInBillingCurrency())
                .isEqualTo(1308.0);
    }

    @Test
    void calculateTotalCostInBillingCurrencyWithSeverance() {
        var testData = sourceReportData.toBuilder()
                .shouldSetSeverance(true)
                .contractData(
                        sourceReportData.getContractData()
                                .toBuilder()
                                .taxRates(Map.of(LineItemType.EOR_SALARY_DISBURSEMENT, Set.of(9.0), LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL, Set.of(9.0)))
                                .severanceInBillingCurrency(1000.0)
                                .build()
                )
                .build();
        assertThat(testData.calculateTotalCostInBillingCurrency())
                .isEqualTo(2308.0);
    }

    @Test
    void calculateFirstInvoiceGrossSalaryGrossAmount() {
        assertThat(sourceReportData.calculateFirstInvoiceGrossSalaryGrossAmount())
                .isEqualTo(2180.0);
    }

    @Test
    void calculateTotalPayrollCostInLocalCurrency() {
        assertThat(sourceReportData.calculateTotalPayrollCostInLocalCurrency())
                .isEqualTo(1230.0);
    }

    @Test
    void calculateTransactionCurrency() {
        assertThat(sourceReportData.calculateTransactionCurrency())
                .isEqualTo(CurrencyCode.SGD);
    }

    @Test
    void calculatePayrollTaxAmount() {
        assertThat(sourceReportData.calculatePayrollTaxAmount())
                .isEqualTo(90.0);
    }

    @Test
    void calculate_employer_contribution() {
        assertThat(sourceReportData.calculateEmployerContribution())
                .isEqualTo(300.0);
    }

    @Test
    void calculate_employer_contribution_type_1() {
        var testReportData = sourceReportData.toBuilder()
                .invoiceType(1)
                .build();
        assertThat(testReportData.calculateEmployerContribution()).isEqualTo(0.0);
    }

    @Test
    void calculate_employer_contribution_null_member_pay() {
        var testReportData = sourceReportData.toBuilder()
                .companyMemberPayWrapper(null)
                .build();
        assertThat(testReportData.calculateEmployerContribution()).isEqualTo(0.0);
    }

    @Test
    void calculate_employer_contribution_null_member_pay_contributions() {
        var testReportData = sourceReportData.toBuilder()
                .companyMemberPayWrapper(
                        buildCompanyMemberPayWrapper(1L, contract, CurrencyCode.SGD, 1230.0, null, 0.0)
                )
                .build();
        assertThat(testReportData.calculateEmployerContribution()).isEqualTo(0.0);
    }

    @Test
    void calculateCompanyLegalName() {
        assertThat(sourceReportData.calculateCompanyLegalName())
                .isEqualTo("Company name");
    }

    @Test
    void calculate_company_name_from_company() {
        var testData = sourceReportData.toBuilder()
                .companyDisplayName(null)
                .build();
        assertThat(testData.calculateCompanyLegalName()).isEqualTo("Company name in Company");
    }

    @Test
    void calculate_gross_salary() {
        assertThat(sourceReportData.calculateGrossSalary())
                .isEqualTo(1300.0);
    }

    @Test
    void calculate_gross_salary_invoice_type_1() {
        var testReportData = sourceReportData.toBuilder()
                .invoiceType(1)
                .contractData(
                        sourceReportData.contractData.toBuilder()
                                .grossSalary(Pair.of(CurrencyCode.SGD, 10000.0))
                                .build()
                )
                .build();
        assertThat(testReportData.calculateGrossSalary()).isEqualTo(10000.0);
    }

    @Test
    void calculate_gross_salary_invoice_type_1_null_gross_salary() {
        var testReportData = sourceReportData.toBuilder()
                .invoiceType(1)
                .contractData(
                        sourceReportData.contractData.toBuilder()
                                .grossSalary(null)
                                .build()
                )
                .build();
        assertThat(testReportData.calculateGrossSalary()).isEqualTo(1300.0);
    }

    @Test
    void calculate_gross_salary_invoice_type_1_null_gross_salary_null_member_pay() {
        var testReportData = sourceReportData.toBuilder()
                .invoiceType(1)
                .contractData(
                        sourceReportData.contractData.toBuilder()
                                .grossSalary(null)
                                .build()
                )
                .companyMemberPayWrapper(null)
                .build();
        assertThat(testReportData.calculateGrossSalary()).isEqualTo(0.0);
    }

    @Test
    void calculate_expense_reimbursement() {
        assertThat(sourceReportData.calculateExpenseReimbursement())
                .isEqualTo(400.0);
    }

    @Test
    void calculate_expense_reimbursement_type_1() {
        var testReportData = sourceReportData.toBuilder()
                .invoiceType(1)
                .build();
        assertThat(testReportData.calculateExpenseReimbursement()).isEqualTo(0.0);
    }

    @Test
    void calculate_expense_null_reimbursement() {
        var testReportData = sourceReportData.toBuilder()
                .companyMemberPayWrapper(null)
                .build();
        assertThat(testReportData.calculateExpenseReimbursement()).isEqualTo(0.0);
    }

    @Test
    void calculateTotalClientDeductions() {
        assertThat(sourceReportData.calculateTotalClientDeductions())
                .isEqualTo(123.0);
    }

    @Test
    void calculateBonus() {
        assertThat(sourceReportData.calculateBonus())
                .isEqualTo(212.0);
    }

    @Test
    void calculateAllowances() {
        assertThat(sourceReportData.calculateAllowances())
                .isEqualTo(211.0);
    }

    @Test
    void calculateCommission() {
        assertThat(sourceReportData.calculateCommission())
                .isEqualTo(312.0);
    }

    @Test
    void calculateContractEndDate() {
        assertThat(sourceReportData.calculateContractEndDate()).isEmpty();
        assertThat(semiMonthLySecondData.calculateContractEndDate()).isEqualTo("2024-07-31");
    }

    @Test
    void calculatePayrollCostWithTax() {
        assertThat(sourceReportData.calculatePayrollCostWithTax())
                .isEqualTo(1090.0);
    }

    @Test
    void calculatePayrollCostInBillingCurrency() {
        assertThat(sourceReportData.calculatePayrollCostInBillingCurrency())
                .isEqualTo(1000.0);
    }

    @Test
    void whenNormal_calculatePayrollCostFromMemberPay() {
        assertThat(sourceReportData.calculatePayrollCostFromMemberPay())
                .isEqualTo(1000.0);
    }

    @Test
    void whenNullMemberPay_calculatePayrollCostFromMemberPay() {
        assertThat(processingFeeSourceReportData.calculatePayrollCostFromMemberPay()).isEqualTo(0.0);
    }

    @Test
    void whenNullAmountTotalCost_calculatePayrollCostFromMemberPay() {
        var testReportData = sourceReportData.toBuilder()
                .companyMemberPayWrapper(
                        buildCompanyMemberPayWrapper(1L, contract, CurrencyCode.SGD, null, 0.0)
                )
                .build();
        assertThat(testReportData.calculatePayrollCostFromMemberPay()).isEqualTo(0.0);
    }

    @Test
    void whenExchangeRateIsZero_calculatePayrollCostFromMemberPay() {
        var testReportData = sourceReportData.toBuilder()
                .exchangeRate(0.0)
                .build();
        assertThat(testReportData.calculatePayrollCostFromMemberPay()).isEqualTo(sourceReportData.companyMemberPayWrapper.getAmountTotalCost());
    }

    @Test
    void calculateContractStartDate() {
        assertThat(sourceReportData.calculateContractStartDate()).isEmpty();
        assertThat(semiMonthLyFirstDataSecondAppearance.calculateContractStartDate()).isEqualTo("2024-01-01");
    }

    @Test
    void calculateTaxRateInDecimalPoint() {
        assertThat(sourceReportData.calculateTaxRateInDecimalPoint())
                .isEqualTo(0.09);
    }

    @Test
    void calculate_tax_rates() {
        assertThat(sourceReportData.calculateTaxRate(sourceReportData.contractData.getTaxRates()))
                .isEqualTo(9);
    }

    @Test
    void calculate_tax_rates_null_input() {
        assertThat(sourceReportData.calculateTaxRate(Map.of())).isEqualTo(0.0);
    }

    @Test
    void calculateCurrencyCode() {
        var resultOptional = sourceReportData.calculateCurrencyCode();
        assertThat(resultOptional).isPresent().contains(CurrencyCode.SGD);
        resultOptional = processingFeeSourceReportData.calculateCurrencyCode();
        assertThat(resultOptional).isPresent().contains(CurrencyCode.USD);
    }

    @Test
    void empty_currency_code_for_null_currency_code_no_contract_data_and_member_pay() {
        var testData = processingFeeSourceReportData.toBuilder()
                .contractData(null)
                .companyMemberPayWrapper(null)
                .currencyCode(null)
                .build();
        var resultOptional = testData.calculateCurrencyCode();
        assertThat(resultOptional).isEmpty();
    }

    @Test
    void empty_currency_code_for_null_currency_code_no_member_pay() {
        var testData = sourceReportData.toBuilder()
                .companyMemberPayWrapper(null)
                .build();
        var resultOptional = testData.calculateCurrencyCode();
        assertThat(resultOptional).isPresent().contains(CurrencyCode.SGD);
    }

    @Test
    void empty_currency_code_for_null_currency_code_member_pay_no_currency_code() {
        var testData = sourceReportData.toBuilder()
                .companyMemberPayWrapper(
                        buildCompanyMemberPayWrapper(1L, contract, null, 1230.0, 0.0)
                )
                .build();
        var resultOptional = testData.calculateCurrencyCode();
        assertThat(resultOptional).isPresent().contains(CurrencyCode.SGD);
    }

    @Test
    void calculate_full_legal_name() {
        assertThat(sourceReportData.calculateName()).isEqualTo("Full Legal Name");
    }

    @Test
    void calculate_full_legal_name_for_null_member() {
        assertThat(processingFeeSourceReportData.calculateName()).isEmpty();
    }

    @Test
    void when_amount_present_calculate_severance_amount_in_local_currency() {
        var testData = sourceReportData.toBuilder()
                .exchangeRate(0.0)
                .shouldSetSeverance(true)
                .contractData(
                        sourceReportData.getContractData()
                                .toBuilder()
                                .taxRates(Map.of(LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL, Set.of(0.0)))
                                .severanceInBaseCurrency(1000.0)
                                .build()

                )
                .build();
        assertThat(testData.calculateSeveranceAmountInLocalCurrency()).isEqualTo(1000.0);
    }


    @Test
    void whenContractDataNull_calculate_severance_amount_in_billing_currency() {
        var testData = sourceReportData.toBuilder()
                .exchangeRate(0.0)
                .companyMemberPayWrapper(
                        buildCompanyMemberPayWrapper(1L, contract, CurrencyCode.SGD, 1230.0, 1000.0)
                )
                .contractData(null)
                .build();
        assertThat(testData.calculateSeveranceAmountInBillingCurrency()).isEqualTo(0.0);
    }

    @Test
    void whenShouldSetSeveranceFalse_calculate_severance_amount_in_billing_currency() {
        var testData = sourceReportData.toBuilder()
                .exchangeRate(0.0)
                .shouldSetSeverance(false)
                .contractData(
                        sourceReportData.getContractData()
                                .toBuilder()
                                .taxRates(Map.of(LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL, Set.of(9.0)))
                                .build()
                )
                .build();
        assertThat(testData.calculateSeveranceAmountInBillingCurrency()).isEqualTo(0.0);
    }

    @Test
    void whenShouldSetSeveranceTrue_calculate_severance_amount_in_billing_currency() {
        var testData = sourceReportData.toBuilder()
                .exchangeRate(0.5)
                .shouldSetSeverance(true)
                .contractData(
                        sourceReportData.getContractData()
                                .toBuilder()
                                .taxRates(Map.of(LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL, Set.of(9.0)))
                                .severanceInBillingCurrency(2180.0)
                                .build()
                )
                .build();
        assertThat(testData.calculateSeveranceAmountInBillingCurrency()).isEqualTo(2180.0);
    }

    @Test
    void whenSeveranceInBillingCurrencyNotSet_calculate_severance_amount_in_billing_currency() {
        var testData = sourceReportData.toBuilder()
                .exchangeRate(0.0)
                .contractData(
                        sourceReportData.getContractData()
                                .toBuilder()
                                .taxRates(Map.of(LineItemType.SEVERANCE_DEPOSIT_EOR_PAYROLL, Set.of(0.0)))
                                .build()
                )
                .build();
        assertThat(testData.calculateSeveranceAmountInBillingCurrency()).isEqualTo(0.0);
    }

    @ParameterizedTest
    @CsvSource({
            "25.5, 25.5",
            ", 0.0",
            "0.0, 0.0",
            "-15.75, -15.75"
    })
    void calculateBankFee_shouldReturnExpectedValue(Double bankFeeInput, double expectedResult) {
        // GIVEN
        var testData = sourceReportData.toBuilder()
                .bankFee(bankFeeInput)
                .build();

        // WHEN
        var result = testData.calculateBankFee();

        // THEN
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void calculateBankFee_forProcessingFeeSourceReportData_shouldReturnZeroWhenBankFeeNotSet() {
        // WHEN
        var result = processingFeeSourceReportData.calculateBankFee();

        // THEN
        assertThat(result).isEqualTo(0.0);
    }

    @SuppressWarnings("SameParameterValue")
    private CompanyMemberPayWrapper buildCompanyMemberPayWrapper(
            Long id,
            ContractOuterClass.Contract contract,
            CurrencyCode currencyCode,
            Double totalAmount,
            Double severanceAmount
    ) {
        var contributions = List.of(
                Payroll.PayComponent.newBuilder()
                        .setValue(100.0)
                        .build(),
                Payroll.PayComponent.newBuilder()
                        .setValue(200.0)
                        .build()
        );
        return buildCompanyMemberPayWrapper(id, contract, currencyCode, totalAmount, contributions, severanceAmount);
    }

    @SuppressWarnings("SameParameterValue")
    private CompanyMemberPayWrapper buildCompanyMemberPayWrapper(
            Long id,
            ContractOuterClass.Contract contract,
            CurrencyCode currencyCode,
            Double totalAmount,
            List<Payroll.PayComponent> contributions,
            Double severanceAmount
    ) {
        return new CompanyMemberPayWrapper(id,
                contract,
                currencyCode,
                null,
                totalAmount,
                1300.0,
                1300.0,
                400.0,
                212.0,
                312.0,
                211.0,
                null,
                null,
                null,
                null,
                contributions,
                null,
                null,
                null,
                null,
                null,
                123.0,
                severanceAmount,
                null, null, Collections.emptyMap(),null
                );
    }

}