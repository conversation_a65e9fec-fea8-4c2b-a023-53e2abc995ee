package com.multiplier.core.payable.graphql;

import com.multiplier.core.payable.sync.manualsync.NetsuiteManualSyncService;
import com.multiplier.payable.types.CompanyPayableSyncInput;
import com.multiplier.core.payable.sync.forwardsync.NetsuiteForwardSyncHandler;
import com.multiplier.payable.types.FinancialTransactionRecordInput;
import com.multiplier.payable.types.RecordType;
import com.multiplier.payable.types.ResyncFinancialTransactionsInput;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PayableDataMutatorTest {

    @Mock
    private NetsuiteForwardSyncHandler netsuiteForwardSyncHandler;

    @Mock
    private NetsuiteManualSyncService netsuiteManualSyncService;

    @InjectMocks
    private PayableDataMutator payableDataMutator;

    @Nested
    class CompanyPayableSyncFromExternalSystem {

        @Test
        void companyPayableSyncFromExternalSystem() {
            var companyPayableSyncInput = CompanyPayableSyncInput
                    .newBuilder()
                    .build();

            payableDataMutator.companyPayableSyncFromExternalSystem(companyPayableSyncInput);

            verify(netsuiteForwardSyncHandler).handle(companyPayableSyncInput);
        }

    }

    @Test
    void if_resync_is_successful_then_return_successful_task_response() {
        //given
        var input = ResyncFinancialTransactionsInput.newBuilder()
                .records(
                        List.of(
                                FinancialTransactionRecordInput.newBuilder()
                                        .id("1")
                                        .type(RecordType.INVOICE)
                                        .build()
                        )
                ).build();

        when(netsuiteManualSyncService.resync(input)).thenReturn(Boolean.TRUE);

        //when
        var result = payableDataMutator.resyncFinancialTransactions(input);

        assertThat(result.getSuccess()).isTrue();
    }

    @Test
    void if_resync_is_failed_then_return_failed_task_response() {
        //given
        var input = ResyncFinancialTransactionsInput.newBuilder()
                .records(
                        List.of(
                                FinancialTransactionRecordInput.newBuilder()
                                        .id("1")
                                        .type(RecordType.INVOICE)
                                        .build()
                        )
                ).build();

        when(netsuiteManualSyncService.resync(input)).thenReturn(Boolean.FALSE);

        //when
        var result = payableDataMutator.resyncFinancialTransactions(input);

        assertThat(result.getSuccess()).isFalse();
    }

}