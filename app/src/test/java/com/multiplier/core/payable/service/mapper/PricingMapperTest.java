package com.multiplier.core.payable.service.mapper;

import com.multiplier.core.payable.repository.model.JpaPricing;
import com.multiplier.core.payable.service.OffboardingPricingService;
import com.multiplier.core.payable.service.PricingFactory;
import com.multiplier.payable.types.PricingInput;
import lombok.val;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doReturn;

@ExtendWith(MockitoExtension.class)
class PricingMapperTest {

    @Mock
    private DiscountTermMapper discountTermMapper;
    @Mock
    private PricingFactory pricingFactory;
    @Mock
    private DepositTermMapper depositTermMapper;
    @Mock
    private GlobalPricingMapper globalPricingMapper;
    @Mock
    private VisaGlobalPricingMapper visaGlobalPricingMapper;
    @Mock
    private OffboardingPricingService offboardingPricingService;
    @InjectMocks
    private PricingMapperImpl pricingMapper;

    @Test
    void mapPricing() {
        val companyId = 1L;
        val pricingInput = PricingInput.newBuilder().build();
        val discountTerms = Collections.EMPTY_LIST;
        val offboardingPricing = Collections.EMPTY_LIST;

        doReturn(offboardingPricing).when(offboardingPricingService).getOffboardingFee(
                companyId, pricingInput.getOffboardingGlobalPricing()
        );

        val jpaPricing = pricingMapper.mapPricing(companyId, pricingInput, discountTerms);

        assertEquals(offboardingPricing, jpaPricing.getOffboardingGlobalPricing());
    }

    @Test
    void mapPricingWithMsaAddendumEnabled() {
        val companyId = 1L;
        val pricingInput = PricingInput.newBuilder().build();
        val offboardingPricing = Collections.EMPTY_LIST;
        val oldPricing = JpaPricing.builder()
                .companyId(companyId)
                .build();

        doReturn(offboardingPricing).when(offboardingPricingService).getOffboardingFee(
                companyId, pricingInput.getOffboardingGlobalPricing()
        );

        val jpaPricing = pricingMapper.mapPricingWithMsaAddendumEnabled(oldPricing, pricingInput);

        assertEquals(offboardingPricing, jpaPricing.getOffboardingGlobalPricing());
    }

    @Test
    void mapFromJpa() {
        val offboardingPricing = Collections.EMPTY_LIST;

        val jpaPricing = JpaPricing.builder()
                .offboardingGlobalPricing(offboardingPricing)
                .build();

        val pricing = pricingMapper.mapFromJpa(jpaPricing);

        assertEquals(offboardingPricing, pricing.getOffboardingGlobalPricing());
    }

}