package com.multiplier.core.payable.sync.mapping;

import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.adapters.api.LineItemDTO;
import com.multiplier.core.payable.repository.model.ExternalSystem;
import com.multiplier.core.payable.repository.model.InvoiceReason;
import com.multiplier.core.payable.repository.model.InvoiceType;
import com.multiplier.payable.types.CurrencyCode;
import com.multiplier.payable.types.InvoiceStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NetsuiteInvoiceMapperProxyTest {

    @Mock
    private LineItemMappingAdapter lineItemMappingAdapter;

    @Mock
    private InvoiceDtoFromNetsuiteInvoiceMapper mockDelegate;

    private NetsuiteInvoiceMapperProxy mapperProxy;

    private NetsuiteInvoice testNetsuiteInvoice;
    private String testExternalId;

    @BeforeEach
    void setUp() {
        testExternalId = "test-external-id-123";

        // Integer, not double - represents percentage as integer (10 = 10%)
        InvoiceItem testInvoiceItem = InvoiceItem.builder()
                .itemTypeId("item-type-1")
                .description("Test item description")
                .quantity(2.0)
                .unitAmount(100.0)
                .grossAmount(200.0)
                .taxAmount(20.0)
                .taxRate(10) // Integer, not double - represents percentage as integer (10 = 10%)
                .taxCode("TAX001")
                .classValue("US")
                .contractId(12345L)
                .memberName("John Doe")
                .baseCurrency("USD")
                .amountInBaseCurrency(200.0)
                .revRecStartDate(LocalDate.of(2023, 1, 1))
                .revRecEndDate(LocalDate.of(2023, 12, 31))
                .build();

        testNetsuiteInvoice = NetsuiteInvoice.builder()
                .externalId(testExternalId)
                .invoiceNo("INV-001")
                .totalAmount(200.0)
                .amountDue(150.0)
                .amountPaid(50.0)
                .reference("Test invoice reference")
                .customerId("customer-123")
                .reasonId("1") // FIRST_INVOICE
                .invoiceType("GROSS")
                .currencyName("USD")
                .currency("1")
                .date(LocalDateTime.of(2023, 6, 15, 10, 30))
                .dueDate(LocalDateTime.of(2023, 7, 15, 10, 30))
                .createdDate(LocalDateTime.of(2023, 6, 15, 10, 30))
                .status("PENDING")
                .items(Collections.singletonList(testInvoiceItem))
                .build();
    }

    @Test
    void shouldCreateProxyWithLineItemMappingAdapter() {
        // WHEN
        NetsuiteInvoiceMapperProxy proxy = new NetsuiteInvoiceMapperProxy(lineItemMappingAdapter);

        // THEN
        assertThat(proxy).isNotNull();
    }

    @Test
    void shouldAcceptNullLineItemMappingAdapter() {
        // WHEN
        NetsuiteInvoiceMapperProxy proxy = new NetsuiteInvoiceMapperProxy(null);

        // THEN
        assertThat(proxy).isNotNull();
        // The proxy should be created successfully even with null adapter
        // The actual null handling will be done by the MapStruct mapper
    }

    @Test
    void shouldInitializeDelegateCorrectlyInPostConstruct() {
        // GIVEN
        mapperProxy = new NetsuiteInvoiceMapperProxy(lineItemMappingAdapter);

        // WHEN
        mapperProxy.init();

        // THEN
        assertThat(mapperProxy).isNotNull();
        // The delegate should be initialized and ready to use
        // We can't directly test the delegate field since it's package-private,
        // but we can verify the proxy doesn't throw exceptions after init
    }

    @Test
    void shouldDelegateMapCallToMapperDelegate() {
        // GIVEN
        InvoiceDTO expectedDto = createMockInvoiceDto();

        try (MockedStatic<Mappers> mappersStatic = mockStatic(Mappers.class)) {
            mappersStatic.when(() -> Mappers.getMapper(InvoiceDtoFromNetsuiteInvoiceMapper.class))
                    .thenReturn(mockDelegate);
            when(mockDelegate.map(testNetsuiteInvoice, testExternalId))
                    .thenReturn(expectedDto);

            mapperProxy = new NetsuiteInvoiceMapperProxy(lineItemMappingAdapter);
            mapperProxy.init();

            // WHEN
            InvoiceDTO result = mapperProxy.map(testNetsuiteInvoice, testExternalId);

            // THEN
            assertThat(result).isNotNull();
            assertThat(result).isEqualTo(expectedDto);
            verify(mockDelegate).map(testNetsuiteInvoice, testExternalId);
        }
    }

    @Test
    void shouldDelegateMapCallWithNullExternalId() {
        // GIVEN
        InvoiceDTO expectedDto = createMockInvoiceDto();

        try (MockedStatic<Mappers> mappersStatic = mockStatic(Mappers.class)) {
            mappersStatic.when(() -> Mappers.getMapper(InvoiceDtoFromNetsuiteInvoiceMapper.class))
                    .thenReturn(mockDelegate);
            when(mockDelegate.map(testNetsuiteInvoice, null))
                    .thenReturn(expectedDto);

            mapperProxy = new NetsuiteInvoiceMapperProxy(lineItemMappingAdapter);
            mapperProxy.init();

            // WHEN
            InvoiceDTO result = mapperProxy.map(testNetsuiteInvoice, null);

            // THEN
            assertThat(result).isNotNull();
            assertThat(result).isEqualTo(expectedDto);
            verify(mockDelegate).map(testNetsuiteInvoice, null);
        }
    }

    @Test
    void shouldHandleNullInputsGracefully() {
        // GIVEN
        try (MockedStatic<Mappers> mappersStatic = mockStatic(Mappers.class)) {
            mappersStatic.when(() -> Mappers.getMapper(InvoiceDtoFromNetsuiteInvoiceMapper.class))
                    .thenReturn(mockDelegate);
            when(mockDelegate.map(null, testExternalId))
                    .thenReturn(null);

            mapperProxy = new NetsuiteInvoiceMapperProxy(lineItemMappingAdapter);
            mapperProxy.init();

            // WHEN & THEN
            // This should delegate to the mapper without throwing exceptions
            InvoiceDTO result = mapperProxy.map(null, testExternalId);

            assertThat(result).isNull();
            verify(mockDelegate).map(null, testExternalId);
        }
    }

    @Test
    void shouldInjectLineItemMappingAdapterIntoDelegate() {
        // GIVEN
        try (MockedStatic<Mappers> mappersStatic = mockStatic(Mappers.class)) {
            mappersStatic.when(() -> Mappers.getMapper(InvoiceDtoFromNetsuiteInvoiceMapper.class))
                    .thenReturn(mockDelegate);

            mapperProxy = new NetsuiteInvoiceMapperProxy(lineItemMappingAdapter);

            // WHEN
            mapperProxy.init();

            // THEN
            // Verify that the proxy is properly initialized
            assertThat(mapperProxy).isNotNull();

            // We can't directly verify field assignment since it's a field access, not a method call
            // Instead, we verify that the delegate was obtained from Mappers
            mappersStatic.verify(() -> Mappers.getMapper(InvoiceDtoFromNetsuiteInvoiceMapper.class));
        }
    }

    @Test
    void shouldHandleMultipleInitializationCalls() {
        // GIVEN
        try (MockedStatic<Mappers> mappersStatic = mockStatic(Mappers.class)) {
            mappersStatic.when(() -> Mappers.getMapper(InvoiceDtoFromNetsuiteInvoiceMapper.class))
                    .thenReturn(mockDelegate);

            mapperProxy = new NetsuiteInvoiceMapperProxy(lineItemMappingAdapter);

            // WHEN
            mapperProxy.init();
            mapperProxy.init(); // Call init multiple times
            mapperProxy.init();

            // THEN
            // Should not throw exceptions and should work correctly
            assertThat(mapperProxy).isNotNull();

            // Verify that the mapper was obtained multiple times (once per init call)
            mappersStatic.verify(() -> Mappers.getMapper(InvoiceDtoFromNetsuiteInvoiceMapper.class), times(3));
        }
    }

    @Test
    void shouldVerifyProxyBehaviorWithMockedDelegate() {
        // GIVEN
        InvoiceDTO expectedDto1 = createMockInvoiceDto();
        InvoiceDTO expectedDto2 = createMockInvoiceDto();
        String externalId1 = "external-id-1";
        String externalId2 = "external-id-2";

        try (MockedStatic<Mappers> mappersStatic = mockStatic(Mappers.class)) {
            mappersStatic.when(() -> Mappers.getMapper(InvoiceDtoFromNetsuiteInvoiceMapper.class))
                    .thenReturn(mockDelegate);
            when(mockDelegate.map(testNetsuiteInvoice, externalId1))
                    .thenReturn(expectedDto1);
            when(mockDelegate.map(testNetsuiteInvoice, externalId2))
                    .thenReturn(expectedDto2);

            mapperProxy = new NetsuiteInvoiceMapperProxy(lineItemMappingAdapter);
            mapperProxy.init();

            // WHEN
            InvoiceDTO result1 = mapperProxy.map(testNetsuiteInvoice, externalId1);
            InvoiceDTO result2 = mapperProxy.map(testNetsuiteInvoice, externalId2);

            // THEN
            assertThat(result1).isEqualTo(expectedDto1);
            assertThat(result2).isEqualTo(expectedDto2);
            verify(mockDelegate).map(testNetsuiteInvoice, externalId1);
            verify(mockDelegate).map(testNetsuiteInvoice, externalId2);
        }
    }

    private InvoiceDTO createMockInvoiceDto() {
        return InvoiceDTO.builder()
                .externalId("mock-external-id")
                .invoiceNo("MOCK-INV-001")
                .customerId("mock-customer-123")
                .date(LocalDate.of(2023, 6, 15))
                .dueDate(LocalDate.of(2023, 7, 15))
                .reference("Mock invoice reference")
                .status(InvoiceStatus.PENDING)
                .totalAmount(200.0)
                .billingCurrencyCode(CurrencyCode.USD)
                .amountPaid(50.0)
                .amountDue(150.0)
                .externalSystem(ExternalSystem.NETSUITE)
                .reason(InvoiceReason.FIRST_INVOICE)
                .type(InvoiceType.GROSS)
                .lineItems(List.of(createMockLineItemDto()))
                .build();
    }

    private LineItemDTO createMockLineItemDto() {
        return LineItemDTO.builder()
                .description("Mock line item description")
                .quantity(2.0)
                .unitAmount(100.0)
                .grossAmount(200.0)
                .build();
    }
}
