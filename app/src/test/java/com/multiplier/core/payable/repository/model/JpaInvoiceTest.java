package com.multiplier.core.payable.repository.model;

import com.multiplier.core.payable.event.database.RecordType;
import com.multiplier.payable.types.InvoiceStatus;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class JpaInvoiceTest {

    @Test
    void givenInvoice_whenCreateAnother_thenTheyEqual() {
        // GIVEN
        var invoice1 = new JpaInvoice();
        invoice1.setStatus(InvoiceStatus.AUTHORIZED);

        // WHEN
        var invoice2 = new JpaInvoice();
        invoice2.setStatus(InvoiceStatus.AUTHORIZED);

        // THEN
        assertEquals(invoice1, invoice2);
    }

    @Test
    void givenInvoice_whenCreateAnother_thenHashCodesEqual() {
        // GIVEN
        var invoice1 = new JpaInvoice();
        invoice1.setStatus(InvoiceStatus.AUTHORIZED);

        // WHEN
        var invoice2 = new JpaInvoice();
        invoice2.setStatus(InvoiceStatus.AUTHORIZED);

        // THEN
        assertEquals(invoice1.hashCode(), invoice2.hashCode());
    }

    @Test
    void givenInvoice_thenCreateAnother_thenToStringsEqual() {
        // GIVEN
        var invoice1 = new JpaInvoice();
        invoice1.setStatus(InvoiceStatus.AUTHORIZED);

        // WHEN
        var invoice2 = new JpaInvoice();
        invoice2.setStatus(InvoiceStatus.AUTHORIZED);

        // THEN
        assertEquals(invoice1.toString(), invoice2.toString());
    }

    @Test
    void givenInvoice_whenToBuilder_thenTheyAreEqual() {
        // GIVEN
        var invoice1 = new JpaInvoice();
        invoice1.setStatus(InvoiceStatus.AUTHORIZED);

        // WHEN
        var invoice2 = invoice1.toBuilder()
                .build();

        // THEN
        assertEquals(invoice1, invoice2);
    }

    @Test
    void whenCreateInvoiceWithConstructor_thenRecordTypeDefaultsToInvoice() {
        // GIVEN
        var invoice = new JpaInvoice();

        // THEN
        assertEquals(RecordType.INVOICE, invoice.getRecordType());
    }

    @Test
    void whenCreateInvoiceWithBuilder_thenRecordTypeDefaultsToInvoice() {
        // GIVEN
        var invoice = JpaInvoice.builder()
                .build();

        // THEN
        assertEquals(RecordType.INVOICE, invoice.getRecordType());
    }
}