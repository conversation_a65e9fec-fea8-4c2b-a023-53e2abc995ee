package com.multiplier.core.payable.service;

import com.multiplier.core.payable.company.adapter.CompanyServiceAdapter;
import com.multiplier.core.payable.repository.JpaEmployeePricingRepository;
import com.multiplier.core.payable.repository.JpaPricingRepository;
import com.multiplier.core.payable.repository.model.JpaDiscountTerm;
import com.multiplier.core.payable.repository.model.JpaEmployeePricing;
import com.multiplier.core.payable.repository.model.JpaPricing;
import com.multiplier.core.payable.repository.model.PriceType;
import com.multiplier.core.payable.service.exception.EntityNotFoundException;
import com.multiplier.core.payable.service.mapper.DiscountTermMapper;
import com.multiplier.core.payable.service.mapper.EmployeePricingMapper;
import com.multiplier.core.payable.service.mapper.PricingMapper;
import com.multiplier.core.payable.service.mapper.VisaPricingMapper;
import com.multiplier.payable.types.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PricingServiceTest {

    @Mock
    private JpaPricingRepository jpaPricingRepository;
    @Mock
    private JpaEmployeePricingRepository jpaEmployeePricingRepository;
    @Mock
    private PricingMapper pricingMapper;
    @Mock
    private DiscountTermMapper discountTermMapper;
    @Mock
    private CompanyServiceAdapter companyServiceAdapter;
    @Spy
    private EmployeePricingMapper employeePricingMapper = Mappers.getMapper(EmployeePricingMapper.class);
    @Spy
    private VisaPricingMapper visaPricingMapper = Mappers.getMapper(VisaPricingMapper.class);

    @InjectMocks
    private PricingService pricingService;

    private static final GlobalPricing employeePrincing = new GlobalPricing(ContractType.EMPLOYEE, 300.00);
    private static final GlobalPricing hrPrincing = new GlobalPricing(ContractType.HR_MEMBER, 20.00);
    private static final GlobalPricing freelancerPrincing = new GlobalPricing(ContractType.FREELANCER, 40.00);

    @Test
    void shouldReturnMappedPricingForGivenCompanyIds() {
        pricingService.getDepositForCompanies(List.of(1L, 2L, 3L));

        ArgumentCaptor<List<Long>> captor = ArgumentCaptor.forClass(List.class);

        verify(jpaPricingRepository, times(1)).findByCompanyIdIn(captor.capture());

        List<List<Long>> allValues = captor.getAllValues();
        assertEquals(1, allValues.size());
        assertEquals(3, allValues.get(0).size());
    }

    @Test
    void validate_freelancer_managementfee_on_country_not_having_pricing_return_global_price() {
        Pricing companyPricing = Pricing.newBuilder()
                .id(10L)
                .globalPricing(List.of(employeePrincing, hrPrincing, freelancerPrincing))
                .build();

        when(jpaEmployeePricingRepository.findByPricingIdForExceptionalCountries(any())).thenReturn(List.of());
        when(jpaPricingRepository.findByCompanyId(any())).thenReturn(new JpaPricing());
        when(pricingMapper.mapFromJpa(any())).thenReturn(companyPricing);

        double fee = pricingService.getManagementFeeByCountryAndContractType(10L, CountryCode.USA, ContractType.FREELANCER);
        assertEquals(40.00, fee);
    }

    @Test
    void validate_employee_managementfee_on_country_not_having_pricing_return_global_price() {
        Pricing companyPricing = Pricing.newBuilder()
                .id(10L)
                .globalPricing(List.of(employeePrincing, hrPrincing, freelancerPrincing))
                .build();

        when(jpaEmployeePricingRepository.findByPricingIdForExceptionalCountries(any())).thenReturn(List.of());
        when(jpaPricingRepository.findByCompanyId(any())).thenReturn(new JpaPricing());
        when(pricingMapper.mapFromJpa(any())).thenReturn(companyPricing);

        double fee = pricingService.getManagementFeeByCountryAndContractType(10L, CountryCode.USA, ContractType.EMPLOYEE);
        assertEquals(300.00, fee);
    }

    @Test
    void validate_hrmember_managementfee_on_country_not_having_pricing_return_global_price() {
        Pricing companyPricing = Pricing.newBuilder()
                .id(10L)
                .globalPricing(List.of(employeePrincing, hrPrincing, freelancerPrincing))
                .build();

        when(jpaEmployeePricingRepository.findByPricingIdForExceptionalCountries(any())).thenReturn(List.of());
        when(jpaPricingRepository.findByCompanyId(any())).thenReturn(new JpaPricing());
        when(pricingMapper.mapFromJpa(any())).thenReturn(companyPricing);

        double fee = pricingService.getManagementFeeByCountryAndContractType(10L, CountryCode.USA, ContractType.HR_MEMBER);
        assertEquals(20.00, fee);
    }

    @Test
    void validate_freelancer_managementfee_on_country_having_pricing_return_country_price() {
        Pricing companyPricing = Pricing.newBuilder()
                .id(10L)
                .globalPricing(List.of(employeePrincing, hrPrincing, freelancerPrincing))
                .build();

        JpaEmployeePricing employeePricing = new JpaEmployeePricing();
        employeePricing.setId(1L);
        employeePricing.setEmployeeType(ContractType.FREELANCER);
        employeePricing.setCountry(CountryCode.PHL);
        employeePricing.setFixedRate(25.00);
        employeePricing.setType(PriceType.COUNTRY);

        when(jpaEmployeePricingRepository.findByPricingIdForExceptionalCountries(any())).thenReturn(List.of(employeePricing));
        when(jpaPricingRepository.findByCompanyId(any())).thenReturn(new JpaPricing());
        when(pricingMapper.mapFromJpa(any())).thenReturn(companyPricing);

        double fee = pricingService.getManagementFeeByCountryAndContractType(10L, CountryCode.PHL, ContractType.FREELANCER);
        assertEquals(25.00, fee);
    }

    @Test
    void validate_employee_managementfee_on_country_having_pricing_return_country_price() {
        Pricing companyPricing = Pricing.newBuilder()
                .id(10L)
                .globalPricing(List.of(employeePrincing, hrPrincing, freelancerPrincing))
                .build();

        JpaEmployeePricing employeePricing = new JpaEmployeePricing();
        employeePricing.setId(1L);
        employeePricing.setEmployeeType(ContractType.EMPLOYEE);
        employeePricing.setCountry(CountryCode.PHL);
        employeePricing.setFixedRate(200.00);
        employeePricing.setType(PriceType.COUNTRY);

        when(jpaEmployeePricingRepository.findByPricingIdForExceptionalCountries(any())).thenReturn(List.of(employeePricing));
        when(jpaPricingRepository.findByCompanyId(any())).thenReturn(new JpaPricing());
        when(pricingMapper.mapFromJpa(any())).thenReturn(companyPricing);

        double fee = pricingService.getManagementFeeByCountryAndContractType(10L, CountryCode.PHL, ContractType.EMPLOYEE);
        assertEquals(200.00, fee);
    }

    @Test
    void validate_hrmember_managementfee_on_country_having_pricing_return_country_price() {
        Pricing companyPricing = Pricing.newBuilder()
                .id(10L)
                .globalPricing(List.of(employeePrincing, hrPrincing, freelancerPrincing))
                .build();

        JpaEmployeePricing employeePricing = new JpaEmployeePricing();
        employeePricing.setId(1L);
        employeePricing.setEmployeeType(ContractType.HR_MEMBER);
        employeePricing.setCountry(CountryCode.PHL);
        employeePricing.setFixedRate(10.00);
        employeePricing.setType(PriceType.COUNTRY);

        when(jpaEmployeePricingRepository.findByPricingIdForExceptionalCountries(any())).thenReturn(List.of(employeePricing));
        when(jpaPricingRepository.findByCompanyId(any())).thenReturn(new JpaPricing());
        when(pricingMapper.mapFromJpa(any())).thenReturn(companyPricing);

        double fee = pricingService.getManagementFeeByCountryAndContractType(10L, CountryCode.PHL, ContractType.HR_MEMBER);
        assertEquals(10.00, fee);
    }

    @Test
    void throw_valid_exception_when_prcicing_null() {

        Exception exception = assertThrows(EntityNotFoundException.class, () -> {
            when(jpaPricingRepository.findByCompanyId(any())).thenReturn(null);
            pricingService.getManagementFeeByCountryAndContractType(10L, CountryCode.PHL, ContractType.FREELANCER);
        });
    }

    @Nested
    class GetDiscountTerms {

        @Test
        void whenJpaPricingIsNull_returnEmptyList() {
            when(jpaPricingRepository.findByCompanyId(anyLong())).thenReturn(null);
            assertEquals(0, pricingService.getDiscountTerms(1L).size());
        }

        @Test
        void whenEmptyDiscountTerms_returnEmptyList() {
            Long companyId = 1L;
            JpaPricing jpaPricing = JpaPricing.builder()
                    .build();
            when(jpaPricingRepository.findByCompanyId(companyId)).thenReturn(jpaPricing);

            assertEquals(0, pricingService.getDiscountTerms(companyId).size());
        }

        @Test
        void whenDiscountTermsArePresent_returnsDiscountTerms() {
            Long companyId = 1L;
            double discount = 1.0;
            DiscountType discountType = DiscountType.ABSOLUTE;

            JpaDiscountTerm jpaDiscountTerm = new JpaDiscountTerm(discount, discountType, null, null, null, null, null, null);
            List<JpaDiscountTerm> discountTerms = List.of(jpaDiscountTerm);

            JpaPricing jpaPricing = JpaPricing.builder()
                    .discountTerms(discountTerms)
                    .build();

            when(jpaPricingRepository.findByCompanyId(companyId)).thenReturn(jpaPricing);

            pricingService.getDiscountTerms(companyId);

            verify(discountTermMapper, times(1)).map(discountTerms);
        }
    }

    @Nested
    class getCompanyBillingCurrency {
        @Test
        void when_JpaPricingNull_thenReturnsNull() {
            when(jpaPricingRepository.findByCompanyId(1L)).thenReturn(null);
            var result = pricingService.getCompanyBillingCurrency(1L);
            assertNull(result);
        }

        @Test
        void when_JpaPricingNotNull_thenReturns() {
            when(jpaPricingRepository.findByCompanyId(1L)).thenReturn(JpaPricing.builder().billingCurrencyCode(CurrencyCode.INR).build());
            var result = pricingService.getCompanyBillingCurrency(1L);
            assertEquals(CurrencyCode.INR, result);
        }
    }

    @Nested
    class EmployeePricingTest {
        @Test
        void create_pricing_with_multiple_contract_type_pricing() {
            PricingInput pricingInput = PricingInput.newBuilder()
                    .discountTerms(List.of())
                    .employeePricing(
                            List.of(
                                    CompanyEmployeePricingInput.newBuilder()
                                            .employeeType(ContractType.EMPLOYEE).fixedRate(600.0).country(CountryCode.PHL)
                                            .build(),
                                    CompanyEmployeePricingInput.newBuilder()
                                            .employeeType(ContractType.CONTRACTOR).fixedRate(1000.0).country(CountryCode.PHL)
                                            .build(),
                                    CompanyEmployeePricingInput.newBuilder()
                                            .employeeType(ContractType.EMPLOYEE).fixedRate(500.0).country(CountryCode.VNM)
                                            .build(),
                                    CompanyEmployeePricingInput.newBuilder()
                                            .employeeType(ContractType.CONTRACTOR).fixedRate(600.0).country(CountryCode.VNM)
                                            .build()
                            )
                    )
                    .build();
            JpaPricing jpaPricing = JpaPricing.builder()
                    .id(1L)
                    .build();

            when(pricingMapper.getDiscountTermFromInput(anyList())).thenReturn(List.of());
            when(pricingMapper.mapPricing(any(), any(), anyList())).thenReturn(jpaPricing);
            when(jpaPricingRepository.save(any())).thenReturn(jpaPricing);
            when(jpaEmployeePricingRepository.findByPricingIdForExceptionalCountries(1L)).thenReturn(List.of(
                    JpaEmployeePricing.builder()
                            .id(1L)
                            .country(CountryCode.PHL).type(PriceType.COUNTRY).employeeType(ContractType.EMPLOYEE).fixedRate(100.0)
                            .build(),
                    JpaEmployeePricing.builder()
                            .id(2L)
                            .country(CountryCode.PHL).type(PriceType.COUNTRY).employeeType(ContractType.CONTRACTOR).fixedRate(200.0)
                            .build(),
                    JpaEmployeePricing.builder()
                            .id(3L)
                            .country(CountryCode.PHL).type(PriceType.COUNTRY).employeeType(ContractType.FREELANCER).fixedRate(300.0)
                            .build(),
                    JpaEmployeePricing.builder()
                            .id(4L)
                            .country(CountryCode.VNM).type(PriceType.COUNTRY).employeeType(ContractType.EMPLOYEE).fixedRate(400.0)
                            .build()
            ));

            pricingService.createPricing(pricingInput, 1000L);

            ArgumentCaptor<List<JpaEmployeePricing>> captor = ArgumentCaptor.captor();
            verify(jpaEmployeePricingRepository, times(2)).saveAll(captor.capture());
            List<JpaEmployeePricing> captured = captor.getAllValues().get(0);
            assertEquals(4, captured.size());
            // PHL - FREELANCER is deleted
            assertThat(captured.stream()
                    .filter(jpaEmployeePricing -> jpaEmployeePricing.getId() != null && jpaEmployeePricing.getId().equals(3L))
                    .count())
                    .isEqualTo(0);
            // new VNM - CONTRACTOR is added
            var vnmContractOptional = captured.stream().filter(jpaEmployeePricing -> jpaEmployeePricing.getId() == null).findAny();
            assertThat(vnmContractOptional).isPresent();
            var vnmContract = vnmContractOptional.get();
            assertThat(vnmContract.getId()).isNull();
            assertThat(vnmContract.getCountry()).isEqualTo(CountryCode.VNM);
            assertThat(vnmContract.getEmployeeType()).isEqualTo(ContractType.CONTRACTOR);
            assertThat(vnmContract.getFixedRate()).isEqualTo(600.0);
            var contractId1Optional = captured.stream().filter(jpaEmployeePricing -> 1L == jpaEmployeePricing.getId()).findAny();
            assertThat(contractId1Optional).isPresent();
            var contractId1 = contractId1Optional.get();
            assertThat(contractId1.getId()).isEqualTo(1L);
            assertThat(contractId1.getCountry()).isEqualTo(CountryCode.PHL);
            assertThat(contractId1.getEmployeeType()).isEqualTo(ContractType.EMPLOYEE);
            assertThat(contractId1.getFixedRate()).isEqualTo(600.0);

            var contractId2Optional = captured.stream().filter(jpaEmployeePricing -> 2L == jpaEmployeePricing.getId()).findAny();
            assertThat(contractId2Optional).isPresent();
            var contractId2 = contractId2Optional.get();
            assertThat(contractId2.getId()).isEqualTo(2L);
            assertThat(contractId2.getCountry()).isEqualTo(CountryCode.PHL);
            assertThat(contractId2.getEmployeeType()).isEqualTo(ContractType.CONTRACTOR);
            assertThat(contractId2.getFixedRate()).isEqualTo(1000.0);

            var contractId4Optional = captured.stream().filter(jpaEmployeePricing -> 4L == jpaEmployeePricing.getId()).findAny();
            assertThat(contractId4Optional).isPresent();
            var contractId4 = contractId4Optional.get();
            assertThat(contractId4.getId()).isEqualTo(4L);
            assertThat(contractId4.getCountry()).isEqualTo(CountryCode.VNM);
            assertThat(contractId4.getEmployeeType()).isEqualTo(ContractType.EMPLOYEE);
            assertThat(contractId4.getFixedRate()).isEqualTo(500.0);
        }
    }

    @Nested
    class VisaPricingTest {

        @Test
        void create_pricing_with_visa_pricing() {
            PricingInput pricingInput = PricingInput.newBuilder()
                    .discountTerms(List.of())
                    .visaPricing(List.of(
                            CompanyVisaPricingInput.newBuilder()
                                    .employeeType(ContractType.EMPLOYEE)
                                    .fixedRate(600.0)
                                    .country(CountryCode.PHL)
                                    .validUntil(LocalDate.of(2023, 01, 01))
                                    .build(),
                            CompanyVisaPricingInput.newBuilder()
                                    .employeeType(ContractType.EMPLOYEE)
                                    .fixedRate(200.0)
                                    .country(CountryCode.PHL)
                                    .build()))
                    .build();

            JpaPricing jpaPricing = JpaPricing.builder()
                    .id(1L)
                    .build();

            when(pricingMapper.getDiscountTermFromInput(anyList())).thenReturn(List.of());
            when(pricingMapper.mapPricing(any(), any(), anyList())).thenReturn(jpaPricing);
            when(jpaPricingRepository.save(any())).thenReturn(jpaPricing);
            when(jpaEmployeePricingRepository.findByPricingIdForExceptionalCountries(any())).thenReturn(List.of());
            when(jpaEmployeePricingRepository.findByPricingIdAndAndType(any(), eq(PriceType.VISA))).thenReturn(List.of());

            pricingService.createPricing(pricingInput, 1000L);

            verify(jpaEmployeePricingRepository, times(2)).saveAll(any());
        }

        @Test
        void update_pricing_with_visa_pricing() {
            // TODO Unit-test
        }

    }

    @Nested
    class UpdateCompanyPricing {
        PricingInput pricingInput = PricingInput.newBuilder()
                .discountTerms(List.of())
                .visaPricing(List.of(
                        CompanyVisaPricingInput.newBuilder()
                                .employeeType(ContractType.EMPLOYEE)
                                .fixedRate(600.0)
                                .country(CountryCode.PHL)
                                .validUntil(LocalDate.of(2023, 01, 01))
                                .build(),
                        CompanyVisaPricingInput.newBuilder()
                                .employeeType(ContractType.EMPLOYEE)
                                .fixedRate(200.0)
                                .country(CountryCode.PHL)
                                .build()))
                .build();

        long COMPANY_ID = 1000L;

        JpaPricing existingPricing =
                JpaPricing.builder().id(1L).companyId(COMPANY_ID).build();

        @BeforeEach
        void setUp() {
            lenient().when(jpaPricingRepository.save(any())).thenReturn(existingPricing);
            lenient().when(pricingMapper.mapPricing(any(), any(), anyList())).thenReturn(existingPricing);
            lenient().when(pricingMapper.mapFromJpa(any())).thenReturn(new Pricing());
            pricingService.setCompanyServiceAdapter(companyServiceAdapter);
        }

        @Nested
        class WhenCompanyDontHavePricing {
            @BeforeEach
            void setup() {
            }

            @Test
            void if_company_have_sfdcAccountNo_then_not_raising_error_when_update_pricing() {
                // TODO Unit-test
            }

            @Test
            void if_company_does_not_have_sfdcAccountNo_then_raising_error_when_update_pricing() {
                // TODO Unit-test
            }
        }
    }
}
