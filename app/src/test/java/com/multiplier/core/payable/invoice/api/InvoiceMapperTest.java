package com.multiplier.core.payable.invoice.api;

import com.netsuite.suitetalk.proxy.v2023_1.platform.core.BooleanCustomFieldRef;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.CustomFieldList;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.CustomFieldRef;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.RecordRef;
import com.netsuite.suitetalk.proxy.v2023_1.transactions.sales.Invoice;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InvoiceMapperTest {

    @Mock
    private InvoiceStatusMapper invoiceStatusMapper;

    @InjectMocks
    private InvoiceMapper mapper;

    @Test
    void givenRequest_whenMap_thenReturnInvoice() {
        // GIVEN
        var status = InvoiceStatus.PENDING;
        var recordRef = mock(RecordRef.class);
        when(invoiceStatusMapper.map(status))
                .thenReturn(recordRef);

        var invoiceId = "awesomeId";
        var request = UpdateInvoiceApiRequest.builder()
                .status(status)
                .sendEmail(true)
                .build();

        var expectedInvoice = new Invoice();
        expectedInvoice.setInternalId(invoiceId);
        expectedInvoice.setApprovalStatus(recordRef);

        var customFieldList = getCustomFieldList(request);
        expectedInvoice.setCustomFieldList(customFieldList);

        // WHEN
        var invoice = mapper.map(invoiceId, request);

        // THEN
        assertThat(invoice).isEqualTo(expectedInvoice);
    }

    private CustomFieldList getCustomFieldList(final UpdateInvoiceApiRequest request) {
        var platformApproved = request.getSendEmail();
        var booleanCustomFieldRef = new BooleanCustomFieldRef(null, "custbody_multi_platform_approve", platformApproved);
        var customFieldRef = new CustomFieldRef[]{booleanCustomFieldRef};
        var customFieldList = new CustomFieldList();
        customFieldList.setCustomField(customFieldRef);
        return customFieldList;
    }

    @Test
    void givenRequestWithNullValues_whenMap_thenReturnInvoice() {
        // GIVEN
        var invoiceId = "awesomeId";
        var request = UpdateInvoiceApiRequest.builder()
                .build();

        var expectedInvoice = new Invoice();
        expectedInvoice.setInternalId(invoiceId);

        // WHEN
        var invoice = mapper.map(invoiceId, request);

        // THEN
        assertThat(invoice).isEqualTo(expectedInvoice);
    }

}