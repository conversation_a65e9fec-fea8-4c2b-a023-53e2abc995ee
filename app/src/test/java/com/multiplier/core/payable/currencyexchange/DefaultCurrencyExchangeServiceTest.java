package com.multiplier.core.payable.currencyexchange;

import com.multiplier.core.payable.adapters.CurrencyServiceAdapter;
import com.multiplier.core.payable.service.exception.CurrencyConversionException;
import com.multiplier.core.schema.currency.Currency;
import com.multiplier.payable.types.CurrencyCode;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DefaultCurrencyExchangeServiceTest {

    @Mock
    private CurrencyServiceAdapter currencyServiceAdapter;

    @InjectMocks
    private DefaultCurrencyExchangeService defaultCurrencyExchangeService;

    @Test
    void givenAmount_whenExchange_thenReturnExchangedAmount() {
        // GIVEN
        var amount = 42d;
        var fromCurrencyCode = CurrencyCode.USD;
        var toCurrencyCode = CurrencyCode.GBP;
        var amountInGbp = 666d;

        when(currencyServiceAdapter.getCurrencyExchangeAmount(BigDecimal.valueOf(amount),
                fromCurrencyCode, toCurrencyCode, Currency.CurrencyConversionSource.INTERNALLY_FIXED))
                .thenReturn(BigDecimal.valueOf(amountInGbp));

        // WHEN
        var exchangedAmount = defaultCurrencyExchangeService.exchange(BigDecimal.valueOf(amount),
                fromCurrencyCode, toCurrencyCode);

        // THEN
        assertEquals(amountInGbp, exchangedAmount.doubleValue());
    }

    @Test
    void givenCurrencyServiceAdapterThrowsException_whenExchange_thenReturnExchangedAmountOnDemand() {
        // GIVEN
        var amount = 42d;
        var fromCurrencyCode = CurrencyCode.USD;
        var toCurrencyCode = CurrencyCode.GBP;
        var amountInGbp = 666d;

        when(currencyServiceAdapter.getCurrencyExchangeAmount(BigDecimal.valueOf(amount),
                fromCurrencyCode, toCurrencyCode, Currency.CurrencyConversionSource.INTERNALLY_FIXED))
                .thenThrow(new CurrencyConversionException("whatever"));

        when(currencyServiceAdapter.getCurrencyExchangeAmount(BigDecimal.valueOf(amount),
                fromCurrencyCode, toCurrencyCode, Currency.CurrencyConversionSource.ON_DEMAND))
                .thenReturn(BigDecimal.valueOf(amountInGbp));

        // WHEN
        var exchangedAmount = defaultCurrencyExchangeService.exchange(BigDecimal.valueOf(amount),
                fromCurrencyCode, toCurrencyCode);

        // THEN
        assertEquals(amountInGbp, exchangedAmount.doubleValue());
    }

    @Test
    void givenCurrencyServiceReturnsAFloatingNumber_whenExchange_thenReturnARoundedExchangedAmount() {
        // GIVEN
        var amount = 42d;
        var fromCurrencyCode = CurrencyCode.USD;
        var toCurrencyCode = CurrencyCode.GBP;
        var amountInGbp = 42.424242;
        var expectedAmountInGbp = 42.42;

        when(currencyServiceAdapter.getCurrencyExchangeAmount(BigDecimal.valueOf(amount),
                fromCurrencyCode, toCurrencyCode, Currency.CurrencyConversionSource.INTERNALLY_FIXED))
                .thenReturn(BigDecimal.valueOf(amountInGbp));

        // WHEN
        var exchangedAmount = defaultCurrencyExchangeService.exchange(BigDecimal.valueOf(amount),
                fromCurrencyCode, toCurrencyCode);

        // THEN
        assertEquals(expectedAmountInGbp, exchangedAmount.doubleValue());
    }
}