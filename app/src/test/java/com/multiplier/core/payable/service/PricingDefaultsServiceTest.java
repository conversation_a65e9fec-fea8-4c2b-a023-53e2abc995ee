package com.multiplier.core.payable.service;

import com.multiplier.core.payable.repository.model.EmployeeTypeGlobalPricing;
import com.multiplier.core.payable.repository.model.VisaTypeGlobalPricing;
import com.multiplier.core.payable.service.mapper.EmployeePricingMapper;
import com.multiplier.core.util.Pair;
import com.multiplier.payable.types.*;
import lombok.val;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static java.util.List.of;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PricingDefaultsServiceTest {

    @Mock
    private PricingFactory pricingFactory;
    @Mock
    private EmployeePricingMapper employeePricingMapper;
    @InjectMocks
    private PricingDefaultsService pricingDefaultsService;

    @Test
    void default_pricing_rule_test() {
        PricingDefaultsService service = new PricingDefaultsService(null, null);
        var outList = service.getDefaultDiscountTerms();
        assertEquals(0, outList.size());
    }

    @Test
    void default_visa_pricing_rule_test() {
        PricingDefaultsService service = new PricingDefaultsService(new PricingFactory(), null);
        var outList = service.getDefaultVisaGlobalPricingInput();
        assertEquals(1, outList.size());
    }

    @Test
    void defaultPricingForOffboarding() {

        val pricingDefaultServiceSply = Mockito.spy(pricingDefaultsService);

        doReturn(Collections.EMPTY_LIST).when(pricingFactory).getDefaultGlobalPricing();
        doReturn(Collections.EMPTY_LIST).when(pricingFactory).getDefaultVisaGlobalPricing();
        doReturn(Collections.EMPTY_LIST).when(employeePricingMapper).mapToInput(List.of());

        val pricingInput = pricingDefaultServiceSply.getDefaultPricingForCompany();

        assertEquals(OffboardingPricingService.getDefaultOffboardingGlobalPricingInput(),
                pricingInput.getOffboardingGlobalPricing());
    }

    @Test
    void should_get_default_pricings_for_visa() {
        var sgEmployeeVisaPricing = VisaEmployeePricing.newBuilder()
                .id(CountryCode.SGP.ordinal())
                .fixedRate(1200.0)
                .employeeType(ContractType.EMPLOYEE)
                .country(CountryCode.SGP)
                .build();

        var employeeVisaPricingDefaults = EmployeePricingDefault.newBuilder()
                .employeePricings(of(sgEmployeeVisaPricing))
                .globalPricing(Collections.emptyList())
                .build();

        var companyVisaPricingInputs = List.of(
                CompanyVisaPricingInput.newBuilder()
                        .fixedRate(1200.0)
                        .employeeType(ContractType.EMPLOYEE)
                        .country(CountryCode.SGP)
                        .build()
        );

        doReturn(Collections.EMPTY_LIST).when(pricingFactory).getDefaultGlobalPricing();
        doReturn(Collections.EMPTY_LIST).when(pricingFactory).getDefaultVisaGlobalPricing();
        when(pricingFactory.getCountryVisaPricing()).thenReturn(Map.of(
                Pair.of(CountryCode.SGP, ContractType.EMPLOYEE), 1200.0
        ));
        when(pricingFactory.getCountryPricing()).thenReturn(Map.of());
        doReturn(Collections.EMPTY_LIST).when(employeePricingMapper).mapToInput(List.of());
        doReturn(companyVisaPricingInputs).when(employeePricingMapper).mapToVisaInput(employeeVisaPricingDefaults.getEmployeePricings());

        val pricingInput = pricingDefaultsService.getDefaultPricingForCompany();

        assertNotNull(pricingInput);
    }

    @Test
    void defaultCountryVisaPricing() {
        var sgEmployeeVisaPricing = VisaEmployeePricing.newBuilder()
                .id(CountryCode.SGP.ordinal())
                .fixedRate(1200.0)
                .employeeType(ContractType.EMPLOYEE)
                .country(CountryCode.SGP)
                .build();

        var employeeVisaPricingDefaults = EmployeePricingDefault.newBuilder()
                .employeePricings(of(sgEmployeeVisaPricing))
                .globalPricing(Collections.emptyList())
                .build();

        when(pricingFactory.getCountryVisaPricing()).thenReturn(Map.of(
                Pair.of(CountryCode.SGP, ContractType.EMPLOYEE), 1200.0
        ));

        val pricingInput = pricingDefaultsService.getEmployeeVisaPricingDefaultCountries();

        assertEquals(employeeVisaPricingDefaults, pricingInput);
    }

    @Test
    void shouldGetDefaultPricings() {
        var expectedEmployPricings = List.of(
                CountryEmployeePricing.newBuilder()
                        .id(CountryCode.VEN.ordinal())
                        .country(CountryCode.VEN)
                        .employeeType(ContractType.EMPLOYEE)
                        .fixedRate(800.0)
                        .build(),
                CountryEmployeePricing.newBuilder()
                        .id(CountryCode.IND.ordinal())
                        .country(CountryCode.IND)
                        .employeeType(ContractType.EMPLOYEE)
                        .fixedRate(400.0)
                        .build()
        );
        var expectedVisaPricings = List.of(
                VisaEmployeePricing.newBuilder()
                        .id(CountryCode.SGP.ordinal())
                        .country(CountryCode.SGP)
                        .employeeType(ContractType.EMPLOYEE)
                        .fixedRate(1200.0)
                        .build(),
                VisaEmployeePricing.newBuilder()
                        .id(CountryCode.ARE.ordinal())
                        .country(CountryCode.ARE)
                        .employeeType(ContractType.EMPLOYEE)
                        .fixedRate(600.0)
                        .build()
        );
        var expectedGlobalPricings = List.of(
                GlobalPricing.newBuilder()
                        .employeeType(ContractType.EMPLOYEE)
                        .fixedRate(800.0)
                        .build(),
                GlobalPricing.newBuilder()
                        .employeeType(ContractType.FREELANCER)
                        .fixedRate(400.0)
                        .build(),
                GlobalPricing.newBuilder()
                        .employeeType(ContractType.CONTRACTOR)
                        .fixedRate(600.0)
                        .build()
        );
        var expectedVisaGlobalPricings = List.of(
                VisaGlobalPricing.newBuilder()
                        .employeeType(ContractType.EMPLOYEE)
                        .fixedRate(900.0)
                        .build()
        );

        when(pricingFactory.getCountryPricing()).thenReturn(
                Map.of(
                        Pair.of(CountryCode.VEN, ContractType.EMPLOYEE), 800.0,
                        Pair.of(CountryCode.IND, ContractType.EMPLOYEE), 400.0
                )
        );
        when(pricingFactory.getDefaultGlobalPricing()).thenReturn(
                List.of(
                        new EmployeeTypeGlobalPricing()
                                .employeeType(ContractType.EMPLOYEE)
                                .globalPrice(800.0),
                        new EmployeeTypeGlobalPricing()
                                .globalPrice(400.0)
                                .employeeType(ContractType.FREELANCER),
                        new EmployeeTypeGlobalPricing()
                                .globalPrice(600.0)
                                .employeeType(ContractType.CONTRACTOR)
                )
        );
        when(pricingFactory.getDefaultVisaGlobalPricing()).thenReturn(
                List.of(
                        new VisaTypeGlobalPricing()
                                .employeeType(ContractType.EMPLOYEE)
                                .globalPrice(900.0)
                )
        );
        when(pricingFactory.getCountryVisaPricing()).thenReturn(
                Map.of(
                        Pair.of(CountryCode.SGP, ContractType.EMPLOYEE), 1200.0,
                        Pair.of(CountryCode.ARE, ContractType.EMPLOYEE), 600.0
                )
        );

        val defaultPricings = pricingDefaultsService.getEmployeePricingDefaultCountries();
        assertThat(defaultPricings.getEmployeePricings()).containsExactlyInAnyOrderElementsOf(expectedEmployPricings);
        assertThat(defaultPricings.getVisaPricing()).containsExactlyInAnyOrderElementsOf(expectedVisaPricings);
        assertThat(defaultPricings.getGlobalPricing()).containsExactlyInAnyOrderElementsOf(expectedGlobalPricings);
        assertThat(defaultPricings.getVisaGlobalPricing()).containsExactlyInAnyOrderElementsOf(expectedVisaGlobalPricings);
    }

}