package com.multiplier.core.payable.report.collector;

import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.core.currency.CurrencyServiceV2;
import com.multiplier.core.currency.FxRateQuery;
import com.multiplier.core.payable.adapters.MemberServiceAdapter;
import com.multiplier.core.payable.report.dataholder.IsrActionInput;
import com.multiplier.core.payable.service.PricingService;
import com.multiplier.core.payable.service.dataholder.SourceReportData;
import com.multiplier.core.util.dto.payroll.CompanyMemberPayWrapper;
import com.multiplier.member.schema.Member;
import com.multiplier.payable.types.CurrencyCode;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class IsrSalaryTypeDataCollectorV2Test {

    private final IsrDataCollector isrSalaryTypeDataCollector = mock();

    private final PricingService pricingService = mock();

    private final CurrencyServiceV2 currencyServiceV2 = mock();

    private final MemberServiceAdapter memberServiceAdapter = mock();

    private final IsrSalaryTypeDataCollectorV2 isrSalaryTypeDataCollectorV2 = new IsrSalaryTypeDataCollectorV2(isrSalaryTypeDataCollector, pricingService, currencyServiceV2, memberServiceAdapter, new PerformanceMonitor());

    @Test
    void should_collect_and_enrich() {
        var mockedContract = mock(ContractOuterClass.Contract.class);
        when(mockedContract.getMemberId()).thenReturn(1L);
        var mockedMember = mock(Member.class);
        when(mockedMember.getId()).thenReturn(1L);
        var mockedMemberPay = mock(CompanyMemberPayWrapper.class);
        when(mockedMemberPay.getCurrency()).thenReturn(CurrencyCode.SGD);
        var sourceReportData = SourceReportData.builder()
                .contract(mockedContract)
                .companyId(1L)
                .companyMemberPayWrapper(mockedMemberPay)
                .build();
        int invoiceType = 2;
        Long companyId = 1L;
        int month = 8;
        int year = 2024;
        var isrActionInput = IsrActionInput.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .invoiceType(invoiceType)
                .build();
        when(isrSalaryTypeDataCollector.collect(isrActionInput)).thenReturn(
                List.of(
                        sourceReportData
                )
        );
        when(pricingService.getBillingCurrencyCode(companyId)).thenReturn(CurrencyCode.USD);
        when(memberServiceAdapter.getMembers(List.of(1L))).thenReturn(List.of(mockedMember));
        when(currencyServiceV2.getFxRates(
                Set.of(new FxRateQuery(1L, CurrencyCode.SGD, CurrencyCode.USD)))
        ).thenReturn(Map.of(new FxRateQuery(1L, CurrencyCode.SGD, CurrencyCode.USD), 0.83));

        var result = isrSalaryTypeDataCollectorV2.collect(isrActionInput);

        assertThat(result).isNotEmpty().hasSize(1);
        var enrichedRow = result.get(0);
        assertThat(enrichedRow.getContract()).isEqualTo(mockedContract);
        assertThat(enrichedRow.getExchangeRate()).isEqualTo(0.83);
        assertThat(enrichedRow.getMember()).isEqualTo(mockedMember);
        assertThat(enrichedRow.getCompanyBillingCurrency()).isEqualTo(CurrencyCode.USD);
    }

}