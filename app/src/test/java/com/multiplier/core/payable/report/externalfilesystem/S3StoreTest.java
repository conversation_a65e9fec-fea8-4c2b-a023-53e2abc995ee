package com.multiplier.core.payable.report.externalfilesystem;

import com.multiplier.core.payable.report.externalfilesystem.FileStoreRequest;
import com.multiplier.core.payable.report.externalfilesystem.S3Store;
import com.multiplier.core.payable.report.externalfilesystem.properties.S3Configuration;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@Slf4j
@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class S3StoreTest {
    @Mock
    private S3Client s3Client;

    @Mock
    private S3Configuration s3Configuration;

    @InjectMocks
    private S3Store s3Store;

    @Test
    void upload_with_content_type() throws IOException {
        // Arrange
        FileStoreRequest request = FileStoreRequest.of("fileUid", "fileName.txt").setPath("mypath").setContentType("text/plain")
                .setData(new ByteArrayInputStream("Hello, World!".getBytes()));
        PutObjectRequest expectedObjectRequest = PutObjectRequest.builder()
                .bucket("dummy-bucket")
                .key("mypath/fileUid")
                .build();
        // Configure the mock S3Configuration to return a dummy bucket name
        when(s3Configuration.getBucket()).thenReturn("dummy-bucket");

        // Act
        s3Store.upload(request);

        // Assert
        verify(s3Client).putObject(eq(expectedObjectRequest), any(RequestBody.class));
    }

    @Test
    void upload_throws_unsupported_operation_if_s3_throws_io_exception() throws IOException {
        // GIVEN
        // Mock an InputStream that throws IOException
        InputStream mockInputStream = mock(InputStream.class);
        when(mockInputStream.readAllBytes()).thenThrow(new IOException("Test IOException"));
        FileStoreRequest fileStoreRequest = FileStoreRequest.of("fileUid", "fileName.txt")
                .setPath("mypath")
                .setData(mockInputStream);
        when(s3Configuration.getBucket()).thenReturn("dummy-bucket");

        // THEN
        assertThrows(RuntimeException.class, () -> s3Store.upload(fileStoreRequest));
    }
}