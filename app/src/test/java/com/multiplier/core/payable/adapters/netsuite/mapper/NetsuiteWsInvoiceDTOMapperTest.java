package com.multiplier.core.payable.adapters.netsuite.mapper;

import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.adapters.netsuite.client.NetsuiteMappings;
import com.multiplier.payable.types.InvoiceStatus;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.CustomFieldRef;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.LongCustomFieldRef;
import com.netsuite.suitetalk.proxy.v2023_1.platform.core.StringCustomFieldRef;
import com.netsuite.suitetalk.proxy.v2023_1.transactions.sales.Invoice;
import com.netsuite.suitetalk.proxy.v2023_1.transactions.sales.InvoiceItem;
import com.netsuite.suitetalk.proxy.v2023_1.transactions.sales.InvoiceItemList;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;

class NetsuiteWsInvoiceDTOMapperTest {
    static {
        new NetsuiteMappings("test");
    }

    NetsuiteWsInvoiceDTOMapper mapper = Mappers.getMapper(NetsuiteWsInvoiceDTOMapper.class);

    @Test
    void givenInvoice_whenMap_thenValuesAreEqual() {
        // GIVEN
        var invoice = new Invoice();
        invoice.setTotal(42d);
        invoice.setEntity(new com.netsuite.suitetalk.proxy.v2023_1.platform.core.RecordRef("", "100", null, null));
        // TODO Set other fields
        var invoiceItemList = new InvoiceItemList();
        var invoiceItem = new InvoiceItem();
        invoiceItem.setItem(new com.netsuite.suitetalk.proxy.v2023_1.platform.core.RecordRef("", "626", null, null));
        invoiceItem.setRate("10.0");
        var customFieldList = new com.netsuite.suitetalk.proxy.v2023_1.platform.core.CustomFieldList();
        var customFields = new ArrayList<CustomFieldRef>();
        var contractIdCustomField = new LongCustomFieldRef(null, "custcol_contract_id", 1L);
        customFields.add(contractIdCustomField);
        var baseCurrencyCustomField = new StringCustomFieldRef(null, "custcol_mlt_base_currency", "SGD");
        customFields.add(baseCurrencyCustomField);
        var amountInBaseCurrencyCustomField = new StringCustomFieldRef(null, "custcol_mlt_amount_base_currency", "42.0");
        customFields.add(amountInBaseCurrencyCustomField);
        customFieldList.setCustomField(customFields.toArray(new CustomFieldRef[0]));

        invoiceItem.setCustomFieldList(customFieldList);
        invoiceItemList.setItem(new InvoiceItem[]{invoiceItem});
        invoice.setItemList(invoiceItemList);

        // WHEN
        var dto = mapper.map(invoice);

        // THEN
        assertEquals(invoice.getTotal(), dto.getTotalAmount());
        assertEquals("100", dto.getCustomerId());
        var firstItem = dto.getLineItems().get(0);
        assertEquals(Double.parseDouble(invoiceItem.getRate()), firstItem.getUnitAmount());
        assertEquals(1L, firstItem.getContractId());
        assertEquals("SGD", firstItem.getBaseCurrency());
        assertEquals(42.0, firstItem.getAmountInBaseCurrency());
        assertEquals(LineItemType.GROSS_SALARY, firstItem.getItemType());
    }

    @ParameterizedTest
    @MethodSource("fromStatus")
    void givenStatus_whenMapStatusToInvoiceStatus_thenValuesAreEqual(String status, InvoiceStatus expected) {
        // GIVEN
        // WHEN
        var result = mapper.mapStatusToInvoiceStatus(status);
        // THEN
        assertEquals(expected, result);
    }

    private static Stream<Arguments> fromStatus() {
        return Stream.of(
                Arguments.of("Pending Approval", InvoiceStatus.DRAFT),
                Arguments.of("DRAFT", InvoiceStatus.DRAFT),
                Arguments.of("Open", InvoiceStatus.AUTHORIZED),
                Arguments.of("AUTHORIZED", InvoiceStatus.AUTHORIZED),
                Arguments.of("Approved", InvoiceStatus.AUTHORIZED),
                Arguments.of("Paid", InvoiceStatus.PAID),
                Arguments.of("PAID", InvoiceStatus.PAID),
                Arguments.of("Paid in Full", InvoiceStatus.PAID),
                Arguments.of("Rejected", InvoiceStatus.VOIDED),
                Arguments.of("VOIDED", InvoiceStatus.VOIDED),
                Arguments.of("SUBMITTED", InvoiceStatus.SUBMITTED),
                Arguments.of("DELETED", InvoiceStatus.DELETED),
                Arguments.of("PENDING", InvoiceStatus.PENDING),
                Arguments.of("OVERDUE", InvoiceStatus.OVERDUE)
        );
    }
}