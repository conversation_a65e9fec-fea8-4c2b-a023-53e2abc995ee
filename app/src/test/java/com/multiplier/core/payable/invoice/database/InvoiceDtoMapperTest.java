package com.multiplier.core.payable.invoice.database;

import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.event.database.RecordType;
import com.multiplier.core.payable.repository.model.*;
import com.multiplier.payable.types.CurrencyCode;
import com.multiplier.payable.types.InvoiceStatus;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

class InvoiceDtoMapperTest {

    private final InvoiceDtoMapper mapper = Mappers.getMapper(InvoiceDtoMapper.class);

    @Test
    void givenJpaInvoice_whenMap_thenValuesAreEqual() {
        // GIVEN
        var now = LocalDate.now().atStartOfDay();
        var invoice = JpaInvoice.builder()
                .id(42L)
                .externalId("awesomeExternalInvoiceId")
                .invoiceNo("awesomeInvoiceNo")
                .createdDate(now)
                .dueDate(now)
                .fullyPaidOnDate(now)
                .reference("Ref123")
                .status(InvoiceStatus.AUTHORIZED)
                .lineItems(List.of(
                        JpaInvoiceLineItem.builder()
                                .build()
                ))
                .amountPaid(BigDecimal.valueOf(42d))
                .amountDue(BigDecimal.valueOf(42d))
                .companyPayable(JpaCompanyPayable.builder()
                        .id(42L)
                        .totalAmount(42d)
                        .build())
                .externalSystem(ExternalSystem.NETSUITE)
                .syncedTime(now)
                .emailSent(true)
                .externalInvoiceGenerated(true)
                .errorReason("awesomeErrorReason")
                .type(InvoiceType.DEPOSIT)
                .recordType(RecordType.INVOICE)
                .createdBy(42L)
                .createdOn(LocalDateTime.of(2023, 9, 21, 0, 0, 0))
                .updatedBy(42L)
                .updatedOn(LocalDateTime.of(2023, 9, 21, 0, 0, 0))
                .totalAmount(new BigDecimal("42.0"))
                .build();

        // WHEN
        var dto = mapper.map(invoice);

        // THEN
        assertEquals(invoice.getExternalId(), dto.getExternalId());
        assertEquals(invoice.getInvoiceNo(), dto.getInvoiceNo());
        assertEquals(invoice.getCreatedDate(), dto.getDate().atStartOfDay());
        assertEquals(invoice.getDueDate().toLocalDate(), dto.getDueDate());
        assertEquals(invoice.getFullyPaidOnDate().toLocalDate(), dto.getFullyPaidOnDate());
        assertEquals(invoice.getReference(), dto.getReference());
        assertEquals(invoice.getStatus(), dto.getStatus());
        assertEquals(invoice.getAmountPaid().doubleValue(), dto.getAmountPaid());
        assertEquals(invoice.getAmountDue().doubleValue(), dto.getAmountDue());
        assertEquals(invoice.getCompanyPayable().getTotalAmount().doubleValue(), dto.getTotalAmount());
        assertEquals(invoice.getExternalSystem(), dto.getExternalSystem());
        assertEquals(invoice.getCompanyPayable().getId(), dto.getCompanyPayableId());
        assertEquals(invoice.getCompanyPayable().getCompanyId(), dto.getCompanyId());
        assertEquals(invoice.getSyncedTime(), dto.getSyncedTime());
        assertEquals(invoice.getEmailSent(), dto.getEmailSent());
        assertEquals(invoice.getExternalInvoiceGenerated(), dto.getExternalInvoiceGenerated());
        assertEquals(invoice.getErrorReason(), dto.getErrorReason());
        assertEquals(invoice.getType(), dto.getType());
        assertEquals(invoice.getRecordType(), dto.getRecordType());
        assertEquals(invoice.getCreatedBy(), dto.getCreatedBy());
        assertEquals(invoice.getCreatedOn(), dto.getCreatedOn());
        assertEquals(invoice.getUpdatedBy(), dto.getUpdatedBy());
        assertEquals(invoice.getUpdatedOn(), dto.getUpdatedOn());
        var item = invoice.getLineItems().get(0);
        var itemDto = dto.getLineItems().get(0);
        assertEquals(item.getDescription(), itemDto.getDescription());
        assertEquals(item.getQuantity(), itemDto.getQuantity());
        assertEquals(item.getUnitPrice(), itemDto.getUnitAmount());
        assertEquals(item.getAccount(), itemDto.getAccountCode());
        assertEquals(item.getTaxType(), itemDto.getTaxType());
        assertEquals(item.getTaxCode(), itemDto.getTaxCode());
        assertEquals(item.getTaxAmount(), itemDto.getTaxAmount());
        assertEquals(item.getTaxRate(), itemDto.getTaxRate());
        assertEquals(item.getContractId(), itemDto.getContractId());
        assertEquals(item.getMemberName(), itemDto.getMemberName());
        assertEquals(item.getItemType(), itemDto.getItemType());
        assertEquals(item.getBaseCurrency(), itemDto.getBaseCurrency());
        assertEquals(item.getAmountInBaseCurrency(), itemDto.getAmountInBaseCurrency());
        assertEquals(item.getClassValue(), itemDto.getClassValue());
        assertEquals(item.getClassDisplay(), itemDto.getClassDisplay());
        assertEquals(item.getCountryName(), itemDto.getCountryName());
        assertEquals(item.getGrossAmount(), itemDto.getGrossAmount());
    }

    // Since we're already calling the jpaInvoice-only mapper for baseDTO then enrich, we don't have to verify other fields mapping
    @Test
    void givenCurrencyCodeAndJpaInvoice_whenMap_thenReturnCorrectInvoiceDTO() {
        // Given
        var currencyCode = CurrencyCode.USD;
        var jpaInvoice = JpaInvoice.builder()
                .build();

        // When
        InvoiceDTO result = mapper.map(currencyCode, jpaInvoice);

        // Then
        assertEquals(CurrencyCode.USD, result.getBillingCurrencyCode());
    }

    @Test
    void givenListsOfJpaCompanyPayablesAndJpaInvoices_whenMap_thenReturnListOfCorrectInvoiceDTOs() {
        // Given
        var jpaCompanyPayable1 = JpaCompanyPayable.builder()
                .id(1L)
                .currency(CurrencyCode.USD).build();

        var jpaCompanyPayable2 = JpaCompanyPayable.builder()
                .id(2L)
                .currency(CurrencyCode.EUR).build();

        // some sample fields for mapping test
        var jpaInvoice1 = JpaInvoice.builder()
                .companyPayable(jpaCompanyPayable1)
                .amountPaid(BigDecimal.valueOf(100))
                .amountDue(BigDecimal.valueOf(50))
                .build();

        var jpaInvoice2 = JpaInvoice.builder()
                .companyPayable(jpaCompanyPayable2)
                .amountPaid(BigDecimal.valueOf(200))
                .amountDue(BigDecimal.valueOf(100))
                .build();

        // When
        List<InvoiceDTO> result = mapper.map(
                List.of(jpaCompanyPayable1, jpaCompanyPayable2),
                List.of(jpaInvoice1, jpaInvoice2)
        );

        // Then
        assertEquals(2, result.size());
        assertEquals(CurrencyCode.USD, result.get(0).getBillingCurrencyCode());
        assertEquals(100.0, result.get(0).getAmountPaid());
        assertEquals(50.0, result.get(0).getAmountDue());

        assertEquals(CurrencyCode.EUR, result.get(1).getBillingCurrencyCode());
        assertEquals(200.0, result.get(1).getAmountPaid());
        assertEquals(100.0, result.get(1).getAmountDue());
    }
}
