package com.multiplier.core.payable.service.lineitem;

import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.company.ManagementFee;
import com.multiplier.core.payable.repository.model.JpaInvoiceLineItem;
import com.multiplier.core.util.dto.payroll.CompanyMemberPayWrapper;
import com.multiplier.payable.types.Contract;
import com.multiplier.payable.types.CountryCode;
import com.multiplier.payable.types.CurrencyCode;
import com.multiplier.payable.types.PayFrequency;
import com.multiplier.payroll.schema.Payroll;
import com.multiplier.schema.common.Common;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mapstruct.factory.Mappers;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ProcessedLineItemMapperTest {

    private final ProcessedLineItemMapper mapper = Mappers.getMapper(ProcessedLineItemMapper.class);

    @Test
    void test_invoice_item_mapper() {
        //given
        var jpaInvoiceItem = JpaInvoiceLineItem.builder()
                .amountInBaseCurrency(2000.0)
                .unitPrice(1000.0)
                .baseCurrency("PHP")
                .contractId(1L)
                .countryName("Philippines")
                .description("awesome description")
                .itemType(LineItemType.GROSS_SALARY)
                .memberName("awesome name")
                .taxType("tax_type")
                .build();

        var metadata = MetadataItem.builder()
                .contractCountryCode(CountryCode.PHL)
                .billingCurrency(CurrencyCode.USD)
                .build();

        //when
        var result = mapper.mapFromInvoiceItem(jpaInvoiceItem, metadata, "MTPLINV01");

        //then
        assertEquals(2000.0, result.getAmountInBaseCurrency());
        assertEquals(1000.0, result.getAmountInBillingCurrency());
        assertEquals(jpaInvoiceItem.getBaseCurrency(), result.getBaseCurrency().name());
        assertEquals(CurrencyCode.USD, result.getBillingCurrency());
        assertEquals(jpaInvoiceItem.getContractId(), result.getContractId());
        assertEquals(CountryCode.PHL, result.getCountryCode());
        assertEquals(jpaInvoiceItem.getCountryName(), result.getCountryName());
        assertEquals(jpaInvoiceItem.getDescription(), result.getDescription());
        assertEquals(jpaInvoiceItem.getItemType(), result.getItemType());
        assertEquals("awesome name", result.getMemberName());
        assertEquals(jpaInvoiceItem.getTaxType(), result.getTaxType());
        assertEquals("MTPLINV01", result.getInvoiceNo());
        assertEquals("tax_type", result.getTaxType());
        assertTrue(result.isBilledAlready());
    }

    @ParameterizedTest
    @CsvSource(value = {
            "MONTHLY, Payroll Cost : PHP 1000.00",
            "SEMIMONTHLY, Payroll Cost (1) : PHP 1000.00"
    })
    void test_payroll_mapper(PayFrequency payFrequency, String expectedDescription) {
        //given
        var startDate = LocalDate.of(2024, 1, 1);
        var endDate = LocalDate.of(2024, 1, 31);
        var memberPay = buildMemberPay(CurrencyCode.PHP, 1, startDate, endDate);

        var metadataItem = MetadataItem.builder()
                .contractId(1L)
                .billingCurrency(CurrencyCode.USD)
                .contractCountryCode(CountryCode.PHL)
                .contractCountryName("Philippines")
                .memberName("awesome name")
                .payFrequency(payFrequency)
                .build();

        //when
        var result = mapper.mapFromPayroll(memberPay, metadataItem, 1000.0, 2000.0, "tax_type");

        //then
        assertEquals(1000.0, result.getAmountInBaseCurrency());
        assertEquals(2000.0, result.getAmountInBillingCurrency());
        assertEquals(memberPay.getCurrency(), result.getBaseCurrency());
        assertEquals(CurrencyCode.USD, result.getBillingCurrency());
        assertEquals(metadataItem.getContractId(), result.getContractId());
        assertEquals(metadataItem.getContractCountryCode(), result.getCountryCode());
        assertEquals(metadataItem.getContractCountryName(), result.getCountryName());
        assertEquals(expectedDescription, result.getDescription());
        assertEquals(LineItemType.EOR_SALARY_DISBURSEMENT, result.getItemType());
        assertEquals(LineItemCategory.PAYROLL, result.getItemCategory());
        assertEquals("awesome name", result.getMemberName());
        assertEquals("tax_type", result.getTaxType());
        assertEquals(startDate, result.getStartPayCycleDate());
        assertEquals(endDate, result.getEndPayCycleDate());
        assertEquals(100L, result.getPayrollCycleId());
        assertFalse(result.isBilledAlready());
    }

    @ParameterizedTest
    @CsvSource(value = {
            "MONTHLY, Total Expenses : PHP 1000.00",
            "SEMIMONTHLY, Total Expenses (1) : PHP 1000.00"
    })
    void test_expense_mapper(PayFrequency payFrequency, String expectedDescription) {
        //given
        var startDate = LocalDate.of(2024, 1, 1);
        var endDate = LocalDate.of(2024, 1, 31);
        var memberPay = buildMemberPay(CurrencyCode.PHP, 1, startDate, endDate);

        var metadataItem = MetadataItem.builder()
                .contractId(1L)
                .billingCurrency(CurrencyCode.USD)
                .contractCountryCode(CountryCode.PHL)
                .contractCountryName("Philippines")
                .memberName("awesome name")
                .payFrequency(payFrequency)
                .build();

        //when
        var result = mapper.mapFromExpense(memberPay, metadataItem, 1000.0, 2000.0, "tax_type");

        //then
        assertEquals(1000.0, result.getAmountInBaseCurrency());
        assertEquals(2000.0, result.getAmountInBillingCurrency());
        assertEquals(memberPay.getCurrency(), result.getBaseCurrency());
        assertEquals(CurrencyCode.USD, result.getBillingCurrency());
        assertEquals(metadataItem.getContractId(), result.getContractId());
        assertEquals(metadataItem.getContractCountryCode(), result.getCountryCode());
        assertEquals(metadataItem.getContractCountryName(), result.getCountryName());
        assertEquals(expectedDescription, result.getDescription());
        assertEquals(LineItemType.EOR_SALARY_DISBURSEMENT, result.getItemType());
        assertEquals(LineItemCategory.EXPENSE, result.getItemCategory());
        assertEquals("awesome name", result.getMemberName());
        assertEquals("tax_type", result.getTaxType());
        assertEquals(startDate, result.getStartPayCycleDate());
        assertEquals(endDate, result.getEndPayCycleDate());
        assertEquals(100L, result.getPayrollCycleId());
        assertFalse(result.isBilledAlready());
    }

    @Test
    void test_new_management_fee_mapper() {
        //given
        var managementFee = ManagementFee.builder()
                .contract(Contract.newBuilder()
                        .id(1L)
                        .build())
                .discountedFee(400.0)
                .originalFee(500.0)
                .appliedDiscount("applied discount")
                .build();

        var metadataItem = MetadataItem.builder()
                .contractId(1L)
                .billingCurrency(CurrencyCode.USD)
                .contractCountryCode(CountryCode.PHL)
                .contractCountryName("Philippines")
                .memberName("awesome name")
                .build();

        //when
        var result = mapper.mapFromNewManagementFee(managementFee, metadataItem, 200.0, "tax_type");

        //then
        assertEquals(400.0, result.getAmountInBaseCurrency());
        assertEquals(200.0, result.getAmountInBillingCurrency());
        assertEquals(CurrencyCode.USD, result.getBaseCurrency());
        assertEquals(CurrencyCode.USD, result.getBillingCurrency());
        assertEquals(metadataItem.getContractId(), result.getContractId());
        assertEquals(metadataItem.getContractCountryCode(), result.getCountryCode());
        assertEquals(metadataItem.getContractCountryName(), result.getCountryName());
        assertEquals("Management Fee : USD 400.00", result.getDescription());
        assertEquals(LineItemType.MANAGEMENT_FEE_EOR, result.getItemType());
        assertEquals(LineItemCategory.MANAGEMENT_FEE, result.getItemCategory());
        assertEquals("awesome name", result.getMemberName());
        assertEquals("tax_type", result.getTaxType());
        assertFalse(result.isBilledAlready());
    }

    @Test
    void test_refund_management_fee_mapper() {
        //given
        var processedLineItem = ProcessedLineItem.builder()
                .amountInBaseCurrency(-1000.0)
                .amountInBillingCurrency(-2000.0)
                .baseCurrency(CurrencyCode.USD)
                .billingCurrency(CurrencyCode.USD)
                .itemType(LineItemType.MANAGEMENT_FEE_EOR)
                .itemCategory(LineItemCategory.MANAGEMENT_FEE)
                .build();

        //when
        var result = mapper.mapFromRefundManagementFee(processedLineItem);

        //then
        assertEquals(-1000.0, result.getAmountInBaseCurrency());
        assertEquals(-2000.0, result.getAmountInBillingCurrency());
        assertEquals("Management Fee Refund: USD -1000.00", result.getDescription());
        assertEquals(LineItemType.MANAGEMENT_FEE_EOR, result.getItemType());
        assertEquals(LineItemCategory.MANAGEMENT_FEE, result.getItemCategory());
        assertFalse(result.isBilledAlready());
    }

    @Test
    void test_adjustment_management_fee_mapper() {
        //given
        var processedLineItem = ProcessedLineItem.builder()
                .amountInBaseCurrency(1000.0)
                .amountInBillingCurrency(2000.0)
                .baseCurrency(CurrencyCode.USD)
                .billingCurrency(CurrencyCode.USD)
                .itemType(LineItemType.MANAGEMENT_FEE_EOR)
                .itemCategory(LineItemCategory.MANAGEMENT_FEE)
                .build();

        //when
        var result = mapper.mapFromAdjustmentManagementFee(processedLineItem);

        //then
        assertEquals(1000.0, result.getAmountInBaseCurrency());
        assertEquals(2000.0, result.getAmountInBillingCurrency());
        assertEquals("Actual Management Fee : USD 1000.00", result.getDescription());
        assertEquals(LineItemType.MANAGEMENT_FEE_EOR, result.getItemType());
        assertEquals(LineItemCategory.MANAGEMENT_FEE, result.getItemCategory());
        assertFalse(result.isBilledAlready());
    }

    @Test
    void when_date_provided_is_null_then_return_null() {
        //when
        var result = mapper.generateLocalDate(null);

        //then
        assertNull(result);
    }

    private CompanyMemberPayWrapper buildMemberPay(CurrencyCode currencyCode, Integer cycle, LocalDate startDate, LocalDate endDate) {

        var payrollCycle = Payroll.PayrollCycle.newBuilder()
                .setStartDate(Common.Date.newBuilder()
                        .setMonth(startDate.getMonthValue())
                        .setDay(startDate.getDayOfMonth())
                        .setYear(startDate.getYear())
                        .build())
                .setEndDate(Common.Date.newBuilder()
                        .setMonth(endDate.getMonthValue())
                        .setDay(endDate.getDayOfMonth())
                        .setYear(endDate.getYear())
                        .build())
                .setId(100L)
                .build();

        return new CompanyMemberPayWrapper(null,
                ContractOuterClass.Contract.newBuilder().setId(1L).build(),
                currencyCode, null, null,
                null,null, null, null, null,
                null, null, null, null,
                null, null, null, null, cycle, payrollCycle,
                null, null,null, null, null,
                Collections.emptyMap(),null
                );
    }
}
