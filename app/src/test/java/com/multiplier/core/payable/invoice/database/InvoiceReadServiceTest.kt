package com.multiplier.core.payable.invoice.database

import com.multiplier.core.payable.adapters.api.InvoiceDTO
import com.multiplier.core.payable.repository.model.InvoiceType
import com.multiplier.core.payable.repository.model.JpaInvoice
import com.multiplier.payable.types.InvoiceStatus
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.whenever
import org.springframework.data.jpa.domain.Specification
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@ExtendWith(MockitoExtension::class)
class InvoiceReadServiceTest {
    @Mock
    private lateinit var jpaInvoiceRepository: JpaInvoiceRepository

    @Mock
    private lateinit var invoiceSpecificationBuilder: InvoiceSpecificationBuilder

    @Mock
    private lateinit var invoiceDtoMapper: InvoiceDtoMapper

    @Mock
    private lateinit var specification: Specification<JpaInvoice>

    @Mock
    private lateinit var jpaInvoiceCustomRepository: JpaInvoiceCustomRepository

    @InjectMocks
    private lateinit var invoiceReadService: InvoiceReadService

    @Test
    fun `getInvoicesByFilters should return empty list when invoice types are empty`() {
        // Arrange
        val invoiceFilters = InvoiceFilters(null, null, emptySet(), emptySet(),null,null,null)

        // Act
        val result = invoiceReadService.getInvoicesByFilters(invoiceFilters)

        // Assert
        assertThat(result).isEmpty()
    }

    @Test
    fun `getInvoicesByFilters should return empty list when invoice statuses are empty`() {
        // Arrange
        val invoiceFilters = InvoiceFilters(null,
            null,
            setOf(InvoiceType.SALARY),
            emptySet(),
            null,
            null,
            null
        )

        // Act
        val result = invoiceReadService.getInvoicesByFilters(invoiceFilters)

        // Assert
        assertThat(result).isEmpty()
    }

    @Test
    fun `getInvoicesByFilters should return list of invoices`() {
        // Arrange
        val invoiceFilters = InvoiceFilters(null,
            null,
            setOf(InvoiceType.INSURANCE),
            setOf(InvoiceStatus.PAID),
            null,
            null,
            null
        )
        val oneInvoiceDTO = mock(InvoiceDTO::class.java)
        val secondInvoiceDTO = mock(InvoiceDTO::class.java)
        val firstJpaInvoice = mock(JpaInvoice::class.java)
        val secondJpaInvoice = mock(JpaInvoice::class.java)
        val jpaInvoices = listOf(firstJpaInvoice, secondJpaInvoice)
        val expectedInvoiceDTOs = listOf(oneInvoiceDTO, secondInvoiceDTO)

        whenever(invoiceSpecificationBuilder.build(invoiceFilters)).thenReturn(specification)
        whenever(jpaInvoiceRepository.findAll(specification)).thenReturn(jpaInvoices)
        whenever(invoiceDtoMapper.map(jpaInvoices)).thenReturn(expectedInvoiceDTOs)

        // Act
        val result = invoiceReadService.getInvoicesByFilters(invoiceFilters)

        // Assert
        assertThat(result).isEqualTo(expectedInvoiceDTOs)
    }

    @Nested
    inner class FindInvoiceByCompanyPayable {
        @Test
        fun `should return null when no invoice found`() {
            // Arrange
            val companyPayableId = 1L

            whenever(jpaInvoiceRepository.findByCompanyPayableId(companyPayableId))
                .thenReturn(Optional.empty())

            // Act
            val result = invoiceReadService.findInvoiceByCompanyPayable(companyPayableId)

            // Assert
            assertThat(result).isNull()
        }

        @Test
        fun `findInvoiceByCompanyPayable should return invoice DTO when invoice found`() {
            // Arrange
            val companyPayableId = 1L
            val invoiceDTO = mock(InvoiceDTO::class.java)
            val jpaInvoice = mock(JpaInvoice::class.java)

            whenever(jpaInvoiceRepository.findByCompanyPayableId(companyPayableId))
                .thenReturn(Optional.of(jpaInvoice))
            whenever(invoiceDtoMapper.map(jpaInvoice)).thenReturn(invoiceDTO)

            // Act
            val result = invoiceReadService.findInvoiceByCompanyPayable(companyPayableId)

            // Assert
            assertThat(result).isEqualTo(invoiceDTO)
        }

        @Test
        fun `getInvoiceById should return invoice DTO when invoice found`() {
            val invoiceId = 1L
            val invoiceDTO = mock(InvoiceDTO::class.java)
            val jpaInvoice = mock(JpaInvoice::class.java)

            whenever(jpaInvoiceRepository.findById(invoiceId))
                .thenReturn(Optional.of(jpaInvoice))
            whenever(invoiceDtoMapper.map(jpaInvoice)).thenReturn(invoiceDTO)

            val result = invoiceReadService.getInvoiceById(invoiceId)

            assertThat(result).isEqualTo(invoiceDTO)
        }

        @Test
        fun `getInvoiceById should throw exception when invoice not found`() {
            val invoiceId = 1L

            whenever(jpaInvoiceRepository.findById(invoiceId))
                .thenReturn(Optional.empty())

            assertThatThrownBy { invoiceReadService.getInvoiceById(invoiceId) }
                .isInstanceOf(InvoiceDatabaseException::class.java)
                .hasMessage("Invoice not found for id = $invoiceId")
        }
    }

    @Nested
    inner class InvoiceIdsByExternalIds {

        @Test
        fun `when externalIds are empty, return empty list`() {
            val externalIds = listOf("1", "2")
            doReturn(emptyList<JpaInvoice>()).`when`(jpaInvoiceRepository).findAllByExternalIdIn(externalIds)

            val result = invoiceReadService.getInvoiceIdsByExternalIds(externalIds)

            assertNotNull(result)
            assertEquals(0, result.size)
        }

        @Test
        fun `when external ids are present, return the ids mapped`() {
            val externalId1 = "1"
            val externalId2 = "2"
            val internal1 = 1L
            val internal2 = 2L
            val externalIds = listOf(externalId1, externalId2)

            val expected = listOf(
                InvoiceIdData(internal1, externalId1),
                InvoiceIdData(internal2, externalId2),
            )

            doReturn(listOf(
                JpaInvoice.builder()
                    .externalId(externalId1)
                    .id(internal1)
                    .build(),
                JpaInvoice.builder()
                    .externalId(externalId2)
                    .id(internal2)
                    .build()
            )).`when`(jpaInvoiceRepository).findAllByExternalIdIn(externalIds)

            val result = invoiceReadService.getInvoiceIdsByExternalIds(externalIds)

            assertEquals(expected, result)
        }

        @Test
        fun `getInvoicesByIdsWithErrorHandling should return map invoices` (){
            val invoiceId = 1L
            val invoiceDTO = mock(InvoiceDTO::class.java)
            val jpaInvoice = mock(JpaInvoice::class.java)

            whenever(invoiceDTO.id).thenReturn(invoiceId)
            whenever(jpaInvoiceRepository.findAllById(listOf(invoiceId)))
                .thenReturn(listOf(jpaInvoice))
            whenever(invoiceDtoMapper.map(jpaInvoice)).thenReturn(invoiceDTO)

            val result = invoiceReadService.getInvoicesByIdsWithErrorHandling(listOf(invoiceId))

            assertThat(result.size).isEqualTo(1)
            assertThat(result[invoiceId]).isEqualTo(invoiceDTO)
        }

        @Test
        fun `getInvoicesByIdsWithErrorHandling should return empty map when invoice ids list is empty`() {
            // Arrange
            val invoiceIds = emptyList<Long>()

            whenever(jpaInvoiceRepository.findAllById(invoiceIds))
                .thenReturn(emptyList())

            // Act
            val result = invoiceReadService.getInvoicesByIdsWithErrorHandling(invoiceIds)

            // Assert
            assertThat(result).isEmpty()
        }

        @Test
        fun `getInvoicesByIdsWithErrorHandling should handle duplicate input ids by deduplicating them`() {
            // Arrange
            val invoiceId = 1L
            val duplicateInvoiceIds = listOf(invoiceId, invoiceId, invoiceId) // Duplicates in input
            val distinctIds = listOf(invoiceId) // What should be passed to repository

            val jpaInvoice = mock(JpaInvoice::class.java)
            val invoiceDTO = mock(InvoiceDTO::class.java)

            whenever(invoiceDTO.id).thenReturn(invoiceId)
            whenever(jpaInvoiceRepository.findAllById(distinctIds))
                .thenReturn(listOf(jpaInvoice))
            whenever(invoiceDtoMapper.map(jpaInvoice)).thenReturn(invoiceDTO)

            // Act
            val result = invoiceReadService.getInvoicesByIdsWithErrorHandling(duplicateInvoiceIds)

            // Assert
            assertThat(result).hasSize(1)
            assertThat(result[invoiceId]).isEqualTo(invoiceDTO)
        }

        @Test
        fun `getInvoicesByIdsWithErrorHandling should handle mapping errors gracefully and continue processing`() {
            // Arrange
            val invoiceId1 = 1L
            val invoiceId2 = 2L
            val invoiceIds = listOf(invoiceId1, invoiceId2)

            val jpaInvoice1 = mock(JpaInvoice::class.java)
            val jpaInvoice2 = mock(JpaInvoice::class.java)
            val invoiceDTO2 = mock(InvoiceDTO::class.java)

            whenever(jpaInvoice1.id).thenReturn(invoiceId1)
            whenever(invoiceDTO2.id).thenReturn(invoiceId2)

            whenever(jpaInvoiceRepository.findAllById(invoiceIds))
                .thenReturn(listOf(jpaInvoice1, jpaInvoice2))

            // First mapping fails, second succeeds
            whenever(invoiceDtoMapper.map(jpaInvoice1)).thenThrow(RuntimeException("Mapping failed for invoice 1"))
            whenever(invoiceDtoMapper.map(jpaInvoice2)).thenReturn(invoiceDTO2)

            // Act
            val result = invoiceReadService.getInvoicesByIdsWithErrorHandling(invoiceIds)

            // Assert
            assertThat(result).hasSize(1)
            assertThat(result[invoiceId1]).isNull() // Failed mapping should not be in result
            assertThat(result[invoiceId2]).isEqualTo(invoiceDTO2) // Successful mapping should be in result
        }

        @Test
        fun `getInvoicesByIdsWithErrorHandling should return empty map when all mappings fail`() {
            // Arrange
            val invoiceId1 = 1L
            val invoiceId2 = 2L
            val invoiceIds = listOf(invoiceId1, invoiceId2)

            val jpaInvoice1 = mock(JpaInvoice::class.java)
            val jpaInvoice2 = mock(JpaInvoice::class.java)

            whenever(jpaInvoice1.id).thenReturn(invoiceId1)
            whenever(jpaInvoice2.id).thenReturn(invoiceId2)

            whenever(jpaInvoiceRepository.findAllById(invoiceIds))
                .thenReturn(listOf(jpaInvoice1, jpaInvoice2))

            // Both mappings fail
            whenever(invoiceDtoMapper.map(jpaInvoice1)).thenThrow(RuntimeException("Mapping failed for invoice 1"))
            whenever(invoiceDtoMapper.map(jpaInvoice2)).thenThrow(RuntimeException("Mapping failed for invoice 2"))

            // Act
            val result = invoiceReadService.getInvoicesByIdsWithErrorHandling(invoiceIds)

            // Assert
            assertThat(result).isEmpty()
        }

        @Test
        fun `getInvoicesByIdsWithErrorHandling should handle partial results when some invoice ids do not exist`() {
            // Arrange
            val existingInvoiceId = 1L
            val nonExistingInvoiceId = 999L
            val invoiceIds = listOf(existingInvoiceId, nonExistingInvoiceId)

            val jpaInvoice = mock(JpaInvoice::class.java)
            val invoiceDTO = mock(InvoiceDTO::class.java)

            whenever(invoiceDTO.id).thenReturn(existingInvoiceId)

            // Repository only returns the existing invoice
            whenever(jpaInvoiceRepository.findAllById(invoiceIds))
                .thenReturn(listOf(jpaInvoice))
            whenever(invoiceDtoMapper.map(jpaInvoice)).thenReturn(invoiceDTO)

            // Act
            val result = invoiceReadService.getInvoicesByIdsWithErrorHandling(invoiceIds)

            // Assert
            assertThat(result).hasSize(1)
            assertThat(result[existingInvoiceId]).isEqualTo(invoiceDTO)
            assertThat(result[nonExistingInvoiceId]).isNull()
        }
    }

    @Nested
    inner class GetInvoicesByIds {

        @Test
        fun `should return empty map when invoice ids list is empty`() {
            // Arrange
            val invoiceIds = emptyList<Long>()

            whenever(jpaInvoiceRepository.findAllById(invoiceIds))
                .thenReturn(emptyList())

            // Act
            val result = invoiceReadService.getInvoicesByIds(invoiceIds)

            // Assert
            assertThat(result).isEmpty()
        }

        @Test
        fun `should return single invoice when single id provided`() {
            // Arrange
            val invoiceId = 1L
            val invoiceIds = listOf(invoiceId)
            val jpaInvoice = mock(JpaInvoice::class.java)
            val invoiceDTO = mock(InvoiceDTO::class.java)

            whenever(invoiceDTO.id).thenReturn(invoiceId)
            whenever(jpaInvoiceRepository.findAllById(invoiceIds))
                .thenReturn(listOf(jpaInvoice))
            whenever(invoiceDtoMapper.map(jpaInvoice)).thenReturn(invoiceDTO)

            // Act
            val result = invoiceReadService.getInvoicesByIds(invoiceIds)

            // Assert
            assertThat(result).hasSize(1)
            assertThat(result[invoiceId]).isEqualTo(invoiceDTO)
        }

        @Test
        fun `should return multiple invoices when multiple ids provided`() {
            // Arrange
            val invoiceId1 = 1L
            val invoiceId2 = 2L
            val invoiceIds = listOf(invoiceId1, invoiceId2)

            val jpaInvoice1 = mock(JpaInvoice::class.java)
            val jpaInvoice2 = mock(JpaInvoice::class.java)
            val invoiceDTO1 = mock(InvoiceDTO::class.java)
            val invoiceDTO2 = mock(InvoiceDTO::class.java)

            whenever(invoiceDTO1.id).thenReturn(invoiceId1)
            whenever(invoiceDTO2.id).thenReturn(invoiceId2)

            whenever(jpaInvoiceRepository.findAllById(invoiceIds))
                .thenReturn(listOf(jpaInvoice1, jpaInvoice2))
            whenever(invoiceDtoMapper.map(jpaInvoice1)).thenReturn(invoiceDTO1)
            whenever(invoiceDtoMapper.map(jpaInvoice2)).thenReturn(invoiceDTO2)

            // Act
            val result = invoiceReadService.getInvoicesByIds(invoiceIds)

            // Assert
            assertThat(result).hasSize(2)
            assertThat(result[invoiceId1]).isEqualTo(invoiceDTO1)
            assertThat(result[invoiceId2]).isEqualTo(invoiceDTO2)
        }

        @Test
        fun `should handle duplicate invoice DTOs by keeping only distinct ones`() {
            // Arrange
            val invoiceId = 1L
            val invoiceIds = listOf(invoiceId)

            val jpaInvoice1 = mock(JpaInvoice::class.java)
            val jpaInvoice2 = mock(JpaInvoice::class.java)
            val invoiceDTO1 = mock(InvoiceDTO::class.java)
            val invoiceDTO2 = mock(InvoiceDTO::class.java)

            // Both DTOs have the same ID (simulating duplicate scenario)
            whenever(invoiceDTO1.id).thenReturn(invoiceId)
            whenever(invoiceDTO2.id).thenReturn(invoiceId)

            whenever(jpaInvoiceRepository.findAllById(invoiceIds))
                .thenReturn(listOf(jpaInvoice1, jpaInvoice2))
            whenever(invoiceDtoMapper.map(jpaInvoice1)).thenReturn(invoiceDTO1)
            whenever(invoiceDtoMapper.map(jpaInvoice2)).thenReturn(invoiceDTO2)

            // Act
            val result = invoiceReadService.getInvoicesByIds(invoiceIds)

            // Assert
            assertThat(result).hasSize(1)
            assertThat(result[invoiceId]).isEqualTo(invoiceDTO1) // First one should be kept due to distinctBy
        }

        @Test
        fun `should return partial results when some invoice ids do not exist`() {
            // Arrange
            val existingInvoiceId = 1L
            val nonExistingInvoiceId = 999L
            val invoiceIds = listOf(existingInvoiceId, nonExistingInvoiceId)

            val jpaInvoice = mock(JpaInvoice::class.java)
            val invoiceDTO = mock(InvoiceDTO::class.java)

            whenever(invoiceDTO.id).thenReturn(existingInvoiceId)

            // Repository only returns the existing invoice
            whenever(jpaInvoiceRepository.findAllById(invoiceIds))
                .thenReturn(listOf(jpaInvoice))
            whenever(invoiceDtoMapper.map(jpaInvoice)).thenReturn(invoiceDTO)

            // Act
            val result = invoiceReadService.getInvoicesByIds(invoiceIds)

            // Assert
            assertThat(result).hasSize(1)
            assertThat(result[existingInvoiceId]).isEqualTo(invoiceDTO)
            assertThat(result[nonExistingInvoiceId]).isNull()
        }

        @Test
        fun `should propagate exception when mapper fails`() {
            // Arrange
            val invoiceId = 1L
            val invoiceIds = listOf(invoiceId)
            val jpaInvoice = mock(JpaInvoice::class.java)
            val expectedException = RuntimeException("Mapping failed")

            whenever(jpaInvoiceRepository.findAllById(invoiceIds))
                .thenReturn(listOf(jpaInvoice))
            whenever(invoiceDtoMapper.map(jpaInvoice)).thenThrow(expectedException)

            // Act & Assert
            assertThatThrownBy { invoiceReadService.getInvoicesByIds(invoiceIds) }
                .isEqualTo(expectedException)
        }
    }
}