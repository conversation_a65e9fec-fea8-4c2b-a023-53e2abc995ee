package com.multiplier.core.payable.service;

import com.multiplier.core.payable.adapters.api.LineItemDTO;
import com.multiplier.core.payable.repository.model.JpaInvoiceLineItem;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.samePropertyValuesAs;
import static org.junit.jupiter.api.Assertions.assertEquals;

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class InvoiceLineInvoiceItemMapperTest {

    private final JpaInvoiceLineItem expected = JpaInvoiceLineItem.builder()
            .description("Any description")
            .account("temp-acc-code")
            .unitPrice(42d)
            .quantity(1.0)
            .build();

    private final LineItemDTO dto = LineItemDTO.builder()
            .description("Any description")
            .accountCode("temp-acc-code")
            .unitAmount(42d)
            .quantity(1.0)
            .build();

    private final InvoiceLineItemMapper mapper = Mappers.getMapper(InvoiceLineItemMapper.class);

    @Test
    void givenDto_whenMap_thenValuesEqual() {
        // WHEN
        var jpaLineItem = mapper.map(dto);

        // THEN
        assertEquals(expected, jpaLineItem);
    }

    @Test
    void should_map_one_lineitems_to_jpa_objects() {
        InvoiceLineItemMapper mapper = Mappers.getMapper(InvoiceLineItemMapper.class);
        List<JpaInvoiceLineItem> jpaLineItems = mapper.map(List.of(dto));
        assertThat(jpaLineItems, hasItem(
                samePropertyValuesAs(expected)
        ));
    }
}