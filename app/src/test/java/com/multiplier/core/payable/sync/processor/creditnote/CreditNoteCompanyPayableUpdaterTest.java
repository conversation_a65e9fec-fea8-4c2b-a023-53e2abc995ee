package com.multiplier.core.payable.sync.processor.creditnote;

import com.multiplier.core.payable.companypayable.database.CompanyPayableDto;
import com.multiplier.core.payable.companypayable.database.CompanyPayableDtoMapper;
import com.multiplier.core.payable.creditnote.database.CreditNoteDto;
import com.multiplier.core.payable.repository.JpaCompanyPayableRepository;
import com.multiplier.core.payable.repository.model.JpaCompanyPayable;
import com.multiplier.payable.service.CompanyPayableBroadcaster;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CreditNoteCompanyPayableUpdaterTest {

    @Mock
    private JpaCompanyPayableRepository jpaCompanyPayableRepository;

    @Mock
    private CreditNoteDtoToJpaCompanyPayableMergeMapper creditNoteDtoToJpaCompanyPayableMergeMapper;

    @Mock
    private CompanyPayableDtoMapper companyPayableDtoMapper;

    @Mock
    private CompanyPayableBroadcaster companyPayableBroadcaster;

    @InjectMocks
    private CreditNoteCompanyPayableUpdater creditNoteCompanyPayableUpdater;

    @Test
    void givenCreditNoteDtoWithNullCompanyPayableId_whenUpdate_thenThrowNullPointerException() {
        // GIVEN
        var creditNoteDto = CreditNoteDto.builder()
                .build();

        // WHEN and THEN
        assertThrows(NullPointerException.class,
                () -> creditNoteCompanyPayableUpdater.update(creditNoteDto));
    }

    @Test
    void givenNotFoundCompanyPayable_whenUpdate_thenThrowException() {
        // GIVEN
        var externalCreditNote = CreditNoteDto.builder()
                .companyPayableId(42L)
                .build();

        when(jpaCompanyPayableRepository.findById(externalCreditNote.getCompanyPayableId()))
                .thenThrow(new IllegalArgumentException("Company payable not found"));

        // WHEN and THEN
        assertThrows(IllegalArgumentException.class,
                () -> creditNoteCompanyPayableUpdater.update(externalCreditNote));
    }

    @Test
    void givenCreditNoteDto_whenUpdate_thenReturnUpdatedCompanyPayable() {
        // GIVEN
        var externalCreditNote = CreditNoteDto.builder()
                .companyPayableId(42L)
                .build();

        var currentCompanyPayable = mock(JpaCompanyPayable.class);
        when(jpaCompanyPayableRepository.findById(externalCreditNote.getCompanyPayableId()))
                .thenReturn(Optional.of(currentCompanyPayable));

        var companyPayableToUpdate = mock(JpaCompanyPayable.class);
        when(creditNoteDtoToJpaCompanyPayableMergeMapper.merge(externalCreditNote, currentCompanyPayable))
                .thenReturn(companyPayableToUpdate);

        var updatedCompanyPayable = mock(JpaCompanyPayable.class);
        when(jpaCompanyPayableRepository.save(companyPayableToUpdate))
                .thenReturn(updatedCompanyPayable);

        var mappedCompanyPayable = mock(CompanyPayableDto.class);
        when(companyPayableDtoMapper.map(updatedCompanyPayable))
                .thenReturn(mappedCompanyPayable);

        // WHEN
        var response = creditNoteCompanyPayableUpdater.update(externalCreditNote);

        // THEN
        assertThat(response).isEqualTo(mappedCompanyPayable);
    }
}