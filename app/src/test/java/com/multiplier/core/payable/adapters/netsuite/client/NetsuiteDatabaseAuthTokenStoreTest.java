package com.multiplier.core.payable.adapters.netsuite.client;

import com.multiplier.core.payable.model.AuthToken;
import com.multiplier.core.payable.repository.JpaExternalInvoiceSystemAuthStoreRepository;
import com.multiplier.core.payable.repository.model.ExternalSystem;
import com.multiplier.core.payable.repository.model.JpaExternalInvoiceSystemAuthStore;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NetsuiteDatabaseAuthTokenStoreTest {

    @Mock
    JpaExternalInvoiceSystemAuthStoreRepository repository;

    @InjectMocks
    NetsuiteDatabaseAuthTokenStore tokenStore;

    @Test
    void shouldReturnValidToken() {
        JpaExternalInvoiceSystemAuthStore authStore = JpaExternalInvoiceSystemAuthStore.builder()
                .accessToken("netsuite-access-token")
                .externalSystem(ExternalSystem.NETSUITE)
                .build();

        when(repository.findByExternalSystem(ExternalSystem.NETSUITE)).thenReturn(Optional.of(authStore));
        AuthToken authToken = tokenStore.getAuthToken();

        assertEquals("netsuite-access-token", authToken.getAccessToken());
    }

    @Test
    void shouldThrowErrorWhenNoTokenFound() {
        when(repository.findByExternalSystem(ExternalSystem.NETSUITE)).thenReturn(Optional.empty());
        assertThrows(NetsuiteConfigurationException.class, () -> tokenStore.getAuthToken());
    }

    @Test
    void shouldStoreUpdatedToken() {
        JpaExternalInvoiceSystemAuthStore authStore = JpaExternalInvoiceSystemAuthStore.builder()
                .accessToken("netsuite-access-token")
                .externalSystem(ExternalSystem.NETSUITE)
                .build();

        ArgumentCaptor<JpaExternalInvoiceSystemAuthStore> captor = ArgumentCaptor.forClass(JpaExternalInvoiceSystemAuthStore.class);

        when(repository.findByExternalSystem(ExternalSystem.NETSUITE)).thenReturn(Optional.of(authStore));
        tokenStore.store("updated-access-token");
        verify(repository, times(1)).save(captor.capture());
        JpaExternalInvoiceSystemAuthStore captureStore = captor.getValue();

        assertEquals("updated-access-token", captureStore.getAccessToken());
    }
}
