package com.multiplier.core.payable.report;

import com.multiplier.core.payable.report.collector.PerformanceMonitor;
import com.multiplier.core.payable.report.database.JpaInvoiceSourceReport;
import com.multiplier.core.payable.report.database.JpaInvoiceSourceReportRepository;
import com.multiplier.core.payable.report.domain.IsrKey;
import com.multiplier.core.payable.report.domain.RetrieveReportFileRequest;
import com.multiplier.core.payable.report.externalfilesystem.FileRetrieveRequest;
import com.multiplier.core.payable.report.externalfilesystem.FileStore;
import com.multiplier.core.payable.report.externalfilesystem.FileStoreRequest;
import com.multiplier.core.payable.service.dataholder.ISRFileMetaData;
import com.multiplier.common.exception.MplBusinessException;
import com.multiplier.common.exception.MplSystemException;
import com.multiplier.core.exception.PayableErrorCode;
import com.multiplier.core.payable.service.exception.ValidationException;
import com.multiplier.core.util.CurrentUserMetaProvider;
import lombok.SneakyThrows;
import org.apache.commons.collections.ListUtils;
import org.hamcrest.CoreMatchers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Answers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@SuppressWarnings("resource")
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
@ExtendWith(MockitoExtension.class)
class DefaultISRFileStoreTest {

    @InjectMocks
    DefaultISRFileStore isrFileStore;

    @Mock
    private JpaInvoiceSourceReportRepository jpaISRRepository;

    @Mock
    private FileStore fileStore;

    @Mock
    private ISRFileMetaDataToJpaISRMapper mapper;

    @Mock
    private FileStoreRequestFactory fileStoreRequestFactory;

    @Mock(answer = Answers.CALLS_REAL_METHODS)
    private PerformanceMonitor performanceMonitor;

    @Mock
    private IsrKeyBasedAuthorizationFeature isrKeyBasedAuthorizationFeature;

    @Mock
    private CurrentUserMetaProvider currentUserMetaProvider;

    @Test
    void should_create_metadata_record_for_isr_when_only_invoice_data_is_given_without_file_content() {
        ISRFileMetaData iSRFileMetaData = ISRFileMetaData.builder().build();
        JpaInvoiceSourceReport jpaInvoiceSourceReport = JpaInvoiceSourceReport.builder().build();

        doReturn(jpaInvoiceSourceReport).when(mapper).map(iSRFileMetaData);
        doReturn(jpaInvoiceSourceReport).when(jpaISRRepository).save(jpaInvoiceSourceReport);

        isrFileStore.createMetaDataRecordFor(iSRFileMetaData);
        verify(jpaISRRepository).save(jpaInvoiceSourceReport);
    }

    @Test
    void should_create_multiple_metadata_record_for_isr_for_multiple_invoice_ids() {
        ISRFileMetaData firstIsrMetaData = ISRFileMetaData.builder().build();
        JpaInvoiceSourceReport firstJpaInvoiceSourceReport = JpaInvoiceSourceReport.builder().build();

        ISRFileMetaData secondIsrMetaData = ISRFileMetaData.builder().build();
        JpaInvoiceSourceReport secondJpaInvoiceSourceReport = JpaInvoiceSourceReport.builder().build();

        doReturn(firstJpaInvoiceSourceReport).when(mapper).map(firstIsrMetaData);
        doReturn(secondJpaInvoiceSourceReport).when(mapper).map(secondIsrMetaData);
        doReturn(List.of(firstJpaInvoiceSourceReport, secondJpaInvoiceSourceReport)).when(jpaISRRepository)
                .saveAll(List.of(firstJpaInvoiceSourceReport, secondJpaInvoiceSourceReport));

        isrFileStore.createMultipleMetaDataRecordFor(List.of(firstIsrMetaData, secondIsrMetaData));
        verify(jpaISRRepository).saveAll(List.of(firstJpaInvoiceSourceReport, secondJpaInvoiceSourceReport));
    }

    @Test
    void should_create_a_file_in_file_store_when_a_file_has_been_passed_with_valid_invoice_id() {
        long companyId = 1;
        int month = 9;
        int year = 2023;
        var isrKey = IsrKey.builder().companyId(companyId).month(month).year(year).build();

        JpaInvoiceSourceReport jpaInvoiceSourceReport = JpaInvoiceSourceReport.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .build();
        var jpaInvoiceSourceReports = List.of(jpaInvoiceSourceReport);

        when(jpaISRRepository.findByMonthAndYearAndCompanyIdInAndCompanyPayableIdIsNull(month, year, List.of(companyId)))
                .thenReturn(jpaInvoiceSourceReports);
        FileStoreRequest request = FileStoreRequest.of("any uid", "anyFileName.txt");

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        var encoded = Base64.getEncoder().encodeToString(byteArrayOutputStream.toByteArray());

        doReturn(request).when(fileStoreRequestFactory).createStoreRequest(any(), any());

        isrFileStore.store(isrKey, encoded);
        verify(jpaISRRepository).findByMonthAndYearAndCompanyIdInAndCompanyPayableIdIsNull(month, year, List.of(companyId));
        verify(fileStoreRequestFactory).createStoreRequest(any(), any());
        verify(fileStore).uploadAndMonitor(request);
    }

    @Test
    void should_download_the_file_when_the_invoice_id_is_given() throws IOException {
        long companyId = 1;
        int month = 9;
        int year = 2023;
        String authCode = "a-b-c-d";
        RetrieveReportFileRequest reportRetrieveRequest = RetrieveReportFileRequest.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .authCode(authCode)
                .authorized(false)
                .build();

        JpaInvoiceSourceReport jpaInvoiceSourceReport = JpaInvoiceSourceReport.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .hash(authCode).build();

        doReturn(Optional.of(jpaInvoiceSourceReport)).when(jpaISRRepository).findByHash(authCode);
        var fileRetrieveRequest = FileRetrieveRequest.builder()
                .fileName("something")
                .uid("something_else")
                .path("some_path")
                .build();

        doReturn(fileRetrieveRequest).when(fileStoreRequestFactory).createRetrieveRequest(jpaInvoiceSourceReport);
        InputStream inputStream = mock(InputStream.class);

        when(inputStream.readAllBytes()).thenReturn(new byte[]{1, 2, 3, 4});

        when(fileStore.retrieve(fileRetrieveRequest)).thenReturn(inputStream);

        var excelISRFile = isrFileStore.retrieve(reportRetrieveRequest);

        assertThat(excelISRFile, is(CoreMatchers.instanceOf(ExcelISRFile.class)));
        verify(jpaISRRepository).findByHash(authCode);
        verify(fileStoreRequestFactory).createRetrieveRequest(jpaInvoiceSourceReport);
        verify(fileStore).retrieve(fileRetrieveRequest);
    }

    @Test
    void throw_exception_if_auth_code_is_different() {
        long companyId = 1;
        int month = 9;
        int year = 2023;
        String authCode = "a-b-c-d";
        RetrieveReportFileRequest input = RetrieveReportFileRequest.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .authCode(authCode)
                .authorized(false)
                .build();

        doReturn(Optional.empty()).when(jpaISRRepository).findByHash(authCode);

        assertThrows(ValidationException.class, () -> isrFileStore.retrieve(input));
    }

    @Test
    void should_download_the_file_without_auth_code_with_payable_id_for_authorized_session() throws IOException {
        long companyId = 1;
        int month = 9;
        int year = 2023;
        String authCode = "a-b-c-d";
        RetrieveReportFileRequest reportRetrieveRequest = RetrieveReportFileRequest.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .companyPayableId(1L)
                .authorized(true)
                .build();

        JpaInvoiceSourceReport jpaInvoiceSourceReport = JpaInvoiceSourceReport.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .companyPayableId(1L)
                .hash(authCode).build();
        var jpaInvoiceSourceReports = List.of(jpaInvoiceSourceReport);
        var companyPayableIds = List.of(1L);

        doReturn(jpaInvoiceSourceReports).when(jpaISRRepository).findByCompanyPayableIdIn(
                argThat(argument -> ListUtils.isEqualList(companyPayableIds, argument))
        );
        var fileRetrieveRequest = FileRetrieveRequest.builder()
                .fileName("something")
                .uid("something_else")
                .path("some_path")
                .build();

        doReturn(fileRetrieveRequest).when(fileStoreRequestFactory).createRetrieveRequest(jpaInvoiceSourceReport);
        InputStream inputStream = mock(InputStream.class);

        when(inputStream.readAllBytes()).thenReturn(new byte[]{1, 2, 3, 4});

        when(fileStore.retrieve(fileRetrieveRequest)).thenReturn(inputStream);

        var excelISRFile = isrFileStore.retrieve(reportRetrieveRequest);

        assertThat(excelISRFile, is(CoreMatchers.instanceOf(ExcelISRFile.class)));
        verify(jpaISRRepository).findByCompanyPayableIdIn(argThat(argument -> ListUtils.isEqualList(companyPayableIds, argument)));
        verify(fileStoreRequestFactory).createRetrieveRequest(jpaInvoiceSourceReport);
        verify(fileStore).retrieve(fileRetrieveRequest);
    }

    @Test
    void should_download_the_file_fallback_for_authorized_session() throws IOException {
        long companyId = 1;
        int month = 9;
        int year = 2023;
        String authCode = "a-b-c-d";
        RetrieveReportFileRequest reportRetrieveRequest = RetrieveReportFileRequest.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .companyPayableId(1L)
                .authorized(true)
                .build();

        JpaInvoiceSourceReport jpaInvoiceSourceReport = JpaInvoiceSourceReport.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .hash(authCode).build();
        var jpaInvoiceSourceReports = List.of(jpaInvoiceSourceReport);

        doReturn(List.of()).when(jpaISRRepository).findByCompanyPayableIdIn(
                argThat(argument -> ListUtils.isEqualList(List.of(1L), argument))
        );
        doReturn(jpaInvoiceSourceReports).when(jpaISRRepository).findByMonthAndYearAndCompanyIdInAndCompanyPayableIdIsNull(month, year, List.of(companyId));
        var fileRetrieveRequest = FileRetrieveRequest.builder()
                .fileName("something")
                .uid("something_else")
                .path("some_path")
                .build();

        doReturn(fileRetrieveRequest).when(fileStoreRequestFactory).createRetrieveRequest(jpaInvoiceSourceReport);
        InputStream inputStream = mock(InputStream.class);

        when(inputStream.readAllBytes()).thenReturn(new byte[]{1, 2, 3, 4});

        when(fileStore.retrieve(fileRetrieveRequest)).thenReturn(inputStream);

        var excelISRFile = isrFileStore.retrieve(reportRetrieveRequest);

        assertThat(excelISRFile, is(CoreMatchers.instanceOf(ExcelISRFile.class)));
        verify(jpaISRRepository).findByCompanyPayableIdIn(argThat(argument -> ListUtils.isEqualList(List.of(1L), argument)));
        verify(jpaISRRepository).findByMonthAndYearAndCompanyIdInAndCompanyPayableIdIsNull(month, year, List.of(companyId));
        verify(fileStoreRequestFactory).createRetrieveRequest(jpaInvoiceSourceReport);
        verify(fileStore).retrieve(fileRetrieveRequest);
    }

    @Test
    void should_throw_exception_when_meta_data_record_is_not_available_for_upload() {
        Optional<JpaInvoiceSourceReport> optional = Optional.empty();
        doReturn(optional).when(jpaISRRepository).findFirstByFileNameOrderByIdDesc("fileName");
        assertThrows(MplSystemException.class, () -> isrFileStore.store("fileName", null));
    }

    @Test
    void should_replace_existing_file_if_exists() {
        var jpaInvoiceSourceReport = JpaInvoiceSourceReport.builder().build();
        var mockInput = mock(InputStream.class);
        Optional<JpaInvoiceSourceReport> optional = Optional.of(jpaInvoiceSourceReport);
        doReturn(optional).when(jpaISRRepository).findFirstByFileNameOrderByIdDesc("fileName");
        var mockFileStoreRequest = mock(FileStoreRequest.class);
        doReturn(mockFileStoreRequest).when(fileStoreRequestFactory).createStoreRequest(jpaInvoiceSourceReport, mockInput);

        isrFileStore.store("fileName", mockInput);

        verify(jpaISRRepository).findFirstByFileNameOrderByIdDesc("fileName");
        verify(fileStoreRequestFactory).createStoreRequest(jpaInvoiceSourceReport, mockInput);
        verify(fileStore).uploadAndMonitor(mockFileStoreRequest);
    }

    @Test
    void should_retrieve_existing_isr_record() {
        Collection<JpaInvoiceSourceReport> existingIsrList = List.of(
                JpaInvoiceSourceReport.builder().month(9).year(2023).companyId(1L).build(),
                JpaInvoiceSourceReport.builder().month(9).year(2023).companyId(2L).build()
        );
        doReturn(existingIsrList).when(jpaISRRepository).findByMonthAndYearAndCompanyIdIn(
                9, 2023, List.of(1L, 2L, 3L)
        );
        var updatedList = new ArrayList<>(isrFileStore.retrieveExistingMetaDataRecord(List.of(
                ISRFileMetaData.builder().month(9).year(2023).companyId(1L).build(),
                ISRFileMetaData.builder().month(9).year(2023).companyId(2L).build(),
                ISRFileMetaData.builder().month(9).year(2023).companyId(3L).build()
        )));
        Assertions.assertEquals(2, updatedList.size());
        Assertions.assertEquals(1L, updatedList.get(0).getCompanyId());
        Assertions.assertEquals(2L, updatedList.get(1).getCompanyId());
    }

    @Test
    void should_download_multiple_files() throws IOException {
        long companyId = 1;
        int month = 9;
        int year = 2023;
        String authCode = "a-b-c-d";
        RetrieveReportFileRequest reportRetrieveRequest = RetrieveReportFileRequest.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .authorized(true)
                .build();

        JpaInvoiceSourceReport firstJpaInvoiceSourceReport = JpaInvoiceSourceReport.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .hash(authCode)
                .companyPayableId(1L)
                .build();
        JpaInvoiceSourceReport similarFirstJpaInvoiceSourceReport = JpaInvoiceSourceReport.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .hash(authCode)
                .companyPayableId(2L)
                .build();
        JpaInvoiceSourceReport secondJpaInvoiceSourceReport = JpaInvoiceSourceReport.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .hash(authCode)
                .companyPayableId(2L)
                .build();
        var jpaInvoiceSourceReports = List.of(firstJpaInvoiceSourceReport, similarFirstJpaInvoiceSourceReport, secondJpaInvoiceSourceReport);

        doReturn(jpaInvoiceSourceReports).when(jpaISRRepository).findByCompanyIdAndMonthAndYear(companyId, month, year);
        var firstFileRetrieveRequest = FileRetrieveRequest.builder()
                .fileName("something")
                .uid("something_else")
                .path("some_path")
                .build();
        var similarFirstFileRetrieveRequest = FileRetrieveRequest.builder()
                .fileName("something")
                .uid("something_else")
                .path("some_path")
                .build();
        var secondFileRetrieveRequest = FileRetrieveRequest.builder()
                .fileName("anotherthing")
                .uid("anotherthing_else")
                .path("another_path")
                .build();

        doReturn(firstFileRetrieveRequest).when(fileStoreRequestFactory).createRetrieveRequest(firstJpaInvoiceSourceReport);
        doReturn(similarFirstFileRetrieveRequest).when(fileStoreRequestFactory).createRetrieveRequest(similarFirstJpaInvoiceSourceReport);
        doReturn(secondFileRetrieveRequest).when(fileStoreRequestFactory).createRetrieveRequest(secondJpaInvoiceSourceReport);
        InputStream inputStream = mock(InputStream.class);

        when(inputStream.readAllBytes()).thenReturn(new byte[]{1, 2, 3, 4});

        when(fileStore.retrieve(firstFileRetrieveRequest)).thenReturn(inputStream);
        when(fileStore.retrieve(secondFileRetrieveRequest)).thenReturn(inputStream);

        var excelISRFiles = isrFileStore.retrieveMultiple(reportRetrieveRequest);

        assertThat(excelISRFiles.size(), is(2));
        assertThat(excelISRFiles.get(0), is(CoreMatchers.instanceOf(ExcelISRFile.class)));
        assertThat(excelISRFiles.get(1), is(CoreMatchers.instanceOf(ExcelISRFile.class)));
        verify(jpaISRRepository).findByCompanyIdAndMonthAndYear(companyId, month, year);
        verify(fileStoreRequestFactory).createRetrieveRequest(firstJpaInvoiceSourceReport);
        verify(fileStoreRequestFactory).createRetrieveRequest(secondJpaInvoiceSourceReport);
        verify(fileStore).retrieve(firstFileRetrieveRequest);
        verify(fileStore).retrieve(secondFileRetrieveRequest);
    }

    @Test
    void should_download_the_file_for_non_authorized_using_auth_code() throws IOException {
        long companyId = 1;
        int month = 9;
        int year = 2023;
        String authCode = "a-b-c-d";
        RetrieveReportFileRequest reportRetrieveRequest = RetrieveReportFileRequest.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .authCode(authCode)
                .authorized(false)
                .build();

        JpaInvoiceSourceReport jpaInvoiceSourceReportWithCorrectAuthCode = JpaInvoiceSourceReport.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .hash(authCode).build();

        doReturn(Optional.of(jpaInvoiceSourceReportWithCorrectAuthCode)).when(jpaISRRepository).findByHash(authCode);
        var fileRetrieveRequest = FileRetrieveRequest.builder()
                .fileName("something")
                .uid("something_else")
                .path("some_path")
                .build();

        doReturn(fileRetrieveRequest).when(fileStoreRequestFactory).createRetrieveRequest(jpaInvoiceSourceReportWithCorrectAuthCode);
        InputStream inputStream = mock(InputStream.class);

        when(inputStream.readAllBytes()).thenReturn(new byte[]{1, 2, 3, 4});

        when(fileStore.retrieve(fileRetrieveRequest)).thenReturn(inputStream);

        var excelISRFile = isrFileStore.retrieve(reportRetrieveRequest);

        assertThat(excelISRFile, is(CoreMatchers.instanceOf(ExcelISRFile.class)));
        verify(jpaISRRepository).findByHash(authCode);
        verify(fileStoreRequestFactory).createRetrieveRequest(jpaInvoiceSourceReportWithCorrectAuthCode);
        verify(fileStore).retrieve(fileRetrieveRequest);
    }

    @Test
    void should_throw_exception_download_the_file_for_non_authorized_with_wrong_auth_code() {
        long companyId = 1;
        int month = 9;
        int year = 2023;
        String authCode = "a-b-c-d";
        RetrieveReportFileRequest reportRetrieveRequest = RetrieveReportFileRequest.builder()
                .companyId(companyId)
                .month(month)
                .year(year)
                .authCode(authCode)
                .authorized(false)
                .build();

        doReturn(Optional.empty()).when(jpaISRRepository).findByHash(authCode);
        assertThrows(ValidationException.class, () -> isrFileStore.retrieve(reportRetrieveRequest));

        verify(jpaISRRepository).findByHash(authCode);
        verifyNoInteractions(fileStoreRequestFactory);
        verifyNoInteractions(fileStore);
    }

    @Test
    @SneakyThrows
    void givenIsrKeyAuthorizationEnabledAndIsrKeyIsValid_whenRetrieve_thenReturnResponse() {
        // GIVEN
        when(isrKeyBasedAuthorizationFeature.isEnabled())
            .thenReturn(true);

        var request = RetrieveReportFileRequest.builder()
            .companyId(42L)
            .month(42)
            .year(42)
            .companyPayableId(42L)
            .authCode("awesomeAuthCode")
            .authorized(false)
            .build();

        var foundIsr = JpaInvoiceSourceReport.builder()
            .companyId(request.getCompanyId())
            .month(request.getMonth())
            .year(request.getYear())
            .companyPayableId(request.getCompanyPayableId())
            .build();
        when(jpaISRRepository.findByHash(request.getAuthCode()))
            .thenReturn(Optional.of(foundIsr));

        var fileRequest = FileRetrieveRequest.builder()
            .uid("awesomeUuid")
            .build();
        when(fileStoreRequestFactory.createRetrieveRequest(foundIsr))
            .thenReturn(fileRequest);

        var inputStream = mock(InputStream.class);
        var theBytes = new byte[]{1, 2, 3, 4};
        when(inputStream.readAllBytes())
            .thenReturn(theBytes);

        when(fileStore.retrieve(fileRequest))
            .thenReturn(inputStream);

        var data = Base64.getEncoder().encodeToString(inputStream.readAllBytes());
        var excelFile = new ExcelISRFile(fileRequest.getFileName(), data);

        // WHEN
        var response = isrFileStore.retrieve(request);

        // THEN
        org.assertj.core.api.Assertions.assertThat(response).isEqualTo(excelFile);
    }

    @Test
    void givenIsrKeyAuthorizationEnabledAndIsrKeyIsInvalid_whenRetrieve_thenReturnResponse() {
        // GIVEN
        when(isrKeyBasedAuthorizationFeature.isEnabled())
            .thenReturn(true);

        var request = RetrieveReportFileRequest.builder()
            .companyId(42L)
            .month(42)
            .year(42)
            .companyPayableId(42L)
            .authCode("awesomeAuthCode")
            .authorized(false)
            .build();

        var foundIsr = JpaInvoiceSourceReport.builder()
            .companyId(request.getCompanyId())
            .month(request.getMonth())
            .year(request.getYear())
            // Omit companyPayableId to make the ISR key invalid
            .build();
        when(jpaISRRepository.findByHash(request.getAuthCode()))
            .thenReturn(Optional.of(foundIsr));

        // WHEN
        assertThrows(ValidationException.class, () -> isrFileStore.retrieve(request));
    }

    @Test
    void should_demonstrate_IOException_handling_in_store_method() {
        // GIVEN
        IsrKey isrKey = IsrKey.builder()
                .companyId(1L)
                .month(10)
                .year(2023)
                .companyPayableId(100L)
                .build();

        String fileDataBlob = "dGVzdCBkYXRh"; // "test data" in base64

        // WHEN & THEN
        // This test demonstrates that the IOException catch block exists and would work
        // In practice, ByteArrayInputStream.close() doesn't throw IOException,
        // but the catch block is there for defensive programming

        // Test that valid input works correctly
        JpaInvoiceSourceReport jpaReport = JpaInvoiceSourceReport.builder()
                .companyId(1L)
                .month(10)
                .year(2023)
                .companyPayableId(100L)
                .fileName("test.xlsx")
                .externalFileUrl("test-url")
                .build();

        FileStoreRequest fileStoreRequest = mock(FileStoreRequest.class);
        when(fileStoreRequest.getFileNameWithoutExtension()).thenReturn("test");

        lenient().when(jpaISRRepository.findByCompanyPayableIdIn(any()))
                .thenReturn(List.of(jpaReport));
        lenient().when(fileStoreRequestFactory.createStoreRequest(any(), any(InputStream.class)))
                .thenReturn(fileStoreRequest);

        // This should work without throwing any exception
        String result = isrFileStore.store(isrKey, fileDataBlob);
        assertEquals("test", result);
    }

    @Test
    void should_throw_MplBusinessException_when_retrieveMultiple_reports_not_found() {
        // GIVEN
        IsrKey isrKey = IsrKey.builder()
                .companyId(123L)
                .month(10)
                .year(2023)
                .build();

        RetrieveReportFileRequest request = RetrieveReportFileRequest.builder()
                .companyId(123L)
                .month(10)
                .year(2023)
                .build();

        when(jpaISRRepository.findByCompanyIdAndMonthAndYear(123L, 10, 2023))
                .thenReturn(Collections.emptyList());

        // WHEN & THEN
        MplBusinessException exception = assertThrows(MplBusinessException.class, () -> {
            isrFileStore.retrieveMultiple(request);
        });

        assertEquals(PayableErrorCode.INVOICE_SOURCE_REPORT_NOT_FOUND, exception.getErrorCode());
        assertEquals("Invoice Report is not found for " + isrKey.serialize(), exception.getMessage());
    }

    @Test
    void should_throw_MplSystemException_when_store_filename_not_found_in_database() {
        // GIVEN
        String originalFilename = "test-file.xlsx";
        InputStream content = new ByteArrayInputStream("test content".getBytes());

        when(jpaISRRepository.findFirstByFileNameOrderByIdDesc(originalFilename))
                .thenReturn(Optional.empty());

        // WHEN & THEN
        MplSystemException exception = assertThrows(MplSystemException.class, () -> {
            isrFileStore.store(originalFilename, content);
        });

        assertEquals(PayableErrorCode.FILE_METADATA_NOT_FOUND, exception.getErrorCode());
        assertEquals("No file name '" + originalFilename + "' found in the database", exception.getMessage());
    }

    @Test
    void should_throw_MplSystemException_when_getJpaInvoiceSourceReportByKey_not_found() {
        // GIVEN
        IsrKey isrKey = IsrKey.builder()
                .companyId(456L)
                .month(11)
                .year(2023)
                .companyPayableId(789L)
                .build();

        String fileDataBlob = "dGVzdCBkYXRh"; // "test data" in base64

        lenient().when(jpaISRRepository.findByCompanyPayableIdIn(any()))
                .thenReturn(Collections.emptyList());

        // WHEN & THEN
        MplSystemException exception = assertThrows(MplSystemException.class, () -> {
            isrFileStore.store(isrKey, fileDataBlob);
        });

        assertEquals(PayableErrorCode.FILE_METADATA_NOT_FOUND, exception.getErrorCode());
        assertEquals("Invoice Report is not found for " + isrKey.serialize(), exception.getMessage());
    }

    @Test
    void should_throw_MplSystemException_when_retrieveSingleFile_throws_IOException() {
        // GIVEN
        RetrieveReportFileRequest request = RetrieveReportFileRequest.builder()
                .companyId(111L)
                .month(12)
                .year(2023)
                .companyPayableId(222L)
                .authorized(true)
                .build();

        JpaInvoiceSourceReport jpaReport = JpaInvoiceSourceReport.builder()
                .companyId(111L)
                .month(12)
                .year(2023)
                .companyPayableId(222L)
                .fileName("test.xlsx")
                .externalFileUrl("test-url")
                .build();

        FileRetrieveRequest fileRetrieveRequest = mock(FileRetrieveRequest.class);
        when(fileRetrieveRequest.getUid()).thenReturn("external-file-123");

        InputStream mockInputStream = mock(InputStream.class);

        lenient().when(jpaISRRepository.findByCompanyPayableIdIn(any()))
                .thenReturn(List.of(jpaReport));
        when(fileStoreRequestFactory.createRetrieveRequest(jpaReport))
                .thenReturn(fileRetrieveRequest);
        when(fileStore.retrieve(fileRetrieveRequest))
                .thenReturn(mockInputStream);

        try {
            when(mockInputStream.readAllBytes()).thenThrow(new IOException("Read error"));
        } catch (IOException e) {
            // This won't happen in test setup
        }

        // WHEN & THEN
        MplSystemException exception = assertThrows(MplSystemException.class, () -> {
            isrFileStore.retrieve(request);
        });

        assertEquals(PayableErrorCode.FILE_PROCESSING_ERROR, exception.getErrorCode());
        assertEquals("Error while reading the ISR file input stream for external-file-123", exception.getMessage());
    }

    @Test
    void should_throw_MplSystemException_when_retrieveSingleFile_throws_RuntimeException() {
        // GIVEN
        RetrieveReportFileRequest request = RetrieveReportFileRequest.builder()
                .companyId(333L)
                .month(1)
                .year(2024)
                .companyPayableId(444L)
                .authorized(true)
                .build();

        JpaInvoiceSourceReport jpaReport = JpaInvoiceSourceReport.builder()
                .companyId(333L)
                .month(1)
                .year(2024)
                .companyPayableId(444L)
                .fileName("test.xlsx")
                .externalFileUrl("test-url")
                .build();

        FileRetrieveRequest fileRetrieveRequest = mock(FileRetrieveRequest.class);
        when(fileRetrieveRequest.getUid()).thenReturn("external-file-456");

        lenient().when(jpaISRRepository.findByCompanyPayableIdIn(any()))
                .thenReturn(List.of(jpaReport));
        when(fileStoreRequestFactory.createRetrieveRequest(jpaReport))
                .thenReturn(fileRetrieveRequest);
        when(fileStore.retrieve(fileRetrieveRequest))
                .thenThrow(new RuntimeException("External storage error"));

        // WHEN & THEN
        MplSystemException exception = assertThrows(MplSystemException.class, () -> {
            isrFileStore.retrieve(request);
        });

        assertEquals(PayableErrorCode.FILE_RETRIEVAL_FAILED, exception.getErrorCode());
        assertEquals("Error while retrieving the ISR for external-file-456", exception.getMessage());
    }

}
