package com.multiplier.core.payable.creditnote.database;

import com.multiplier.core.payable.adapters.api.LineItemType;
import com.multiplier.core.payable.repository.model.ExternalSystem;
import com.multiplier.core.payable.repository.model.JpaInvoice;
import com.multiplier.payable.types.CreditNoteReason;
import com.multiplier.payable.types.CreditNoteStatus;
import com.multiplier.payable.types.CurrencyCode;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;

class CreditNoteDtoMapperTest {

    private final CreditNoteDtoMapper mapper = Mappers.getMapper(CreditNoteDtoMapper.class);

    @Test
    void givenJpaCreditNote_whenMappingToDto_thenCorrectFieldsMapped() {
        // GIVEN
        var creditNote = JpaCreditNote.builder()
                .id(42L)
                .creditNoteNo("CN123")
                .reference("Ref123")
                .reason(CreditNoteReason.INVOICE_VOIDED)
                .companyId(42L)
                .status(CreditNoteStatus.DRAFT)
                .currencyCode(CurrencyCode.USD)
                .month(8)
                .year(2023)
                .amountTotal(1000.0)
                .amountApplied(900.0)
                .amountUnapplied(100.0)
                .externalSystem(ExternalSystem.NETSUITE)
                .appliedInvoices(Set.of(
                        JpaInvoice.builder()
                                .externalId("awesomeExternalInvoiceId")
                                .build()
                ))
                .lineItems(List.of(
                        JpaCreditNoteLineItem.builder()
                                .itemType(LineItemType.GROSS_SALARY)
                                .description("awesomeDescription")
                                .unitAmount(42d)
                                .grossAmount(42d)
                                .taxAmount(42d)
                                .taxRate("42")
                                .taxCode("awesomeTaxCode")
                                .countryName("awesomeCountryName")
                                .contractId(42L)
                                .memberName("awesomeMemberName")
                                .baseCurrency(CurrencyCode.USD)
                                .amountInBaseCurrency(42d)
                                .fxRate(42d)
                                .build()
                ))
                .build();

        // WHEN
        var dto = mapper.map(creditNote);

        // THEN
        assertEquals(creditNote.getId(), dto.getId());
        assertEquals(creditNote.getCreditNoteNo(), dto.getCreditNoteNo());
        assertEquals(creditNote.getReference(), dto.getReference());
        assertEquals(creditNote.getReason(), dto.getReason());
        assertEquals(creditNote.getCompanyId(), dto.getCompanyId());
        assertEquals(creditNote.getStatus(), dto.getStatus());
        assertEquals(creditNote.getCurrencyCode(), dto.getCurrencyCode());
        assertEquals(creditNote.getMonth(), dto.getMonth());
        assertEquals(creditNote.getYear(), dto.getYear());
        assertEquals(creditNote.getAmountTotal(), dto.getAmountTotal());
        assertEquals(creditNote.getAmountApplied(), dto.getAmountApplied());
        assertEquals(creditNote.getAmountUnapplied(), dto.getAmountUnapplied());
        assertEquals(creditNote.getExternalSystem(), dto.getExternalSystem());
        assertEquals(creditNote.getAppliedInvoices().stream()
                .map(JpaInvoice::getExternalId)
                .collect(Collectors.toList()), dto.getExternalInvoiceIds());
        var item = creditNote.getLineItems().get(0);
        var itemDto = dto.getItems().get(0);
        assertEquals(item.getItemType(), itemDto.getItemType());
        assertEquals(item.getDescription(), itemDto.getDescription());
        assertEquals(item.getUnitAmount(), itemDto.getUnitAmount());
        assertEquals(item.getGrossAmount(), itemDto.getGrossAmount());
        assertEquals(item.getTaxAmount(), itemDto.getTaxAmount());
        assertEquals(Double.parseDouble(item.getTaxRate()), itemDto.getTaxRate());
        assertEquals(item.getTaxCode(), itemDto.getTaxCode());
        assertEquals(item.getContractId(), itemDto.getContractId());
        assertEquals(item.getMemberName(), itemDto.getMemberName());
        assertEquals(item.getBaseCurrency(), itemDto.getBaseCurrency());
        assertEquals(item.getAmountInBaseCurrency(), itemDto.getAmountInBaseCurrency());
        assertEquals(item.getFxRate(), itemDto.getFxRate());
    }
}