package com.multiplier.core.payable.sync.eventhandler;

import com.multiplier.core.payable.adapters.api.InvoiceDTO;
import com.multiplier.core.payable.event.database.EventDto;
import com.multiplier.core.payable.repository.model.ExternalSystem;
import com.multiplier.core.payable.sync.eventdataprocessor.EventDataProcessor;
import com.multiplier.core.payable.sync.eventstore.EventIdentifier;
import com.multiplier.core.payable.event.database.EventType;
import com.multiplier.core.payable.event.database.RecordType;
import com.multiplier.core.payable.sync.eventstore.EventStoreFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EventHandlerTest {

    private EventDataProcessor eventDataProcessor;

    @Mock
    private List<EventDataProcessor> netsuiteEventDataProcessors;

    @InjectMocks
    private TestEventHandler testNetsuiteEventHandler;

    @Mock
    private EventStoreFactory eventStoreFactory;

    @BeforeEach
    public void setUp() {
        netsuiteEventDataProcessors = new ArrayList<>();
        eventDataProcessor = mock(EventDataProcessor.class);
        netsuiteEventDataProcessors.add(eventDataProcessor);
        testNetsuiteEventHandler = new TestEventHandler(netsuiteEventDataProcessors);
    }

    @Test
    void givenInvoiceEvent_whenHandle_thenCallEventDataProcessor() {
        // GIVEN
        var id = 42L;
        var externalInvoiceId = "awesomeInvoiceId";
        var eventDto = EventDto.builder()
                .id(id)
                .externalId(externalInvoiceId)
                .recordType(RecordType.INVOICE)
                .eventType(EventType.CREATE)
                .invoice(InvoiceDTO.builder().build())
                .eventTime(LocalDateTime.now())
                .build();
        when(eventDataProcessor.getType()).thenReturn(RecordType.INVOICE);
        when(eventDataProcessor.isEventDataPresent(eventDto, ExternalSystem.NETSUITE)).thenReturn(true);

        // WHEN
        testNetsuiteEventHandler.handle(List.of(eventDto));

        // THEN
        verify(eventDataProcessor).process(any(), any());
    }

    @Test
    void givenInvoiceEventWithIsEventDataPresentReturnsTrue_whenHandle_thenResultIsCorrect() {
        // GIVEN
        var id = 42L;
        var eventDto = EventDto.builder()
                .id(id)
                .externalId("42")
                .recordType(RecordType.INVOICE)
                .eventType(EventType.CREATE)
                .eventTime(LocalDateTime.now())
                .build();
        when(eventDataProcessor.getType()).thenReturn(RecordType.INVOICE);
        when(eventDataProcessor.isEventDataPresent(eventDto, ExternalSystem.NETSUITE)).thenReturn(true);

        var eventHandler = new TestEventHandler(
                netsuiteEventDataProcessors
        );

        var expectedResult = EventHandlerResult.builder()
                .postponedEventIdentifiers(Collections.emptyList())
                .successfulEventIdentifiers(List.of(EventIdentifier.from(eventDto)))
                .failedEventIdentifiers(Collections.emptyList())
                .build();

        // WHEN
        var result = eventHandler.handle(List.of(eventDto));

        // THEN
        assertEquals(expectedResult, result);
    }

    @Test
    void givenInvoiceEventWithIsEventDataPresentReturnsFalse_whenHandle_thenResultIsCorrect() {
        // GIVEN
        var id = 42L;
        var eventDto = EventDto.builder()
                .id(id)
                .externalId("42")
                .recordType(RecordType.INVOICE)
                .eventType(EventType.CREATE)
                .eventTime(LocalDateTime.now())
                .build();
        when(eventDataProcessor.getType()).thenReturn(RecordType.INVOICE);
        when(eventDataProcessor.isEventDataPresent(eventDto, ExternalSystem.NETSUITE)).thenReturn(false);

        EventHandler eventHandler = new TestEventHandler(
                netsuiteEventDataProcessors
        );

        var expectedResult = EventHandlerResult.builder()
                .postponedEventIdentifiers(List.of(EventIdentifier.from(eventDto)))
                .successfulEventIdentifiers(Collections.emptyList())
                .failedEventIdentifiers(Collections.emptyList())
                .build();

        // WHEN
        var result = eventHandler.handle(List.of(eventDto));

        // THEN
        assertEquals(expectedResult, result);
    }

    @Test
    void givenInvoiceEventWithIsEventDataPresentThrowsException_whenHandle_thenResultIsCorrect() {
        // GIVEN
        var id = 42L;
        var eventDto = EventDto.builder()
                .id(id)
                .externalId("42")
                .recordType(RecordType.INVOICE)
                .eventType(EventType.CREATE)
                .eventTime(LocalDateTime.now())
                .build();
        when(eventDataProcessor.getType()).thenReturn(RecordType.INVOICE);
        when(eventDataProcessor.isEventDataPresent(eventDto, ExternalSystem.NETSUITE)).thenThrow(new IllegalArgumentException("Something wrong"));

        EventHandler eventHandler = new TestEventHandler(
                netsuiteEventDataProcessors
        );

        var expectedResult = EventHandlerResult.builder()
                .postponedEventIdentifiers(Collections.emptyList())
                .successfulEventIdentifiers(Collections.emptyList())
                .failedEventIdentifiers(List.of(EventIdentifier.from(eventDto)))
                .build();

        // WHEN
        var result = eventHandler.handle(List.of(eventDto));

        // THEN
        assertEquals(expectedResult, result);
    }

    @Test
    void givenEventIsNotPostponedAndFailed_whenIsAllowedToProcess_thenReturnTrue() {
        // GIVEN
        var eventIdentifier = getEventIdentifierToCheck(46L);
        var postponedEventIdentifiers = getPostponedEventIdentifiers(List.of(42L, 43L));
        var failedEventIdentifiers = getFailedEventIdentifiers(List.of(44L, 45L));

        // WHEN
        boolean result = testNetsuiteEventHandler.isAllowedToProcessRightNow(eventIdentifier, postponedEventIdentifiers, failedEventIdentifiers);

        // THEN
        assertTrue(result);
    }

    private EventIdentifier getEventIdentifierToCheck(Long id) {
        return EventIdentifier.builder()
                .id(id)
                .build();
    }

    private List<EventIdentifier> getPostponedEventIdentifiers(List<Long> ids) {
        var eventIdentifiers = new ArrayList<EventIdentifier>();
        for (int i = 0; i < ids.size(); ++i) {
            eventIdentifiers.add(EventIdentifier.builder()
                    .id(ids.get(i))
                    .build());
        }
        return eventIdentifiers;
    }

    private List<EventIdentifier> getFailedEventIdentifiers(List<Long> ids) {
        var eventIdentifiers = new ArrayList<EventIdentifier>();
        for (int i = 0; i < ids.size(); ++i) {
            eventIdentifiers.add(EventIdentifier.builder()
                    .id(ids.get(i))
                    .build());
        }
        return eventIdentifiers;
    }

    @Test
    void givenEventIsPostponed_whenIsAllowedToProcess_thenReturnFalse() {
        var eventIdentifierToCheck = getEventIdentifierToCheck(42L);
        var postponedEventIdentifiers = getPostponedEventIdentifiers(List.of(42L, 43L));
        var failedEventIdentifiers = getFailedEventIdentifiers(List.of(44L, 45L));

        // WHEN
        boolean result = testNetsuiteEventHandler.isAllowedToProcessRightNow(eventIdentifierToCheck, postponedEventIdentifiers, failedEventIdentifiers);

        // THEN
        assertFalse(result);
    }

    @Test
    void givenEventIsFailed_whenIsAllowedToProcess_thenReturnFalse() {
        // GIVEN
        var eventIdentifierToCheck = getEventIdentifierToCheck(44L);
        var postponedEventIdentifiers = getPostponedEventIdentifiers(List.of(42L, 43L));
        var failedEventIdentifiers = getFailedEventIdentifiers(List.of(44L, 45L));

        // WHEN
        boolean result = testNetsuiteEventHandler.isAllowedToProcessRightNow(eventIdentifierToCheck, postponedEventIdentifiers, failedEventIdentifiers);

        // THEN
        assertFalse(result);
    }

    @Test
    void testWhenBothPostponedAndFailed_returnsFalse() {
        var eventIdentifierToCheck = getEventIdentifierToCheck(42L);
        var postponedEventIdentifiers = getPostponedEventIdentifiers(List.of(42L, 43L));
        var failedEventIdentifiers = getFailedEventIdentifiers(List.of(42L, 44L));

        boolean result = testNetsuiteEventHandler.isAllowedToProcessRightNow(eventIdentifierToCheck, postponedEventIdentifiers, failedEventIdentifiers);
        // Expected output: false
        // Explanation: The externalInvoiceId "PO123" is present in postponedInvoiceIds, so it should not be allowed to process, even though it's also present in allFailedInvoiceIds.
        assertEquals(false, result);
    }

    @Test
    void testWhenBothListsAreEmpty_returnTrue() {
        var eventIdentifier = getEventIdentifierToCheck(42L);
        var postponedEventIdentifiers = Collections.<EventIdentifier>emptyList();
        var failedEventIdentifiers = Collections.<EventIdentifier>emptyList();

        boolean result = testNetsuiteEventHandler.isAllowedToProcessRightNow(eventIdentifier, postponedEventIdentifiers, failedEventIdentifiers);
        // Expected output: true
        // Explanation: Both postponedInvoiceIds and allFailedInvoiceIds are empty, so the externalInvoiceId "INV567" should be allowed to process.
        assertEquals(true, result);
    }

    private static class TestEventHandler extends BaseEventHandler {

        public TestEventHandler(List<EventDataProcessor> eventDataProcessors) {
            super(eventDataProcessors);
        }

        @Override
        public ExternalSystem getExternalSystem() {
            return ExternalSystem.NETSUITE;
        }
    }
}