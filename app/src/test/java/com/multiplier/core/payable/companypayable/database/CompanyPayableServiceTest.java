package com.multiplier.core.payable.companypayable.database;

import com.multiplier.core.payable.repository.JpaCompanyPayableRepository;
import com.multiplier.core.payable.repository.model.JpaCompanyPayable;
import com.multiplier.core.payable.repository.model.JpaPayableItem;
import com.multiplier.payable.engine.vas.IncidentManagementFee;
import com.multiplier.payable.engine.vas.IncidentType;
import com.multiplier.payable.engine.common.Amount;
import com.multiplier.payable.service.CompanyPayableBroadcaster;
import com.multiplier.payable.types.CompanyPayableType;
import com.multiplier.payable.types.CountryCode;
import com.multiplier.payable.types.CurrencyCode;
import com.multiplier.payable.types.PayableStatus;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CompanyPayableServiceTest {

    @Mock
    private JpaCompanyPayableRepository jpaCompanyPayableRepository;

    @Mock
    private JpaCompanyPayableMapper jpaCompanyPayableMapper;

    @Mock
    private CompanyPayableDtoMapper companyPayableDtoMapper;

    @Mock
    private CompanyPayableBroadcaster companyPayableBroadcaster;

    @InjectMocks
    private CompanyPayableService companyPayableService;

    @Test
    void givenDto_whenSave_thenValuesAreEqual() {
        // GIVEN
        var companyPayableDto = CompanyPayableDto.builder()
                .companyId(42L)
                .build();

        var jpaCompanyPayable = JpaCompanyPayable.builder()
                .build();
        when(jpaCompanyPayableMapper.map(companyPayableDto))
                .thenReturn(jpaCompanyPayable);

        var savedJpaCompanyPayable = jpaCompanyPayable;
        when(jpaCompanyPayableRepository.save(jpaCompanyPayable))
                .thenReturn(savedJpaCompanyPayable);

        var returnedCompanyPayableDto = companyPayableDto;
        when(companyPayableDtoMapper.map(savedJpaCompanyPayable))
                .thenReturn(returnedCompanyPayableDto);

        // WHEN
        var savedCompanyPayable = companyPayableService.save(companyPayableDto);

        // THEN
        assertEquals(companyPayableDto, savedCompanyPayable);
    }

    @Test
    void givenDtoWithNullCompanyId_whenSave_thenThrowNullPointerException() {
        // GIVEN
        var companyPayableDto = CompanyPayableDto.builder()
                .build();

        // WHEN
        assertThrows(NullPointerException.class,
                () -> companyPayableService.save(companyPayableDto));
    }

    @Test
    void givenId_whenGet_thenReturnDto() {
        // GIVEN
        var companyPayableId = 42L;
        var jpaCompanyPayable = JpaCompanyPayable.builder()
                .id(companyPayableId)
                .build();

        when(jpaCompanyPayableRepository.getReferenceById(companyPayableId))
                .thenReturn(jpaCompanyPayable);

        var returnedCompanyPayableDto = CompanyPayableDto.builder()
                .companyId(42L)
                .build();
        when(companyPayableDtoMapper.map(jpaCompanyPayable))
                .thenReturn(returnedCompanyPayableDto);

        // WHEN
        var foundCompanyPayable = companyPayableService.get(companyPayableId);

        // THEN
        assertThat(foundCompanyPayable).isEqualTo(returnedCompanyPayableDto);
    }

    @Test
    void givenIds_whenGet_thenReturnDtos() {
        // GIVEN
        var companyPayableId = 42L;
        var companyPayableIds = List.of(companyPayableId);
        var jpaCompanyPayable = JpaCompanyPayable.builder()
                .id(companyPayableId)
                .build();
        var jpaCompanyPayables = List.of(jpaCompanyPayable);
        when(jpaCompanyPayableRepository.findAllById(companyPayableIds))
                .thenReturn(jpaCompanyPayables);

        var returnedCompanyPayableDto = CompanyPayableDto.builder()
                .companyId(42L)
                .build();
        var returnedCompanyPayableDtos = List.of(returnedCompanyPayableDto);
        when(companyPayableDtoMapper.map(jpaCompanyPayables))
                .thenReturn(returnedCompanyPayableDtos);

        // WHEN
        var foundCompanyPayables = companyPayableService.get(companyPayableIds);

        // THEN
        assertThat(foundCompanyPayables).isEqualTo(returnedCompanyPayableDtos);
    }

    @Test
    void givenId_whenMarkCompanyPayableAsDeleted_thenReturnTrue() {
        // GIVEN
        var companyPayableId = 42L;
        var jpaCompanyPayable = JpaCompanyPayable.builder()
                .id(companyPayableId)
                .status(PayableStatus.DRAFT)
                .build();
        when(jpaCompanyPayableRepository.findById(companyPayableId))
                .thenReturn(Optional.of(jpaCompanyPayable));
        when(jpaCompanyPayableRepository.save(jpaCompanyPayable))
                .thenReturn(jpaCompanyPayable);

        // WHEN
        var result = companyPayableService.markCompanyPayableAsDeleted(companyPayableId);

        // THEN
        assertThat(result).isTrue();
        assertThat(jpaCompanyPayable.getStatus()).isEqualTo(PayableStatus.DELETED);
        verify(jpaCompanyPayableRepository).save(jpaCompanyPayable);
    }

    @Test
    void givenId_whenMarkCompanyPayableAsDeleted_thenReturnFalse() {
        // GIVEN
        var companyPayableId = 42L;
        when(jpaCompanyPayableRepository.findById(companyPayableId))
                .thenReturn(Optional.empty());

        // WHEN
        var result = companyPayableService.markCompanyPayableAsDeleted(companyPayableId);

        // THEN
        assertThat(result).isFalse();
    }

    @Test
    void givenTransactionId_whenGetCompanyPayableByTransactionId_thenReturnDtos() {
        // GIVEN
        var transactionId = "txn123";
        var jpaCompanyPayable = JpaCompanyPayable.builder()
                .id(42L)
                .transactionId(transactionId)
                .build();
        var jpaCompanyPayables = List.of(jpaCompanyPayable);
        when(jpaCompanyPayableRepository.findByTransactionId(transactionId))
                .thenReturn(jpaCompanyPayables);

        var returnedCompanyPayableDto = CompanyPayableDto.builder()
                .companyId(42L)
                .build();
        var returnedCompanyPayableDtos = List.of(returnedCompanyPayableDto);
        when(companyPayableDtoMapper.map(jpaCompanyPayables))
                .thenReturn(returnedCompanyPayableDtos);

        // WHEN
        var foundCompanyPayables = companyPayableService.getCompanyPayableByTransactionId(transactionId);

        // THEN
        assertThat(foundCompanyPayables).isEqualTo(returnedCompanyPayableDtos);
    }

    @Test
    void givenVasIncidentInvoiceWithBillIds_whenGetVasIncidentBillIds_thenReturnFlattenedBillIds() {
        // GIVEN
        var companyPayableId = 42L;

        // Create incident management fees with different bill IDs
        var incidentFee1 = new IncidentManagementFee(
                123L,
                new Amount(BigDecimal.valueOf(100.0), CurrencyCode.USD),
                null,
                456L,
                CountryCode.IND,
                IncidentType.LAPTOP,
                List.of(1001L, 1002L)
        );

        var incidentFee2 = new IncidentManagementFee(
                124L,
                new Amount(BigDecimal.valueOf(50.0), CurrencyCode.USD),
                null,
                457L,
                CountryCode.IND,
                IncidentType.MONITOR,
                List.of(2001L, 2002L, 2003L)
        );

        // Create payable items with incident management fees
        var payableItem1 = JpaPayableItem.builder()
                .incidentManagementFee(incidentFee1)
                .build();

        var payableItem2 = JpaPayableItem.builder()
                .incidentManagementFee(incidentFee2)
                .build();

        var payableItem3 = JpaPayableItem.builder()
                .incidentManagementFee(null) // This should be filtered out
                .build();

        var jpaCompanyPayable = JpaCompanyPayable.builder()
                .id(companyPayableId)
                .type(CompanyPayableType.VAS_INCIDENT_INVOICE)
                .items(Set.of(payableItem1, payableItem2, payableItem3))
                .build();

        // Mock the repository method call
        when(jpaCompanyPayableRepository.findByIdAndType(companyPayableId, CompanyPayableType.VAS_INCIDENT_INVOICE))
                .thenReturn(List.of(jpaCompanyPayable));

        // WHEN
        var billIds = companyPayableService.getVasIncidentBillIds(companyPayableId);

        // THEN
        assertThat(billIds).isNotNull();
        assertEquals(5, billIds.size());
        assertThat(billIds).containsExactlyInAnyOrder(1001L, 1002L, 2001L, 2002L, 2003L);
    }

    @Test
    void givenNonVasIncidentInvoice_whenGetVasIncidentBillIds_thenReturnEmptyList() {
        // GIVEN
        var companyPayableId = 42L;

        // Mock the repository to return empty list for non-VAS incident invoice type
        when(jpaCompanyPayableRepository.findByIdAndType(companyPayableId, CompanyPayableType.VAS_INCIDENT_INVOICE))
                .thenReturn(List.of());

        // WHEN
        var billIds = companyPayableService.getVasIncidentBillIds(companyPayableId);

        // THEN
        assertThat(billIds).isNotNull();
        assertEquals(0, billIds.size());
    }
    
    @Test
    void givenVasIncidentInvoiceWithNoIncidentManagementFees_whenGetVasIncidentBillIds_thenReturnEmptyList() {
        // GIVEN
        var companyPayableId = 42L;

        // Create payable items without incident management fees
        var payableItem1 = JpaPayableItem.builder()
                .incidentManagementFee(null) // No incident management fee
                .build();

        var payableItem2 = JpaPayableItem.builder()
                .incidentManagementFee(null) // No incident management fee
                .build();

        var jpaCompanyPayable = JpaCompanyPayable.builder()
                .id(companyPayableId)
                .type(CompanyPayableType.VAS_INCIDENT_INVOICE)
                .items(Set.of(payableItem1, payableItem2))
                .build();

        // Mock the repository method call
        when(jpaCompanyPayableRepository.findByIdAndType(companyPayableId, CompanyPayableType.VAS_INCIDENT_INVOICE))
                .thenReturn(List.of(jpaCompanyPayable));

        // WHEN
        var billIds = companyPayableService.getVasIncidentBillIds(companyPayableId);

        // THEN
        assertThat(billIds).isNotNull();
        assertEquals(0, billIds.size());
    }
}
