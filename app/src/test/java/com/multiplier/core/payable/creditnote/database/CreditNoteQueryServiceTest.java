package com.multiplier.core.payable.creditnote.database;

import com.multiplier.core.payable.creditnote.composition.CreditNoteMapper;
import lombok.val;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CreditNoteQueryServiceTest {

    @Mock
    private CreditNoteMapper creditNoteMapper;

    @Mock
    private CreditNoteQueryMapper creditNoteQueryMapper;

    @Mock
    private UserExperienceBasedCreditNoteSpecificationFactory userExperienceBasedCreditNoteSpecificationFactory;

    @Mock
    private CreditNotePageRequestBuilder creditNotePageRequestBuilder;

    @Mock
    private JpaCreditNoteRepository jpaCreditNoteRepository;

    @Mock
    private CreditNoteDtoMapper creditNoteDtoMapper;

    @InjectMocks
    private CreditNoteQueryService creditNoteQueryService;

    @Test
    void givenInput_whenGetCreditNotesWithPagination_thenReturnPageDto() {
        // GIVEN
        var creditNoteQueryDto = CreditNoteQueryDto.builder()
                .pageNumber(42)
                .pageSize(42)
                .build();

        var specification = mock(Specification.class);
        var creditNoteSpecificationBuilder = mock(CreditNoteSpecificationBuilder.class);

        when(userExperienceBasedCreditNoteSpecificationFactory.getCreditNoteSpecificBuilder()).thenReturn(creditNoteSpecificationBuilder);
        when(creditNoteSpecificationBuilder.build(creditNoteQueryDto))
                .thenReturn(specification);

        var dataPageRequest = mock(org.springframework.data.domain.PageRequest.class);
        when(creditNotePageRequestBuilder.build(creditNoteQueryDto))
                .thenReturn(dataPageRequest);

        var jpaCreditNotes = new ArrayList<JpaCreditNote>();
        var jpaCreditNote = JpaCreditNote.builder()
                .build();
        jpaCreditNotes.add(jpaCreditNote);
        var pagedJpaCreditNotes = mock(Page.class);
        when(pagedJpaCreditNotes.getContent()).thenReturn(jpaCreditNotes);
        when(pagedJpaCreditNotes.getTotalElements()).thenReturn(200L);
        when(pagedJpaCreditNotes.getNumber()).thenReturn(0);
        when(pagedJpaCreditNotes.getSize()).thenReturn(10);
        when(jpaCreditNoteRepository.findAll(specification, dataPageRequest)).thenReturn(pagedJpaCreditNotes);

        var creditNoteDtos = List.of(CreditNoteDto.builder()
                .build());
        when(creditNoteQueryMapper.map(pagedJpaCreditNotes.getContent()))
                .thenReturn(creditNoteDtos);

        // WHEN
        var pagedCreditNotes = creditNoteQueryService.getCreditNotesWithPagination(creditNoteQueryDto);

        // THEN
        assertEquals(creditNoteDtos, pagedCreditNotes.getContent());
        assertEquals(pagedJpaCreditNotes.getTotalElements(), pagedCreditNotes.getCount());
        assertEquals(pagedJpaCreditNotes.getNumber(), pagedCreditNotes.getPageNumber());
        assertEquals(pagedJpaCreditNotes.getSize(), pagedCreditNotes.getPageSize());
    }

    @Nested
    class FindCreditNoteByCompanyPayableId {

        @Test
        void givenCompanyPayableId_whenCreditNoteIsNotPresent_returnNull() {
            // GIVEN
            val companyPayableId = 42L;
            when(jpaCreditNoteRepository.findByCompanyPayableId(companyPayableId))
                    .thenReturn(Optional.empty());

            // WHEN
            val creditNoteDtoOptional = creditNoteQueryService.findCreditNoteByCompanyPayableId(companyPayableId);

            // THEN
            assertEquals(Optional.empty(), creditNoteDtoOptional);
        }

        @Test
        void givenCompanyPayableId_whenCreditNoteIsPresent_returnCreditNote() {
            //GIVEN
            val companyPayableId = 1L;
            val creditNoteId = 2L;
            val jpaCreditNote = JpaCreditNote.builder()
                    .id(creditNoteId)
                    .build();
            val creditNoteDto = CreditNoteDto.builder()
                    .id(creditNoteId)
                    .build();

            when(jpaCreditNoteRepository.findByCompanyPayableId(companyPayableId))
                    .thenReturn(Optional.of(jpaCreditNote));

            when(creditNoteQueryMapper.map(jpaCreditNote))
                    .thenReturn(creditNoteDto);

            //WHEN
            val creditNoteDtoResult = creditNoteQueryService.findCreditNoteByCompanyPayableId(companyPayableId);

            //THEN
            assertTrue(creditNoteDtoResult.isPresent());
            assertEquals(creditNoteDto, creditNoteDtoResult.get());
        }

    }

    @Nested
    class FindCreditNoteByCompanyPayableIdWithAppliedInvoiceIds {

        @Test
        void testWhenCreditNoteIsNotPresent_returnEmptyList() {
            val companyPayableId = 1L;
            doReturn(Optional.empty()).when(jpaCreditNoteRepository).findByCompanyPayableId(companyPayableId);

            val result = creditNoteQueryService.findCreditNoteByCompanyPayableIdWithAppliedInvoiceIds(companyPayableId);

            verifyNoInteractions(creditNoteDtoMapper);
            assertEquals(Optional.empty(), result);
        }

        @Test
        void testWhenCreditNoteIsPresent_returnCreditNote() {
            val companyPayableId = 1L;
            val jpaCreditNote = mock(JpaCreditNote.class);
            val creditNoteDto = mock(CreditNoteDto.class);

            doReturn(Optional.of(jpaCreditNote))
                    .when(jpaCreditNoteRepository)
                    .findByCompanyPayableId(companyPayableId);

            doReturn(creditNoteDto)
                    .when(creditNoteDtoMapper)
                    .map(jpaCreditNote);

            val result = creditNoteQueryService.findCreditNoteByCompanyPayableIdWithAppliedInvoiceIds(companyPayableId);

            assertEquals(Optional.of(creditNoteDto), result);
        }
    }

    @Nested
    class FindCreditNotesByIds {

        @Test
        void testWhenCreditNoteNotPresent_returnEmptyMap(){
            List<Long> creditNoteIds = List.of(1L);

            when(jpaCreditNoteRepository.findAllById(any())).thenReturn(Collections.emptyList());
            val result = creditNoteQueryService.findCreditNotesByIds(creditNoteIds);
            verifyNoInteractions(creditNoteDtoMapper);
            assertEquals(0, result.size());
        }

        @Test
        void testWhenCreditNotePresent_returnMapOfCreditNote(){
            List<Long> creditNoteIds = List.of(1L);
            val jpaCreditNote = mock(JpaCreditNote.class);
            val creditNoteDto = mock(CreditNoteDto.class);

            doReturn(List.of(jpaCreditNote))
                    .when(jpaCreditNoteRepository)
                    .findAllById(creditNoteIds);

            doReturn(creditNoteDto)
                    .when(creditNoteDtoMapper)
                    .map(jpaCreditNote);

            val result = creditNoteQueryService.findCreditNotesByIds(creditNoteIds);

            assertEquals(1, result.size());
        }
    }

    @Nested
    class FindCreditNotesByIdsWithErrorHandling {

        @Test
        void testWhenCreditNoteNotPresent_returnEmptyMap(){
            List<Long> creditNoteIds = List.of(1L);

            when(jpaCreditNoteRepository.findAllById(any())).thenReturn(Collections.emptyList());
            val result = creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(creditNoteIds);
            verifyNoInteractions(creditNoteDtoMapper);
            assertEquals(0, result.size());
        }

        @Test
        void testWhenCreditNotePresent_returnMapOfCreditNote(){
            List<Long> creditNoteIds = List.of(1L);
            val jpaCreditNote = mock(JpaCreditNote.class);
            val creditNoteDto = mock(CreditNoteDto.class);
            when(creditNoteDto.getId()).thenReturn(1L);

            doReturn(List.of(jpaCreditNote))
                    .when(jpaCreditNoteRepository)
                    .findAllById(creditNoteIds);

            doReturn(creditNoteDto)
                    .when(creditNoteDtoMapper)
                    .map(jpaCreditNote);

            val result = creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(creditNoteIds);

            assertEquals(1, result.size());
            assertEquals(creditNoteDto, result.get(1L));
        }

        @Test
        void testWhenMappingThrowsException_skipFailedCreditNoteAndContinueProcessing(){
            List<Long> creditNoteIds = List.of(1L, 2L);
            val jpaCreditNote1 = mock(JpaCreditNote.class);
            val jpaCreditNote2 = mock(JpaCreditNote.class);
            val creditNoteDto2 = mock(CreditNoteDto.class);

            when(jpaCreditNote1.getId()).thenReturn(1L);
            when(creditNoteDto2.getId()).thenReturn(2L);

            doReturn(List.of(jpaCreditNote1, jpaCreditNote2))
                    .when(jpaCreditNoteRepository)
                    .findAllById(creditNoteIds);

            // First credit note mapping throws exception
            doThrow(new RuntimeException("Mapping failed for credit note 1"))
                    .when(creditNoteDtoMapper)
                    .map(jpaCreditNote1);

            // Second credit note mapping succeeds
            doReturn(creditNoteDto2)
                    .when(creditNoteDtoMapper)
                    .map(jpaCreditNote2);

            val result = creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(creditNoteIds);

            // Should only contain the successfully mapped credit note
            assertEquals(1, result.size());
            assertEquals(creditNoteDto2, result.get(2L));
            assertNull(result.get(1L));

            // Verify both mappings were attempted
            verify(creditNoteDtoMapper).map(jpaCreditNote1);
            verify(creditNoteDtoMapper).map(jpaCreditNote2);
        }

        @Test
        void testWhenAllMappingsThrowException_returnEmptyMap(){
            List<Long> creditNoteIds = List.of(1L, 2L);
            val jpaCreditNote1 = mock(JpaCreditNote.class);
            val jpaCreditNote2 = mock(JpaCreditNote.class);

            when(jpaCreditNote1.getId()).thenReturn(1L);
            when(jpaCreditNote2.getId()).thenReturn(2L);

            doReturn(List.of(jpaCreditNote1, jpaCreditNote2))
                    .when(jpaCreditNoteRepository)
                    .findAllById(creditNoteIds);

            // Both credit note mappings throw exceptions
            doThrow(new RuntimeException("Mapping failed for credit note 1"))
                    .when(creditNoteDtoMapper)
                    .map(jpaCreditNote1);

            doThrow(new RuntimeException("Mapping failed for credit note 2"))
                    .when(creditNoteDtoMapper)
                    .map(jpaCreditNote2);

            val result = creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(creditNoteIds);

            // Should return empty map since all mappings failed
            assertEquals(0, result.size());

            // Verify both mappings were attempted
            verify(creditNoteDtoMapper).map(jpaCreditNote1);
            verify(creditNoteDtoMapper).map(jpaCreditNote2);
        }

        @Test
        void testWhenDuplicateCreditNotesPresent_returnDistinctCreditNotes(){
            List<Long> creditNoteIds = List.of(1L);
            val jpaCreditNote1 = mock(JpaCreditNote.class);
            val creditNoteDto1 = mock(CreditNoteDto.class);

            when(creditNoteDto1.getId()).thenReturn(1L);
            when(jpaCreditNoteRepository.findAllById(creditNoteIds)).thenReturn(List.of(jpaCreditNote1));
            doReturn(creditNoteDto1)
                    .when(creditNoteDtoMapper)
                    .map(jpaCreditNote1);

            val result = creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(creditNoteIds);

            // Should only contain one credit note due to handling of duplicates
            assertEquals(1, result.size());
            // Either creditNoteDto1 or creditNoteDto2 should be in the result
            assertSame(result.get(1L), creditNoteDto1);
        }

        @Test
        void testWhenEmptyListProvided_returnEmptyMap() {
            // Given
            List<Long> emptyCreditNoteIds = List.of();

            // When
            val result = creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(emptyCreditNoteIds);

            // Then
            assertEquals(0, result.size());
            verifyNoInteractions(jpaCreditNoteRepository);
            verifyNoInteractions(creditNoteDtoMapper);
        }

        @Test
        void testWhenDuplicateInputIds_shouldDeduplicateBeforeQuery() {
            // Given
            List<Long> duplicateCreditNoteIds = List.of(1L, 1L, 2L, 2L, 1L);
            List<Long> expectedDistinctIds = List.of(1L, 2L);

            val jpaCreditNote1 = mock(JpaCreditNote.class);
            val jpaCreditNote2 = mock(JpaCreditNote.class);
            val creditNoteDto1 = mock(CreditNoteDto.class);
            val creditNoteDto2 = mock(CreditNoteDto.class);

            when(creditNoteDto1.getId()).thenReturn(1L);
            when(creditNoteDto2.getId()).thenReturn(2L);

            doReturn(List.of(jpaCreditNote1, jpaCreditNote2))
                    .when(jpaCreditNoteRepository)
                    .findAllById(expectedDistinctIds);

            doReturn(creditNoteDto1).when(creditNoteDtoMapper).map(jpaCreditNote1);
            doReturn(creditNoteDto2).when(creditNoteDtoMapper).map(jpaCreditNote2);

            // When
            val result = creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(duplicateCreditNoteIds);

            // Then
            assertEquals(2, result.size());
            assertEquals(creditNoteDto1, result.get(1L));
            assertEquals(creditNoteDto2, result.get(2L));

            // Verify repository was called with distinct IDs only
            verify(jpaCreditNoteRepository).findAllById(expectedDistinctIds);
        }

        @Test
        void testWhenMultipleCreditNotesPresent_returnAllMappedCreditNotes() {
            // Given
            List<Long> creditNoteIds = List.of(1L, 2L, 3L);
            val jpaCreditNote1 = mock(JpaCreditNote.class);
            val jpaCreditNote2 = mock(JpaCreditNote.class);
            val jpaCreditNote3 = mock(JpaCreditNote.class);
            val creditNoteDto1 = mock(CreditNoteDto.class);
            val creditNoteDto2 = mock(CreditNoteDto.class);
            val creditNoteDto3 = mock(CreditNoteDto.class);

            when(creditNoteDto1.getId()).thenReturn(1L);
            when(creditNoteDto2.getId()).thenReturn(2L);
            when(creditNoteDto3.getId()).thenReturn(3L);

            doReturn(List.of(jpaCreditNote1, jpaCreditNote2, jpaCreditNote3))
                    .when(jpaCreditNoteRepository)
                    .findAllById(creditNoteIds);

            doReturn(creditNoteDto1).when(creditNoteDtoMapper).map(jpaCreditNote1);
            doReturn(creditNoteDto2).when(creditNoteDtoMapper).map(jpaCreditNote2);
            doReturn(creditNoteDto3).when(creditNoteDtoMapper).map(jpaCreditNote3);

            // When
            val result = creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(creditNoteIds);

            // Then
            assertEquals(3, result.size());
            assertEquals(creditNoteDto1, result.get(1L));
            assertEquals(creditNoteDto2, result.get(2L));
            assertEquals(creditNoteDto3, result.get(3L));
        }

        @Test
        void testWhenPartialCreditNotesFound_returnOnlyFoundCreditNotes() {
            // Given - Request 3 IDs but only 2 exist in database
            List<Long> requestedCreditNoteIds = List.of(1L, 2L, 999L);
            val jpaCreditNote1 = mock(JpaCreditNote.class);
            val jpaCreditNote2 = mock(JpaCreditNote.class);
            val creditNoteDto1 = mock(CreditNoteDto.class);
            val creditNoteDto2 = mock(CreditNoteDto.class);

            when(creditNoteDto1.getId()).thenReturn(1L);
            when(creditNoteDto2.getId()).thenReturn(2L);

            // Repository only returns 2 credit notes (ID 999 doesn't exist)
            doReturn(List.of(jpaCreditNote1, jpaCreditNote2))
                    .when(jpaCreditNoteRepository)
                    .findAllById(requestedCreditNoteIds);

            doReturn(creditNoteDto1).when(creditNoteDtoMapper).map(jpaCreditNote1);
            doReturn(creditNoteDto2).when(creditNoteDtoMapper).map(jpaCreditNote2);

            // When
            val result = creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(requestedCreditNoteIds);

            // Then
            assertEquals(2, result.size());
            assertEquals(creditNoteDto1, result.get(1L));
            assertEquals(creditNoteDto2, result.get(2L));
            assertNull(result.get(999L)); // Non-existent ID should not be in result
        }

        @Test
        void testWhenMappingThrowsSpecificExceptionTypes_shouldHandleGracefully() {
            // Given
            List<Long> creditNoteIds = List.of(1L, 2L, 3L);
            val jpaCreditNote1 = mock(JpaCreditNote.class);
            val jpaCreditNote2 = mock(JpaCreditNote.class);
            val jpaCreditNote3 = mock(JpaCreditNote.class);
            val creditNoteDto3 = mock(CreditNoteDto.class);

            when(jpaCreditNote1.getId()).thenReturn(1L);
            when(jpaCreditNote2.getId()).thenReturn(2L);
            when(creditNoteDto3.getId()).thenReturn(3L);

            doReturn(List.of(jpaCreditNote1, jpaCreditNote2, jpaCreditNote3))
                    .when(jpaCreditNoteRepository)
                    .findAllById(creditNoteIds);

            // Different types of exceptions
            doThrow(new NullPointerException("Null field in credit note 1"))
                    .when(creditNoteDtoMapper).map(jpaCreditNote1);
            doThrow(new IllegalArgumentException("Invalid data in credit note 2"))
                    .when(creditNoteDtoMapper).map(jpaCreditNote2);
            doReturn(creditNoteDto3).when(creditNoteDtoMapper).map(jpaCreditNote3);

            // When
            val result = creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(creditNoteIds);

            // Then
            assertEquals(1, result.size());
            assertEquals(creditNoteDto3, result.get(3L));
            assertNull(result.get(1L));
            assertNull(result.get(2L));

            // Verify all mappings were attempted
            verify(creditNoteDtoMapper).map(jpaCreditNote1);
            verify(creditNoteDtoMapper).map(jpaCreditNote2);
            verify(creditNoteDtoMapper).map(jpaCreditNote3);
        }



        @Test
        void testLoggingBehavior_shouldLogCorrectMessages() {
            // Given
            List<Long> creditNoteIds = List.of(1L, 2L);
            val jpaCreditNote1 = mock(JpaCreditNote.class);
            val jpaCreditNote2 = mock(JpaCreditNote.class);
            val creditNoteDto2 = mock(CreditNoteDto.class);

            when(jpaCreditNote1.getId()).thenReturn(1L);
            when(creditNoteDto2.getId()).thenReturn(2L);

            doReturn(List.of(jpaCreditNote1, jpaCreditNote2))
                    .when(jpaCreditNoteRepository)
                    .findAllById(creditNoteIds);

            // First mapping fails, second succeeds
            doThrow(new RuntimeException("Mapping failed"))
                    .when(creditNoteDtoMapper).map(jpaCreditNote1);
            doReturn(creditNoteDto2).when(creditNoteDtoMapper).map(jpaCreditNote2);

            // When
            val result = creditNoteQueryService.findCreditNotesByIdsWithErrorHandling(creditNoteIds);

            // Then
            assertEquals(1, result.size());
            assertEquals(creditNoteDto2, result.get(2L));

            // Note: In a real test environment, you might want to verify logging using a logging framework test utility
            // For now, we verify the behavior continues correctly despite the exception
        }
    }
}