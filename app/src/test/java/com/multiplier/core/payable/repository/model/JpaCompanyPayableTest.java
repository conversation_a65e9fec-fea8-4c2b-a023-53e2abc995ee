package com.multiplier.core.payable.repository.model;

import com.multiplier.payable.types.CurrencyCode;
import com.multiplier.payable.types.PayableStatus;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class JpaCompanyPayableTest {

    @Test
    void givenCompanyPayable_whenCreateAnother_thenTheyEqual() {
        // GIVEN
        var uuid = UUID.randomUUID();
        var companyPayable1 = simplePayable(uuid);

        // WHEN
        var companyPayable2 = simplePayable(uuid);

        // THEN
        assertEquals(companyPayable1, companyPayable2);
    }

    @Test
    void givenCompanyPayable_whenCreateAnother_thenHashCodesEqual() {
        // GIVEN
        var uuid = UUID.randomUUID();
        var companyPayable1 = simplePayable(uuid);

        // WHEN
        var companyPayable2 = simplePayable(uuid);

        // THEN
        assertEquals(companyPayable1.hashCode(), companyPayable2.hashCode());
    }

    @Test
    void givenCompanyPayable_whenCreateAnother_thenToStringsEqual() {
        // GIVEN
        var uuid = UUID.randomUUID();
        JpaCompanyPayable companyPayable1 = simplePayable(uuid);


        // WHEN
        var companyPayable2 = simplePayable(uuid);


        // THEN
        assertEquals(companyPayable1.toString(), companyPayable2.toString());
    }

    @Test
    void givenCompanyPayable_whenToBuilder_thenTheyAreEqual() {
        // GIVEN
        var uuid = UUID.randomUUID();
        JpaCompanyPayable companyPayable1 = simplePayable(uuid);

        // WHEN
        var companyPayable2 = companyPayable1.toBuilder()
                .build();

        // THEN
        assertEquals(companyPayable1, companyPayable2);
    }

    @Test
    void givenDifferentCompanyPayables_whenEquals_thenReturnFalse() {
        // GIVEN
        var uuid1 = UUID.randomUUID();
        var uuid2 = UUID.randomUUID();
        var companyPayable1 = simplePayable(uuid1);
        var companyPayable2 = simplePayable(uuid2);

        // WHEN & THEN
        assertNotEquals(companyPayable1, companyPayable2);
    }

    @Test
    void givenCompanyPayable_whenEqualsWithNull_thenReturnFalse() {
        // GIVEN
        var uuid = UUID.randomUUID();
        var companyPayable = simplePayable(uuid);

        // WHEN & THEN
        assertNotEquals(companyPayable, null);
    }

    @Test
    void givenCompanyPayable_whenEqualsWithDifferentClass_thenReturnFalse() {
        // GIVEN
        var uuid = UUID.randomUUID();
        var companyPayable = simplePayable(uuid);
        var differentObject = "different";

        // WHEN & THEN
        assertNotEquals(companyPayable, differentObject);
    }

    @Test
    void givenCompanyPayable_whenEqualsWithSameInstance_thenReturnTrue() {
        // GIVEN
        var uuid = UUID.randomUUID();
        var companyPayable = simplePayable(uuid);

        // WHEN & THEN
        assertEquals(companyPayable, companyPayable);
    }

    @Test
    void givenCompanyPayablesWithDifferentIds_whenEquals_thenReturnFalse() {
        // GIVEN
        var uuid = UUID.randomUUID();
        var companyPayable1 = simplePayable(uuid);
        var companyPayable2 = simplePayable(uuid);
        companyPayable2.setId(999L);

        // WHEN & THEN
        assertNotEquals(companyPayable1, companyPayable2);
    }

    @Test
    void givenCompanyPayablesWithDifferentStatuses_whenEquals_thenReturnFalse() {
        // GIVEN
        var uuid = UUID.randomUUID();
        var companyPayable1 = simplePayable(uuid);
        var companyPayable2 = simplePayable(uuid);
        companyPayable2.setStatus(PayableStatus.PAID);

        // WHEN & THEN
        assertNotEquals(companyPayable1, companyPayable2);
    }

    @Test
    void givenCompanyPayablesWithDifferentCompanyIds_whenEquals_thenReturnFalse() {
        // GIVEN
        var uuid = UUID.randomUUID();
        var companyPayable1 = simplePayable(uuid);
        var companyPayable2 = simplePayable(uuid);
        companyPayable2.setCompanyId(999L);

        // WHEN & THEN
        assertNotEquals(companyPayable1, companyPayable2);
    }

    @Test
    void givenCompanyPayablesWithDifferentMonths_whenEquals_thenReturnFalse() {
        // GIVEN
        var uuid = UUID.randomUUID();
        var companyPayable1 = simplePayable(uuid);
        var companyPayable2 = simplePayable(uuid);
        companyPayable2.setMonth(12);

        // WHEN & THEN
        assertNotEquals(companyPayable1, companyPayable2);
    }

    @Test
    void givenCompanyPayablesWithDifferentYears_whenEquals_thenReturnFalse() {
        // GIVEN
        var uuid = UUID.randomUUID();
        var companyPayable1 = simplePayable(uuid);
        var companyPayable2 = simplePayable(uuid);
        companyPayable2.setYear(2024);

        // WHEN & THEN
        assertNotEquals(companyPayable1, companyPayable2);
    }

    @Test
    void givenCompanyPayablesWithDifferentTotalAmounts_whenEquals_thenReturnFalse() {
        // GIVEN
        var uuid = UUID.randomUUID();
        var companyPayable1 = simplePayable(uuid);
        var companyPayable2 = simplePayable(uuid);
        companyPayable2.setTotalAmount(999.0d);

        // WHEN & THEN
        assertNotEquals(companyPayable1, companyPayable2);
    }

    @Test
    void givenCompanyPayablesWithDifferentCurrencies_whenEquals_thenReturnFalse() {
        // GIVEN
        var uuid = UUID.randomUUID();
        var companyPayable1 = simplePayable(uuid);
        var companyPayable2 = simplePayable(uuid);
        companyPayable2.setCurrency(CurrencyCode.EUR);

        // WHEN & THEN
        assertNotEquals(companyPayable1, companyPayable2);
    }

    @Test
    void givenCompanyPayablesWithDifferentItems_whenEquals_thenReturnFalse() {
        // GIVEN
        var uuid1 = UUID.randomUUID();
        var uuid2 = UUID.randomUUID();
        var companyPayable1 = simplePayable(uuid1);
        var companyPayable2 = simplePayable(uuid2);

        // WHEN & THEN
        assertNotEquals(companyPayable1, companyPayable2);
    }

    @Test
    void givenSameCompanyPayables_whenHashCode_thenReturnSameHashCode() {
        // GIVEN
        var uuid = UUID.randomUUID();
        var companyPayable1 = simplePayable(uuid);
        var companyPayable2 = simplePayable(uuid);

        // WHEN & THEN
        assertEquals(companyPayable1.hashCode(), companyPayable2.hashCode());
    }

    @Test
    void givenDifferentCompanyPayables_whenHashCode_thenReturnDifferentHashCode() {
        // GIVEN
        var uuid1 = UUID.randomUUID();
        var uuid2 = UUID.randomUUID();
        var companyPayable1 = simplePayable(uuid1);
        var companyPayable2 = simplePayable(uuid2);

        // WHEN & THEN
        assertNotEquals(companyPayable1.hashCode(), companyPayable2.hashCode());
    }

    @Test
    void givenNetsuiteSource_whenIsManualTransaction_thenReturnTrue() {
        // GIVEN
        var companyPayable = new JpaCompanyPayable();
        companyPayable.setSource(PayableSource.NETSUITE);

        // WHEN & THEN
        assertTrue(companyPayable.isManualTransaction());
    }

    @Test
    void givenSystemSource_whenIsManualTransaction_thenReturnFalse() {
        // GIVEN
        var companyPayable = new JpaCompanyPayable();
        companyPayable.setSource(PayableSource.SYSTEM);

        // WHEN & THEN
        assertFalse(companyPayable.isManualTransaction());
    }

    @Test
    void givenNullSource_whenIsManualTransaction_thenReturnFalse() {
        // GIVEN
        var companyPayable = new JpaCompanyPayable();
        companyPayable.setSource(null);

        // WHEN & THEN
        assertFalse(companyPayable.isManualTransaction());
    }

    @Test
    void givenTransactionIdWithGeneratedByOldEngine_whenIsLegacyTransaction_thenReturnTrue() {
        // GIVEN
        var companyPayable = new JpaCompanyPayable();
        companyPayable.setTransactionId("generatedByOldEngine-123");

        // WHEN & THEN
        assertTrue(companyPayable.isLegacyTransaction());
    }

    @Test
    void givenTransactionIdWithoutGeneratedByOldEngine_whenIsLegacyTransaction_thenReturnFalse() {
        // GIVEN
        var companyPayable = new JpaCompanyPayable();
        companyPayable.setTransactionId("normalTransaction-123");

        // WHEN & THEN
        assertFalse(companyPayable.isLegacyTransaction());
    }

    @Test
    void givenNullTransactionId_whenIsLegacyTransaction_thenReturnFalse() {
        // GIVEN
        var companyPayable = new JpaCompanyPayable();
        companyPayable.setTransactionId(null);

        // WHEN & THEN
        assertFalse(companyPayable.isLegacyTransaction());
    }

    @NotNull
    private static JpaCompanyPayable simplePayable(UUID companyPayableLineItemId) {
        var itemData = JpaPayableItemData.builder().amountTotalCost(100d).contractId(123L).currencyCode(CurrencyCode.USD).build();
        var item = JpaPayableItem.builder().id(companyPayableLineItemId).totalCost(100d).itemData(Set.of(itemData)).build();

        var companyPayable = new JpaCompanyPayable();
        companyPayable.setId(123L);
        companyPayable.setStatus(PayableStatus.AUTHORIZED);
        companyPayable.setMonth(3);
        companyPayable.setYear(2023);
        companyPayable.setTotalAmount(123.0d);
        companyPayable.setCompanyId(321L);
        companyPayable.setItems(Set.of(item));
        companyPayable.setCurrency(CurrencyCode.USD);

        return companyPayable;
    }

}
