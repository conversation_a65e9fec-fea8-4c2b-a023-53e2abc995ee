import com.adarshr.gradle.testlogger.theme.ThemeType
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import java.io.ByteArrayOutputStream
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

plugins {
    /**
     * Provide Spring Boot support such as repackaging, "spring-boot-dependencies".
     * See https://docs.spring.io/spring-boot/docs/current/gradle-plugin/reference/htmlsingle/#managing-dependencies.dependency-management-plugin.using-in-isolation
     */

    id("org.springframework.boot") version "3.3.10"

    /**
     * Provide Maven-like dependency management to manage dependencies such as Spring Cloud.
     * See https://docs.spring.io/dependency-management-plugin/docs/current/reference/html/#getting-started
     */
    id("io.spring.dependency-management") version "1.1.4"

    id("org.liquibase.gradle") version "2.2.1"
    id("com.google.protobuf") version "0.8.17"
    id("org.sonarqube") version "4.0.0.2929"
    id("jacoco")
    kotlin("jvm") version "2.1.0"
    kotlin("kapt") version "2.1.0"
    kotlin("plugin.spring") version "2.1.0"
    kotlin("plugin.lombok") version "2.1.0"
    kotlin("plugin.allopen") version "2.1.0"
    kotlin("plugin.jpa") version "2.1.0"
    id("maven-publish")
    id("com.mikepenz.aboutlibraries.plugin") version "10.8.3"
    id("com.adarshr.test-logger") version "3.2.0"

    /**
     * Automatically lombok and delombok annotations.
     * See https://projectlombok.org/setup/gradle#the-lombok-gradle-plugin
     */
    id("io.freefair.lombok") version "8.4"
}

group = "com.multiplier"
version = "0.0.1-SNAPSHOT"
java.sourceCompatibility = JavaVersion.VERSION_17

repositories {
    mavenCentral()
    mavenLocal()
}

dependencyManagement {
    imports {
        mavenBom("org.springframework.cloud:spring-cloud-dependencies:2023.0.3")
    }
}

dependencies {
    implementation("org.junit.jupiter:junit-jupiter")
    implementation("com.google.code.gson:gson:2.10.1")
    //KAPT
    kapt("org.mapstruct:mapstruct-processor:1.5.5.Final")
    kapt("org.projectlombok:lombok-mapstruct-binding:0.2.0")
    kapt("org.springframework.boot:spring-boot-configuration-processor")

    runtimeOnly("org.postgresql:postgresql")

    implementation("org.springframework.kafka:spring-kafka")
    implementation("com.github.daniel-shuy:kafka-protobuf-serde:2.2.0")

    implementation("org.mapstruct:mapstruct:1.5.5.Final")
    implementation("org.springframework.boot:spring-boot-starter")
    implementation("org.springframework.boot:spring-boot-starter-thymeleaf")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    implementation("org.projectlombok:lombok:1.18.30")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-mail")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign")
    implementation("io.github.openfeign:feign-httpclient:11.0")
    implementation("com.netflix.graphql.dgs:graphql-dgs-spring-boot-starter:8.3.0")
    implementation("com.graphql-java:graphql-java-extended-scalars:18.1")
    implementation("org.springframework.retry:spring-retry")
    implementation("org.zalando:problem-spring-web:0.27.0")
    implementation("io.hypersistence:hypersistence-utils-hibernate-63:3.7.2")

    /**
     * Downgrade envers and core to "6.2.6.Final" to fix a known issue of new Spring Boot 3.
     * See https://discourse.hibernate.org/t/defaultrevisionentity-java-lang-illegalargumentexception-can-not-set-static-jakarta-persistence-metamodel-mappedsuperclasstype-field-org-hibernate-envers-defaultrevisionentity-class-to-org-hibernate-metamodel-model-domain-internal-entitytypeimpl/8851/6
     */
    implementation("org.hibernate.orm:hibernate-envers:6.2.6.Final")
    implementation("org.hibernate.orm:hibernate-core:6.2.6.Final")

    implementation("net.javacrumbs.shedlock:shedlock-core:4.33.0")
    implementation("net.javacrumbs.shedlock:shedlock-spring:4.33.0")
    implementation("net.javacrumbs.shedlock:shedlock-provider-jdbc-template:4.33.0")
    implementation("org.apache.poi:poi-ooxml:4.1.1")
    implementation("com.opencsv:opencsv:5.5.2")
    // Fix CVE-2025-48734: Force secure commons-beanutils version (latest OpenCSV 5.11 and Liquibase 4.32.0 still use vulnerable 1.10.0)
    implementation("commons-beanutils:commons-beanutils:1.11.0")
    implementation(multiplier.platform.spring.starter) {
        exclude(group = "com.multiplier", module = "access-control-spring")
    }
    implementation("com.multiplier.platform:spring-redisson-starter:3.0.2") {
        exclude(group = "com.multiplier.common", module = "multiplier-common-transport")
        exclude(group = "com.multiplier.common", module = "multiplier-common-transport-spring")
    }
    implementation("com.multiplier:access-control-spring:2.0.4")
    implementation("org.liquibase:liquibase-core")
    implementation("com.multiplier:growthbook-sdk:1.1.0")
    implementation("org.springframework.boot:spring-boot-starter-oauth2-client:2.7.4")
    implementation(group = "io.github.microutils", name = "kotlin-logging-jvm", version = "2.1.23")

    implementation("org.springframework.boot:spring-boot-configuration-processor")
    implementation("net.devh:grpc-spring-boot-starter:2.15.0.RELEASE")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.json:json:20231013")
    implementation("io.jsonwebtoken:jjwt-api:0.11.2")
    implementation("io.jsonwebtoken:jjwt-impl:0.11.2")
    implementation("io.jsonwebtoken:jjwt-jackson:0.11.2")
    implementation("org.apache.commons:commons-text:1.10.0")
    implementation("io.vavr:vavr:0.10.4")

    implementation("com.github.xeroapi:xero-java:4.29.1")

    /**
     * Keep backward compatibility with for xero-java
     * until it totally migrates from JavaEE to JakartaEE
     * See https://github.com/XeroAPI/Xero-Java/pull/361#issuecomment-1980128621
     */
    implementation("jakarta.ws.rs:jakarta.ws.rs-api:2.1.6")

    /*
     * Fix "jakarta/ws/rs/ext/RuntimeDelegate"
     * See https://stackoverflow.com/questions/76388336/what-is-causing-the-java-lang-classnotfoundexception-in-maven-dependencies-for-j#76392371
     */
    implementation("org.glassfish.jersey.core:jersey-common:2.34")

    implementation("software.amazon.awssdk:s3:2.20.140")
    implementation("com.multiplier:payable-service-graph:1.14.744")
    implementation("com.multiplier:core-service-schema:1.1.204") {
        exclude("net.devh", "grpc-server-spring-boot-starter")
    }
    implementation("com.multiplier.messaging:billing-service:0.2.74")
    implementation("com.multiplier:contract-service-schema:1.4.35-kotlin19")
    implementation("com.multiplier:country-service-schema:0.0.15")
    implementation("com.multiplier:member-service-schema:1.0.14")
    implementation("com.multiplier:payroll-service-schema:1.1.557")
    implementation("com.multiplier:billing-service-schema:1.3.9")
    implementation("com.multiplier:org-management-service-schema:0.1.4")
    implementation("com.multiplier:company-service-schema:3.5.159") {
        exclude("com.multiplier", "core-service-schema")
        exclude("net.devh", "grpc-server-spring-boot-starter")
    }
    implementation("com.multiplier:pricing-service-schema:0.0.324")
    implementation("com.multiplier:metering-service-schema:1.0.4")
    implementation("com.multiplier:product-catalogue-service-schema:2.2.44")
    implementation("com.multiplier.grpc:grpc-common:1.2.14")

    implementation("com.multiplier:pigeon-service-schema:1.3.9") {
        exclude("net.devh", "grpc-server-spring-boot-starter")
    }
    implementation("com.multiplier:pigeon-service-client:1.3.9") {
        exclude("net.devh", "grpc-client-spring-boot-starter")
    }
    implementation("com.multiplier:deposit-service-grpc-schema:1.0.61")
    implementation("com.multiplier:severance-service-grpc-schema:1.0.14")
    implementation("com.multiplier:compensation-service-grpc-schema:0.0.545")
    implementation("com.multiplier:vas-incidental-service-grpc-schema:1.0.10")

    implementation("com.playtika.reactivefeign:feign-reactor-spring-cloud-starter:3.3.0")

    implementation("org.apache.httpcomponents:fluent-hc:4.5.13")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.17.0")

    implementation("com.multiplier.messaging:payable-service:0.2.21")

    runtimeOnly("org.codehaus.janino:janino:3.1.7") // Logback conditions
    runtimeOnly("net.logstash.logback:logstash-logback-encoder:7.2") // Structured (JSON) logging

    implementation("axis:axis:1.4")
    implementation(fileTree(mapOf("dir" to "lib", "includes" to listOf("suitetalk-*0.jar"))))
    runtimeOnly("commons-httpclient:commons-httpclient:3.1") //this is being used by NS wsclient and we cannot upgrade this aleady as 3.1 is the final version.
    runtimeOnly("org.slf4j:log4j-over-slf4j")
    compileOnly("com.google.code.findbugs:jsr305:3.0.1")

    implementation(project(":schema"))
    implementation("com.hubspot.jackson:jackson-datatype-protobuf:0.9.13")

    // Define explicit version for the dependencies to fix vulnerabilities
    implementation("commons-io:commons-io:2.14.0")
    implementation("org.apache.commons:commons-compress:1.26.0")
    implementation("io.netty:netty-common:4.1.119.Final")
    implementation("com.google.oauth-client:google-oauth-client:1.33.3")
    implementation("io.netty:netty-handler:4.1.119.Final")

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        exclude(group = "com.vaadin.external.google", module = "android-json")
    }
    testImplementation("org.mapstruct:mapstruct:1.5.5.Final")
    testImplementation("io.grpc:grpc-testing:1.58.0")
    testImplementation("io.mockk:mockk:1.13.17")
    testImplementation("org.testcontainers:testcontainers:1.17.6")
    testImplementation("org.testcontainers:junit-jupiter:1.17.6")
    testImplementation("org.testcontainers:postgresql:1.17.6")
    testImplementation("io.github.serpro69:kotlin-faker:1.14.0")

    testImplementation("com.h2database:h2")
    testAnnotationProcessor("org.projectlombok:lombok-mapstruct-binding:0.2.0")

    testImplementation(kotlin("test-junit5"))
    testImplementation("org.mockito.kotlin:mockito-kotlin:4.0.0") // for Mockito Kotlin support

    testImplementation ("org.springframework.kafka:spring-kafka-test") // for Kafka IT

    testImplementation("io.github.hakky54:logcaptor:2.9.3")

    liquibaseRuntime("info.picocli:picocli:4.6.3")
    liquibaseRuntime("org.liquibase.ext:liquibase-hibernate5:4.25.1")
    liquibaseRuntime("org.postgresql:postgresql")
    liquibaseRuntime(files(sourceSets.main.get().output.classesDirs))
}

tasks.withType<KotlinCompile> {
    kotlinOptions {
        jvmTarget = "17"
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
}

tasks.withType<Jar> {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

tasks.getByName("jar") {
    enabled = false
}

repositories {
    var codeartifactToken = System.getenv("CODEARTIFACT_AUTH_TOKEN")
    if (codeartifactToken.isNullOrBlank()) {
        val profile = (System.getenv("MULTIPLIER_AWS_PROFILE") ?: "").ifBlank { "default" }
        try {
            ByteArrayOutputStream().use {
                exec {
                    commandLine(
                        "aws",
                        "codeartifact",
                        "get-authorization-token",
                        "--domain",
                        "multiplier-artifacts",
                        "--domain-owner",
                        "778085304246",
                        "--query",
                        "authorizationToken",
                        "--output",
                        "text",
                        "--profile",
                        profile
                    )
                    standardOutput = it
                }
                codeartifactToken = it.toString().trim()
            }
        } catch (ex: Exception) {
            println("Couldn't load codeartifact token.")
        }
    }

    mavenCentral()
    mavenLocal()
    maven(url = "https://multiplier-artifacts-778085304246.d.codeartifact.ap-southeast-1.amazonaws.com/maven/multiplier-artifacts/") {
        credentials {
            username = "aws"
            password = codeartifactToken
        }
    }
}

tasks.jacocoTestReport {
    reports {
        html.required.set(true)
        xml.required.set(true)
        xml.outputLocation.set(file("${buildDir}/reports/jacoco.xml"))
    }

    classDirectories.setFrom(
        files(classDirectories.files.map {
            fileTree(it) {
                exclude(
                    "**/domain/**/*Jpa*.*",
                )
            }
        })
    )
}

sonarqube {
    properties {
        property("sonar.java.coveragePlugin", "jacoco")
        property("sonar.host.url", "https://sonarcloud.io")
        property("sonar.projectKey", "Multiplier-Core_payable-service")
        property("sonar.projectName", "payable-service")
        property("sonar.organization", "multiplier")
        property("sonar.coverage.jacoco.xmlReportPaths", "${buildDir}/reports/jacoco.xml")
        property(
            "sonar.coverage.exclusions",
            listOf(
                "**/configuration/**",
                "**/domain/**/*Jpa*.kt,",
                "**/com/multiplier/payable/engine/vendorBill/api/VendorBillStubs.kt"
            )
        )
    }
}

if (!project.hasProperty("runList")) {
    project.ext.set("runList", "main")
}

project.ext.set(
    "diffChangelogFile",
    "src/main/resources/liquibase/changelog/" + LocalDateTime.now()
        .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "_changelog.xml"
)

liquibase {
    activities.register("main") {
        this.arguments = mapOf(
            "driver" to "org.postgresql.Driver",
            "url" to "***************************************",
            "referenceUrl" to "hibernate:spring:com.multiplier.core?dialect=org.hibernate.dialect.PostgreSQLDialect&hibernate.physical_naming_strategy=org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy&hibernate.implicit_naming_strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy",
            "username" to "coredb",
            "password" to "password",
            "changeLogFile" to project.ext.get("diffChangelogFile"),
            "defaultSchemaName" to "payable",
            "logLevel" to "debug",
            "classpath" to "src/main/resources/"
        )
    }

    runList = project.ext.get("runList")
}

kapt {
    keepJavacAnnotationProcessors = true
}

allOpen {
    annotation("javax.persistence.Entity")
    annotation("javax.persistence.Embeddable")
    annotation("javax.persistence.MappedSuperclass")
}

aboutLibraries {
    duplicationMode = com.mikepenz.aboutlibraries.plugin.DuplicateMode.MERGE
    duplicationRule = com.mikepenz.aboutlibraries.plugin.DuplicateRule.GROUP
}

extra["snakeyaml.version"] = 2.0

testlogger {
    theme = ThemeType.STANDARD
    showExceptions = true
    showStackTraces = true
    showFullStackTraces = false
    showCauses = true
    slowThreshold = 2000
    showSummary = true
    showSimpleNames = false
    showPassed = true
    showSkipped = true
    showFailed = true
    showOnlySlow = false
    showStandardStreams = true
    showPassedStandardStreams = true
    showSkippedStandardStreams = true
    showFailedStandardStreams = true
    logLevel = LogLevel.LIFECYCLE
}

sourceSets {
    create("integrationTest") {
        /**
         * Add integrationTest's directories to the set of source directories
         * See https://ryanharrison.co.uk/2018/07/25/kotlin-add-integration-test-module.html
         */
        java.srcDir("src/integrationTest/java")
        kotlin.srcDir("src/integrationTest/kotlin")
        resources.srcDir("src/integrationTest/resources")

        compileClasspath += sourceSets.main.get().output
        runtimeClasspath += sourceSets.main.get().output
        compileClasspath += sourceSets.test.get().output
        runtimeClasspath += sourceSets.test.get().output
    }
}

val integrationTestImplementation by configurations.getting {
    extendsFrom(configurations.testImplementation.get())
}

val integrationTestRuntimeOnly by configurations.getting

configurations["integrationTestRuntimeOnly"].extendsFrom(configurations.runtimeOnly.get())

val integrationTest = tasks.register<Test>("integrationTest") {
    description = "Runs integration tests."
    group = "verification"

    testClassesDirs = sourceSets["integrationTest"].output.classesDirs
    classpath = sourceSets["integrationTest"].runtimeClasspath
    shouldRunAfter("test")

    useJUnitPlatform()

    testLogging {
        events("passed")
    }

    /**
     * Resolve "InaccessibleObjectException: Unable to make protected java.time.Clock() accessible:
     * module java.base does not "opens java.time" to unnamed module"
     * See https://stackoverflow.com/questions/67782975/how-to-fix-the-module-java-base-does-not-opens-java-io-to-unnamed-module#69179997
     */
    jvmArgs(
        "--add-opens", "java.base/java.time=ALL-UNNAMED"
    )
}

tasks.check { dependsOn(integrationTest) }

/**
 * Resolve "Execution failed for task ':app:processIntegrationTestResources'.
 * Entry liquibase/changelog/0_init_integration_test.xml is a duplicate
 * but no duplicate handling strategy has been set."
 * See https://github.com/gradle/gradle/issues/17236#issuecomment-923074298
 */
project.tasks.named("processIntegrationTestResources", Copy::class.java) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
