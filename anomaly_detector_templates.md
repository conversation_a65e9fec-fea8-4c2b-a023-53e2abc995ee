# Anomaly Detector Templates

## Implementation Guidelines

```
When implementing anomaly detection rules, ensure:

1. Code Quality:
   - No detekt errors (especially max 2 return statements per function)
   - No ktlint errors
   - Proper error handling and logging
   - Null safety checks

2. Testing:
   - Write comprehensive unit tests
   - Test happy path, error path, and edge cases
   - Mock all dependencies
   - Test feature flag behavior if applicable

3. Integration:
   - Update DetectionRuleType enum with the new rule type
   - Add rule to appropriate anomaly detectors (FirstInvoiceAnomalyDetector/SecondInvoiceAnomalyDetector)
   - Ensure feature flags are properly configured

4. Documentation:
   - Update this file with implementation status
   - Add any implementation notes or learnings
```

## Template Structure

```
Rule Name: [RuleName]
Status: [IMPLEMENTED/PENDING/IN_PROGRESS/PLACEHOLDER]
Priority: [HIGH/MEDIUM/LOW]
Description: [Brief description of what the rule checks]
Severity: ERROR/WARN

Condition: [Plain English description of the condition that triggers the anomaly]
Data Required: [What data is needed to perform the check]
Dependencies: [Services needed, e.g., pricingService:PricingService]

Detection Strategy: [COMPARISON/THRESHOLD/VALIDATION/DATE_RANGE]
Comparison Fields: [Fields to compare, e.g., expectedAmount:actualAmount]
Data Source: [Where to get the data, e.g., invoice.lineItems, contract.terms]

Threshold Values: [Any threshold values needed, e.g., "100000 USD for high value invoices"]
Date Range Logic: [Logic for date range checks if applicable]
Validation Rules: [Specific validation rules if applicable]

Error Message: [Template for the error message]
Feature Flag: [Optional - name for feature flag to enable/disable]
Transaction Types: [FIRST_INVOICE, SECOND_INVOICE, etc.]

Rule Type: [Corresponding DetectionRuleType enum value]
Implementation Notes: [Any special considerations or learnings from implementation]
```

## Example Templates

### 1. BillingCurrency

```
Rule Name: BillingCurrency
Status: IMPLEMENTED
Priority: HIGH
Description: Checks if the invoice billing currency matches the expected currency for the company
Severity: ERROR

Condition: The invoice billing currency must match the company's configured billing currency
Data Required: Invoice billing currency, company billing currency
Dependencies: pricingService:PricingService

Detection Strategy: COMPARISON
Comparison Fields: invoiceBillingCurrency:companyBillingCurrency
Data Source: invoice.billingCurrencyCode, pricingService.getCompanyBillingCurrency(companyId)

Threshold Values: N/A
Date Range Logic: N/A
Validation Rules: N/A

Error Message: Invoice Billing currency is expected to be %s but is %s
Feature Flag: ENABLE_BILLING_CURRENCY_CHECK
Transaction Types: FIRST_INVOICE, SECOND_INVOICE

Rule Type: BILLING_CURRENCY
Implementation Notes:
- Used when expression to reduce return statements for detekt compliance
- Added to both FirstInvoiceAnomalyDetector and SecondInvoiceAnomalyDetector
- Null checks for invoiceDTO and companyPayable are essential
```

### 2. HighValueInvoice

```
Rule Name: HighValueInvoice
Status: IMPLEMENTED
Priority: HIGH
Description: Checks if the invoice amount exceeds historical thresholds using per-employee normalization
Severity: ERROR

Condition: The invoice amount should not exceed historical per-employee thresholds or absolute fallback threshold
Data Required: Invoice total amount, currency, line items with contract IDs, historical invoice data
Dependencies: IADCompanyPayableFetcher (for historical data)

Detection Strategy: HISTORIC_COMPARISON with per-employee normalization
Comparison Fields: currentPerEmployeeAmount:historicalPerEmployeeThreshold, invoiceAmount:absoluteThreshold
Data Source: invoice.totalAmount, invoice.lineItems[].contractId, historical company payables

Threshold Values:
- Per-employee: 150% of historical maximum per-employee amount (dynamic)
- Absolute fallback: 100000 (when insufficient historical data or no employees detected)
Date Range Logic: Compare against previous 6 months of invoice data
Validation Rules:
  - Count unique contract IDs from employee-related line items (GROSS_SALARY, BILLED_GROSS_SALARY, BILLED_GROSS_SALARY_SUPPLEMENTARY, EOR_SALARY_DISBURSEMENT, EOR_EXPENSE_DISBURSEMENT)
  - Calculate current per-employee amount: totalAmount / employeeCount
  - Fetch historical invoices for the same company (last 6 months using IADCompanyPayableFetcher)
  - IMPORTANT: Only include AUTHORIZED, PAID, and PARTIALLY_PAID historical invoices to ensure clean threshold calculation
  - Calculate historical per-employee amounts for each historical invoice
  - Apply threshold factor (150% of historical maximum per-employee amount)
  - Flag invoices that exceed per-employee threshold
  - Fallback to absolute threshold (100000) when insufficient historical data or no employees detected
  - Employee count normalization avoids false positives for companies with varying employee counts

Error Message:
- Per-employee: "Per-employee amount %.2f %s exceeds historical threshold %.2f %s (%.1f%% of historical max). Current: %.2f %s for %d employees vs historical max: %.2f %s per employee"
- Absolute fallback: "Invoice amount %.2f %s (%.2f USD) exceeds absolute threshold %.2f USD. Invoice needs manual intervention"

Feature Flag: ENABLE_HIGH_VALUE_INVOICE_CHECK
Transaction Types: FIRST_INVOICE, SECOND_INVOICE

Rule Type: HIGH_VALUE_INVOICE
Implementation Notes:
- Now uses per-employee comparison with currency conversion for absolute threshold fallback
- Requires IADCompanyPayableFetcher and CurrencyServiceV2 dependencies
- Employee count based on unique contract IDs from employee-related line items (GROSS_SALARY, BILLED_GROSS_SALARY, BILLED_GROSS_SALARY_SUPPLEMENTARY, EOR_SALARY_DISBURSEMENT, EOR_EXPENSE_DISBURSEMENT)
- Handles companies with varying employee counts and recent growth - avoids false positives
- Graceful degradation: falls back to absolute threshold when historical data insufficient or no employees detected
- Currency conversion: converts invoice amount to USD for absolute threshold comparison (handles INR, EUR, GBP, etc.)
- Action: ERROR
- Filters historical invoices to exclude DRAFT, PENDING, VOIDED, DELETED statuses
- Minimum 3 months historical data required for per-employee comparison
- Uses 150% of historical maximum per-employee amount as threshold
- Comprehensive test coverage including per-employee logic, absolute fallback, and edge cases
- Added to both FirstInvoiceAnomalyDetector and SecondInvoiceAnomalyDetector
- Follows existing IAD patterns and integrates with existing anomaly detection framework
- Solves the employee onboarding false positive problem mentioned by user
- More meaningful comparison of actual per-employee costs rather than absolute amounts
- Currency-aware absolute threshold prevents false positives/negatives from currency mismatches
```

### 3. PayrollDifference

```
Rule Name: PayrollDifference
Status: IMPLEMENTED
Priority: HIGH
Description: Checks if there's a mismatch between invoice amounts and payroll data
Severity: ERROR

Condition: The invoice amounts should match the corresponding payroll data
Data Required: Invoice line items, payroll data
Dependencies: payrollServiceAdapter:PayrollServiceAdapter

Detection Strategy: COMPARISON
Comparison Fields: invoiceAmount:payrollAmount
Data Source: invoice.lineItems, payrollServiceAdapter.getCompaniesPayroll()

Threshold Values: N/A
Date Range Logic: N/A
Validation Rules: For each line item, the amount should match the corresponding payroll amount

Error Message: Mismatch for contract ID: %s Expected: %s, Found: %s
Feature Flag: ENABLE_PAYROLL_DIFFERENCE_CHECK
Transaction Types: SECOND_INVOICE

Rule Type: PAYROLL_DIFFERENCE
Implementation Notes:
- Already implemented in the codebase
- Used in SecondInvoiceAnomalyDetector
```

### 4. AnnualPlanDateRangeDuplicate

```
Rule Name: AnnualPlanDateRangeDuplicate
Status: IMPLEMENTED
Priority: HIGH
Description: Checks for overlapping date ranges in annual management fees
Severity: ERROR

Condition: Annual management fee date ranges should not overlap
Data Required: Annual management fee data with date ranges
Dependencies: transactionTemplateProvider:TransactionTemplateProvider, annualManagementFeeProvider:AnnualManagementFeeProvider, featureFlagService:FeatureFlagService

Detection Strategy: DATE_RANGE
Comparison Fields: dateRange:dateRange
Data Source: annualManagementFeeProvider.get(command)

Threshold Values: N/A
Date Range Logic: Group items by contract ID, sort by start date, check if any consecutive items have overlapping date ranges
Validation Rules: N/A

Error Message: Annual Management Fee for contract ID %s has duplicate date range during SECOND INVOICE generation in payable IDs %s
Feature Flag: ENABLE_IAD_ANNUAL_FEE_FLAG_NAME
Transaction Types: SECOND_INVOICE

Rule Type: ANNUAL_PLAN_DUPLICATE_DATE_RANGE
Implementation Notes:
- Already implemented in the codebase
- Used in SecondInvoiceAnomalyDetector
- Requires checking if annual plan template is merged
```

### 5. CreditNote

```
Rule Name: CreditNote
Status: IMPLEMENTED
Priority: MEDIUM
Description: Checks if an invoice has a negative amount, indicating it's a credit note
Severity: ERROR

Condition: The invoice amount should not be negative
Data Required: Invoice total amount
Dependencies: N/A

Detection Strategy: VALIDATION
Comparison Fields: invoiceAmount:0
Data Source: invoice.totalAmount

Threshold Values: 0 (amount should not be less than zero)
Date Range Logic: N/A
Validation Rules: Invoice amount must be greater than or equal to zero

Error Message: Invoice amount is negative. Credit notes need manual intervention
Feature Flag: ENABLE_CREDIT_NOTE_CHECK
Transaction Types: FIRST_INVOICE, SECOND_INVOICE

Rule Type: CREDIT_NOTE
Implementation Notes:
- Implemented as a simple validation rule checking for negative invoice amounts
- Added to both FirstInvoiceAnomalyDetector and SecondInvoiceAnomalyDetector
- Used when expression to reduce return statements for detekt compliance
```

### 6. FXRate

```
Rule Name: FXRateRule
Status: IMPLEMENTED
Priority: HIGH
Description: Checks if the FX rates used in the invoice are correct
Severity: ERROR

Condition: When invoice currency and line item currency are the same, verify that costs match exactly.
Data Required: Line items with currency codes and costs, payable items with currency codes and costs
Dependencies: None

Detection Strategy: COMPARISON
Comparison Fields: lineItem.unitAmount:payableItem.totalCost
Data Source: invoice.lineItems, companyPayable.items

Threshold Values: N/A (exact match required)
Date Range Logic: N/A
Validation Rules:
  - If currencyCode == invoiceBillingCurrency, then lineItemDTO.unitAmount must equal payableItem.totalCost

Error Message: Fx rate used is not 1 for %s to %s
Feature Flag:
Transaction Types: FIRST_INVOICE

Rule Type: DetectionRuleType.FX_RATE
Implementation Notes:
  - Only checks when currency codes match (same currency)
  - Uses a cache of line items mapped by description for efficient lookup
  - Special handling for MEMBER_MANAGEMENT_FEE items
  - Does not validate FX rates for different currencies
```

### 7. ReferenceTextCheck

```
Rule Name: ReferenceTextCheck
Status: IMPLEMENTED
Priority: HIGH
Description: Check invoice ref text contains correct MMM and YY parts for the billing period
Severity: ERROR

Condition: Invoice reference text must contain both the correct MMM and YY parts for the billing period
Data Required: Invoice reference text, billing month/year from company payable
Dependencies: None

Detection Strategy: VALIDATION
Comparison Fields: invoiceReference contains expectedMonth AND expectedYear
Data Source: invoiceDTO.reference, companyPayable.month, companyPayable.year

Threshold Values: N/A
Date Range Logic: N/A
Validation Rules: Invoice reference text must contain both MMM and YY parts matching billing period (case-insensitive, flexible positioning)

Error Message: Reference text should contain '%s' and '%s' for billing period %02d/%d but found '%s'
Feature Flag: ENABLE_REFERENCE_TEXT_CHECK
Transaction Types: SECOND_INVOICE

Rule Type: REFERENCE_TEXT_CHECK
Implementation Notes:
  - Uses 3-letter month abbreviations (Jan, Feb, Mar, etc.)
  - Uses 2-digit year format (24, 23, etc.)
  - Billing period based on company payable data (not current date)
  - Validates that reference text CONTAINS both MMM and YY parts separately (maximum flexibility)
  - Implemented with case-insensitive comparison for flexibility
  - No apostrophe requirement - allows any format as long as both parts are present
  - Month and year can appear in any position within the text
  - Examples: "Jan'24 Salary", "Jan 24 Invoice", "24 Year Jan Month", "Invoice Jan for 24 period"
  - Added comprehensive unit tests covering all months, years, and edge cases
  - Added to SecondInvoiceAnomalyDetector as specified
  - Updated from exact text matching to separate MMM and YY validation
```

### 8. PaymentTerms

```
Rule Name: PaymentTermsRule
Status: IMPLEMENTED
Priority: HIGH
Description: Check if due date on the invoice matches with system payment terms for the company
Severity: ERROR

Condition: Validate that the invoice due date matches the expected due date calculated from invoice date + company payment terms
Data Required: Invoice due date, invoice date, company payment terms (paymentTermInDays)
Dependencies: PricingService (to get company payment terms)

Detection Strategy: COMPARISON
Comparison Fields: invoiceDueDate:expectedDueDate
Data Source: invoiceDTO.dueDate, invoiceDTO.date, pricing.paymentTermInDays

Threshold Values: N/A (exact match required)
Date Range Logic: N/A
Validation Rules:
  - Calculate expected due date: invoiceDate + paymentTermInDays
  - Compare invoice.dueDate with calculated expected due date
  - Handle cases where paymentTermInDays is null (default to 7 days)
  - Account for potential date calculation edge cases

Error Message: Invoice due date '%s' does not match expected due date '%s' based on payment terms (%d days)
Feature Flag: ENABLE_PAYMENT_TERMS_CHECK
Transaction Types: SECOND_INVOICE

Rule Type: DetectionRuleType.PAYMENT_TERMS
Implementation Notes:
  - Requires PricingService dependency to fetch company payment terms
  - Default payment terms: 7 days if not configured
  - Expected due date calculation: invoice.date.plusDays(paymentTermInDays)
  - Handle null/missing payment terms gracefully
  - Graceful error handling for pricing service failures
  - Comprehensive unit tests covering all scenarios including edge cases
  - Added to SecondInvoiceAnomalyDetector as specified
  - Follows existing comparison rule patterns from BillingCurrencyRule
```

### 9. HighValueItem

```
Rule Name: HighValueItemRule
Status: IMPLEMENTED
Priority: HIGH
Description: Cap the max per-unit amount per line item on the invoice - Historic data comparison using per-unit costs
Severity: ERROR

Condition: Validate that each line item's per-unit amount does not exceed historical per-unit thresholds based on past invoice data
Data Required: Line item per-unit amounts (unitAmount, unitPrice), historical line item data for comparison
Dependencies: IADCompanyPayableFetcher (for historical data), JpaCompanyPayableRepository (for previous invoices)

Detection Strategy: HISTORIC_COMPARISON
Comparison Fields: currentLineItemPerUnitAmount:historicPerUnitThresholdAmount
Data Source: invoiceDTO.lineItems[].unitAmount, historical JpaInvoice.lineItems[].unitPrice

Threshold Values: Dynamic based on historical data analysis (e.g., 150% of historical maximum per-unit amount)
Date Range Logic: Compare against previous 6 months of invoice data
Validation Rules:
  - Use per-unit amount for each line item: unitAmount (for LineItemDTO)
  - Fetch historical invoices for the same company (last 6 months using IADCompanyPayableFetcher)
  - IMPORTANT: Only include AUTHORIZED, PAID, and PARTIALLY_PAID historical invoices to ensure clean threshold calculation
  - Extract line items from historical JpaInvoice.lineItems (JpaInvoiceLineItem[])
  - Group historical line items by LineItemType for better comparison
  - Calculate historical maximum per-unit amount per line item type: unitPrice (for JpaInvoiceLineItem)
  - Apply threshold factor (e.g., 150% of historical max per-unit) to determine acceptable limit
  - Flag line items that exceed historical per-unit threshold
  - Quantity-independent comparison avoids false positives for high-volume orders

Error Message: Line item '%s' per-unit amount %s %.2f exceeds historical per-unit threshold %s %.2f (%.1f%% of historical max)
Feature Flag: ENABLE_HIGH_VALUE_ITEM_CHECK
Transaction Types: SECOND_INVOICE

Rule Type: DetectionRuleType.HIGH_VALUE_ITEM
Implementation Notes:
  - UPDATED: Now uses per-unit comparison instead of total amount comparison
  - Requires IADCompanyPayableFetcher dependency to fetch historical company payables
  - Use JpaCompanyPayableRepository.findByCompanyIdAndYearAndMonth() pattern for historical data
  - Compare per-unit amount: lineItem.unitAmount (for LineItemDTO)
  - For historical data: jpaLineItem.unitPrice (for JpaInvoiceLineItem)
  - Group historical data by LineItemType (GROSS_SALARY, MANAGEMENT_FEE_EOR, etc.)
  - Handle currency normalization using baseCurrency field
  - Apply statistical thresholds (e.g., 150% of historical maximum per-unit amount per line item type)
  - Handle cases with insufficient historical data (minimum 3 months of data required)
  - UPDATED: Only include AUTHORIZED, PAID, and PARTIALLY_PAID invoices (excludes DRAFT, PENDING, VOIDED, DELETED)
  - Access historical line items via: companyPayable.getInvoice().getLineItems()
  - Use existing IAD pattern: fetch 6 months of previous data for comparison
  - Action: ERROR
  - BENEFIT: Avoids false positives for companies with high quantities or many employees
  - BENEFIT: More meaningful comparison of actual per-unit costs rather than total amounts
  - Comprehensive unit tests covering all scenarios including edge cases and per-unit logic
  - Added to SecondInvoiceAnomalyDetector as specified
  - Graceful degradation when historical data is unavailable
  - Follows existing IAD patterns from IADInvHistoryGrossSalary and similar rules
```

### 10. BaseCurrencyCheck

```
Rule Name: BaseCurrencyCheckRule
Status: IMPLEMENTED
Priority: HIGH
Description: Payroll output (functional) currency ≠ FX conversion base
Severity: ERROR

Condition: Validate that payroll output currency matches FX conversion base currency
Data Required: Payroll system currency, Invoice FX conversion base currency
Dependencies: PayrollServiceAdapter (for payroll currency), InvoiceDTO (for FX base currency)

Detection Strategy: COMPARISON
Comparison Fields: payrollOutputCurrency:fxConversionBaseCurrency
Data Source: PayrollServiceAdapter.getCompaniesPayroll(), InvoiceDTO.lineItems[].baseCurrency

Validation Rules:
  - Get payroll output currency from payroll system for the company/month/year
  - Get FX conversion base currency from invoice line items (baseCurrency field)
  - Compare the two currencies for exact match
  - Fail if currencies do not match (indicates system inconsistency)
  - Handle cases where currencies cannot be determined

Error Message: Payroll output currency %s does not match FX conversion base currency %s. Currency mismatch detected between payroll system and invoice processing
Feature Flag: ENABLE_BASE_CURRENCY_CHECK
Transaction Types: SECOND_INVOICE

Rule Type: DetectionRuleType.BASE_CURRENCY_CHECK
Implementation Notes:
  - Requires PayrollServiceAdapter dependency to fetch payroll currency data
  - Uses CompanyMemberPayWrapper.getCurrency() to get payroll output currency
  - Uses InvoiceDTO.lineItems[].baseCurrency as FX conversion base currency
  - Fallback to billingCurrencyCode if no baseCurrency in line items
  - Action: ERROR (blocks processing) - currency mismatch indicates serious system issue
  - Graceful handling of missing data or service failures
  - Comprehensive error messages for different failure scenarios
  - Validates currency consistency between payroll and invoice systems
  - Critical for ensuring FX conversion accuracy and data integrity
  - Added to SecondInvoiceAnomalyDetector as specified
  - Comprehensive unit tests covering all scenarios including edge cases
  - Uses when expressions to reduce return statements for detekt compliance
  - Proper null safety checks and exception handling
```

### 11. FirstInvoiceCurrencyMatch

```
Rule Name: FirstInvoiceCurrencyMatchRule
Status: IMPLEMENTED
Priority: HIGH
Description: 2nd Invoice currency matches ALL first invoice currencies and validates currency consistency
Severity: ERROR

Condition: Validate that ALL first invoices for the same company/period use the same currency AND that currency matches the second invoice currency
Data Required: Current (second) invoice currency, ALL first invoice currencies for same company/period
Dependencies: JpaInvoiceRepository (for first invoice lookup), JpaCompanyPayableRepository (for first invoice payables)

Detection Strategy: MULTI_COMPARISON
Comparison Fields: secondInvoiceCurrency:allFirstInvoiceCurrencies
Data Source: InvoiceDTO.billingCurrencyCode, JpaCompanyPayable.currency (from ALL first invoices)

Validation Rules:
  - Get current (second) invoice currency from InvoiceDTO.billingCurrencyCode
  - Find ALL first invoices for same company/month/year using CompanyPayableType.FIRST_INVOICE
  - Extract currencies from ALL first invoice payables (not just first match)
  - Validate that ALL first invoices have the SAME currency (currency consistency check)
  - Compare the consistent first invoice currency with second invoice currency
  - FAIL if multiple first invoices have different currencies
  - FAIL if consistent first invoice currency differs from second invoice currency
  - Handle cases where no first invoices can be found (skip validation)

Error Messages:
  - Inconsistent First Invoices: "Multiple first invoices found with different currencies: %s for company %d, month %d, year %d. All first invoices must use the same currency"
  - First vs Second Mismatch: "Second invoice currency %s does not match consistent first invoice currency %s. Currency mismatch detected between first and second invoices"

Feature Flag: ENABLE_FIRST_INVOICE_CURRENCY_MATCH
Transaction Types: SECOND_INVOICE

Rule Type: DetectionRuleType.FIRST_INVOICE_CURRENCY_MATCH
Implementation Notes:
  - ENHANCED: Now validates ALL first invoices (not just first match) for currency consistency
  - NEW: Two-stage validation - first invoice consistency + first vs second comparison
  - NEW: FirstInvoiceCurrencyStatus enum (NO_FIRST_INVOICES, INCONSISTENT_CURRENCIES, CONSISTENT_CURRENCY)
  - NEW: FirstInvoiceCurrencyResult data class for structured validation results
  - NEW: validateFirstInvoiceCurrencies() method processes ALL first invoices
  - Requires JpaInvoiceRepository and JpaCompanyPayableRepository dependencies
  - Uses findByCompanyIdAndYearAndMonthAndTypeAndStatusNotIn() to find ALL first invoice payables
  - Filters out VOIDED/DELETED payables for accurate comparison
  - Action: ERROR (blocks processing) - currency mismatch indicates serious inconsistency
  - Graceful handling of missing first invoices or service failures
  - Enhanced error messages for different failure scenarios with detailed currency lists
  - Validates currency consistency between funding (first) and reconciliation (second) invoices
  - Critical for ensuring consistent currency handling across ALL related invoice generations
  - Added to SecondInvoiceAnomalyDetector as specified
  - Comprehensive unit tests covering all scenarios including edge cases (11 test cases)
  - Enhanced logging shows count of first invoices validated
  - Uses when expressions to reduce return statements for detekt compliance
  - Proper null safety checks and exception handling
  - Source: "Funding vs Reconciliation comparison" as specified in requirements
  - UPDATED: Modified per user requirement to validate ALL first invoices for consistency
```

### 12. AmountAlreadyBilledMismatchMfee

```
Rule Name: AmountAlreadyBilledMismatchMfeeRule
Status: IMPLEMENTED
Priority: HIGH
Description: System's "already billed M fee" ≠ NS "already billed"
Severity: ERROR

Condition: Validate that the system's calculated "already billed management fee" amount matches NetSuite's "already billed" amount
Data Required: BILLED_MANAGEMENT_FEE line items, NetSuite invoice data for comparison
Dependencies: NetsuiteInvoiceProvider (for NS data), InvoiceAdapter (for NS invoice fetching)

Detection Strategy: COMPARISON
Comparison Fields: systemAlreadyBilledAmount:netsuiteAlreadyBilledAmount
Data Source: InvoiceDTO.lineItems[BILLED_MANAGEMENT_FEE], NetSuite invoice data

Validation Rules:
  - Extract BILLED_MANAGEMENT_FEE line items from current second invoice
  - For each BILLED_MANAGEMENT_FEE line item, get the system's "already billed" amount
  - Fetch corresponding NetSuite invoice data for the same contracts/periods
  - Extract NetSuite's "already billed" amount for management fees
  - Compare system amount with NetSuite amount for exact match
  - Generate FAIL result if amounts don't match
  - Handle currency conversion if needed
  - Handle cases where NetSuite data is unavailable

Error Message: System's already billed management fee amount %s %s does not match NetSuite's already billed amount %s %s for contract %s
Feature Flag: ENABLE_AMOUNT_ALREADY_BILLED_MISMATCH_MFEE_CHECK
Transaction Types: SECOND_INVOICE

Rule Type: DetectionRuleType.AMOUNT_ALREADY_BILLED_MISMATCH_MFEE
Implementation Notes:
  - Added DetectionRuleType.AMOUNT_ALREADY_BILLED_MISMATCH_MFEE to enum
  - Added InvoiceAnomalyDetectorType.AMOUNT_ALREADY_BILLED_MISMATCH_MFEE to enum
  - Created AmountAlreadyBilledMismatchMfeeRule extending BaseAnomalyDetectionRule
  - Added rule to SecondInvoiceAnomalyDetector qualified detection types
  - Updated SecondInvoiceAnomalyDetectorTest with new rule type
  - Created comprehensive unit tests covering all scenarios
  - Implemented CORRECTED NetSuite data fetching logic (current period only)
  - Added JpaCompanyPayableRepository dependency for first invoice data access
  - Uses NetsuiteInvoiceProvider.getByBatch() for efficient NetSuite data retrieval
  - Uses InvoiceAdapter.list() to fetch detailed NetSuite invoice data
  - Compares BILLED_MANAGEMENT_FEE (second invoice) with MANAGEMENT_FEE_EOR (first invoice) by contract ID
  - Filters to FIRST_INVOICE payables only (not historical aggregation)
  - Targets only BILLED_MANAGEMENT_FEE line items in second invoices
  - Action: ERROR (blocks processing) - amount mismatch indicates serious billing discrepancy
  - Graceful handling of missing NetSuite data or service failures
  - Comprehensive error messages for different failure scenarios
  - Critical for ensuring billing accuracy between system and NetSuite
  - Proper null safety checks and exception handling
  - Source: "Invoice lines + NS data" as specified in requirements
  - COMPLETE: Corrected logic compares current period first invoice amounts only
  - FIXED: No longer does historical aggregation - compares like-with-like amounts
  - HANDLES: Management fee changes correctly (period-specific comparison)
```


### 13. AmountAlreadyBilledMismatchPayrollCost

```
Rule Name: AmountAlreadyBilledMismatchPayrollCostRule
Status: IMPLEMENTED
Priority: HIGH
Description: System's "already billed payroll cost" ≠ NS "already billed"
Severity: ERROR

Condition: Validate that the system's calculated "already billed payroll cost" amount matches NetSuite's "already billed" amount
Data Required: BILLED_GROSS_SALARY and BILLED_GROSS_SALARY_SUPPLEMENTARY line items, NetSuite invoice data for comparison
Dependencies: NetsuiteInvoiceProvider (for NS data), InvoiceAdapter (for NS invoice fetching), JpaCompanyPayableRepository (for first invoice data)

Detection Strategy: COMPARISON
Comparison Fields: systemAlreadyBilledAmount:netsuiteAlreadyBilledAmount
Data Source: InvoiceDTO.lineItems[BILLED_GROSS_SALARY, BILLED_GROSS_SALARY_SUPPLEMENTARY], NetSuite invoice data

Validation Rules:
  - Extract BILLED_GROSS_SALARY and BILLED_GROSS_SALARY_SUPPLEMENTARY line items from current second invoice
  - For each billed payroll cost line item, get the system's "already billed" amount
  - Fetch corresponding NetSuite invoice data for the same contracts/periods
  - Extract NetSuite's "already billed" amount for gross salary
  - Compare system amount with NetSuite amount for exact match
  - Generate FAIL result if amounts don't match
  - Handle currency conversion if needed
  - Handle cases where NetSuite data is unavailable

Error Message: System's already billed payroll cost amount %.2f %s does not match NetSuite's first invoice amount %.2f %s for contract %d
Feature Flag: ENABLE_AMOUNT_ALREADY_BILLED_MISMATCH_PAYROLL_COST_CHECK
Transaction Types: SECOND_INVOICE

Rule Type: DetectionRuleType.AMOUNT_ALREADY_BILLED_MISMATCH_PAYROLL_COST
Implementation Notes:
  - Added DetectionRuleType.AMOUNT_ALREADY_BILLED_MISMATCH_PAYROLL_COST to enum
  - Added InvoiceAnomalyDetectorType.AMOUNT_ALREADY_BILLED_MISMATCH_PAYROLL_COST to enum
  - Created AmountAlreadyBilledMismatchPayrollCostRule extending BaseAnomalyDetectionRule
  - Added rule to SecondInvoiceAnomalyDetector qualified detection types
  - Updated SecondInvoiceAnomalyDetectorTest with new rule type
  - Created comprehensive unit tests covering all scenarios
  - Implemented NetSuite data fetching logic (current period only)
  - Added JpaCompanyPayableRepository dependency for first invoice data access
  - Uses NetsuiteInvoiceProvider.getByBatch() for efficient NetSuite data retrieval
  - Uses InvoiceAdapter.list() to fetch detailed NetSuite invoice data
  - Compares BILLED_GROSS_SALARY/BILLED_GROSS_SALARY_SUPPLEMENTARY (second invoice) with GROSS_SALARY (first invoice) by contract ID
  - Filters to FIRST_INVOICE payables only (not historical aggregation)
  - Targets BILLED_GROSS_SALARY and BILLED_GROSS_SALARY_SUPPLEMENTARY line items in second invoices
  - Action: ERROR (blocks processing) - amount mismatch indicates serious billing discrepancy
  - Graceful handling of missing NetSuite data or service failures
  - Comprehensive error messages for different failure scenarios
  - Critical for ensuring billing accuracy between system and NetSuite
  - Proper null safety checks and exception handling
  - Source: "Invoice lines + NS data" as specified in requirements
  - COMPLETE: Compares current period first invoice amounts only
  - FIXED: No historical aggregation - compares like-with-like amounts
  - HANDLES: Payroll cost changes correctly (period-specific comparison)
```

## Implementation Workflow

1. Select a rule from the templates above that is marked as PENDING
2. Update its status to IN_PROGRESS in this file
3. Implement the rule following the implementation guidelines
4. Add the rule to the appropriate anomaly detectors
5. Write comprehensive unit tests
6. Update the rule's status to IMPLEMENTED in this file
7. Add any implementation notes or learnings
