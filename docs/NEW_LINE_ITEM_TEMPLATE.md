# New Line Item Implementation Template

## Instructions
This template is for adding NEW line items to EXISTING transaction types.
If you need to create a completely new transaction/invoice type, use NEW_TRANSACTION_TYPE_TEMPLATE.md first.

Fill out this template and the AI will implement the new line item systematically.

---

## Basic Information

### Line Item Details
- **Line Item Enum Name**: `_____________________`
  - *Example: "VAS_INCIDENT_PICKUP_DELIVERY_FEE"*
  - *Must be UPPER_CASE with underscores*

- **Line Item Display Name (refName)**: `_____________________`
  - *Example: "VAS Incident Pickup Delivery Fee"*
  - *This is used for NetSuite mapping - must be exact*

- **Target Transaction Type**: `_____________________`
  - *Available Options:*
    - *FIRST_INVOICE, SECOND_INVOICE, VAS_INCIDENT_INVOICE*
    - *DEPOSIT_INVOICE, GP_FUNDING_INVOICE, INSURANCE_INVOICE*
    - *ANNUAL_PLAN_INVOICE, ANNUAL_PLAN_AOR_INVOICE*
    - *GP_SERVICE_INVOICE, FREELANCER_INVOICE, VENDOR_BILL*
    - *ORDER_FORM_ADVANCE, OFF_CYCLE (if implemented)*

---

## NetSuite Configuration

### NetSuite Mapping
- **NetSuite Item ID**: `_____________________`
  - *Example: "1234"*
  - *Get this from NetSuite admin - must be exact numeric ID*

- **Tax Treatment**: `_____________________`
  - *Options:*
    - *Standard Rated (8% GST) - for service fees*
    - *Zero Rated (0% GST) - for salary/payroll items*
    - *Out of Scope - for deposits/payments*

- **NetSuite Mapping Exists**: `_____________________`
  - *Options: YES, NO*
  - *Check if refName already exists in netsuite-mapping files*

---

## Data Source

### Service Information
- **Data Source Service**: `_____________________`
  - *Example: "BillingServiceAdapter" (for management fees), "PayrollServiceAdapter", "VasIncidentServiceAdapter"*
  - *Use BillingServiceAdapter for management fee line items*
  - *Must be existing service adapter*

---

## Business Logic

### Description Builder
- **Description Type**: `_____________________`
  - *Options: STATIC, COUNTRY_BASED, DYNAMIC*

- **Description Details**:
  - **Static Description**: `_____________________`
    - *Example: "Off Cycle Management Fee"*

  - **Country-Based Descriptions** (if applicable):
    - **India**: `_____________________`
    - **North America**: `_____________________`
    - **Others/ROW**: `_____________________`

  - **Dynamic Pattern** (if applicable): `_____________________`
    - *Example: "Off Cycle Payroll Cost - {currency} {amount}"*

### Normalizer Configuration
- **Normalizer Needed**: `_____________________`
  - *Options: YES, NO*
  - *YES = Create new normalizer, NO = Share existing normalizer*

- **Existing Normalizer to Share** (if NO): `_____________________`
  - *Specify which existing normalizer file to use*

---

## Implementation Files

### Files to Update (6 files)
*AI will automatically update these*

1. ✅ LineItemType.java - Add enum with refName
2. ✅ PayableItemTypeMapper.java - Add value mapping
3. ✅ InvoiceTypeMapper.kt - Add gRPC mapping
4. ✅ NetSuite mapping files (production/release/stage) - Add tax mapping
5. ✅ LineItem.proto - Add to gRPC schema
6. ✅ GrpcItemTypeMapper.kt - Update mapper

### Files to Create (3+ files)
*AI will create these with appropriate naming using reference files*

1. ✅ `app/src/main/kotlin/com/multiplier/payable/engine/collector/offcycle/{LINE_ITEM}DataCollector.kt`
   - **Reference**: `app/src/main/kotlin/com/multiplier/payable/engine/collector/offcycle/OffCycleManagementFeeDataCollector.kt`

2. ✅ `app/src/main/kotlin/com/multiplier/payable/engine/collector/offcycle/{LINE_ITEM}PayableItemStoreNormalizer.kt` (if needed)
   - **Management Fee Reference**: `app/src/main/kotlin/com/multiplier/payable/engine/collector/offcycle/OffCycleManagementFeePayableItemStoreNormalizer.kt`
   - **Payment/Payroll Reference**: `app/src/main/kotlin/com/multiplier/payable/engine/collector/offcycle/OffCyclePayrollPaymentPayableItemStoreNormalizer.kt`

3. ✅ `app/src/main/kotlin/com/multiplier/payable/engine/reconciler/descriptionbuilder/offCycle/{LINE_ITEM}DescriptionBuilder.kt`
   - **Reference**: `app/src/main/kotlin/com/multiplier/payable/engine/reconciler/descriptionbuilder/offCycle/OffCycleManagementFeeDescriptionBuilder.kt`



---

## Example: Completed Template

```markdown
## Basic Information
- **Line Item Enum Name**: `OFFCYCLE_MANAGEMENT_FEE`
- **Line Item Display Name**: `Off Cycle Management Fee`
- **Target Transaction Type**: `OFF_CYCLE`

## NetSuite Configuration
- **NetSuite Item ID**: `5678`
- **Tax Treatment**: `Standard Rated (8% GST)`
- **NetSuite Mapping Exists**: `NO`

## Data Source
- **Data Source Service**: `BillingServiceAdapter`

## Business Logic
- **Description Type**: `DYNAMIC`
- **Dynamic Pattern**: `Off Cycle Management Fee - {currency} {amount}`
- **Normalizer Needed**: `YES`
```

---

## Normalizer Decision

### When to Create New Normalizer:
- **YES**: Line item has unique business logic or data transformation needs
- **NO**: Line item can reuse existing normalizer logic from similar line items

### Reference Files Available:
- **Management Fee Pattern**: `OffCycleManagementFeePayableItemStoreNormalizer.kt`
- **Payment/Payroll Pattern**: `OffCyclePayrollPaymentPayableItemStoreNormalizer.kt`

*Note: There is a chance that both management fee line items and normal line items can share the same normalizer, so ask the user for this decision.*

---

## Notes for AI Implementation

When implementing based on this template:

1. **Update all 6 files** with new line item mappings
2. **Check NetSuite mappings** - Add if refName doesn't exist
3. **Create data collector** - Use appropriate reference file
4. **Handle normalizer logic** - Follow decision above
5. **Create description builder** - Use reference file

### CRITICAL PATH REQUIREMENTS:
- **MUST use the EXACT file paths provided above** - Do NOT use different or alternative paths
- **MUST use the specified reference files** - Do NOT use other files as templates
- **File naming convention**: Replace {LINE_ITEM} with the actual line item name (e.g., OffCycleManagementFee, VasIncidentPickupDelivery)
- **NetSuite files**: Update all 3 environment files (production, release, stage) with identical mappings

### REQUIRED DEPENDENCY UPDATES:
1. **Update graph-registry dependencies**:
   - Update `com.multiplier:payable-service-graph` dependency
   - Update `com.multiplier:payroll-service-schema` dependency
   - Add new line item to the graph schema
   - **Reference PR**: https://github.com/Multiplier-Core/graph-registry/pull/2859/files

2. **Database Template Update**:
   - Update the transaction template in database table: `transaction_template_v2` to include the new line item
   - This is required for proper invoice generation with the new line item

---

**Template Version**: 1.0
**Last Updated**: 2024
**Dependencies**: Target transaction type must exist (use NEW_TRANSACTION_TYPE_TEMPLATE.md first if needed)
