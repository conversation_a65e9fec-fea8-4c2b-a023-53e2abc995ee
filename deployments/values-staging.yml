environments:
  # Contract Severance History Scheduler Configuration
  CONTRACT_SEVERANCE_HISTORY_SCHEDULER_CRON: "0 0 3,15 1,6,11,16,20-31 * ?"  # 3 AM and 3 PM UTC: every 5 days in first 20 days (1,6,11,16), daily from 20th onwards
  CONTRACT_SEVERANCE_HISTORY_SCHEDULER_TIMEZONE: "UTC"
  CONTRACT_SEVERANCE_HISTORY_SCHEDULER_BATCH_SIZE: "5"  # Smaller batch size for staging
  CONTRACT_SEVERANCE_HISTORY_SCHEDULER_ENABLED: "true"

  CLOUD_AWS_REGION_STATIC: ap-southeast-1
  CLOUD_AWS_S3_BUCKET: stg-app-tech-appdocuments-multipliers3
  FEIGN_CLIENT_CONFIG_NETSUITERESTAPI_CONNECTTIMEOUT: '120000'
  FEIGN_CLIENT_CONFIG_NETSUITERESTAPI_DECODE404: 'false'
  FEIGN_CLIENT_CONFIG_NETSUITERESTAPI_LOGGERLEVEL: FULL
  FEIGN_CLIENT_CONFIG_NETSUITERESTAPI_READTIMEOUT: '120000'
  GRPC_SERVER_SECURITY_ENABLED: 'true'
  JAVA_HEAP_MAX_MEM: -Xmx1g
  NETSUITE_INVLINEDELETE_RESTLET_SCRIPTID: '860'
  NETSUITE_PDF_RESTLET_SCRIPTID: '773'
  OPS_3DTATUPDATESEMAILS: <EMAIL>;<EMAIL>;<EMAIL>
  OPS_CONTRACTEMAIL: <EMAIL>
  OPS_CSMLEADEMAIL: <EMAIL>
  OPS_CUSTOMERSUCCESSEMAIL: <EMAIL>
  OPS_CXRECIPIENTEMAIL: <EMAIL>
  OPS_EORCONTRACTSIGNEREMAIL: <EMAIL>
  OPS_FINANCEEMAIL: <EMAIL>
  OPS_FREELANCERUPDATESEMAIL: <EMAIL>
  OPS_MEMBEROFFBOARDINGEMAIL: <EMAIL>
  OPS_MEMBERONBOARDINGEMAIL: <EMAIL>
  OPS_MSASALESEMAIL: <EMAIL>
  OPS_MSASIGNEREMAIL: <EMAIL>
  OPS_OPERATIONSEMAIL: <EMAIL>
  OPS_PAYROLLUPDATESEMAIL: <EMAIL>
  OPS_RECIPIENTEMAIL: <EMAIL>
  OPS_REVENUEOPERATIONSEMAIL: <EMAIL>
  OPS_SALESEMAIL: <EMAIL>
  OPS_SENDEREMAIL: <EMAIL>
  PLATFORM_COMPANYSERVICE_KAFKA_GROUPID: company-service
  PLATFORM_DISPUTESERVICE_KAFKA_GROUPID: dispute-service
  PLATFORM_DISPUTESERVICE_KAFKA_TOPIC: topic.internal.v1.dispute.status.update
  PLATFORM_DISPUTESERVICE_SYSTEM_NOTIFICATION_BILLING_EMAIL: <EMAIL>
  PLATFORM_DISPUTESERVICE_SYSTEM_NOTIFICATION_EMAIL: <EMAIL>
  PLATFORM_LEGALENTITY_KAFKA_GROUPID: payable-service-legal-entity
  PLATFORM_LEGALENTITY_KAFKA_TOPIC: topic.internal.v1.company.legal-entity
  PLATFORM_INVOICEENGINE_KAFKA_CONSUMER_CONCURRENCY: '3'
  PLATFORM_INVOICEENGINE_KAFKA_CONSUMER_MAXPOLLRECORDS: '5'
  SPRING_PROFILES_ACTIVE: stage
  SPRING_SCHEDULER_CONTACTUPDATE_CRONTIME: 0 5/30 * * * ?
  XERO_SCHEDULER_ENABLED: 'true'
  XERO_TOKENSERVERURL: https://identity.xero.com/connect/token
  NETSUITE_RESTLET_RESYNC_SCRIPT_ID: '1248'
  NETSUITE_RESTLET_DOWNLOAD_SAVED_SEARCH_SCRIPT_ID: '1367'
  awslogs-group: /stg/app/tech/payableService/cloudwatchLogGroup
  awslogs-stream-prefix: ecs
kind: v2
name: payableService
resources:
  cpu: 512
  memory: 2048
secrets:
  APM_SERVER_URL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/apmServer/param
  APM_TOKEN: arn:aws:secretsmanager:ap-southeast-1:133139256227:secret:/mgt/monitoring/devops/elasticsearch/apmtoken/secret-qmnKQx
  FEIGN_CLIENT_CONFIG_NETSUITERESTAPI_URL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/rest/apiUrl/param
  REACTIVE_FEIGN_CLIENT_CONFIG_NETSUITERESTLETASYNCAPI_URL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/restlet/apiUrl/param
  FEIGN_CLIENT_CONFIG_DOCGEN_SERVICE_URL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/feignClient/docgenService/param
  GROWTHBOOK_BASEURL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/growthbook/param
  GROWTHBOOK_ENVKEY: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/growthbook/env/key/param
  GRPC_CLIENT_BILLINGSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/apse1/shared/url/grpc/billing-service
  GRPC_CLIENT_COMPANYSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/grpc/companyService/param
  GRPC_CLIENT_CONTRACTSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/grpc/contractService/param
  GRPC_CLIENT_CORESERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/grpc/coreService/param
  GRPC_CLIENT_COUNTRYSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/grpc/countryService/param
  GRPC_CLIENT_DEPOSITSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/apse1/shared/url/grpc/deposit-service
  GRPC_CLIENT_MEMBERSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/grpc/memberService/param
  GRPC_CLIENT_ORGMANAGEMENTSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/grpc/orgManagementService/param
  GRPC_CLIENT_PAYROLLSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/grpc/payrollService/param
  GRPC_CLIENT_PRICINGSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/grpc/pricingService/param
  GRPC_CLIENT_PIGEONSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/grpc/pigeonService/param
  GRPC_CLIENT_COMPENSATION_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/grpc/compensationService/param
  JWT_PUBLIC_KEY: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/employee/jwt/publicKey/param
  NETSUITE_OAUTH_CERTIFICATEID: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/oauth/certificateId/param
  NETSUITE_OAUTH_PRIVATEKEY: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/oauth/privateKey/param
  NETSUITE_OAUTH_REALMID: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/oauth/realmId/param
  NETSUITE_WEBHOOK_PRESHAREDKEY: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/webhook/presharedKey/param
  NETSUITE_WEBSERVICE_ACCOUNT: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/webservice/account/param
  NETSUITE_WEBSERVICE_TBACONSUMERKEY: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/webservice/tbaConsumerKey/param
  NETSUITE_WEBSERVICE_TBACONSUMERSECRET: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/webservice/tbaConsumerSecret/param
  NETSUITE_WEBSERVICE_TBATOKENID: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/webservice/tbaTokenId/param
  NETSUITE_WEBSERVICE_TBATOKENSECRET: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/webservice/tbaTokenSecret/param
  NETSUITE_WEBSERVICE_WSURL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/webservice/wsUrl/param
  OPSPLATFORM_FRONTEND_PARTNERBASEURL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/platfrom/ops/frontend/partner/param
  OPSPLATFORM_FRONTEND_USERBASEURL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/platfrom/ops/frontend/user/param
  PLATFORM_COMPANYSERVICE_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_BILLINGSERVICE_KAFKA_CONSUMER_BOOTSTRAPSERVERS: /stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_DISPUTESERVICE_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_LEGALENTITY_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_NETSUITETRANSACTION_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_INVOICEENGINE_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PIGEON_CLIENT_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_DOCGEN_BASEURL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/platform/docgenService/private/param
  PLATFORM_DOCGEN_PUBLICBASEURL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/platform/docgenService/public/param
  PLATFORM_FRONTEND_BASEURL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/platform/frontend/param
  PLATFORM_PAYABLESERVICE_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_PAYROLLSERVICE_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_USERSERVICE_BASEURL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/platform/userService/param
  PLATFORM_USERSERVICE_SYSTEM_USER_PASSWORD: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/platform/userService/password/param
  PLATFORM_USERSERVICE_SYSTEM_USER_USERNAME: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/platform/userService/username/param
  SPRING_DATASOURCE_PASSWORD: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/payableService/db/password
  SPRING_DATASOURCE_URL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/coreService/db/url/param
  SPRING_DATASOURCE_USERNAME: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/payableService/db/user
  SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_NETSUITE_TOKENURI: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/rest/tokenUri/param
  SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_NETSUITE_CLIENTID: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/webservice/tbaConsumerKey/param
  SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_NETSUITE_CLIENTSECRET: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/webservice/tbaConsumerSecret/param
  XERO_CLIENTID: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/xero/client/id/param
  XERO_CLIENTSECRET: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/xero/client/secret/param
  XERO_SECRET: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/xero/secret/param
  GRPC_CLIENT_AUTHORITY_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/url/grpc/userService/param
  GRPC_CLIENT_SEVERANCE_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/apse1/shared/url/grpc/severance-service
  GRPC_CLIENT_VAS_INCIDENTAL_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/apse1/shared/url/grpc/vas-incidental-service
  FEIGN_CLIENT_CONFIG_NETSUITERESTLETAPI_URL: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/netsuite/restlet/apiUrl/param
  GRPC_CLIENT_PRODUCT_CATALOGUE_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:************:parameter/stg/apse1/shared/url/grpc/product-catalogue-service
  REDIS_HOST: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/redisCluster/host/master/param
  REDIS_PASSWORD: arn:aws:ssm:ap-southeast-1:************:parameter/stg/app/tech/services/shared/redisCluster/password/param