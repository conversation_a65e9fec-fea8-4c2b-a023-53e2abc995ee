syntax = "proto3";

import "com/multiplier/payable/common/CommonV2.proto";

package com.multiplier.payable.grpc.schema.lineitem;

option java_multiple_files = true;

message GrpcLineItem {
  string description = 1;
  double quantity = 2;
  common.schema.GrpcAmount unitPrice = 3;
  common.schema.GrpcTax tax = 4;
  optional int64 contractId = 5;
  optional string memberName = 6;
  GrpcLineItemType itemType = 7;
  common.schema.GrpcAmount amountInBaseCurrency = 8;
  optional string countryName = 9;
  optional common.schema.GrpcAmount grossAmount = 10; //can be null until back-sync.
  optional common.schema.GrpcDate startInvoiceCycleDate = 11;
  optional common.schema.GrpcDate endInvoiceCycleDate = 12;
}

enum GrpcLineItemType {
  UNKNOWN_LINE_ITEM_TYPE = 0;
  EOR_SALARY_DISBURSEMENT = 1;
  GROSS_SALARY = 2;
  FREELANCER_PAYMENT = 3;
  LAPTOP_FEE_OTHERS = 4;
  LAP_FEE_SG = 5;
  MANAGEMENT_FEE_EOR = 6;
  MANAGEMENT_FEE_EOR_PAYROLL = 7;
  MANAGEMENT_FEE_FREELANCER = 8;
  MANAGEMENT_FEE_PEO = 9;
  MANAGEMENT_FEE_OTHERS = 10;
  MEMBER_DEPOSIT = 11;
  OTHER_SETUP_FEE = 12;
  OTHER_SERVICES = 13;
  PAYMENT_FEE = 14;
  VISA_FEE = 15;
  PEO_SALARY_DISBURSEMENT = 16;
  INSURANCE_PREMIUM = 17;
  PLATFORM_FEE = 18;
  SUBSCRIPTION_FEE = 19;
  ANNUAL_MANAGEMENT_FEE_EOR = 20;
  EOR_EXPENSE_DISBURSEMENT = 21;
  BILLED_GROSS_SALARY_SUPPLEMENTARY = 22;
  BILLED_GROSS_SALARY = 23;
  BILLED_MANAGEMENT_FEE = 24;
  VAT_AGGREGATED = 25;
  SEVERANCE_DEPOSIT_EOR_PAYROLL = 26;
  ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL = 27;
  GP_FUND_REQUEST = 28;
  MEMBER_PAYABLE_PAYOUT_FEE = 29;
  VAS_INCIDENT_LAPTOP_PAYMENT = 30;
  VAS_INCIDENT_LAPTOP_MANAGEMENT_FEE = 31;
  VAS_INCIDENT_MONITOR_PAYMENT = 32;
  VAS_INCIDENT_MONITOR_MANAGEMENT_FEE = 33;
  VAS_INCIDENT_ACCESSORIES_PAYMENT = 34;
  VAS_INCIDENT_ACCESSORIES_MANAGEMENT_FEE = 35;
  VAS_INCIDENT_DISCOUNT = 36;
  VAS_INCIDENT_PICKUP_DELIVERY_AMOUNT = 37;
  VAS_INCIDENT_PICKUP_DELIVERY_FEE = 38;
  VAS_INCIDENT_STORAGE_AMOUNT = 39;
  VAS_INCIDENT_STORAGE_FEE = 40;
  VAS_INCIDENT_CONTRACT_CUSTOMISATION_FEE = 41;
  VAS_INCIDENT_LEGAL_CONSULTATION_FEE = 42;
  VAS_INCIDENT_OTHERS_SERVICE_AMOUNT = 43;
  VAS_INCIDENT_OTHERS_SERVICE_FEE = 44;
  VAS_INCIDENT_OTHERS_VISA_FEE = 45;
  PAYROLL_OFFCYCLE_COST = 46;
  PAYROLL_OFFCYCLE_EXPENSE = 47;
  PAYROLL_OFFCYCLE_MANAGEMENT_FEE = 48;
  BANK_FEE = 49;
}