syntax = "proto3";

import "com/multiplier/payable/common/CommonV2.proto";
import "com/multiplier/payable/grpc/schema/lineitem/LineItem.proto";

package com.multiplier.payable.grpc.schema.creditnote;

option java_multiple_files = true;

//data types
message GrpcCreditNote {
  int64 id = 1;
  int64 companyId = 2;
  repeated int64 appliedInvoices = 3;
  GrpcCreditNoteStatus status = 4;
  string externalId = 5;
  string currencyCode = 6;
  double amountTotal = 7;
  double amountDue = 8 [deprecated = true];
  double amountPaid = 9 [deprecated = true];
  optional string memo = 10;
  repeated GrpcCreditNoteLineItem items = 11;
  double amountApplied = 12;
  double amountUnapplied = 13;
  common.schema.GrpcDate createdDate = 14;
}

message GrpcCreditNoteLineItem {
  string description = 1;
  double totalCost = 2 [deprecated = true]; //cost after taxes
  double billingCost = 3 [deprecated = true]; //cost which we want to send
  string currencyCode = 4 [deprecated = true];
  optional int64 contractId = 5;
  optional float taxRate = 6[deprecated = true];
  lineitem.GrpcLineItemType lineItemType = 7;
  common.schema.GrpcAmount unitAmount = 8;
  common.schema.GrpcTax tax = 9;
  common.schema.GrpcAmount grossAmount = 10;
  string countryName = 11;
  optional string memberName = 12;
  common.schema.GrpcAmount amountInBaseCurrency = 13;
  common.schema.GrpcDate startInvoiceCycleDate = 14;
  common.schema.GrpcDate endInvoiceCycleDate = 15;
  double quantity = 16;
}

//enums
enum GrpcCreditNoteStatus {
  CREDIT_NOTE_STATUS_UNKNOWN = 0;
  CREDIT_NOTE_STATUS_DRAFT = 1;
  CREDIT_NOTE_STATUS_FULLY_APPLIED = 2;
  CREDIT_NOTE_STATUS_DELETED = 3;
}

enum GrpcCreditNoteReason {
  CREDIT_NOTE_REASON_UNKNOWN = 0;
  CREDIT_NOTE_REASON_INVOICE_VOIDED = 1;
  CREDIT_NOTE_REASON_INVOICE_CORRECTION = 2;
  CREDIT_NOTE_REASON_DISCOUNT = 3;
  CREDIT_NOTE_REASON_DEPOSIT_REFUND = 4;
  CREDIT_NOTE_REASON_SECOND_INVOICE = 5;
  CREDIT_NOTE_REASON_INSURANCE_CANCELLATION = 6;
  CREDIT_NOTE_REASON_WRITE_OFF = 7;
  CREDIT_NOTE_REASON_EXPENSES_REFUNDS = 8;
  CREDIT_NOTE_REASON_FREELANCER = 9;
  CREDIT_NOTE_REASON_DOUBLE_PAYMENT = 10;
}

//request
message CreateCreditNoteRequest {

}

message GetCreditNoteByIdsRequest {
  repeated int64 ids = 1;
}

//response
message CreateCreditNoteResponse {
  bool isSuccess = 1;
  GrpcCreditNote creditNote = 2;
}

message GrpcCreditNoteById {
  int64 id = 1;
  GrpcCreditNote creditNote = 2;
}

message GetCreditNoteByIdsResponse {
  repeated GrpcCreditNoteById creditNoteById = 1;
}

//rpc
service GrpcCreditNoteService {
  rpc CreateCreditNote(CreateCreditNoteRequest) returns (CreateCreditNoteResponse) {}
  rpc GetCreditNoteByIds(GetCreditNoteByIdsRequest) returns (GetCreditNoteByIdsResponse) {}
}
